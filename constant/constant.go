package constant

import (
	"net/url"
	"time"
)

const (
	FCRDManagement            = "fcrd"
	EuroPriceInfo             = "EuroPriceInfo"
	EuroWindAndSolarInfo      = "EuroWindAndSolarInfo"
	NetherlandsImbalancePrice = "NetherlandsImbalancePrice"
	NetherlandsAfrrPrice      = "NetherlandsAfrrPrice"
	FcrdPrice                 = "FcrdPrice"
)

var (
	ApiUrl                      = "https://web-api.tp.entsoe.eu/api?"
	Token                       = "securityToken=f341a462-7f8d-4966-8295-241ccf08cc35&"
	DocumentTypeForPrice        = "documentType=A44&"
	DocumentTypeForWindAndSolar = "documentType=A69&ProcessType=A01&"
	// 德国

	ParamsGermany = url.Values{
		"in_Domain":   {"10Y1001A1001A82H"},
		"out_Domain":  {"10Y1001A1001A82H"},
		"periodStart": {time.Now().Format("20060102") + "0000"},
		"periodEnd":   {time.Now().Format("20060102") + "2300"},
	}

	// 丹麦
	ParmasDenmark_1 = url.Values{
		"in_Domain":   {"10YDK-1--------W"},
		"out_Domain":  {"10YDK-1--------W"},
		"periodStart": {time.Now().Format("20060102") + "0000"},
		"periodEnd":   {time.Now().Format("20060102") + "2300"},
	}
	ParmasDenmark_2 = url.Values{
		"in_Domain":   {"10YDK-2--------M"},
		"out_Domain":  {"10YDK-2--------M"},
		"periodStart": {time.Now().Format("20060102") + "0000"},
		"periodEnd":   {time.Now().Format("20060102") + "2300"},
	}
	//挪威
	ParmasNO_1 = url.Values{
		"in_Domain":   {"10YNO-1--------2"},
		"out_Domain":  {"10YNO-1--------2"},
		"periodStart": {time.Now().Format("20060102") + "0000"},
		"periodEnd":   {time.Now().Format("20060102") + "2300"},
	}
	ParmasNO_2 = url.Values{
		"in_Domain":   {"10YNO-2--------T"},
		"out_Domain":  {"10YNO-2--------T"},
		"periodStart": {time.Now().Format("20060102") + "0000"},
		"periodEnd":   {time.Now().Format("20060102") + "2300"},
	}
	ParmasNO_3 = url.Values{
		"in_Domain":   {"10YNO-3--------J"},
		"out_Domain":  {"10YNO-3--------J"},
		"periodStart": {time.Now().Format("20060102") + "0000"},
		"periodEnd":   {time.Now().Format("20060102") + "2300"},
	}
	ParmasNO_4 = url.Values{
		"in_Domain":   {"10YNO-4--------9"},
		"out_Domain":  {"10YNO-4--------9"},
		"periodStart": {time.Now().Format("20060102") + "0000"},
		"periodEnd":   {time.Now().Format("20060102") + "2300"},
	}
	ParmasNO_5 = url.Values{
		"in_Domain":   {"10Y1001A1001A48H"},
		"out_Domain":  {"10Y1001A1001A48H"},
		"periodStart": {time.Now().Format("20060102") + "0000"},
		"periodEnd":   {time.Now().Format("20060102") + "2300"},
	}
	//瑞典
	ParmasSE_1 = url.Values{
		"in_Domain":   {"10Y1001A1001A44P"},
		"out_Domain":  {"10Y1001A1001A44P"},
		"periodStart": {time.Now().Format("20060102") + "0000"},
		"periodEnd":   {time.Now().Format("20060102") + "2300"},
	}
	ParmasSE_2 = url.Values{
		"in_Domain":   {"10Y1001A1001A45N"},
		"out_Domain":  {"10Y1001A1001A45N"},
		"periodStart": {time.Now().Format("20060102") + "0000"},
		"periodEnd":   {time.Now().Format("20060102") + "2300"},
	}
	ParmasSE_3 = url.Values{
		"in_Domain":   {"10Y1001A1001A46L"},
		"out_Domain":  {"10Y1001A1001A46L"},
		"periodStart": {time.Now().Format("20060102") + "0000"},
		"periodEnd":   {time.Now().Format("20060102") + "2300"},
	}
	ParmasSE_4 = url.Values{
		"in_Domain":   {"10Y1001A1001A47J"},
		"out_Domain":  {"10Y1001A1001A47J"},
		"periodStart": {time.Now().Format("20060102") + "0000"},
		"periodEnd":   {time.Now().Format("20060102") + "2300"},
	}
	ParamsNL = url.Values{
		"in_Domain":   {"10YNL----------L"},
		"out_Domain":  {"10YNL----------L"},
		"periodStart": {time.Now().Format("20060102") + "0000"},
		"periodEnd":   {time.Now().Format("20060102") + "2300"},
	}
	Country = []string{
		"Germany", "Denmark_1", "Denmark_2", "Norway_1", "Norway_2", "Norway_3", "Norway_4", "Norway_5",
		"Sweden_1", "Sweden_2", "Sweden_3", "Sweden_4", "Netherlands",
	}

	CountryMap = map[string]url.Values{
		"Germany": ParamsGermany, "Denmark_1": ParmasDenmark_1, "Denmark_2": ParmasDenmark_2, "Norway_1": ParmasNO_1, "Norway_2": ParmasNO_2,
		"Norway_3": ParmasNO_3, "Norway_4": ParmasNO_4, "Norway_5": ParmasNO_5, "Sweden_1": ParmasSE_1,
		"Sweden_2": ParmasSE_2, "Sweden_3": ParmasSE_3, "Sweden_4": ParmasSE_4, "Netherlands": ParamsNL,
	}
)
