package exec

import (
	"context"
	"encoding/csv"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"net/http"
	"sort"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/patrickmn/go-cache"
	"github.com/rs/xid"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	um "git.nevint.com/golang-libs/common-utils/model"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	welkin_cache "git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/domain/common"
	domain_git "git.nevint.com/welkin2/welkin-backend/domain/git"
	"git.nevint.com/welkin2/welkin-backend/domain/health"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	"git.nevint.com/welkin2/welkin-backend/service"
)

type Internal interface {
	IssueCommand2Station(area string) gin.HandlerFunc
	GenerateNewSign() gin.HandlerFunc
	RequestWithSign() gin.HandlerFunc
	QueryPeople() gin.HandlerFunc
	CsvParse() gin.HandlerFunc
	GetElectricityPrice() gin.HandlerFunc
	GetElectricityByOrganized() gin.HandlerFunc
	GetVehicleType() gin.HandlerFunc
	GetBasicNameMapping() gin.HandlerFunc
	GetBasicNameMappingByKey() gin.HandlerFunc

	PushRedRabbitParams() gin.HandlerFunc
	PushMazuParams() gin.HandlerFunc
	SyncDeviceProto() gin.HandlerFunc
	SyncPowerChargeDevices() gin.HandlerFunc
}

type internalHandler struct {
	logger *zap.SugaredLogger
	oss    service.OSS
	conf   *ucfg.Config
}

func NewInternalHandler(conf *ucfg.Config) Internal {
	return &internalHandler{
		logger: log.Logger.Named("Mock"),
		oss: service.OSS{
			NMP:    conf.OSS.NMPUrl,
			AppId:  conf.Sentry.AppId,
			Logger: log.Logger.Named("OSS"),
		},
		conf: conf,
	}
}

func (i *internalHandler) QueryPeople() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.QueryPeopleResponse

		fuzzyName := c.Query("fuzzy_name")
		peopleList, total, err := common.QueryPeople(c, fuzzyName)
		if err != nil {
			log.CtxLog(c).Errorf("fail to query people, err: %v, fuzzy_name: %s", err, fuzzyName)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data.PeopleList = peopleList
		response.Data.Total = int64(total)
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

type QueryPeopleResp struct {
	ResultCode string `json:"result_code"`
	Data       struct {
		Amount int `json:"amount"`
		List   []struct {
			EmployeeId         string  `json:"employee_id"`
			WorkerUserId       string  `json:"worker_user_id"`
			Name               string  `json:"name"`
			Domain             string  `json:"domain"`
			EmployeeStatus     string  `json:"employee_status"`
			DataDifference     string  `json:"data_difference"`
			EmployeeDifference string  `json:"employee_difference"`
			Country            *string `json:"country"`
			Area               *string `json:"area"`
		} `json:"list"`
	} `json:"data"`
	Message    string `json:"message"`
	DebugMsg   string `json:"debug_msg"`
	DisplayMsg string `json:"display_msg"`
	RequestId  string `json:"request_id"`
	Path       string `json:"path"`
	TxId       string `json:"tx_id"`
	ServerTime int    `json:"server_time"`
}

func (i *internalHandler) IssueCommand2Station(area string) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.Response
		)
		requestData := struct {
			AppId    int                    `json:"app_id" binding:"required"`
			Key      string                 `json:"key" binding:"required"`
			DeviceId string                 `json:"device_id" binding:"required"`
			Data     map[string]interface{} `json:"data" binding:"required"`
		}{}
		if err := c.BindJSON(&requestData); err != nil {
			log.CtxLog(c).Errorf("failed to parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		requestId := xid.New().String()
		err := i.oss.IssueCommand(requestData.AppId, requestData.DeviceId, requestId, requestData.Key, requestData.Data)
		if err != nil {
			log.CtxLog(c).Errorf("failed to issue command to station, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = map[string]interface{}{
			"request_id": requestId,
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (i *internalHandler) GenerateNewSign() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.Response
		)
		requestData := struct {
			AppId       *string                `json:"app_id"`                    // 默认为"101021"
			AppSecret   *string                `json:"app_secret"`                // 根据默认appId取默认appSecret
			ContentType *string                `json:"content_type"`              // 默认为application/json
			Method      string                 `json:"method" binding:"required"` // e.g. POST, GET
			Path        string                 `json:"path" binding:"required"`   // such as "/service/file/getUploadToken"
			URLParams   map[string]interface{} `json:"url_params"`
			BodyParams  map[string]interface{} `json:"body_params" binding:"required"`
		}{}
		if err := c.BindJSON(&requestData); err != nil {
			log.CtxLog(c).Errorf("failed to parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		ts := time.Now().Unix()
		sign := ucmd.Sign{
			Timestamp:  ts,
			Method:     requestData.Method,
			Path:       requestData.Path,
			BodyParams: requestData.BodyParams,
		}
		var appId, appSecret, contentType string
		if requestData.AppId == nil {
			appId = i.conf.Sentry.AppId
		} else {
			appId = *requestData.AppId
		}
		if requestData.AppSecret == nil {
			appSecret = i.conf.Sentry.AppSecret
		} else {
			appSecret = *requestData.AppSecret
		}
		if requestData.ContentType == nil {
			contentType = "application/json"
		} else {
			contentType = *requestData.ContentType
		}
		if appId == "" || appSecret == "" || contentType == "" {
			log.CtxLog(c).Errorf("`app_id`, `app_secret` and `content_type` cannot be empty!")
			um.FailWithBadRequest(c, &response, "`app_id`, `app_secret` and `content_type` cannot be empty!")
			return
		}
		sign.AppId = appId
		sign.AppSecret = appSecret
		sign.ContentType = contentType
		if requestData.URLParams != nil {
			sign.URLParams = requestData.URLParams
		}
		response.Data = map[string]interface{}{
			"timestamp": ts,
			"sign":      sign.Generate(),
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (i *internalHandler) RequestWithSign() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.Response
		)
		requestData := struct {
			AppId       *string                `json:"app_id"`                    // 默认为"101021"
			AppSecret   *string                `json:"app_secret"`                // 根据默认appId取默认appSecret
			ContentType *string                `json:"content_type"`              // 默认为application/json
			Host        string                 `json:"host" binding:"required"`   // such as 'https://fms-test.nioint.com'
			Method      string                 `json:"method" binding:"required"` // e.g. POST, GET
			Path        string                 `json:"path" binding:"required"`   // such as "/service/file/getUploadToken"
			URLParams   map[string]interface{} `json:"url_params"`
			BodyParams  map[string]interface{} `json:"body_params" binding:"required"`
		}{}
		if err := c.BindJSON(&requestData); err != nil {
			log.CtxLog(c).Errorf("failed to parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		ts := time.Now().Unix()
		sign := ucmd.Sign{
			Timestamp:  ts,
			Method:     requestData.Method,
			Path:       requestData.Path,
			BodyParams: requestData.BodyParams,
		}
		var appId, appSecret, contentType string
		if requestData.AppId == nil {
			appId = i.conf.Sentry.AppId
		} else {
			appId = *requestData.AppId
		}
		if requestData.AppSecret == nil {
			appSecret = i.conf.Sentry.AppSecret
		} else {
			appSecret = *requestData.AppSecret
		}
		if requestData.ContentType == nil {
			contentType = "application/json"
		} else {
			contentType = *requestData.ContentType
		}
		if appId == "" || appSecret == "" || contentType == "" {
			log.CtxLog(c).Errorf("`app_id`, `app_secret` and `content_type` cannot be empty!")
			um.FailWithBadRequest(c, &response, "`app_id`, `app_secret` and `content_type` cannot be empty!")
			return
		}
		sign.AppId = appId
		sign.AppSecret = appSecret
		sign.ContentType = contentType
		var urlPartition string
		if requestData.URLParams != nil {
			sign.URLParams = requestData.URLParams
			for k, v := range requestData.URLParams {
				urlPartition += fmt.Sprintf("&%s=%v", k, v)
			}
		}
		sn := sign.Generate()
		if sn == "" {
			log.CtxLog(c).Errorf("RequestWithSign: %v", "`sign` is empty")
			um.FailWithBadRequest(c, &response, "`sign` is empty")
			return
		}
		url := fmt.Sprintf("%s%s?app_id=%s&sign=%s&timestamp=%d", requestData.Host, requestData.Path, appId, sn, ts)
		if urlPartition != "" {
			url += urlPartition
		}
		ct := ucmd.NewHttpClient(ucmd.HttpClient{
			URL:    url,
			Method: requestData.Method,
			Header: map[string]string{
				"Content-Type": contentType,
			},
			RequestBody: requestData.BodyParams,
		})
		body, statusCode, err := ct.Do()
		if err != nil {
			log.CtxLog(c).Errorf("failed to http request, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		defer body.Close()
		data, dErr := io.ReadAll(body)
		if dErr != nil {
			log.CtxLog(c).Errorf("failed to http request, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if statusCode != http.StatusOK {
			log.CtxLog(c).Errorf("failed to http request, err: %s", string(data))
			um.FailWithInternalServerError(c, &response, string(data))
			return
		}

		var result map[string]interface{}
		if err = json.Unmarshal(data, &result); err != nil {
			log.CtxLog(c).Errorf("failed to unmarshal http response, err: %s", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = result
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (i *internalHandler) CsvParse() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.Response
		)
		file, err := c.FormFile("file")
		if err != nil {
			log.CtxLog(c).Errorf("c.FormFile err. err: %v", err)
			um.FailWithInternalServerError(c, &model.Response{}, err.Error())
			return
		}
		MaxSize := int64(20 * 1024 * 1024)
		if file.Size > MaxSize {
			log.CtxLog(c).Errorf("file size is limit , get size: %v", file.Size)
			um.FailWithBadRequest(c, &response, "file size is limited")
			return
		}
		if file.Header.Get("Content-Type") != "text/csv" {
			log.CtxLog(c).Errorf("file is not text/csv ,type: %v", file.Header.Get("Content-Type"))
			um.FailWithBadRequest(c, &response, fmt.Sprintf("file is not text/csv ,type: %v", file.Header.Get("Content-Type")))
			return
		}
		f, err := file.Open()
		if err != nil {
			log.CtxLog(c).Errorf("file.Open err. err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		csvReader := csv.NewReader(f)
		csvParseResult := CsvParseResult{
			FileName:    file.Filename,
			ContentType: file.Header.Get("Content-Type"),
			Size:        file.Size,
			CsvData:     []map[string]string{},
		}
		idx := 0
		csvHeader := []string{}
		for {
			idx++
			record, err := csvReader.Read()
			if err == io.EOF {
				break
			}
			if err != nil {
				log.CtxLog(c).Errorf("csvReader.Read err. err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}

			// 跳过表头
			if idx == 1 {
				for _, s := range record {
					csvHeader = append(csvHeader, s)
				}
				continue
			}
			rowData := map[string]string{}
			for i, value := range record {
				rowData[csvHeader[i]] = value
			}
			csvParseResult.CsvData = append(csvParseResult.CsvData, rowData)
		}
		response.Data = csvParseResult
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

type CsvParseResult struct {
	FileName    string              `json:"file_name"`
	ContentType string              `json:"content_type"`
	Size        int64               `json:"size"`
	CsvData     []map[string]string `json:"csv_data"`
}

func (i *internalHandler) GetElectricityPrice() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  common.GetElectricityPriceRequest
			response common.GetElectricityPriceResponse
		)
		if ucmd.GetArea() == "Europe" {
			um.FailWithBadRequest(c, &response, "unsupported area")
			return
		}
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		total, res, err := common.FindElectricityPrice(c, request)
		if err != nil {
			log.CtxLog(c).Errorf("fail to find electricity price, err: %v, request: %s", err, ucmd.ToJsonStrIgnoreErr(request))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = res
		response.Total = total
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

// GetElectricityByOrganized 获取组织好的电价
// 省市 电价类型 电压 单一/两部制
func (i *internalHandler) GetElectricityByOrganized() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response common.GetElectricityByOrganizedResponse
		)
		if ucmd.GetArea() == "Europe" {
			um.FailWithBadRequest(c, &response, "unsupported area")
			return
		}

		response, err := i.getAllNewestElectricityDataFromCache(c)
		if err != nil {
			log.CtxLog(c).Errorf("fail to getAllNewestElectricityDataFromCache, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

var ElectricityDataCache *cache.Cache

func (i *internalHandler) getAllNewestElectricityDataFromCache(ctx context.Context) (common.GetElectricityByOrganizedResponse, error) {
	if ElectricityDataCache == nil {
		ElectricityDataCache = cache.New(time.Hour, cache.NoExpiration)
	}
	v, exist := ElectricityDataCache.Get("response_cache")
	if exist {
		return v.(common.GetElectricityByOrganizedResponse), nil
	}

	err := i.refreshElectricityDataCache(ctx)
	if err != nil {
		return common.GetElectricityByOrganizedResponse{}, err
	}
	v, exist = ElectricityDataCache.Get("response_cache")
	if exist {
		return v.(common.GetElectricityByOrganizedResponse), nil
	}
	return common.GetElectricityByOrganizedResponse{}, errors.New("cannot load data")
}

//电价类型
//- 大工业：big_industry
//- 一般工商业：general_business
//- 非居民用电：no_resident
//- 代理购电转供电：power_purchase_to_power_supply
//电压等级
//- >110kv: over_110
//- >35kv: over_thirty_five
//- 1~10kv: one_ten
//- <1kv: below_one
//计费方式
//- 单一制: single
//- 两部制: double

var (
	PriceTypeMap = map[string]string{
		"big_industry":                   "大工业",
		"general_business":               "一般工商业",
		"no_resident":                    "非居民用电",
		"power_purchase_to_power_supply": "代理购电转供电",
	}
	VoltageLevelMap = map[string]string{
		"over_110":         ">110kv",
		"over_thirty_five": ">35kv",
		"one_ten":          "1~10kv",
		"below_one":        "<1kv",
	}
	BillMethodMap = map[string]string{
		"single": "单一制",
		"double": "两部制",
	}
)

func (i *internalHandler) refreshElectricityDataCache(ctx context.Context) error {
	mongoElectricityPriceData, err := i.getAllNewestElectricityDataFromDB(ctx)
	if err != nil {
		return err
	}
	resp := common.GetElectricityByOrganizedResponse{
		Data: map[string]map[string]map[string]map[string]map[string][]common.ElectricityPriceInfo{},
	}
	for _, priceData := range mongoElectricityPriceData {
		resp.Month = priceData.Month
		_, found := resp.Data[priceData.ProvinceName]
		if !found {
			resp.Data[priceData.ProvinceName] = map[string]map[string]map[string]map[string][]common.ElectricityPriceInfo{}
		}
		district := priceData.DistrictSplit
		if district == "" {
			district = priceData.ProvinceName
		}
		_, found = resp.Data[priceData.ProvinceName][district]
		if !found {
			resp.Data[priceData.ProvinceName][district] = map[string]map[string]map[string][]common.ElectricityPriceInfo{}
		}
		priceType, found := PriceTypeMap[priceData.PriceType]
		if !found {
			continue
		}
		_, found = resp.Data[priceData.ProvinceName][district][priceType]
		if !found {
			resp.Data[priceData.ProvinceName][district][priceType] = map[string]map[string][]common.ElectricityPriceInfo{}
		}
		voltageLevel, found := VoltageLevelMap[priceData.VoltageLevel]
		if !found {
			continue
		}
		_, found = resp.Data[priceData.ProvinceName][district][priceType][voltageLevel]
		if !found {
			resp.Data[priceData.ProvinceName][district][priceType][voltageLevel] = map[string][]common.ElectricityPriceInfo{}
		}
		billingMethod, found := BillMethodMap[priceData.BillingMethod]
		if !found {
			continue
		}
		_, found = resp.Data[priceData.ProvinceName][district][priceType][voltageLevel][billingMethod]
		if !found {
			resp.Data[priceData.ProvinceName][district][priceType][voltageLevel][billingMethod] = []common.ElectricityPriceInfo{}
		}

		priceInfos := []common.ElectricityPriceInfo{}
		for _, priceInfo := range priceData.PriceInfo {
			agentPrice, err := strconv.ParseFloat(priceInfo.AgentPrice, 64)
			if err != nil {
				continue
			}
			price, err := strconv.ParseFloat(priceInfo.Price, 64)
			if err != nil {
				continue
			}
			priceInfos = append(priceInfos, common.ElectricityPriceInfo{
				PriceStart: priceInfo.PriceStart,
				AgentPrice: math.Round(agentPrice*100) / 100,
				PriceTag:   priceInfo.PriceTag,
				PriceEnd:   priceInfo.PriceEnd,
				Price:      math.Round(price*100) / 100,
			})
		}
		sort.Slice(priceInfos, func(i, j int) bool {
			return priceInfos[i].PriceStart < priceInfos[j].PriceStart
		})
		resp.Data[priceData.ProvinceName][district][priceType][voltageLevel][billingMethod] = priceInfos
	}
	ElectricityDataCache.Set("response_cache", resp, cache.NoExpiration)
	return nil
}

func (i *internalHandler) getAllNewestElectricityDataFromDB(ctx context.Context) ([]model.MongoElectricityPriceData, error) {
	var newestPriceData model.MongoElectricityPriceData
	err := client.GetWatcher().PLCMongodb().Client.Database(umw.Algorithm).Collection("electricity_price").FindOne(ctx, bson.D{}, options.FindOne().SetSort(bson.M{"month": -1})).Decode(&newestPriceData)
	if err != nil {
		return nil, err
	}
	var priceData []model.MongoElectricityPriceData
	cur, err := client.GetWatcher().PLCMongodb().Client.Database(umw.Algorithm).Collection("electricity_price").Find(ctx, bson.D{})
	if err != nil {
		return nil, err
	}
	if err = cur.All(ctx, &priceData); err != nil {
		return nil, err
	}
	return priceData, nil
}

func (i *internalHandler) GetVehicleType() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.Response
		response.Data = model.VehicleBrandMap
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (i *internalHandler) GetBasicNameMapping() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.Response
		cityCompanySet := map[string]bool{}
		devices := welkin_cache.PowerSwapCache.GetAllDevices()
		for _, deviceInfo := range devices {
			cityCompany := deviceInfo.CityCompany
			if cityCompany == "" {
				continue
			}
			cityCompanySet[cityCompany] = true
		}
		cityCompanyList := []string{}
		for cityCompany, _ := range cityCompanySet {
			cityCompanyList = append(cityCompanyList, cityCompany)
		}
		response.Data = map[string]interface{}{
			"vehicle": model.VehicleBrandMap, // 车辆品牌、车型
			"frontend_project": map[string]string{ // 前端设备类型映射
				umw.PowerSwap:  "powerSwap",
				umw.PowerSwap2: "powerSwap2",
				umw.PUS3:       "powerSwap3",
				umw.PUS4:       "powerSwap4",
				"FYPUS1":       "firefly1",
			},
			"battery_refresh_battery_capacity": []string{"50", "60", "70", "75", "84", "85", "100", "150"},
			"car_platform":                     []string{"NT1.0", "NT1.2", "NT2.0", "NT2.2", "NT3.0"},
			"city_company":                     cityCompanyList,
			"city_company_group":               []string{"1", "2", "3", "4"},
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (i *internalHandler) GetBasicNameMappingByKey() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.Response
		key := c.Query("key")
		switch key {
		case "worksheet_status":
			w := &health.Worksheet{}
			res, err := w.GetDistinctStatus(c)
			if err != nil {
				log.CtxLog(c).Errorf("GetBasicNameMappingByKey, fail to get distinct worksheet status: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			response.Data = res
		default:
			log.CtxLog(c).Errorf("GetBasicNameMappingByKey, invalid key: %s", key)
			um.FailWithBadRequest(c, &response, "invalid key")
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (i *internalHandler) PushRedRabbitParams() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response um.Base
		err := PushRedRabbitParams()
		if err != nil {
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (i *internalHandler) PushMazuParams() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response um.Base
		err := PushMazuParams()
		if err != nil {
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (i *internalHandler) SyncDeviceProto() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response um.Base
		var request struct {
			Branches []string `json:"branches"`
		}
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("SyncDeviceProto, failed to parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		localPath := domain_git.ClonePathProtoBufPs
		gitDO := &domain_git.GitDO{}
		g := ucmd.NewErrGroup(c.Copy())
		g.GoRecover(func() error {
			return gitDO.SyncProtobufPS(c.Copy(), localPath, request.Branches...)
		})
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (i *internalHandler) SyncPowerChargeDevices() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.Response
		var requestData struct {
			ResourceType  string `json:"resource_type"`
			ResourceModel string `json:"resource_model"`
		}
		if err := c.BindJSON(&requestData); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		copyedGinCtx := c.Copy()
		go func() {
			defer ucmd.RecoverPanic()
			ossPowerChargeDevices, err := service.GetAllPowerChargeDevicesFromOSS(copyedGinCtx, requestData.ResourceType, requestData.ResourceModel)
			if err != nil {
				log.CtxLog(copyedGinCtx).Errorf("service.GetAllPowerChargeDevicesFromOSS err. err:%v", err)
				return
			}
			for _, powerChargeDevice := range ossPowerChargeDevices {
				filter := bson.D{{"resource_id", powerChargeDevice.ResourceId}}
				project := ucmd.RenameProjectOSS(powerChargeDevice.ResourceModel)
				deviceId := powerChargeDevice.ResourceId
				deviceId, err = service.GetRealDeviceId(copyedGinCtx, powerChargeDevice.ResourceId)
				if err != nil {
					log.CtxLog(copyedGinCtx).Errorf("service.GetRealDeviceId err. err:%v", err)
					continue
				}
				update := bson.M{
					"project":      project,
					"resource_id":  powerChargeDevice.ResourceId,
					"device_id":    deviceId,
					"updated_time": time.Now().UnixMilli(),
				}
				serviceState, err := strconv.ParseInt(powerChargeDevice.ServiceState, 10, 64)
				if err == nil {
					update["service_state"] = int32(serviceState)
				}
				if powerChargeDevice.Description != "" {
					update["description"] = powerChargeDevice.Description
				}
				if err = client.GetWatcher().Mongodb().UpdateOne(umw.OAuthDB, "charger_basic_info", filter, update, true); err != nil {
					log.CtxLog(copyedGinCtx).Errorf("failed to sync Power Charger v3.1: %v, update: %s", err, ucmd.ToJsonStrIgnoreErr(update))
				}
			}
		}()
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}
