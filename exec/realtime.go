package exec

import (
	"bytes"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	um "git.nevint.com/golang-libs/common-utils/model"
	umrb "git.nevint.com/golang-libs/common-utils/model/redrabbit"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	"git.nevint.com/welkin2/welkin-backend/util"
)

type Realtime interface {
	GetRealtime() gin.HandlerFunc
	RealtimeDataList() gin.HandlerFunc
	WelkinRealtimeDataList() gin.HandlerFunc
	OSSRealtimeDataList() gin.HandlerFunc
	RealtimeIdList() gin.HandlerFunc
	GetElectricityData() gin.HandlerFunc
	GetPDUKMStatus() gin.HandlerFunc
	GetBmsSocData() gin.HandlerFunc
	GetACDCData() gin.HandlerFunc
	GetSCTData() gin.HandlerFunc
}

type realtime struct {
	watcher client.Watcher
	logger  *zap.SugaredLogger
}

func NewRealtimeHandler(watcher client.Watcher) Realtime {
	logger := log.Logger.Named("Realtime")
	if err := cache.CacheRealtimeDataID(watcher.Mongodb().Client); err != nil {
		logger.Panicf("failed to cache realtime data id, err: %v", err)
	}
	return &realtime{
		watcher: watcher,
		logger:  logger,
	}
}

func (r *realtime) GetRealtime() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			requestData model.RealtimeParam
			response    model.Response
		)
		project := c.Param("project")
		deviceId := c.Param("device_id")

		if project != umw.PUS3 {
			log.CtxLog(c).Warnf("project: %s, query realtime not supported", project)
			um.FailWithNotFound(c, &response, fmt.Sprintf("project: %s, query realtime not supported", project))
			return
		}
		if err := c.BindQuery(&requestData); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if err := util.CheckTimeRange(requestData.StartTime, requestData.EndTime); err != nil {
			log.CtxLog(c).Errorf("check time range err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		if requestData.StartTime < int64(1700468400000) && requestData.EndTime > int64(1700468400000) {
			log.CtxLog(c).Errorf("start_time and end_time in invalid time range, start_time: %d, end_time: %d", requestData.StartTime, requestData.EndTime)
			um.FailWithBadRequest(c, &response, "start_time and end_time in invalid time range, start_time must be >= '2023-11-20 16:20:00' or end_time must be <= '2023-11-20 16:20:00'")
			return
		}
		tdw := &client.TDWatcher{
			TDClient:     r.watcher.TDEngine(),
			RedisClient:  r.watcher.Redis(),
			DeviceId:     deviceId,
			StartTs:      &requestData.StartTime,
			EndTs:        &requestData.EndTime,
			FilterFields: make([]string, 0),
			Descending:   true,
			Logger:       log.CtxLog(c).Named("TDEngine"),
		}
		scanStruct, dataIdFieldMap, err := tdw.GetFields("device2welkin_realtime", "realtime_pus3", requestData.DataId, false)
		if err != nil {
			log.CtxLog(c).Errorf("get realtime fields fail, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		_, rows, err := tdw.FilterDataByFields("device2welkin_realtime")
		if err != nil {
			log.CtxLog(c).Errorf("get realtime data by fields fail: %v, err: %v", tdw.FilterFields, err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}

		results := make(map[string]*model.RealtimeV1Data)
		if rows != nil {
			for rows.Next() {
				// 获取动态的查询结构体
				columns := client.ReflectFields(scanStruct)
				if err = rows.Scan(columns...); err != nil {
					log.CtxLog(c).Errorf("scan realtime data from tdengine fail, err: %v", err)
					continue
				}
				for dataId, v := range dataIdFieldMap {
					if results[dataId] == nil {
						results[dataId] = &model.RealtimeV1Data{Type: v["var_type"]}
					}
					results[dataId].Data = append(results[dataId].Data, model.RealtimeValue{
						Timestamp: scanStruct.FieldByName("Ts").Interface().(time.Time).UnixMilli(),
						Value:     scanStruct.FieldByName("R" + dataId).Interface(),
					})
				}
			}
		}
		response.Data = results
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (r *realtime) GetElectricityData() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.Response
		)
		project := c.Param("project")

		if project != umw.PUS3 {
			log.CtxLog(c).Warnf("`project`: %s is invalid", project)
			um.FailWithNotFound(c, &response, "`project` is invalid")
			return
		}
		deviceId := c.Query("device_id")
		tsStr := c.Query("ts")
		if deviceId == "" || len(tsStr) != 13 {
			log.CtxLog(c).Errorf("`device_id`: %s or `ts`: %s are required", deviceId, tsStr)
			um.FailWithBadRequest(c, &response, "`device_id` or `ts` are required")
			return
		}
		ts, err := strconv.ParseInt(tsStr, 10, 64)
		if err != nil {
			log.CtxLog(c).Errorf("`ts`: %s is invalid: %v", tsStr, err)
			um.FailWithBadRequest(c, &response, "`ts` is invalid")
			return
		}
		startTs := ts - 10000
		tdw := &client.TDWatcher{
			TDClient:     r.watcher.TDEngine(),
			RedisClient:  r.watcher.Redis(),
			DeviceId:     deviceId,
			StartTs:      &startTs, // 查询区间往前移1min
			EndTs:        &ts,
			FilterFields: make([]string, 0),
			Descending:   true,
			Logger:       log.CtxLog(c).Named("TDEngine"),
		}
		dataId := []string{"104509", "104525", "104541"}
		scanStruct, _, err := tdw.GetRealtimeFields("device2welkin_realtime", "realtime_pus3", strings.Join(dataId, ","), true)
		if err != nil {
			log.CtxLog(c).Errorf("get welkin realtime data by fields fail, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		_, rows, err := tdw.FilterDataByFields("device2welkin_realtime")
		if err != nil {
			log.CtxLog(c).Errorf("get welkin realtime data by fields fail: %v, err: %v", tdw.FilterFields, err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		results := make(map[string]interface{})
		if rows != nil {
			for rows.Next() {
				// 获取动态的查询结构体
				columns := client.ReflectFields(scanStruct)
				if err = rows.Scan(columns...); err != nil {
					log.CtxLog(c).Errorf("scan welkin realtime data from tdengine fail, err: %v", err)
					continue
				}
				results = map[string]interface{}{
					"total_power":           0,
					"line1_total_power":     scanStruct.FieldByName("R104509").Interface().(model.SafeType).GetRealValue(),
					"line2_total_power":     scanStruct.FieldByName("R104525").Interface().(model.SafeType).GetRealValue(),
					"powerswap_total_power": scanStruct.FieldByName("R104541").Interface().(model.SafeType).GetRealValue(),
				}
				// 如果找到一条，则终止
				break
			}
		}
		rawData, err := r.watcher.RbMongodb().GetOne(project, umrb.RBDevices, bson.D{
			bson.E{Key: "_id", Value: deviceId}}, client.MongoOptions{Projection: &bson.M{"params": 1}})
		if err != nil {
			log.CtxLog(c).Errorf("failed to get total power in data id: 902416， err: %v", err)
		} else if rawData != nil {
			record := struct {
				Params []struct {
					Key   int64       `json:"key" bson:"key"`
					Value interface{} `json:"value" bson:"value"`
				} `json:"params" bson:"params"`
			}{}
			if err = bson.Unmarshal(rawData, &record); err != nil {
				log.CtxLog(c).Errorf("failed to get total power in data id: 902416， err: %v", err)
			} else {
				for _, item := range record.Params {
					if item.Key == int64(902416) {
						results["total_power"] = item.Value
						break
					}
				}
			}
		}
		response.Data = results
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (r *realtime) GetPDUKMStatus() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.Response
		)
		project := c.Param("project")

		if project != umw.PUS3 {
			log.CtxLog(c).Warnf("`project`: %s is invalid", project)
			um.FailWithNotFound(c, &response, "`project` is invalid")
			return
		}
		deviceId := c.Query("device_id")
		tsStr := c.Query("ts")
		if deviceId == "" || len(tsStr) != 13 {
			log.CtxLog(c).Errorf("`device_id`: %s or `ts`: %s are required", deviceId, tsStr)
			um.FailWithBadRequest(c, &response, "`device_id` or `ts` are required")
			return
		}
		ts, err := strconv.ParseInt(tsStr, 10, 64)
		if err != nil {
			log.CtxLog(c).Errorf("`ts`: %s is invalid: %v", tsStr, err)
			um.FailWithBadRequest(c, &response, "`ts` is invalid")
			return
		}
		startTs := ts - 60000
		tdw := &client.TDWatcher{
			TDClient:     r.watcher.TDEngine(),
			RedisClient:  r.watcher.Redis(),
			DeviceId:     deviceId,
			StartTs:      &startTs, // 查询区间往前移1min
			EndTs:        &ts,
			FilterFields: make([]string, 0),
			Descending:   true,
			Logger:       log.CtxLog(c).Named("TDEngine"),
		}
		dataId := make([]string, 0)
		for id := 104300; id <= 104369; id++ {
			dataId = append(dataId, fmt.Sprintf("%d", id))
		}
		scanStruct, _, err := tdw.GetFields(umw.Device2OSSRealtime, "realtime_pus3", strings.Join(dataId, ","), false)
		if err != nil {
			log.CtxLog(c).Errorf("get pdu km fields fail, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		_, rows, err := tdw.FilterDataByFields(umw.Device2OSSRealtime)
		if err != nil {
			log.CtxLog(c).Errorf("get oss realtime data by fields fail: %v, err: %v", tdw.FilterFields, err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		results := make(map[string]int)
		if rows != nil {
			for rows.Next() {
				// 获取动态的查询结构体
				columns := client.ReflectFields(scanStruct)
				if err = rows.Scan(columns...); err != nil {
					log.CtxLog(c).Errorf("scan oss realtime data from tdengine fail, err: %v", err)
					continue
				}
				// 104300-104313 A-1#
				// 104314-104327 A-2#
				// 104328-104341 A-3#
				// 104342-104355 A-4#
				// 104356-104369 A-5#
				for index := 0; index <= 68; index = index + 2 {
					pos, neg := scanStruct.FieldByName(fmt.Sprintf("R%d", 104300+index)).Interface().(int),
						scanStruct.FieldByName(fmt.Sprintf("R%d", 104300+index+1)).Interface().(int)
					var group, number int
					q := index / 7
					if q == 0 || q == 1 {
						group, number = 1, index/2+1
					} else if q == 2 || q == 3 {
						group, number = 2, index/2-6
					} else if q == 4 || q == 5 {
						group, number = 3, index/2-13
					} else if q == 6 || q == 7 {
						group, number = 4, index/2-20
					} else if q == 8 || q == 9 {
						group, number = 5, index/2-27
					}
					if pos == 1 && neg == 1 {
						results[fmt.Sprintf("%d#_KM%d", group, number)] = 1
					} else {
						results[fmt.Sprintf("%d#_KM%d", group, number)] = 0
					}
				}
				// 如果找到一条，则终止
				break
			}
		}
		response.Data = results
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (r *realtime) GetBmsSocData() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.Response
		)
		project := c.Param("project")

		if project != umw.PUS3 {
			log.CtxLog(c).Warnf("`project`: %s is invalid", project)
			um.FailWithNotFound(c, &response, "`project` is invalid")
			return
		}
		deviceId := c.Query("device_id")
		tsStr := c.Query("ts")
		if deviceId == "" || len(tsStr) != 13 {
			log.CtxLog(c).Errorf("`device_id`: %s or `ts`: %s are required", deviceId, tsStr)
			um.FailWithBadRequest(c, &response, "`device_id` or `ts` are required")
			return
		}
		ts, err := strconv.ParseInt(tsStr, 10, 64)
		if err != nil {
			log.CtxLog(c).Errorf("`ts`: %s is invalid: %v", tsStr, err)
			um.FailWithBadRequest(c, &response, "`ts` is invalid")
			return
		}
		startTs := ts - 60000
		tdw := &client.TDWatcher{
			TDClient:     r.watcher.TDEngine(),
			RedisClient:  r.watcher.Redis(),
			DeviceId:     deviceId,
			StartTs:      &startTs, // 查询区间往前移1min
			EndTs:        &ts,
			FilterFields: make([]string, 0),
			Descending:   true,
			Logger:       log.CtxLog(c).Named("TDEngine"),
		}
		dataId := make([]string, 0)
		for id := 1; id <= 21; id++ {
			dataId = append(dataId, []string{
				fmt.Sprintf("%d", 1000*id+7),
				fmt.Sprintf("%d", 1000*id+20),
				fmt.Sprintf("%d", 1000*id+22),
				fmt.Sprintf("%d", 1000*id+23),
				fmt.Sprintf("%d", 1000*id+25),
				fmt.Sprintf("%d", 1000*id+26),
				fmt.Sprintf("%d", 1000*id+508)}...)
		}
		scanStruct, _, err := tdw.GetFields(umw.Device2OSSRealtime, "realtime_pus3", strings.Join(dataId, ","), true)
		if err != nil {
			log.CtxLog(c).Errorf("get oss realtime data by fields fail, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		_, rows, err := tdw.FilterDataByFields(umw.Device2OSSRealtime)
		if err != nil {
			log.CtxLog(c).Errorf("get oss realtime data by fields fail: %v, err: %v", tdw.FilterFields, err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		results := make([]map[string]interface{}, 0)
		if rows != nil {
			for rows.Next() {
				// 获取动态的查询结构体
				columns := client.ReflectFields(scanStruct)
				if err = rows.Scan(columns...); err != nil {
					log.CtxLog(c).Errorf("scan oss realtime data from tdengine fail, err: %v", err)
					continue
				}
				// 7: 动力电池标称总能量
				// 20: 电池包总电压
				// 22: 用户SOC
				// 23: 实际SOC
				// 25: 充电请求电压
				// 26: 充电请求电流
				// 508: bms充电状态
				for index := 1; index <= 21; index++ {
					var slot string
					if index >= 1 && index <= 10 {
						slot = fmt.Sprintf("C%d", index)
					} else {
						slot = fmt.Sprintf("A%d", index-10)
					}
					results = append(results, map[string]interface{}{
						"slot":                     slot,
						"battery_nomal_capacity":   scanStruct.FieldByName(fmt.Sprintf("R%d", 1000*index+7)).Interface().(model.SafeType).GetRealValue(),
						"battery_pack_voltage":     scanStruct.FieldByName(fmt.Sprintf("R%d", 1000*index+20)).Interface().(model.SafeType).GetRealValue(),
						"bms_user_soc":             scanStruct.FieldByName(fmt.Sprintf("R%d", 1000*index+22)).Interface().(model.SafeType).GetRealValue(),
						"bms_real_soc":             scanStruct.FieldByName(fmt.Sprintf("R%d", 1000*index+23)).Interface().(model.SafeType).GetRealValue(),
						"charging_request_voltage": scanStruct.FieldByName(fmt.Sprintf("R%d", 1000*index+25)).Interface().(model.SafeType).GetRealValue(),
						"charging_request_current": scanStruct.FieldByName(fmt.Sprintf("R%d", 1000*index+26)).Interface().(model.SafeType).GetRealValue(),
						"charging_status":          scanStruct.FieldByName(fmt.Sprintf("R%d", 1000*index+508)).Interface().(model.SafeType).GetRealValue(),
					})
				}
				// 如果找到一条，则终止
				break
			}
		}
		response.Data = results
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (r *realtime) GetACDCData() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.Response
		)
		project := c.Param("project")

		if project != umw.PUS3 {
			log.CtxLog(c).Warnf("`project`: %s is invalid", project)
			um.FailWithNotFound(c, &response, "`project` is invalid")
			return
		}
		deviceId := c.Query("device_id")
		tsStr := c.Query("ts")
		if deviceId == "" || len(tsStr) != 13 {
			log.CtxLog(c).Errorf("`device_id`: %s or `ts`: %s are required", deviceId, tsStr)
			um.FailWithBadRequest(c, &response, "`device_id` or `ts` are required")
			return
		}
		ts, err := strconv.ParseInt(tsStr, 10, 64)
		if err != nil {
			log.CtxLog(c).Errorf("`ts`: %s is invalid: %v", tsStr, err)
			um.FailWithBadRequest(c, &response, "`ts` is invalid")
			return
		}
		startTs := ts - 60000
		tdw := &client.TDWatcher{
			TDClient:     r.watcher.TDEngine(),
			RedisClient:  r.watcher.Redis(),
			DeviceId:     deviceId,
			StartTs:      &startTs, // 查询区间往前移1min
			EndTs:        &ts,
			FilterFields: make([]string, 0),
			Descending:   true,
			Logger:       log.CtxLog(c).Named("TDEngine"),
		}
		dataId := make([]string, 0)
		for id := 0; id < 30; id++ {
			for i := 3; i <= 12; i++ {
				// 输出电压: 100000+id*30+3
				// 输出电流: 100000+id*30+4
				if i == 10 {
					continue
				}
				dataId = append(dataId, fmt.Sprintf("%d", 100000+id*30+i))
			}
		}
		scanStruct, _, err := tdw.GetRealtimeFields("device2welkin_realtime", "realtime_pus3", strings.Join(dataId, ","), true)
		if err != nil {
			log.CtxLog(c).Errorf("get welkin realtime data by fields fail, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		_, rows, err := tdw.FilterDataByFields("device2welkin_realtime")
		if err != nil {
			log.CtxLog(c).Errorf("get welkin realtime data by fields fail: %v, err: %v", tdw.FilterFields, err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		results := make(map[string][]map[string]interface{}, 0)
		for i := 1; i <= 10; i++ {
			results[fmt.Sprintf("ACDC%d", i)] = make([]map[string]interface{}, 0)
		}
		if rows != nil {
			for rows.Next() {
				// 获取动态的查询结构体
				columns := client.ReflectFields(scanStruct)
				if err = rows.Scan(columns...); err != nil {
					log.CtxLog(c).Errorf("scan welkin realtime data from tdengine fail, err: %v", err)
					continue
				}
				for id := 0; id < 30; id++ {
					index := fmt.Sprintf("ACDC%d", id/3+1)
					results[index] = append(results[index], map[string]interface{}{
						"module":         fmt.Sprintf("AC/DC%d", id+1),
						"output_voltage": scanStruct.FieldByName(fmt.Sprintf("R%d", 100000+id*30+3)).Interface().(model.SafeType).GetRealValue(),
						"output_current": scanStruct.FieldByName(fmt.Sprintf("R%d", 100000+id*30+4)).Interface().(model.SafeType).GetRealValue(),
					})
				}
				// 如果找到一条，则终止
				break
			}
		}
		response.Data = results
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (r *realtime) GetSCTData() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.Response
		)
		project := c.Param("project")

		if project != umw.PUS3 {
			log.CtxLog(c).Warnf("`project`: %s is invalid", project)
			um.FailWithNotFound(c, &response, "`project` is invalid")
			return
		}
		deviceId := c.Query("device_id")
		tsStr := c.Query("ts")
		if deviceId == "" || len(tsStr) != 13 {
			log.CtxLog(c).Errorf("`device_id`: %s or `ts`: %s are required", deviceId, tsStr)
			um.FailWithBadRequest(c, &response, "`device_id` or `ts` are required")
			return
		}
		ts, err := strconv.ParseInt(tsStr, 10, 64)
		if err != nil {
			log.CtxLog(c).Errorf("`ts`: %s is invalid: %v", tsStr, err)
			um.FailWithBadRequest(c, &response, "`ts` is invalid")
			return
		}
		startTs := ts - 60000
		tdw := &client.TDWatcher{
			TDClient:     r.watcher.TDEngine(),
			RedisClient:  r.watcher.Redis(),
			DeviceId:     deviceId,
			StartTs:      &startTs, // 查询区间往前移1min
			EndTs:        &ts,
			FilterFields: make([]string, 0),
			Descending:   true,
			Logger:       log.CtxLog(c).Named("TDEngine"),
		}
		dataId := make([]string, 0)
		for index := 0; index < 4; index++ {
			// 电表输出电压: 200000+index*80+13
			// 电表输出电流: 200000+index*80+14
			// 输出功率: 200000+index*80+26
			// bms请求电压: 200000+index*80+27
			// bms请求电流: 200000+index*80+28
			// bms当前soc: 200000+index*80+29
			dataId = append(dataId, []string{
				fmt.Sprintf("%d", 200000+index*80+13), fmt.Sprintf("%d", 200000+index*80+14),
				fmt.Sprintf("%d", 200000+index*80+26), fmt.Sprintf("%d", 200000+index*80+27),
				fmt.Sprintf("%d", 200000+index*80+28), fmt.Sprintf("%d", 200000+index*80+29)}...)
		}
		scanStruct, _, err := tdw.GetFields(umw.Device2OSSRealtime, "realtime_pus3", strings.Join(dataId, ","), true)
		if err != nil {
			log.CtxLog(c).Errorf("get oss realtime data by fields fail, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		_, rows, err := tdw.FilterDataByFields(umw.Device2OSSRealtime)
		if err != nil {
			log.CtxLog(c).Errorf("get oss realtime data by fields fail: %v, err: %v", tdw.FilterFields, err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		results := make(map[string]map[string]interface{}, 0)
		for i := 1; i <= 4; i++ {
			results[fmt.Sprintf("SCT%d", i)] = make(map[string]interface{})
		}
		if rows != nil {
			for rows.Next() {
				// 获取动态的查询结构体
				columns := client.ReflectFields(scanStruct)
				if err = rows.Scan(columns...); err != nil {
					log.CtxLog(c).Errorf("scan oss realtime data from tdengine fail, err: %v", err)
					continue
				}
				for index := 0; index < 4; index++ {
					results[fmt.Sprintf("SCT%d", index+1)] = map[string]interface{}{
						"output_voltage":      scanStruct.FieldByName(fmt.Sprintf("R%d", 200000+index*80+13)).Interface().(model.SafeType).GetRealValue(),
						"output_current":      scanStruct.FieldByName(fmt.Sprintf("R%d", 200000+index*80+14)).Interface().(model.SafeType).GetRealValue(),
						"output_energy":       scanStruct.FieldByName(fmt.Sprintf("R%d", 200000+index*80+26)).Interface().(model.SafeType).GetRealValue(),
						"bms_request_voltage": scanStruct.FieldByName(fmt.Sprintf("R%d", 200000+index*80+27)).Interface().(model.SafeType).GetRealValue(),
						"bms_request_current": scanStruct.FieldByName(fmt.Sprintf("R%d", 200000+index*80+28)).Interface().(model.SafeType).GetRealValue(),
						"bms_current_soc":     scanStruct.FieldByName(fmt.Sprintf("R%d", 200000+index*80+29)).Interface().(model.SafeType).GetRealValue(),
					}
				}
				// 如果找到一条，则终止
				break
			}
		}
		response.Data = results
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (r *realtime) RealtimeDataList() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			requestData model.RealtimeParam
			response    model.AddTotalResponse
		)
		project := c.Param("project")
		deviceId := c.Param("device_id")

		if project != umw.PUS3 && project != umw.PUS4 && project != umw.FYPUS1 {
			log.CtxLog(c).Warnf("project: %s, query realtime not supported", project)
			um.FailWithNotFound(c, &response, fmt.Sprintf("project: %s, query realtime not supported", project))
			return
		}
		if err := c.BindQuery(&requestData); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if err := util.CheckTimeRange(requestData.StartTime, requestData.EndTime); err != nil {
			log.CtxLog(c).Errorf("check time range err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		if !requestData.Download {
			if requestData.Page == 0 {
				requestData.Page = 1
			}
			if requestData.Size == 0 {
				requestData.Size = 2000
			}
		}

		// 获取实时数据点
		getRealtimeData := func(dbName, dataIds string, getAll bool) ([]interface{}, int64, error) {
			tdw := &client.TDWatcher{
				TDClient:     r.watcher.TDEngine(),
				RedisClient:  r.watcher.Redis(),
				DeviceId:     deviceId,
				StartTs:      &requestData.StartTime,
				EndTs:        &requestData.EndTime,
				Descending:   requestData.Descending,
				FilterFields: make([]string, 0),
				Logger:       log.CtxLog(c).Named("TDEngine"),
			}
			if !getAll {
				tdw.Offset = (requestData.Page - 1) * requestData.Size
				tdw.Limit = requestData.Size
			}
			stableName := fmt.Sprintf("realtime_%s", ucmd.RenameProjectDB(project))
			scanStruct, dataIdMap, err := tdw.GetRealtimeFields(dbName, stableName, dataIds, true)
			if err != nil {
				log.CtxLog(c).Errorf("get realtime fields fail, err: %v", err)
				return nil, 0, err
			}
			//fmt.Printf("data id map: %s\n", ucmd.ToJsonStrIgnoreErr(dataIdMap))
			total, rows, err := tdw.FilterDataByFields(dbName)
			if err != nil {
				log.CtxLog(c).Errorf("get realtime data by fields fail: %v, err: %v", tdw.FilterFields, err)
				return nil, 0, err
			}

			results := make([]interface{}, 0)
			if rows != nil {
				for rows.Next() {
					// 获取动态的查询结构体
					columns := client.ReflectFields(scanStruct)
					if err = rows.Scan(columns...); err != nil {
						log.CtxLog(c).Errorf("scan realtime data from tdengine fail, err: %v", err)
						continue
					}
					result := map[string]interface{}{
						"timestamp": scanStruct.FieldByName("Ts").Interface().(time.Time).UnixMilli(),
					}
					validDataIdMap := map[string]bool{}
					for dataId := range dataIdMap {
						data := scanStruct.FieldByName("R" + dataId).Interface().(model.SafeType)
						result[dataId] = data.GetRealValue()
						if data.IsValid() {
							validDataIdMap[dataId] = true
						} else {
							validDataIdMap[dataId] = false
						}
					}
					// 全部数据点不合法的时间点 剔除
					allDataIdInvalid := true
					for dataId := range dataIdMap {
						if validDataIdMap[dataId] == true {
							allDataIdInvalid = false
						}
					}
					if !allDataIdInvalid {
						results = append(results, result)
					}
				}
			}
			return results, total, nil
		}

		var ossDataId, welkinDataId []string
		for _, id := range strings.Split(requestData.DataId, ",") {
			if project == umw.PUS3 {
				if _, ok := cache.OSSRealtimePS3Data[id]; ok {
					ossDataId = append(ossDataId, id)
					continue
				}
				if _, ok := cache.WelkinRealtimePS3Data[id]; ok {
					welkinDataId = append(welkinDataId, id)
					continue
				}
			} else if project == umw.PUS4 {
				if _, ok := cache.WelkinRealtimePS4Data[id]; ok {
					welkinDataId = append(welkinDataId, id)
					continue
				}
			} else if project == umw.FYPUS1 {
				if _, ok := cache.WelkinRealtimeFY1Data[id]; ok {
					welkinDataId = append(welkinDataId, id)
					continue
				}
			}
		}
		getAll := true
		if len(ossDataId) == 0 || len(welkinDataId) == 0 {
			getAll = false
		}
		var results []interface{}
		if len(ossDataId) != 0 {
			dbName := "device2oss_realtime"
			records, total, err := getRealtimeData(dbName, strings.Join(ossDataId, ","), getAll)
			if err != nil {
				log.CtxLog(c).Errorf("fail to get oss realtime data, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			results = append(results, records...)
			response.Total += int(total)
		}
		if len(welkinDataId) != 0 {
			dbName := "device2welkin_realtime"
			records, total, err := getRealtimeData(dbName, strings.Join(welkinDataId, ","), getAll)
			if err != nil {
				log.CtxLog(c).Errorf("fail to get welkin realtime data, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			results = append(results, records...)
			response.Total += int(total)
		}
		if getAll {
			sort.Slice(results, func(i, j int) bool {
				result1, ok := results[i].(map[string]interface{})
				if !ok {
					return false
				}
				result2, ok := results[j].(map[string]interface{})
				if !ok {
					return false
				}
				ts1, ok := result1["timestamp"].(int64)
				if !ok {
					return false
				}
				ts2, ok := result2["timestamp"].(int64)
				if !ok {
					return false
				}
				return ts1 > ts2
			})
			startIndex, endIndex := (requestData.Page-1)*requestData.Size, requestData.Page*requestData.Size
			if startIndex >= len(results) {
				errMsg := fmt.Sprintf("startIndex %d too large, page: %d, size: %d", startIndex, requestData.Page, requestData.Size)
				log.CtxLog(c).Error(errMsg)
				um.FailWithBadRequest(c, &response, errMsg)
				return
			}
			if endIndex > len(results) {
				endIndex = len(results)
			}
			results = results[startIndex:endIndex]
		}

		if !requestData.Download {
			response.Data = results
			//log.CtxLog(c).Infof("succeed to list realtime data, filter fields: %v, start_time: %d, end_time: %d", tdw.FilterFields, tdw.StartTs, tdw.EndTs)
			um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
			return
		}

		// 下载数据
		headersRaw := []string{"timestamp"}
		headersRaw = append(headersRaw, welkinDataId...)
		headersRaw = append(headersRaw, ossDataId...)

		var realtimeData, ossRealtimeData, welkinRealtimeData []model.Device2CloudVariableMap
		if len(welkinDataId) > 0 {
			byteData, err := r.watcher.Mongodb().NewMongoEntry(bson.D{{"data_id", bson.M{"$in": welkinDataId}}}).ListAll(umw.Device2Cloud, fmt.Sprintf("welkin-realtime-%s", ucmd.RenameProjectDB(project)), client.Ordered{})
			if err != nil {
				log.CtxLog(c).Errorf("fail to get welkin realtime cn name, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			if err = json.Unmarshal(byteData, &welkinRealtimeData); err != nil {
				log.CtxLog(c).Errorf("fail to unmarshal welkin realtime data, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
		}
		if len(ossDataId) > 0 {
			byteData, err := r.watcher.Mongodb().NewMongoEntry(bson.D{{"data_id", bson.M{"$in": ossDataId}}}).ListAll(umw.Device2Cloud, fmt.Sprintf("oss-realtime-%s", ucmd.RenameProjectDB(project)), client.Ordered{})
			if err != nil {
				log.CtxLog(c).Errorf("fail to get oss realtime cn name, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			if err = json.Unmarshal(byteData, &ossRealtimeData); err != nil {
				log.CtxLog(c).Errorf("fail to unmarshal oss realtime data, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
		}
		realtimeData = append(welkinRealtimeData, ossRealtimeData...)

		headerMap := make(map[string]string)
		for _, rd := range realtimeData {
			headerMap[rd.DataId] = rd.VarCNName
		}
		headers := make([]string, len(headersRaw))
		headers[0] = "时间"
		for i := 1; i < len(headers); i++ {
			headers[i] = headerMap[headersRaw[i]]
		}

		records := make([][]string, len(results)+1)
		for i := range records {
			records[i] = make([]string, len(headersRaw))
		}
		records[0] = headers
		for i, result := range results {
			for j, header := range headersRaw {
				var record string
				resultMap, ok := result.(map[string]interface{})
				if !ok {
					log.CtxLog(c).Errorf("expect result to be map[string]interface{}, get value: %v, type: %T", result, result)
					break
				}
				if header == "timestamp" {
					ts, ok := resultMap[header].(int64)
					if !ok {
						log.CtxLog(c).Errorf("expect ts to be int64, get value: %v, type: %T", resultMap[header], resultMap[header])
						break
					}
					record = time.UnixMilli(ts).Format("2006-01-02 15:04:05.000")
				} else {
					record = fmt.Sprintf("%v", resultMap[header])
				}
				records[i+1][j] = record
			}
		}
		fileName := fmt.Sprintf("%s-%d.csv", deviceId, time.Now().UnixMilli())

		buffer := &bytes.Buffer{}
		buffer.WriteString("\xEF\xBB\xBF")
		cw := csv.NewWriter(buffer)
		if err := cw.WriteAll(records); err != nil {
			log.CtxLog(c).Errorf("fail to write to csv, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		c.Writer.Header().Set("Content-Type", "text/csv")
		util.Download(c, fileName, buffer.Bytes())
	}
}

func (r *realtime) RealtimeIdList() gin.HandlerFunc {
	return func(c *gin.Context) {
		response := struct {
			model.Response
			DataIdDescription map[string]string `json:"data_id_description"`
		}{}
		var colName string
		project := c.Param("project")
		if project == umw.PUS3 {
			colName = "welkin-realtime-pus3"
		} else if project == umw.PowerSwap2 {
			colName = "realtime-powerswap2"
		} else if project == umw.PUS4 {
			colName = "welkin-realtime-pus4"
		} else if project == umw.FYPUS1 {
			colName = "welkin-realtime-fypus1"
		} else {
			log.CtxLog(c).Errorf("invalid project: %s", project)
			um.FailWithBadRequest(c, &response, "invalid project")
			return
		}
		rawData, err := r.watcher.Mongodb().NewMongoEntry().ListAll(umw.Device2Cloud, colName, client.Ordered{})
		if err != nil {
			log.CtxLog(c).Errorf("failed to list all %s realtime data id, err: %v", project, err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		records := make([]model.Device2CloudVariableMap, 0)
		if err = json.Unmarshal(rawData, &records); err != nil {
			log.CtxLog(c).Errorf("failed to unmarshal all %s realtime data id, err: %v", project, err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		extraRecords := make([]model.Device2CloudVariableMap, 0)
		// 补充三代站oss-realtime-pus3表中的点
		if project == umw.PUS3 {
			neededDataId := make([]string, 0)
			// 电池相关
			for index := 1; index <= 21; index++ {
				neededDataId = append(neededDataId, fmt.Sprintf("%d012", index))
				neededDataId = append(neededDataId, fmt.Sprintf("%d021", index))
				neededDataId = append(neededDataId, fmt.Sprintf("%d022", index))
				neededDataId = append(neededDataId, fmt.Sprintf("%d023", index))
				neededDataId = append(neededDataId, fmt.Sprintf("%d026", index))
			}
			neededDataId = append(neededDataId, fmt.Sprintf("%d", 205109)) // PLC工作模式状态
			rawData, err = r.watcher.Mongodb().NewMongoEntry(bson.D{{"data_id", bson.M{"$in": neededDataId}}}).ListAll(umw.Device2Cloud, "oss-realtime-pus3", client.Ordered{})
			if err != nil {
				log.CtxLog(c).Errorf("failed to list extra %s oss realtime data id, err: %v", project, err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			if err = json.Unmarshal(rawData, &extraRecords); err != nil {
				log.CtxLog(c).Errorf("failed to unmarshal extra %s oss realtime data id, err: %v", project, err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
		}
		// 补充四代站oss-realtime-pus4表中的点
		if project == umw.PUS4 {
			// 系统遥调使能状态, 5min功率下限, 5min功率上限
			neededDataId := []string{"4371", "8300", "8301"}
			for id := 8350; id <= 8372; id++ { // 二次调频相关参数
				neededDataId = append(neededDataId, fmt.Sprintf("%d", id))
			}
			rawData, err = r.watcher.Mongodb().NewMongoEntry(bson.D{{"data_id", bson.M{"$in": neededDataId}}}).ListAll(umw.Device2Cloud, "oss-realtime-pus4", client.Ordered{})
			if err != nil {
				log.CtxLog(c).Errorf("failed to list extra %s oss realtime data id, err: %v", project, err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			if err = json.Unmarshal(rawData, &extraRecords); err != nil {
				log.CtxLog(c).Errorf("failed to unmarshal extra %s oss realtime data id, err: %v", project, err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
		}
		records = append(records, extraRecords...)

		results := make(map[string]map[string][]string)
		response.DataIdDescription = make(map[string]string)
		mu := sync.Mutex{}
		lang := c.Query("lang")
		execFunc := func(i int) {
			mu.Lock()
			if lang == "en" && records[i].VarENName != "" {
				response.DataIdDescription[records[i].DataId] = records[i].VarENName
			} else {
				response.DataIdDescription[records[i].DataId] = records[i].VarCNName
			}
			subsystem := records[i].Subsystem
			category := records[i].Category
			if lang == "en" {
				subsystem = model.DataIDClassModel[project][subsystem]
				category = model.DataIDClassModel[project][category]
			}
			if results[subsystem] == nil {
				results[subsystem] = make(map[string][]string)
			}

			if category != "" {
				if results[subsystem][category] == nil {
					results[subsystem][category] = make([]string, 0)
				}
				results[subsystem][category] = append(results[subsystem][category], records[i].DataId)
			}
			mu.Unlock()
		}
		ucmd.ParallelizeExec(len(records), execFunc)
		if project == umw.PowerSwap2 {
			if lang == "en" {
				results["Power Grid"] = powerGridDataId(project)[lang]
			} else {
				results["电网互动"] = powerGridDataId(project)[lang]
			}
		}
		response.Data = results
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func powerGridDataId(project string) (results map[string]map[string][]string) {
	results = make(map[string]map[string][]string)
	if project == umw.PowerSwap2 {
		results["zh"] = map[string][]string{"基本信息": []string{"1047"}}
		results["en"] = map[string][]string{"Basic Info": []string{"1047"}}

		for id := 605180; id <= 605187; id++ {
			if id == 605181 {
				continue
			}
			results["zh"]["基本信息"] = append(results["zh"]["基本信息"], fmt.Sprintf("%d", id))
			results["en"]["Basic Info"] = append(results["en"]["Basic Info"], fmt.Sprintf("%d", id))
		}
		for id := 605132; id <= 605139; id++ {
			if id == 605136 {
				continue
			}
			results["zh"]["基本信息"] = append(results["zh"]["基本信息"], fmt.Sprintf("%d", id))
			results["en"]["Basic Info"] = append(results["en"]["Basic Info"], fmt.Sprintf("%d", id))
		}

		for index := 1; index <= 13; index++ {
			if results["zh"][fmt.Sprintf("%d#电池仓", index)] == nil {
				results["zh"][fmt.Sprintf("%d#电池仓", index)] = make([]string, 0)
			}
			if results["en"][fmt.Sprintf("%d#Battery Slot", index)] == nil {
				results["en"][fmt.Sprintf("%d#Battery Slot", index)] = make([]string, 0)
			}
			results["zh"][fmt.Sprintf("%d#电池仓", index)] = append(results["zh"][fmt.Sprintf("%d#电池仓", index)],
				[]string{fmt.Sprintf("%d", 605139+index), fmt.Sprintf("%d", 605187+index), fmt.Sprintf("%d", 500105+index*1000),
					fmt.Sprintf("%d", 500108+index*1000), fmt.Sprintf("%d", 500134+index*1000)}...) // 电池分配充电功率,支路限流功率,电池充电功率,电池SOC,电池长时充电功率限值
			results["en"][fmt.Sprintf("%d#Battery Slot", index)] = append(results["en"][fmt.Sprintf("%d#Battery Slot", index)],
				[]string{fmt.Sprintf("%d", 605139+index), fmt.Sprintf("%d", 605187+index), fmt.Sprintf("%d", 500105+index*1000),
					fmt.Sprintf("%d", 500108+index*1000), fmt.Sprintf("%d", 500134+index*1000)}...)
		}
	}
	return
}

func (r *realtime) WelkinRealtimeDataList() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			requestData model.RealtimeParam
			response    model.AddTotalResponse
		)
		project := c.Param("project")
		deviceId := c.Param("device_id")

		if project != umw.PUS3 {
			log.CtxLog(c).Warnf("project: %s, query realtime not supported", project)
			um.FailWithNotFound(c, &response, fmt.Sprintf("project: %s, query realtime not supported", project))
			return
		}
		if err := c.BindQuery(&requestData); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if err := util.CheckTimeRange(requestData.StartTime, requestData.EndTime); err != nil {
			log.CtxLog(c).Errorf("check time range err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		if !requestData.Download {
			if requestData.Page == 0 {
				requestData.Page = 1
			}
			if requestData.Size == 0 {
				requestData.Size = 2000
			}
		}
		tdw := &client.TDWatcher{
			TDClient:     r.watcher.TDEngine(),
			RedisClient:  r.watcher.Redis(),
			DeviceId:     deviceId,
			StartTs:      &requestData.StartTime,
			EndTs:        &requestData.EndTime,
			FilterFields: make([]string, 0),
			Offset:       (requestData.Page - 1) * requestData.Size,
			Limit:        requestData.Size,
			Descending:   requestData.Descending,
			Logger:       log.CtxLog(c).Named("TDEngine"),
		}
		stbName := fmt.Sprintf("realtime_%s", strings.ToLower(project))
		scanStruct, dataIdMap, err := tdw.GetRealtimeFields("device2welkin_realtime", stbName, requestData.DataId, true)
		if err != nil {
			log.CtxLog(c).Errorf("get realtime fields fail, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		total, rows, err := tdw.FilterDataByFields("device2welkin_realtime")
		if err != nil {
			log.CtxLog(c).Errorf("get realtime data by fields fail: %v, err: %v", tdw.FilterFields, err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Total = int(total)

		results := make([]interface{}, 0)
		if rows != nil {
			for rows.Next() {
				// 获取动态的查询结构体
				columns := client.ReflectFields(scanStruct)
				if err = rows.Scan(columns...); err != nil {
					log.CtxLog(c).Errorf("scan realtime data from tdengine fail, err: %v", err)
					continue
				}
				result := map[string]interface{}{
					"timestamp": scanStruct.FieldByName("Ts").Interface().(time.Time).UnixMilli(),
				}
				for dataId := range dataIdMap {
					result[dataId] = scanStruct.FieldByName("R" + dataId).Interface().(model.SafeType).GetRealValue()
				}
				results = append(results, result)
			}
		}
		if !requestData.Download {
			response.Data = results
			log.CtxLog(c).Infof("succeed to list welkin realtime data, filter fields: %v, start_time: %d, end_time: %d", tdw.FilterFields, tdw.StartTs, tdw.EndTs)
			um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
			return
		} else {
			headersRaw := []string{"timestamp"}
			for dataId := range dataIdMap {
				headersRaw = append(headersRaw, dataId)
			}
			collectionName := fmt.Sprintf("welkin-realtime-%s", ucmd.RenameProjectDB(project))
			byteData, err := r.watcher.Mongodb().NewMongoEntry(bson.D{{"data_id", bson.M{"$in": headersRaw}}}).ListAll(umw.Device2Cloud, collectionName, client.Ordered{})
			if err != nil {
				log.CtxLog(c).Errorf("fail to get cn name, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			var realtimeData []model.Device2CloudVariableMap
			if err = json.Unmarshal(byteData, &realtimeData); err != nil {
				log.CtxLog(c).Errorf("fail to unmarshal realtime data, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			headerMap := make(map[string]string)
			for _, rd := range realtimeData {
				headerMap[rd.DataId] = rd.VarCNName
			}
			headers := make([]string, len(headersRaw))
			headers[0] = "时间"
			for i := 1; i < len(headers); i++ {
				headers[i] = headerMap[headersRaw[i]]
			}

			records := make([][]string, len(results)+1)
			for i := range records {
				records[i] = make([]string, len(dataIdMap)+1)
			}
			records[0] = headers
			for i, result := range results {
				for j, header := range headersRaw {
					var record string
					resultMap, ok := result.(map[string]interface{})
					if !ok {
						log.CtxLog(c).Errorf("expect result to be map[string]interface{}, get value: %v", result)
						break
					}
					if header == "timestamp" {
						ts, ok := resultMap[header].(int64)
						if !ok {
							log.CtxLog(c).Errorf("expect ts to be int64, get value: %v", result)
							break
						}
						record = time.UnixMilli(ts).Format("2006-01-02 15:04:05.000")
					} else {
						record = fmt.Sprintf("%v", resultMap[header])
					}
					records[i+1][j] = record
				}
			}
			fileName := fmt.Sprintf("%s-%d.csv", deviceId, time.Now().UnixMilli())

			buffer := &bytes.Buffer{}
			buffer.WriteString("\xEF\xBB\xBF")
			cw := csv.NewWriter(buffer)
			if err = cw.WriteAll(records); err != nil {
				log.CtxLog(c).Errorf("fail to write to csv, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			c.Writer.Header().Set("Content-Type", "text/csv")
			util.Download(c, fileName, buffer.Bytes())
		}
		return
	}
}

func (r *realtime) OSSRealtimeDataList() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			requestData model.RealtimeParam
			response    model.AddTotalResponse
		)
		project := c.Param("project")
		deviceId := c.Param("device_id")

		if project != umw.PUS3 && project != umw.PowerSwap2 && project != umw.PUS4 {
			log.CtxLog(c).Warnf("project: %s, query realtime not supported", project)
			um.FailWithNotFound(c, &response, fmt.Sprintf("project: %s, query realtime not supported", project))
			return
		}
		if err := c.BindQuery(&requestData); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if err := util.CheckTimeRange(requestData.StartTime, requestData.EndTime); err != nil {
			log.CtxLog(c).Errorf("check time range err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if !requestData.Download {
			if requestData.Page == 0 {
				requestData.Page = 1
			}
			if requestData.Size == 0 {
				requestData.Size = 2000
			}
		}
		tdw := &client.TDWatcher{
			TDClient:     r.watcher.TDEngine(),
			RedisClient:  r.watcher.Redis(),
			DeviceId:     deviceId,
			StartTs:      &requestData.StartTime,
			EndTs:        &requestData.EndTime,
			FilterFields: make([]string, 0),
			Offset:       (requestData.Page - 1) * requestData.Size,
			Limit:        requestData.Size,
			Descending:   requestData.Descending,
			Logger:       log.CtxLog(c).Named("TDEngine"),
		}
		stbName := fmt.Sprintf("realtime_%s", strings.ToLower(project))
		scanStruct, dataIdMap, err := tdw.GetRealtimeFields("device2oss_realtime", stbName, requestData.DataId, true)
		if err != nil {
			log.CtxLog(c).Errorf("get realtime fields fail, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		total, rows, err := tdw.FilterDataByFields("device2oss_realtime")
		if err != nil {
			log.CtxLog(c).Errorf("get realtime data by fields fail: %v, err: %v", tdw.FilterFields, err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Total = int(total)

		results := make([]interface{}, 0)
		if rows != nil {
			for rows.Next() {
				// 获取动态的查询结构体
				columns := client.ReflectFields(scanStruct)
				if err = rows.Scan(columns...); err != nil {
					log.CtxLog(c).Errorf("scan realtime data from tdengine fail, err: %v", err)
					continue
				}
				result := map[string]interface{}{
					"timestamp": scanStruct.FieldByName("Ts").Interface().(time.Time).UnixMilli(),
				}
				validDataIdMap := map[string]bool{}
				for dataId := range dataIdMap {
					data := scanStruct.FieldByName("R" + dataId).Interface().(model.SafeType)
					result[dataId] = data.GetRealValue()
					if data.IsValid() {
						validDataIdMap[dataId] = true
					} else {
						validDataIdMap[dataId] = false
					}
					// 二代站伺服告警点
					if _, found := model.ServoFaultPS2[dataId]; found {
						servoFault, cErr := ConvertServoFaultCode(umw.PowerSwap2, dataId, fmt.Sprintf("%v", result[dataId]), "zh")
						if cErr != nil {
							log.CtxLog(c).Warnf("fail to convert powerswap2 servo fault code, err: %v", cErr)
						} else {
							result[dataId] = servoFault.RealCode
						}
					}
				}
				// 全部数据点不合法的时间点 剔除
				allDataIdInvalid := true
				for dataId := range dataIdMap {
					if validDataIdMap[dataId] == true {
						allDataIdInvalid = false
					}
				}
				if !allDataIdInvalid {
					results = append(results, result)
				}
			}
		}
		if !requestData.Download {
			response.Data = results
			log.CtxLog(c).Infof("succeed to list realtime data, filter fields: %v, start_time: %d, end_time: %d", tdw.FilterFields, tdw.StartTs, tdw.EndTs)
			um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
			return
		} else {
			headersRaw := []string{"timestamp"}
			for dataId := range dataIdMap {
				headersRaw = append(headersRaw, dataId)
			}
			var collectionName string
			if project == umw.PUS3 {
				collectionName = "oss-realtime-pus3"
			} else if project == umw.PowerSwap2 {
				collectionName = "realtime-powerswap2"
			} else if project == umw.PUS4 {
				collectionName = "oss-realtime-pus4"
			}
			byteData, err := r.watcher.Mongodb().NewMongoEntry(bson.D{{"data_id", bson.M{"$in": headersRaw}}}).ListAll(umw.Device2Cloud, collectionName, client.Ordered{})
			if err != nil {
				log.CtxLog(c).Errorf("fail to get cn name, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			var realtimeData []model.Device2CloudVariableMap
			if err = json.Unmarshal(byteData, &realtimeData); err != nil {
				log.CtxLog(c).Errorf("fail to unmarshal realtime data, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			headerMap := make(map[string]string)
			for _, rd := range realtimeData {
				headerMap[rd.DataId] = rd.VarCNName
			}
			headers := make([]string, len(headersRaw))
			headers[0] = "时间"
			for i := 1; i < len(headers); i++ {
				headers[i] = headerMap[headersRaw[i]]
			}

			records := make([][]string, len(results)+1)
			for i := range records {
				records[i] = make([]string, len(dataIdMap)+1)
			}
			records[0] = headers
			for i, result := range results {
				for j, header := range headersRaw {
					var record string
					resultMap, ok := result.(map[string]interface{})
					if !ok {
						log.CtxLog(c).Errorf("expect result to be map[string]interface{}, get value: %v", result)
						break
					}
					if header == "timestamp" {
						ts, ok := resultMap[header].(int64)
						if !ok {
							log.CtxLog(c).Errorf("expect ts to be int64, get value: %v", result)
							break
						}
						record = time.UnixMilli(ts).Format("2006-01-02 15:04:05.000")
					} else {
						record = fmt.Sprintf("%v", resultMap[header])
					}
					records[i+1][j] = record
				}
			}
			fileName := fmt.Sprintf("%s-%d.csv", deviceId, time.Now().UnixMilli())

			buffer := &bytes.Buffer{}
			buffer.WriteString("\xEF\xBB\xBF")
			cw := csv.NewWriter(buffer)
			if err = cw.WriteAll(records); err != nil {
				log.CtxLog(c).Errorf("fail to write to csv, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			c.Writer.Header().Set("Content-Type", "text/csv")
			util.Download(c, fileName, buffer.Bytes())
		}
		return
	}
}
