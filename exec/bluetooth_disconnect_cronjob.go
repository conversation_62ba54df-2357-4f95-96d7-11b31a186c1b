package exec

import (
	"context"
	"encoding/json"
	"errors"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	model "git.nevint.com/welkin2/welkin-backend/model/mongo"
	"git.nevint.com/welkin2/welkin-backend/service"
)

type BluetoothDisconnectStatHandler struct {
	Watcher        client.Watcher
	DateStr        string // 2024-11-22
	StartTimestamp int64
	EndTimestamp   int64
}

func (b *BluetoothDisconnectStatHandler) prepare(ctx context.Context) error {
	loc, _ := time.LoadLocation("Asia/Shanghai")
	startTime, err := time.ParseInLocation("2006-01-02", b.DateStr, loc)
	if err != nil {
		return err
	}
	endTime := startTime.Add(time.Hour * 24)
	b.StartTimestamp = startTime.UnixMilli()
	b.EndTimestamp = endTime.UnixMilli()
	return nil
}

// Process 需要幂等
// 113535,113536,113537,113538,113539,113540,113541,113542,113543,113544,113545,113546,113547,113548,113549,113550
func (b *BluetoothDisconnectStatHandler) Process(ctx context.Context) error {
	err := b.prepare(ctx)
	if err != nil {
		return err
	}
	// 统计当天的所有告警
	// "113535", "113536", "113537", "113538", "113539", "113540", "113541", "113542", "113543", "113544", "113545", "113546", "113547", "113548", "113549", "113550"
	byteData, err := b.Watcher.Mongodb().NewMongoEntry(bson.D{
		{"data_id", bson.M{"$in": []string{"113535", "113536", "113537", "113538", "113539", "113540", "113541", "113542", "113543", "113544", "113545", "113546", "113547", "113548", "113549", "113550"}}},
		{"create_ts", bson.M{"$gte": b.StartTimestamp, "$lt": b.EndTimestamp}},
	}).
		ListAll("alarminfo", "pus4", client.Ordered{})
	if err != nil {
		return err
	}
	var alarmInfoList []umw.MongoAlarmRecord
	if err = json.Unmarshal(byteData, &alarmInfoList); err != nil {
		return err
	}

	// 统计出这些告警是否发生在换电中, 并存储
	deviceAlarmCount := map[string]int64{}
	deviceAlarmNotInServiceCount := map[string]int64{}
	deviceAlarmInserviceCount := map[string]int64{}
	mongoBluetoothDisconnectAlarms := []model.MongoBluetoothDisconnectAlarm{}
	alarmHappenInServiceCount := int64(0)
	for _, alarmRecord := range alarmInfoList {
		var mongoServiceInfo umw.MongoServiceInfo
		err := b.Watcher.Mongodb().NewMongoEntry(bson.D{
			bson.E{Key: "device_id", Value: alarmRecord.DeviceId},
			{"service_start_time", bson.M{"$lt": alarmRecord.CreateTS}},
			{"service_end_time", bson.M{"$gte": alarmRecord.CreateTS}},
		}).FindOne("serviceinfo", "pus4", options.FindOne(), &mongoServiceInfo)
		if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
			return err
		}
		IsHappenInService := false
		if !errors.Is(err, mongo.ErrNoDocuments) {
			IsHappenInService = true
			alarmHappenInServiceCount++
		}
		mongoBluetoothDisconnectAlarm := model.MongoBluetoothDisconnectAlarm{
			DeviceId:          alarmRecord.DeviceId,
			Project:           "PUS4",
			AlarmId:           alarmRecord.DataId,
			IsHappenInService: IsHappenInService,
			CreateTs:          alarmRecord.CreateTS,
			ClearTs:           alarmRecord.ClearTS,
			Date:              time.Now(),
		}
		mongoBluetoothDisconnectAlarms = append(mongoBluetoothDisconnectAlarms, mongoBluetoothDisconnectAlarm)
		if IsHappenInService {
			deviceAlarmInserviceCount[alarmRecord.DeviceId]++
		} else {
			deviceAlarmNotInServiceCount[alarmRecord.DeviceId]++
		}
		deviceAlarmCount[alarmRecord.DeviceId]++
	}
	records := []interface{}{}
	for _, bluetoothDisconnectAlarm := range mongoBluetoothDisconnectAlarms {
		records = append(records, bluetoothDisconnectAlarm)
	}
	if len(records) != 0 {
		err = b.Watcher.Mongodb().NewMongoEntry().InsertMany("diagnosis-management", "bluetooth_disconnect_alarm", records,
			client.IndexOption{Name: "device_id_alarm_id_create_ts", Fields: bson.D{
				{"device_id", 1},
				{"alarm_id", 1},
				{"create_ts", 1},
			}, Unique: true},
			client.IndexOption{Name: "create_ts_device_id", Fields: bson.D{
				{"create_ts", 1},
				{"device_id", 1},
			}, Unique: false},
		)
		//if err != nil {
		//
		//	return err
		//}
	}

	// 统计当天已经激活的站
	allDevices := cache.PowerSwapCache.GetAllDevices()
	activePus4DeviceCount := int64(0)
	resourceIds := []string{}
	for _, device := range allDevices {
		if device.Project == "PUS4" {
			resourceIds = append(resourceIds, device.ResourceId)
		}
	}
	resourceIdActiveTimeMap, err := service.GetDeviceActiveTime(ctx, resourceIds)
	if err != nil {
		return err
	}
	for _, device := range allDevices {
		activeTime, found := resourceIdActiveTimeMap[device.ResourceId]
		if !found {
			continue
		}
		if device.Project == "PUS4" && activeTime < b.StartTimestamp {
			activePus4DeviceCount++
		}
	}

	mongoBluetoothDisconnectDailyStats := []model.MongoBluetoothDisconnectDailyStat{}
	for deviceId, count := range deviceAlarmCount {
		mongoBluetoothDisconnectDailyStats = append(mongoBluetoothDisconnectDailyStats, model.MongoBluetoothDisconnectDailyStat{
			StatDay:                b.DateStr,
			DeviceId:               deviceId,
			ActiveDeviceCount:      activePus4DeviceCount,
			AlarmCount:             count,
			AlarmInServiceCount:    deviceAlarmInserviceCount[deviceId],
			AlarmNotInServiceCount: deviceAlarmNotInServiceCount[deviceId],
		})
	}
	mongoBluetoothDisconnectDailyStatsRecord := []interface{}{}
	for _, mongoBluetoothDisconnectDailyStat := range mongoBluetoothDisconnectDailyStats {
		mongoBluetoothDisconnectDailyStatsRecord = append(mongoBluetoothDisconnectDailyStatsRecord, mongoBluetoothDisconnectDailyStat)
	}
	if len(mongoBluetoothDisconnectDailyStats) != 0 {
		err = b.Watcher.Mongodb().NewMongoEntry().InsertMany("diagnosis-management", "daily_bluetooth_disconnect_stat", mongoBluetoothDisconnectDailyStatsRecord,
			client.IndexOption{Name: "stat_day_device_id", Fields: bson.D{
				{"stat_day", 1},
				{"device_id", 1},
			}, Unique: true},
		)
	}

	//mongoBluetoothDisconnectDailyStat := model.MongoBluetoothDisconnectDailyStat{
	//	StatDay:             b.DateStr,
	//	ActiveDeviceCount:   activePus4DeviceCount,
	//	AlarmCount:          int64(len(mongoBluetoothDisconnectAlarms)),
	//	AlarmInServiceCount: alarmHappenInServiceCount,
	//}
	//
	//err = b.Watcher.Mongodb().NewMongoEntry(bson.D{bson.E{Key: "stat_day", Value: b.DateStr}}).UpdateOne(
	//	"diagnosis-management", "daily_bluetooth_disconnect_stat", bson.M{"$set": mongoBluetoothDisconnectDailyStat}, true, []client.IndexOption{
	//		{
	//			Name: "stat_day",
	//			Fields: bson.D{
	//				{"stat_day", 1},
	//			},
	//			Unique: true,
	//		},
	//	}...)
	//if err != nil {
	//	return err
	//}
	return nil
}
