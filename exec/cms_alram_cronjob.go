package exec

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"sort"
	"time"

	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	"git.nevint.com/golang-libs/common-utils/larkservice"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	"git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	"git.nevint.com/welkin2/welkin-backend/service"
)

// 一次性代码没啥价值，没必要看，单纯为算法打工 🤷

type CmsAlarmHandler struct {
	Date      string // YYYYMMDD
	Location  *time.Location
	startTime time.Time
	endTime   time.Time
}

const (
	NeedExcelCount    = 10
	MissRateThreshold = 0.1
	CmsAlarmKey       = "cmsAlarm"
)

func (c *CmsAlarmHandler) Process(ctx context.Context) error {
	_, found := config.Cfg.AlarmReceiver[CmsAlarmKey]
	if !found {
		logger.Logger.Infof("cms daily alarm don't need calculate. reason: no alarm receiver config")
		return nil
	}
	err := c.prepare(ctx)
	if err != nil {
		return err
	}
	err = c.CheckElectricityModel(ctx)
	if err != nil {
		return err
	}
	err = c.CheckPredictServie(ctx)
	if err != nil {
		return err
	}
	return nil
}

func (c *CmsAlarmHandler) prepare(ctx context.Context) error {
	startTime, err := time.ParseInLocation("20060102", c.Date, c.Location)
	if err != nil {
		return err
	}
	endTime := startTime.Add(time.Hour * 24)
	c.startTime = startTime
	c.endTime = endTime
	return nil
}

func (c *CmsAlarmHandler) CheckPredictServie(ctx context.Context) error {
	deviceTotal := map[string]int{}
	deviceMiss := map[string]int{}
	filter := bson.D{
		{"date", bson.M{"$gte": time.UnixMilli(c.startTime.UnixMilli()), "$lte": time.UnixMilli(c.endTime.UnixMilli())}},
	}
	Limit := int64(500)
	Offset := int64(0)
	for true {
		cur, err := client.GetWatcher().PLCMongodb().Client.Database(umw.Algorithm).Collection("cms-order-info").Find(ctx, filter, options.Find().SetSort(bson.M{"date": 1}).SetSkip(Offset).SetLimit(Limit))
		if err != nil {
			return err
		}
		var cMSOrderInfos []umw.MongoCMSOrderInfo
		err = cur.All(ctx, &cMSOrderInfos)
		if err != nil {
			return err
		}
		for _, cmsOrderInfo := range cMSOrderInfos {
			deviceTotal[cmsOrderInfo.DeviceId]++
			v70, _ := cmsOrderInfo.PredictedService["70"].([]int)
			v70prob, _ := cmsOrderInfo.PredictedService["70_prob"].([]float64)
			v100, _ := cmsOrderInfo.PredictedService["100"].([]int)
			v100prob, _ := cmsOrderInfo.PredictedService["100_prob"].([]float64)
			vtotal, _ := cmsOrderInfo.PredictedService["total"].([]int)
			vtotalprob, _ := cmsOrderInfo.PredictedService["total_prob"].([]float64)
			if len(v70) == 0 || len(v70prob) == 0 || len(v100) == 0 || len(v100prob) == 0 || len(vtotal) == 0 || len(vtotalprob) == 0 {
				deviceMiss[cmsOrderInfo.DeviceId]++
			}
		}

		if len(cMSOrderInfos) < int(Limit) {
			break
		}
		Offset = Offset + int64(len(cMSOrderInfos))
	}
	result := map[string]float64{}
	needAlarmDevices := []string{}
	for deviceId, total := range deviceTotal {
		miss, found := deviceMiss[deviceId]
		missRate := float64(0)
		if found {
			missRate = float64(miss) / float64(total)
		}
		result[deviceId] = missRate
		if missRate >= MissRateThreshold {
			needAlarmDevices = append(needAlarmDevices, deviceId)
		}
	}

	type KeyVal struct {
		Key string
		Val float64
	}
	sortedKV := []KeyVal{}
	for key, value := range result {
		sortedKV = append(sortedKV, KeyVal{
			key, value,
		})
	}
	sort.Slice(sortedKV, func(i, j int) bool {
		return sortedKV[i].Val > sortedKV[j].Val
	})

	// 构建excel
	f := excelize.NewFile()
	index, _ := f.NewSheet("Sheet1")
	f.SetCellValue("Sheet1", "A1", "设备id")
	f.SetCellValue("Sheet1", "B1", "设备名称")
	f.SetCellValue("Sheet1", "C1", "缺失率")
	for i, keyVal := range sortedKV {
		deviceName := ""
		deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(keyVal.Key)
		if found {
			deviceName = deviceInfo.Description
		}
		f.SetCellValue("Sheet1", fmt.Sprintf("A%v", i+2), keyVal.Key)
		f.SetCellValue("Sheet1", fmt.Sprintf("B%v", i+2), deviceName)
		f.SetCellValue("Sheet1", fmt.Sprintf("C%v", i+2), keyVal.Val)
	}
	f.SetActiveSheet(index)
	buff, err := f.WriteToBuffer()
	if err != nil {
		return err
	}
	fileName := fmt.Sprintf("predicted_service_missing_%v_%v.xlsx", time.Now().UnixMilli(), c.Date)
	url, err := c.store2FMS(ctx, buff.Bytes(), fileName)
	if err != nil {
		return err
	}

	card := larkservice.NewInfoCard()
	card.HeaderName = fmt.Sprintf("[CMS] - 换电站预测订单缺失(缺失率>0.1) Date:%v 一共:%v个站", c.Date, len(needAlarmDevices))
	if len(needAlarmDevices) > 0 {
		card.HeaderColor = "red"
		card.AppendElement(larkservice.ElementDetail, []larkservice.DetailMessage{
			{Msg: fmt.Sprintf("显示%v个，全部详情请下载", NeedExcelCount)},
		})
	} else {
		card.AppendElement(larkservice.ElementDetail, []larkservice.DetailMessage{
			{Msg: fmt.Sprintf("非常棒，顶呱呱👍🏻,没有异常")},
		})
	}

	idx := 0
	msg := ""
	for _, value := range needAlarmDevices {
		if idx > NeedExcelCount {
			break
		}
		deviceName := ""
		deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(value)
		if found {
			deviceName = deviceInfo.Description
		}
		msg = msg + "\n" + fmt.Sprintf("%v:%v", deviceName, value)
		card.AppendElement(larkservice.ElementKeyVal, []larkservice.KeyValMessage{
			{Key: "设备名称：", Val: deviceName},
			{Key: "设备id：", Val: value},
			{Key: "缺失率：", Val: fmt.Sprintf("%v", result[value])},
		})
		idx++
	}
	card.AppendElement(larkservice.ElementDetail, []larkservice.DetailMessage{
		{Msg: fmt.Sprintf("\n 下载地址:%v", url)},
	})
	content, err := card.Build()
	if err != nil {
		return err
	}
	receivers := config.Cfg.AlarmReceiver[CmsAlarmKey]
	for _, receiver := range receivers {
		receiverInfo := larkservice.Receiver{
			Type:       receiver.Type,
			ReceiveIds: receiver.ReceiverIds,
		}
		err = larkservice.SendCard(content, receiverInfo)
		if err != nil {
			return err
		}
	}
	return nil
}

func (c *CmsAlarmHandler) CheckElectricityModel(ctx context.Context) error {
	deviceMissPriceModel := map[string]bool{}
	deviceMissElectricityPrice := map[string]bool{}
	Limit := int64(500)
	Offset := int64(0)
	for true {
		cur, err := client.GetWatcher().PLCMongodb().Client.Database(umw.Algorithm).Collection("cms-hive-history-data").Find(ctx, bson.D{}, options.Find().SetSort(bson.M{"_id": 1}).SetSkip(Offset).SetLimit(Limit))
		if err != nil {
			return err
		}
		var cmsHivHistoryDatas []umw.MongoCmsHivHistoryData
		err = cur.All(ctx, &cmsHivHistoryDatas)
		if err != nil {
			return err
		}
		for _, cmsHivHistoryData := range cmsHivHistoryDatas {
			if cmsHivHistoryData.Date != c.Date {
				continue
			}
			if cmsHivHistoryData.ElectricityPriceModel == "" {
				deviceMissPriceModel[cmsHivHistoryData.DeviceId] = true
			}
			if cmsHivHistoryData.HourlyElectricityPrice == nil || len(cmsHivHistoryData.HourlyElectricityPrice.(primitive.A)) == 0 {
				deviceMissElectricityPrice[cmsHivHistoryData.DeviceId] = true
			}
		}

		if len(cmsHivHistoryDatas) < int(Limit) {
			break
		}
		Offset = Offset + int64(len(cmsHivHistoryDatas))
	}

	devices := []string{}
	for deviceId, _ := range deviceMissPriceModel {
		devices = append(devices, deviceId)
	}
	err := c.processDeviceMissPriceModel(ctx, devices)
	if err != nil {
		return err
	}
	devices = []string{}
	for deviceId, _ := range deviceMissElectricityPrice {
		devices = append(devices, deviceId)
	}
	err = c.processDeviceMissElectricityPrice(ctx, devices)
	if err != nil {
		return err
	}

	return nil
}

func (c *CmsAlarmHandler) processDeviceMissPriceModel(ctx context.Context, deviceIds []string) error {
	fileName := fmt.Sprintf("electricity_price_model_missing_%v_%v.xlsx", time.Now().UnixMilli(), c.Date)
	data, err := c.genExcel(ctx, deviceIds)
	if err != nil {
		return err
	}
	url, err := c.store2FMS(ctx, data, fileName)
	if err != nil {
		return err
	}

	card := larkservice.NewInfoCard()
	card.HeaderName = fmt.Sprintf("[CMS] - 换电站电价标签缺失情况 Date:%v 一共:%v个站", c.Date, len(deviceIds))
	if len(deviceIds) > 0 {
		card.HeaderColor = "red"
		card.AppendElement(larkservice.ElementDetail, []larkservice.DetailMessage{
			{Msg: fmt.Sprintf("显示%v个，更多请下载", NeedExcelCount)},
		})
	} else {
		card.AppendElement(larkservice.ElementDetail, []larkservice.DetailMessage{
			{Msg: fmt.Sprintf("非常棒，顶呱呱👍🏻,没有异常")},
		})
	}

	idx := 0
	msg := ""
	for _, value := range deviceIds {
		if idx > NeedExcelCount {
			break
		}
		deviceName := ""
		deviceInfo, found := cache.PowerSwapCache.GetDeviceByResourceId(value)
		if found {
			deviceName = deviceInfo.Description
		}
		msg = msg + "\n" + fmt.Sprintf("%v:%v", deviceName, value)
		card.AppendElement(larkservice.ElementKeyVal, []larkservice.KeyValMessage{
			{Key: "设备名称：", Val: deviceName},
			{Key: "设备id：", Val: value},
		})
		idx++
	}
	if len(deviceIds) > 0 {
		card.AppendElement(larkservice.ElementDetail, []larkservice.DetailMessage{
			{Msg: fmt.Sprintf("\n 下载地址:%v", url)},
		})
	}
	content, err := card.Build()
	if err != nil {
		return err
	}
	receivers := config.Cfg.AlarmReceiver[CmsAlarmKey]
	err = larkservice.SendAlarmCard(content, receivers)
	if err != nil {
		return err
	}
	return nil
}

func (c *CmsAlarmHandler) processDeviceMissElectricityPrice(ctx context.Context, deviceIds []string) error {
	fileName := fmt.Sprintf("electricity_price_missing_%v_%v.xlsx", time.Now().UnixMilli(), c.Date)
	data, err := c.genExcel(ctx, deviceIds)
	if err != nil {
		return err
	}
	url, err := c.store2FMS(ctx, data, fileName)
	if err != nil {
		return err
	}

	card := larkservice.NewInfoCard()
	card.HeaderName = fmt.Sprintf("[CMS] - 换电站电价信息缺失情况 Date:%v 一共:%v个站", c.Date, len(deviceIds))
	if len(deviceIds) > 0 {
		card.HeaderColor = "red"
		card.AppendElement(larkservice.ElementDetail, []larkservice.DetailMessage{
			{Msg: fmt.Sprintf("显示%v个，更多请下载", NeedExcelCount)},
		})
	} else {
		card.AppendElement(larkservice.ElementDetail, []larkservice.DetailMessage{
			{Msg: fmt.Sprintf("非常棒，顶呱呱👍🏻,没有异常")},
		})
	}

	idx := 0
	msg := ""
	for _, value := range deviceIds {
		if idx > NeedExcelCount {
			break
		}
		deviceName := ""
		deviceInfo, found := cache.PowerSwapCache.GetDeviceByResourceId(value)
		if found {
			deviceName = deviceInfo.Description
		}
		msg = msg + "\n" + fmt.Sprintf("%v:%v", deviceName, value)
		card.AppendElement(larkservice.ElementKeyVal, []larkservice.KeyValMessage{
			{Key: "设备名称：", Val: deviceName},
			{Key: "设备id：", Val: value},
		})
		idx++
	}
	if len(deviceIds) > 0 {
		card.AppendElement(larkservice.ElementDetail, []larkservice.DetailMessage{
			{Msg: fmt.Sprintf("\n 下载地址:%v", url)},
		})
	}
	content, err := card.Build()
	if err != nil {
		return err
	}
	receivers := config.Cfg.AlarmReceiver[CmsAlarmKey]
	for _, receiver := range receivers {
		receiverInfo := larkservice.Receiver{
			Type:       receiver.Type,
			ReceiveIds: receiver.ReceiverIds,
		}
		err = larkservice.SendCard(content, receiverInfo)
		if err != nil {
			return err
		}
	}
	return nil
}

func (c *CmsAlarmHandler) genExcel(ctx context.Context, devices []string) ([]byte, error) {
	f := excelize.NewFile()
	index, _ := f.NewSheet("Sheet1")
	f.SetCellValue("Sheet1", "A1", "设备id")
	f.SetCellValue("Sheet1", "B1", "设备名称")
	for i, value := range devices {
		deviceName := ""
		deviceInfo, found := cache.PowerSwapCache.GetDeviceByResourceId(value)
		if found {
			deviceName = deviceInfo.Description
		}
		f.SetCellValue("Sheet1", fmt.Sprintf("A%v", i+2), value)
		f.SetCellValue("Sheet1", fmt.Sprintf("B%v", i+2), deviceName)
	}
	f.SetActiveSheet(index)
	buff, err := f.WriteToBuffer()
	if err != nil {
		return nil, err
	}
	return buff.Bytes(), nil
}

func (c *CmsAlarmHandler) store2FMS(ctx context.Context, data []byte, fileName string) (string, error) {
	buffer := bytes.NewBuffer(data)
	fmsFileDir := "/algorithm/cms/alarm/"
	tokenRes, tokenErr := service.GetFMS().GetFileUploadToken(fmsFileDir, fileName, model.NeedPublic, buffer.String(), ucmd.GetArea())
	if tokenErr != nil {
		return "", tokenErr
	}
	rd := tokenRes.ResultData
	rd.SupplierHttp.Header["Content-Type"] = "application/octet-stream"
	if err := service.GetFMS().UploadFile(rd.SupplierHttp.URL, rd.SupplierHttp.Header, buffer); err != nil {
		return "", err
	}
	fileURL := ""
	for _, item := range rd.DomainInfoList {
		if item.DomainAttr.CDN {
			fileURL = fmt.Sprintf("%s://%s/%s", item.Protocol, item.Domain, rd.FileKey)
			break
		}
	}
	if fileURL == "" {
		return "", errors.New("gen empty file url")
	}
	return fileURL, nil
}
