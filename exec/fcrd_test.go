package exec

import (
	"git.nevint.com/welkin2/welkin-backend/client"
	"os"
	"testing"
	"time"

	"git.nevint.com/welkin2/welkin-backend/model"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	ucfg "git.nevint.com/golang-libs/common-utils/config"
	log "git.nevint.com/welkin2/welkin-backend/logger"
)

// initTestCfgEU initializes the config for the EU environment(stg)
func initStgCfgEU() *ucfg.Config {
	os.Setenv("ENV", "stg")
	os.Setenv("APOLLO_META", "https://apollo-config-stg-eu.nioint.com")
	cfg := &ucfg.Config{}
	ucfg.GetConfigApollo("config.json", true, cfg, ucfg.NeedValidate(true), ucfg.ServiceName("ppd-welkin-backend"), ucfg.Cluster("ppd-welkin-backend-eu-aks-stg"))
	cfg.Log.DirPath = "/tmp"
	log.Init(cfg.Log, true)
	return cfg
}

func TestFcrd_ParseHourRevenue(t *testing.T) {
	cfg := initStgCfgEU()
	f := NewFcrd(cfg, client.NewWatcher(gin.New(), cfg, true)) // Create an instance of Fcrd

	// Define the input data
	r := model.DataResult{
		Capacity: 0.1,
		Revenue:  2.64,
		Sc:       1.02,
		Partner:  0.34,
	}
	p := model.DataPrice{
		Timestamp: "2022-01-01T00:00:00Z",
		D1:        50,
		D2:        40,
	}

	// Call the method being tested
	result, err := f.parseHourRevenue(r, p)

	// Assert no error
	assert.NoError(t, err)

	// Assert the expected output values
	expectedTimestamp := int64(1640995200000)
	expectedDate := "2022-01-01T00:00:00Z"
	expectedIdealBidCapacity := 0.3
	expectedActualBidCapacity := 0.1
	expectedIdealRevenue := int32(792)
	expectedRevenue := int32(264)

	assert.Equal(t, expectedTimestamp, result.Timestamp, "Unexpected timestamp")
	assert.Equal(t, expectedDate, result.Date, "Unexpected date")
	assert.Equal(t, expectedIdealBidCapacity, result.IdealBidCapacity, "Unexpected ideal bid capacity")
	assert.Equal(t, expectedActualBidCapacity, result.ActualBidCapacity, "Unexpected actual bid capacity")
	assert.Equal(t, expectedIdealRevenue, result.IdealRevenue, "Unexpected ideal revenue")
	assert.Equal(t, expectedRevenue, result.Revenue, "Unexpected revenue")
}
func TestFcrd_AggregateHourRevenue(t *testing.T) {
	f := &Fcrd{} // Create an instance of Fcrd

	// Define the input data
	s := []model.FcrdRevenueHourly{
		{
			Project:           "Project1",
			DeviceID:          "Device1",
			IdealBidCapacity:  0.3,
			ActualBidCapacity: 0.1,
			IdealRevenue:      792,
			Revenue:           264,
		},
		{
			Project:           "Project1",
			DeviceID:          "Device1",
			IdealBidCapacity:  0.3,
			ActualBidCapacity: 0.2,
			IdealRevenue:      792,
			Revenue:           264,
		},
		{
			Project:           "Project1",
			DeviceID:          "Device1",
			IdealBidCapacity:  0.3,
			ActualBidCapacity: 0.2,
			IdealRevenue:      792,
			Revenue:           264,
		},
	}

	// Call the method being tested
	result, err := f.aggregateHourRevenue(s, 0)

	// Assert no error
	assert.NoError(t, err)

	// Assert the expected output values
	expectedProject := "Project1"
	expectedDeviceID := "Device1"
	expectedIdealBidCapacity := 0.3
	expectedActualBidCapacity := 0.17
	expectedIdealRevenue := int32(792 + 792 + 792)
	expectedRevenue := int32(264 + 264 + 264)

	assert.Equal(t, expectedProject, result.Project, "Unexpected project")
	assert.Equal(t, expectedDeviceID, result.DeviceID, "Unexpected device ID")
	assert.Equal(t, expectedIdealBidCapacity, result.IdealBidCapacity, "Unexpected ideal bid capacity")
	assert.Equal(t, expectedActualBidCapacity, result.ActualBidCapacity, "Unexpected actual bid capacity")
	assert.Equal(t, expectedIdealRevenue, result.IdealRevenue, "Unexpected ideal revenue")
	assert.Equal(t, expectedRevenue, result.Revenue, "Unexpected revenue")
}

func TestFcrd_FetchRevenue(t *testing.T) {
	f := &Fcrd{} // Create an instance of Fcrd

	// Define the input data
	date := "2024-05-05"

	// Call the method being tested
	result, err := f.fetchRevenue(date)

	// Assert no error
	assert.NoError(t, err)

	// Assert the expected output values
	expectedLength := 24

	//

	assert.Len(t, result, expectedLength, "Unexpected length of revenue data")
	t.Log(result)
}

func TestFcrd_WindowAggregation_actual(t *testing.T) {
	cfg := initStgCfgEU()
	f := NewFcrd(cfg, client.NewWatcher(gin.New(), cfg, true)) // Create an instance of Fcrd
	// Define the input data
	date := "2024-05-05"

	// Call the method being tested
	hrs, err := f.fetchRevenue(date)
	assert.NoError(t, err)

	windowLength := 24
	var startTs int64 = 1714860000000

	res, err := f.windowAggregation(windowLength, hrs, startTs)
	assert.NoError(t, err)
	expectedResult := []model.FcrdRevenue{
		{
			Project:           "PowerSwap2",
			DeviceID:          "PS-NIO-7db7c5aa-ff6ccc64",
			Timestamp:         1714860000000,
			IdealBidCapacity:  0.3,
			ActualBidCapacity: 0.1,
			IdealRevenue:      28658,
			Revenue:           9555,
		},
	}
	assert.Equal(t, expectedResult, res, "Unexpected result")
}

func TestFcrd_StoreMoreData(t *testing.T) {
	cfg := initStgCfgEU()
	f := NewFcrd(cfg, client.NewWatcher(gin.New(), cfg, true)) // Create an instance of Fcrd

	//generate date string from "2024-01-01" to "2024-05-08"
	layout := "2006-01-02"
	dates := []string{}
	start, _ := time.Parse(layout, "2024-05-09")
	end, _ := time.Parse(layout, "2024-05-12")

	for d := start; !d.After(end); d = d.AddDate(0, 0, 1) {
		dates = append(dates, d.Format("2006-01-02"))
	}
	for _, date := range dates {
		hrs, err := f.fetchRevenue(date)
		assert.NoError(t, err)
		err = f.storeRevenueHourly(hrs)
		assert.NoError(t, err)
	}

}
