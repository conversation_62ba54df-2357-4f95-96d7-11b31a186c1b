package exec

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"os"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gomodule/redigo/redis"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"github.com/mitchellh/mapstructure"
	"github.com/patrickmn/go-cache"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	"git.nevint.com/golang-libs/common-utils/cronjob"
	"git.nevint.com/golang-libs/common-utils/larkservice"
	um "git.nevint.com/golang-libs/common-utils/model"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	uprom "git.nevint.com/golang-libs/common-utils/prometheus"

	wlkin_cache "git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	"git.nevint.com/welkin2/welkin-backend/domain/activity"
	domain_device "git.nevint.com/welkin2/welkin-backend/domain/device"
	domain_git "git.nevint.com/welkin2/welkin-backend/domain/git"
	domain_image "git.nevint.com/welkin2/welkin-backend/domain/image"
	"git.nevint.com/welkin2/welkin-backend/domain/psos"
	"git.nevint.com/welkin2/welkin-backend/domain/psos_scheduler"
	domain_service "git.nevint.com/welkin2/welkin-backend/domain/service"
	domain_service2 "git.nevint.com/welkin2/welkin-backend/domain_service"
	"git.nevint.com/welkin2/welkin-backend/domain_service/device_simulation"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/service"
)

func InitCronJobs(cfg *ucfg.Config) {
	logger := log.Logger.Named("CronJob")
	watcher := client.NewWatcherByParam(cfg, logger)
	prom := uprom.NewEmptyPrometheus("welkin", "/prometheus")
	ctx := context.Background()
	wlkin_cache.PowerSwapCache = &wlkin_cache.DeviceInfoCache{
		Cache:         cache.New(cache.NoExpiration, cache.NoExpiration),
		ResourceCache: cache.New(cache.NoExpiration, cache.NoExpiration),
	}
	wlkin_cache.ChargerCache = &wlkin_cache.DeviceInfoCache{
		Cache:         cache.New(cache.NoExpiration, cache.NoExpiration),
		ResourceCache: cache.New(cache.NoExpiration, cache.NoExpiration),
	}
	wlkin_cache.SwapEventCache = &wlkin_cache.EventCache{
		Cache:  make(map[string]*cache.Cache),
		Mu:     sync.RWMutex{},
		Logger: logger,
	}
	wlkin_cache.DateInfoCache = &wlkin_cache.DateCache{
		Cache:  cache.New(cache.NoExpiration, cache.NoExpiration),
		Logger: logger,
	}
	// 目前只在国内
	loc, _ := time.LoadLocation("Asia/Shanghai")

	cronjob.TimerExecute(ctx, func() {
		wlkin_cache.PowerSwapCache.RefreshDeviceInfoCache(watcher.Mongodb().Client)
	}, time.Second*60*15, true)

	cronjob.TimerExecute(ctx, func() {
		wlkin_cache.ChargerCache.RefreshChargerInfoCache(watcher.Mongodb().Client)
	}, time.Second*60*15, true)

	cronjob.TimerExecute(ctx, func() {
		wlkin_cache.SwapEventCache.RefreshSwapEventCache(watcher.Mongodb().Client)
	}, time.Second*60*20, true)

	// 挂车告警缓存
	wlkin_cache.InitAlarmInfoCache()
	wlkin_cache.StuckAlarmInfoCache.Logger = logger
	cronjob.TimerExecute(ctx, func() {
		wlkin_cache.StuckAlarmInfoCache.RefreshStuckAlarmInfoCache(watcher.Mongodb().Client)
	}, time.Second*60*60, true)

	// 设备告警缓存
	wlkin_cache.DeviceAlarmInfoCache.Logger = logger
	cronjob.TimerExecute(ctx, func() {
		wlkin_cache.DeviceAlarmInfoCache.RefreshDeviceAlarmInfoCache(watcher.Mongodb().Client)
	}, time.Second*60*60, true)

	// 日期信息缓存
	cronjob.TimerExecute(ctx, func() {
		err := wlkin_cache.DateInfoCache.RefreshDateInfoCache(watcher.Mongodb().Client)
		if err != nil {
			logger.Errorf("refresh date info. err:%v", err)
			panic(err)
		}
	}, time.Second*60*60, true)

	getRedisConn := func() redis.Conn {
		var conn redis.Conn
		if watcher.Redis().Type == "cluster" {
			conn = watcher.Redis().CPool.Get()
		} else {
			conn = watcher.Redis().Pool.Get()
		}
		return conn
	}

	parkingOccupationJob := cronjob.CronJobTask{
		Name:             "parking_occupation_data_process",
		Express:          "15 * * * *",
		DistributionLock: getRedisConn(),
		TimeLocation:     loc,
		TimeOutDuration:  time.Minute * 15,
		ProcessFunc: func() error {
			nowTime := time.Now().In(loc)
			startTime := nowTime.Add(-(time.Hour * 48))
			p := ParkingOccupationHandler{
				Watcher:        watcher,
				Projects:       []string{"pus2", "pus3"},
				TimeLocation:   loc,
				StartTimestamp: startTime.UnixMilli(),
				EndTimestamp:   nowTime.UnixMilli(),
			}
			return p.Process(ctx)
		},
		Metric: prom,
	}
	parkingOccupationJob.StartTask()

	serviceCheckJob := cronjob.CronJobTask{
		Name:             "service_check_job",
		Express:          "6 * * * *",
		DistributionLock: getRedisConn(),
		ProcessFunc: func() error {
			s := ServiceCheckHandler{
				Watcher:      watcher,
				Projects:     []string{"pus2", "pus3", "pus4"},
				TimeLocation: loc,
			}
			return s.Process(ctx)
		},
		TimeOutDuration: time.Minute * 3,
		TimeLocation:    loc,
		Metric:          prom,
	}
	serviceCheckJob.StartTask()
	// 测试使用
	//go func() {
	//	defer ucmd.RecoverPanic()
	//	s := ServiceCheckHandler{
	//		Watcher:      watcher,
	//		Projects:     []string{"pus2", "pus3", "pus4"},
	//		TimeLocation: loc,
	//	}
	//	err := s.TestRun(ctx)
	//	if err != nil {
	//		fmt.Println(fmt.Sprintf("TestRun err. err:%v", err))
	//	}
	//}()

	serviceStatisticsJob := cronjob.CronJobTask{
		Name:             "service_statistics_job",
		Express:          "10 * * * *",
		DistributionLock: getRedisConn(),
		ProcessFunc: func() error {
			endTime := time.Now().Truncate(time.Hour)
			startTime := endTime.Add(-time.Hour)
			s := ServiceStatisticsHandler{
				Watcher:   watcher,
				Projects:  []string{umw.PowerSwap, umw.PowerSwap2, umw.PUS3, umw.PUS4},
				StartTime: startTime,
				EndTime:   endTime,
				Logger:    logger,
			}
			return s.ProcessHourly(ctx)
		},
		TimeOutDuration: time.Minute * 10,
		TimeLocation:    loc,
	}
	serviceStatisticsJob.StartTask()

	bluetoothDisconnectStatJob := cronjob.CronJobTask{
		Name:             "bluetooth_disconnect_stst_job",
		Express:          "5 4 * * *",
		DistributionLock: getRedisConn(),
		ProcessFunc: func() error {
			dateStr := time.Now().In(loc).Add(-24 * time.Hour).Format("2006-01-02")
			s := BluetoothDisconnectStatHandler{
				Watcher: watcher,
				DateStr: dateStr,
			}
			return s.Process(ctx)
		},
		TimeOutDuration: time.Minute * 20,
		TimeLocation:    loc,
	}
	bluetoothDisconnectStatJob.StartTask()
	//for i := 15; i <= 24; i++ {
	//	s := BluetoothDisconnectStatHandler{
	//		Watcher: watcher,
	//		DateStr: fmt.Sprintf("2024-11-%v", i),
	//	}
	//	err := s.Process(ctx)
	//	if err != nil {
	//		panic(fmt.Sprintf("执行完成 %v", err))
	//	}
	//}

	psosSchedulerWatchDog := cronjob.CronJobTask{
		Name:             "psos_scheduler_watch_dog_job",
		Express:          "* * * * *",
		DistributionLock: getRedisConn(),
		ProcessFunc: func() error {
			psosScheduler := psos_scheduler.PsosSchedulerDO{
				Watcher: watcher,
				Fms:     *service.GetFMS(),
			}
			return psosScheduler.WatchDog(ctx)
		},
		TimeOutDuration: time.Second * 50,
	}
	if ucmd.GetEnv() != "test" {
		psosSchedulerWatchDog.StartTask()
	}

	cmsAlarmJob := cronjob.CronJobTask{
		Name:             "cms_daily_alarm_job",
		Express:          "0 9 * * *",
		DistributionLock: getRedisConn(),
		ProcessFunc: func() error {
			nowTime := time.Now().In(loc)
			startTime := nowTime.Add(-(time.Hour * 24))
			cmsAlarmHandler := CmsAlarmHandler{
				Date:     startTime.Format("20060102"),
				Location: loc,
			}
			return cmsAlarmHandler.Process(ctx)
		},
		TimeOutDuration: time.Minute * 5,
	}
	cmsAlarmJob.StartTask()

	activityStatsJob := cronjob.CronJobTask{
		Name:             "activity_stats_job",
		Express:          "45 0 * * *",
		DistributionLock: getRedisConn(),
		TimeLocation:     time.Local,
		TimeOutDuration:  time.Minute * 5,
		ProcessFunc: func() error {
			now := time.Now()
			endTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
			startTime := endTime.Add(-time.Hour * 24)
			a := activity.ActivityDO{
				StartTime: startTime.UnixMilli(),
				EndTime:   endTime.UnixMilli(),
				Projects:  []string{umw.PowerSwap2, umw.PUS3, umw.PUS4},
			}
			ginCtx := gin.Context{Request: &http.Request{Header: make(http.Header)}}
			ginCtx.Request.Header.Set("X-Request-ID", "cronjob")
			a.CalculateActivity(&ginCtx)
			return nil
		},
	}
	activityStatsJob.StartTask()

	// 欧洲电力交易，定时判断电池是否充满
	checkBatterySocJob := cronjob.CronJobTask{
		Name:             "check_battery_soc_job",
		Express:          "* * * * *",
		DistributionLock: getRedisConn(),
		TimeLocation:     time.Local,
		TimeOutDuration:  time.Second * 30,
		ProcessFunc: func() error {
			for deviceId := range domain_device.FCRDReserveDevices {
				d := domain_device.Device{
					Project:  umw.PowerSwap2,
					DeviceId: deviceId,
				}
				d.CheckSlotBatterySoc()
			}
			return nil
		},
	}
	if ucmd.GetArea() == "Europe" && ucmd.GetEnv() == "prod" {
		checkBatterySocJob.StartTask()
	}

	// 判断电池运营照片是否缺失
	checkBatteryImageJob := cronjob.CronJobTask{
		Name:             "check_battery_image_job",
		Express:          "0 * * * *",
		DistributionLock: getRedisConn(),
		TimeLocation:     time.Local,
		TimeOutDuration:  time.Minute * 10,
		ProcessFunc: func() error {
			now := time.Now()
			ginCtx := gin.Context{Request: &http.Request{Header: make(http.Header)}}
			ginCtx.Request.Header.Set("X-Request-ID", "cronjob")
			imageCheckCfg := config.Cfg.ExtraConfig["imageCheck"]
			var imageCheck config.ImageCheck
			err := mapstructure.Decode(imageCheckCfg, &imageCheck)
			if err != nil {
				return fmt.Errorf("fail to decode imageCheckCfg: %v", err)
			}
			startTs, endTs := now.Add(-time.Duration(imageCheck.CheckHour+1)*time.Hour).UnixMilli(), now.Add(-time.Duration(imageCheck.CheckHour)*time.Hour).UnixMilli()
			for _, project := range []string{umw.PowerSwap, umw.PowerSwap2, umw.PUS3, umw.PUS4} {
				for _, algorithm := range []string{domain_image.AlgorithmBBSA, domain_image.AlgorithmBSA} {
					img := domain_image.NewImage(project, algorithm, startTs, endTs)
					img.GetFaultCamera(&ginCtx, imageCheck.AlarmThreshold)
				}
			}
			return nil
		},
	}
	if ucmd.GetArea() == um.China {
		checkBatteryImageJob.StartTask()
	}

	// 推送赤兔配置点到数仓
	pushRedRabbitParamsJob := cronjob.CronJobTask{
		Name:             "push_red_rabbit_params_job",
		Express:          "0 0 * * *",
		DistributionLock: getRedisConn(),
		TimeLocation:     time.Local,
		TimeOutDuration:  time.Minute * 10,
		ProcessFunc: func() error {
			return PushRedRabbitParams()
		},
	}
	pushRedRabbitParamsJob.StartTask()

	// 推送赤兔液位告警屏蔽功能点到智能运维 仅国内生产环境
	if ucmd.GetArea() == um.China && ucmd.GetEnv() == "prod" {
		pushRedRabbitMaskLiquidParamJob := cronjob.CronJobTask{
			Name:             "push_red_rabbit_mask_liquid_param_job",
			Express:          "*/10 * * * *",
			DistributionLock: getRedisConn(),
			TimeLocation:     time.Local,
			TimeOutDuration:  time.Minute * 10,
			ProcessFunc: func() error {
				return PushMaskLiquidParam()
			},
		}
		pushRedRabbitMaskLiquidParamJob.StartTask()
	}

	// 满意度诊断数据刷写
	syncSatisfyJob := cronjob.CronJobTask{
		Name:             "sync_satisfy_job",
		Express:          "0 8 * * *",
		DistributionLock: getRedisConn(),
		TimeLocation:     time.Local,
		TimeOutDuration:  time.Minute * 15,
		ProcessFunc: func() error {
			now := time.Now()
			ginCtx := gin.Context{Request: &http.Request{Header: make(http.Header)}}
			ginCtx.Request.Header.Set("X-Request-ID", "cronjob")
			yesterday := now.Add(-24 * time.Hour)
			satisfy := &domain_service.Satisfy{}
			satisfy.SyncSatisfyData(&ginCtx, time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, time.Local).UnixMilli())
			return nil
		},
	}
	if ucmd.GetArea() == um.China && ucmd.GetEnv() == "prod" {
		syncSatisfyJob.StartTask()
	}

	// 推送mazu配置点到数仓
	pushMazuParamsJob := cronjob.CronJobTask{
		Name:             "push_mazu_params_job",
		Express:          "0 10 * * *",
		DistributionLock: getRedisConn(),
		TimeLocation:     time.Local,
		TimeOutDuration:  time.Minute * 10,
		ProcessFunc: func() error {
			return PushMazuParams()
		},
	}
	if ucmd.GetArea() == um.China {
		pushMazuParamsJob.StartTask()
	}

	// 统计过去一周的天级换电次数
	dailySwapCountJob := cronjob.CronJobTask{
		Name:             "daily_swap_count_job",
		Express:          "0 1 * * *",
		DistributionLock: getRedisConn(),
		TimeLocation:     time.Local,
		TimeOutDuration:  time.Minute * 10,
		ProcessFunc: func() error {
			now := time.Now()
			endDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
			startDay := endDay.Add(-time.Hour * 24 * 7)
			s := ServiceStatisticsHandler{
				Watcher:   watcher,
				Projects:  []string{umw.PowerSwap, umw.PowerSwap2, umw.PUS3, umw.PUS4},
				StartTime: startDay,
				EndTime:   endDay,
				Logger:    logger,
			}
			return s.ProcessDaily(ctx)
		},
	}
	dailySwapCountJob.StartTask()

	// 每日同步设备告警点表
	syncAlarmTagJob := cronjob.CronJobTask{
		Name:             "sync_alarm_tag_job",
		Express:          "0 2 * * *",
		DistributionLock: getRedisConn(),
		TimeLocation:     time.Local,
		TimeOutDuration:  time.Minute * 15,
		ProcessFunc: func() error {
			if ucmd.GetEnv() != "prod" && ucmd.GetEnv() != "stg" {
				return nil
			}
			localPath := domain_git.ClonePathProtoBufPs
			gitDO := &domain_git.GitDO{}
			return gitDO.SyncProtobufPS(ctx, localPath)
		},
	}
	syncAlarmTagJob.StartTask()

	// 每日执行psos全量设备仿真
	psosSimulationJob := cronjob.CronJobTask{
		Name:             "psos_simulation_job",
		Express:          "0 1 * * *",
		DistributionLock: getRedisConn(),
		TimeLocation:     time.Local,
		TimeOutDuration:  time.Minute * 15,
		ProcessFunc: func() error {
			if !(ucmd.GetEnv() == "prod" && ucmd.GetArea() == um.China) {
				return nil
			}
			runPsos := func(project string, chargeStrategy string) error {
				now := time.Now()
				endDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
				startDay := endDay.Add(-time.Hour * 24)
				cond := device_simulation.SimulationAllDevicesCond{
					Project:        project,
					ChargeStrategy: chargeStrategy,
					StartTs:        startDay.UnixMilli(),
					EndTs:          endDay.UnixMilli(),
				}
				switchOn, switchOff := 1, 0
				if chargeStrategy == device_simulation.ChargeStrategyOnlyEps {
					cond.CmsSwitch = &switchOn
					cond.BatteryRestSwitch = &switchOff
				} else if chargeStrategy == device_simulation.ChargeStrategyEpsSilent {
					cond.CmsSwitch = &switchOn
					cond.BatteryRestSwitch = &switchOn
				} else if chargeStrategy == device_simulation.ChargeStrategyNonStra {
					cond.CmsSwitch = &switchOff
					cond.BatteryRestSwitch = &switchOff
				}
				domain_service2.GetDeviceSimulation().StartSimulationAllDevices(context.Background(), cond)
				return nil
			}
			g := ucmd.NewErrGroup(context.Background())
			for _, project := range []string{umw.PowerSwap2, umw.PUS3, umw.PUS4} {
				for _, strategy := range []string{device_simulation.ChargeStrategyNonStra, device_simulation.ChargeStrategyOnlyEps, device_simulation.ChargeStrategyEpsSilent} {
					g.GoRecover(func() error {
						return runPsos(project, strategy)
					})
				}
			}
			return nil
		},
	}
	psosSimulationJob.StartTask()

	// 每日推送psos全量设备仿真结果
	psosSimulationResultJob := cronjob.CronJobTask{
		Name:             "psos_simulation_result_job",
		Express:          "0 9 * * *",
		DistributionLock: getRedisConn(),
		TimeLocation:     time.Local,
		TimeOutDuration:  time.Minute * 15,
		ProcessFunc: func() error {
			if ucmd.GetEnv() != "prod" || ucmd.GetArea() != um.China {
				return nil
			}
			now := time.Now()
			endDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
			day := endDay.Add(-time.Hour * 24).Format("20060102")
			msg := fmt.Sprintf("【%s】PSOS仿真数据更新结果  ", day)
			for _, project := range []string{umw.PowerSwap2, umw.PUS3, umw.PUS4} {
				for _, strategy := range []string{device_simulation.ChargeStrategyNonStra, device_simulation.ChargeStrategyOnlyEps, device_simulation.ChargeStrategyEpsSilent} {
					count, err := client.GetWatcher().Mongodb().NewMongoEntry(bson.D{{"day", day}, {"project", project}, {"charge_strategy", strategy}}).Count(umw.Algorithm, psos.CollectionResult)
					if err != nil {
						log.CtxLog(ctx).Errorf("psos_simulation_result_job err: %v, project: %s, strategy: %s", err, project, strategy)
					}
					msg += fmt.Sprintf("%s(%s):%d; ", project, strategy, count)
				}
			}
			_ = larkservice.SendMessage(larkservice.MsgText, fmt.Sprintf("{\"text\": \"%s\"}", msg), larkservice.Receiver{
				Type:       larkim.ReceiveIdTypeEmail,
				ReceiveIds: config.Cfg.CardBot.Receivers["develop"],
			})
			return nil
		},
	}
	psosSimulationResultJob.StartTask()
}

func PushRedRabbitParams() (err error) {
	defer func() {
		if err != nil {
			_ = larkservice.SendMessage(larkservice.MsgText, fmt.Sprintf("{\"text\": \"[%s] 赤兔参数推送失败, time: %v\"}", os.Getenv("ENV"), time.Now()), larkservice.Receiver{
				Type:       larkim.ReceiveIdTypeEmail,
				ReceiveIds: config.Cfg.CardBot.Receivers["develop"],
			})
		}
	}()
	var res []struct {
		DataId  int64  `json:"data_id" bson:"data_id"`
		Project string `json:"project" bson:"project"`
	}
	_, err = client.GetWatcher().Mongodb().NewMongoEntry().FindMany(umw.Device2Cloud, "rb2datasight", options.Find(), &res)
	if err != nil {
		return err
	}
	dataMap := make(map[string][]int64)
	for _, item := range res {
		dataMap[item.Project] = append(dataMap[item.Project], item.DataId)
	}
	sendToKafka := func(requestBody map[string]interface{}) error {
		url := fmt.Sprintf("%s/plc/broker/v2/welkin-gen/%s", config.Cfg.Welkin.BackendUrl, "redrabbit_params")
		ct := ucmd.NewHttpClient(ucmd.HttpClient{
			URL:    url,
			Method: "POST",
			Header: map[string]string{
				"Content-Type": "application/json",
			},
			RequestBody: requestBody,
		})
		body, statusCode, err := ct.Do()
		if err != nil {
			return err
		}
		defer body.Close()
		data, dErr := io.ReadAll(body)
		if dErr != nil {
			return dErr
		}
		if statusCode != http.StatusOK {
			return fmt.Errorf("status code: %d, body: %s", statusCode, string(data))
		}
		return nil
	}
	pushRedRabbit := func(project string) error {
		var params []struct {
			DeviceId string `json:"_id" bson:"_id"`
			Params   []struct {
				Key   int64       `json:"key" bson:"key"`
				Value interface{} `json:"value" bson:"value"`
			} `json:"params" bson:"params"`
		}
		pipeline := mongo.Pipeline{
			bson.D{{
				"$match", bson.M{
					"params": bson.M{
						"$elemMatch": bson.M{
							"key": bson.M{"$in": dataMap[project]},
						},
					},
				},
			}},
			bson.D{{
				"$unwind", "$params",
			}},
			bson.D{{
				"$match", bson.M{
					"params.key": bson.M{
						"$in": dataMap[project],
					},
				},
			}},
			bson.D{{
				"$group", bson.M{
					"_id":    "$_id",
					"params": bson.M{"$push": "$params"},
				},
			}},
			bson.D{{
				"$project", bson.M{
					"_id":    1,
					"params": 1,
				},
			}},
		}
		err = client.GetWatcher().RbMongodb().NewMongoEntry().Aggregate(project, "devices", pipeline, &params)
		if err != nil {
			return err
		}
		for _, item := range params {
			sendParams := make(map[string]interface{})
			for _, p := range item.Params {
				sendParams[fmt.Sprintf("%d", p.Key)] = p.Value
			}
			requestBody := map[string]interface{}{
				"msg_timestamp": time.Now().UnixMilli(),
				"data": map[string]interface{}{
					"project":   project,
					"device_id": item.DeviceId,
					"params":    sendParams,
				},
			}
			if err = sendToKafka(requestBody); err != nil {
				return err
			}
		}
		return nil
	}
	if err = pushRedRabbit(umw.PowerSwap2); err != nil {
		return err
	}
	if err = pushRedRabbit(umw.PUS3); err != nil {
		return err
	}
	if err = pushRedRabbit(umw.PUS4); err != nil {
		return err
	}
	return nil
}

func PushMaskLiquidParam() (err error) {
	defer func() {
		if err != nil {
			_ = larkservice.SendMessage(larkservice.MsgText, fmt.Sprintf("{\"text\": \"[%s] 赤兔参数推送失败, time: %v\"}", os.Getenv("ENV"), time.Now()), larkservice.Receiver{
				Type:       larkim.ReceiveIdTypeEmail,
				ReceiveIds: config.Cfg.CardBot.Receivers["develop"],
			})
		}
	}()

	dataMap := map[string][]int64{
		umw.PUS3: {902006},
		umw.PUS4: {971906},
	}
	sendToKafka := func(requestBody map[string]interface{}) error {
		url := fmt.Sprintf("%s/plc/broker/v2/welkin-gen/%s", config.Cfg.Welkin.BackendUrl, "mask_liquid_param")
		ct := ucmd.NewHttpClient(ucmd.HttpClient{
			URL:    url,
			Method: "POST",
			Header: map[string]string{
				"Content-Type": "application/json",
			},
			RequestBody: requestBody,
		})
		body, statusCode, err := ct.Do()
		if err != nil {
			return err
		}
		defer body.Close()
		data, dErr := io.ReadAll(body)
		if dErr != nil {
			return dErr
		}
		if statusCode != http.StatusOK {
			return fmt.Errorf("status code: %d, body: %s", statusCode, string(data))
		}
		return nil
	}
	pushMaskLiquid := func(project string) error {
		var params []struct {
			DeviceId string `json:"_id" bson:"_id"`
			Params   []struct {
				Key   int64       `json:"key" bson:"key"`
				Value interface{} `json:"value" bson:"value"`
			} `json:"params" bson:"params"`
		}
		pipeline := mongo.Pipeline{
			bson.D{{
				"$match", bson.M{
					"params": bson.M{
						"$elemMatch": bson.M{
							"key": bson.M{"$in": dataMap[project]},
						},
					},
				},
			}},
			bson.D{{
				"$unwind", "$params",
			}},
			bson.D{{
				"$match", bson.M{
					"params.key": bson.M{
						"$in": dataMap[project],
					},
				},
			}},
			bson.D{{
				"$group", bson.M{
					"_id":    "$_id",
					"params": bson.M{"$push": "$params"},
				},
			}},
			bson.D{{
				"$project", bson.M{
					"_id":    1,
					"params": 1,
				},
			}},
		}
		err = client.GetWatcher().RbMongodb().NewMongoEntry().Aggregate(project, "devices", pipeline, &params)
		if err != nil {
			return err
		}
		for _, item := range params {
			sendParams := make(map[string]interface{})
			for _, p := range item.Params {
				sendParams[fmt.Sprintf("%d", p.Key)] = p.Value
			}
			deviceInfo, ok := wlkin_cache.PowerSwapCache.GetSingleDevice(item.DeviceId)
			if !ok {
				continue
			}
			requestBody := map[string]interface{}{
				"msg_timestamp": time.Now().UnixMilli(),
				"data": map[string]interface{}{
					"project":     project,
					"device_id":   item.DeviceId,
					"resource_id": deviceInfo.ResourceId,
					"params":      sendParams,
				},
			}
			if err = sendToKafka(requestBody); err != nil {
				return err
			}
		}
		return nil
	}
	if err = pushMaskLiquid(umw.PUS3); err != nil {
		return err
	}
	if err = pushMaskLiquid(umw.PUS4); err != nil {
		return err
	}
	return nil
}

func PushMazuParams() error {
	var res []struct {
		DataId  int64  `json:"data_id" bson:"data_id"`
		Project string `json:"project" bson:"project"`
	}
	_, err := client.GetWatcher().Mongodb().NewMongoEntry().FindMany(umw.Device2Cloud, "rb_mazu2op", options.Find(), &res)
	if err != nil {
		return err
	}
	dataMap := make(map[string][]int64)
	for _, item := range res {
		dataMap[item.Project] = append(dataMap[item.Project], item.DataId)
	}
	sendToKafka := func(requestBody map[string]interface{}) error {
		url := fmt.Sprintf("%s/plc/broker/v2/welkin-gen/%s", config.Cfg.Welkin.BackendUrl, "mazu_params")
		ct := ucmd.NewHttpClient(ucmd.HttpClient{
			URL:    url,
			Method: "POST",
			Header: map[string]string{
				"Content-Type": "application/json",
			},
			RequestBody: requestBody,
		})
		body, statusCode, err := ct.Do()
		if err != nil {
			return err
		}
		defer body.Close()
		data, dErr := io.ReadAll(body)
		if dErr != nil {
			return dErr
		}
		if statusCode != http.StatusOK {
			return fmt.Errorf("status code: %d, body: %s", statusCode, string(data))
		}
		return nil
	}
	pushMazu := func(project string) error {
		var params []struct {
			DeviceId    string `json:"device_id" bson:"device_id"`
			MazuVersion string `json:"mazu_version" bson:"mazu_version"`
			Params      []struct {
				Key   int64       `json:"key" bson:"key"`
				Value interface{} `json:"value" bson:"value"`
			} `json:"params" bson:"params"`
		}
		pipeline := mongo.Pipeline{
			bson.D{{
				"$match", bson.M{
					"is_active": true,
				},
			}},
			bson.D{{
				"$unwind", "$params",
			}},
			bson.D{{
				"$match", bson.M{
					"params.key": bson.M{
						"$in": dataMap[project],
					},
				},
			}},
			bson.D{{
				"$group", bson.M{
					"_id": bson.M{
						"device_id":    "$_id",
						"mazu_version": "$MAZU-APP",
					},
					"params": bson.M{"$push": "$params"},
				},
			}},
			bson.D{{
				"$project", bson.M{
					"device_id":    "$_id.device_id",
					"mazu_version": "$_id.mazu_version",
					"params":       1,
				},
			}},
		}
		err = client.GetWatcher().RbMongodb().NewMongoEntry().Aggregate(project, "devices", pipeline, &params)
		if err != nil {
			return err
		}
		g := ucmd.NewErrGroup(context.Background(), 20)
		for _, item := range params {
			sendParams := make(map[string]interface{})
			for _, p := range item.Params {
				sendParams[fmt.Sprintf("%d", p.Key)] = p.Value
			}
			deviceInfo, ok := wlkin_cache.PowerSwapCache.GetSingleDevice(item.DeviceId)
			if !ok {
				continue
			}
			requestBody := map[string]interface{}{
				"msg_timestamp": time.Now().UnixMilli(),
				"msg_type":      "mazu_params",
				"data": map[string]interface{}{
					"project":      project,
					"resource_id":  deviceInfo.ResourceId,
					"mazu_version": item.MazuVersion,
					"params":       sendParams,
				},
			}
			g.GoRecover(func() error {
				return sendToKafka(requestBody)
			})
		}
		return g.Wait()
	}
	if err = pushMazu(umw.PUS3); err != nil {
		return err
	}
	if err = pushMazu(umw.PUS4); err != nil {
		return err
	}
	return nil
}

type ParkingOccupationStat struct {
	TotalCount    int64
	OccupantCount int64
}

type ParkingOccupationHandler struct {
	Watcher      client.Watcher
	Projects     []string // 哪些站要算
	TimeLocation *time.Location

	StartTimestamp int64
	EndTimestamp   int64
	Result         map[string]map[string]map[int]*ParkingOccupationStat // device_id -> day -> hour -> ParkingOccupationStat
}

func (p *ParkingOccupationHandler) Process(ctx context.Context) error {
	if len(p.Projects) == 0 {
		return nil
	}
	for _, project := range p.Projects {
		if project == "pus2" || project == "powerswap2" {
			err := p.calculateParkingOccupation(ctx, "algorithm-daily-report-powerswap2")
			if err != nil {
				return err
			}
		} else if project == "pus3" {
			err := p.calculateParkingOccupation(ctx, "algorithm-daily-report-pus3")
			if err != nil {
				return err
			}
		}
	}
	err := p.store(ctx)
	if err != nil {
		return err
	}

	return nil
}

func (p *ParkingOccupationHandler) store(ctx context.Context) error {
	for deviceId, dayHourRes := range p.Result {
		for day, hourRes := range dayHourRes {
			for hour, stat := range hourRes {
				filter := bson.D{
					bson.E{Key: "device_id", Value: deviceId},
					bson.E{Key: "service_day", Value: day},
					bson.E{Key: "hour", Value: hour},
				}
				updateDoc := map[string]interface{}{
					"device_id":                   deviceId,
					"service_day":                 day,
					"hour":                        hour,
					"total_sapa_count":            stat.TotalCount,
					"sapa_predict_occupant_count": stat.OccupantCount,
					"date":                        time.UnixMilli(time.Now().UnixMilli()),
				}
				indexOptions := []client.IndexOption{
					{Name: "uniq_device_id_day_hour", Fields: bson.D{{"device_id", 1}, {"service_day", 1}, {"hour", 1}}, Unique: true},
					{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 180 * 24 * 3600},
				}
				err := p.Watcher.PLCMongodb().UpsertSapaParkingOccupation("algorithm", "sapa_parking_occupation", filter, updateDoc, indexOptions...)
				if err != nil {
					return err
				}
			}
		}
	}

	return nil
}

func (p *ParkingOccupationHandler) calculateParkingOccupation(ctx context.Context, dbName string) error {

	for i := 1; i <= 12; i++ {
		collectionName := fmt.Sprintf("SAPA_%v", i)
		var algorithmDailyReports []umw.MongoAlgorithmDailyReport
		cur, err := p.Watcher.Mongodb().Client.Database(dbName).Collection(collectionName).Find(ctx, bson.D{}, options.Find().SetSort(bson.M{"date": -1}).SetLimit(1)) // 降序拿最大
		if err = cur.All(ctx, &algorithmDailyReports); err != nil {
			return err
		}
		if len(algorithmDailyReports) == 0 {
			continue
		}
		maxEndtime := algorithmDailyReports[0].EndTime

		cur, err = p.Watcher.Mongodb().Client.Database(dbName).Collection(collectionName).Find(ctx, bson.D{}, options.Find().SetSort(bson.M{"date": 1}).SetLimit(1)) // 升序拿最小
		if err = cur.All(ctx, &algorithmDailyReports); err != nil {
			return err
		}
		minEndTime := algorithmDailyReports[0].EndTime
		if p.StartTimestamp > maxEndtime || p.EndTimestamp < minEndTime {
			continue
		}
		err = p.calculate(ctx, dbName, collectionName)
		if err != nil {
			return err
		}
	}
	return nil
}

func (p *ParkingOccupationHandler) calculate(ctx context.Context, db, collection string) error {
	filter := bson.D{
		{"date", bson.M{"$gte": time.UnixMilli(p.StartTimestamp), "$lte": time.UnixMilli(p.EndTimestamp)}},
	}
	Limit := int64(1000)
	Offset := int64(0)
	for true {
		var algorithmDailyReports []umw.MongoAlgorithmDailyReport
		cur, err := p.Watcher.Mongodb().Client.Database(db).Collection(collection).Find(ctx, filter, options.Find().SetSort(bson.M{"date": 1}).SetSkip(Offset).SetLimit(Limit))
		if err = cur.All(ctx, &algorithmDailyReports); err != nil {
			return err
		}

		for _, report := range algorithmDailyReports {
			if !(report.EndTime >= p.StartTimestamp && report.EndTime <= p.EndTimestamp) {
				continue
			}
			deviceId := report.DeviceId
			currTime := time.UnixMilli(report.EndTime).In(p.TimeLocation)
			dayStr := currTime.Format("2006-01-02")
			hour := currTime.Hour()
			if p.Result == nil {
				p.Result = map[string]map[string]map[int]*ParkingOccupationStat{}
			}
			if p.Result[deviceId] == nil {
				p.Result[deviceId] = map[string]map[int]*ParkingOccupationStat{}
			}
			if p.Result[deviceId][dayStr] == nil {
				p.Result[deviceId][dayStr] = map[int]*ParkingOccupationStat{}
			}
			if p.Result[deviceId][dayStr][hour] == nil {
				p.Result[deviceId][dayStr][hour] = &ParkingOccupationStat{
					TotalCount:    0,
					OccupantCount: 0,
				}
			}
			p.Result[deviceId][dayStr][hour].TotalCount = p.Result[deviceId][dayStr][hour].TotalCount + 1
			if report.OutputValue != nil && *report.OutputValue == 1 {
				p.Result[deviceId][dayStr][hour].OccupantCount = p.Result[deviceId][dayStr][hour].OccupantCount + 1
			}
		}

		Offset = Offset + int64(len(algorithmDailyReports))
		if int64(len(algorithmDailyReports)) != Limit {
			break
		}
	}
	return nil
}
