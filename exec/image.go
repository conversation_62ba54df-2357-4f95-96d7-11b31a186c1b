package exec

import (
	"archive/zip"
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"github.com/prometheus/client_golang/prometheus"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	udao "git.nevint.com/golang-libs/common-utils/dao"
	"git.nevint.com/golang-libs/common-utils/larkservice"
	um "git.nevint.com/golang-libs/common-utils/model"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	uprom "git.nevint.com/golang-libs/common-utils/prometheus"

	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	"git.nevint.com/welkin2/welkin-backend/domain/activity"
	domain_image "git.nevint.com/welkin2/welkin-backend/domain/image"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	"git.nevint.com/welkin2/welkin-backend/service"
	"git.nevint.com/welkin2/welkin-backend/util"
)

type Image interface {
	UploadImage(*uprom.Prometheus, string) gin.HandlerFunc
	GetImageTypeList() gin.HandlerFunc
	GetSpecifiedDeviceImages() gin.HandlerFunc
	GetImagesByProject() gin.HandlerFunc
	GetImageAlgorithmMapping() gin.HandlerFunc
	DeleteImage() gin.HandlerFunc
	GetCameraInfo() gin.HandlerFunc
	UpdateCameraJudgeResult() gin.HandlerFunc
	ClearCameraBlacklist() gin.HandlerFunc
	GetCameraDevice() gin.HandlerFunc
	GetCameraDetails() gin.HandlerFunc
	GetCameraTableInfo() gin.HandlerFunc
	SyncCameraTable() gin.HandlerFunc
	GetCameraAcceptanceResult() gin.HandlerFunc
	SendCameraAcceptanceResult() gin.HandlerFunc
}

type image struct {
	mu      sync.Mutex
	config  *ucfg.Config
	area    string
	watcher client.Watcher
	logger  *zap.SugaredLogger
	OSS     service.OSS
	FMS     service.FMS
	WF      service.WF
}

func NewImageHandler(watcher client.Watcher, conf *ucfg.Config, area string) Image {
	handler := &image{
		config:  conf,
		area:    area,
		watcher: watcher,
		logger:  log.Logger.Named("Welkin-Image"),
		OSS: service.OSS{
			URL:       conf.OSS.PowUrl,
			BrokerURL: fmt.Sprintf("%s/plc/broker", conf.Welkin.BackendUrl),
			AppId:     conf.Sentry.AppId,
			AppSecret: conf.Sentry.AppSecret,
			Header: map[string]string{
				"Content-Type": "application/json;charset=UTF-8",
			},
			Logger: log.Logger.Named("OSS"),
		},
		WF: service.WF{
			URL:       conf.Workflow.Url,
			AppId:     conf.Sentry.AppId,
			AppSecret: conf.Sentry.AppSecret,
			FlowCode:  conf.Workflow.LogExportFlowCode,
			Header: map[string]string{
				"Content-Type": "application/json;charset=UTF-8",
			},
			Logger: log.Logger.Named("WorkFlow"),
		},
		FMS: service.FMS{
			URL:          conf.FMS.Url,
			AppId:        conf.Sentry.AppId,
			AppSecret:    conf.Sentry.AppSecret,
			ClientId:     conf.FMS.ClientId,
			ClientSecret: conf.FMS.ClientSecret,
			PriBucketKey: conf.FMS.PriBucketKey,
			PubBucketKey: conf.FMS.PubBucketKey,
			Header: map[string]string{
				"Content-Type": "application/json;charset=UTF-8",
			},
			Logger: log.Logger.Named("FMS"),
		},
	}
	return handler
}

func (i *image) GetImageTypeList() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.ImageTypeListResponse
		response.Data = make([]map[string]interface{}, 0)
		lang := c.Query("lang")
		for k, v := range umw.ImageTypeDescriptionMap[util.GetLang(i.area, lang)] {
			response.Data = append(response.Data, map[string]interface{}{"type": k, "name": fmt.Sprintf("%d-%s", k, v)})
		}
		sort.Slice(response.Data, func(i, j int) bool { return response.Data[i]["type"].(int) < response.Data[j]["type"].(int) })
		response.Total = len(response.Data)
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (i *image) GetSpecifiedDeviceImages() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			uriParam model.ImageParam
			response model.ImageListResponse
		)
		project := c.Param("project")
		deviceId := c.Param("device_id")
		if err := c.BindQuery(&uriParam); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if uriParam.StartTime == 0 || uriParam.EndTime == 0 {
			log.CtxLog(c).Errorf("`start_time` and `end_time` are required!")
			um.FailWithBadRequest(c, &response, "`start_time` and `end_time` are required!")
			return
		}
		util.SetURIParamDefault(&uriParam.CommonUriParam)
		response = model.ImageListResponse{
			Page:      uriParam.Page,
			Size:      uriParam.Size,
			StartTime: uriParam.StartTime,
			EndTime:   uriParam.EndTime,
			Data:      make([]model.ImageDetails, 0),
		}

		filter := bson.D{util.SelectedTimeDuration("image_gen_time", uriParam.StartTime, uriParam.EndTime)}
		if uriParam.ServiceId != "" {
			filter = append(filter, bson.E{Key: "service_id", Value: uriParam.ServiceId})
		}
		if uriParam.Abnormal != nil {
			var val bool
			if *uriParam.Abnormal == model.ErrorCodeOfFailure {
				val = true
			}
			filter = append(filter, bson.E{Key: "abnormal", Value: val})
		}
		if uriParam.ImageType != "" {
			typeList := make([]int, 0)
			for _, t := range strings.Split(uriParam.ImageType, ",") {
				v, e := strconv.Atoi(t)
				if e != nil {
					log.CtxLog(c).Errorf("failed to parse int, err: %v", e)
					continue
				}
				typeList = append(typeList, v)
			}
			filter = append(filter, bson.E{Key: "image_type", Value: bson.M{"$in": typeList}})
		}
		dbName := fmt.Sprintf("%s-%s", umw.ImageInfo, ucmd.RenameProjectDB(project))
		byteData, total, err := i.watcher.Mongodb().NewMongoEntry(filter).ListByPagination(dbName, deviceId,
			client.Pagination{Limit: int64(uriParam.Size), Offset: int64((uriParam.Page - 1) * uriParam.Size)},
			client.Ordered{Key: "image_gen_time", Descending: uriParam.Descending})
		if err != nil {
			log.CtxLog(c).Errorf("failed to get image info, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var records []umw.MongoImageInfo
		if err = json.Unmarshal(byteData, &records); err != nil {
			log.CtxLog(c).Errorf("failed to unmarshal image info, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Total = total
		lang := c.Query("lang")
		for _, r := range records {
			response.Data = append(response.Data, model.ImageDetails{
				ServiceId:        r.ServiceId,
				BatteryId:        r.BatteryId,
				ImageType:        r.ImageType,
				ImageCNType:      umw.ImageTypeDescriptionMap[util.GetLang(i.area, lang)][r.ImageType],
				ImageDescription: umw.ImageTypeDescriptionMap[util.GetLang(i.area, lang)][r.ImageType],
				ImageSize:        r.ImageSize,
				ImageName:        r.ImageName,
				ImageURL:         r.ImageURL,
				Abnormal:         r.Abnormal,
				CameraType:       r.CameraType,
				VehicleType:      r.VehicleType,
				ImageGenTime:     r.ImageGenTime,
			})
		}

		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

// UploadImageV1 图片类型分为运营和非运营，运营图片需要存储到cdn和minio，同时通过kafka推送给oss，非运营图片只需要存储至minio。
// minio的有效期都是6个月
func (d *device) UploadImageV1(area string, prom *uprom.Prometheus) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			errCode          int
			serviceID        string
			err              error
			uploadImgRequest model.UpLoadImgRequest

			requestData model.UploadImageRequest
			response    um.Base
		)
		d.OSS.PromCollectors = prom.MetricCollectors
		project := c.Param("project")
		deviceId := c.Param("device_id")
		version := c.Query("_version")
		lang := c.Query("lang")

		if version == "old" {
			if err := c.ShouldBind(&requestData); err != nil {
				if errors.Is(err, io.EOF) {
					log.CtxLog(c).Warnf("upload image, err: %s", err.Error())
				} else {
					log.CtxLog(c).Errorf("upload image, err: %s", err.Error())
				}
				um.FailWithBadRequest(c, &response, err.Error())
				return
			}
			log.CtxLog(c).Infof("upload image by old version, image_type: %d", *requestData.Type)
			if requestData.ServiceID == "" {
				requestData.ServiceID = "no_service_id"
			}
			f, fh, err := c.Request.FormFile("file")
			if err != nil {
				log.CtxLog(c).Errorf("upload image, err: %s", err.Error())
				um.FailWithBadRequest(c, &response, err.Error())
				return
			}
			defer f.Close()
			if fh.Size > model.MAXIMAGESIZE || fh.Size < model.MINIMAGESIZE {
				log.CtxLog(c).Errorf("upload image, file size mismatched")
				um.FailWithBadRequest(c, &response, "file size mismatched")
				return
			}
			if version != "old" {
				if err = util.CheckFileSHA512(fh, requestData.SHA512); err != nil {
					log.CtxLog(c).Errorf("upload image, err: %s", err.Error())
					um.FailWithBadRequest(c, &response, err.Error())
					return
				}
			}
			if version == "old" {
				file, err := fh.Open()
				if err != nil {
					log.CtxLog(c).Errorf("file open failed, err: " + err.Error())
					um.FailWithBadRequest(c, &response, err.Error())
					return
				}
				defer file.Close()
				fileBytes, err := ioutil.ReadAll(file)
				if err != nil {
					log.CtxLog(c).Errorf("read file failed, err: " + err.Error())
					um.FailWithBadRequest(c, &response, err.Error())
					return
				}
				requestData.SHA512 = util.GenSHA512ByBytes(fileBytes)
			}

			var abnormal bool
			imgStatusCode := requestData.ImgStatusCode
			if imgStatusCode != nil {
				if *imgStatusCode == model.ErrorCodeOfFailure {
					abnormal = true
					n := strings.Split(fh.Filename, ".")
					if !strings.HasSuffix(n[0], "_failure") {
						n[0] = fmt.Sprintf("%s_failure", n[0])
					}
					fh.Filename = strings.Join(n, ".")
				} else if *imgStatusCode != model.ErrorCodeOfNormal {
					log.CtxLog(c).Errorf("upload image, `img_status_code` can only be 0 or 1")
					um.FailWithBadRequest(c, &response, "`img_status_code` can only be 0 or 1")
					return
				}
			}

			buffer := bytes.NewBuffer(nil)
			if _, err = io.Copy(buffer, f); err != nil {
				log.CtxLog(c).Errorf("upload image, err: %s", err.Error())
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}

			origImgTS := requestData.TS
			imgData := umw.MongoImageInfo{
				DeviceId:        deviceId,
				ServiceId:       requestData.ServiceID,
				ImageType:       *requestData.Type,
				ImageSize:       fh.Size,
				ImageSHA512:     requestData.SHA512,
				ImageName:       strings.Replace(fh.Filename, "__err", fmt.Sprintf("_%d_err", time.Now().UnixMilli()), -1),
				Abnormal:        abnormal,
				ImageGenTime:    origImgTS,
				ImageUploadTime: requestData.UploadFileTime,
				Date:            util.ConvertTime(origImgTS),
			}
			if requestData.CameraType != nil {
				imgData.CameraType = *requestData.CameraType
			}
			if requestData.VehicleType != nil {
				imgData.VehicleType = *requestData.VehicleType
			}
			if requestData.BatteryId != nil {
				imgData.BatteryId = *requestData.BatteryId
			}

			imgHandler := &service.ImageBuffer{FMS: d.FMS, OSS: d.OSS, MongoClient: d.watcher.Mongodb(), GRPCClient: d.watcher.Grpc(), DeviceId: deviceId, Buffer: buffer,
				ImageData: imgData, Logger: log.CtxLog(c).Named("Image Handler"), PromCollectors: prom.MetricCollectors, Ctx: c}

			// 一代站检测照片是否异常，该算法模型只检测类型为2和4的电池上表面图片
			if project == umw.PowerSwap && (*requestData.Type == 2 || *requestData.Type == 4) {
				go imgHandler.DetectAndUploadPowerSwapAbnormalImg(c.Copy(), area, origImgTS)
			}

			appType := "internal"
			if needToOss(imgData.ImageType) {
				appType = "operation"
			}
			if err = imgHandler.UploadOneImage(area, lang, project, appType, origImgTS); err != nil {
				log.CtxLog(c).Errorf("failed to upload image, err: %s", err.Error())
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
			return
		}

		if err := c.ShouldBind(&uploadImgRequest); err != nil {
			log.CtxLog(c).Errorf("upload-image, err: %s", err.Error())
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error_code": 1, "message": err.Error()})
			return
		}
		var f multipart.File
		var fh *multipart.FileHeader
		// 获取FormFile的文件
		if project == "PUS3" {
			f, fh, err = c.Request.FormFile("file")
			if uploadImgRequest.ErrCode == nil {
				log.CtxLog(c).Errorf("upload-image, err: `errorCode` is nil")
				c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error_code": 1, "message": "`errorCode` is nil"})
				return
			}
			errCode = *uploadImgRequest.ErrCode
			serviceID = uploadImgRequest.ServiceID
		} else if project == "PowerSwap2" {
			f, fh, err = c.Request.FormFile("image")
			errCode = uploadImgRequest.ErrorCode
			serviceID = uploadImgRequest.PowerSwapServiceID
		} else {
			log.CtxLog(c).Errorf("upload-image, err: `project` is invalid, %s", project)
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error_code": 1, "message": "`project` is invalid"})
			return
		}

		if err != nil {
			log.CtxLog(c).Errorf("upload-image, err: %s", err.Error())
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error_code": 1, "message": err.Error()})
			return
		}

		defer f.Close()
		// 检查文件的大小是否超过限制，如果朝超过直接返回错误
		if fh.Size > model.MAXIMAGESIZE || fh.Size < model.MINIMAGESIZE {
			log.CtxLog(c).Errorf("upload-image, file size mismatched")
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error_code": 1, "message": "file size mismatched"})
			return
		}
		//检查文件的SHA512，如果失败直接返回错误
		if project == umw.PUS3 {
			if err = util.CheckFileSHA512(fh, uploadImgRequest.SHA512); err != nil {
				log.CtxLog(c).Errorf("upload-image, err: %s", err.Error())
				c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error_code": 1, "message": err.Error()})
				return
			}
		} else if project == umw.PowerSwap2 {
			if err = util.CheckFileMD5(fh, uploadImgRequest.Md5); err != nil {
				log.CtxLog(c).Errorf("upload-image, err: %s", err.Error())
				c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error_code": 1, "message": err.Error()})
				return
			}
		}

		var abnormal bool
		if errCode == model.ErrorCodeOfFailure {
			abnormal = true
			n := strings.Split(fh.Filename, ".")
			if !strings.HasSuffix(n[0], "_failure") {
				n[0] = fmt.Sprintf("%s_failure", n[0])
			}
			fh.Filename = strings.Join(n, ".")
		} else if errCode != model.ErrorCodeOfNormal {
			log.CtxLog(c).Errorf("upload image, `err_code` can only be 0 or 1")
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error_code": 1, "message": err.Error()})
			return
		}

		buffer := bytes.NewBuffer(nil)
		if _, err = io.Copy(buffer, f); err != nil {
			log.CtxLog(c).Errorf("upload image, err: %s", err.Error())
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error_code": 1, "message": err.Error()})
			return
		}

		if serviceID == "" {
			serviceID = "no_service_id"
		}
		imgData := umw.MongoImageInfo{
			DeviceId:        deviceId,
			ServiceId:       serviceID,
			ImageType:       *uploadImgRequest.Type,
			ImageSize:       fh.Size,
			ImageSHA512:     uploadImgRequest.SHA512,
			ImageName:       strings.Replace(fh.Filename, "__err", fmt.Sprintf("_%d_err", time.Now().UnixMilli()), -1),
			Abnormal:        abnormal,
			CameraType:      uploadImgRequest.CameraType,
			ImageGenTime:    uploadImgRequest.TS,
			ImageUploadTime: uploadImgRequest.UpLoadTs,
			Date:            util.ConvertTime(uploadImgRequest.TS),
		}

		imgHandler := &service.ImageBuffer{FMS: d.FMS, OSS: d.OSS, MongoClient: d.watcher.Mongodb(), GRPCClient: d.watcher.Grpc(), DeviceId: deviceId, Buffer: buffer,
			ImageData: imgData, Logger: log.CtxLog(c).Named("Image Handler"), PromCollectors: prom.MetricCollectors, Ctx: c}

		appType := "internal"
		if needToOss(imgData.ImageType) {
			appType = "operation"
		}

		if err = imgHandler.UploadOneImage(area, lang, project, appType, imgData.ImageGenTime); err != nil {
			log.CtxLog(c).Errorf("failed to upload image, err: %s", err.Error())
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error_code": 1, "message": err.Error()})
			return
		}
		c.JSON(http.StatusOK, gin.H{"error_code": 0, "message": "ok"})
		return
	}
}

// 设备向云发自定义数据，通过biz id分collection
func (d *device) UploadData(string, *uprom.Prometheus) gin.HandlerFunc {
	return func(c *gin.Context) {
		resp := um.Base{}
		data := map[string]interface{}{}
		bizId := c.Param("biz_id")
		project := c.Param("project")
		deviceId := c.Param("device_id")
		if strings.TrimSpace(bizId) == "" || strings.TrimSpace(strings.TrimSpace(project)) == "" || strings.TrimSpace(strings.TrimSpace(deviceId)) == "" {
			log.CtxLog(c).Errorf("bizId or project or deviceId empty")
			um.FailWithBadRequest(c, &resp, "bizId or project or deviceId empty")
			return
		}
		data["project"] = project
		data["device_id"] = deviceId
		data["create_timestamp"] = time.Now().UnixMilli()
		data["create_day"] = time.Now().Format("20060102")
		data["date"] = time.Now()
		err := c.BindJSON(&data)
		if err != nil {
			log.CtxLog(c).Errorf("UploadData: %v", err)
			um.FailWithBadRequest(c, &resp, err.Error())
			return
		}
		ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
		defer cancel()
		_, err = d.watcher.Mongodb().Client.Database("device_upload").Collection(bizId).InsertOne(ctx, data)
		if err != nil {
			log.CtxLog(c).Errorf("UploadData: %v", err)
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error_code": 1, "message": err.Error()})
			return
		}
		c.JSON(http.StatusOK, gin.H{"error_code": 0, "message": "ok"})
		return
	}
}

// 设备向云发自定义数据，数组形式上传，通过biz id分collection
func (d *device) UploadDataList(string, *uprom.Prometheus) gin.HandlerFunc {
	return func(c *gin.Context) {
		resp := um.Base{}
		var dataList []interface{}
		bizId := c.Param("biz_id")
		project := c.Param("project")
		deviceId := c.Param("device_id")
		if strings.TrimSpace(bizId) == "" || strings.TrimSpace(strings.TrimSpace(project)) == "" || strings.TrimSpace(strings.TrimSpace(deviceId)) == "" {
			log.CtxLog(c).Errorf("bizId or project or deviceId empty")
			um.FailWithBadRequest(c, &resp, "bizId or project or deviceId empty")
			return
		}
		var request []map[string]interface{}
		err := c.BindJSON(&request)
		if err != nil {
			log.CtxLog(c).Errorf("UploadData: %v", err)
			um.FailWithBadRequest(c, &resp, err.Error())
			return
		}

		for _, record := range request {
			record["project"] = project
			record["device_id"] = deviceId
			record["create_timestamp"] = time.Now().UnixMilli()
			record["date"] = time.Now()
			dataList = append(dataList, record)
		}
		err = d.watcher.Mongodb().NewMongoEntry().InsertMany("device_upload", bizId, dataList)
		if mongo.IsDuplicateKeyError(err) {
			log.CtxLog(c).Warnf("mongodb DuplicateKeyError err:%v", err)
		}
		if err != nil && !mongo.IsDuplicateKeyError(err) {
			log.CtxLog(c).Errorf("UploadData: %v", err)
			um.FailWithInternalServerError(c, &resp, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &resp, "ok", http.StatusOK)
		return
	}
}

// 设备打点数据上传，用于监控设备指标，以及搭建grafana看板
func (d *device) PushMetric() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			requestData model.UploadMetricRequest
			response    um.Base
		)
		deviceId := c.Param("device_id")
		project := c.Param("project")
		err := c.BindJSON(&requestData)
		if err != nil {
			log.CtxLog(c).Errorf("c.BindJSON err, err: %v", err.Error())
			um.FailWithBadRequest(c, &response, err.Error())
		}
		// TODO debug after delete
		log.CtxLog(c).Infof("PushMetric body:%v", ucmd.ToJsonStrIgnoreErr(requestData))

		for _, metric := range requestData.GaugeMetric {
			for key, value := range metric {
				client.GetPrometheus().MetricCollectors[client.DeviceGaugeMetric].(*prometheus.GaugeVec).WithLabelValues(deviceId, project, key).Set(value)
			}
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (d *device) PushEvent() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			requestData model.UploadEventRequest
			response    um.Base
		)
		deviceId := c.Param("device_id")
		project := c.Param("project")
		err := c.BindJSON(&requestData)
		if err != nil {
			log.CtxLog(c).Errorf("c.BindJSON err, err: %v", err.Error())
			um.FailWithBadRequest(c, &response, err.Error())
		}
		log.CtxLog(c).Infof("push device event. deviceId:%v project:%v data:%v", deviceId, project, ucmd.ToJsonStrIgnoreErr(requestData))
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

// UploadImage 图片类型分为运营(operation)和非运营(internal)，运营图片需要通过kafka推送给oss。
// 存储方式详见 https://nio.feishu.cn/wiki/wikcnJ00AeXYL47I4u5xeoj0VOC
func (i *image) UploadImage(prom *uprom.Prometheus, appType string) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			requestData model.UploadImageRequest
			response    um.Base
		)
		i.OSS.PromCollectors = prom.MetricCollectors
		project := c.Param("project")
		deviceId := c.Param("device_id")
		lang := c.Query("lang")

		if err := c.ShouldBind(&requestData); err != nil {
			if errors.Is(err, io.EOF) {
				log.CtxLog(c).Warnf("upload %s image, device_id: %s, err: %s", appType, deviceId, err.Error())
			} else {
				log.CtxLog(c).Errorf("upload %s image, device_id: %s, err: %s", appType, deviceId, err.Error())
			}
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		f, fh, err := c.Request.FormFile("file")
		if err != nil {
			log.CtxLog(c).Errorf("upload %s image, image_type: %v, service_id: %s, err: %s", appType, *requestData.Type, requestData.ServiceID, err.Error())
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		defer f.Close()
		if fh.Size > model.MAXIMAGESIZE || fh.Size < model.MINIMAGESIZE {
			log.CtxLog(c).Errorf("upload %s image, image_type: %v, service_id: %s, file size mismatched", appType, *requestData.Type, requestData.ServiceID)
			um.FailWithBadRequest(c, &response, "file size mismatched")
			return
		}
		if err = util.CheckFileSHA512(fh, requestData.SHA512); err != nil {
			log.CtxLog(c).Errorf("upload %s image, image_type: %v, service_id: %s, err: %s", appType, *requestData.Type, requestData.ServiceID, err.Error())
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		var abnormal bool
		imgStatusCode := requestData.ImgStatusCode
		if imgStatusCode != nil {
			if *imgStatusCode == model.ErrorCodeOfFailure {
				abnormal = true
				n := strings.Split(fh.Filename, ".")
				if !strings.HasSuffix(n[0], "_failure") {
					n[0] = fmt.Sprintf("%s_failure", n[0])
				}
				fh.Filename = strings.Join(n, ".")
			} else if *imgStatusCode != model.ErrorCodeOfNormal {
				log.CtxLog(c).Errorf("upload %s image, image_type: %v, service_id: %s, `img_status_code` can only be 0 or 1", appType, *requestData.Type, requestData.ServiceID)
				um.FailWithBadRequest(c, &response, "`img_status_code` can only be 0 or 1")
				return
			}
		}

		buffer := bytes.NewBuffer(nil)
		if _, err = io.Copy(buffer, f); err != nil {
			log.CtxLog(c).Errorf("upload %s image, image_type: %v, service_id: %s, err: %s", appType, *requestData.Type, requestData.ServiceID, err.Error())
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}

		origImgTS := requestData.TS
		sid := requestData.ServiceID
		if sid == "" {
			sid = "no_service_id"
		}
		imgData := umw.MongoImageInfo{
			DeviceId:    deviceId,
			ServiceId:   sid,
			ImageType:   *requestData.Type,
			ImageSize:   fh.Size,
			ImageSHA512: requestData.SHA512,
			// 对于__err.jpg图片名称，可能会出现类型不一样，但名称一样，所以名称加一个时间戳，保证唯一性
			ImageName:       strings.Replace(fh.Filename, "__err", fmt.Sprintf("_%d_err", time.Now().UnixMilli()), -1),
			Abnormal:        abnormal,
			ImageGenTime:    origImgTS,
			ImageUploadTime: requestData.UploadFileTime,
			Date:            util.ConvertTime(origImgTS),
		}
		if requestData.CameraType != nil {
			imgData.CameraType = *requestData.CameraType
		}
		if requestData.VehicleType != nil {
			imgData.VehicleType = *requestData.VehicleType
		}
		if requestData.BatteryId != nil {
			imgData.BatteryId = *requestData.BatteryId
		}
		if requestData.ExtraData != nil {
			extraDataString := *requestData.ExtraData
			var extraData interface{}
			switch *requestData.Type {
			case 29: // CC
				extraData = &domain_image.CCExtraData{}
			}
			if len(extraDataString) > 0 {
				if err = json.Unmarshal([]byte(extraDataString), extraData); err != nil {
					log.CtxLog(c).Errorf("algorithm name: %d, unmarshal extra data fail, extra_data: %v, err: %v", *requestData.Type, *requestData.ExtraData, err)
					imgData.ExtraData = nil
				} else {
					imgData.ExtraData = extraData
				}
			}
		}

		imgHandler := &service.ImageBuffer{FMS: i.FMS, OSS: i.OSS, MongoClient: i.watcher.Mongodb(), GRPCClient: i.watcher.Grpc(), DeviceId: deviceId, Buffer: buffer,
			ImageData: imgData, Logger: log.CtxLog(c).Named("Image Handler"), PromCollectors: prom.MetricCollectors, Ctx: c.Copy()}
		// 一代站检测照片是否异常，该算法模型只检测类型为2和4的电池上表面图片
		if project == umw.PowerSwap && (*requestData.Type == 2 || *requestData.Type == 4) {
			go imgHandler.DetectAndUploadPowerSwapAbnormalImg(c.Copy(), i.area, origImgTS)
		}
		if err = imgHandler.UploadOneImage(i.area, lang, project, appType, origImgTS); err != nil {
			log.CtxLog(c).Errorf("failed to upload %s image, image_type: %v, service_id: %s, err: %s", appType, *requestData.Type, requestData.ServiceID, err.Error())
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

// 1: 电池下表面相机矫正算法(亏电电池),1需要发送，0不需要发送
// 2: 站电池上表面异物检测算法(亏电电池)
// 3: 电池下表面相机矫正算法(满电电池)
// 4: 电池上表面异物检测算法(满电电池)
// 9: 车轮定位算法
// 10: 车在平台算法
// 11: 人在平台算法
// 12: 枪头掉落检测算法
// 13: 停车过程中行人检测算法
// 14: 车轮定位相机检查算法
// 15: 电池上表面摄像头矫正算法
// 16: 车辆进站方向识别
// 17: 车辆到站提醒检测算法
// 18: 卷帘门防夹检测算法
func needToOss(flag int) (need bool) {
	switch flag {
	// 运营图片
	case 1, 2, 3, 4, 5, 6, 7, 8, 12, 15, 20, 23:
		need = true
	default:
	}
	return
}

func (i *image) GetImagesByProject() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			uriParam model.InternalImageDataParam
			response model.InternalImageDataResponse
		)

		project := c.Param("project")
		if err := c.BindQuery(&uriParam); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		if uriParam.Download == 1 {
			uriParam.PageNo = 1
			uriParam.PageSize = 9999999
		}

		filter := bson.D{bson.E{Key: "image_gen_time", Value: bson.M{"$gte": uriParam.StartTime, "$lte": uriParam.EndTime}}}
		imgTypeList := make([]int, 0)
		for _, name := range strings.Split(uriParam.AlgorithmName, ",") {
			v, err := strconv.Atoi(name)
			if err != nil {
				log.CtxLog(c).Warnf("failed to parse image type, err: %v", err)
				continue
			}
			imgTypeList = append(imgTypeList, v)
		}
		filter = append(filter, bson.E{Key: "image_type", Value: bson.M{"$in": imgTypeList}})
		if uriParam.CameraType != "" {
			filter = append(filter, bson.E{Key: "camera_type", Value: bson.M{"$in": strings.Split(uriParam.CameraType, ",")}})
		}
		if uriParam.VehicleType != "" {
			filter = append(filter, bson.E{Key: "vehicle_type", Value: bson.M{"$in": strings.Split(uriParam.VehicleType, ",")}})
		}
		if uriParam.FailureValue != nil {
			var abnormal bool
			if *uriParam.FailureValue == 1 {
				abnormal = true
			}
			filter = append(filter, bson.E{Key: "abnormal", Value: abnormal})
		}

		records, err := i.watcher.Mongodb().FindManyImagesByProject(project, uriParam.DeviceId, filter)
		if err != nil {
			log.CtxLog(c).Errorf("failed to get image info, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var deviceArr []string
		for deviceID := range records {
			deviceArr = append(deviceArr, deviceID)
		}
		sort.Slice(deviceArr, func(i, j int) bool {
			return records[deviceArr[i]][0].ImageGenTime > records[deviceArr[j]][0].ImageGenTime
		})
		var count int
		startIndex, endIndex := (uriParam.PageNo-1)*uriParam.PageSize, uriParam.PageNo*uriParam.PageSize
		var imageDownloadData []activity.ImageDownloadInfo
		for _, deviceId := range deviceArr {
			response.Total += len(records[deviceId])
			for _, r := range records[deviceId] {
				count++
				if count <= startIndex {
					continue
				}
				if count > endIndex {
					break
				}
				response.Data = append(response.Data, model.InternalImageData{
					Id:           r.Id.Hex(),
					ImageName:    r.ImageName,
					DeviceId:     deviceId,
					CameraType:   r.CameraType,
					ImageGenTime: r.ImageGenTime,
					ImageSize:    r.ImageSize,
					ImageUrl:     r.ImageURL,
				})
				if uriParam.Download == 1 {
					imageDownloadData = append(imageDownloadData, activity.ImageDownloadInfo{
						ImageGenTime: r.ImageGenTime,
						ImageSize:    r.ImageSize,
						ImageType:    r.ImageType,
						Abnormal:     r.Abnormal,
					})
				}
			}
		}

		if uriParam.Download != 1 {
			um.SuccessWithMessageForGin(c, &response, "success", http.StatusOK)
			return
		}

		// 下载时，更新活跃度数据
		go activity.UpdateImageDownloadInfo(c, project, imageDownloadData)
		// 拼接压缩包名字
		lang := c.Query("lang")
		imgType := imgTypeList[0]
		algorithmName := strings.SplitN(umw.ImageTypeDescriptionMap[util.GetLang(i.area, lang)][imgType], " ", 2)[0]
		if algorithmName == "-" {
			algorithmName = fmt.Sprintf("%d", imgType)
		}
		zipName := fmt.Sprintf("%s-%s-%s.zip", algorithmName, util.DecodeTime(time.UnixMilli(uriParam.StartTime)),
			util.DecodeTime(time.UnixMilli(uriParam.EndTime)))

		// 设置rw的header信息中的content-type，对于zip可选以下两种
		// rw.Header().Set("Content-Type", "application/octet-stream")
		rw := c.Writer
		rw.Header().Set("Content-Type", "application/zip")
		// 设置rw的header信息中的Content-Disposition为attachment类型
		rw.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", zipName))
		zipW := zip.NewWriter(rw)
		defer zipW.Close()
		for _, imageInfo := range response.Data {
			if imageInfo.ImageUrl == "" {
				continue
			}
			// path.Base 取以斜杠为界限的最后一个元素
			f, err := zipW.Create(imageInfo.DeviceId + "/" + imageInfo.ImageName)
			if err != nil {
				log.CtxLog(c).Errorf("zipW.Create err: %v", err)
				break
			}
			url := imageInfo.ImageUrl
			if strings.Contains(url, "minio") {
				url = strings.Replace(url, "https:", "http:", -1)
			}
			res, err := http.Get(url)
			if err != nil {
				log.CtxLog(c).Errorf("http.Get err: %v", err)
				continue
			}
			reader := bufio.NewReaderSize(res.Body, int(imageInfo.ImageSize))
			imageByte, _ := ioutil.ReadAll(reader)
			res.Body.Close()
			_, err = f.Write(imageByte)
			if err != nil {
				log.CtxLog(c).Errorf("f.Write err: %v", err)
				continue
			}
		}
	}
}

func (i *image) GetImageAlgorithmMapping() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			res model.AlgorithmNameList
		)
		for k, v := range umw.AlgorithmImageTypeListMap {
			res.Data = append(res.Data, model.AlgorithmName{Name: k, Value: v})
		}
		sort.Slice(res.Data, func(i, j int) bool { return res.Data[i].Name < res.Data[j].Name })
		um.SuccessWithMessageForGin(c, &res, "success", http.StatusOK)
	}
}
func (i *image) DeleteImage() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response um.Base
		)
		if i.area == um.Europe {
			user := c.GetHeader("X-User-ID")
			var request model.DeleteImageRequest
			if err := c.ShouldBind(&request); err != nil {
				log.CtxLog(c).Errorf("delete image, err: %s", err.Error())
				um.FailWithBadRequest(c, &response, err.Error())
				return
			}

			dbName := fmt.Sprintf("%s-%s", umw.ImageInfo, ucmd.RenameProjectDB(c.Param("project")))
			objID, err := primitive.ObjectIDFromHex(request.Id)
			if err != nil {
				log.CtxLog(c).Errorf("get objectId, err: %s", err.Error())
				um.FailWithBadRequest(c, &response, err.Error())
				return
			}
			info, err := i.watcher.Mongodb().DeleteImageInfo(dbName, request.DeviceId, bson.M{"_id": objID})
			if err != nil {
				log.CtxLog(c).Errorf("update imageUrl, err: %s", err.Error())
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}

			record := bson.M{"image_type": info.ImageType, "device_id": info.DeviceId, "service_id": info.ServiceId,
				"image_gen_time": info.ImageGenTime, "image_delete_time": time.Now().UnixMilli(), "delete_by": user}
			if err := i.watcher.Mongodb().NewMongoEntry(bson.D{bson.E{Key: "service_id", Value: info.ServiceId},
				bson.E{Key: "image_type", Value: info.ImageType}}).InsertOne(dbName, "delete-info", record); err != nil {
				log.CtxLog(c).Errorf("insert delete info, err: %s", err.Error())
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
		}
		um.SuccessWithMessageForGin(c, &response, "success", http.StatusOK)
		return
	}
}

func (i *image) GetCameraInfo() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.CameraInfoResponse
		)
		project := c.Param("project")
		colName := ucmd.RenameProjectDB(project) + "_camera_info"
		byteData, err := i.watcher.Mongodb().NewMongoEntry().ListAll("camera_management", colName, client.Ordered{})
		if err != nil {
			log.CtxLog(c).Errorf("failed to get camera info, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if err = json.Unmarshal(byteData, &response.Data); err != nil {
			log.CtxLog(c).Errorf("failed to unmarshal camera info, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if c.Query("lang") == "en" {
			for o, curr := range response.Data {
				response.Data[o].CameraName = curr.CameraNameEn
			}
		}
		um.SuccessWithMessageForGin(c, &response, "success", http.StatusOK)
		return
	}
}

// UpdateCameraJudgeResult 更新人工判定结果和算法状态
func (i *image) UpdateCameraJudgeResult() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			req      model.UpdateCameraJudgeResultRequest
			err      error
			response um.Base
		)
		project := c.Param("project")
		judgeBy := c.GetHeader("X-User-ID")
		if err = c.BindJSON(&req); err != nil {
			log.CtxLog(c).Errorf("update camera judge result, json parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		update := make(bson.M)
		if req.JudgeResult != nil {
			update["judge_result"] = *req.JudgeResult
			update["judge_by"] = judgeBy
			update["judge_ts"] = time.Now().UnixMilli()
		}
		if req.InBlacklist != nil {
			update["in_blacklist"] = *req.InBlacklist
		}
		err = i.watcher.Mongodb().NewMongoEntry(bson.D{
			{"device_id", req.DeviceId},
			{"camera_type", req.CameraType},
		}).UpdateOne("camera_management", fmt.Sprintf("%s_device_camera_data", ucmd.RenameProjectDB(project)), bson.M{"$set": update}, false)
		if err != nil {
			log.CtxLog(c).Errorf("update camera judge result error, err: %s", err.Error())
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "success", http.StatusOK)
		return
	}
}

// ClearCameraBlacklist 摄像头一键移出黑名单
func (i *image) ClearCameraBlacklist() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.GetCameraDeviceRequest
			response um.Base
		)
		project := c.Param("project")
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("clear camera blacklist, json parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		filter := client.MakeCameraManagementFilter(request)
		err := i.watcher.Mongodb().NewMongoEntry(filter).UpdateMany(
			"camera_management",
			fmt.Sprintf("%s_device_camera_data", ucmd.RenameProjectDB(project)),
			bson.M{"$set": bson.M{"in_blacklist": false}},
		)
		if err != nil {
			log.CtxLog(c).Errorf("clear camera blacklist, err: %s, project: %s, request: %+v", err.Error(), project, request)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}

		um.SuccessWithMessageForGin(c, &response, "success", http.StatusOK)
		return
	}
}

// GetCameraDevice 获取满足筛选条件的站点
func (i *image) GetCameraDevice() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.GetCameraDeviceRequest
			response model.GetCameraDeviceResponse
		)
		project := c.Param("project")
		var err error
		if err = c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("get camera device, uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if request.PageNo == 0 {
			request.PageNo = 1
		}
		if request.PageSize == 0 {
			request.PageSize = 10
		}
		response.Data, response.Total, err = i.watcher.Mongodb().GetCameraDeviceInfo(project, request, i.area, c.Query("lang"))
		if err != nil {
			log.CtxLog(c).Errorf("get camera device, mongo get camera info incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		for j, curr := range response.Data {
			response.Data[j].Id = int64((request.PageNo-1)*request.PageSize + j + 1)
			if curr.DeviceId != "" {
				conn := udao.NewRedisConn(i.watcher.Redis())
				tags, err := client.GetDeviceTag(conn, i.watcher.Mongodb(), curr.DeviceId)
				conn.Close()
				if err != nil {
					log.CtxLog(c).Errorf("cannot find the device name from mongo, device_id: %s, err: %v", curr.DeviceId, err)
				} else {
					response.Data[j].DeviceName = tags.Description
				}
			}
		}
		um.SuccessWithMessageForGin(c, &response, "success", http.StatusOK)
		return
	}
}

// GetCameraDetails 获取站点详细的照片
func (i *image) GetCameraDetails() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			req      model.GetCameraDetailRequest
			err      error
			response model.GetCameraDetailResponse
		)
		project := c.Param("project")
		if err = c.BindQuery(&req); err != nil {
			log.CtxLog(c).Errorf("get camera details, uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if len(req.CameraType) == 0 {
			req.CameraType, err = i.watcher.Mongodb().GetCameraType(project)
			if err != nil {
				log.CtxLog(c).Errorf("mongo get camera all type incorrect: %v", err)
				um.FailWithBadRequest(c, &response, err.Error())
				return
			}
		}
		var res map[string]umw.MongoImageInfo
		res, err = i.watcher.Mongodb().FindOneCameraNewestCCImage(project, req.DeviceId, req.CameraType, i.area)
		if err != nil {
			log.CtxLog(c).Errorf("mongo get camera info incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		for _, value := range res {
			response.Data = append(response.Data, model.CameraDetail{
				CameraType: value.CameraType,
				CreateTS:   value.ImageGenTime,
				ImageUrl:   value.ImageURL,
			})
		}
		sort.Slice(response.Data, func(i, j int) bool {
			return response.Data[i].CameraType < response.Data[j].CameraType
		})
		um.SuccessWithMessageForGin(c, &response, "success", http.StatusOK)
	}
}

func (i *image) GetCameraTableInfo() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.GetCameraDeviceRequest
			response model.CameraImageInfoCsvWebResponse
		)
		project := c.Param("project")
		var err error
		if err = c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("get camera table info, uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		lang := c.Query("lang")
		byteData, err := i.watcher.Mongodb().NewMongoEntry(client.MakeCameraManagementFilter(request)).ListAll("camera_management", fmt.Sprintf("%s_device_camera_data", ucmd.RenameProjectDB(project)), client.Ordered{Key: "device_id"})
		if err != nil {
			log.CtxLog(c).Errorf("fail to get camera table info: %v, request: %+v, project: %s", err, request, project)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var csvData []model.CameraImageInfo
		if err = json.Unmarshal(byteData, &csvData); err != nil {
			log.CtxLog(c).Errorf("fail to parse camera table info: %v, request: %+v, project: %s", err, request, project)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		// 摄像头信息
		cameraInfo, err := i.watcher.Mongodb().FindCameraInfo(project, bson.M{})
		if err != nil {
			return
		}
		cameraInfoMap := make(map[string]model.CameraInfo)
		for _, ci := range cameraInfo {
			cameraInfoMap[ci.CameraType] = ci
		}
		deviceMap := make(map[string]string)
		for j, curr := range csvData {
			if curr.DeviceId != "" {
				if name, ok := deviceMap[curr.DeviceId]; !ok {
					conn := udao.NewRedisConn(i.watcher.Redis())
					tags, err := client.GetDeviceTag(conn, i.watcher.Mongodb(), curr.DeviceId)
					conn.Close()
					if err != nil {
						log.CtxLog(c).Errorf("cannot find the device name from mongo, device_id: %s, err: %v", curr.DeviceId, err)
					} else {
						deviceMap[curr.DeviceId] = tags.Description
						csvData[j].DeviceName = tags.Description
					}
				} else {
					csvData[j].DeviceName = name
				}
			}
			if lang == "en" {
				csvData[j].CameraName = cameraInfoMap[curr.CameraType].CameraNameEn
			} else {
				csvData[j].CameraName = cameraInfoMap[curr.CameraType].CameraName
			}
		}
		response.Data = csvData
		response.Total = int64(len(csvData))
		um.SuccessWithMessageForGin(c, &response, "success", http.StatusOK)
	}
}

func (i *image) SyncCameraTable() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request struct {
				Projects string `json:"projects"`
			}
			response um.Base
		)
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("sync camera table, json parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		group := ucmd.NewErrGroup(c)
		group.GoRecover(func() error {
			for _, project := range strings.Split(request.Projects, ",") {
				// 处理device_camera_data中没有照片的记录
				records, err := i.watcher.Mongodb().GetEmptyImageCamera(project)
				if err != nil {
					log.CtxLog(c).Errorf("sync camera table, handle empty image records error: %v, project: %s", err, project)
					return err
				}
				i.watcher.Mongodb().UpdateDeviceImageInfo(project, i.area, records)

				// device_basic_info中的所有设备
				var expectDevices []struct {
					DeviceId string `json:"device_id" bson:"device_id"`
					Area     string `json:"area" bson:"area"`
				}
				byteData, err := i.watcher.Mongodb().ListAll(umw.OAuthDB, umw.DeviceBaseInfo, bson.D{
					{"project", project},
					{"description", bson.M{"$exists": true, "$ne": ""}}}, client.MongoOptions{Projection: &bson.M{"_id": 0, "device_id": 1, "area": 1}})
				if err != nil {
					log.CtxLog(c).Errorf("sync camera table, get expected device id error: %v, project: %s", err, project)
					return err
				}
				if err = json.Unmarshal(byteData, &expectDevices); err != nil {
					log.CtxLog(c).Errorf("sync camera table, parse expected device id error: %v, project: %s, data: %s", err, project, string(byteData))
					return err
				}

				// device_camera_data中已保存的设备
				devices, err := i.watcher.Mongodb().Client.Database("camera_management").Collection(fmt.Sprintf("%s_device_camera_data", ucmd.RenameProjectDB(project))).Distinct(c, "device_id", bson.M{})
				if err != nil {
					log.CtxLog(c).Errorf("sync camera table, get distinct device id error: %v, project: %s", err, project)
					return err
				}
				deviceMap := make(map[string]struct{})
				for j := range devices {
					deviceId, ok := devices[j].(string)
					if !ok {
						log.CtxLog(c).Warnf("sync camera table, invalid device id: %v", devices[j])
						continue
					}
					deviceMap[deviceId] = struct{}{}
				}

				// 所有摄像头类型
				cameraTypeStr, err := i.watcher.Mongodb().GetCameraType(project)
				if err != nil {
					log.CtxLog(c).Errorf("sync camera table, get camera type error: %v, project: %s", err, project)
					return err
				}
				allCameraTypes := strings.Split(cameraTypeStr, ",")

				g := ucmd.NewErrGroup(c, 10)
				// 获取device_camera_data中缺失的设备
				for j := range expectDevices {
					if _, ok := deviceMap[expectDevices[j].DeviceId]; ok {
						continue
					}
					expectDevice := expectDevices[j]
					proj := project
					g.GoRecover(func() error {
						cameraInfoList := make([]interface{}, 0)
						for _, cameraType := range allCameraTypes {
							hasImage := i.watcher.Mongodb().CheckCameraNewImage(i.watcher.Mongodb().Client.Database(fmt.Sprintf("imageinfo-%s", ucmd.RenameProjectDB(proj))).Collection(expectDevice.DeviceId), i.area, cameraType, proj)
							cameraInfoList = append(cameraInfoList, model.CameraImageInfo{
								Area:       expectDevice.Area,
								DeviceId:   expectDevice.DeviceId,
								CameraType: cameraType,
								HasImage:   hasImage,
							})
							//fmt.Printf("deviceId: %s, cameraType: %s, hasImage: %v\n", expectDevice.DeviceId, cameraType, hasImage)
						}
						if len(cameraInfoList) > 0 {
							if err = i.watcher.Mongodb().NewMongoEntry().InsertMany("camera_management", fmt.Sprintf("%s_device_camera_data", ucmd.RenameProjectDB(proj)), cameraInfoList, []client.IndexOption{
								{
									Name:   "deviceId_camera_unique",
									Fields: bson.D{{"device_id", 1}, {"camera_type", 1}},
									Unique: true,
								},
								{
									Name:   "has_image",
									Fields: bson.D{{"has_image", 1}},
								},
							}...); err != nil {
								log.CtxLog(c).Errorf("sync camera table, fail to insert device camera data, err: %v", err)
								return err
							}
						}
						return nil
					})
				}
				if err = g.Wait(); err != nil {
					return err
				}
			}
			return nil
		})

		um.SuccessWithMessageForGin(c, &response, "success", http.StatusOK)
	}
}

func (i *image) GetCameraAcceptanceResult() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.Response
		cameraInfo := &domain_image.CameraInfo{
			DeviceId: c.Query("device_id"),
			Project:  c.Param("project"),
		}
		acceptanceResult, err := cameraInfo.GetCameraAcceptanceResult(c)
		if err != nil {
			log.CtxLog(c).Errorf("fail to GetCameraAcceptanceResult, %v", err)
			if errors.Is(err, domain_image.ErrCameraNotVerified) {
				um.SuccessWithErrCode(c, &response, err.Error(), -1)
				return
			}
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = acceptanceResult
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (i *image) SendCameraAcceptanceResult() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  domain_image.SendCameraAcceptanceResultRequest
			response um.Base
		)
		acceptanceUserId := c.GetHeader("X-User-ID")
		if acceptanceUserId == "" {
			log.CtxLog(c).Errorf("SendCameraAcceptanceResult, empty user id")
			um.FailWithBadRequest(c, &response, "empty user id")
			return
		}
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("SendCameraAcceptanceResult, json parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		project := c.Param("project")
		ic := larkservice.NewInfoCard()
		ic.HeaderName = fmt.Sprintf("[%s] 摄像头验收结果", project)
		ic.AppendElement(larkservice.ElementKeyVal, []larkservice.KeyValMessage{
			{Key: "设备ID", Val: request.DeviceId},
			{Key: "设备名称", Val: request.Description},
			{Key: "验收人", Val: acceptanceUserId},
			{Key: "验收时间", Val: time.Now().Format("2006-01-02 15:04:05")},
		})
		ic.AppendElement(larkservice.ElementDetail, []larkservice.DetailMessage{
			{Msg: fmt.Sprintf("**判断结果**：\n\t%s", strings.Join(request.AcceptanceResult, "\n\t"))},
		})
		ic.AppendElement(larkservice.ElementDetail, []larkservice.DetailMessage{
			{Msg: fmt.Sprintf("**备注**：%s", request.Remark)},
		})
		ic.AppendElement(larkservice.ElementButton, []larkservice.Button{
			{Text: "点击前往天宫平台", Url: fmt.Sprintf("%s/owl/camera-management?project=%s&device_id=%s", config.Cfg.Welkin.FrontendUrl, project, request.DeviceId)},
		})
		cardContent, err := ic.Build()
		if err != nil {
			log.CtxLog(c).Errorf("SendCameraAcceptanceResult, make card err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		receiver := larkservice.Receiver{
			Type: larkim.ReceiveIdTypeUserId,
		}
		if request.IsOutside {
			receiver.ReceiveIds = []string{acceptanceUserId}
		} else if request.UserId != "" {
			receiver.ReceiveIds = []string{request.UserId}
		} else {
			// 若请求里没有user_id，则发送给验收人自己作为兜底
			receiver.ReceiveIds = []string{acceptanceUserId}
		}
		log.CtxLog(c).Infof("SendCameraAcceptanceResult, send card to %s, card content: %s", ucmd.ToJsonStrIgnoreErr(receiver), cardContent)
		if err = larkservice.SendCard(cardContent, receiver); err != nil {
			log.CtxLog(c).Errorf("SendCameraAcceptanceResult, fail to send card: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		// 去掉将该站摄像头的待验收标签
		ci := &domain_image.CameraInfo{
			DeviceId: request.DeviceId,
			Project:  project,
		}
		if err = ci.ResetAcceptance(c); err != nil {
			log.CtxLog(c).Errorf("SendCameraAcceptanceResult, fail to reset acceptance: %v, device: %s, project: %s", err, ci.DeviceId, ci.Project)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}
