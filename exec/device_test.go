package exec

import (
	"context"
	"fmt"
	"testing"

	"github.com/gin-gonic/gin"

	ucfg "git.nevint.com/golang-libs/common-utils/config"
	ulog "git.nevint.com/golang-libs/common-utils/logger"

	"git.nevint.com/welkin2/welkin-backend/client"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	"git.nevint.com/welkin2/welkin-backend/service"
)

func newTestDevice() *device {
	// Note: to fix the compile error
	//cfg := ucfg.GetWelkinConfigApollo(true)
	cfg := &ucfg.Config{}
	defer ulog.Final()
	log.Init(cfg.Log, true)

	testDevice := &device{
		config:  cfg,
		watcher: client.NewWatcher(gin.New(), cfg, true),
		logger:  log.Logger.Named(model.DEVICE),
		OSS: service.OSS{
			URL:       cfg.OSS.PowUrl,
			BrokerURL: fmt.Sprintf("%s/plc/broker", cfg.Welkin.BackendUrl),
			AppId:     cfg.Sentry.AppId,
			AppSecret: cfg.Sentry.AppSecret,
			Header: map[string]string{
				"Content-Type": "application/json;charset=UTF-8",
			},
			Logger: log.Logger.Named("OSS"),
		},
		WF: service.WF{
			URL:       cfg.Workflow.Url,
			AppId:     cfg.Sentry.AppId,
			AppSecret: cfg.Sentry.AppSecret,
			FlowCode:  cfg.Workflow.LogExportFlowCode,
			Header: map[string]string{
				"Content-Type": "application/json;charset=UTF-8",
			},
			Logger: log.Logger.Named("WorkFlow"),
		},
		FMS: service.FMS{
			URL:          cfg.FMS.Url,
			AppId:        cfg.Sentry.AppId,
			AppSecret:    cfg.Sentry.AppSecret,
			ClientId:     cfg.FMS.ClientId,
			ClientSecret: cfg.FMS.ClientSecret,
			PriBucketKey: cfg.FMS.PriBucketKey,
			PubBucketKey: cfg.FMS.PubBucketKey,
			Header: map[string]string{
				"Content-Type": "application/json;charset=UTF-8",
			},
			Logger: log.Logger.Named("FMS"),
		},
	}
	return testDevice
}

func Test_SSOAuth(t *testing.T) {
	d := newTestDevice()
	response := model.OAuthResponse{
		Project:  "PUS3",
		DeviceId: "PS-NIO-881d84b0-9b20682e",
		UserId:   "",
	}
	pwd := ""
	ssoResponse, err := service.SSOAuth(d.config, response.UserId, pwd)
	if err != nil {
		t.Errorf("failed to decrypt `password`, err: %v", err)
		return
	}
	t.Log(ssoResponse)
}

func TestOSS_SendCommandForLogFile(t *testing.T) {
	d := newTestDevice()
	//paramCode := "uploadLogFile"
	paramCode := "syncFilePath"
	paramValue := "/var/log/"
	response, ok := d.OSS.SendCommandForLogFile(context.Background(), "patrick.cheung", "PS-NIO-qiantest-test0027", paramCode, paramValue)
	if !ok {
		errCode := -3
		if response.ResultCode == "DCC_EXISTS_REMOTE_OPERATION_WITH_SAME_DEVICE_ID" {
			errCode = -4
		}
		t.Error(response, errCode)
	} else {
		t.Log(response)
	}
}

func TestFMS_GetFileUploadToken(t *testing.T) {
	d := newTestDevice()
	response, err := d.FMS.GetFileUploadToken("/Users/<USER>/test/", "test3.log", model.FILE, "", "")
	if err != nil {
		t.Error(err)
	} else {
		t.Log(response)
		supplierHttp := response.ResultData.SupplierHttp
		t.Log(supplierHttp.URL)
		t.Log(supplierHttp.Header)
		err := d.FMS.UploadFile(supplierHttp.URL, supplierHttp.Header, nil)
		if err != nil {
			t.Error(err)
		} else {
			t.Log("ok")
		}
	}
}

func TestFMS_GetFileAuthorizeURL(t *testing.T) {
	d := newTestDevice()
	urlList := []string{
		"https://cdn-pp-private-dev.eu.nio.com/welkin/0/Users/<USER>/test/test3.log",
	}
	response, err := d.FMS.GetFileAuthorizeURL(urlList)
	if err != nil {
		t.Error(err)
	} else {
		t.Log(response)
	}
}
