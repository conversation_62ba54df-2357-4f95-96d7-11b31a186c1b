package exec

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	"git.nevint.com/welkin2/welkin-backend/constant"

	"github.com/avast/retry-go"
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/rs/xid"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	"git.nevint.com/golang-libs/common-utils/croncluster"
	udao "git.nevint.com/golang-libs/common-utils/dao"
	um "git.nevint.com/golang-libs/common-utils/model"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	"git.nevint.com/welkin2/welkin-backend/client"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	"git.nevint.com/welkin2/welkin-backend/service"
	"git.nevint.com/welkin2/welkin-backend/util"
	"github.com/360EntSecGroup-Skylar/excelize"
)

type Fcrd struct {
	watcher            client.Watcher
	oss                service.OSS
	websocket          ucfg.WebsocketConfig
	logger             *zap.SugaredLogger
	scnordicURL        string
	scnordicAPIKey     string
	scnordicInstanceID string
}

var ErrEmptyData = fmt.Errorf("empty data")

// NewFcrd returns a new Frcd service instence
func NewFcrd(conf *ucfg.Config, watcher client.Watcher) *Fcrd {
	fcrd := &Fcrd{
		watcher: watcher,
		oss: service.OSS{
			NMP:    conf.OSS.NMPUrl,
			AppId:  conf.Sentry.AppId,
			Logger: log.Logger.Named("OSS"),
		},
		websocket:          conf.Websocket,
		logger:             log.Logger.Named("FCRD"),
		scnordicURL:        conf.Scnordic.URL,
		scnordicAPIKey:     conf.Scnordic.APIKey,
		scnordicInstanceID: conf.Scnordic.InstanceID,
	}
	return fcrd
}

// fetchRevenue fetches revenue data from truegreen.scnordic.com
// date format: "2021-09-01"
func (f *Fcrd) fetchRevenue(date string) ([]model.FcrdRevenueHourly, error) {
	const (
		REGULATION = "FCR-D-INC"
	)

	// http request for scnordic api with api key
	query := url.Values{}
	query.Add("apikey", f.scnordicAPIKey)
	query.Add("instanceId", f.scnordicInstanceID)
	query.Add("regulation", REGULATION)
	query.Add("productionDate", date)

	reqURL := fmt.Sprintf("%s?%s", f.scnordicURL, query.Encode())
	resp, err := http.Get(reqURL)
	if err != nil {
		f.logger.Errorf("failed to fetch revenue data from scnordic, err: %v", err)
		return nil, err
	}
	rawBody, err := io.ReadAll(resp.Body)
	if err != nil {
		f.logger.Errorf("failed to read response body, err: %v", err)
		return nil, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		err := fmt.Errorf("failed to fetch revenue data from scnordic, status code: %d, body: %s", resp.StatusCode, string(rawBody))
		f.logger.Error(err.Error())
		return nil, err
	}

	dataResp := model.FcrdRevenueAPIResponse{}
	if err := json.Unmarshal(rawBody, &dataResp); err != nil {
		f.logger.Errorf("failed to unmarshal response body, err: %v", err)
		return nil, err
	}

	l := len(dataResp.Data.Results)
	// NOTE: the Data.Results.MarketPrice may be empty, so we need to check the length of the MarketPrice
	if len(dataResp.Data.MarketPrice) != l {
		f.logger.Warnf("the length of the MarketPrice is not equal to the length of the Results, MarketPrice: %d, Results: %d", len(dataResp.Data.MarketPrice), l)
		return nil, fmt.Errorf("the length of the MarketPrice is not equal to the length of the Results")
	}

	hrs := make([]model.FcrdRevenueHourly, l)
	for i := 0; i < l; i++ {
		result := dataResp.Data.Results[i]
		price := dataResp.Data.MarketPrice[i]
		revenue, err := f.parseHourRevenue(result, price)
		if err != nil {
			f.logger.Errorf("failed to parse hour revenue, err: %v", err)
			continue
		}
		hrs[i] = *revenue
	}
	return hrs, nil
}

// storeRevenueHourly stores the hourly revenue data to mongodb
func (f *Fcrd) storeRevenueHourly(hrs []model.FcrdRevenueHourly) error {
	toAny := func(hrs []model.FcrdRevenueHourly) []any {
		a := make([]any, len(hrs))
		for i, v := range hrs {
			a[i] = v
		}
		return a
	}
	if err := f.watcher.Mongodb().NewMongoEntry().InsertMany(umw.FCRDManagement, model.FcrdRevenueHourly{}.CollectionName(), toAny(hrs)); err != nil {
		f.logger.Errorf("failed to insert revenue data to mongodb, err: %v", err)
		return err
	}
	return nil
}

// CronTaskFetchRevenue is a cron task to fetch today's revenue data from truegreen.scnordic.com and save it to mongodb
func (f *Fcrd) CronTaskFetchRevenue() {
	//T-1 Date
	// if time.Now() is 2024-05-10, then the date is 2024-05-09
	date := time.Now().AddDate(0, 0, -1).Format("2006-01-02")
	hrs, err := f.fetchRevenue(date)
	if err != nil {
		f.logger.Errorf("CronTaskFetchRevenue: failed to fetch revenue data, err: %v, date: %s", err, date)
		return
	}
	if err := f.storeRevenueHourly(hrs); err != nil {
		f.logger.Errorf("CronTaskFetchRevenue: failed to store revenue data, err: %v", err)
	}
	return
}

// AddCronJob registers the fcrd related cron tasks for the croncluster.CronTask
func (f *Fcrd) AddCronJobs(c *croncluster.CronTask) {
	// Fcrd Revenue
	// NOTE: consider the api data generation unstale, we need to fetch the data at 2:00, 12:00, 21:00
	_, err := c.AddFunc("0 2,12,21 * * *", f.CronTaskFetchRevenue)
	if err != nil {
		panic(err)
	}
}

func (f *Fcrd) parseHourRevenue(r model.DataResult, p model.DataPrice) (*model.FcrdRevenueHourly, error) {
	const (
		//TODO: rate isn't a fixed value for every station/region, it should be fetched from the database.
		idealBidCapacity = 0.3  // fixed 0.3 MW
		rate             = 0.66 // rate of our revenue
		project          = "PowerSwap2"
		deviceID         = "PS-NIO-7db7c5aa-ff6ccc64"
	)

	ts, err := time.Parse(time.RFC3339, p.Timestamp)
	if err != nil {
		f.logger.Errorf("failed to parse timestamp, err: %v", err)
		return nil, err
	}
	// if the capacity is 0, we need to calculate the revenue with the ideal bid capacity
	if r.Capacity == 0 {
		rh := &model.FcrdRevenueHourly{
			Project:           project,
			DeviceID:          deviceID,
			Timestamp:         ts.UnixMilli(),
			Date:              p.Timestamp,
			IdealBidCapacity:  idealBidCapacity,
			ActualBidCapacity: 0,
			IdealRevenue:      int32(math.Round(idealBidCapacity * p.D2 * rate * 100)),
			Revenue:           0,
			Price:             int32(math.Round(p.D2 * 100)),
		}
		return rh, nil
	}

	// guessPrice, return which price is the nearest to the guessPrice
	guessPrice := func(x, d1, d2 float64) float64 {
		if math.Abs(x-d1) < math.Abs(x-d2) {
			return d1
		}
		return d2
	}
	// count the all revenue
	allRevenue := r.Revenue + r.Sc + r.Partner
	// calculate the price, consider the calculation of float may cause the precision error, so we need to compare the result with D1 and D2
	calculatePrice := allRevenue / r.Capacity
	// determine which price, D1 or D2
	price := guessPrice(calculatePrice, p.D1, p.D2)

	// round the revenue and price to 2 decimal places, and multiply by 100 to store in the database, unit: cent
	idealRevenue := int32(math.Round(idealBidCapacity * price * rate * 100))
	revenue := int32(math.Round(r.Revenue * 100))

	rh := &model.FcrdRevenueHourly{
		Project:           project,
		DeviceID:          deviceID,
		Timestamp:         ts.UnixMilli(),
		Date:              p.Timestamp,
		IdealBidCapacity:  idealBidCapacity,
		ActualBidCapacity: r.Capacity,
		IdealRevenue:      idealRevenue,
		Revenue:           revenue,
		Price:             int32(math.Round(price * 100)),
	}

	return rh, nil
}

type sortableFcrdRevenueHourly []model.FcrdRevenueHourly

func (s sortableFcrdRevenueHourly) Len() int           { return len(s) }
func (s sortableFcrdRevenueHourly) Less(i, j int) bool { return s[i].Timestamp < s[j].Timestamp }
func (s sortableFcrdRevenueHourly) Swap(i, j int)      { s[i], s[j] = s[j], s[i] }

// windowAggregation aggregates the hourly revenue to daily revenue by windowLength
// windowLength: the length of the window, unit: hour
func (f *Fcrd) windowAggregation(windowLength int, hrs []model.FcrdRevenueHourly, startTs int64) ([]model.FcrdRevenue, error) {
	// millisecond of an hour
	const millisecondOfHour int64 = 1000 * 60 * 60
	if windowLength <= 0 {
		return nil, fmt.Errorf("window length should be greater than 0")
	}
	if len(hrs) == 0 {
		return nil, ErrEmptyData
	}
	res := []model.FcrdRevenue{}
	// sort the hourly revenue by timestamp
	if !sort.IsSorted(sortableFcrdRevenueHourly(hrs)) {
		sort.Sort(sortableFcrdRevenueHourly(hrs))
	}
	for len(hrs) > 0 {
		fmt.Println(len(hrs))
		// group the hourly revenue by windowLength, check the timestamp
		var group []model.FcrdRevenueHourly
		// the end timestamp of the group, unit: millisecond
		endTs := int64(windowLength)*millisecondOfHour + startTs
		for step, r := range hrs {
			// if the timestamp is greater than the endTs, break the loop, or the step is the last index of the slice
			if r.Timestamp >= endTs {
				group = hrs[:step]
				hrs = hrs[step:]
				break
			}
			if step == len(hrs)-1 {
				group = hrs
				hrs = nil
			}
		}
		// if the group is empty, continue to the next group
		if len(group) == 0 {
			// update the startTs
			startTs = endTs
			continue
		}
		// aggregate the hourly revenue to daily revenue
		dr, err := f.aggregateHourRevenue(group, startTs)
		if err != nil {
			err = fmt.Errorf("*Fcrd.windowAggregation error: %w", err)
			return nil, err
		}
		// update the startTs
		startTs = endTs

		// append the daily revenue to the result
		res = append(res, *dr)
		// remove the grouped hourly revenue from the original data
	}
	return res, nil
}

// aggregateHourRevenue aggregates the hourly revenue to daily revenue
func (f *Fcrd) aggregateHourRevenue(s []model.FcrdRevenueHourly, startTs int64) (*model.FcrdRevenue, error) {
	if len(s) == 0 {
		return nil, fmt.Errorf("empty data")
	}
	dailyRevenue := model.FcrdRevenue{
		Project:   s[0].Project,
		DeviceID:  s[0].DeviceID,
		Timestamp: startTs,
	}
	for _, r := range s {
		dailyRevenue.IdealBidCapacity += r.IdealBidCapacity
		dailyRevenue.ActualBidCapacity += r.ActualBidCapacity
		dailyRevenue.IdealRevenue += r.IdealRevenue
		dailyRevenue.Revenue += r.Revenue
	}
	dailyRevenue.IdealBidCapacity = math.Round((dailyRevenue.IdealBidCapacity/float64(len(s)))*100) / 100
	dailyRevenue.ActualBidCapacity = math.Round((dailyRevenue.ActualBidCapacity/float64(len(s)))*100) / 100
	return &dailyRevenue, nil
}

func (f *Fcrd) RevenueHistoryDownload() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Req is the request struct for the RevenueHistory
		type Params struct {
			Project  string `uri:"project" binding:"required"`
			DeviceID string `uri:"device_id" binding:"required"`
		}
		type Req struct {
			StartTime int64 `form:"start_time" binding:"required"`
			EndTime   int64 `form:"end_time" binding:"required"`
		}
		// Resp is the response struct for the RevenueHistory
		type Resp struct {
			um.Base
			Total   int64               `json:"total"`
			History []model.FcrdRevenue `json:"history"`
		}
		params := Params{}
		if err := c.ShouldBindUri(&params); err != nil {
			um.FailWithBadRequest(c, &um.Base{}, err.Error())
			return
		}
		req := Req{}
		if err := c.ShouldBindQuery(&req); err != nil {
			um.FailWithBadRequest(c, &um.Base{}, err.Error())
			return
		}
		if req.StartTime > req.EndTime {
			um.FailWithBadRequest(c, &um.Base{}, "start_time should be less than end_time")
			return
		}
		cli := f.watcher.Mongodb().Client
		if cli == nil {
			um.FailWithInternalServerError(c, &um.Base{}, "failed to get mongodb client")
			return
		}
		filter := bson.M{
			"project":   params.Project,
			"device_id": params.DeviceID,
			"timestamp": bson.M{"$gte": req.StartTime, "$lt": req.EndTime},
		}
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		cur, err := cli.Database(umw.FCRDManagement).Collection(model.FcrdRevenueHourly{}.CollectionName()).Find(ctx, filter)
		if err != nil {
			log.CtxLog(c).Errorf("failed to get the hourly revenue, err: %v", err)
			um.FailWithInternalServerError(c, &um.Base{}, err.Error())
			return
		}
		defer cur.Close(ctx)
		hrs := []model.FcrdRevenueHourly{}
		if err := cur.All(ctx, &hrs); err != nil {
			log.CtxLog(c).Errorf("failed to get the hourly revenue, err: %v", err)
			um.FailWithInternalServerError(c, &um.Base{}, err.Error())
			return
		}
		xlsxFile := createExcelFile(hrs)
		c.Header("Content-Type", "application/octet-stream")
		c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=revenue_history_%s.xlsx", params.DeviceID))
		if err := xlsxFile.Write(c.Writer); err != nil {
			log.CtxLog(c).Errorf("failed to write the excel file, err: %v", err)
			um.FailWithInternalServerError(c, &um.Base{}, err.Error())
			return
		}
	}
}

func createExcelFile(hrs []model.FcrdRevenueHourly) *excelize.File {
	f := excelize.NewFile()

	// 创建一个新的 Sheet
	index := f.NewSheet("Sheet1")

	// 设置表头
	f.SetCellValue("Sheet1", "A1", "Project")
	f.SetCellValue("Sheet1", "B1", "DeviceID")
	f.SetCellValue("Sheet1", "C1", "Timestamp")
	f.SetCellValue("Sheet1", "D1", "Date")
	f.SetCellValue("Sheet1", "E1", "IdealBidCapacity")
	f.SetCellValue("Sheet1", "F1", "ActualBidCapacity")
	f.SetCellValue("Sheet1", "G1", "IdealRevenue")
	f.SetCellValue("Sheet1", "H1", "Revenue")
	f.SetCellValue("Sheet1", "I1", "Price")

	// 填充数据
	for i, hr := range hrs {
		f.SetCellValue("Sheet1", fmt.Sprintf("A%d", i+2), hr.Project)
		f.SetCellValue("Sheet1", fmt.Sprintf("B%d", i+2), hr.DeviceID)
		f.SetCellValue("Sheet1", fmt.Sprintf("C%d", i+2), hr.Timestamp)
		f.SetCellValue("Sheet1", fmt.Sprintf("D%d", i+2), hr.Date)
		f.SetCellValue("Sheet1", fmt.Sprintf("E%d", i+2), hr.IdealBidCapacity)
		f.SetCellValue("Sheet1", fmt.Sprintf("F%d", i+2), hr.ActualBidCapacity)
		f.SetCellValue("Sheet1", fmt.Sprintf("G%d", i+2), hr.IdealRevenue)
		f.SetCellValue("Sheet1", fmt.Sprintf("H%d", i+2), hr.Revenue)
		f.SetCellValue("Sheet1", fmt.Sprintf("I%d", i+2), hr.Price)
	}

	// 设置活动的 Sheet
	f.SetActiveSheet(index)

	return f
}

// RevenueHistory returns the revenue history of the FCRD
func (f *Fcrd) RevenueHistory() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Req is the request struct for the RevenueHistory
		type Params struct {
			Project  string `uri:"project" binding:"required"`
			DeviceID string `uri:"device_id" binding:"required"`
		}
		type Req struct {
			StartTime int64 `form:"start_time" binding:"required"`
			EndTime   int64 `form:"end_time" binding:"required"`
		}
		// Resp is the response struct for the RevenueHistory
		type Resp struct {
			um.Base
			Total   int64               `json:"total"`
			History []model.FcrdRevenue `json:"history"`
		}
		params := Params{}
		if err := c.ShouldBindUri(&params); err != nil {
			log.CtxLog(c).Errorf("RevenueHistory: %v", err)
			um.FailWithBadRequest(c, &um.Base{}, err.Error())
			return
		}
		req := Req{}
		if err := c.ShouldBindQuery(&req); err != nil {
			log.CtxLog(c).Errorf("RevenueHistory: %v", err)
			um.FailWithBadRequest(c, &um.Base{}, err.Error())
			return
		}
		if req.StartTime > req.EndTime {
			log.CtxLog(c).Errorf("RevenueHistory: %v", "start_time should be less than end_time")
			um.FailWithBadRequest(c, &um.Base{}, "start_time should be less than end_time")
			return
		}
		cli := f.watcher.Mongodb().Client
		if cli == nil {
			log.CtxLog(c).Errorf("RevenueHistory: failed to get mongodb client")
			um.FailWithInternalServerError(c, &um.Base{}, "failed to get mongodb client")
			return
		}
		filter := bson.M{
			"project":   params.Project,
			"device_id": params.DeviceID,
			"timestamp": bson.M{"$gte": req.StartTime, "$lt": req.EndTime},
		}
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		cur, err := cli.Database(umw.FCRDManagement).Collection(model.FcrdRevenueHourly{}.CollectionName()).Find(ctx, filter)
		if err != nil {
			log.CtxLog(c).Errorf("failed to get the hourly revenue, err: %v", err)
			um.FailWithInternalServerError(c, &um.Base{}, err.Error())
			return
		}
		defer cur.Close(ctx)
		hrs := []model.FcrdRevenueHourly{}
		if err := cur.All(ctx, &hrs); err != nil {
			log.CtxLog(c).Errorf("failed to get the hourly revenue, err: %v", err)
			um.FailWithInternalServerError(c, &um.Base{}, err.Error())
			return
		}
		resp := Resp{}
		resp.History, err = f.windowAggregation(24, hrs, req.StartTime)
		if err != nil {
			if err == ErrEmptyData {
				log.CtxLog(c).Warn("records not found, start_time: %d, end_time: %d", req.StartTime, req.EndTime)
				um.SuccessWithMessageForGin(c, &resp, "records not found", http.StatusOK)
				return
			}
			log.CtxLog(c).Errorf("failed to aggregate the hourly revenue to daily revenue, err: %v", err)
			um.FailWithInternalServerError(c, &um.Base{}, err.Error())
			return
		}
		resp.Total = int64(len(resp.History))
		um.SuccessWithMessageForGin(c, &resp, "ok", http.StatusOK)
	}
}

func (f *Fcrd) GetFcrdPrice() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.AddTotalResponse
		)
		project := c.Param("project")

		if project != umw.PowerSwap2 && project != umw.PUS3 {
			log.CtxLog(c).Errorf("`project`: %s is invalid", project)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("`project`: %s is invalid", project))
			return
		}
		filter := bson.D{}
		filter = append(filter, bson.E{Key: "publish_ts", Value: util.GetDateUnix()})
		raw, err := f.watcher.Mongodb().NewMongoEntry(filter).GetOne(constant.FCRDManagement, constant.FcrdPrice)
		if err != nil {
			log.CtxLog(c).Errorf("failed to get fcrPrice, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if raw == nil {
			um.FailWithNotFound(c, &response, "records not found")
			return
		}
		var fcrdPrice model.FcrdPrice
		if err = bson.Unmarshal(raw, &fcrdPrice); err != nil {
			log.CtxLog(c).Errorf("failed to unmarshal fcrPrice, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = fcrdPrice
		response.Total = len(fcrdPrice.Records)
		fmt.Println(response.Data)
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (f *Fcrd) CreateNewPowerParams() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			requestData model.NewPowerParams
			response    um.Base
		)

		userId := c.GetHeader("X-User-ID")
		if userId == "" {
			log.CtxLog(c).Errorf("`X-User-ID` is empty")
			um.FailWithBadRequest(c, &response, "`X-User-ID` is empty")
			return
		}
		project := c.Param("project")
		if project != umw.PowerSwap2 && project != umw.PUS3 {
			log.CtxLog(c).Errorf("`project`: %s is invalid", project)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("`project`: %s is invalid", project))
			return
		}
		if err := c.BindJSON(&requestData); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if requestData.Type != "reserve" && requestData.Type != "win" {
			log.CtxLog(c).Errorf("`type`: %s is not supported", requestData.Type)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("`type`: %s is not supported", requestData.Type))
			return
		}
		if requestData.Type == "win" && (requestData.WinningBidUpCapacity == nil || requestData.WinningBidPrice == nil) {
			log.CtxLog(c).Errorf("`winning_bid_up_capacity` and `winning_bid_price` cannot be empty")
			um.FailWithBadRequest(c, &response, "`winning_bid_up_capacity` and `winning_bid_price` cannot be empty")
			return
		}

		details := make([]umw.DeviceCommandResult, 0)
		conn := udao.NewRedisConn(f.watcher.Redis())
		for _, deviceId := range requestData.DeviceIds {
			detail := umw.DeviceCommandResult{
				DeviceId: deviceId,
				ErrCode:  1,
			}
			tags, err := client.GetDeviceTag(conn, f.watcher.Mongodb(), deviceId)
			if err != nil {
				log.CtxLog(c).Errorf("cannot find the device name from mongo, device_id: %s, err: %v", deviceId, err)
			} else {
				detail.Description = tags.Description
			}
			details = append(details, detail)
		}
		conn.Close()

		record := umw.MongoPowerParamsRecord{
			Name:              requestData.Name,
			CommandId:         xid.New().String(),
			Type:              requestData.Type,
			Project:           project,
			StartTimeReserved: requestData.StartTimeReserved,
			EndTimeReserved:   requestData.EndTimeReserved,
			ReservedBatteries: requestData.ReservedBatteries,
			Creator:           userId,
			CreateTime:        time.Now().UnixMilli(),
			ErrCode:           1,
			Details:           details,
		}
		if requestData.Type == "win" {
			record.WinningBidUpCapacity = *requestData.WinningBidUpCapacity
			record.WinningBidPrice = *requestData.WinningBidPrice
		}

		indexOptions := []client.IndexOption{
			{Name: "command_id", Fields: bson.D{{"command_id", 1}}, Unique: true},
			{Name: "command_revoke_id", Fields: bson.D{{"command_revoke_id", 1}}},
			{Name: "name", Fields: bson.D{{"name", 1}}},
			{Name: "create_time", Fields: bson.D{{"create_time", 1}}},
		}
		colName := fmt.Sprintf("%s-%s", umw.PowerParamsRecord, strings.ToLower(project))
		err := f.watcher.Mongodb().NewMongoEntry(bson.D{bson.E{Key: "command_id", Value: record.CommandId}}).InsertOne(
			umw.FCRDManagement, colName, record, indexOptions...)
		if err != nil {
			log.CtxLog(c).Errorf("failed to insert a new power params record, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (f *Fcrd) UpdateNewPowerParams() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			requestData model.NewPowerParams
			response    um.Base
		)
		userId := c.GetHeader("X-User-ID")
		if userId == "" {
			log.CtxLog(c).Errorf("`X-User-ID` is empty")
			um.FailWithBadRequest(c, &response, "`X-User-ID` is empty")
			return
		}
		project := c.Param("project")
		if project != umw.PowerSwap2 && project != umw.PUS3 {
			log.CtxLog(c).Errorf("`project`: %s is invalid", project)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("`project`: %s is invalid", project))
			return
		}
		if err := c.BindJSON(&requestData); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if requestData.Type != "reserve" && requestData.Type != "win" {
			log.CtxLog(c).Errorf("`type`: %s is not supported", requestData.Type)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("`type`: %s is not supported", requestData.Type))
			return
		}
		if requestData.Type == "win" && (requestData.WinningBidUpCapacity == nil || requestData.WinningBidPrice == nil) {
			log.CtxLog(c).Errorf("`winning_bid_up_capacity` and `winning_bid_price` cannot be empty")
			um.FailWithBadRequest(c, &response, "`winning_bid_up_capacity` and `winning_bid_price` cannot be empty")
			return
		}

		commandId := c.Param("command_id")
		colName := fmt.Sprintf("%s-%s", umw.PowerParamsRecord, strings.ToLower(project))
		rawData, err := f.watcher.Mongodb().NewMongoEntry(bson.D{bson.E{Key: "command_id", Value: commandId}}).GetOne(
			umw.FCRDManagement, colName)
		if rawData == nil {
			log.CtxLog(c).Warnf("get power params record, command_id: %s err: record not found", commandId)
			um.FailWithBadRequest(c, &response, "record not found")
			return
		}
		if err != nil {
			log.CtxLog(c).Errorf("failed to get the existed power params record, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var record umw.MongoPowerParamsRecord
		if err = bson.Unmarshal(rawData, &record); err != nil {
			log.CtxLog(c).Errorf("failed to unmarshal the existed power params record, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		details := make([]umw.DeviceCommandResult, 0)
		conn := udao.NewRedisConn(f.watcher.Redis())
		for _, deviceId := range requestData.DeviceIds {
			detail := umw.DeviceCommandResult{
				DeviceId: deviceId,
				ErrCode:  1,
			}
			tags, err := client.GetDeviceTag(conn, f.watcher.Mongodb(), deviceId)
			if err != nil {
				log.CtxLog(c).Errorf("cannot find the device name from mongo, device_id: %s, err: %v", deviceId, err)
			} else {
				detail.Description = tags.Description
			}
			details = append(details, detail)
		}
		conn.Close()

		record.Name = requestData.Name
		record.StartTimeReserved = requestData.StartTimeReserved
		record.EndTimeReserved = requestData.EndTimeReserved
		record.ReservedBatteries = requestData.ReservedBatteries

		update := bson.M{
			"name":                record.Name,
			"start_time_reserved": record.StartTimeReserved,
			"end_time_reserved":   record.EndTimeReserved,
			"reserved_batteries":  record.ReservedBatteries,
			"details":             details,
		}
		if requestData.Type == "win" {
			update["winning_bid_up_capacity"] = *requestData.WinningBidUpCapacity
			update["winning_bid_price"] = *requestData.WinningBidPrice
		}
		err = f.watcher.Mongodb().NewMongoEntry().UpdateById(umw.FCRDManagement, colName, record.Id, bson.M{"$set": update})
		if err != nil {
			log.CtxLog(c).Errorf("failed to upsert new power params records, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (f *Fcrd) IssueFCRDCommand() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			key         string
			devicesList []string
			commandId   string
			requestId   string
			commandData map[string]interface{}
			response    um.Base
		)
		userId := c.GetHeader("X-User-ID")
		if userId == "" {
			log.CtxLog(c).Errorf("`X-User-ID` is empty")
			um.FailWithBadRequest(c, &response, "`X-User-ID` is empty")
			return
		}
		project := c.Param("project")
		if project != umw.PowerSwap2 && project != umw.PUS3 {
			log.CtxLog(c).Errorf("`project`: %s is invalid", project)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("`project`: %s is invalid", project))
			return
		}

		category := c.Param("category")
		switch category {
		case "issue":
			// support operation: reserve, win
			requestData := struct {
				Type                 string           `json:"type" binding:"required"`
				CommandId            string           `json:"command_id" binding:"required"`
				DeviceIds            []string         `json:"device_ids" binding:"required"`
				StartTimeReserved    int64            `json:"start_time_reserved" binding:"required"`
				EndTimeReserved      int64            `json:"end_time_reserved" binding:"required"`
				ReservedBatteries    map[string]int32 `json:"reserved_batteries" binding:"required"`
				WinningBidUpCapacity *int32           `json:"winning_bid_up_capacity"`
			}{}
			if err := c.BindJSON(&requestData); err != nil {
				log.CtxLog(c).Errorf("/oss/command/issue, parse request body, err: %v", err)
				um.FailWithBadRequest(c, &response, err.Error())
				return
			}
			if requestData.Type != "reserve" && requestData.Type != "win" {
				log.CtxLog(c).Errorf("/oss/command/issue, operation: %s is not supported", requestData.Type)
				um.FailWithBadRequest(c, &response, fmt.Sprintf("/oss/command/issue, operation: %s is not supported", requestData.Type))
				return
			}
			if requestData.Type == "win" && requestData.WinningBidUpCapacity == nil {
				log.CtxLog(c).Errorf("/oss/command/issue, `winning_bid_up_capacity` cannot be empty")
				um.FailWithBadRequest(c, &response, "/oss/command/issue, `winning_bid_up_capacity` cannot be empty")
				return
			}
			commandData = map[string]interface{}{
				"start_time_reserved": requestData.StartTimeReserved,
				"end_time_reserved":   requestData.EndTimeReserved,
				"reserved_batteries":  requestData.ReservedBatteries,
			}
			devicesList = requestData.DeviceIds
			commandId = requestData.CommandId
			requestId = requestData.CommandId
			if requestData.Type == "win" {
				key = "FCRDBatteryWin"
				commandData["winning_bid_up_capacity"] = requestData.WinningBidUpCapacity
			} else {
				key = "FCRDBatteryReserve"
			}
		case "revoke":
			// support operation: reserve, win
			requestData := struct {
				Type      string   `json:"type" binding:"required"`
				DeviceIds []string `json:"device_ids" binding:"required"`
				CommandId string   `json:"command_id" binding:"required"`
			}{}
			if err := c.BindJSON(&requestData); err != nil {
				log.CtxLog(c).Errorf("/oss/command/revoke, parse request body, err: %v", err)
				um.FailWithBadRequest(c, &response, err.Error())
				return
			}
			if requestData.Type != "reserve" && requestData.Type != "win" {
				log.CtxLog(c).Errorf("/oss/command/revoke, type: %s is not supported", requestData.Type)
				um.FailWithBadRequest(c, &response, fmt.Sprintf("/oss/command/revoke, type: %s is not supported", requestData.Type))
				return
			}
			devicesList = requestData.DeviceIds
			commandId = requestData.CommandId
			requestId = xid.New().String()
			commandData = map[string]interface{}{
				"fcrd_battery_command_id": requestData.CommandId,
			}
			if requestData.Type == "reserve" {
				key = "FCRDBatteryReserveRevoke"
			} else {
				key = "FCRDBatteryWinRevoke"
			}
		default:
			log.CtxLog(c).Errorf("url: %s not found", c.Request.URL)
			um.FailWithNotFound(c, &response, fmt.Sprintf("url: %s not found", c.Request.URL))
			return
		}

		deviceIds := strings.Join(devicesList, ",")
		update := bson.M{}
		err := retry.Do(
			func() error {
				// 对于推送指令而言，request_id等于command_id
				// 对于撤销指令而言，request_id等于command_revoke_id
				err := f.oss.IssueCommand(30001, deviceIds, requestId, key, commandData)
				if err != nil {
					return err
				}
				return nil
			}, []retry.Option{
				retry.Delay(100 * time.Millisecond),
				retry.Attempts(3),
				retry.LastErrorOnly(true),
			}...)
		ts := time.Now().UnixMilli()
		if err != nil {
			switch key {
			case "FCRDBatteryReserve", "FCRDBatteryWin":
				update["err_code"] = 2 // 下发指令失败
				update["command_push_time"] = ts
				update["details.$[].command_push_time"] = ts
				update["details.$[].err_code"] = 2
				update["details.$[].fail_reason"] = err.Error()
			case "FCRDBatteryReserveRevoke", "FCRDBatteryWinRevoke":
				update["err_code"] = 5 // 撤销指令失败
				update["command_revoke_time"] = ts
				update["command_revoke_id"] = requestId
				update["details.$[].command_revoke_time"] = ts
				update["details.$[].err_code"] = 5
				update["details.$[].fail_reason"] = err.Error()
			}
			log.CtxLog(c).Errorf("failed to push %s strategy to %s, request_id: %s, err: %v", key, deviceIds, requestId, err)
		} else {
			switch key {
			case "FCRDBatteryReserve", "FCRDBatteryWin":
				update["err_code"] = 3 // 指令下发中
				update["command_push_time"] = ts
				update["details.$[].command_push_time"] = ts
				update["details.$[].err_code"] = 3
			case "FCRDBatteryReserveRevoke", "FCRDBatteryWinRevoke":
				update["err_code"] = 6 // 指令撤销中
				update["command_revoke_id"] = requestId
				update["command_revoke_time"] = ts
				update["details.$[].command_revoke_time"] = ts
				update["details.$[].err_code"] = 6
			}
			log.CtxLog(c).Infof("succeeded to push %s strategy to %s, request_id: %s", key, deviceIds, requestId)
		}
		colName := fmt.Sprintf("%s-%s", umw.PowerParamsRecord, strings.ToLower(project))
		err = f.watcher.Mongodb().NewMongoEntry(bson.D{bson.E{Key: "command_id", Value: commandId}}).UpdateOne(
			umw.FCRDManagement, colName, bson.M{"$set": update}, false)
		if err != nil {
			log.CtxLog(c).Errorf("failed to update power params record, device_ids: %s, request_id: %s, err: %v", deviceIds, requestId, err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		log.CtxLog(c).Infof("succeeded to update power params record, device_ids: %s, request_id: %s", deviceIds, requestId)
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (f *Fcrd) ListPowerParamsRecord() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.AddTotalResponse
		)
		project := c.Param("project")
		if project != umw.PowerSwap2 && project != umw.PUS3 {
			log.CtxLog(c).Errorf("`project`: %s is invalid", project)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("`project`: %s is invalid", project))
			return
		}
		uriParam := struct {
			model.CommonUriInTimeRangeParam
			Name    *string `form:"name" json:"name" uri:"name"`
			ErrCode *int32  `form:"err_code" json:"err_code" uri:"err_code"`
			Type    string  `form:"type" json:"type" uri:"type"`
		}{}
		if err := c.BindQuery(&uriParam); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		filter := bson.D{}
		if uriParam.StartTime != 0 || uriParam.EndTime != 0 {
			filter = bson.D{util.SelectedTimeDuration("create_time", uriParam.StartTime, uriParam.EndTime)}
		}
		if uriParam.Name != nil {
			filter = append(filter, bson.E{Key: "name", Value: *uriParam.Name})
		}
		if uriParam.ErrCode != nil {
			filter = append(filter, bson.E{Key: "err_code", Value: *uriParam.ErrCode})
		}
		if uriParam.Type != "" {
			filter = append(filter, bson.E{Key: "type", Value: uriParam.Type})
		}
		colName := fmt.Sprintf("%s-%s", umw.PowerParamsRecord, strings.ToLower(project))
		byteData, total, err := f.watcher.Mongodb().NewMongoEntry(filter).ListByPagination(
			umw.FCRDManagement, colName, client.Pagination{Limit: int64(uriParam.Size), Offset: int64((uriParam.Page - 1) * uriParam.Size)},
			client.Ordered{Key: "create_time", Descending: true})
		if err != nil {
			log.CtxLog(c).Errorf("failed to get power params records, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var records []umw.MongoPowerParamsRecord
		if err = json.Unmarshal(byteData, &records); err != nil {
			log.CtxLog(c).Errorf("failed to unmarshal power params records, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = records
		response.Total = int(total)
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (f *Fcrd) GetPowerParamsRecord() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.AddTotalResponse
		)
		project := c.Param("project")
		if project != umw.PowerSwap2 && project != umw.PUS3 {
			log.CtxLog(c).Errorf("`project`: %s is invalid", project)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("`project`: %s is invalid", project))
			return
		}
		uriParam := struct {
			model.CommonUriInTimeRangeParam
			Name    *string `form:"name" json:"name" uri:"name"`
			ErrCode *int32  `form:"err_code" json:"err_code" uri:"err_code"`
			Type    string  `form:"type" json:"type" uri:"type"`
		}{}
		if err := c.BindQuery(&uriParam); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		deviceId := c.Param("device_id")

		filter := bson.D{}
		if uriParam.StartTime != 0 || uriParam.EndTime != 0 {
			filter = bson.D{util.SelectedTimeDuration("create_time", uriParam.StartTime, uriParam.EndTime)}
		}
		if uriParam.Name != nil {
			filter = append(filter, bson.E{Key: "name", Value: *uriParam.Name})
		}
		filter = append(filter, bson.E{Key: "details.device_id", Value: deviceId})

		if uriParam.ErrCode != nil {
			filter = append(filter, bson.E{Key: "details.err_code", Value: *uriParam.ErrCode})
		}
		if uriParam.Type != "" {
			filter = append(filter, bson.E{Key: "type", Value: uriParam.Type})
		}
		colName := fmt.Sprintf("%s-%s", umw.PowerParamsRecord, strings.ToLower(project))
		byteData, total, err := f.watcher.Mongodb().NewMongoEntry(filter).ListByPagination(
			umw.FCRDManagement, colName, client.Pagination{Limit: int64(uriParam.Size), Offset: int64((uriParam.Page - 1) * uriParam.Size)},
			client.Ordered{Key: "create_time", Descending: true})
		if err != nil {
			log.CtxLog(c).Errorf("failed to get power params records, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var records []umw.MongoPowerParamsRecord
		if err = json.Unmarshal(byteData, &records); err != nil {
			log.CtxLog(c).Errorf("failed to unmarshal power params records, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = records
		results := make([]map[string]interface{}, 0)
		for _, item := range records {
			result := map[string]interface{}{
				"name":                item.Name,
				"type":                item.Type,
				"creator":             item.Creator,
				"create_time":         item.CreateTime,
				"command_push_time":   item.Details[0].CommandPushTime,
				"push_finish_time":    item.Details[0].PushFinishTime,
				"err_code":            item.Details[0].ErrCode,
				"fail_reason":         item.Details[0].FailReason,
				"command_revoke_time": item.Details[0].CommandRevokeTime,
				"revoke_finish_time":  item.Details[0].RevokeFinishTime,
				"start_time_reserved": item.StartTimeReserved,
				"end_time_reserved":   item.EndTimeReserved,
				"reserved_batteries":  item.ReservedBatteries,
			}
			if item.Type == "win" {
				result["winning_bid_up_capacity"] = item.WinningBidUpCapacity
				result["winning_bid_price"] = item.WinningBidPrice
			}
			results = append(results, result)
		}
		response.Total = int(total)
		response.Data = results
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (f *Fcrd) GetNetherlandsImbalancePrice() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.AddTotalResponse
		)
		project := c.Param("project")
		timeRange := c.Query("time_limit")
		if timeRange == "" {
			log.CtxLog(c).Errorf("`time_limit`: %s is invalid", timeRange)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("`time_limit`: %s is invalid", timeRange))
			return
		}
		timeType := string(timeRange[len(timeRange)-1])
		if timeType != "m" && timeType != "h" && timeType != "d" {
			log.CtxLog(c).Errorf("`time_limit`: %s is invalid", timeRange)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("`time_limit`: %s is invalid", timeRange))
			return
		}

		timeDuration, err := strconv.ParseInt(timeRange[:len(timeRange)-1], 10, 64)

		if err != nil {
			log.CtxLog(c).Errorf("`time_duration`: %s is invalid", c.Param("time_duration"))
			um.FailWithBadRequest(c, &response, fmt.Sprintf("`time_duration`: %s is invalid", c.Param("time_duration")))
			return
		}

		if project != umw.PowerSwap2 && project != umw.PUS3 {
			log.CtxLog(c).Errorf("`project`: %s is invalid", project)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("`project`: %s is invalid", project))
			return
		}
		origins := f.websocket.Origins
		upgrader := websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				origin := r.Header.Get("Origin")
				for _, allowOrigin := range origins {
					if allowOrigin == origin {
						return true
					}
				}
				return false
			},
		}
		conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
		if err != nil {
			log.CtxLog(c).Errorf("upgrade to websocket err: %s", err.Error())
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		ws := ucmd.NewWsClient(conn)
		go ws.Send()
		go ws.Receive()

		var historyData []struct {
			PublishTime int64   `bson:"publish_ts" json:"publish_ts"`
			MaxPrice    float64 `bson:"max_price" json:"max_price"`
			MinPrice    float64 `bson:"min_price" json:"min_price"`
			Unit        string  `bson:"unit_price" json:"unit_price"`
		}
		ticker := time.NewTicker(5 * time.Second)
		defer ticker.Stop()
		for {

			startTime := time.Now().Unix() - timeDuration*60*60

			filter := bson.D{}
			filter = append(filter, bson.E{Key: "publish_ts", Value: bson.M{"$gte": startTime, "$lte": time.Now().Unix()}})

			raw, err := f.watcher.Mongodb().ListAll(constant.FCRDManagement, constant.NetherlandsImbalancePrice, filter,
				client.MongoOptions{
					Projection: &bson.M{"_id": 0, "publish_ts": 1, "min_price": 1, "max_price": 1, "unit_price": 1},
					Ordered:    &client.Ordered{Key: "publish_ts", Descending: false}})

			if err != nil {
				log.CtxLog(c).Errorf("failed to get imbalance price record, err: %v", err)
				ws.FailMessage(&response, err.Error(), -1)
				return
			}
			if raw != nil {
				if err = json.Unmarshal(raw, &historyData); err != nil {
					log.CtxLog(c).Errorf("failed to unmarshal imbalance price record, err: %v", err)
					ws.FailMessage(&response, err.Error(), 3)
					return
				}
			}

			response.Total = len(historyData)
			fmt.Println(len(historyData))
			results := make([]map[string]interface{}, 0)
			for _, item := range historyData {
				results = append(results, map[string]interface{}{
					"publish_ts": item.PublishTime,
					"max_price":  item.MaxPrice,
					"min_price":  item.MinPrice,
					"unit_price": item.Unit,
				})
			}

			response.Data = results
			err = ws.SuccessMessage(&response, "ok")
			if err != nil {
				log.CtxLog(c).Errorf("failed to send message, err: %v", err)
				ws.FailMessage(&response, err.Error(), -1)
				return
			}
			select {
			case <-ticker.C:
				continue
			case <-ws.Done():
				log.CtxLog(c).Infof("websocket closed")
				return
			}
		}

	}
}

func (f *Fcrd) GetNetherlandsRealtime() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.Response
		)
		project := c.Param("project")
		if project != umw.PowerSwap2 && project != umw.PUS3 {
			log.CtxLog(c).Errorf("`project`: %s is invalid", project)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("`project`: %s is invalid", project))
			return
		}
		origins := f.websocket.Origins
		upgrader := websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				origin := r.Header.Get("Origin")
				for _, allowOrigin := range origins {
					if allowOrigin == origin {
						return true
					}
				}
				return false
			},
		}
		conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
		if err != nil {
			log.CtxLog(c).Errorf("upgrade to websocket err: %s", err.Error())
			return
		}
		ws := ucmd.NewWsClient(conn)
		go ws.Send()
		go ws.Receive()

		if project == umw.PowerSwap2 {
			ticker := time.NewTicker(10 * time.Second)
			defer ticker.Stop()
			for {
				rows, _ := f.watcher.TDEngine().Query("select * from netherlandsdata.realtime_power order by ts desc limit 1;")
				var ts time.Time
				var power_value float64
				for rows.Next() {
					if err := rows.Scan(&ts, &power_value); err != nil {
						fmt.Printf("failed to scan database name: %v\n", err)
						return
					}
					result := make(map[string]interface{})
					result["timestamp"] = ts
					result["power_value"] = power_value
					response.Data = result
					err = ws.SuccessMessage(&response, "ok")
					if err != nil {
						log.CtxLog(c).Errorf("failed to send message, err: %v", err)
						return
					}
					select {
					case <-ticker.C:
						continue
					case <-ws.Done():
						log.CtxLog(c).Infof("websocket closed")

						return
					}

				}
			}

		}

	}

}

func (f *Fcrd) GetRealtimeDataHistory() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.AddTotalResponse
		)
		project := c.Param("project")
		if project != umw.PowerSwap2 && project != umw.PUS3 {
			log.CtxLog(c).Errorf("`project`: %s is invalid", project)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("`project`: %s is invalid", project))
			return
		}

		deviceId := c.Param("device_id")
		timeRange := c.Query("time_limit")
		if timeRange == "" {
			log.CtxLog(c).Errorf("`time_limit`: %s is invalid", timeRange)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("`time_limit`: %s is invalid", timeRange))
			return
		}
		timeType := string(timeRange[len(timeRange)-1])
		if timeType != "m" && timeType != "h" && timeType != "d" {
			log.CtxLog(c).Errorf("`time_limit`: %s is invalid", timeRange)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("`time_limit`: %s is invalid", timeRange))
			return
		}
		timeDuration, err := strconv.ParseInt(timeRange[:len(timeRange)-1], 10, 64)
		if err != nil {
			log.CtxLog(c).Errorf("`time_limit`: %s is invalid", timeRange)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("`time_limit`: %s is invalid", timeRange))
			return
		}
		origins := f.websocket.Origins
		upgrader := websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				origin := r.Header.Get("Origin")
				for _, allowOrigin := range origins {
					if allowOrigin == origin {
						return true
					}
				}
				return false
			},
		}
		conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
		if err != nil {
			log.CtxLog(c).Errorf("upgrade to websocket err: %s", err.Error())
			return
		}
		ws := ucmd.NewWsClient(conn)
		go ws.Send()
		go ws.Receive()

		dataId := make([]string, 0)
		var dbName, stbName string
		var gridFrequency, allowChargePowerMax, needChargePowerMin, stationUsePower, baseLinePower, remoteDistributePower string
		if project == umw.PowerSwap2 {
			// 1047: 电网频率
			// 605133: 最大可充功率
			// 605134: 最小需充功率
			// 605139: 站实时功率
			// 605185: 站基线功率
			// 605137: 遥调功率
			gridFrequency = "R1047"
			allowChargePowerMax = "R605133"
			needChargePowerMin = "R605134"
			//stationUsePower = "R605139"
			stationUsePower = "R1001"
			baseLinePower = "R605185"
			remoteDistributePower = "R605137"
			//dataId = []string{"1047", "605133", "605134", "605137", "605139", "605185"}更改如果没有测试通过，再改回来
			dataId = []string{"1047", "605133", "605134", "605137", "1001", "605185"}

			dbName, stbName = umw.Device2OSSRealtime, "realtime_powerswap2"
		}

		ticker := time.NewTicker(time.Second * 5)
		for {
			var startTs int64
			ts := time.Now().UnixMilli()
			switch timeType {
			case "m":
				startTs = ts - timeDuration*60*1000
			case "h":
				startTs = ts - timeDuration*60*60*1000
			case "d":
				startTs = ts - timeDuration*24*60*60*1000
			}
			tdw := &client.TDWatcher{
				TDClient:     f.watcher.TDEngine(),
				DeviceId:     deviceId,
				StartTs:      &startTs,
				EndTs:        &ts,
				FilterFields: make([]string, 0),
				Descending:   false,
				Logger:       log.CtxLog(c).Named("TDEngine"),
			}
			scanStruct, _, err := tdw.GetFields(dbName, stbName, strings.Join(dataId, ","), true)
			if err != nil {
				log.CtxLog(c).Errorf("get oss realtime data by fields fail, err: %v", err)
				ws.FailMessage(&response, err.Error(), 1)
				return
			}
			_, rows, err := tdw.FilterDataByFields(dbName)
			if err != nil {
				log.CtxLog(c).Errorf("get oss realtime data by fields fail: %v, err: %v", tdw.FilterFields, err)
				err = ws.FailMessage(&response, err.Error(), 3)
			}
			results := make([]map[string]interface{}, 0)
			if rows != nil {
				for rows.Next() {
					// 获取动态的查询结构体
					columns := client.ReflectFields(scanStruct)
					if err = rows.Scan(columns...); err != nil {
						log.CtxLog(c).Errorf("scan oss realtime data from tdengine fail, err: %v", err)
						continue
					}
					results = append(results, map[string]interface{}{
						"timestamp":               scanStruct.FieldByName("Ts").Interface().(time.Time).UnixMilli(),
						"grid_frequency":          scanStruct.FieldByName(gridFrequency).Interface().(model.SafeType).GetRealValue(),
						"allow_charge_power_max":  scanStruct.FieldByName(allowChargePowerMax).Interface().(model.SafeType).GetRealValue(),
						"need_charge_power_min":   scanStruct.FieldByName(needChargePowerMin).Interface().(model.SafeType).GetRealValue(),
						"station_use_power":       scanStruct.FieldByName(stationUsePower).Interface().(model.SafeType).GetRealValue(),
						"base_line_power":         scanStruct.FieldByName(baseLinePower).Interface().(model.SafeType).GetRealValue(),
						"remote_distribute_power": scanStruct.FieldByName(remoteDistributePower).Interface().(model.SafeType).GetRealValue(),
					})
				}
			}
			response.Total = len(results)
			response.Data = results
			err = ws.SuccessMessage(&response, "ok")

			select {
			case <-ticker.C:
				continue
			case <-ws.Done():
				log.CtxLog(c).Infof("websocket closed")
				return
			}
		}
	}
}

func (f *Fcrd) GetWinningBidHistory() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.AddTotalResponse
		)
		project := c.Param("project")
		if project != umw.PowerSwap2 && project != umw.PUS3 {
			log.CtxLog(c).Errorf("`project`: %s is invalid", project)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("`project`: %s is invalid", project))
			return
		}

		deviceId := c.Param("device_id")
		timeRange := c.Query("time_limit")
		if timeRange == "" {
			log.CtxLog(c).Errorf("`time_limit`: %s is invalid", timeRange)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("`time_limit`: %s is invalid", timeRange))
			return
		}
		timeType := string(timeRange[len(timeRange)-1])
		if timeType != "m" && timeType != "h" && timeType != "d" {
			log.CtxLog(c).Errorf("`time_limit`: %s is invalid", timeRange)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("`time_limit`: %s is invalid", timeRange))
			return
		}
		timeDuration, err := strconv.ParseInt(timeRange[:len(timeRange)-1], 10, 64)
		if err != nil {
			log.CtxLog(c).Errorf("`time_limit`: %s is invalid", timeRange)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("`time_limit`: %s is invalid", timeRange))
			return
		}
		origins := f.websocket.Origins
		log.CtxLog(c).Infof("server origins: %v", origins)
		upgrader := websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				origin := r.Header.Get("Origin")
				log.CtxLog(c).Infof("client origin: %s", origin)
				for _, allowOrigin := range origins {
					if allowOrigin == origin {
						return true
					}
				}
				return false
			},
		}
		conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
		if err != nil {
			log.CtxLog(c).Errorf("upgrade to websocket err: %s", err.Error())
			return
		}
		ws := ucmd.NewWsClient(conn)
		go ws.Send()
		go ws.Receive()

		var winningBidData []struct {
			StartTimeReserved    int64 `json:"start_time_reserved" bson:"start_time_reserved"`
			EndTimeReserved      int64 `json:"end_time_reserved" bson:"end_time_reserved"`
			WinningBidUpCapacity int32 `json:"winning_bid_up_capacity" bson:"winning_bid_up_capacity"`
		}

		ticker := time.NewTicker(time.Second * 5)
		defer ticker.Stop()
		for {
			var startTs int64
			ts := time.Now().UnixMilli()
			switch timeType {
			case "m":
				startTs = ts - timeDuration*60*1000
			case "h":
				startTs = ts - timeDuration*60*60*1000
			case "d":
				startTs = ts - timeDuration*24*60*60*1000
			}
			log.CtxLog(c).Infof("startTs: %d, ts: %d", startTs, ts)

			filter := bson.D{
				bson.E{Key: "$or", Value: bson.A{
					bson.M{"start_time_reserved": bson.M{"$gte": startTs, "$lte": ts}},
					bson.M{"start_time_reserved": bson.M{"$lte": startTs}, "end_time_reserved": bson.M{"$gte": startTs}},
				}},
				bson.E{Key: "type", Value: "win"},
				bson.E{Key: "project", Value: project},
				bson.E{Key: "details", Value: bson.M{"$elemMatch": bson.M{"device_id": deviceId}}},
			}

			rawData, err := f.watcher.Mongodb().ListAll(umw.FCRDManagement, fmt.Sprintf("%s-%s", umw.PowerParamsRecord, strings.ToLower(project)), filter,
				client.MongoOptions{
					Projection: &bson.M{"_id": 0, "winning_bid_up_capacity": 1, "start_time_reserved": 1, "end_time_reserved": 1},
					Ordered:    &client.Ordered{Key: "create_time", Descending: false},
				})

			if err != nil {
				log.CtxLog(c).Errorf("failed to list all winning bid capacity and price: %v", err)
				ws.FailMessage(&response, err.Error(), 3)
			}
			if rawData != nil {
				if err = json.Unmarshal(rawData, &winningBidData); err != nil {
					log.CtxLog(c).Errorf("failed to unmarshal winning bid data: %v", err)
					ws.FailMessage(&response, err.Error(), 3)
					return
				}
			}
			response.Total = len(winningBidData)
			results := make([]map[string]interface{}, 0)
			AlignStartTime := func(startTs int64, dataStartTs int64) int64 {
				if startTs > dataStartTs {
					return startTs
				}
				return dataStartTs
			}
			AlignEndTime := func(endTs int64, dataEndTs int64) int64 {
				if endTs < dataEndTs {
					return endTs
				}
				return dataEndTs
			}
			for _, item := range winningBidData {
				results = append(results, map[string]interface{}{
					"start_time_reserved":     AlignStartTime(startTs, item.StartTimeReserved),
					"end_time_reserved":       AlignEndTime(ts, item.EndTimeReserved),
					"winning_bid_up_capacity": item.WinningBidUpCapacity,
				})
			}
			response.Data = results
			err = ws.SuccessMessage(&response, "ok")

			select {
			case <-ticker.C:
				continue
			case <-ws.Done():
				log.CtxLog(c).Infof("websocket closed")
				return
			}
		}
	}
}

func (f *Fcrd) GetNetherlandsEPrice() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.AddTotalResponse
		)
		project := c.Param("project")
		if project != umw.PowerSwap2 && project != umw.PUS3 {
			log.CtxLog(c).Errorf("`project`: %s is invalid", project)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("`project`: %s is invalid", project))
			return
		}

		filter := bson.D{}
		filter = append(filter, bson.E{Key: "publish_ts", Value: util.GetDateUnix()})
		filter = append(filter, bson.E{Key: "area", Value: "Netherlands"})
		raw, err := f.watcher.Mongodb().NewMongoEntry(filter).GetOne(constant.FCRDManagement, constant.EuroPriceInfo)
		if err != nil {
			log.CtxLog(c).Errorf("failed to get netherland EPrice, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if raw == nil {
			log.CtxLog(c).Warnf("no data found")
			um.FailWithBadRequest(c, &response, "no data found")
			return
		}

		var record model.PointRecord
		if err = bson.Unmarshal(raw, &record); err != nil {
			log.CtxLog(c).Errorf("failed to unmarshal netherland EPrice records, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = record
		response.Total = len(record.Points)
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (f *Fcrd) GetCurrentRealtime() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.Response
		)
		project := c.Param("project")
		if project != umw.PowerSwap2 && project != umw.PUS3 {
			log.CtxLog(c).Errorf("`project`: %s is invalid", project)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("`project`: %s is invalid", project))
			return
		}
		deviceId := c.Param("device_id")
		origins := f.websocket.Origins
		upgrader := websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				origin := r.Header.Get("Origin")
				for _, allowOrigin := range origins {
					if allowOrigin == origin {
						return true
					}
				}
				return false
			},
		}
		conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
		if err != nil {
			log.CtxLog(c).Errorf("upgrade to websocket err: %s", err.Error())
			return
		}
		ws := ucmd.NewWsClient(conn)
		go ws.Send()
		go ws.Receive()

		dataId := make([]string, 0)
		slotDataIdMap := make(map[string]string)
		var dbName, stbName string
		if project == umw.PowerSwap2 {
			for id := 605132; id <= 605139; id++ {
				if id == 605136 {
					continue
				}
				dataId = append(dataId, fmt.Sprintf("%d", id))
			}
			for id := 605180; id <= 605187; id++ {
				if id == 605181 {
					continue
				}
				dataId = append(dataId, fmt.Sprintf("%d", id))
			}
			for slot := 1; slot <= 13; slot++ {
				slotDataIdMap[fmt.Sprintf("%d", 605139+slot)] = "distribute_power"             // 分配充电功率
				slotDataIdMap[fmt.Sprintf("%d", 605187+slot)] = "limit_power"                  // 支路限流功率
				slotDataIdMap[fmt.Sprintf("%d", 500156+slot*1000)] = "bms_battery_type"        // 电池类型
				slotDataIdMap[fmt.Sprintf("%d", 500010+slot*1000)] = "branch_work_status"      // 支路工作状态
				slotDataIdMap[fmt.Sprintf("%d", 500105+slot*1000)] = "battery_pack_power"      // 电池充电功率
				slotDataIdMap[fmt.Sprintf("%d", 500108+slot*1000)] = "bms_customer_usage_soc"  // 电池用户soc
				slotDataIdMap[fmt.Sprintf("%d", 500134+slot*1000)] = "bms_chrg_power_limit_lt" // 充电请求功率
			}
			for id := range slotDataIdMap {
				dataId = append(dataId, id)
			}
			dataId = append(dataId, "1001")
			log.CtxLog(c).Infof("GetCurrentRealtime dataId: %v", dataId)
			dbName, stbName = umw.Device2OSSRealtime, "realtime_powerswap2"
		}

		ticker := time.NewTicker(time.Second * 5)
		for {
			ts := time.Now().UnixMilli()
			startTs := ts - 30*1000 // 往前推移30秒
			tdw := &client.TDWatcher{
				TDClient:     f.watcher.TDEngine(),
				DeviceId:     deviceId,
				StartTs:      &startTs,
				EndTs:        &ts,
				FilterFields: make([]string, 0),
				Descending:   true,
				Logger:       log.CtxLog(c).Named("TDEngine"),
			}
			scanStruct, _, err := tdw.GetFields(dbName, stbName, strings.Join(dataId, ","), true)
			if err != nil {
				log.CtxLog(c).Errorf("get oss realtime data by fields fail, err: %v", err)
				ws.FailMessage(&response, err.Error(), 1)
				return
			}
			_, rows, err := tdw.FilterDataByFields(dbName)
			if err != nil {
				log.CtxLog(c).Errorf("get oss realtime data by fields fail: %v, err: %v", tdw.FilterFields, err)
				err = ws.FailMessage(&response, err.Error(), 3)
			}
			result := make(map[string]interface{})
			if rows != nil {
				for rows.Next() {
					// 获取动态的查询结构体
					columns := client.ReflectFields(scanStruct)
					if err = rows.Scan(columns...); err != nil {
						log.CtxLog(c).Errorf("scan oss realtime data from tdengine fail, err: %v", err)
						continue
					}

					batteryInfo := make([]map[string]interface{}, 0)
					if project == umw.PowerSwap2 {
						for slot := 1; slot <= 13; slot++ {
							batteryInfo = append(batteryInfo, map[string]interface{}{
								"slot_id": slot,
								slotDataIdMap[fmt.Sprintf("%d", 500156+slot*1000)]: scanStruct.FieldByName(fmt.Sprintf("R%d", 500156+slot*1000)).Interface().(model.SafeType).GetRealValue(),
								slotDataIdMap[fmt.Sprintf("%d", 500134+slot*1000)]: scanStruct.FieldByName(fmt.Sprintf("R%d", 500134+slot*1000)).Interface().(model.SafeType).GetRealValue(),
								slotDataIdMap[fmt.Sprintf("%d", 605139+slot)]:      scanStruct.FieldByName(fmt.Sprintf("R%d", 605139+slot)).Interface().(model.SafeType).GetRealValue(),
								slotDataIdMap[fmt.Sprintf("%d", 500010+slot*1000)]: scanStruct.FieldByName(fmt.Sprintf("R%d", 500010+slot*1000)).Interface().(model.SafeType).GetRealValue(),
								slotDataIdMap[fmt.Sprintf("%d", 500108+slot*1000)]: scanStruct.FieldByName(fmt.Sprintf("R%d", 500108+slot*1000)).Interface().(model.SafeType).GetRealValue(),
								slotDataIdMap[fmt.Sprintf("%d", 605187+slot)]:      scanStruct.FieldByName(fmt.Sprintf("R%d", 605187+slot)).Interface().(model.SafeType).GetRealValue(),
								slotDataIdMap[fmt.Sprintf("%d", 500105+slot*1000)]: scanStruct.FieldByName(fmt.Sprintf("R%d", 500105+slot*1000)).Interface().(model.SafeType).GetRealValue(),
							})
						}

						result = map[string]interface{}{
							"battery_70_reserved_nums":  scanStruct.FieldByName("R605183").Interface().(model.SafeType).GetRealValue(),
							"battery_100_reserved_nums": scanStruct.FieldByName("R605184").Interface().(model.SafeType).GetRealValue(),
							"power_schedule_mode":       scanStruct.FieldByName("R605180").Interface().(model.SafeType).GetRealValue(),
							"protocol_version":          scanStruct.FieldByName("R605182").Interface().(model.SafeType).GetRealValue(),
							"base_line_power":           scanStruct.FieldByName("R605185").Interface().(model.SafeType).GetRealValue(),
							"can_charge_energy_max":     scanStruct.FieldByName("R605186").Interface().(model.SafeType).GetRealValue(),
							"can_discharge_energy_max":  scanStruct.FieldByName("R605187").Interface().(model.SafeType).GetRealValue(),
							"min_power_coe":             scanStruct.FieldByName("R605132").Interface().(model.SafeType).GetRealValue(),
							"allow_charge_power_max":    scanStruct.FieldByName("R605133").Interface().(model.SafeType).GetRealValue(),
							"need_charge_power_min":     scanStruct.FieldByName("R605134").Interface().(model.SafeType).GetRealValue(),
							"remote_control_enable":     scanStruct.FieldByName("R605135").Interface().(model.SafeType).GetRealValue(),
							"remote_distribute_power":   scanStruct.FieldByName("R605137").Interface().(model.SafeType).GetRealValue(),
							"station_power_limit":       scanStruct.FieldByName("R605138").Interface().(model.SafeType).GetRealValue(),
							//"station_use_power":         scanStruct.FieldByName("R605139").Interface().(model.SafeType).GetRealValue(),如果生产环境没有这个数据，可以注释掉
							"station_use_power": scanStruct.FieldByName("R1001").Interface().(model.SafeType).GetRealValue(),
							"battery_info":      batteryInfo,
						}
					}
					// 降序查找，找到其中一个则结束
					break
				}
			}
			response.Data = result
			err = ws.SuccessMessage(&response, "ok")

			select {
			case <-ticker.C:
				continue
			case <-ws.Done():
				log.CtxLog(c).Infof("websocket closed")
				return
			}
		}
	}
}
