package exec

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	"git.nevint.com/welkin2/welkin-backend/logger"
	model "git.nevint.com/welkin2/welkin-backend/model/mongo"
	"git.nevint.com/welkin2/welkin-backend/util"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type StuckDiagnosisHandler struct {
	Watcher          client.Watcher
	Cfg              ucfg.Config
	Projects         []string      // 哪些站要算 pus2 pus3
	StartTimestamp   int64         // 开始计算的时间戳
	LookBackDuration time.Duration // 回溯多久的订单数据
	TimeLocation     *time.Location
}

func (s *StuckDiagnosisHandler) calculateAlarmStep(ctx context.Context, stuckService umw.MongoStuckService) []umw.MonogoStuckStep {
	if len(stuckService.StuckAlarms) == 0 || len(stuckService.ServiceSteps) == 0 {
		return nil
	}
	res := []umw.MonogoStuckStep{}
	for i, serviceStep := range stuckService.ServiceSteps {
		if i == 0 {
			continue
		}
		currentStep := stuckService.ServiceSteps[i-1]
		stepStartTime := stuckService.ServiceSteps[i-1].EventTimestamp
		stepEndTime := serviceStep.EventTimestamp

		for _, stuckAlarm := range stuckService.StuckAlarms {
			if stuckAlarm.CreateTs >= stepStartTime && stuckAlarm.CreateTs <= stepEndTime {
				stuckStep := umw.MonogoStuckStep{
					ServiceId:          stuckService.ServiceId,
					DeviceId:           stuckService.DeviceId,
					EventCode:          currentStep.EventCode,
					EventName:          currentStep.EventName,
					StuckAlarmRecordId: stuckAlarm.Id,
					AlarmId:            stuckAlarm.AlarmId,
					Project:            stuckService.Project,
					ServiceStartTime:   stuckService.ServiceStartTime,
					Date:               time.Now(),
				}
				res = append(res, stuckStep)
			}
		}
	}
	return res
}

func (s *StuckDiagnosisHandler) getServiceSteps(ctx context.Context, stuckService umw.MongoStuckService) ([]umw.ServiceStep, error) {
	resourceId := s.getResourceId(ctx, stuckService.DeviceId)
	serviceIdParam := ""
	if stuckService.Project == "PowerSwap2" {
		serviceIdParam = fmt.Sprintf("3003=%v", stuckService.ServiceId)
	}
	if stuckService.Project == "PUS3" {
		serviceIdParam = fmt.Sprintf("500003=%v", stuckService.ServiceId)
	}
	if stuckService.Project == "PUS4" {
		serviceIdParam = fmt.Sprintf("990004=%v", stuckService.ServiceId)
	}
	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL: fmt.Sprintf("%v/pe/prime/platform/event/list?device_id=%v&event_start_time=%v&event_end_time=%v&size=200&%v",
			s.Cfg.OSS.PowUrl, resourceId, stuckService.ServiceStartTime-(1000*60*10), stuckService.ServiceEndTime+(1000*60*10), serviceIdParam),
		Method: "GET",
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		return nil, errors.New(fmt.Sprintf("getServiceSteps err. err:%v", err.Error()))
	}
	defer body.Close()
	data, err := io.ReadAll(body)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("getServiceSteps err. err:%v", err.Error()))
	}
	if statusCode != http.StatusOK {
		return nil, errors.New("getServiceSteps http response status code not ok")
	}
	var response DeviceEventResponse
	if err = json.Unmarshal(data, &response); err != nil {
		return nil, errors.New(fmt.Sprintf("getServiceSteps fail to unmarshal response, err: %v", err))
	}
	if response.ResultCode != "success" {
		return nil, errors.New(fmt.Sprintf("getServiceSteps fail to get, response.ResultCode:%v resourceId:%v", response.ResultCode, resourceId))
	}
	res := []umw.ServiceStep{}
	for _, event := range response.Data.Events {
		res = append(res, umw.ServiceStep{
			EventCode:      event.EventCode,
			EventName:      event.EventName,
			EventTimestamp: event.EventTimestamp,
		})
	}
	return res, nil
}

func (s *StuckDiagnosisHandler) getResourceId(ctx context.Context, deviceId string) string {
	deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(deviceId)
	if found {
		return deviceInfo.ResourceId
	}
	return deviceId
}

func (s *StuckDiagnosisHandler) hasOccWorkflow(ctx context.Context, deviceId string, startTimestamp, endTimestamp int64) (bool, error) {
	// 设备id 请求oss 要用虚拟id
	deviceId = s.getResourceId(ctx, deviceId)
	requestBody := map[string]interface{}{
		"query_type":     "all",
		"worksheet_type": "occ_mobile_maintenance_workflow",
		"filter_conditions": []interface{}{
			map[string]interface{}{
				"form_field":  "create_time",
				"form_value":  fmt.Sprintf("%v", startTimestamp),
				"filter_type": "time_start",
			},
			map[string]interface{}{
				"form_field":  "create_time",
				"form_value":  fmt.Sprintf("%v", endTimestamp),
				"filter_type": "time_end",
			},
			map[string]interface{}{
				"form_field":  "powerswap_id",
				"form_value":  deviceId,
				"filter_type": "equals",
			},
		},
	}
	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:    fmt.Sprintf("%v/pe/workflow/v1/worksheets/process-instance/count", s.Cfg.OSS.PowUrl),
		Method: "POST",
		Header: map[string]string{
			"Content-Type": "application/json;charset=UTF-8",
		},
		RequestBody: requestBody,
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		return false, errors.New(fmt.Sprintf("hasOccWorkflow err. err:%v", err.Error()))
	}
	if statusCode != http.StatusOK {
		return false, errors.New("hasOccWorkflow http response status code not ok")
	}
	defer body.Close()
	data, err := io.ReadAll(body)
	var response OccWorkflowResponse
	if err = json.Unmarshal(data, &response); err != nil {
		return false, errors.New(fmt.Sprintf("hasOccWorkflow fail to unmarshal response, err: %v", err))
	}
	if response.Data > 0 {
		return true, nil
	}
	return false, nil
}

type OccWorkflowResponse struct {
	RequestId   string `json:"request_id"`
	ServerTime  int64  `json:"server_time"`
	ResultCode  string `json:"result_code"`
	EncryptType int    `json:"encrypt_type"`
	Data        int    `json:"data"`
}

type DeviceEventResponse struct {
	RequestId   string `json:"request_id"`
	ServerTime  int64  `json:"server_time"`
	ResultCode  string `json:"result_code"`
	EncryptType int    `json:"encrypt_type"`
	Data        struct {
		TotalResults int `json:"total_results"`
		PageNo       int `json:"page_no"`
		Size         int `json:"size"`
		Events       []struct {
			EventCode      string `json:"event_code"`
			EventName      string `json:"event_name"`
			EventTimestamp int64  `json:"event_timestamp"`
		} `json:"events"`
	} `json:"data"`
}

func (s *StuckDiagnosisHandler) prepare(ctx context.Context) error {
	return nil
}

type MongoHiveStuckService struct {
	ServiceId  string `json:"service_id" bson:"service_id"`
	DeviceId   string `json:"device_id" bson:"device_id"`
	Project    string `json:"project" bson:"project"`
	ServiceDay string `json:"service_day" bson:"service_day"`
}

func (s *StuckDiagnosisHandler) CalculateUseHivedata(ctx context.Context, serviceDay string, doDailyStat bool) error {
	logger.CtxLog(ctx).Infof("start CalculateUseHivedata. date:%v", serviceDay)
	startExcteTime := time.Now()
	for _, project := range s.Projects {
		err := s.calculateUseHivedata(ctx, project, serviceDay)
		if err != nil {
			return err
		}
	}
	if doDailyStat {
		dailyStuckDiagnosisHandler := DailyStuckDiagnosisHandler{
			Watcher:      client.GetWatcher(),
			TimeLocation: util.GetTimeLoc(),
			Cfg:          *config.Cfg,
		}
		err := dailyStuckDiagnosisHandler.Process(ctx, serviceDay)
		if err != nil {
			logger.CtxLog(ctx).Errorf("dailyStuckDiagnosisHandler.Process err. err:%v", err)
			return err
		}
	}
	logger.CtxLog(ctx).Infof("finish CalculateUseHivedata. date:%v cost:%v", serviceDay, time.Since(startExcteTime))
	return nil
}

func (s *StuckDiagnosisHandler) calculateUseHivedata(ctx context.Context, project string, serviceDay string) error {
	projectColumName := project
	if project == "pus2" {
		projectColumName = "PowerSwap2"
	} else if project == "pus3" {
		projectColumName = "PUS3"
	} else if project == "pus4" {
		projectColumName = "PUS4"
	}

	allMongoHiveStuckServices := []MongoHiveStuckService{}
	filter := bson.D{{"service_day", serviceDay}, {"project", projectColumName}}
	Limit := int64(100)
	Offset := int64(0)
	for true {
		var mongoHiveStuckServices []MongoHiveStuckService
		cur, err := s.Watcher.PLCMongodb().Client.Database("algorithm").Collection("hive_stuck_services").Find(ctx, filter, options.Find().SetSort(bson.M{"_id": 1}).SetSkip(Offset).SetLimit(Limit))
		logger.CtxLog(ctx).Infof("db query:%v", ucmd.ToJsonStrIgnoreErr(filter))
		if err = cur.All(ctx, &mongoHiveStuckServices); err != nil {
			return err
		}

		allMongoHiveStuckServices = append(allMongoHiveStuckServices, mongoHiveStuckServices...)
		Offset = Offset + int64(len(mongoHiveStuckServices))
		if int64(len(mongoHiveStuckServices)) != Limit {
			break
		}
	}
	logger.CtxLog(ctx).Infof("stcuk calculate. allMongoHiveStuckServices count:%v", len(allMongoHiveStuckServices))

	res := []umw.MongoStuckService{}
	// 切分并遍历
	step := 100
	for i := 0; i < len(allMongoHiveStuckServices); i += step {
		// 确保切片不会超出数组的界限
		end := i + step
		if end > len(allMongoHiveStuckServices) {
			end = len(allMongoHiveStuckServices)
		}
		subSlice := allMongoHiveStuckServices[i:end]
		serviceIds := []string{}
		for _, mongoHiveStuckService := range subSlice {
			serviceIds = append(serviceIds, mongoHiveStuckService.ServiceId)
		}
		logger.CtxLog(ctx).Infof("stcuk calculate. serviceIds serviceIds:%v", serviceIds)
		var serviceInfos []model.MongoServiceInfoV2

		cur, err := s.Watcher.Mongodb().Client.Database("serviceinfo").Collection(ucmd.RenameProjectDB(projectColumName)).Find(ctx, bson.D{
			bson.E{Key: "service_id", Value: bson.M{"$in": serviceIds}},
		}, options.Find().SetSort(bson.M{"service_id": 1}))
		if err = cur.All(ctx, &serviceInfos); err != nil {
			return err
		}
		for _, serviceInfo := range serviceInfos {
			var alarmRecords []umw.MongoStuckAlarmRecord
			cur, err := s.Watcher.Mongodb().Client.Database("diagnosis-management").Collection("stuck-alarm").Find(ctx, bson.D{
				{"device_id", serviceInfo.DeviceId},
				{"create_ts", bson.M{"$gte": serviceInfo.StartTime, "$lte": serviceInfo.EndTime}},
			}, options.Find().SetSort(bson.M{"create_ts": 1}))
			if err != nil {
				logger.CtxLog(ctx).Errorf("stcuk calculate. find stuck alarm error. err:%v", err)
				//return err
			}
			if err = cur.All(ctx, &alarmRecords); err != nil {
				logger.CtxLog(ctx).Errorf("stcuk calculate. cur.All error. err:%v", err)
				//return err
			}
			for _, alarm := range alarmRecords {
				_, err := s.Watcher.Mongodb().Client.Database("diagnosis-management").Collection("stuck-alarm").UpdateOne(ctx, bson.D{
					bson.E{Key: "_id", Value: alarm.Id},
				}, bson.M{"$set": bson.M{
					"service_id": serviceInfo.ServiceId,
				}})
				if err != nil {
					return err
				}
			}
			res = append(res, umw.MongoStuckService{
				ServiceId:   serviceInfo.ServiceId,
				DeviceId:    serviceInfo.DeviceId,
				StuckAlarms: alarmRecords,
				//ServiceSteps
				Project:          projectColumName,
				ServiceStartTime: serviceInfo.StartTime,
				ServiceEndTime:   serviceInfo.EndTime,
				VehicleId:        serviceInfo.EvId,
				EvType:           serviceInfo.EvType,
				InsertTs:         time.Now().UnixMilli(),
				UpdateTs:         time.Now().UnixMilli(),
				Date:             time.Now(),
			})
		}
	}
	logger.CtxLog(ctx).Infof("stcuk calculate. res count:%v", len(res))

	for _, mongoStuckService := range res {
		// 加载设备在这个订单中的步骤信息
		serviceSteps, err := s.getServiceSteps(ctx, mongoStuckService)
		if err != nil {
			//return err
			continue
		}
		mongoStuckService.ServiceSteps = serviceSteps
		err = s.Watcher.Mongodb().UpdateOneWithIdx("diagnosis-management", "stuck-service", bson.D{
			bson.E{Key: "service_id", Value: mongoStuckService.ServiceId},
		}, mongoStuckService, true, []client.IndexOption{
			{Name: "service_id", Fields: bson.D{
				{"service_id", 1},
			}, Unique: true},
			{Name: "service_start_time_device_id", Fields: bson.D{
				{"service_start_time", 1},
				{"device_id", 1},
			}, Unique: true},
			{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 180 * 24 * 3600},
		}...)
		if err != nil {
			return err
		}

		// 更新原订单表挂车字段
		dbName := fmt.Sprintf("%v-%v", umw.ServiceInfo, project)
		collectionMonthName := util.EncodeMonth(int(time.UnixMilli(mongoStuckService.ServiceStartTime).In(s.TimeLocation).Month()))
		_, err = s.Watcher.Mongodb().Client.Database(dbName).Collection(collectionMonthName).UpdateOne(ctx, bson.D{{"service_id", mongoStuckService.ServiceId}}, bson.M{"$set": bson.M{"is_stuck": true}})
		if err != nil {
			logger.CtxLog(ctx).Errorf("update original services table stuck status fail. mongoStuckService:%v err:%v", ucmd.ToJsonStrIgnoreErr(mongoStuckService), err)
		}
		// 更新订单v2表挂车字段
		_, err = s.Watcher.Mongodb().Client.Database(umw.ServiceInfo).Collection(project).UpdateOne(ctx, bson.D{{"service_id", mongoStuckService.ServiceId}}, bson.M{"$set": bson.M{"is_stuck": true}})
		if err != nil {
			logger.CtxLog(ctx).Errorf("update original services v2 table stuck status fail. mongoStuckService:%v err:%v", ucmd.ToJsonStrIgnoreErr(mongoStuckService), err)
		}

		// 计算挂车步骤
		stuckSteps := s.calculateAlarmStep(ctx, mongoStuckService)
		for _, stuckStep := range stuckSteps {
			err = s.Watcher.Mongodb().UpdateOneWithIdx("diagnosis-management", "stuck-step", bson.D{
				bson.E{Key: "service_id", Value: stuckStep.ServiceId},
				bson.E{Key: "stuck_alarm_record_id", Value: stuckStep.StuckAlarmRecordId},
				bson.E{Key: "event_code", Value: stuckStep.EventCode},
			}, stuckStep, true, []client.IndexOption{
				{Name: "service_id_stuck_alarm_record_id_event_code", Fields: bson.D{
					{"service_id", 1},
					{"stuck_alarm_record_id", 1},
					{"event_code", 1},
				}, Unique: true},
				{Name: "event_code", Fields: bson.D{
					{"event_code", 1},
				}, Unique: false},
				{Name: "service_start_time_event_code", Fields: bson.D{
					{"service_start_time", 1},
					{"event_code", 1},
				}, Unique: false},
				{Name: "device_id_event_code_service_start_time", Fields: bson.D{
					{"device_id", 1},
					{"event_code", 1},
					{"service_start_time", 1},
				}, Unique: false},
				{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 180 * 24 * 3600},
			}...)
			if err != nil {
				return err
			}
		}
	}
	return nil
}
