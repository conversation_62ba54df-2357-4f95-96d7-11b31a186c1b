package exec

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	um "git.nevint.com/golang-libs/common-utils/model"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	domain_plc "git.nevint.com/welkin2/welkin-backend/domain/plc"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	"git.nevint.com/welkin2/welkin-backend/util"
)

type PLC interface {
	GetPLCRecords() gin.HandlerFunc
	GetSensor(area string) gin.HandlerFunc
	GetConverter(area string) gin.HandlerFunc
	GetDIRecords(area string) gin.HandlerFunc
	GetOperationLog() gin.HandlerFunc
	GetPLCExists() gin.HandlerFunc
	GetPLCArchiveStatus() gin.HandlerFunc
	RestorePLC() gin.HandlerFunc
	GetRestoredPLCRecords() gin.HandlerFunc
}

type plc struct {
	watcher client.Watcher
	logger  *zap.SugaredLogger
}

func NewPLCHandler(watcher client.Watcher) PLC {
	logger := log.Logger.Named(model.PLC)
	if err := cache.CachePLCDataID(watcher.Mongodb().Client); err != nil {
		logger.Panicf("failed to cache plc data id, err: %v", err)
	}
	return &plc{
		watcher: watcher,
		logger:  logger,
	}
}

func (p *plc) GetPLCRecords() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			uriParam model.PLCRecordParam
			response model.PLCRecordsResponse
		)
		project := c.Param("project")
		deviceId := c.Param("device_id")
		if err := c.BindQuery(&uriParam); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		response = model.PLCRecordsResponse{
			StartTime: uriParam.StartTime,
			EndTime:   uriParam.EndTime,
			Project:   project,
			DeviceId:  deviceId,
			ServiceId: uriParam.ServiceId,
		}
		if uriParam.ServiceId == "" {
			log.CtxLog(c).Errorf("`service_id` is required")
			um.FailWithBadRequest(c, &response, "`service_id` is required")
			return
		}

		if uriParam.PLStepNum == "" && uriParam.BCStepNum == "" {
			log.CtxLog(c).Errorf("`pl_step_num` or `bc_step_num`, one of them is required")
			um.FailWithBadRequest(c, &response, "`pl_step_num` or `bc_step_num`, one of them is required")
			return
		}
		if uriParam.Axis == "" {
			log.CtxLog(c).Errorf("`axis` is required")
			um.FailWithBadRequest(c, &response, "`axis` is required")
			return
		}
		if err := util.CheckTimeRange(uriParam.StartTime, uriParam.EndTime); err != nil {
			log.CtxLog(c).Errorf("err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		plcDO := &domain_plc.PLCDO{}
		cond := domain_plc.ListPLCCond{
			DeviceId:  deviceId,
			Project:   project,
			ServiceId: uriParam.ServiceId,
			StartTime: uriParam.StartTime,
			EndTime:   uriParam.EndTime,
			PLStepNum: uriParam.PLStepNum,
			BCStepNum: uriParam.BCStepNum,
		}
		res, err := plcDO.ListPLC(c, cond)
		if err != nil {
			log.CtxLog(c).Errorf("failed to list plc records, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = domain_plc.ConvertPLCDO2VO(res, uriParam.Axis)
		log.CtxLog(c).Infof("succeeded to get plc records, cond: %s", ucmd.ToJsonStrIgnoreErr(cond))
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (p *plc) GetSensor(area string) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			uriParam model.SensorParam
			response model.Response
		)
		project := c.Param("project")
		deviceId := c.Param("device_id")

		if err := c.BindQuery(&uriParam); err != nil {
			log.CtxLog(c).Errorf("get sensor data, uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		if err := util.CheckTimeRange(uriParam.StartTime, uriParam.EndTime); err != nil {
			log.CtxLog(c).Errorf("get sensor data, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if uriParam.ServiceId == "" || uriParam.VarName == "" {
			log.CtxLog(c).Errorf("both `service_id` and `varname` are required")
			um.FailWithBadRequest(c, &response, "both `service_id` and `varname` are required")
			return
		}
		if uriParam.PLStepNum == nil && uriParam.BCStepNum == nil {
			log.CtxLog(c).Errorf("`pl_step_num` or `bc_step_num`, one of them is required")
			um.FailWithBadRequest(c, &response, "`pl_step_num` or `bc_step_num`, one of them is required")
			return
		}

		sensorDO := &domain_plc.SensorDO{}
		cond := domain_plc.ListSensorCond{
			DeviceId:  deviceId,
			Project:   project,
			ServiceId: uriParam.ServiceId,
			StartTime: uriParam.StartTime,
			EndTime:   uriParam.EndTime,
			PLStepNum: uriParam.PLStepNum,
			BCStepNum: uriParam.BCStepNum,
			VarNames:  uriParam.VarName,
		}
		res, err := sensorDO.ListSensor(c, cond)
		if err != nil {
			log.CtxLog(c).Errorf("failed to list sensor data, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = res.SensorData
		log.CtxLog(c).Infof("succeeded to get sensor data, uriParam: %v, device_id: %s", uriParam, deviceId)
		um.SuccessWithMessageForGin(c, &response, "success", http.StatusOK)
	}
}

func (p *plc) GetConverter(area string) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			uriParam model.ConverterParam
			response model.Response
		)
		project := c.Param("project")
		deviceId := c.Param("device_id")

		if project != umw.PUS3 && project != umw.PowerSwap && project != umw.PowerSwap2 && project != umw.PUS4 && project != umw.FYPUS1 {
			log.CtxLog(c).Warnf("project: %s, query converter not supported", project)
			um.FailWithNotFound(c, &response, fmt.Sprintf("project: %s, query converter not supported", project))
			return
		}
		if err := c.BindQuery(&uriParam); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		if err := util.CheckTimeRange(uriParam.StartTime, uriParam.EndTime); err != nil {
			log.CtxLog(c).Errorf("err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if uriParam.ServiceId == "" || uriParam.Converter == "" {
			log.CtxLog(c).Errorf("both `service_id` and `converter` are required")
			um.FailWithBadRequest(c, &response, "both `service_id` and `converter` are required")
			return
		}
		if uriParam.PLStepNum == nil && uriParam.BCStepNum == nil {
			log.CtxLog(c).Errorf("`pl_step_num` or `bc_step_num`, one of them is required")
			um.FailWithBadRequest(c, &response, "`pl_step_num` or `bc_step_num`, one of them is required")
			return
		}

		converterDO := &domain_plc.ConverterDO{}
		cond := domain_plc.ListConverterCond{
			DeviceId:  deviceId,
			Project:   project,
			ServiceId: uriParam.ServiceId,
			StartTime: uriParam.StartTime,
			EndTime:   uriParam.EndTime,
			PLStepNum: uriParam.PLStepNum,
			BCStepNum: uriParam.BCStepNum,
			ConvNums:  uriParam.Converter,
		}
		res, err := converterDO.ListConverter(c, cond)
		if err != nil {
			log.CtxLog(c).Errorf("failed to list converter data, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = res.ConverterData
		log.CtxLog(c).Infof("succeeded to get converter data, uriParam: %v, device_id: %s", uriParam, deviceId)
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (p *plc) GetDIRecords(area string) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			uriParam model.DIRecordParam
			response model.Response
		)
		project := c.Param("project")
		deviceId := c.Param("device_id")

		if !(project == umw.PUS3 || project == umw.PUS4 || project == umw.FYPUS1) {
			log.CtxLog(c).Warnf("project: %s, query di not supported", project)
			um.FailWithNotFound(c, &response, fmt.Sprintf("project: %s, query di not supported", project))
			return
		}
		if err := c.BindQuery(&uriParam); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		if err := util.CheckTimeRange(uriParam.StartTime, uriParam.EndTime); err != nil {
			log.CtxLog(c).Errorf("err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if uriParam.ServiceId == "" || uriParam.VarName == "" {
			log.CtxLog(c).Errorf("both `service_id` and `varname` is required")
			um.FailWithBadRequest(c, &response, "both `service_id` and `varname` is required")
			return
		}
		if uriParam.PLStepNum == nil && uriParam.BCStepNum == nil {
			log.CtxLog(c).Errorf("`pl_step_num` or `bc_step_num`, one of them is required")
			um.FailWithBadRequest(c, &response, "`pl_step_num` or `bc_step_num`, one of them is required")
			return
		}

		diDO := &domain_plc.DIDO{}
		cond := domain_plc.ListDICond{
			DeviceId:  deviceId,
			Project:   project,
			ServiceId: uriParam.ServiceId,
			StartTime: uriParam.StartTime,
			EndTime:   uriParam.EndTime,
			PLStepNum: uriParam.PLStepNum,
			BCStepNum: uriParam.BCStepNum,
			VarNames:  uriParam.VarName,
		}
		res, err := diDO.ListDI(c, cond)
		if err != nil {
			log.CtxLog(c).Errorf("failed to list di data, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = res.DIData
		log.CtxLog(c).Infof("succeeded to get di data, uriParam: %v, device_id: %s", uriParam, deviceId)
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (p *plc) GetOperationLog() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			uriParam model.GetOperationLogRequest
			response model.OperationLogsData
		)
		project := c.Param("project")
		deviceId := c.Param("device_id")
		if err := c.BindQuery(&uriParam); err != nil {
			log.CtxLog(c).Errorf("device_id: %s, get op-log uri parameters are incorrect: %v", deviceId, err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		util.SetURIParamDefault(&uriParam.CommonUriParam)

		if err := util.CheckTimeRange(uriParam.StartTime, uriParam.EndTime); err != nil {
			log.CtxLog(c).Errorf("device_id: %s, get op-log err: %v", deviceId, err)
			um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
			return
		}

		dbName := fmt.Sprintf("%s-%s", umw.OperationLog, ucmd.RenameProjectDB(project))
		// 默认查看站本地操作日志
		if uriParam.Type == 0 {
			filter := bson.D{
				util.SelectedTimeDuration("timestamp", uriParam.StartTime, uriParam.EndTime),
				{"type", bson.M{"$exists": false}},
			}
			if uriParam.Operator != nil {
				filter = append(filter, bson.E{Key: "user", Value: *uriParam.Operator})
			}
			if uriParam.OperationDescription != nil {
				filter = append(filter, bson.E{Key: "button", Value: bson.M{"$regex": *uriParam.OperationDescription}})
			}
			if uriParam.OperationInterface != nil {
				filter = append(filter, bson.E{Key: "page", Value: bson.M{"$regex": *uriParam.OperationInterface}})
			}
			byteData, total, err := p.watcher.Mongodb().NewMongoEntry(filter).ListByPagination(
				dbName, deviceId, client.Pagination{
					Limit:  int64(uriParam.Size),
					Offset: int64((uriParam.Page - 1) * uriParam.Size),
				}, client.Ordered{Key: "timestamp", Descending: uriParam.Descending})
			if err != nil {
				log.CtxLog(c).Errorf("device_id: %s, op-log data, get error: %v", deviceId, err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			var records []umw.MongoOperationLog
			if err = json.Unmarshal(byteData, &records); err != nil {
				log.CtxLog(c).Errorf("failed to unmarshal op-log data, device_id: %s, err: %v", deviceId, err)
				um.FailWithBadRequest(c, &response, err.Error())
				return
			}

			log.CtxLog(c).Infof("succeeded to get op-log data, start_time: %d end_time: %d project: %s, col: %s, total: %d",
				uriParam.StartTime, uriParam.EndTime, project, deviceId, total)
			var opLogData []model.MPCOperationLogPS2
			for _, item := range records {
				opLogData = append(opLogData, model.MPCOperationLogPS2{
					Timestamp:      item.Timestamp,
					SendTimestamp:  item.SendTimestamp,
					WriteTimestamp: item.WriteTimestamp,
					Action:         item.Action,
					Page:           item.Page,
					Button:         item.Button,
					UserId:         item.User,
					Args:           item.Args,
				})
			}
			response = model.OperationLogsData{
				Total:         int(total),
				Page:          uriParam.Page,
				Size:          uriParam.Size,
				StartTime:     uriParam.StartTime,
				EndTime:       uriParam.EndTime,
				OperationLogs: opLogData,
			}
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (p *plc) GetPLCExists() gin.HandlerFunc {
	return func(c *gin.Context) {
		var request struct {
			ServiceId string `json:"service_id" form:"service_id"`
			DeviceId  string `json:"device_id" form:"device_id"`
			StartTime int64  `json:"start_time" form:"start_time"`
			EndTime   int64  `json:"end_time" form:"end_time"`
		}
		var response struct {
			um.Base
			HasPlcRecord bool `json:"has_plc_record"`
		}
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("fail to bind query: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		project := c.Param("project")
		filter := bson.D{
			{"metadata.device_id", request.DeviceId},
			{"ts", bson.M{"$gte": time.UnixMilli(request.StartTime).Add(-time.Minute * 5), "$lte": time.UnixMilli(request.EndTime).Add(time.Minute * 5)}},
			{"service_id", request.ServiceId},
		}
		cnt, err := client.GetWatcher().MongodbSupportTimeseries().NewMongoEntry(filter).Count(domain_plc.DBPLC, fmt.Sprintf("%s_%s", domain_plc.CollectionPLC, ucmd.RenameProjectDB(project)))
		if err != nil {
			log.CtxLog(c).Errorf("fail to count plc records, err: %v, service: %s", err, ucmd.ToJsonStrIgnoreErr(request))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if cnt > 0 {
			response.HasPlcRecord = true
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (p *plc) GetPLCArchiveStatus() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.RestorePLCRequest
			response model.Response
		)
		project := c.Param("project")
		deviceId := c.Param("device_id")
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("fail to bind query: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		resData := make([]map[string]interface{}, 0)
		if ucmd.GetEnv() == "prod" && ucmd.GetArea() == um.China {
			plcDO := &domain_plc.PLCDO{}
			cond := domain_plc.ArchiveCond{
				Type:             umw.PLCRecord,
				ServiceStartTime: request.ServiceStartTime,
				DeviceId:         deviceId,
				Project:          project,
				ServiceId:        request.ServiceId,
			}
			status, err := plcDO.GetPLCArchiveStatus(c, cond)
			if err != nil {
				log.CtxLog(c).Errorf("failed to get plc archive status, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			resData = append(resData, map[string]interface{}{
				"type":        umw.PLCRecord,
				"isRestored":  status == domain_plc.StatusRestored,
				"status":      status,
			})
		} else {
			resData = append(resData, map[string]interface{}{
				"type":        umw.PLCRecord,
				"isRestored":  false,
				"status":      domain_plc.StatusUnarchived,
			})
		}

		response.Data = resData
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (p *plc) RestorePLC() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.RestorePLCRequest
			response model.Response
		)
		requestType := c.Param("type")
		project := c.Param("project")
		deviceId := c.Param("device_id")
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("fail to bind json: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		switch requestType {
		case "plc-record":
			plcDO := &domain_plc.PLCDO{}
			cond := domain_plc.ArchiveCond{
				Type:             requestType,
				ServiceStartTime: request.ServiceStartTime,
				DeviceId:         deviceId,
				Project:          project,
				ServiceId:        request.ServiceId,
			}
			cCopy := c.Copy()
			go func() {
				defer func() {
					ucmd.RecoverPanic()
				}()
				err := plcDO.RestorePLC(cCopy, cond)
				if err != nil {
					log.CtxLog(cCopy).Errorf("failed to restore plc, err: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
					return
				}
			}()
		default:
			log.CtxLog(c).Errorf("invalid request type: %s", requestType)
			um.FailWithBadRequest(c, &response, "invalid request type")
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

// 由于原本的plc保存在时序集合，plc的时间作为过期字段，所以不能把恢复的plc的数据直接插入时序集合，否则会直接过期
// 恢复的plc数据保存在新的集合，过期时间为30天
func (p *plc) GetRestoredPLCRecords() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			uriParam model.PLCRecordParam
			response model.PLCRecordsResponse
		)
		project := c.Param("project")
		deviceId := c.Param("device_id")
		if err := c.BindQuery(&uriParam); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		response = model.PLCRecordsResponse{
			StartTime: uriParam.StartTime,
			EndTime:   uriParam.EndTime,
			Project:   project,
			DeviceId:  deviceId,
			ServiceId: uriParam.ServiceId,
		}
		if uriParam.ServiceId == "" {
			log.CtxLog(c).Errorf("`service_id` is required")
			um.FailWithBadRequest(c, &response, "`service_id` is required")
			return
		}

		if uriParam.PLStepNum == "" && uriParam.BCStepNum == "" {
			log.CtxLog(c).Errorf("`pl_step_num` or `bc_step_num`, one of them is required")
			um.FailWithBadRequest(c, &response, "`pl_step_num` or `bc_step_num`, one of them is required")
			return
		}
		if uriParam.Axis == "" {
			log.CtxLog(c).Errorf("`axis` is required")
			um.FailWithBadRequest(c, &response, "`axis` is required")
			return
		}
		if err := util.CheckTimeRange(uriParam.StartTime, uriParam.EndTime); err != nil {
			log.CtxLog(c).Errorf("err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		plcDO := &domain_plc.PLCDO{}
		cond := domain_plc.ListPLCCond{
			DeviceId:  deviceId,
			Project:   project,
			ServiceId: uriParam.ServiceId,
			StartTime: uriParam.StartTime,
			EndTime:   uriParam.EndTime,
			PLStepNum: uriParam.PLStepNum,
			BCStepNum: uriParam.BCStepNum,
		}
		res, err := plcDO.ListArchiveRestoredPLC(c, cond)
		if err != nil {
			log.CtxLog(c).Errorf("failed to list plc records, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = domain_plc.ConvertPLCDO2VO(res, uriParam.Axis)
		log.CtxLog(c).Infof("succeeded to get plc records, cond: %s", ucmd.ToJsonStrIgnoreErr(cond))
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}
