package exec

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"regexp"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang/protobuf/jsonpb"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/rs/xid"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	um "git.nevint.com/golang-libs/common-utils/model"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	uprom "git.nevint.com/golang-libs/common-utils/prometheus"
	uwf "git.nevint.com/golang-libs/common-utils/workflow"

	"git.nevint.com/welkin2/welkin-backend/client"
	domain_plc "git.nevint.com/welkin2/welkin-backend/domain/plc"
	domain_service "git.nevint.com/welkin2/welkin-backend/domain/service"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	"git.nevint.com/welkin2/welkin-backend/pb"
	"git.nevint.com/welkin2/welkin-backend/service"
	"git.nevint.com/welkin2/welkin-backend/util"
)

type Quality interface {
	GetFactoryTestInfo() gin.HandlerFunc
	UploadFactoryData(*uprom.Prometheus) gin.HandlerFunc
	CreateTorqueRecord() gin.HandlerFunc
	FactoryDataList(reportType string) gin.HandlerFunc
	DownloadFactoryTestData() gin.HandlerFunc
	GetPLCForFeatureCalculation() gin.HandlerFunc
	DownloadFactoryTorqueData(area string) gin.HandlerFunc
	DeviceRegisterCallback() gin.HandlerFunc
	UploadCertRecord() gin.HandlerFunc
	GetCertRecord() gin.HandlerFunc
	SyncTorqueData() gin.HandlerFunc
	GenerateTorqueRecord(*uprom.Prometheus) gin.HandlerFunc
	GetServiceForTorque() gin.HandlerFunc
}

type quality struct {
	promCollectors map[string]prometheus.Collector
	watcher        client.Watcher
	logger         *zap.SugaredLogger
	conf           *ucfg.Config
}

func NewQualityHandler(watcher client.Watcher, conf *ucfg.Config) Quality {
	return &quality{
		watcher: watcher,
		logger:  log.Logger.Named(model.QUALITY),
		conf:    conf,
	}
}

func (q *quality) SyncTorqueData() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			res []umw.MongoTestReportData
		)
		sync := c.Query("sync")
		if len(sync) == 0 {
			return
		}
		res, err := q.watcher.Mongodb().TorqueDataFind("factoryData", "test-report", bson.M{})
		if err != nil {
			log.CtxLog(c).Errorf("mongodb find error %s", err.Error())
			return
		}
		for _, r := range res {
			q.SendMEMSTorqueMessage(c, r)
		}
	}
}

func (q *quality) GetFactoryTestInfo() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			req model.GetFactoryTestInfoRequest
			res model.GetFactoryTestInfoResponse
		)
		if err := c.BindQuery(&req); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &res, err.Error())
			return
		}
		mgoRes, err := q.watcher.Mongodb().GetFactoryTestInfo(req)
		if err != nil {
			um.SuccessWithErrCode(c, &res, err.Error(), 3)
			return
		}
		for _, info := range mgoRes {
			prev := model.FactoryTestInfo{
				TestTime:    info.TestTime,
				DeviceID:    info.DeviceId,
				Description: "加解锁枪加锁扭矩检测",
				Info:        "加解锁枪头的加锁扭力符合扭矩值要求(N.m)1～8为：110±10，9～10：35±3",
				Data:        []model.GunValue{},
			}
			var results model.TestData
			err = json.Unmarshal([]byte(info.Json), &results)
			if err != nil {
				um.SuccessWithErrCode(c, &res, err.Error(), 1)
				continue
			}
			for _, result := range results.Result {
				if len(result.ManualDescription.Result) < 30 {
					continue
				}
				if result.NumberString == "4.10.33" {
					var eachGunData model.GunValue
					for i := 1; i <= 10; i++ {
						eachGunData.GunID = i
						if i < 9 {
							eachGunData.TorqueValue = "110±10"
						} else {
							eachGunData.TorqueValue = "35±3"
						}
						eachGunData.FirstValue, _ = strconv.ParseFloat(result.ManualDescription.Result[(i-1)*3], 64)
						eachGunData.SecondValue, _ = strconv.ParseFloat(result.ManualDescription.Result[(i-1)*3+1], 64)
						eachGunData.ThirdValue, _ = strconv.ParseFloat(result.ManualDescription.Result[(i-1)*3+2], 64)
						prev.Data = append(prev.Data, eachGunData)
					}
				}
				prev.TestResult = result.ManualTestResults
			}
			res.TestData = append(res.TestData, prev)
		}
		um.SuccessWithMessageForGin(c, &res, "success", http.StatusOK)
	}
}

func (q *quality) UploadFactoryData(prom *uprom.Prometheus) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			requestData model.FactoryTestRequestData
			response    model.FactoryUploadDataResponse
		)
		q.promCollectors = prom.MetricCollectors
		project := c.Param("project")
		deviceId := c.Param("device_id")
		response.RequestId = c.GetHeader("X-Request-ID")
		if response.RequestId == "" {
			response.RequestId = xid.New().String()
		}

		if err := c.ShouldBind(&requestData); err != nil {
			log.CtxLog(c).Errorf("upload factory test data, err: %s", err.Error())
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if requestData.SuccessRate < 0 || requestData.SuccessRate > 1 {
			log.CtxLog(c).Errorf("upload factory test data, err: success_rate is %f", requestData.SuccessRate)
			um.FailWithBadRequest(c, &response, "success_rate is invalid")
			return
		}
		userId := c.GetHeader("X-User-ID")
		if userId == "" {
			log.CtxLog(c).Errorf("need header: X-User-ID")
			um.FailWithBadRequest(c, &response, "need header: X-User-ID")
			return
		}

		f, fh, err := c.Request.FormFile("file")
		if err != nil {
			log.CtxLog(c).Errorf("upload factory test data, failed to get xml file, err: %s", err.Error())
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		defer f.Close()
		if err = util.CheckFileSHA512(fh, requestData.SHA512); err != nil {
			log.CtxLog(c).Errorf("upload factory test data, err: %s", err.Error())
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		servicesinfo := make([]umw.ReportServiceInfo, 0)
		if err := json.Unmarshal([]byte(requestData.ServiceInfo), &servicesinfo); err != nil {
			log.CtxLog(c).Errorf("failed to unmarshal serviceinfo of the factory test data, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if len(servicesinfo) == 0 {
			log.CtxLog(c).Errorf("project: %s, device_id: %s, filename: %s, file_gen_time: %d, `serviceinfo` is empty",
				project, deviceId, fh.Filename, requestData.ReportGenTime)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		sheet, err := excelize.OpenReader(f)
		if err != nil {
			log.CtxLog(c).Errorf("failed to open file reader, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		defer sheet.Close()

		buffer := bytes.NewBuffer(nil)
		if err = sheet.Write(buffer); err != nil {
			log.CtxLog(c).Errorf("failed to save file to buffer, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		pbReportDetails := &pb.AllMessages{}
		if requestData.ReportDetails != "" {
			if err = jsonpb.UnmarshalString(requestData.ReportDetails, pbReportDetails); err != nil {
				log.CtxLog(c).Errorf("failed to parse report details from pb, err: %v", err)
				um.FailWithBadRequest(c, &response, err.Error())
				return
			}
		}

		log.CtxLog(c).Infof("factory test report details: %s", pbReportDetails.String())

		// Deprecated: compatible with old logic - category is required in future
		category := 1
		if requestData.Category != nil {
			category = *requestData.Category
		}
		testReport := umw.MongoTestReportData{
			RequestId:        response.RequestId,
			Project:          project,
			DeviceId:         deviceId,
			ReportName:       fh.Filename,
			ReportGenTime:    requestData.ReportGenTime,
			ReportUploadTime: time.Now().UnixMilli(),
			Manufacturer:     requestData.Manufacturer,
			Reporter:         requestData.Reporter,
			TestType:         requestData.TestType,
			Category:         category,
			FailCount:        *requestData.FailCount,
			SuccessRate:      requestData.SuccessRate,
			UpdateTime:       time.Now().UnixMilli(),
			Report:           buffer.Bytes(),
		}
		testr := &client.TestReport{Client: q.watcher.Mongodb().Client, Data: &testReport}
		err = testr.InsertOrUpdate(testr.Data.RequestId, []client.IndexOption{
			{Name: "request_id", Fields: bson.D{{"request_id", 1}}, Unique: true},
			{Name: "device_id", Fields: bson.D{{"device_id", 1}}},
			{Name: "report_gen_time", Fields: bson.D{{"report_gen_time", 1}}},
		}...)
		if err != nil {
			log.CtxLog(c).Errorf("failed to insert or update test factory data, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}

		startTime := servicesinfo[0].StartTime
		endTime := servicesinfo[0].EndTime
		for _, si := range servicesinfo[1:] {
			if si.StartTime < startTime {
				startTime = si.StartTime
			}
			if si.EndTime > endTime {
				endTime = si.EndTime
			}
		}

		torquer := &client.TorqueReport{Client: q.watcher.Mongodb().Client, Data: &umw.MongoTorqueReportData{
			RequestId:         response.RequestId,
			Project:           project,
			DeviceId:          deviceId,
			Manufacturer:      requestData.Manufacturer,
			Category:          category,
			TestReportName:    fh.Filename,
			TestReportGenTime: requestData.ReportGenTime,
			StartTime:         startTime,
			EndTime:           endTime,
			ServicesInfo:      servicesinfo,
			InsertTS:          time.Now().UnixMilli(),
		}}

		err = torquer.InsertOrUpdate(torquer.Data.RequestId, []client.IndexOption{
			{Name: "request_id", Fields: bson.D{{"request_id", 1}}, Unique: true},
			{Name: "device_id", Fields: bson.D{{"device_id", 1}}},
			{Name: "insert_ts", Fields: bson.D{{"insert_ts", 1}}},
		}...)
		if err != nil {
			log.CtxLog(c).Errorf("failed to insert or update torque data, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		// 发送 mems 部门接口
		go q.SendMEMSTorqueMessage(c, testReport)

		am := service.Algorithm{
			URL:            fmt.Sprintf("%s/algorithm-platform", q.conf.Welkin.BackendUrl),
			Logger:         q.logger,
			PromCollectors: q.promCollectors,
		}
		go am.SendServiceInfoForTorqueFeatureCalculation(c.Copy(), response.RequestId, deviceId, category, servicesinfo)

		log.CtxLog(c).Infof("succeeded to upload report, device_id: %s, report_name: %s, report_gen_time: %d, request_id: %s",
			deviceId, fh.Filename, requestData.ReportGenTime, response.RequestId)
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (q *quality) CreateTorqueRecord() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.Response
		)
		project := c.Param("project")
		deviceId := c.Param("device_id")
		requestId := xid.New().String()
		requestData := struct {
			ServiceInfo []umw.ReportServiceInfo `json:"service_info"`
		}{}

		if err := c.ShouldBind(&requestData); err != nil {
			log.CtxLog(c).Errorf("create torque record, err: %s", err.Error())
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		startTime := requestData.ServiceInfo[0].StartTime
		endTime := requestData.ServiceInfo[0].EndTime
		for _, si := range requestData.ServiceInfo[1:] {
			if si.StartTime < startTime {
				startTime = si.StartTime
			}
			if si.EndTime > endTime {
				endTime = si.EndTime
			}
		}
		category, err := strconv.Atoi(c.Param("category"))
		if err != nil {
			log.CtxLog(c).Errorf("create torque record, err: %s", err.Error())
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		torquer := &client.TorqueReport{Client: q.watcher.Mongodb().Client, Data: &umw.MongoTorqueReportData{
			RequestId:    requestId,
			Project:      project,
			DeviceId:     deviceId,
			Category:     category,
			StartTime:    startTime,
			EndTime:      endTime,
			ServicesInfo: requestData.ServiceInfo,
			InsertTS:     time.Now().UnixMilli(),
		}}

		err = torquer.InsertOrUpdate(torquer.Data.RequestId, []client.IndexOption{
			{Name: "request_id", Fields: bson.D{{"request_id", 1}}, Unique: true},
			{Name: "device_id", Fields: bson.D{{"device_id", 1}}},
			{Name: "insert_ts", Fields: bson.D{{"insert_ts", 1}}},
		}...)
		if err != nil {
			log.CtxLog(c).Errorf("failed to create torque record, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = struct {
			RequestId string `json:"request_id"`
		}{requestId}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (q *quality) FactoryDataList(reportType string) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			uriParam model.FactoryDataParam
			response model.FactoryDataResponse
		)
		project := c.Param("project")
		category, err := strconv.Atoi(c.Param("category"))
		if err != nil || (category != 1 && category != 2) {
			log.CtxLog(c).Errorf("uri is not found, category: %d, err: %v", category, err)
			um.FailWithNotFound(c, &response, fmt.Sprintf("uri is not found, category: %d, err: %v", category, err))
			return
		}
		if err := c.BindQuery(&uriParam); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		util.SetURIParamDefault(&uriParam.CommonUriParam)
		response = model.FactoryDataResponse{
			Page:      uriParam.Page,
			Size:      uriParam.Size,
			StartTime: uriParam.StartTime,
			EndTime:   uriParam.EndTime,
			Project:   project,
		}
		if uriParam.StartTime == 0 || uriParam.EndTime == 0 {
			log.CtxLog(c).Errorf("err: `start_time` and `end_time` are required!")
			um.FailWithBadRequest(c, &response, "err: `start_time` and `end_time` are required!")
			return
		}
		if err := util.CheckTimeRange(uriParam.StartTime, uriParam.EndTime); err != nil {
			log.CtxLog(c).Errorf("err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		opts := client.FilterOptions{
			Project:   project,
			StartTime: uriParam.StartTime,
			EndTime:   uriParam.EndTime,
			Type:      &category,
		}
		if uriParam.DeviceId != "" {
			opts.DeviceId = uriParam.DeviceId
		}

		records, total, err := q.watcher.Mongodb().FindManyFactoryData(umw.FactoryData, reportType,
			client.Ordered{Key: uriParam.Sort, Descending: uriParam.Descending},
			client.Size{Limit: int64(uriParam.Size), Offset: int64((uriParam.Page - 1) * uriParam.Size)}, opts)
		if err != nil {
			log.CtxLog(c).Errorf("failed to get factory %s data, project: %s, err: %v", reportType, project, err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if reportType == umw.TestReport {
			for _, r := range records.([]umw.MongoTestReportData) {
				detail := model.FactoryTestDataDetails{
					ID:            r.Id.Hex(),
					DeviceId:      r.DeviceId,
					ReportGenTime: r.ReportGenTime,
					Manufacturer:  r.Manufacturer,
					Reporter:      r.Reporter,
					FailCount:     r.FailCount,
					SuccessRate:   r.SuccessRate,
				}
				if len(r.Report) != 0 {
					detail.HasReport = true
				}
				response.Data = append(response.Data, detail)
			}
		} else {
			for _, r := range records.([]umw.MongoTorqueReportData) {
				detail := model.FactoryTorqueDataDetails{
					ID:                 r.Id.Hex(),
					RequestId:          r.RequestId,
					DeviceId:           r.DeviceId,
					ReportGenTime:      r.ReportGenTime,
					ServicesCount:      len(r.ServicesInfo),
					ServicesValidCount: int(r.ServicesValidCount),
					StartTime:          r.StartTime,
					EndTime:            r.EndTime,
				}
				if uriParam.Details {
					detail.ServicesInfo = r.ServicesInfo
				}
				response.Data = append(response.Data, detail)
			}
		}

		response.Total = total
		log.CtxLog(c).Infof("succeeded to get factory %s data, filter: %v", reportType, opts)
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (q *quality) DownloadFactoryTestData() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			record   umw.MongoTestReportData
			response um.Base
		)

		reportId := c.Param("report_id")
		cur, err := q.watcher.Mongodb().FindFactoryDataByID(umw.FactoryData, umw.TestReport, reportId)
		if err != nil {
			log.CtxLog(c).Errorf("failed to get factory test data, report_id: %s, err: %v", reportId, err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if err = cur.Decode(&record); err != nil {
			log.CtxLog(c).Errorf("failed to get factory test data, report_id: %s, err: %v", reportId, err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if record.Report == nil {
			log.CtxLog(c).Errorf("failed to get factory test data, report_id: %s, err: report is null", reportId)
			um.FailWithInternalServerError(c, &response, "err: report is null")
			return
		}

		c.Writer.Header().Set("Content-Type", "application/vnd.ms-excel")
		util.Download(c, record.ReportName, record.Report)

		return
	}
}

func (q *quality) GetPLCForFeatureCalculation() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			uriParam model.PLCRecordParam
			response model.PLCRecordsForFeatureCalculationResponse
		)

		project := c.Param("project")
		deviceId := c.Param("device_id")

		if err := c.BindQuery(&uriParam); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		response = model.PLCRecordsForFeatureCalculationResponse{
			StartTime: uriParam.StartTime,
			EndTime:   uriParam.EndTime,
			Project:   project,
			DeviceId:  deviceId,
			ServiceId: uriParam.ServiceId,
		}
		if uriParam.ServiceId == "" {
			log.CtxLog(c).Errorf("`service_id` is required")
			um.FailWithBadRequest(c, &response, "`service_id` is required")
			return
		}
		if err := util.CheckTimeRange(uriParam.StartTime, uriParam.EndTime); err != nil {
			log.CtxLog(c).Errorf("err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		plcDO := &domain_plc.PLCDO{}
		cond := domain_plc.ListPLCCond{
			DeviceId:  deviceId,
			Project:   project,
			ServiceId: uriParam.ServiceId,
			StartTime: uriParam.StartTime,
			EndTime:   uriParam.EndTime,
			PLStepNum: uriParam.PLStepNum,
			BCStepNum: uriParam.BCStepNum,
		}
		res, err := plcDO.ListPLC(c, cond)
		if err != nil {
			log.CtxLog(c).Errorf("failed to list plc records, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		results := make([]interface{}, 0)
		var total int
		switch project {
		case umw.PUS3:
			total = len(res.PUS3Data)
			for _, record := range res.PUS3Data {
				rowData := make(map[string]interface{})
				plcData := domain_plc.NewPlcMongoData(project, record)
				for axis := 1; axis < 41; axis++ {
					torque, speed, position := plcData.ParsePLCRecordDetails(axis)
					if torque == nil || speed == nil || position == nil {
						continue
					}
					rowData[strconv.Itoa(axis)] = map[string]interface{}{
						"torque":   *torque,
						"speed":    *speed,
						"position": *position,
					}
				}
				rowData["timestamp"] = record.Ts.UnixMilli()
				rowData["pl_step_num"] = record.PlStepNum
				rowData["bc_step_num"] = record.BcStepNum
				results = append(results, rowData)
			}
		case umw.PUS4:
			total = len(res.PUS4Data)
			for _, record := range res.PUS4Data {
				rowData := make(map[string]interface{})
				plcData := domain_plc.NewPlcMongoData(project, record)
				for axis := 1; axis < 37; axis++ {
					torque, speed, position := plcData.ParsePLCRecordDetails(axis)
					if torque == nil || speed == nil || position == nil {
						continue
					}
					rowData[strconv.Itoa(axis)] = map[string]interface{}{
						"torque":   *torque,
						"speed":    *speed,
						"position": *position,
					}
				}
				rowData["timestamp"] = record.Ts.UnixMilli()
				rowData["pl_step_num"] = record.PlStepNum
				rowData["bc_step_num"] = record.BcStepNum
				results = append(results, rowData)
			}
		default:
		}

		response.Data = results
		response.Total = total
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (q *quality) DownloadFactoryTorqueData(area string) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			record   umw.MongoTorqueReportData
			response um.Base
		)

		reportId := c.Param("report_id")
		cur, err := q.watcher.Mongodb().FindFactoryDataByID(umw.FactoryData, umw.TorqueReport, reportId)
		if err != nil {
			log.CtxLog(c).Errorf("failed to get factory torque data, report_id: %s, err: %v", reportId, err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if err = cur.Decode(&record); err != nil {
			log.CtxLog(c).Errorf("failed to get factory torque data, report_id: %s, err: %v", reportId, err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		fileName := record.TestReportName
		if fileName == "" {
			fileName = record.DeviceId + ".xlsx"
		}
		// 若数据库里已经存了生成好的扭矩报告，则直接返回
		fileName = fmt.Sprintf("torque-%s-%s", xid.New().String(), fileName)
		if record.Report != nil && int(record.ServicesValidCount) == len(record.ServicesInfo) {
			c.Writer.Header().Set("Content-Type", "application/vnd.ms-excel")
			util.Download(c, fileName, record.Report)
			return
		}

		if record.Project == umw.PUS3 {
			// 数据库里没有生成过的扭矩报告，生成一个新的
			records, err := q.watcher.Mongodb().FindTorqueFeatureValues(umw.FactoryData, umw.TorqueFeatureCalculation, record.RequestId)
			if err != nil {
				log.CtxLog(c).Errorf("failed to get factory torque data, report_id: %s, err: %v", reportId, err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			if len(records) == 0 {
				log.CtxLog(c).Warnf("torque feature values no found")
				um.SuccessWithErrCode(c, &response, "torque feature values no found", 3)
				return
			}

			var validServicesNum int
			var featureValues umw.FeatureVariables
			valuesSumMap := make(map[string]float64)
			for _, r := range records {
				if r.IncompletePLCRecord || r.NotFoundPLCRecord {
					continue
				}
				validServicesNum++
				for _, result := range r.FeatureResults {
					valuesSumMap[result.Name] += result.Value
				}
			}

			for k, v := range valuesSumMap {
				mean := v / float64(validServicesNum)
				approx, err := strconv.ParseFloat(fmt.Sprintf("%.2f", mean), 64)
				if err != nil {
					log.CtxLog(c).Warnf("failed to get approximation, err: %v", err)
					approx = mean
				}
				valuesSumMap[k] = approx
			}
			resByte, err := json.Marshal(valuesSumMap)
			if err != nil {
				log.CtxLog(c).Errorf("failed to marshal feature values, err: %v", err)
				um.FailWithBadRequest(c, &response, err.Error())
				return
			}
			if err = json.Unmarshal(resByte, &featureValues); err != nil {
				log.CtxLog(c).Errorf("failed to unmarshal feature values, err: %v", err)
				um.FailWithBadRequest(c, &response, err.Error())
				return
			}

			torqueData := Torque{
				DeviceId:      record.DeviceId,
				Manufacturer:  record.Manufacturer,
				TotalServices: len(record.ServicesInfo),
				ValidServices: validServicesNum,
				StartDate:     util.DecodeTime(time.UnixMilli(record.StartTime)),
				EndDate:       util.DecodeTime(time.UnixMilli(record.EndTime)),
				FeatureValues: featureValues,
			}
			buffer, err := torqueData.GenerateNewTorqueReport(area, record.Project)
			if err != nil {
				log.CtxLog(c).Errorf("failed to generate torque report, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}

			torquer := &client.TorqueReport{Client: q.watcher.Mongodb().Client, Data: &umw.MongoTorqueReportData{
				RequestId:          record.RequestId,
				Report:             buffer.Bytes(),
				ServicesValidCount: int32(torqueData.ValidServices),
				ReportGenTime:      time.Now().UnixMilli(),
			}}

			go torquer.InsertOrUpdate(torquer.Data.RequestId)

			c.Writer.Header().Set("Content-Type", "application/vnd.ms-excel")
			c.Writer.Header().Set("Content-Transfer-Encoding", "binary")
			util.Download(c, fileName, buffer.Bytes())

			return
		} else if record.Project == umw.PUS4 {
			// 将换电服务映射到不同车辆品牌(NIO+ONVO)
			serviceBrandMap := make(map[string]string)
			brandServicesNum := make(map[string]int)
			for _, serviceInfo := range record.ServicesInfo {
				// 兼容手动上传（无车辆品牌）
				if serviceInfo.EvBrand == "" {
					serviceInfo.EvBrand = model.EvBrandNIO
				}
				serviceBrandMap[serviceInfo.ServiceId] = serviceInfo.EvBrand
				brandServicesNum[serviceInfo.EvBrand] += 1
			}
			// 数据库里没有生成过的扭矩报告，生成一个新的
			records, err := q.watcher.Mongodb().FindTorqueFeatureValues(umw.FactoryData, umw.TorqueFeatureCalculation, record.RequestId)
			if err != nil {
				log.CtxLog(c).Errorf("failed to get factory torque data, report_id: %s, err: %v", reportId, err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			if len(records) == 0 {
				log.CtxLog(c).Warnf("torque feature values no found")
				um.SuccessWithErrCode(c, &response, "torque feature values no found", 3)
				return
			}

			var servicesValidCount int32
			brandValidServicesNum := make(map[string]int)            // 区分品牌
			brandValuesSumMap := make(map[string]map[string]float64) // 区分品牌
			for _, r := range records {
				if r.IncompletePLCRecord || r.NotFoundPLCRecord {
					continue
				}
				brand := serviceBrandMap[r.ServiceId]
				brandValidServicesNum[brand]++
				servicesValidCount++
				if brandValuesSumMap[brand] == nil {
					brandValuesSumMap[brand] = make(map[string]float64)
				}
				for _, result := range r.FeatureResults {
					brandValuesSumMap[brand][result.Name] += result.Value
				}
			}

			brandFeatureValues := make(map[string]umw.FeatureVariables)
			for brand, valuesSumMap := range brandValuesSumMap {
				for k, v := range valuesSumMap {
					mean := v / float64(brandValidServicesNum[brand])
					approx, err := strconv.ParseFloat(fmt.Sprintf("%.2f", mean), 64)
					if err != nil {
						log.CtxLog(c).Warnf("failed to get approximation, err: %v", err)
						approx = mean
					}
					valuesSumMap[k] = approx
				}
				resByte, err := json.Marshal(valuesSumMap)
				if err != nil {
					log.CtxLog(c).Errorf("failed to marshal feature values, err: %v", err)
					um.FailWithBadRequest(c, &response, err.Error())
					return
				}
				var featureValues umw.FeatureVariables
				if err = json.Unmarshal(resByte, &featureValues); err != nil {
					log.CtxLog(c).Errorf("failed to unmarshal feature values, err: %v", err)
					um.FailWithBadRequest(c, &response, err.Error())
					return
				}
				brandFeatureValues[brand] = featureValues
			}

			torqueData := TorqueV2{
				DeviceId:           record.DeviceId,
				Manufacturer:       record.Manufacturer,
				BrandTotalServices: brandServicesNum,
				BrandValidServices: brandValidServicesNum,
				StartDate:          util.DecodeTime(time.UnixMilli(record.StartTime)),
				EndDate:            util.DecodeTime(time.UnixMilli(record.EndTime)),
				BrandFeatureValues: brandFeatureValues,
			}
			buffer, err := torqueData.GenerateNewTorqueReport(area, record.Project)
			if err != nil {
				log.CtxLog(c).Errorf("failed to generate torque report, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}

			torquer := &client.TorqueReport{Client: q.watcher.Mongodb().Client, Data: &umw.MongoTorqueReportData{
				RequestId:          record.RequestId,
				Report:             buffer.Bytes(),
				ServicesValidCount: servicesValidCount,
				ReportGenTime:      time.Now().UnixMilli(),
			}}

			go torquer.InsertOrUpdate(torquer.Data.RequestId)

			c.Writer.Header().Set("Content-Type", "application/vnd.ms-excel")
			c.Writer.Header().Set("Content-Transfer-Encoding", "binary")
			util.Download(c, fileName, buffer.Bytes())

			return
		} else {
			log.CtxLog(c).Errorf("DownloadFactoryTorqueData, invalid project: %s, report_id: %s", record.Project, reportId)
			um.FailWithBadRequest(c, &response, "invalid project")
			return
		}
	}
}

func (q *quality) GenerateTorqueRecord(prom *uprom.Prometheus) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  domain_service.ServiceForTorqueRequest
			response um.Base
		)
		q.promCollectors = prom.MetricCollectors
		project := c.Param("project")
		category, _ := strconv.Atoi(c.Param("category"))
		env := "stg"
		// category目前只支持 1出厂报告
		if category != 1 {
			log.CtxLog(c).Errorf("generate torque record, invalid category: %s", category)
			um.FailWithBadRequest(c, &response, "invalid category")
			return
		}
		requestId := xid.New().String()
		if err := c.ShouldBind(&request); err != nil {
			log.CtxLog(c).Errorf("generate torque record, err: %s", err.Error())
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		srv := domain_service.Service{
			ServiceStartTime: request.StartTime,
			ServiceEndTime:   request.EndTime,
			Project:          project,
			DeviceId:         request.DeviceId,
		}
		request.Page = 1
		request.Size = 200
		res, total, err := srv.GetServicesForTorque(c, c.Param("category"), request.Page, request.Size, false)
		if err != nil {
			log.CtxLog(c).Errorf("fail to get services for torque, err: %v, request: %s", err, ucmd.ToJsonStrIgnoreErr(request))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		// 限制一次计算200个订单的扭矩特征值
		if total > request.Size {
			err = fmt.Errorf("too many services, limit %d, get %d", request.Size, total)
			log.CtxLog(c).Error(err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		var servicesInfo []umw.ReportServiceInfo
		for _, item := range res {
			servicesInfo = append(servicesInfo, umw.ReportServiceInfo{
				ServiceId: item.ServiceId,
				Env:       env,
				StartTime: item.StartTime,
				EndTime:   item.EndTime,
				Success:   item.Success,
			})
		}

		torquer := &client.TorqueReport{Client: q.watcher.Mongodb().Client, Data: &umw.MongoTorqueReportData{
			RequestId:    requestId,
			Project:      project,
			DeviceId:     request.DeviceId,
			Category:     category,
			StartTime:    request.StartTime,
			EndTime:      request.EndTime,
			ServicesInfo: servicesInfo,
			InsertTS:     time.Now().UnixMilli(),
		}}
		err = torquer.InsertOrUpdate(torquer.Data.RequestId, []client.IndexOption{
			{Name: "request_id", Fields: bson.D{{"request_id", 1}}, Unique: true},
			{Name: "device_id", Fields: bson.D{{"device_id", 1}}},
			{Name: "insert_ts", Fields: bson.D{{"insert_ts", 1}}},
		}...)
		if err != nil {
			log.CtxLog(c).Errorf("failed to create torque record, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}

		am := service.Algorithm{
			URL:            fmt.Sprintf("%s/algorithm-platform", q.conf.Welkin.BackendUrl),
			Logger:         q.logger,
			PromCollectors: q.promCollectors,
		}
		go am.SendServiceInfoForTorqueFeatureCalculation(c.Copy(), requestId, request.DeviceId, category, servicesInfo)
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (q *quality) GetServiceForTorque() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  domain_service.ServiceForTorqueRequest
			response domain_service.ServiceForTorqueResponse
		)
		project := c.Param("project")
		if project != umw.PUS4 {
			log.CtxLog(c).Errorf("invalid project: %s", project)
			um.FailWithBadRequest(c, &response, "invalid project")
			return
		}
		category := c.Param("category")
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if err := util.CheckTimeRange(request.StartTime, request.EndTime); err != nil {
			log.CtxLog(c).Errorf("the time range is incorrect, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		util.SetURIParamDefault(&request.CommonUriParam)

		srv := domain_service.Service{
			ServiceStartTime: request.StartTime,
			ServiceEndTime:   request.EndTime,
			Project:          project,
			DeviceId:         request.DeviceId,
		}
		res, total, err := srv.GetServicesForTorque(c, category, request.Page, request.Size, true)
		if err != nil {
			log.CtxLog(c).Errorf("fail to get services for torque, err: %v, request: %s", err, ucmd.ToJsonStrIgnoreErr(request))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = res
		response.Total = total
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (q *quality) DeviceRegisterCallback() gin.HandlerFunc {
	return func(c *gin.Context) {
		var request uwf.ApprovalCallback
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{"result_code": "fail", "message": err.Error()})
			return
		}
		_, err := q.watcher.Mongodb().NewMongoEntry(
			bson.D{bson.E{Key: "flow_instance_id", Value: request.FlowInstanceId}}).UpdateAndGetOne(
			umw.FactoryData, umw.DeviceAuthApproval, bson.D{
				{"$set", bson.D{
					{"status", request.Status},
					{"current_node_name", request.CurrentNodeName},
					{"previous_node_name", request.PreviousNodeName},
					{"update_ts", time.Now().UnixMilli()},
				}},
			})
		if err != nil {
			log.CtxLog(c).Errorf("update device register approval history err: %s", err.Error())
		}
		if request.Status == "success" {
			now := time.Now()
			deviceInfo := umw.MongoBrownDragonDeviceInfo{
				DeviceIdentifier: request.Context["device_identifier"],
				CreatedTime:      now.UnixMilli(),
				Date:             now,
			}
			if err = q.watcher.Mongodb().InsertBrowndragonDeviceInfo(umw.FactoryData, umw.BrowndragonDeviceInfo, deviceInfo); err != nil {
				log.CtxLog(c).Errorf("insert browndragon device info err: %s", err.Error())
				c.JSON(http.StatusInternalServerError, gin.H{"result_code": "fail", "message": err.Error()})
				return
			}
		}

		log.CtxLog(c).Infof("succeed to handle device register approval callback")
		c.JSON(http.StatusOK, gin.H{"result_code": "success", "message": "ok"})
	}
}

func (q *quality) UploadCertRecord() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.BrownDragonCertRecordData
			response um.Base
		)
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if request.Step == 3 {
			// 删除临时烧写用户权限
			err := q.watcher.Mongodb().NewMongoEntry(bson.D{
				{"user_id", request.UserId},
				{"role", bson.A{6}},
			}).DeleteOne(umw.OAuthDB, umw.UserBaseInfo)
			if err != nil {
				log.CtxLog(c).Errorf("fail to delete user info, data: %v, err: %v", request, err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
		}
		err := q.watcher.Mongodb().NewMongoEntry(bson.D{
			{"timestamp", request.Timestamp},
			{"device_identifier", request.DeviceIdentifier},
		}).ReplaceOne(umw.FactoryData, umw.BrowndragonCertRecord, request, true, client.IndexOption{
			Name:   "ts_device_unique_combine",
			Fields: bson.D{{"timestamp", -1}, {"device_identifier", -1}},
			Unique: true,
		})
		if err != nil {
			log.CtxLog(c).Errorf("fail to insert or update browndragon cert record, data: %v, err: %v", request, err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}

		log.CtxLog(c).Infof("succeeded to upload browndragon cert record, data: %v", request)
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (q *quality) GetCertRecord() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request model.GetCertRecordRequest
		)
		response := struct {
			um.Base
			InfoList []umw.MongoBrownDragonCertRecord `json:"info_list"`
		}{InfoList: make([]umw.MongoBrownDragonCertRecord, 0)}
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("get browndragon cert record, uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		base64QrCode, err := url.QueryUnescape(request.QrCode)
		if err != nil {
			log.CtxLog(c).Errorf("get browndragon cert record, failed to unescape qr_code, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		qrCodeByte, err := base64.StdEncoding.DecodeString(base64QrCode)
		if err != nil {
			log.CtxLog(c).Errorf("get browndragon cert record, failed to decode qr_code, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		qrCode := string(qrCodeByte)
		// 校验设备是否匹配
		byteData, err := q.watcher.Mongodb().NewMongoEntry(bson.D{
			{"device_type", request.DeviceType},
		}).ListAll(umw.FactoryData, umw.BrowndragonMatchRules, client.Ordered{})
		if err != nil {
			log.CtxLog(c).Errorf("get browndragon cert record, fail to get browndragon match rules, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var rules []umw.MongoBrownDragonMatchRules
		if err = json.Unmarshal(byteData, &rules); err != nil {
			log.CtxLog(c).Errorf("get browndragon cert record, fail to unmarshal browndragon match rules, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		match := false
		for _, rule := range rules {
			qrMatch, err := regexp.MatchString(rule.QrCode, qrCode)
			if err != nil {
				log.CtxLog(c).Errorf("get browndragon cert record, fail to match qr pattern, err: %v", err)
				continue
			}
			snMatch, err := regexp.MatchString(rule.SerialNumber, request.SerialNumber)
			if err != nil {
				log.CtxLog(c).Errorf("get browndragon cert record, fail to match sn pattern, err: %v", err)
				continue
			}
			if qrMatch && snMatch {
				match = true
				break
			}
		}
		if !match {
			log.CtxLog(c).Infof("get browndragon cert record, no rule matches with the request: %s", ucmd.ToJsonStrIgnoreErr(request))
			um.SuccessWithErrCode(c, &response, fmt.Sprintf("no rule matches with the request: %s", ucmd.ToJsonStrIgnoreErr(request)), 1)
			return
		}

		// 烧写记录查询
		byteData, err = q.watcher.Mongodb().NewMongoEntry(bson.D{
			{"device_type", request.DeviceType},
			{"$or", bson.A{bson.M{"serial_number": request.SerialNumber}, bson.M{"qr_code": qrCode}}},
		}).ListAll(umw.FactoryData, umw.BrowndragonCertRecord, client.Ordered{})
		if err != nil {
			log.CtxLog(c).Errorf("get browndragon cert record, fail to get browndragon cert record, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if err = json.Unmarshal(byteData, &response.InfoList); err != nil {
			log.CtxLog(c).Errorf("get browndragon cert record, fail to unmarshal browndragon cert record, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		log.CtxLog(c).Infof("succeed to get browndragon cert record, request: %s", ucmd.ToJsonStrIgnoreErr(request))
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (q *quality) SendMEMSTorqueMessage(c *gin.Context, req umw.MongoTestReportData) {
	if os.Getenv("K8S_ENV") != "prod" {
		return
	}
	var (
		m        int
		data     []byte
		response model.MEMSMeasurementResponse
	)
	res := model.MEMSTorqueMessageRequest{
		LoadFrom:                 "天宫",
		EqpName:                  "换电站",
		ModelName:                "3.0代",
		ApiType:                  "CREATE",
		UniqueId:                 req.RequestId,
		SerialNumber:             req.DeviceId,
		CalibrationDate:          time.UnixMilli(req.ReportGenTime).Format("2006-01-02"),
		CalibrationSpecification: "V 3.0换电站特殊特性清单",
		CalibrationOrg:           "斯沃普换电站工厂",
		Calibrator: []model.CalibratorInfo{
			{
				CalibratorcNo: "1",
			},
			{
				CalibratorcNo: "2",
			},
			{
				CalibratorcNo: "3",
			},
			{
				CalibratorcNo: "4",
			},
			{
				CalibratorcNo: "5",
			},
			{
				CalibratorcNo: "6",
			},
			{
				CalibratorcNo: "7",
			},
			{
				CalibratorcNo: "8",
			},
			{
				CalibratorcNo: "9",
			},
			{
				CalibratorcNo: "10",
			},
			{
				CalibratorcNo: "11",
			},
			{
				CalibratorcNo: "12",
			},
		},
	}
	m = 1000
	f, err := excelize.OpenReader(bytes.NewReader(req.Report))
	if err != nil {
		log.CtxLog(c).Errorf("xlsx open error: %s", err.Error())
		return
	}
	rows, _ := f.GetRows("Check List")
	for i, row := range rows {
		switch {
		case i >= m+2 && i <= m+13:
			var curr model.MeasurementInfo
			v1, _ := strconv.ParseFloat(row[8], 10)
			v2, _ := strconv.ParseFloat(row[9], 10)
			v3, _ := strconv.ParseFloat(row[10], 10)
			curr.MeasureUnit = "Nm"
			curr.MeasureId = strconv.Itoa(i - m - 1)
			curr.MeasureFeature = curr.MeasureId + "号枪扭矩"
			curr.Average, _ = strconv.ParseFloat(fmt.Sprintf("%.3f", (v1+v2+v3)/3), 64)
			if curr.MeasureId == "9" || curr.MeasureId == "10" {
				curr.TargetValue = 35
			} else {
				curr.TargetValue = 110
			}
			res.MeasurementInformationList = append(res.MeasurementInformationList, curr)
		default:
			if len(row) == 0 {
				continue
			}
			if row[0] == "4.13.23" {
				m = i
			}
		}
	}
	ts := time.Now().Unix()
	path := "/dapi/ttMeasurementRecordApi/saveInfo"
	sign := ucmd.Sign{
		Timestamp:   ts,
		AppId:       q.conf.Sentry.AppId,
		AppSecret:   q.conf.Sentry.AppSecret,
		Method:      "POST",
		Path:        path,
		ContentType: "application/json",
		URLParams:   map[string]interface{}{},
		BodyParams: map[string]interface{}{
			"measurementRecordList": []model.MEMSTorqueMessageRequest{
				res,
			},
		},
	}
	sn := sign.Generate()
	if sn == "" {
		log.CtxLog(c).Errorf("get sign, err: `sign` is empty")
		return
	}
	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL: fmt.Sprintf("%s%s?app_id=%s&sign=%s&timestamp=%d",
			q.conf.MEMS.URL, path, sign.AppId, sn, ts),
		Method: "POST",
		Header: map[string]string{
			"Content-Type": "application/json",
		},
		RequestBody: map[string]interface{}{
			"measurementRecordList": []model.MEMSTorqueMessageRequest{
				res,
			},
		},
	})
	body, _, err := ct.Do()
	if err != nil {
		log.CtxLog(c).Errorf("http do err: %s", err.Error())
		return
	}
	defer body.Close()
	if data, err = io.ReadAll(body); err != nil {
		log.CtxLog(c).Errorf("post request err: %s", err.Error())
		return
	}
	if err = json.Unmarshal(data, &response); err != nil {
		log.CtxLog(c).Errorf("json unmarshal err: %s", err.Error())
		return
	}
	if response.Code != 200 {
		log.CtxLog(c).Errorf("response err: response code not 200")
		return
	}
	log.CtxLog(c).Infof("successly post mems http request")
}
