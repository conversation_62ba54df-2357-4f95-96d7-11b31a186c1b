package exec

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	domain_service "git.nevint.com/welkin2/welkin-backend/domain/service"
	"git.nevint.com/welkin2/welkin-backend/model"
	mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"
)

type ServiceStatisticsHandler struct {
	Watcher   client.Watcher
	Projects  []string
	StartTime time.Time
	EndTime   time.Time

	Logger *zap.SugaredLogger
}

func (s *ServiceStatisticsHandler) ProcessHourly(ctx context.Context) error {
	hour := s.StartTime.Hour()
	today := time.Date(s.StartTime.Year(), s.StartTime.Month(), s.StartTime.Day(), 0, 0, 0, 0, time.Local)

	makeHourlyOrderInfo := func(ctx context.Context, project string) error {
		services, err := s.Watcher.Mongodb().GetDeviceHourlyService(ctx, project, s.StartTime, s.EndTime)
		if err != nil {
			s.Logger.Errorf("fail to get %s services: %v", project, err)
			return err
		}
		var records []interface{}
		count := 0
		for _, service := range services {
			deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(service.DeviceId)
			if deviceInfo.ResourceId == "" {
				continue
			}
			count = count + service.ServiceCount70 + service.ServiceCount100
			description := ""
			if found {
				description = deviceInfo.Description
			}
			records = append(records, model.HourlyOrderInfo{
				SwapStationId:   deviceInfo.ResourceId,
				SwapStationName: description,
				DeviceModel:     project,
				Day:             today.Format("2006-01-02"),
				Hour:            hour,
				BatteryType:     "70kWh",
				ServiceCount:    service.ServiceCount70,
				DayTs:           today.UnixMilli(),
				Date:            today,
			}, model.HourlyOrderInfo{
				SwapStationId:   deviceInfo.ResourceId,
				SwapStationName: description,
				DeviceModel:     project,
				Day:             today.Format("2006-01-02"),
				Hour:            hour,
				BatteryType:     "100kWh",
				ServiceCount:    service.ServiceCount100,
				DayTs:           today.UnixMilli(),
				Date:            today,
			})
		}
		if len(records) == 0 {
			return nil
		}
		if err = s.Watcher.PLCMongodb().NewMongoEntry().InsertMany(umw.Algorithm, "hourly_order_info", records, []client.IndexOption{
			{
				Name: "day_hour_deviceId_battery_unique",
				Fields: bson.D{
					{"day_ts", -1},
					{"hour", 1},
					{"swap_station_id", 1},
					{"battery_type", 1},
				},
			},
			{
				Name:        "expire_date",
				Fields:      bson.D{{"date", -1}},
				ExpiredTime: 180 * 24 * 3600,
			},
		}...); err != nil {
			s.Logger.Errorf("fail to insert hourly order info, err: %v", err)
			return err
		}
		return nil
	}

	g := ucmd.NewErrGroup(ctx)
	for i := range s.Projects {
		project := s.Projects[i]
		g.GoRecover(func() error {
			return makeHourlyOrderInfo(ctx, project)
		})
	}
	if err := g.Wait(); err != nil {
		s.Logger.Errorf("calculate service statistics, %v", err)
		return err
	}

	return nil
}

func (s *ServiceStatisticsHandler) ProcessDaily(ctx context.Context) error {
	for _, project := range s.Projects {
		for day := s.StartTime; day.Before(s.EndTime); day = day.AddDate(0, 0, 1) {
			serviceDO := domain_service.ServiceDO{}
			cond := domain_service.CountServiceCond{
				Project:          project,
				ServiceStartTime: day.UnixMilli(),
				ServiceEndTime:   day.AddDate(0, 0, 1).UnixMilli(),
			}
			total, err := serviceDO.CountService(ctx, cond)
			if err != nil {
				s.Logger.Errorf("ProcessDaily, fail to count %s service: %v, cond: %s", project, err, ucmd.ToJsonStrIgnoreErr(cond))
				return err
			}
			record := mmgo.ServiceDailyCount{
				Project: project,
				Count:   total,
				Day:     day.UnixMilli(),
				DayStr:  day.Format("2006-01-02"),
				Date:    day,
			}
			err = s.Watcher.Mongodb().NewMongoEntry(bson.D{{"project", project}, {"day", record.Day}}).UpdateOne(umw.ServiceInfo, domain_service.CollectionServiceDailyCount, bson.D{{"$set", record}}, true, []client.IndexOption{
				{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 545 * 24 * 3600},
				{Name: "project_day_unique", Fields: bson.D{{"project", 1}, {"day", -1}}, Unique: true},
			}...)
			if err != nil {
				s.Logger.Errorf("ProcessDaily, fail to update service daily count, err: %v, record: %s", err, ucmd.ToJsonStrIgnoreErr(record))
				return err
			}
		}
	}
	s.Logger.Infof("ProcessDaily, success to process service daily count, start: %s, end: %s", s.StartTime.Format("2006-01-02"), s.EndTime.Format("2006-01-02"))
	return nil
}
