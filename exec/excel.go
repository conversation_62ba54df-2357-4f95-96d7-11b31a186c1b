package exec

import (
	"bytes"
	"fmt"
	"git.nevint.com/welkin2/welkin-backend/model"
	"os"

	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	um "git.nevint.com/golang-libs/common-utils/model"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
)

type Torque struct {
	DeviceId      string
	Manufacturer  string
	TotalServices int
	ValidServices int
	StartDate     string
	EndDate       string
	FeatureValues umw.FeatureVariables
	Logger        *zap.SugaredLogger
}

func (t *Torque) GenerateNewTorqueReport(area, project string) (*bytes.Buffer, error) {
	path, _ := os.Getwd()
	lang, target, qualified, unqualified, validCountName := "zh", "扭矩报告", "合格", "不合格", "有效换电次数"
	if area == um.Europe {
		lang, target, qualified, unqualified, validCountName = "en", "Torque Report", "Qualified", "Unqualified", "valid count"
	}
	f, err := excelize.OpenFile(fmt.Sprintf("%s/torque-report-template-%s-%s.xlsx", path, lang, ucmd.RenameProjectDB(project)))
	if err != nil {
		t.Logger.Errorf("failed to open torque report template, err: %v", err)
		return nil, err
	}
	defer func() {
		if err = f.Close(); err != nil {
			t.Logger.Errorf("failded to close file, err: %v", err)
		}
	}()

	if err = f.SetSheetName("Sheet1", target); err != nil {
		return nil, err
	}

	warnStyle, _ := f.NewStyle(&excelize.Style{
		Border: []excelize.Border{
			{
				Type:  "right",
				Color: "#000000",
				Style: 1,
			},
			{
				Type:  "left",
				Color: "#000000",
				Style: 1,
			},
			{
				Type:  "top",
				Color: "#000000",
				Style: 1,
			},
			{
				Type:  "bottom",
				Color: "#000000",
				Style: 1,
			},
		},
		Font: &excelize.Font{
			Size:   11,
			Family: "文泉驿点阵正黑",
			Color:  "#FFFFFF",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#EF0706"},
			Shading: 1,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
	})

	successStyle, _ := f.NewStyle(&excelize.Style{
		Border: []excelize.Border{
			{
				Type:  "right",
				Color: "#000000",
				Style: 1,
			},
			{
				Type:  "left",
				Color: "#000000",
				Style: 1,
			},
			{
				Type:  "top",
				Color: "#000000",
				Style: 1,
			},
			{
				Type:  "bottom",
				Color: "#000000",
				Style: 1,
			},
		},
		Font: &excelize.Font{
			Size:   11,
			Family: "文泉驿点阵正黑",
			Color:  "#FFFFFF",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#06B050"},
			Shading: 1,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
	})

	f.SetCellValue(target, "B3", t.DeviceId)
	f.SetCellValue(target, "E3", t.Manufacturer)
	f.SetCellValue(target, "B4", fmt.Sprintf("%s - %s", t.StartDate, t.EndDate))
	f.SetCellValue(target, "E4", fmt.Sprintf("%d（%s：%d）", t.TotalServices, validCountName, t.ValidServices))

	f.SetCellValue(target, "E7", t.FeatureValues.LeftFrontPushRodMean)
	if t.FeatureValues.LeftFrontPushRodMean > 20 {
		f.SetCellValue(target, "F7", unqualified)
		f.SetCellStyle(target, "F7", "F7", warnStyle)
	} else {
		f.SetCellValue(target, "F7", qualified)
		f.SetCellStyle(target, "F7", "F7", successStyle)
	}
	f.SetCellValue(target, "E8", t.FeatureValues.RightFrontPushRodMean)
	if t.FeatureValues.RightFrontPushRodMean > 20 {
		f.SetCellValue(target, "F8", unqualified)
		f.SetCellStyle(target, "F8", "F8", warnStyle)
	} else {
		f.SetCellValue(target, "F8", qualified)
		f.SetCellStyle(target, "F8", "F8", successStyle)
	}
	f.SetCellValue(target, "E9", t.FeatureValues.LeftBackPushRodMean)
	if t.FeatureValues.LeftBackPushRodMean > 20 {
		f.SetCellValue(target, "F9", unqualified)
		f.SetCellStyle(target, "F9", "F9", warnStyle)
	} else {
		f.SetCellValue(target, "F9", qualified)
		f.SetCellStyle(target, "F9", "F9", successStyle)
	}
	f.SetCellValue(target, "E10", t.FeatureValues.RightBackPushRodMean)
	if t.FeatureValues.RightBackPushRodMean > 20 {
		f.SetCellValue(target, "F10", unqualified)
		f.SetCellStyle(target, "F10", "F10", warnStyle)
	} else {
		f.SetCellValue(target, "F10", qualified)
		f.SetCellStyle(target, "F10", "F10", successStyle)
	}
	f.SetCellValue(target, "E11", t.FeatureValues.LeftFrontPushRodExtremum)
	if t.FeatureValues.LeftFrontPushRodExtremum > 30 {
		f.SetCellValue(target, "F11", unqualified)
		f.SetCellStyle(target, "F11", "F11", warnStyle)
	} else {
		f.SetCellValue(target, "F11", qualified)
		f.SetCellStyle(target, "F11", "F11", successStyle)
	}
	f.SetCellValue(target, "E12", t.FeatureValues.RightFrontPushRodExtremum)
	if t.FeatureValues.RightFrontPushRodExtremum > 30 {
		f.SetCellValue(target, "F12", unqualified)
		f.SetCellStyle(target, "F12", "F12", warnStyle)
	} else {
		f.SetCellValue(target, "F12", qualified)
		f.SetCellStyle(target, "F12", "F12", successStyle)
	}
	f.SetCellValue(target, "E13", t.FeatureValues.LeftBackPushRodExtremum)
	if t.FeatureValues.LeftBackPushRodExtremum > 30 {
		f.SetCellValue(target, "F13", unqualified)
		f.SetCellStyle(target, "F13", "F13", warnStyle)
	} else {
		f.SetCellValue(target, "F13", qualified)
		f.SetCellStyle(target, "F13", "F13", successStyle)
	}
	f.SetCellValue(target, "E14", t.FeatureValues.RightBackPushRodExtremum)
	if t.FeatureValues.RightBackPushRodExtremum > 30 {
		f.SetCellValue(target, "F14", unqualified)
		f.SetCellStyle(target, "F14", "F14", warnStyle)
	} else {
		f.SetCellValue(target, "F14", qualified)
		f.SetCellStyle(target, "F14", "F14", successStyle)
	}

	f.SetCellValue(target, "E15", t.FeatureValues.LeftHatchDoorMean)
	if t.FeatureValues.LeftHatchDoorMean > 38 {
		f.SetCellValue(target, "F15", unqualified)
		f.SetCellStyle(target, "F15", "F15", warnStyle)
	} else {
		f.SetCellValue(target, "F15", qualified)
		f.SetCellStyle(target, "F15", "F15", successStyle)
	}
	f.SetCellValue(target, "E16", t.FeatureValues.RightHatchDoorMean)
	if t.FeatureValues.RightHatchDoorMean > 38 {
		f.SetCellValue(target, "F16", unqualified)
		f.SetCellStyle(target, "F16", "F16", warnStyle)
	} else {
		f.SetCellValue(target, "F16", qualified)
		f.SetCellStyle(target, "F16", "F16", successStyle)
	}
	f.SetCellValue(target, "E17", t.FeatureValues.LeftHatchDoorExtremum)
	if t.FeatureValues.LeftHatchDoorExtremum > 80 {
		f.SetCellValue(target, "F17", unqualified)
		f.SetCellStyle(target, "F17", "F17", warnStyle)
	} else {
		f.SetCellValue(target, "F17", qualified)
		f.SetCellStyle(target, "F17", "F17", successStyle)
	}
	f.SetCellValue(target, "E18", t.FeatureValues.RightHatchDoorExtremum)
	if t.FeatureValues.RightHatchDoorExtremum > 80 {
		f.SetCellValue(target, "F18", unqualified)
		f.SetCellStyle(target, "F18", "F18", warnStyle)
	} else {
		f.SetCellValue(target, "F18", qualified)
		f.SetCellStyle(target, "F18", "F18", successStyle)
	}

	f.SetCellValue(target, "E19", t.FeatureValues.RgvMoveMean)
	if t.FeatureValues.RgvMoveMean > 25 {
		f.SetCellValue(target, "F19", unqualified)
		f.SetCellStyle(target, "F19", "F19", warnStyle)
	} else {
		f.SetCellValue(target, "F19", qualified)
		f.SetCellStyle(target, "F19", "F19", successStyle)
	}

	f.SetCellValue(target, "E20", t.FeatureValues.RgvLiftNoloadMean)
	if t.FeatureValues.RgvLiftNoloadMean > 25 {
		f.SetCellValue(target, "F20", unqualified)
		f.SetCellStyle(target, "F20", "F20", warnStyle)
	} else {
		f.SetCellValue(target, "F20", qualified)
		f.SetCellStyle(target, "F20", "F20", successStyle)
	}

	f.SetCellValue(target, "E21", t.FeatureValues.RgvLiftMean)
	if t.FeatureValues.RgvLiftMean > 40 {
		f.SetCellValue(target, "F21", unqualified)
		f.SetCellStyle(target, "F21", "F21", warnStyle)
	} else {
		f.SetCellValue(target, "F21", qualified)
		f.SetCellStyle(target, "F21", "F21", successStyle)
	}

	f.SetCellValue(target, "E22", t.FeatureValues.LeftFrontLocatePinMean)
	if t.FeatureValues.LeftFrontLocatePinMean > 22 {
		f.SetCellValue(target, "F22", unqualified)
		f.SetCellStyle(target, "F22", "F22", warnStyle)
	} else {
		f.SetCellValue(target, "F22", qualified)
		f.SetCellStyle(target, "F22", "F22", successStyle)
	}
	f.SetCellValue(target, "E23", t.FeatureValues.RightBackLocatePinMean)
	if t.FeatureValues.RightBackLocatePinMean > 22 {
		f.SetCellValue(target, "F23", unqualified)
		f.SetCellStyle(target, "F23", "F23", warnStyle)
	} else {
		f.SetCellValue(target, "F23", qualified)
		f.SetCellStyle(target, "F23", "F23", successStyle)
	}

	f.SetCellValue(target, "E24", t.FeatureValues.FrontGuideChainMean)
	if t.FeatureValues.FrontGuideChainMean > 40 {
		f.SetCellValue(target, "F24", unqualified)
		f.SetCellStyle(target, "F24", "F24", warnStyle)
	} else {
		f.SetCellValue(target, "F24", qualified)
		f.SetCellStyle(target, "F24", "F24", successStyle)
	}
	f.SetCellValue(target, "E25", t.FeatureValues.BackGuideChainMean)
	if t.FeatureValues.BackGuideChainMean > 40 {
		f.SetCellValue(target, "F25", unqualified)
		f.SetCellStyle(target, "F25", "F25", warnStyle)
	} else {
		f.SetCellValue(target, "F25", qualified)
		f.SetCellStyle(target, "F25", "F25", successStyle)
	}

	f.SetCellValue(target, "E26", t.FeatureValues.LockUnlockGunMean1)
	if t.FeatureValues.LockUnlockGunMean1 >= 35 {
		f.SetCellValue(target, "F26", unqualified)
		f.SetCellStyle(target, "F26", "F26", warnStyle)
	} else {
		f.SetCellValue(target, "F26", qualified)
		f.SetCellStyle(target, "F26", "F26", successStyle)
	}
	f.SetCellValue(target, "E27", t.FeatureValues.LockUnlockGunMean2)
	if t.FeatureValues.LockUnlockGunMean2 >= 35 {
		f.SetCellValue(target, "F27", unqualified)
		f.SetCellStyle(target, "F27", "F27", warnStyle)
	} else {
		f.SetCellValue(target, "F27", qualified)
		f.SetCellStyle(target, "F27", "F27", successStyle)
	}
	f.SetCellValue(target, "E28", t.FeatureValues.LockUnlockGunMean3)
	if t.FeatureValues.LockUnlockGunMean3 >= 35 {
		f.SetCellValue(target, "F28", unqualified)
		f.SetCellStyle(target, "F28", "F28", warnStyle)
	} else {
		f.SetCellValue(target, "F28", qualified)
		f.SetCellStyle(target, "F28", "F28", successStyle)
	}
	f.SetCellValue(target, "E29", t.FeatureValues.LockUnlockGunMean4)
	if t.FeatureValues.LockUnlockGunMean4 >= 35 {
		f.SetCellValue(target, "F29", unqualified)
		f.SetCellStyle(target, "F29", "F29", warnStyle)
	} else {
		f.SetCellValue(target, "F29", qualified)
		f.SetCellStyle(target, "F29", "F29", successStyle)
	}
	f.SetCellValue(target, "E30", t.FeatureValues.LockUnlockGunMean5)
	if t.FeatureValues.LockUnlockGunMean5 >= 35 {
		f.SetCellValue(target, "F30", unqualified)
		f.SetCellStyle(target, "F30", "F30", warnStyle)
	} else {
		f.SetCellValue(target, "F30", qualified)
		f.SetCellStyle(target, "F30", "F30", successStyle)
	}
	f.SetCellValue(target, "E31", t.FeatureValues.LockUnlockGunMean6)
	if t.FeatureValues.LockUnlockGunMean6 >= 35 {
		f.SetCellValue(target, "F31", unqualified)
		f.SetCellStyle(target, "F31", "F31", warnStyle)
	} else {
		f.SetCellValue(target, "F31", qualified)
		f.SetCellStyle(target, "F31", "F31", successStyle)
	}
	f.SetCellValue(target, "E32", t.FeatureValues.LockUnlockGunMean7)
	if t.FeatureValues.LockUnlockGunMean7 >= 35 {
		f.SetCellValue(target, "F32", unqualified)
		f.SetCellStyle(target, "F32", "F32", warnStyle)
	} else {
		f.SetCellValue(target, "F32", qualified)
		f.SetCellStyle(target, "F32", "F32", successStyle)
	}
	f.SetCellValue(target, "E33", t.FeatureValues.LockUnlockGunMean8)
	if t.FeatureValues.LockUnlockGunMean8 >= 35 {
		f.SetCellValue(target, "F33", unqualified)
		f.SetCellStyle(target, "F33", "F33", warnStyle)
	} else {
		f.SetCellValue(target, "F33", qualified)
		f.SetCellStyle(target, "F33", "F33", successStyle)
	}
	f.SetCellValue(target, "E34", t.FeatureValues.LockUnlockGunMean9)
	if t.FeatureValues.LockUnlockGunMean9 >= 35 {
		f.SetCellValue(target, "F34", unqualified)
		f.SetCellStyle(target, "F34", "F34", warnStyle)
	} else {
		f.SetCellValue(target, "F34", qualified)
		f.SetCellStyle(target, "F34", "F34", successStyle)
	}
	f.SetCellValue(target, "E35", t.FeatureValues.LockUnlockGunMean10)
	if t.FeatureValues.LockUnlockGunMean10 >= 35 {
		f.SetCellValue(target, "F35", unqualified)
		f.SetCellStyle(target, "F35", "F35", warnStyle)
	} else {
		f.SetCellValue(target, "F35", qualified)
		f.SetCellStyle(target, "F35", "F35", successStyle)
	}

	f.SetCellValue(target, "E36", t.FeatureValues.ShuttleMean)
	if t.FeatureValues.ShuttleMean > 30 {
		f.SetCellValue(target, "F36", unqualified)
		f.SetCellStyle(target, "F36", "F36", warnStyle)
	} else {
		f.SetCellValue(target, "F36", qualified)
		f.SetCellStyle(target, "F36", "F36", successStyle)
	}

	f.SetCellValue(target, "E37", t.FeatureValues.PalletForkMean)
	if t.FeatureValues.PalletForkMean > 25 {
		f.SetCellValue(target, "F37", unqualified)
		f.SetCellStyle(target, "F37", "F37", warnStyle)
	} else {
		f.SetCellValue(target, "F37", qualified)
		f.SetCellStyle(target, "F37", "F37", successStyle)
	}

	f.SetCellValue(target, "E38", t.FeatureValues.StackerShiftNoloadMean)
	if t.FeatureValues.StackerShiftNoloadMean > 25 {
		f.SetCellValue(target, "F38", unqualified)
		f.SetCellStyle(target, "F38", "F38", warnStyle)
	} else {
		f.SetCellValue(target, "F38", qualified)
		f.SetCellStyle(target, "F38", "F38", successStyle)
	}
	f.SetCellValue(target, "E39", t.FeatureValues.StackerShiftLoadMean)
	if t.FeatureValues.StackerShiftLoadMean > 25 {
		f.SetCellValue(target, "F39", unqualified)
		f.SetCellStyle(target, "F39", "F39", warnStyle)
	} else {
		f.SetCellValue(target, "F39", qualified)
		f.SetCellStyle(target, "F39", "F39", successStyle)
	}
	f.SetCellValue(target, "E40", t.FeatureValues.StackerLiftLoadMean)
	if t.FeatureValues.StackerLiftLoadMean > 50 {
		f.SetCellValue(target, "F40", unqualified)
		f.SetCellStyle(target, "F40", "F40", warnStyle)
	} else {
		f.SetCellValue(target, "F40", qualified)
		f.SetCellStyle(target, "F40", "F40", successStyle)
	}
	f.SetCellValue(target, "E41", t.FeatureValues.StackerLiftNoloadMean)
	if t.FeatureValues.StackerLiftNoloadMean < 5 {
		f.SetCellValue(target, "F41", unqualified)
		f.SetCellStyle(target, "F41", "F41", warnStyle)
	} else {
		f.SetCellValue(target, "F41", qualified)
		f.SetCellStyle(target, "F41", "F41", successStyle)
	}
	buffer := bytes.NewBuffer(nil)
	err = f.Write(buffer)

	return buffer, err
}

// TorqueV2 扭矩报告需要按不同车型生成
type TorqueV2 struct {
	DeviceId           string
	Manufacturer       string
	BrandTotalServices map[string]int
	BrandValidServices map[string]int
	StartDate          string
	EndDate            string
	BrandFeatureValues map[string]umw.FeatureVariables
	Logger             *zap.SugaredLogger
}

func (t *TorqueV2) GenerateNewTorqueReport(area, project string) (*bytes.Buffer, error) {
	path, _ := os.Getwd()
	lang, targetName, qualified, unqualified, validCountName := "zh", "扭矩报告", "合格", "不合格", "有效换电次数"
	if area == um.Europe {
		lang, targetName, qualified, unqualified, validCountName = "en", "Torque Report", "Qualified", "Unqualified", "valid count"
	}
	f, err := excelize.OpenFile(fmt.Sprintf("%s/torque-report-template-%s-%s.xlsx", path, lang, ucmd.RenameProjectDB(project)))
	if err != nil {
		t.Logger.Errorf("failed to open torque report template, err: %v", err)
		return nil, err
	}
	defer func() {
		if err = f.Close(); err != nil {
			t.Logger.Errorf("failded to close file, err: %v", err)
		}
	}()
	brands := []string{model.EvBrandNIO, model.EvBrandONVO}
	targets := make([]string, 0)
	for _, brand := range brands {
		targets = append(targets, fmt.Sprintf("%s(%s)", targetName, brand))
	}
	if err = f.SetSheetName("Sheet1", targets[0]); err != nil {
		return nil, err
	}
	if err = f.SetSheetName("Sheet2", targets[1]); err != nil {
		return nil, err
	}

	warnStyle, _ := f.NewStyle(&excelize.Style{
		Border: []excelize.Border{
			{
				Type:  "right",
				Color: "#000000",
				Style: 1,
			},
			{
				Type:  "left",
				Color: "#000000",
				Style: 1,
			},
			{
				Type:  "top",
				Color: "#000000",
				Style: 1,
			},
			{
				Type:  "bottom",
				Color: "#000000",
				Style: 1,
			},
		},
		Font: &excelize.Font{
			Size:   11,
			Family: "文泉驿点阵正黑",
			Color:  "#FFFFFF",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#EF0706"},
			Shading: 1,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
	})

	successStyle, _ := f.NewStyle(&excelize.Style{
		Border: []excelize.Border{
			{
				Type:  "right",
				Color: "#000000",
				Style: 1,
			},
			{
				Type:  "left",
				Color: "#000000",
				Style: 1,
			},
			{
				Type:  "top",
				Color: "#000000",
				Style: 1,
			},
			{
				Type:  "bottom",
				Color: "#000000",
				Style: 1,
			},
		},
		Font: &excelize.Font{
			Size:   11,
			Family: "文泉驿点阵正黑",
			Color:  "#FFFFFF",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#06B050"},
			Shading: 1,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
	})

	for i, target := range targets {
		brand := brands[i]
		f.SetCellValue(target, "B3", t.DeviceId)
		f.SetCellValue(target, "E3", t.Manufacturer)
		f.SetCellValue(target, "B4", fmt.Sprintf("%s - %s", t.StartDate, t.EndDate))
		f.SetCellValue(target, "E4", fmt.Sprintf("%d（%s：%d）", t.BrandTotalServices[brand], validCountName, t.BrandValidServices[brand]))

		if t.BrandTotalServices[brand] == 0 {
			continue
		}

		f.SetCellValue(target, "E7", t.BrandFeatureValues[brand].LeftFrontPushRodMean)
		if t.BrandFeatureValues[brand].LeftFrontPushRodMean > 20 {
			f.SetCellValue(target, "F7", unqualified)
			f.SetCellStyle(target, "F7", "F7", warnStyle)
		} else {
			f.SetCellValue(target, "F7", qualified)
			f.SetCellStyle(target, "F7", "F7", successStyle)
		}
		f.SetCellValue(target, "E8", t.BrandFeatureValues[brand].RightFrontPushRodMean)
		if t.BrandFeatureValues[brand].RightFrontPushRodMean > 20 {
			f.SetCellValue(target, "F8", unqualified)
			f.SetCellStyle(target, "F8", "F8", warnStyle)
		} else {
			f.SetCellValue(target, "F8", qualified)
			f.SetCellStyle(target, "F8", "F8", successStyle)
		}
		f.SetCellValue(target, "E9", t.BrandFeatureValues[brand].LeftBackPushRodMean)
		if t.BrandFeatureValues[brand].LeftBackPushRodMean > 20 {
			f.SetCellValue(target, "F9", unqualified)
			f.SetCellStyle(target, "F9", "F9", warnStyle)
		} else {
			f.SetCellValue(target, "F9", qualified)
			f.SetCellStyle(target, "F9", "F9", successStyle)
		}
		f.SetCellValue(target, "E10", t.BrandFeatureValues[brand].RightBackPushRodMean)
		if t.BrandFeatureValues[brand].RightBackPushRodMean > 20 {
			f.SetCellValue(target, "F10", unqualified)
			f.SetCellStyle(target, "F10", "F10", warnStyle)
		} else {
			f.SetCellValue(target, "F10", qualified)
			f.SetCellStyle(target, "F10", "F10", successStyle)
		}
		f.SetCellValue(target, "E11", t.BrandFeatureValues[brand].LeftFrontPushRodExtremum)
		if t.BrandFeatureValues[brand].LeftFrontPushRodExtremum > 30 {
			f.SetCellValue(target, "F11", unqualified)
			f.SetCellStyle(target, "F11", "F11", warnStyle)
		} else {
			f.SetCellValue(target, "F11", qualified)
			f.SetCellStyle(target, "F11", "F11", successStyle)
		}
		f.SetCellValue(target, "E12", t.BrandFeatureValues[brand].RightFrontPushRodExtremum)
		if t.BrandFeatureValues[brand].RightFrontPushRodExtremum > 30 {
			f.SetCellValue(target, "F12", unqualified)
			f.SetCellStyle(target, "F12", "F12", warnStyle)
		} else {
			f.SetCellValue(target, "F12", qualified)
			f.SetCellStyle(target, "F12", "F12", successStyle)
		}
		f.SetCellValue(target, "E13", t.BrandFeatureValues[brand].LeftBackPushRodExtremum)
		if t.BrandFeatureValues[brand].LeftBackPushRodExtremum > 30 {
			f.SetCellValue(target, "F13", unqualified)
			f.SetCellStyle(target, "F13", "F13", warnStyle)
		} else {
			f.SetCellValue(target, "F13", qualified)
			f.SetCellStyle(target, "F13", "F13", successStyle)
		}
		f.SetCellValue(target, "E14", t.BrandFeatureValues[brand].RightBackPushRodExtremum)
		if t.BrandFeatureValues[brand].RightBackPushRodExtremum > 30 {
			f.SetCellValue(target, "F14", unqualified)
			f.SetCellStyle(target, "F14", "F14", warnStyle)
		} else {
			f.SetCellValue(target, "F14", qualified)
			f.SetCellStyle(target, "F14", "F14", successStyle)
		}

		f.SetCellValue(target, "E15", t.BrandFeatureValues[brand].LeftHatchDoorMean)
		if t.BrandFeatureValues[brand].LeftHatchDoorMean > 38 {
			f.SetCellValue(target, "F15", unqualified)
			f.SetCellStyle(target, "F15", "F15", warnStyle)
		} else {
			f.SetCellValue(target, "F15", qualified)
			f.SetCellStyle(target, "F15", "F15", successStyle)
		}
		f.SetCellValue(target, "E16", t.BrandFeatureValues[brand].RightHatchDoorMean)
		if t.BrandFeatureValues[brand].RightHatchDoorMean > 38 {
			f.SetCellValue(target, "F16", unqualified)
			f.SetCellStyle(target, "F16", "F16", warnStyle)
		} else {
			f.SetCellValue(target, "F16", qualified)
			f.SetCellStyle(target, "F16", "F16", successStyle)
		}
		f.SetCellValue(target, "E17", t.BrandFeatureValues[brand].LeftHatchDoorExtremum)
		if t.BrandFeatureValues[brand].LeftHatchDoorExtremum > 80 {
			f.SetCellValue(target, "F17", unqualified)
			f.SetCellStyle(target, "F17", "F17", warnStyle)
		} else {
			f.SetCellValue(target, "F17", qualified)
			f.SetCellStyle(target, "F17", "F17", successStyle)
		}
		f.SetCellValue(target, "E18", t.BrandFeatureValues[brand].RightHatchDoorExtremum)
		if t.BrandFeatureValues[brand].RightHatchDoorExtremum > 80 {
			f.SetCellValue(target, "F18", unqualified)
			f.SetCellStyle(target, "F18", "F18", warnStyle)
		} else {
			f.SetCellValue(target, "F18", qualified)
			f.SetCellStyle(target, "F18", "F18", successStyle)
		}

		f.SetCellValue(target, "E19", t.BrandFeatureValues[brand].RgvMoveMean)
		if t.BrandFeatureValues[brand].RgvMoveMean > 25 {
			f.SetCellValue(target, "F19", unqualified)
			f.SetCellStyle(target, "F19", "F19", warnStyle)
		} else {
			f.SetCellValue(target, "F19", qualified)
			f.SetCellStyle(target, "F19", "F19", successStyle)
		}

		f.SetCellValue(target, "E20", t.BrandFeatureValues[brand].RgvLiftNoloadMean)
		if t.BrandFeatureValues[brand].RgvLiftNoloadMean > 25 {
			f.SetCellValue(target, "F20", unqualified)
			f.SetCellStyle(target, "F20", "F20", warnStyle)
		} else {
			f.SetCellValue(target, "F20", qualified)
			f.SetCellStyle(target, "F20", "F20", successStyle)
		}

		f.SetCellValue(target, "E21", t.BrandFeatureValues[brand].RgvLiftMean)
		if t.BrandFeatureValues[brand].RgvLiftMean > 40 {
			f.SetCellValue(target, "F21", unqualified)
			f.SetCellStyle(target, "F21", "F21", warnStyle)
		} else {
			f.SetCellValue(target, "F21", qualified)
			f.SetCellStyle(target, "F21", "F21", successStyle)
		}

		f.SetCellValue(target, "E22", t.BrandFeatureValues[brand].LeftFrontLocatePinMean)
		if t.BrandFeatureValues[brand].LeftFrontLocatePinMean > 22 {
			f.SetCellValue(target, "F22", unqualified)
			f.SetCellStyle(target, "F22", "F22", warnStyle)
		} else {
			f.SetCellValue(target, "F22", qualified)
			f.SetCellStyle(target, "F22", "F22", successStyle)
		}
		f.SetCellValue(target, "E23", t.BrandFeatureValues[brand].RightBackLocatePinMean)
		if t.BrandFeatureValues[brand].RightBackLocatePinMean > 22 {
			f.SetCellValue(target, "F23", unqualified)
			f.SetCellStyle(target, "F23", "F23", warnStyle)
		} else {
			f.SetCellValue(target, "F23", qualified)
			f.SetCellStyle(target, "F23", "F23", successStyle)
		}

		f.SetCellValue(target, "E24", t.BrandFeatureValues[brand].FrontGuideChainMean)
		if t.BrandFeatureValues[brand].FrontGuideChainMean > 40 {
			f.SetCellValue(target, "F24", unqualified)
			f.SetCellStyle(target, "F24", "F24", warnStyle)
		} else {
			f.SetCellValue(target, "F24", qualified)
			f.SetCellStyle(target, "F24", "F24", successStyle)
		}
		f.SetCellValue(target, "E25", t.BrandFeatureValues[brand].BackGuideChainMean)
		if t.BrandFeatureValues[brand].BackGuideChainMean > 40 {
			f.SetCellValue(target, "F25", unqualified)
			f.SetCellStyle(target, "F25", "F25", warnStyle)
		} else {
			f.SetCellValue(target, "F25", qualified)
			f.SetCellStyle(target, "F25", "F25", successStyle)
		}

		f.SetCellValue(target, "E26", t.BrandFeatureValues[brand].LockUnlockGunMean1)
		if t.BrandFeatureValues[brand].LockUnlockGunMean1 >= 35 {
			f.SetCellValue(target, "F26", unqualified)
			f.SetCellStyle(target, "F26", "F26", warnStyle)
		} else {
			f.SetCellValue(target, "F26", qualified)
			f.SetCellStyle(target, "F26", "F26", successStyle)
		}
		f.SetCellValue(target, "E27", t.BrandFeatureValues[brand].LockUnlockGunMean2)
		if t.BrandFeatureValues[brand].LockUnlockGunMean2 >= 35 {
			f.SetCellValue(target, "F27", unqualified)
			f.SetCellStyle(target, "F27", "F27", warnStyle)
		} else {
			f.SetCellValue(target, "F27", qualified)
			f.SetCellStyle(target, "F27", "F27", successStyle)
		}
		f.SetCellValue(target, "E28", t.BrandFeatureValues[brand].LockUnlockGunMean3)
		if t.BrandFeatureValues[brand].LockUnlockGunMean3 >= 35 {
			f.SetCellValue(target, "F28", unqualified)
			f.SetCellStyle(target, "F28", "F28", warnStyle)
		} else {
			f.SetCellValue(target, "F28", qualified)
			f.SetCellStyle(target, "F28", "F28", successStyle)
		}
		f.SetCellValue(target, "E29", t.BrandFeatureValues[brand].LockUnlockGunMean4)
		if t.BrandFeatureValues[brand].LockUnlockGunMean4 >= 35 {
			f.SetCellValue(target, "F29", unqualified)
			f.SetCellStyle(target, "F29", "F29", warnStyle)
		} else {
			f.SetCellValue(target, "F29", qualified)
			f.SetCellStyle(target, "F29", "F29", successStyle)
		}
		f.SetCellValue(target, "E30", t.BrandFeatureValues[brand].LockUnlockGunMean5)
		if t.BrandFeatureValues[brand].LockUnlockGunMean5 >= 35 {
			f.SetCellValue(target, "F30", unqualified)
			f.SetCellStyle(target, "F30", "F30", warnStyle)
		} else {
			f.SetCellValue(target, "F30", qualified)
			f.SetCellStyle(target, "F30", "F30", successStyle)
		}
		f.SetCellValue(target, "E31", t.BrandFeatureValues[brand].LockUnlockGunMean6)
		if t.BrandFeatureValues[brand].LockUnlockGunMean6 >= 35 {
			f.SetCellValue(target, "F31", unqualified)
			f.SetCellStyle(target, "F31", "F31", warnStyle)
		} else {
			f.SetCellValue(target, "F31", qualified)
			f.SetCellStyle(target, "F31", "F31", successStyle)
		}
		f.SetCellValue(target, "E32", t.BrandFeatureValues[brand].LockUnlockGunMean7)
		if t.BrandFeatureValues[brand].LockUnlockGunMean7 >= 35 {
			f.SetCellValue(target, "F32", unqualified)
			f.SetCellStyle(target, "F32", "F32", warnStyle)
		} else {
			f.SetCellValue(target, "F32", qualified)
			f.SetCellStyle(target, "F32", "F32", successStyle)
		}
		f.SetCellValue(target, "E33", t.BrandFeatureValues[brand].LockUnlockGunMean8)
		if t.BrandFeatureValues[brand].LockUnlockGunMean8 >= 35 {
			f.SetCellValue(target, "F33", unqualified)
			f.SetCellStyle(target, "F33", "F33", warnStyle)
		} else {
			f.SetCellValue(target, "F33", qualified)
			f.SetCellStyle(target, "F33", "F33", successStyle)
		}
		f.SetCellValue(target, "E34", t.BrandFeatureValues[brand].LockUnlockGunMean9)
		if t.BrandFeatureValues[brand].LockUnlockGunMean9 >= 35 {
			f.SetCellValue(target, "F34", unqualified)
			f.SetCellStyle(target, "F34", "F34", warnStyle)
		} else {
			f.SetCellValue(target, "F34", qualified)
			f.SetCellStyle(target, "F34", "F34", successStyle)
		}
		f.SetCellValue(target, "E35", t.BrandFeatureValues[brand].LockUnlockGunMean10)
		if t.BrandFeatureValues[brand].LockUnlockGunMean10 >= 35 {
			f.SetCellValue(target, "F35", unqualified)
			f.SetCellStyle(target, "F35", "F35", warnStyle)
		} else {
			f.SetCellValue(target, "F35", qualified)
			f.SetCellStyle(target, "F35", "F35", successStyle)
		}

		f.SetCellValue(target, "E36", t.BrandFeatureValues[brand].PalletForkMean)
		if t.BrandFeatureValues[brand].PalletForkMean > 25 {
			f.SetCellValue(target, "F36", unqualified)
			f.SetCellStyle(target, "F36", "F36", warnStyle)
		} else {
			f.SetCellValue(target, "F36", qualified)
			f.SetCellStyle(target, "F36", "F36", successStyle)
		}

		f.SetCellValue(target, "E37", t.BrandFeatureValues[brand].StackerShiftNoloadMean)
		if t.BrandFeatureValues[brand].StackerShiftNoloadMean > 25 {
			f.SetCellValue(target, "F37", unqualified)
			f.SetCellStyle(target, "F37", "F37", warnStyle)
		} else {
			f.SetCellValue(target, "F37", qualified)
			f.SetCellStyle(target, "F37", "F37", successStyle)
		}
		f.SetCellValue(target, "E38", t.BrandFeatureValues[brand].StackerShiftLoadMean)
		if t.BrandFeatureValues[brand].StackerShiftLoadMean > 25 {
			f.SetCellValue(target, "F38", unqualified)
			f.SetCellStyle(target, "F38", "F38", warnStyle)
		} else {
			f.SetCellValue(target, "F38", qualified)
			f.SetCellStyle(target, "F38", "F38", successStyle)
		}
		f.SetCellValue(target, "E39", t.BrandFeatureValues[brand].StackerLiftLoadMean)
		if t.BrandFeatureValues[brand].StackerLiftLoadMean > 50 {
			f.SetCellValue(target, "F39", unqualified)
			f.SetCellStyle(target, "F39", "F39", warnStyle)
		} else {
			f.SetCellValue(target, "F39", qualified)
			f.SetCellStyle(target, "F39", "F39", successStyle)
		}
		f.SetCellValue(target, "E40", t.BrandFeatureValues[brand].StackerLiftNoloadMean)
		if t.BrandFeatureValues[brand].StackerLiftNoloadMean < 5 {
			f.SetCellValue(target, "F40", unqualified)
			f.SetCellStyle(target, "F40", "F40", warnStyle)
		} else {
			f.SetCellValue(target, "F40", qualified)
			f.SetCellStyle(target, "F40", "F40", successStyle)
		}
	}

	buffer := bytes.NewBuffer(nil)
	err = f.Write(buffer)

	return buffer, err
}
