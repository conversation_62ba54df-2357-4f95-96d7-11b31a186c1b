package exec

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"git.nevint.com/golang-libs/common-utils/cmd"
	"git.nevint.com/golang-libs/common-utils/larkservice"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	domain_plc "git.nevint.com/welkin2/welkin-backend/domain/plc"
	"git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/util"
)

type ServiceCheckHandler struct {
	Watcher               client.Watcher
	Projects              []string // 哪些站要算
	TimeLocation          *time.Location
	PlcDataMissingService map[string][]string
	startTime             time.Time
	endTime               time.Time
}

const (
	PlcDataLoseAlarm = "plcDataLoseAlarm"
)

func (s *ServiceCheckHandler) Process(ctx context.Context) error {
	if len(s.Projects) == 0 {
		return nil
	}
	if cmd.GetEnv() != "prod" {
		logger.Logger.Infof("ServiceCheck is not prod env, no need check")
		return nil
	}
	s.PlcDataMissingService = map[string][]string{}
	for _, project := range s.Projects {
		s.PlcDataMissingService[project] = []string{}
		err := s.checkPastHour(ctx, project)
		if err != nil {
			return err
		}
	}
	return s.sendAlarm(ctx)
}

func (s *ServiceCheckHandler) checkPastHour(ctx context.Context, project string) error {
	nowTime := time.Now().In(s.TimeLocation)
	courTime := nowTime.Add(-(2 * time.Hour))
	minute := courTime.Minute()
	s.startTime = courTime.Add(-(time.Minute * time.Duration(minute)))
	s.endTime = courTime.Add(time.Minute * time.Duration(60-minute))

	dbName := fmt.Sprintf("%s-%s", umw.ServiceInfo, project)
	collectionMonthName := util.EncodeDate(courTime.Format("2006-01-02"))

	filter := bson.D{
		{"date", bson.M{"$gte": time.UnixMilli(s.startTime.UnixMilli()), "$lte": time.UnixMilli(s.endTime.UnixMilli())}},
	}
	Limit := int64(1000)
	Offset := int64(0)
	for {
		var sericeInfos []umw.MongoServiceInfo
		cur, err := s.Watcher.Mongodb().Client.Database(dbName).Collection(collectionMonthName).Find(ctx, filter, options.Find().SetSort(bson.M{"date": 1}).SetSkip(Offset).SetLimit(Limit))
		if err = cur.All(ctx, &sericeInfos); err != nil {
			return err
		}

		for _, sericeInfo := range sericeInfos {
			// 服务异常结束的不作为统计
			if sericeInfo.FinishResult == 0 {
				continue
			}
			serviceId := sericeInfo.ServiceId
			plcDO := &domain_plc.PLCDO{}
			cond := domain_plc.ListPLCCond{
				DeviceId:  sericeInfo.DeviceId,
				Project:   project,
				ServiceId: serviceId,
				StartTime: sericeInfo.StartTime,
				EndTime:   sericeInfo.EndTime,
			}
			count, err := plcDO.CountPLC(ctx, cond)
			if err != nil {
				return err
			}
			if count == 0 {
				s.PlcDataMissingService[project] = append(s.PlcDataMissingService[project], serviceId)
			}
		}

		Offset = Offset + int64(len(sericeInfos))
		if int64(len(sericeInfos)) != Limit {
			break
		}
	}
	return nil
}

// 给人数据用, 时间不要跨月
func (s *ServiceCheckHandler) TestRun(ctx context.Context) error {
	if len(s.Projects) == 0 {
		return nil
	}
	for _, project := range s.Projects {
		err := s.CheckInTimeRange(ctx, project, "2024-11-19", "2024-11-25")
		if err != nil {
			return err
		}
	}
	return nil
}

// 给人数据用, 时间不要跨月
// startDate,endDate 格式 YYYY-MM-DD
func (s *ServiceCheckHandler) CheckInTimeRange(ctx context.Context, project string, startDate, endDate string) error {
	startTime, err := time.ParseInLocation("2006-01-02", startDate, s.TimeLocation)
	if err != nil {
		fmt.Println(fmt.Sprintf("错误 %v", err))
		panic(err)
	}
	endTime, err := time.ParseInLocation("2006-01-02", endDate, s.TimeLocation)
	if err != nil {
		fmt.Println(fmt.Sprintf("错误 %v", err))
		panic(err)
	}
	fmt.Println(fmt.Sprintf("调试 开始: project:%v 开始时间:%v 结束时间:%v", project, startDate, endDate))

	s.startTime = startTime
	s.endTime = endTime

	dbName := fmt.Sprintf("%s-%s", umw.ServiceInfo, project)
	collectionMonthName := util.EncodeDate(startDate)

	filter := bson.D{
		{"date", bson.M{"$gte": time.UnixMilli(s.startTime.UnixMilli()), "$lte": time.UnixMilli(s.endTime.UnixMilli())}},
	}
	Limit := int64(1000)
	Offset := int64(0)
	serviceCount := 0
	emptyPlcServiceCount := 0
	for true {
		var sericeInfos []umw.MongoServiceInfo
		cur, err := s.Watcher.Mongodb().Client.Database(dbName).Collection(collectionMonthName).Find(ctx, filter, options.Find().SetSort(bson.M{"date": 1}).SetSkip(Offset).SetLimit(Limit))
		if err = cur.All(ctx, &sericeInfos); err != nil {
			return err
		}

		for _, sericeInfo := range sericeInfos {
			// 服务异常结束的不作为统计
			if sericeInfo.FinishResult == 0 {
				continue
			}
			serviceCount++
			serviceId := sericeInfo.ServiceId
			deviceId := sericeInfo.DeviceId
			plcDbName := fmt.Sprintf("plc-record-%v_%v", project, collectionMonthName)
			count, err := s.Watcher.PLCMongodb().Client.Database(plcDbName).Collection(deviceId).CountDocuments(ctx, bson.D{bson.E{Key: "service_id", Value: serviceId}})
			if err != nil {
				return err
			}
			if count == 0 {
				emptyPlcServiceCount++
				//s.PlcDataMissingService[project] = append(s.PlcDataMissingService[project], serviceId)
			}
		}

		Offset = Offset + int64(len(sericeInfos))
		if int64(len(sericeInfos)) != Limit {
			break
		}
	}
	fmt.Println(fmt.Sprintf("调试 结果: project:%v 开始时间:%v 结束时间:%v 总单数:%v 丢plc单数:%v", project, startDate, endDate, serviceCount, emptyPlcServiceCount))
	return nil
}

func (s *ServiceCheckHandler) sendAlarm(ctx context.Context) error {
	needAlarm := false
	for _, data := range s.PlcDataMissingService {
		if len(data) != 0 {
			needAlarm = true
		}
	}
	if !needAlarm {
		logger.Logger.Infof("ServiceCheckHandler. don't need alarm %v", cmd.ToJsonStrIgnoreErr(s))
		return nil
	}
	_, found := config.Cfg.AlarmReceiver[PlcDataLoseAlarm]
	if !found {
		logger.Logger.Infof("no alarm receiver config")
		return nil
	}
	card := larkservice.NewInfoCard()
	card.HeaderName = fmt.Sprintf("[ENV:%v]服务PLC数据丢失 %v ~ %v", cmd.GetEnv(), s.startTime.Format("2006-01-02 15:04:05"), s.endTime.Format("2006-01-02 15:04:05"))

	for _, project := range s.Projects {
		// todo: 最方便的方式临时改一下
		if project == "pus2" {
			project = "ps2"
		}
		card.AppendElement(larkservice.ElementDetail, []larkservice.DetailMessage{
			{Msg: fmt.Sprintf("**%v 丢失服务单数%v 丢数据服务id：**\n%v", project, len(s.PlcDataMissingService[project]), s.PlcDataMissingService[project])},
		})
	}
	content, err := card.Build()
	if err != nil {
		return err
	}

	receivers := config.Cfg.AlarmReceiver[PlcDataLoseAlarm]
	for _, receiver := range receivers {
		receiverInfo := larkservice.Receiver{
			Type:       receiver.Type,
			ReceiveIds: receiver.ReceiverIds,
		}
		err = larkservice.SendCard(content, receiverInfo)
		if err != nil {
			return err
		}
	}

	return nil
}
