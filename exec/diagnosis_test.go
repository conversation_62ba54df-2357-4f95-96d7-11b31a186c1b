package exec

import (
	"fmt"
	"git.nevint.com/welkin2/welkin-backend/client"
	"net/http"
	"os"
	"testing"

	"github.com/gin-gonic/gin"
	"net/http/httptest"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	ulog "git.nevint.com/golang-libs/common-utils/logger"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
)

var (
	w   client.Watcher
	cfg *ucfg.Config
)

func init() {
	os.Setenv("APOLLO_META", "https://apollo-config-cn-stg.nioint.com")
	os.Setenv("ENV", "stg")
	cfg = &ucfg.Config{}
	ucfg.GetConfigApollo("config.json", true, cfg, ucfg.NeedValidate(true), ucfg.ServiceName("ppd-welkin-backend"), ucfg.Cluster(ucfg.GetWelkinApolloCluster()))
	defer ulog.Final()
	cfg.Log.DirPath = "/tmp/logs"
	log.Init(cfg.Log, true)
	logger := log.Logger.Named("CronJob")
	w = client.NewWatcherByParam(cfg, logger)
	InitCronJobs(cfg)
}

func TestFunc_partitionByBoundaries(t *testing.T) {
	devices := []model.DeviceStuckServiceCount{
		{
			DeviceId:   "PS-NIO-5e93cf5f-6ec32733",
			Proportion: 0.78,
		},
		{
			DeviceId:   "PS-NIO-5ed91344-518e9d8d",
			Proportion: 0.2,
		},
		{
			DeviceId:   "PS-NIO-5ee80c91-39287b4a",
			Proportion: 0.19,
		},
		{
			DeviceId:   "PS-NIO-605802b3-2b890529",
			Proportion: 0.12,
		},
		{
			DeviceId:   "PS-NIO-6190f84c-9f47e4a1",
			Proportion: 0.09,
		},
		{
			DeviceId:   "PS-NIO-61ed4df9-4ce1bee2",
			Proportion: 0.05,
		},
		{
			DeviceId:   "PS-NIO-61fdc3c6-f5d36bd7",
			Proportion: 0.022,
		},
		{
			DeviceId:   "PS-NIO-6202ca44-472ef7bb",
			Proportion: 0.022,
		},
		{
			DeviceId:   "PS-NIO-6257a010-79bb88f6",
			Proportion: 0.012,
		},
		{
			DeviceId:   "PS-NIO-62a4c533-0f51a930",
			Proportion: 0.002,
		},
	}
	deviceMap := partitionByBoundaries(devices)
	for key, items := range deviceMap {
		fmt.Printf("key %s:  %s\n\n", key, ucmd.ToJsonStrIgnoreErr(items))
	}
}

func TestDiagnosis_ListStuckAlarmId(t *testing.T) {
	d := NewDiagnosisHandler(w, cfg)
	r := gin.Default()
	r.GET("/diagnosis/v1/stuck/:project/alarm_id", d.ListStuckAlarmId())

	go func() {
		r.Run(":8080")
	}()

	recorder := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "http://localhost:8080/diagnosis/v1/stuck/PUS3/alarm_id?description=PLC左开合门", nil)
	r.ServeHTTP(recorder, req)
	if recorder.Code != http.StatusOK {
		t.Fatalf("Expected to get status %d but instead got %d\n", http.StatusOK, recorder.Code)
	}
	responseBody := recorder.Body.String()
	t.Log(responseBody)
}

func TestDiagnosis_ListStuckAlarms(t *testing.T) {
	d := NewDiagnosisHandler(w, cfg)
	r := gin.Default()
	r.GET("/diagnosis/v1/stuck/:project/alarm", d.ListStuckAlarms())

	go func() {
		r.Run(":8080")
	}()

	recorder := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "http://localhost:8080/diagnosis/v1/stuck/PUS3/alarm?start_time=1715238000000&end_time=1715324400000&page=1&size=1", nil)
	r.ServeHTTP(recorder, req)
	if recorder.Code != http.StatusOK {
		t.Fatalf("Expected to get status %d but instead got %d\n", http.StatusOK, recorder.Code)
	}
	responseBody := recorder.Body.String()
	t.Log(responseBody)
}

func TestDiagnosis_ListJira(t *testing.T) {
	d := NewDiagnosisHandler(w, cfg)
	r := gin.Default()
	r.GET("/diagnosis/v1/jira/list", d.ListJira())
	go func() {
		r.Run(":8080")
	}()

	recorder := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "http://localhost:8080/diagnosis/v1/jira/list?type=故障&key=挂车", nil)
	r.ServeHTTP(recorder, req)
	if recorder.Code != http.StatusOK {
		t.Fatalf("Expected to get status %d but instead got %d\n", http.StatusOK, recorder.Code)
	}
	responseBody := recorder.Body.String()
	t.Log(responseBody)
}

func TestDiagnosis_ListStuckAlarmInfo(t *testing.T) {
	d := NewDiagnosisHandler(w, cfg)
	r := gin.Default()
	r.GET("/diagnosis/v1/stuck/:project/alarm/list", d.ListStuckAlarmInfo())
	go func() {
		r.Run(":8080")
	}()

	recorder := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "http://localhost:8080/diagnosis/v1/stuck/PUS3/alarm/list?start_time=1715238000000&end_time=1715324400000&page=1&size=10&device_id=PS-NIO-3285ff15-7f564f27", nil)
	r.ServeHTTP(recorder, req)
	if recorder.Code != http.StatusOK {
		t.Fatalf("Expected to get status %d but instead got %d\n", http.StatusOK, recorder.Code)
	}
	responseBody := recorder.Body.String()
	t.Log(responseBody)
}

func TestDiagnosis_ListStuckAlarmDistribution(t *testing.T) {
	d := NewDiagnosisHandler(w, cfg)
	r := gin.Default()
	r.GET("/diagnosis/v1/stuck/:project/alarm/:alarm_id", d.ListStuckAlarmDistribution())
	go func() {
		r.Run(":8080")
	}()
	recorder := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "http://localhost:8080/diagnosis/v1/stuck/PUS3/alarm/699900?start_time=1715186927000&end_time=1715618927000", nil)
	r.ServeHTTP(recorder, req)
	if recorder.Code != http.StatusOK {
		t.Fatalf("Expected to get status %d but instead got %d\n", http.StatusOK, recorder.Code)
	}
	responseBody := recorder.Body.String()
	t.Log(responseBody)
}
