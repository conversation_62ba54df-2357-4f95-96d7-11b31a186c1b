package exec

import (
	"bytes"
	"encoding/csv"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"math/rand"
	"mime/multipart"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/avast/retry-go"
	"github.com/gin-gonic/gin"
	"github.com/gomodule/redigo/redis"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/rs/xid"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	udao "git.nevint.com/golang-libs/common-utils/dao"
	um "git.nevint.com/golang-libs/common-utils/model"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	"git.nevint.com/welkin2/welkin-backend/domain/activity"
	domain_device "git.nevint.com/welkin2/welkin-backend/domain/device"
	"git.nevint.com/welkin2/welkin-backend/domain/psos"
	"git.nevint.com/welkin2/welkin-backend/domain/psos_scheduler"
	"git.nevint.com/welkin2/welkin-backend/domain_service"
	"git.nevint.com/welkin2/welkin-backend/domain_service/device_simulation"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	mongo_model "git.nevint.com/welkin2/welkin-backend/model/mongo"
	"git.nevint.com/welkin2/welkin-backend/service"
	"git.nevint.com/welkin2/welkin-backend/util"
)

var (
	fttMutex         = sync.RWMutex{}
	algorithmIntMap  = make(map[string]int)
	algorithmFTTMap  = make(map[string]map[string]bool)
	projectIgnoreAlg = map[string]map[int]struct{}{
		umw.PowerSwap2: {
			17: {},
		},
		umw.PUS3: {
			3:  {},
			4:  {},
			5:  {},
			7:  {},
			8:  {},
			17: {},
		},
	}
)

func init() {
	for k, v := range umw.IntAlgorithmMap {
		algorithmIntMap[v] = k
		for project, mp := range model.AiAlgorithmIdMap {
			if _, ok := mp[k]; !ok {
				if model.NoPfsAlgorithms[project] == nil {
					model.NoPfsAlgorithms[project] = make(map[int]struct{})
				}
				model.NoPfsAlgorithms[project][k] = struct{}{}
			}
		}

	}
	algorithmIntMap[model.BSAService] = 6
	algorithmIntMap[model.BSAVehicle] = 6
	algorithmIntMap[model.RSDSOpen] = 16
	algorithmIntMap[model.RSDSClose] = 16
	delete(model.NoPfsAlgorithms[umw.PowerSwap2], 3)
}

type Algorithm interface {
	UploadDeviceRecords() gin.HandlerFunc
	AddNewPublishVersion() gin.HandlerFunc
	UpdatePublishVersion() gin.HandlerFunc
	GetAlgorithmData() gin.HandlerFunc
	UpdateAlgorithmData() gin.HandlerFunc
	GetAlgorithmNames() gin.HandlerFunc
	AddAlgorithmTestReport() gin.HandlerFunc
	GetPublishTimestamps() gin.HandlerFunc
	GetDataVersioningData() gin.HandlerFunc
	AddNewDataset(area string) gin.HandlerFunc
	ConfirmDataset() gin.HandlerFunc
	AddQMDatasetInfo(area string) gin.HandlerFunc
	DeleteDVFile(area string) gin.HandlerFunc
	UpdatePieInfo() gin.HandlerFunc
	DeletePieInfo() gin.HandlerFunc
	GetAlgorithmVisualData() gin.HandlerFunc
	GetSuccessRateAll() gin.HandlerFunc
	GetSuccessRateTrend() gin.HandlerFunc
	GetSuccessRateLowest() gin.HandlerFunc
	GetVehicleBattery() gin.HandlerFunc
	UpdateFTTList() gin.HandlerFunc
	GetAlgorithmFTT() gin.HandlerFunc
	GetCountAlgorithmFTT() gin.HandlerFunc
	GetAecStatus() gin.HandlerFunc
	LPRDataReport() gin.HandlerFunc
	GetLPRDataReport() gin.HandlerFunc
	GetCMSOrderInfo() gin.HandlerFunc
	StoreCMSResult() gin.HandlerFunc
	ListCMSData() gin.HandlerFunc
	ListEPSData() gin.HandlerFunc
	StorePsosResult() gin.HandlerFunc
	ListPsosTasks() gin.HandlerFunc
	QueryPsosTaskName() gin.HandlerFunc
	QueryPsosConfigId() gin.HandlerFunc
	ListPsosSimulations() gin.HandlerFunc
	GetPsosSimulationById() gin.HandlerFunc
	GetPsosTaskGraphData() gin.HandlerFunc
	StopPsosTask() gin.HandlerFunc
	PsosSimulationHeartbeat() gin.HandlerFunc
	PsosSimulationGenReport() gin.HandlerFunc
	SAPAAlarmList() gin.HandlerFunc
	DownloadSAPAAlarm() gin.HandlerFunc
	SAPAStatPanel() gin.HandlerFunc
	DownloadSAPAStat() gin.HandlerFunc
	BatteryEnum() gin.HandlerFunc
	CalculateBatteryConfig() gin.HandlerFunc
	PsosSingleConfig() gin.HandlerFunc
	PsosBatchConfig() gin.HandlerFunc
	PsosBatteryInfo() gin.HandlerFunc
	PsosServiceList() gin.HandlerFunc
	ListPsosConfigs() gin.HandlerFunc
	DeletePsosConfig() gin.HandlerFunc
	GetPsosDeviceParam() gin.HandlerFunc
	NewTaskByConfig() gin.HandlerFunc
	GetPsosConfig() gin.HandlerFunc
	PsosWatchDog() gin.HandlerFunc
	PsosRunAllDevices() gin.HandlerFunc
	CalculateActivityStats() gin.HandlerFunc
	ListActivityStats() gin.HandlerFunc
	ListActivityDevices() gin.HandlerFunc
	ListDailyReport() gin.HandlerFunc
}

type alg struct {
	watcher    client.Watcher
	oss        service.OSS
	backendUrl string
	config     struct {
		PowerSwap2 ucfg.AlgConfigDetail
		PUS3       ucfg.AlgConfigDetail
	}
	fms            service.FMS
	logger         *zap.SugaredLogger
	promCollectors map[string]prometheus.Collector
}

type algorithmVisualDataMap struct {
	sync.Mutex
	mp map[string]*umw.AlgorithmVisualData
}

func NewAlgorithmHandler(watcher client.Watcher, appId string, ossCfg ucfg.OSSConfig, conf *ucfg.Config) Algorithm {
	cfg := conf.Welkin
	a := alg{
		watcher: watcher,
		oss: service.OSS{
			URL:    ossCfg.PowUrl,
			NMP:    ossCfg.NMPUrl,
			AiURL:  ossCfg.AiUrl,
			AppId:  appId,
			Logger: log.Logger.Named("OSS"),
		},
		backendUrl: cfg.BackendUrl,
		config: struct {
			PowerSwap2 ucfg.AlgConfigDetail
			PUS3       ucfg.AlgConfigDetail
		}{cfg.Algorithm.PowerSwap2, cfg.Algorithm.PUS3},
		logger: log.Logger.Named(model.ALGORITHM),
		fms: service.FMS{
			URL:          conf.FMS.Url,
			AppId:        conf.Sentry.AppId,
			AppSecret:    conf.Sentry.AppSecret,
			ClientId:     conf.FMS.ClientId,
			ClientSecret: conf.FMS.ClientSecret,
			PriBucketKey: conf.FMS.PriBucketKey,
			PubBucketKey: conf.FMS.PubBucketKey,
			Header: map[string]string{
				"Content-Type": "application/json;charset=UTF-8",
			},
			Logger: log.Logger.Named("FMS"),
		},
	}
	if err := getFTTMap(a.watcher.Mongodb()); err != nil {
		a.logger.Panicf("failed to get ftt map, err : %v", err)
	}
	return &a
}

func getFTTMap(mc *client.MongoClient) error {
	// 获取FTT算法列表
	var fttList []umw.MongoFTTList
	byteData, err := mc.NewMongoEntry().ListAll(umw.Algorithm, umw.FTTList, client.Ordered{})
	if err != nil {
		return err
	}
	if err = json.Unmarshal(byteData, &fttList); err != nil {
		return err
	}
	for _, algFtt := range fttList {
		if algorithmFTTMap[algFtt.Project] == nil {
			algorithmFTTMap[algFtt.Project] = make(map[string]bool)
		}
		algorithmFTTMap[algFtt.Project][algFtt.AlgorithmName] = algFtt.IsFTT
	}
	return nil
}

// UploadDeviceRecords is designed for AI platform to record ses.
// Ref: https://git.nevint.com/PERD/Cloud/backend/welkin2/welkin-backend/-/issues/1
func (a *alg) UploadDeviceRecords() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			requestData model.DeviceSESData
			response    um.Base
		)
		if err := c.ShouldBindJSON(&requestData.MongoSESRecords); err != nil {
			log.CtxLog(c).Errorf("request body format is incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		if err := a.watcher.Mongodb().NewMongoEntry().InsertManySesRecords(requestData.MongoSESRecords); err != nil {
			log.CtxLog(c).Errorf("failed to insert or update ses records, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		log.CtxLog(c).Infof("succeeded to insert or update ses records")
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (a *alg) AddNewPublishVersion() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.VersionBase
			response um.Base
		)
		log.CtxLog(c).Named("AddNewPublishVersion")
		project := c.Param("project")
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithMessageForGin(c, &response, err.Error(), -1, http.StatusBadRequest)
			return
		}

		err := a.watcher.Mongodb().InsertPublishVersion(umw.Algorithm, umw.PublishVersion, umw.PublishVersionInfo, project, request)
		if err != nil {
			um.SuccessWithErrCode(c, &response, err.Error(), 3)
			return
		}
		um.SuccessWithMessageForGin(c, &response, "succeeded to add new publish version", http.StatusOK)
	}
}

func (a *alg) UpdatePublishVersion() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.VersionInfo
			response um.Base
		)
		log.CtxLog(c).Named("UpdatePublishVersion")
		project := c.Param("project")
		publishVersionIdRaw := c.Param("publish_version_id")
		publishVersionId, err := strconv.Atoi(publishVersionIdRaw)
		if err != nil {
			log.CtxLog(c).Errorf("`publish_version_id` should be int type!")
			um.FailWithMessageForGin(c, &response, "`publish_version_id` should be int type!", 1, http.StatusBadRequest)
			return
		}
		if err = c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithMessageForGin(c, &response, fmt.Sprintf("parse request body, err: %v", err.Error()), -1, http.StatusBadRequest)
			return
		}
		request.PublishVersionId = int64(publishVersionId)
		err = a.watcher.Mongodb().UpdatePublishVersion(umw.Algorithm, umw.PublishVersion, umw.PublishVersionInfo, project, request)
		if err != nil {
			if strings.Contains(err.Error(), model.ErrDuplicateAlgorithm) {
				errMessage := strings.Split(err.Error(), ":")
				message := fmt.Sprintf("Fail to add duplicate algorithms: %s", errMessage[1])
				if len(errMessage[2]) > 0 {
					message = fmt.Sprintf("%s. Succeed to add algorithms: %s", message, errMessage[2])
				}
				um.SuccessWithErrCode(c, &response, message, 4)
				return
			}
			um.SuccessWithErrCode(c, &response, err.Error(), 3)
			return
		}
		um.SuccessWithMessageForGin(c, &response, "succeeded to update publish version", http.StatusOK)
	}
}

func (a *alg) GetAlgorithmData() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.CommonUriParam
			response model.VersionAlgorithmDataResponse
		)
		log.CtxLog(c).Named("GetAlgorithmData")
		project := c.Param("project")
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithMessageForGin(c, &response, fmt.Sprintf("uri parameters are incorrect: %s", err.Error()), 1, http.StatusBadRequest)
			return
		}
		if request.Page == 0 {
			request.Page = 1
		}
		if request.Size == 0 {
			request.Size = 10
		}

		dbName, collectionPublishVersion, collectionVersionInfo := umw.Algorithm, umw.PublishVersion, umw.PublishVersionInfo
		resData, total, err := a.watcher.Mongodb().GetAlgorithmData(dbName, collectionPublishVersion, collectionVersionInfo, project, request)
		if err != nil {
			log.CtxLog(c).Errorf(fmt.Sprintf("get algorithm data error: %s, project: %s", err.Error(), project))
			um.FailWithMessageForGin(c, &response, fmt.Sprintf("get algorithm data error: %s, project: %s", err.Error(), project), 2, http.StatusInternalServerError)
			return
		}
		log.CtxLog(c).Infof("succeeded to get algorithm data, database: %s, collection version: %s, collection version info: %s, page %d, size: %d", dbName, collectionPublishVersion, collectionVersionInfo, request.Page, request.Size)
		response.Data = resData
		response.Total = int(total)
		um.SuccessWithMessageForGin(c, &response, "succeeded to get algorithm data", http.StatusOK)
		return
	}
}

func (a *alg) UpdateAlgorithmData() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.VersionUpdateAlgorithmParams
			response um.Base
		)
		log.CtxLog(c).Named("UpdateAlgorithmData")
		project := c.Param("project")
		if project != umw.PowerSwap && project != umw.PowerSwap2 && project != umw.PUS3 {
			log.CtxLog(c).Errorf(fmt.Sprintf("`project` is invalid: %s", project))
			um.FailWithMessageForGin(c, &response, fmt.Sprintf("`project` is invalid: %s", project), 1, http.StatusBadRequest)
			return
		}
		publishVersionIdRaw := c.Param("publish_version_id")
		algorithmIdRaw := c.Param("algorithm_id")
		publishVersionId, err := strconv.Atoi(publishVersionIdRaw)
		if err != nil {
			log.CtxLog(c).Errorf("`publish_version_id` should be int type!")
			um.FailWithMessageForGin(c, &response, "`publish_version_id` should be int type!", 1, http.StatusBadRequest)
			return
		}
		algorithmId, err := strconv.Atoi(algorithmIdRaw)
		if err != nil {
			log.CtxLog(c).Errorf("`algorithm_id` should be int type!")
			um.FailWithMessageForGin(c, &response, "`algorithm_id` should be int type!", 1, http.StatusBadRequest)
			return
		}
		if err = c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithMessageForGin(c, &response, fmt.Sprintf("parse request body, err: %v", err.Error()), -1, http.StatusBadRequest)
			return
		}

		request.AlgorithmId = int64(algorithmId)
		err = a.watcher.Mongodb().UpdateAlgorithmData(umw.Algorithm, umw.PublishVersion, umw.PublishVersionInfo, int64(publishVersionId), request)
		if err != nil {
			um.SuccessWithErrCode(c, &response, err.Error(), 3)
			return
		}
		um.SuccessWithMessageForGin(c, &response, "succeeded to update algorithm data", http.StatusOK)
	}
}

func (a *alg) GetAlgorithmNames() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.VersionAlgorithmNameResponse
		log.CtxLog(c).Named("GetAlgorithmNames")
		project := c.Query("project")
		top := c.Query("top")

		dbName, collectionPublishVersion, collectionVersionInfo := umw.Algorithm, umw.PublishVersion, umw.PublishVersionInfo
		resData, err := a.watcher.Mongodb().GetAlgorithmNames(dbName, collectionPublishVersion, collectionVersionInfo, project)
		if err != nil {
			log.CtxLog(c).Errorf(fmt.Sprintf("get algorithm names error: %s, project: %s", err.Error(), project))
			um.FailWithMessageForGin(c, &response, fmt.Sprintf("get algorithm names error: %s, project: %s", err.Error(), project), 2, http.StatusInternalServerError)
			return
		}
		log.CtxLog(c).Infof("succeeded to get algorithm names, database: %s, collection version: %s, collection version info: %s", dbName, collectionPublishVersion, collectionVersionInfo)
		// 若需要置顶算法，则将算法表头中的这几个算法放到前面
		response.Data = sortAlgorithmNames(resData, top)
		um.SuccessWithMessageForGin(c, &response, "succeeded to get algorithm data", http.StatusOK)
		return
	}
}

func (a *alg) AddAlgorithmTestReport() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.VersionAddTestReportParams
			response um.Base
		)
		log.CtxLog(c).Named("AddTestReport")
		project := c.Param("project")
		if project != umw.PowerSwap && project != umw.PowerSwap2 && project != umw.PUS3 {
			log.CtxLog(c).Errorf(fmt.Sprintf("`project` is invalid: %s", project))
			um.FailWithMessageForGin(c, &response, fmt.Sprintf("`project` is invalid: %s", project), 1, http.StatusBadRequest)
			return
		}
		algorithmIdRaw := c.Param("algorithm_id")
		algorithmId, err := strconv.Atoi(algorithmIdRaw)
		if err != nil {
			log.CtxLog(c).Errorf("`algorithm_id` should be int type!")
			um.FailWithMessageForGin(c, &response, "`algorithm_id` should be int type!", 1, http.StatusBadRequest)
			return
		}
		if err = c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithMessageForGin(c, &response, fmt.Sprintf("parse request body, err: %v", err.Error()), -1, http.StatusBadRequest)
			return
		}
		request.AlgorithmId = int64(algorithmId)

		err = a.watcher.Mongodb().AddAlgorithmTestReport(umw.Algorithm, umw.PublishVersionInfo, request)
		if err != nil {
			um.SuccessWithErrCode(c, &response, err.Error(), 3)
			return
		}
		um.SuccessWithMessageForGin(c, &response, "succeeded to add algorithm test report", http.StatusOK)
	}
}

func (a *alg) GetPublishTimestamps() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.VersionPublishTsResponse
		log.CtxLog(c).Named("GetPublishTimestamps")

		dbName, collection := umw.Algorithm, umw.PublishVersion
		resData, err := a.watcher.Mongodb().GetPublishTimestamps(dbName, collection)
		if err != nil {
			log.CtxLog(c).Errorf(fmt.Sprintf("get publish timestamps error: %s", err.Error()))
			um.FailWithMessageForGin(c, &response, fmt.Sprintf("get publish timestamps error: %s", err.Error()), 2, http.StatusInternalServerError)
			return
		}
		log.CtxLog(c).Infof("succeeded to get publish timestamps, database: %s, collection: %s", dbName, collection)
		response.Data = resData
		um.SuccessWithMessageForGin(c, &response, "succeeded to get publish timestamps", http.StatusOK)
		return
	}
}

func (a *alg) GetDataVersioningData() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.DataVersioningParams
			response model.DataVersioningResponse
		)
		log.CtxLog(c).Named("GetDataVersioningData")
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithMessageForGin(c, &response, fmt.Sprintf("uri parameters are incorrect: %s", err.Error()), 1, http.StatusBadRequest)
			return
		}
		if request.Page == 0 {
			request.Page = 1
		}
		if request.Size == 0 {
			request.Size = 10
		}

		dbName, collectionPublishVersion, collectionVersionInfo := umw.Algorithm, umw.PublishVersion, umw.PublishVersionInfo
		resData, total, err := a.watcher.Mongodb().GetDataVersioningData(dbName, collectionPublishVersion, collectionVersionInfo, request)
		if err != nil {
			log.CtxLog(c).Errorf(fmt.Sprintf("get data versioning data error: %s", err.Error()))
			um.FailWithMessageForGin(c, &response, fmt.Sprintf("get data versioning data error: %s", err.Error()), 2, http.StatusInternalServerError)
			return
		}
		for i, data := range resData {
			nowTs := time.Now().UnixMilli()
			var totalSize int64
			if data.PublishTs > nowTs {
				resData[i].IfCanEdit = true
			}
			//if len(data.FileInfo) == 0{
			//	resData[i].CreateTs = 0
			//}
			for _, file := range data.FileInfo {
				totalSize += file.FileSize
			}
			resData[i].TotalSize = totalSize
		}
		response.Data = resData
		response.Total = total
		log.CtxLog(c).Infof("succeeded to get data versioning data, database: %s, collection: %s, page %d, size: %d, %s", dbName, collectionVersionInfo, request.Page, request.Size, collectionPublishVersion)
		um.SuccessWithMessageForGin(c, &response, "succeeded to get data versioning data", http.StatusOK)
	}
}

func sortAlgorithmNames(algorithmNames []string, top string) []string {
	if len(top) != 0 {
		topMap := make(map[string]struct{})
		topList := strings.Split(top, ",")
		for _, topAlgorithm := range topList {
			topMap[topAlgorithm] = struct{}{}
		}
		topAlg := make([]string, 0)
		backAlg := make([]string, 0)
		for _, algorithmName := range algorithmNames {
			if _, ok := topMap[algorithmName]; !ok {
				backAlg = append(backAlg, algorithmName)
			} else {
				topAlg = append(topAlg, algorithmName)
			}
		}
		return append(topAlg, backAlg...)
	} else {
		return algorithmNames
	}
}
func (a *alg) AddNewDataset(area string) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			mongodbRequest model.AddNewDatasetMongoRequest
			response       model.AddNewDatasetResponse
		)
		log.CtxLog(c).Named("AddNewDataset")
		if area == um.Europe {
			log.CtxLog(c).Warnf("area: %s, add new dataset not supported", area)
			um.FailWithNotFound(c, &response, "area: Europe, add new dataset not supported")
			return
		}
		id, ok := c.GetPostForm("id")
		if !ok {
			log.CtxLog(c).Errorf("id postForm parameters are incorrect")
			um.FailWithMessageForGin(c, &response, "id postForm  parameters are incorrect", 1, http.StatusBadRequest)
			return
		}
		publishTsStr, ok := c.GetPostForm("publish_ts")
		if !ok {
			log.CtxLog(c).Errorf("publish_ts postForm parameters are incorrect")
			um.FailWithMessageForGin(c, &response, "publish_ts postForm  parameters are incorrect", 1, http.StatusBadRequest)
			return
		}
		publishTs, err := strconv.Atoi(publishTsStr)
		if err != nil {
			log.CtxLog(c).Errorf("publishTs turn string to int is incorrect")
			um.FailWithMessageForGin(c, &response, "id turn string to int is incorrect", 1, http.StatusBadRequest)
			return
		}
		if time.Now().UnixMilli() > int64(publishTs) {
			log.CtxLog(c).Errorf("unable to upload file after publish time")
			um.FailWithMessageForGin(c, &response, "unable to upload file after publish time", 1, http.StatusBadRequest)
			return
		}
		intId, err := strconv.Atoi(id)
		if err != nil {
			log.CtxLog(c).Errorf("id turn string to int is incorrect")
			um.FailWithMessageForGin(c, &response, "id turn string to int is incorrect", 1, http.StatusBadRequest)
			return
		}
		size, err := a.watcher.Mongodb().CheckIfQM(intId)
		if err != nil {
			log.CtxLog(c).Errorf("id check if QM is incorrect")
			um.FailWithMessageForGin(c, &response, "id check if QM is incorrect", 1, http.StatusBadRequest)
			return
		}
		algorithmName, ok := c.GetPostForm("algorithm_name")
		if !ok {
			log.CtxLog(c).Errorf("algorithm_name postForm parameters are incorrect")
			um.FailWithMessageForGin(c, &response, "algorithm_name postForm parameters are incorrect", 1, http.StatusBadRequest)
			return
		}
		fileHeader, err := c.FormFile("file")
		if err != nil {
			log.CtxLog(c).Errorf("file parameters are incorrect: %v", err)
			um.FailWithMessageForGin(c, &response, err.Error(), 1, http.StatusBadRequest)
			return
		}
		file, err := fileHeader.Open()
		if err != nil {
			log.CtxLog(c).Errorf("file parameters are incorrect: %v", err)
			um.FailWithMessageForGin(c, &response, err.Error(), 1, http.StatusBadRequest)
			return
		}
		defer file.Close()
		mongodbRequest.FileName = fileHeader.Filename
		mongodbRequest.FileSize = fileHeader.Size
		createTs := time.Now().UnixMilli()
		createDate := time.Unix(createTs/1000, 0).Format("2006-01-02")

		mongodbRequest.FileUrl, err = service.GetFMS().UploadMultipart(c, fileHeader, fmt.Sprintf("/algorithm/dataset/%s/%s/", algorithmName, createDate))
		if err != nil {
			log.CtxLog(c).Errorf("fail to upload multipart file to fms: %v", err)
			um.FailWithMessageForGin(c, &response, err.Error(), 3, http.StatusBadRequest)
			return
		}
		err = a.watcher.Mongodb().InsertAlgorithmDataset(mongodbRequest, int64(intId), createTs, size)
		if err != nil {
			log.CtxLog(c).Errorf("insertAlgorithmDataset is incorrect: %v", err)
			um.FailWithMessageForGin(c, &response, err.Error(), 3, http.StatusBadRequest)
			return
		}
		log.CtxLog(c).Infof("succeeded to add data versioning dataset")
		um.SuccessWithMessageForGin(c, &response, "succeeded to add data versioning dataset", http.StatusOK)
	}
}

func (a *alg) ConfirmDataset() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.ConfirmDatasetRequest
			response um.Base
		)
		log.CtxLog(c).Named("ConfirmDataset")
		err := c.BindJSON(&request)
		if err != nil {
			log.CtxLog(c).Errorf("id parameters is incorrect")
			um.FailWithMessageForGin(c, &response, fmt.Sprintf("id parameters is incorrect"), 1, http.StatusBadRequest)
			return
		}
		err = a.watcher.Mongodb().ChangeAlgorithmHasTestReportStatus(request.Id)
		if err != nil {
			log.CtxLog(c).Errorf("update status is incorrect")
			um.FailWithMessageForGin(c, &response, fmt.Sprintf("update status is incorrect"), 3, http.StatusBadRequest)
			return
		}
		log.CtxLog(c).Infof("succeeded to update has_test_report status, database")
		um.SuccessWithMessageForGin(c, &response, "succeeded to update has_test_report status", http.StatusOK)
	}
}

func (a *alg) AddQMDatasetInfo(area string) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			mongodbRequest model.AddNewDatasetMongoRequest
			response       um.Base
		)
		if area == um.Europe {
			log.CtxLog(c).Warnf("area: %s, add qm dataset info not supported", area)
			um.FailWithNotFound(c, &response, "area: Europe, add qm dataset info not supported")
			return
		}
		algorithmName, ok := c.GetPostForm("algorithm_name")
		if !ok {
			log.CtxLog(c).Errorf("algorithm_name postForm parameters are incorrect")
			um.FailWithMessageForGin(c, &response, "algorithm_name postForm  parameters are incorrect", 1, http.StatusBadRequest)
			return
		}
		algorithmVersion, ok := c.GetPostForm("algorithm_version")
		if !ok {
			log.CtxLog(c).Errorf("algorithm_version postForm parameters are incorrect")
			um.FailWithMessageForGin(c, &response, "algorithm_version postForm  parameters are incorrect", 1, http.StatusBadRequest)
			return
		}
		publishTsStr, ok := c.GetPostForm("publish_ts")
		if !ok {
			log.CtxLog(c).Errorf("publish_ts postForm parameters are incorrect")
			um.FailWithMessageForGin(c, &response, "publish_ts postForm  parameters are incorrect", 1, http.StatusBadRequest)
			return
		}
		publishTsInt, err := strconv.Atoi(publishTsStr)
		if err != nil {
			log.CtxLog(c).Errorf("publish_ts turn string to int is incorrect: %v", err)
			um.FailWithMessageForGin(c, &response, err.Error(), 1, http.StatusBadRequest)
			return
		}
		publishDate := time.Unix(int64(publishTsInt)/1000, 0).Format("2006-01-02")
		fileHeader, err := c.FormFile("file")
		if err != nil {
			log.CtxLog(c).Errorf("file parameters are incorrect: %v", err)
			um.FailWithMessageForGin(c, &response, err.Error(), 1, http.StatusBadRequest)
			return
		}
		mongodbRequest.FileName = fileHeader.Filename
		mongodbRequest.FileSize = fileHeader.Size

		mongodbRequest.FileUrl, err = service.GetFMS().UploadMultipart(c, fileHeader, fmt.Sprintf("/algorithm/dataset/%s/%s/", algorithmName, publishDate))
		if err != nil {
			log.CtxLog(c).Errorf("fail to upload multipart file to fms: %v", err)
			um.FailWithMessageForGin(c, &response, err.Error(), 3, http.StatusBadRequest)
			return
		}
		err = a.watcher.Mongodb().AddQMDataset(algorithmName, algorithmVersion, int64(publishTsInt), mongodbRequest)
		if err != nil {
			log.CtxLog(c).Errorf("file save in mongodb is incorrect: %v", err)
			um.FailWithMessageForGin(c, &response, err.Error(), 3, http.StatusBadRequest)
			return
		}
		log.CtxLog(c).Infof("succeeded to add QM Dataset")
		um.SuccessWithMessageForGin(c, &response, "succeeded to add QM Dataset", http.StatusOK)
	}
}

func (a *alg) DeleteDVFile(area string) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.DeleteDVFileRequest
			response um.Base
		)
		if area == um.Europe {
			log.CtxLog(c).Warnf("area: %s, delete dv file not supported", area)
			um.FailWithNotFound(c, &response, "area: Europe, delete dv file not supported")
			return
		}
		err := c.BindJSON(&request)
		if err != nil {
			log.CtxLog(c).Errorf("file_url parameters are incorrect: %v", err)
			um.FailWithMessageForGin(c, &response, err.Error(), 1, http.StatusBadRequest)
			return
		}
		if time.Now().UnixMilli() > request.PublishTs {
			log.CtxLog(c).Errorf("unable to delete file after publish time")
			um.FailWithMessageForGin(c, &response, fmt.Sprintf("unable to delete file after publish time: %d", request.PublishTs), 1, http.StatusBadRequest)
			return
		}
		_, err = a.watcher.Mongodb().DeleteDVFile(request)
		if err != nil {
			log.CtxLog(c).Errorf("mongodb delete file is incorrect: %v", err)
			um.FailWithMessageForGin(c, &response, err.Error(), 3, http.StatusBadRequest)
			return
		}
		err = service.GetFMS().DeleteFile(c, []string{request.FileUrl})
		if err != nil {
			log.CtxLog(c).Errorf("fms delete file is incorrect: %v", err)
			um.FailWithMessageForGin(c, &response, err.Error(), 3, http.StatusBadRequest)
			return
		}
		log.CtxLog(c).Infof("succeeded to delete DV File")
		um.SuccessWithMessageForGin(c, &response, "success", http.StatusOK)
	}
}

func (a *alg) UpdatePieInfo() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  umw.PieInfoData
			response um.Base
		)
		log.CtxLog(c).Named("UpdatePieInfo")
		project := c.Param("project")
		if project != umw.PUS3 && project != umw.PowerSwap && project != umw.PowerSwap2 {
			log.CtxLog(c).Errorf("invalid project: %s", project)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("invalid project: %s", project))
			return
		}
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %s", err.Error())
			um.FailWithBadRequest(c, &response, fmt.Sprintf("parse request body, err: %s", err.Error()))
			return
		}

		if err := a.watcher.Mongodb().InsertOrUpdatePieInfo(umw.Algorithm, fmt.Sprintf("%s-%s", umw.PieInfo, ucmd.RenameProjectDB(project)), request); err != nil {
			log.CtxLog(c).Errorf("update pie info err: %s", err.Error())
			um.FailWithInternalServerError(c, &response, fmt.Sprintf("update pie info err: %s", err.Error()))
			return
		}
		log.CtxLog(c).Infof("succeed to insert or update pie info")
		um.SuccessWithMessageForGin(c, &response, "succeed to insert or update pie info", http.StatusOK)
	}
}

func (a *alg) DeletePieInfo() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response um.Base
		log.CtxLog(c).Named("DeletePieInfo")
		project := c.Param("project")
		if project != umw.PUS3 && project != umw.PowerSwap && project != umw.PowerSwap2 {
			log.CtxLog(c).Errorf("invalid project: %s", project)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("invalid project: %s", project))
			return
		}
		id, err := strconv.Atoi(c.Param("pie_id"))
		if err != nil {
			log.CtxLog(c).Errorf("`pie_id` should be int64, err: %s", err.Error())
			um.FailWithBadRequest(c, &response, fmt.Sprintf("`pie_id` should be int64, err: %s", err.Error()))
			return
		}

		if err = a.watcher.Mongodb().DeletePieInfo(umw.Algorithm, fmt.Sprintf("%s-%s", umw.PieInfo, ucmd.RenameProjectDB(project)), int64(id)); err != nil {
			log.CtxLog(c).Errorf("delete pie info err: %s", err.Error())
			um.FailWithInternalServerError(c, &response, fmt.Sprintf("delete pie info err: %s", err.Error()))
			return
		}
		log.CtxLog(c).Infof("succeed to delete pie info")
		um.SuccessWithMessageForGin(c, &response, "succeed to delete pie info", http.StatusOK)
	}
}

func (a *alg) GetAlgorithmVisualData() gin.HandlerFunc {
	return func(c *gin.Context) {
		log.CtxLog(c).Named("GetAlgorithmVisualData")
		response := struct {
			model.Response
			ExpectSuccessRate float64 `json:"expect_success_rate"`
		}{}
		project := c.Param("project")
		if project != umw.PUS3 && project != umw.PowerSwap && project != umw.PowerSwap2 {
			log.CtxLog(c).Errorf("invalid project: %s", project)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("invalid project: %s", project))
			return
		}
		responseMap := &algorithmVisualDataMap{mp: make(map[string]*umw.AlgorithmVisualData)}
		request := struct {
			Update bool  `form:"update"`
			FTT    int   `form:"ftt"`
			Day    int64 `form:"day"`
		}{}
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("invalid query params, err: %s", err.Error())
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		var startDay time.Time
		if request.Day == 0 {
			startDay = util.TimeDay(time.Now().AddDate(0, 0, -1).UnixMilli())
		} else {
			startDay = time.UnixMilli(request.Day)
		}

		var renew bool
		var results []umw.AlgorithmVisualData
		if !request.Update {
			// 昨天的信息，先查看是否有缓存
			rawData, err := a.watcher.Mongodb().NewMongoEntry(bson.D{
				bson.E{Key: "date", Value: startDay},
				bson.E{Key: "project", Value: project}}).GetOne(umw.Algorithm, umw.VisualData)
			if err != nil {
				log.CtxLog(c).Errorf("failed to get algorithm visual data, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			var record umw.MongoAlgorithmVisualData
			if rawData != nil {
				if err = bson.Unmarshal(rawData, &record); err != nil {
					log.CtxLog(c).Errorf("failed to unmarshal algorithm visual data, err: %v", err)
					um.FailWithInternalServerError(c, &response, err.Error())
					return
				}
				results = record.Data
			} else {
				renew = true
			}
		} else {
			renew = true
		}
		// 若未计算过昨日数据，或指定需要重新计算昨日数据，则进行查表计算
		if renew {
			pfsMap, pErr := a.getParametricFormulaSwitchMap(c, project, startDay.UnixMilli(), true)
			if pErr != nil {
				log.CtxLog(c).Errorf("fail to get pfs: %v, project: %s, day: %d", pErr, project, startDay.UnixMilli())
				return
			}
			opts := model.AlgorithmSuccessRateRequest{
				StartTime: startDay.UnixMilli(),
			}

			g := ucmd.NewErrGroup(c)
			// 获取数据上传量信息
			g.GoRecover(func() error {
				return a.getAlgorithmCount(c, project, responseMap, opts)
			})
			// 获取算法昨日到站成功率
			g.GoRecover(func() error {
				return a.getAlgorithmSuccessRate(c, project, responseMap, opts, pfsMap)
			})
			// 获取AEC有效运行率和算法正常运行率
			g.GoRecover(func() error {
				return a.getAlgorithmAecData(c, project, responseMap, opts)
			})
			// 获取算法的版本信息
			g.GoRecover(func() error {
				return a.getAlgorithmVersion(c, project, responseMap)
			})
			// 获取算法FTT信息，即每个算法是否是FTT算法
			g.GoRecover(func() error {
				return a.getAlgorithmFTT(project, responseMap)
			})
			// 获取站点总数
			deviceMap := make(map[int]map[string]struct{})
			g.GoRecover(func() error {
				return a.getDeviceTotal(c, project, responseMap, opts, deviceMap)
			})

			if err := g.Wait(); err != nil {
				log.CtxLog(c).Errorf("goroutine err: %s", err.Error())
				um.FailWithInternalServerError(c, &response, fmt.Sprintf("goroutine err: %s", err.Error()))
				return
			}
			fttData := make([]umw.AlgorithmVisualData, 0)
			noFttData := make([]umw.AlgorithmVisualData, 0)
			fttMutex.RLock()
			for k, v := range responseMap.mp {
				algId := algorithmIntMap[k]
				if algId == 0 {
					continue
				}
				// 去除当前设备类型不支持的算法
				if projectIgnoreAlg[project] != nil {
					if _, ok := projectIgnoreAlg[project][algId]; ok {
						continue
					}
				}
				if !client.IsNoPfsAlgorithm(project, algId) {
					v.Pfs = true
				}
				if v.Pfs {
					for d := range pfsMap[algId] {
						if _, ok := deviceMap[algId][d]; ok {
							v.PfsDevices++
						}
					}
				}
				// RSDV_VLSV改名为RSDV_ViP
				if v.AlgorithmId == 12 {
					v.Name = model.RSDVViP
				}
				if algorithmFTTMap[project][k] {
					fttData = append(fttData, *v)
				} else {
					noFttData = append(noFttData, *v)
				}
			}
			fttMutex.RUnlock()
			sort.Slice(fttData, func(i, j int) bool {
				return (fttData[i].SuccessRate > fttData[j].SuccessRate) || (fttData[i].SuccessRate == fttData[j].SuccessRate && fttData[i].AlgorithmId < fttData[j].AlgorithmId)
			})
			sort.Slice(noFttData, func(i, j int) bool {
				return (noFttData[i].SuccessRate > noFttData[j].SuccessRate) || (noFttData[i].SuccessRate == noFttData[j].SuccessRate && noFttData[i].AlgorithmId < noFttData[j].AlgorithmId)
			})
			results = append(fttData, noFttData...)
			err := a.watcher.Mongodb().NewMongoEntry(bson.D{{"date", startDay},
				{"project", project}}).ReplaceOne(umw.Algorithm, umw.VisualData, umw.MongoAlgorithmVisualData{
				Project: project,
				Day:     startDay.UnixMilli(),
				Data:    results,
				Date:    startDay,
			}, true, client.IndexOption{
				Name:        "expire_date",
				Fields:      bson.D{{"date", -1}},
				ExpiredTime: 180 * 24 * 3600,
			})
			if err != nil {
				// 如果插入失败，不重要，主要是用于缓存，正常返回数据
				log.CtxLog(c).Errorf("fail to insert algorithm visual data: %s", err.Error())
			}
		}
		// 筛选FTT算法
		idx := sort.Search(len(results), func(i int) bool {
			return !results[i].IsFTT
		})
		if request.FTT == 0 {
			response.Data = results[:idx]
		} else if request.FTT == 1 {
			response.Data = results[idx:]
		}
		if project == umw.PowerSwap2 {
			response.ExpectSuccessRate = a.config.PowerSwap2.ExpectFTTSuccessRate
		} else if project == umw.PUS3 {
			response.ExpectSuccessRate = a.config.PUS3.ExpectFTTSuccessRate
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) getDeviceTotal(c *gin.Context, project string, responseMap *algorithmVisualDataMap, opts model.AlgorithmSuccessRateRequest, deviceMap map[int]map[string]struct{}) error {
	g := ucmd.NewErrGroup(c, 5)
	mu := sync.Mutex{}
	for algId, algName := range umw.IntAlgorithmMap {
		algIdCopy, algNameCopy := algId, algName
		g.GoRecover(func() error {
			optsCopy := opts
			optsCopy.AlgorithmId = algIdCopy
			deviceTotal, devices, err := a.watcher.Mongodb().GetDeviceTotal(fmt.Sprintf("algorithm-daily-report-%s", strings.ToLower(project)), optsCopy)
			if err != nil {
				log.CtxLog(c).Errorf("GetDeviceTotal err: %s, algorithmId: %d", err.Error(), algIdCopy)
				return err
			}
			mu.Lock()
			deviceMap[algIdCopy] = devices
			mu.Unlock()
			for _, an := range util.SplitAlgorithm(algNameCopy) {
				responseMap.Lock()
				if responseMap.mp[an] == nil {
					responseMap.mp[an] = &umw.AlgorithmVisualData{}
				}
				responseMap.mp[an].AlgorithmId = algIdCopy
				responseMap.mp[an].Name = an
				responseMap.mp[an].TotalDevices = deviceTotal
				responseMap.Unlock()
			}
			return nil
		})
	}
	if err := g.Wait(); err != nil {
		return err
	}
	return nil
}

func (a *alg) getAlgorithmCount(c *gin.Context, project string, responseMap *algorithmVisualDataMap, opts model.AlgorithmSuccessRateRequest) error {
	start := time.Now()
	dbName := fmt.Sprintf("%s-%s", umw.ImageInfo, ucmd.RenameProjectDB(project))
	g := ucmd.NewErrGroup(c, 5)
	for algName, imgTypes := range umw.AlgorithmImageTypeListMap {
		if algName == "RSDV" {
			algName += "_PiP"
		}
		// 不在算法可视化的算法列表中
		if algName == "VBSL" || algName == "BBSA" {
			continue
		}
		algNameCopy, imgTypesCopy := algName, imgTypes

		g.GoRecover(func() error {
			imgTypeList := strings.Split(imgTypesCopy, ",")
			if len(imgTypeList) == 0 {
				return nil
			}
			// 一个算法可能对应多个image type
			algorithmCountInfo, err := a.watcher.Mongodb().GetAlgorithmCount(dbName, time.UnixMilli(opts.StartTime), imgTypeList)
			if err != nil {
				log.CtxLog(c).Errorf("GetAlgorithmCount err: %s", err.Error())
				return err
			}
			// 若没有数据，则不将0值写入返回数据
			if algorithmCountInfo.ImageCount == 0 && algorithmCountInfo.DeviceCount == 0 {
				return nil
			}
			for _, an := range util.SplitAlgorithm(algNameCopy) {
				responseMap.Lock()
				if responseMap.mp[an] == nil {
					responseMap.mp[an] = &umw.AlgorithmVisualData{}
				}
				responseMap.mp[an].AlgorithmId = algorithmIntMap[algNameCopy]
				responseMap.mp[an].Name = an
				responseMap.mp[an].ImageCount = algorithmCountInfo.ImageCount / (1024 * 1024)
				responseMap.mp[an].DeviceCount = algorithmCountInfo.DeviceCount
				responseMap.Unlock()
			}

			return nil
		})
	}
	if err := g.Wait(); err != nil {
		return err
	}
	responseMap.Lock()
	if responseMap.mp["RSDV_VLSV"] == nil {
		responseMap.mp["RSDV_VLSV"] = &umw.AlgorithmVisualData{}
	}
	responseMap.mp["RSDV_VLSV"].AlgorithmId = algorithmIntMap["RSDV_VLSV"]
	responseMap.mp["RSDV_VLSV"].Name = "RSDV_VLSV"
	if responseMap.mp["RSDV_PiP"] != nil {
		responseMap.mp["RSDV_VLSV"].ImageCount = responseMap.mp["RSDV_PiP"].ImageCount
		responseMap.mp["RSDV_VLSV"].DeviceCount = responseMap.mp["RSDV_PiP"].DeviceCount
	}
	responseMap.Unlock()
	log.CtxLog(c).Infof("succeed to get algorithm count, time: %v", time.Now().Sub(start))
	return nil
}

func (a *alg) getAlgorithmSuccessRate(c *gin.Context, project string, responseMap *algorithmVisualDataMap, opts model.AlgorithmSuccessRateRequest, pfsMap map[int]map[string]struct{}) error {
	start := time.Now()
	g := ucmd.NewErrGroup(c, 5)
	for algId, algName := range umw.IntAlgorithmMap {
		algIdCopy, algNameCopy := algId, algName
		g.GoRecover(func() error {
			optsCopy := opts
			optsCopy.AlgorithmId = algIdCopy
			total, successRate, err, extraData := a.watcher.Mongodb().GetAlgorithmSuccessRate(fmt.Sprintf("algorithm-daily-report-%s", strings.ToLower(project)), project, optsCopy, pfsMap)
			if err != nil {
				log.CtxLog(c).Errorf("GetAlgorithmSuccessCount err: %s, algorithmId: %d", err.Error(), algIdCopy)
				return err
			}
			if total == 0 {
				return nil
			}
			// BSA和RSDS算法分别拆分为两个算法
			for i, an := range util.SplitAlgorithm(algNameCopy) {
				responseMap.Lock()
				if responseMap.mp[an] == nil {
					responseMap.mp[an] = &umw.AlgorithmVisualData{}
				}
				responseMap.mp[an].AlgorithmId = algIdCopy
				responseMap.mp[an].Name = an
				if i == 0 {
					responseMap.mp[an].SuccessRate = successRate
				} else if i == 1 && len(extraData) > 0 && total > 0 {
					responseMap.mp[an].SuccessRate = float64(extraData[0].(int64)) / float64(total)
				}
				responseMap.Unlock()
			}
			return nil
		})
	}
	if err := g.Wait(); err != nil {
		return err
	}
	log.CtxLog(c).Infof("succeed to get algorithm success rate, time: %v", time.Now().Sub(start))
	return nil
}

func (a *alg) getAlgorithmAecData(c *gin.Context, project string, responseMap *algorithmVisualDataMap, opts model.AlgorithmSuccessRateRequest) error {
	start := time.Now()
	var collectionName string
	for key := range util.ParseTimeRangeList(opts.StartTime, opts.StartTime) {
		collectionName = key
	}
	total, err := a.watcher.Mongodb().GetServiceOrderCount(fmt.Sprintf("serviceinfo-%s", ucmd.RenameProjectDB(project)), collectionName, opts.StartTime)
	if err != nil {
		log.CtxLog(c).Errorf("GetServiceOrderCount err: %s", err.Error())
		return err
	}
	if total == 0 {
		return nil
	}
	g := ucmd.NewErrGroup(c, 5)
	for algId, algName := range umw.IntAlgorithmMap {
		algIdCopy, algNameCopy := algId, algName
		g.GoRecover(func() error {
			optsCopy := opts
			optsCopy.AlgorithmId = algIdCopy
			codeNotNull, codeZero, err := a.watcher.Mongodb().GetAlgorithmAecData(fmt.Sprintf("algorithm-daily-report-%s", strings.ToLower(project)), optsCopy)
			if err != nil {
				log.CtxLog(c).Errorf("GetAlgorithmAecData err: %s", err.Error())
				return err
			}
			for _, an := range util.SplitAlgorithm(algNameCopy) {
				responseMap.Lock()
				if responseMap.mp[an] == nil {
					responseMap.mp[an] = &umw.AlgorithmVisualData{}
				}
				responseMap.mp[an].Name = an
				responseMap.mp[an].AlgorithmId = algIdCopy
				responseMap.mp[an].AecEffectiveRate = float64(codeNotNull) / float64(total)
				responseMap.mp[an].AecCorrectRate = float64(codeZero) / float64(total)
				responseMap.Unlock()
			}
			return nil
		})
	}
	if err = g.Wait(); err != nil {
		return err
	}
	log.CtxLog(c).Infof("succeed to get algorithm aec data, time: %v", time.Now().Sub(start))
	return nil
}

func (a *alg) getAlgorithmVersion(c *gin.Context, project string, responseMap *algorithmVisualDataMap) error {
	start := time.Now()
	_, versionMap, err := a.watcher.Mongodb().GetAlgorithmVersion(umw.Algorithm, umw.PublishVersion, umw.PublishVersionInfo, project)
	if err != nil {
		log.CtxLog(c).Errorf("GetAlgorithmShadow err: %s", err.Error())
		return err
	}
	for _, algName := range umw.IntAlgorithmMap {
		renameAlgName := util.RenameAlgorithmName(project, algName)
		if versionInfo, ok := versionMap[renameAlgName]; ok {
			// 获取算法更新时间
			updateTs, err := a.watcher.Mongodb().GetAlgorithmUpdateTs(renameAlgName)
			if err != nil {
				log.CtxLog(c).Errorf("fail to GetAlgorithmUpdateTs, err: %v", err)
				continue
			}

			for _, an := range util.SplitAlgorithm(algName) {
				responseMap.Lock()
				if responseMap.mp[an] == nil {
					responseMap.mp[an] = &umw.AlgorithmVisualData{}
				}
				responseMap.mp[an].AlgorithmId = algorithmIntMap[algName]
				responseMap.mp[an].Name = an
				responseMap.mp[an].Shadow = versionInfo.Shadow
				responseMap.mp[an].UpdateTs = updateTs
				responseMap.mp[an].Version = versionInfo.AlgorithmVersion
				responseMap.Unlock()
			}
		}
	}
	log.CtxLog(c).Infof("succeed to get algorithm shadow, time: %v", time.Now().Sub(start))
	return nil
}

func (a *alg) getAlgorithmFTT(project string, responseMap *algorithmVisualDataMap) error {
	for _, algName := range umw.IntAlgorithmMap {
		for _, an := range util.SplitAlgorithm(algName) {
			responseMap.Lock()
			if responseMap.mp[an] == nil {
				responseMap.mp[an] = &umw.AlgorithmVisualData{}
			}
			fttMutex.RLock()
			responseMap.mp[an].IsFTT = algorithmFTTMap[project][an]
			fttMutex.RUnlock()
			responseMap.Unlock()
		}
	}
	return nil
}

func (a *alg) GetSuccessRateAll() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request model.AlgorithmSuccessRateRequest
		)
		log.CtxLog(c).Named("GetSuccessRateAll")
		response := struct {
			um.Base
			Total       int64   `json:"total"`
			SuccessRate float64 `json:"success_rate"`
		}{}
		project := c.Param("project")
		if project != umw.PUS3 && project != umw.PowerSwap && project != umw.PowerSwap2 {
			log.CtxLog(c).Errorf("invalid project: %s", project)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("invalid project: %s", project))
			return
		}
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("uri parameters are incorrect: %s", err.Error()))
			return
		}
		if request.StartTime == 0 {
			request.StartTime = util.TimeDay(time.Now().AddDate(0, 0, -1).UnixMilli()).UnixMilli()
		}
		pfsMap, pErr := a.getParametricFormulaSwitchMap(c, project, request.StartTime, request.UsePfs)
		if pErr != nil {
			log.CtxLog(c).Errorf("fail to get pfs: %v, project: %s, day: %d", pErr, project, request.StartTime)
			um.FailWithInternalServerError(c, &response, pErr.Error())
			return
		}
		total, successRate, err, extraData := a.watcher.Mongodb().GetAlgorithmSuccessRate(fmt.Sprintf("algorithm-daily-report-%s", strings.ToLower(project)), project, request, pfsMap)
		if err != nil {
			log.CtxLog(c).Errorf("get algorithm success rate err: %s", err.Error())
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.SuccessRate = successRate
		response.Total = total
		if request.Name == model.BSAVehicle || request.Name == model.RSDSClose {
			if len(extraData) > 0 && total > 0 {
				response.SuccessRate = float64(extraData[0].(int64)) / float64(total)
			}
		}

		log.CtxLog(c).Info("succeed to GetSuccessRateAll")
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) GetSuccessRateTrend() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request model.AlgorithmSuccessRateRequest
		)
		log.CtxLog(c).Named("GetSuccessRateTrend")
		project := c.Param("project")
		response := struct {
			um.Base
			Data []model.DailySuccessRate `json:"data"`
		}{}
		if project != umw.PUS3 && project != umw.PowerSwap && project != umw.PowerSwap2 {
			log.CtxLog(c).Errorf("invalid project: %s", project)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("invalid project: %s", project))
			return
		}
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("uri parameters are incorrect: %s", err.Error()))
			return
		}
		dataLen := int((request.EndTime - request.StartTime) / model.DayMillisecond)
		response.Data = make([]model.DailySuccessRate, dataLen)
		g := ucmd.NewErrGroup(c, 10)
		for i := 0; i < dataLen; i++ {
			idx := i
			g.GoRecover(func() error {
				day := request.StartTime + int64(idx)*model.DayMillisecond
				opts := model.AlgorithmSuccessRateRequest{
					AlgorithmId: request.AlgorithmId,
					VehicleType: request.VehicleType,
					BatteryType: request.BatteryType,
					TimeRange:   request.TimeRange,
					StartTime:   day,
					DeviceId:    request.DeviceId,
				}
				pfsMap, pErr := a.getParametricFormulaSwitchMap(c, project, opts.StartTime, request.UsePfs)
				if pErr != nil {
					log.CtxLog(c).Errorf("fail to get pfs: %v, project: %s, day: %d", pErr, project, request.StartTime)
					return pErr
				}
				total, successRate, err, extraData := a.watcher.Mongodb().GetAlgorithmSuccessRate(fmt.Sprintf("algorithm-daily-report-%s", strings.ToLower(project)), project, opts, pfsMap)
				if err != nil {
					return err
				}
				if request.Name == model.BSAVehicle || request.Name == model.RSDSClose {
					if len(extraData) > 0 && total > 0 {
						successRate = float64(extraData[0].(int64)) / float64(total)
					} else {
						successRate = 0
					}
				}
				response.Data[idx] = model.DailySuccessRate{
					Day:         day,
					SuccessRate: successRate,
				}
				return nil
			})
		}
		if err := g.Wait(); err != nil {
			log.CtxLog(c).Errorf("fail to GetAlgorithmSuccessRate, err: %s", err.Error())
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		log.CtxLog(c).Info("succeed to GetSuccessRateTrend")
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) GetSuccessRateLowest() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request model.AlgorithmSuccessRateRequest
		)
		log.CtxLog(c).Named("GetSuccessRateLowest")
		project := c.Param("project")
		response := struct {
			um.Base
			Data []model.DeviceSuccessRate `json:"data"`
		}{Data: make([]model.DeviceSuccessRate, 0)}
		if project != umw.PUS3 && project != umw.PowerSwap && project != umw.PowerSwap2 {
			log.CtxLog(c).Errorf("invalid project: %s", project)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("invalid project: %s", project))
			return
		}
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("uri parameters are incorrect: %s", err.Error()))
			return
		}

		pfsMap, pErr := a.getParametricFormulaSwitchMap(c, project, request.StartTime, request.UsePfs)
		if pErr != nil {
			log.CtxLog(c).Errorf("fail to get pfs: %v, project: %s, day: %d", pErr, project, request.StartTime)
			um.FailWithInternalServerError(c, &response, pErr.Error())
			return
		}

		deviceSuccessRate, err := a.watcher.Mongodb().GetAlgorithmSuccessRateLowest(fmt.Sprintf("algorithm-daily-report-%s", strings.ToLower(project)), project, request, pfsMap)
		if err != nil {
			log.CtxLog(c).Errorf("fail to GetAlgorithmSuccessRateLowest, err: %s", err.Error())
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = deviceSuccessRate
		log.CtxLog(c).Info("succeed to GetSuccessRateLowest")
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) GetVehicleBattery() gin.HandlerFunc {
	return func(c *gin.Context) {
		log.CtxLog(c).Named("GetVehicleBattery")
		response := struct {
			um.Base
			VehicleList []string `json:"vehicle_list"`
			BatteryList []string `json:"battery_list"`
		}{VehicleList: make([]string, 0), BatteryList: make([]string, 0)}

		requestData := struct {
			AlgorithmId int   `json:"algorithm_id" form:"algorithm_id" uri:"algorithm_id" binding:"required"`
			Day         int64 `json:"day" form:"day" uri:"day"`
		}{}
		if err := c.BindQuery(&requestData); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		project := c.Param("project")
		if project != umw.PUS3 && project != umw.PowerSwap && project != umw.PowerSwap2 {
			log.CtxLog(c).Errorf("invalid project: %s", project)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("invalid project: %s", project))
			return
		}
		var startDay time.Time
		if requestData.Day == 0 {
			startDay = util.TimeDay(time.Now().AddDate(0, 0, -1).UnixMilli())
		} else {
			startDay = util.TimeDay(requestData.Day)
		}
		dbName := fmt.Sprintf("algorithm-daily-report-%s", strings.ToLower(project))
		collectionName := fmt.Sprintf("%s_%s", umw.IntAlgorithmMap[requestData.AlgorithmId], strconv.Itoa(int(startDay.Month())))
		vehicleList, batteryList, err := a.watcher.Mongodb().GetVehicleBattery(dbName, collectionName, startDay)
		if err != nil {
			log.CtxLog(c).Errorf("fail to GetVehicleBattery, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.VehicleList = vehicleList
		response.BatteryList = batteryList
		log.CtxLog(c).Info("succeed to GetVehicleBattery")
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) UpdateFTTList() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  umw.MongoFTTList
			response um.Base
		)
		log.CtxLog(c).Named("UpdateFTTList")
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %s", err.Error())
			um.FailWithBadRequest(c, &response, fmt.Sprintf("parse request body, err: %s", err.Error()))
			return
		}
		project := c.Param("project")
		if project != umw.PUS3 && project != umw.PowerSwap && project != umw.PowerSwap2 {
			log.CtxLog(c).Errorf("invalid project: %s", project)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("invalid project: %s", project))
			return
		}

		request.Project = project
		err := a.watcher.Mongodb().NewMongoEntry(bson.D{
			{"project", project},
			{"algorithm_name", request.AlgorithmName},
		}).ReplaceOne(umw.Algorithm, umw.FTTList, request, true)
		if err != nil {
			log.CtxLog(c).Errorf("fail to update ftt list, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) GetCountAlgorithmFTT() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.GetCountFTTRequest
			response model.GetCountFTTResponse
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("uri parameters are incorrect: %s", err.Error()))
			return
		}
		project := c.Param("project")
		if project != umw.PUS3 && project != umw.PowerSwap && project != umw.PowerSwap2 {
			log.CtxLog(c).Errorf("invalid project: %s", project)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("invalid project: %s", project))
			return
		}
		if request.Day == 0 {
			request.Day = time.Now().AddDate(0, 0, -1).UnixMilli()
		}
		startDay := util.TimeDay(request.Day)
		fttAlgorithms := make([]int, 0)
		for _, algorithm := range strings.Split(request.AlgorithmName, ",") {
			fttAlgorithms = append(fttAlgorithms, algorithmIntMap[algorithm])
		}
		fttList := make([]int, len(fttAlgorithms))
		copy(fttList, fttAlgorithms)
		fttInfo, fttErr := a.PangeaCalculateFTT(c, project, startDay.UnixMilli(), fttList)
		if fttErr != nil {
			log.CtxLog(c).Errorf("fail to calculate ftt, err: %v, project: %s, day: %d", fttErr, project, startDay.UnixMilli())
			um.FailWithBadRequest(c, &response, fmt.Sprintf("fail to calculate ftt: %s", project))
			return
		}
		response.FTT = fttInfo.FTT
		response.Day = request.Day
		log.CtxLog(c).Infof("succeed to get algorithm ftt")
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) GetAlgorithmFTT() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response struct {
			um.Base
			FTT            float64 `json:"ftt"`
			TheoreticalFTT float64 `json:"theoretical_ftt"`
			FTTTrend       []struct {
				Day int64   `json:"day"`
				FTT float64 `json:"ftt"`
			} `json:"ftt_trend"`
			DeviceFTT []umw.DeviceFTT `json:"device_ftt"`
		}
		log.CtxLog(c).Named("GetAlgorithmFTT")
		request := struct {
			StartTime int64 `form:"start_time"`
			EndTime   int64 `form:"end_time"`
			Update    bool  `form:"update"`
			Day       int64 `form:"day"`
			UsePfs    bool  `form:"use_pfs"`
		}{}
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if request.StartTime == 0 {
			request.StartTime = util.TimeDay(time.Now().AddDate(0, 0, -7).UnixMilli()).UnixMilli()
		}
		if request.EndTime == 0 {
			request.EndTime = util.TimeDay(time.Now().AddDate(0, 0, -1).UnixMilli()).UnixMilli()
		}
		if request.StartTime > request.EndTime {
			errStr := fmt.Sprintf("invalid start_time or end_time, request: %v", request)
			log.CtxLog(c).Errorf(errStr)
			um.FailWithBadRequest(c, &response, errStr)
			return
		}
		project := c.Param("project")
		if project != umw.PUS3 && project != umw.PowerSwap && project != umw.PowerSwap2 {
			log.CtxLog(c).Errorf("invalid project: %s", project)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("invalid project: %s", project))
			return
		}
		if request.Day == 0 {
			request.Day = time.Now().AddDate(0, 0, -1).UnixMilli()
		}
		request.UsePfs = true
		usePfs := request.UsePfs

		// 查找ftt表中是否有昨日的数据
		var fttInfoList []umw.MongoFTT
		var renew bool
		startDay := util.TimeDay(request.Day)
		if !request.Update {
			byteData, err := a.watcher.Mongodb().NewMongoEntry(bson.D{
				{"project", project},
				{"day", startDay.UnixMilli()},
				{"use_pfs", request.UsePfs},
			}).ListAll(umw.Algorithm, umw.FTT, client.Ordered{})
			if err != nil {
				log.CtxLog(c).Errorf("fail to get ftt, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			if err = json.Unmarshal(byteData, &fttInfoList); err != nil {
				log.CtxLog(c).Errorf("fail to unmarshal ftt info list, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			if len(fttInfoList) == 0 {
				renew = true
			}
		} else {
			renew = true
		}
		// 若昨日ftt数据还未计算，或请求强制更新，则重新计算昨日ftt
		if renew {
			var fttAlgorithms []int
			for algorithm, isFTT := range algorithmFTTMap[project] {
				if isFTT {
					fttAlgorithms = append(fttAlgorithms, algorithmIntMap[algorithm])
				}
			}
			g := ucmd.NewErrGroup(c)
			g.GoRecover(func() error {
				fttList := make([]int, len(fttAlgorithms))
				copy(fttList, fttAlgorithms)
				fttInfo, fttErr := a.CalculateFTT(c, project, startDay.UnixMilli(), fttList, request.UsePfs)
				if fttErr != nil {
					log.CtxLog(c).Errorf("fail to calculate ftt, err: %v, project: %s, day: %d", fttErr, project, startDay.UnixMilli())
					return fttErr
				}
				usePfs = fttInfo.UsePfs
				indexOption := []client.IndexOption{
					{
						Name:   "day_project_pfs_unique",
						Fields: bson.D{{"day", -1}, {"project", 1}, {"use_pfs", 1}},
						Unique: true,
					},
				}
				fttErr = a.watcher.Mongodb().NewMongoEntry(bson.D{
					{"project", project},
					{"day", startDay.UnixMilli()},
					{"use_pfs", fttInfo.UsePfs},
				}).ReplaceOne(umw.Algorithm, umw.FTT, fttInfo, true, indexOption...)
				if fttErr != nil {
					log.CtxLog(c).Errorf("fail to insert fft, err: %v, project: %s, day: %d, use_pfs: %v", fttErr, project, startDay.UnixMilli(), fttInfo.UsePfs)
					return fttErr
				}
				response.FTT = fttInfo.FTT
				response.DeviceFTT = fttInfo.DeviceFTT
				return nil
			})
			if err := g.Wait(); err != nil {
				log.CtxLog(c).Errorf("goroutine err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
		} else {
			// 昨日ftt数据已计算，直接读取使用
			fttInfo := fttInfoList[0]
			response.FTT = fttInfo.FTT
			response.DeviceFTT = fttInfo.DeviceFTT
		}
		if project == umw.PowerSwap2 {
			response.TheoreticalFTT = a.config.PowerSwap2.TheoreticalFtt
		} else if project == umw.PUS3 {
			response.TheoreticalFTT = a.config.PUS3.TheoreticalFtt
		}

		// 获取FTT趋势
		var fttTrend []struct {
			Day int64   `json:"day"`
			FTT float64 `json:"ftt"`
		}
		fttTrendRaw, fttErr := a.watcher.Mongodb().NewMongoEntry(bson.D{
			{"project", project},
			{"day", bson.M{"$gte": request.StartTime, "$lte": request.EndTime}},
			{"use_pfs", usePfs},
		}).ListAll(umw.Algorithm, umw.FTT, client.Ordered{Key: "day", Descending: false})
		if fttErr != nil {
			log.CtxLog(c).Errorf("fail to get ftt trend, err: %v", fttErr)
			um.FailWithInternalServerError(c, &response, fttErr.Error())
			return
		}
		if fttErr = json.Unmarshal(fttTrendRaw, &fttTrend); fttErr != nil {
			log.CtxLog(c).Errorf("fail to unmarshal ftt info list, err: %v", fttErr)
			um.FailWithInternalServerError(c, &response, fttErr.Error())
			return
		}
		response.FTTTrend = fttTrend

		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) PangeaCalculateFTT(c *gin.Context, project string, day int64, fttAlgList []int) (fttInfo umw.MongoFTT, err error) {
	fttInfo.Day = day
	fttInfo.Project = project
	fttInfo.UpdateTs = time.Now().UnixMilli()
	fttInfo.Date = time.UnixMilli(day)
	pfsMap, err := a.getParametricFormulaSwitchMap(c, project, day, true)
	if err != nil {
		return
	}
	_, ftt, err := a.calculateFTT(c, project, day, fttAlgList, pfsMap, true)
	if err != nil {
		return
	}
	fttInfo.FTT = ftt
	return
}

func (a *alg) calculateParametricFormulaSwitch(c *gin.Context, project string, day int64) (pfsMap map[int]map[string]struct{}, err error) {
	algorithmList := make([]int, 0)
	for algId := range umw.IntAlgorithmMap {
		algorithmList = append(algorithmList, algId)
	}
	pfsMap = make(map[int]map[string]struct{})
	var pfsList []interface{}
	pfsMap, pfsList, err = a.watcher.RbMongodb().GetYesterdayParametricFormulaSwitch(project, day, algorithmList)
	if err != nil {
		log.CtxLog(c).Errorf("fail to get yesterday parametric formula switch, err: %v", err)
		return
	}
	indexOption := []client.IndexOption{
		{
			Name:   "day_device_unique",
			Fields: bson.D{{"date", -1}, {"device_id", 1}},
			Unique: true,
		},
		{
			Name:        "expire_date",
			Fields:      bson.D{{"date", -1}},
			ExpiredTime: 180 * 24 * 3600,
		},
	}
	err = a.watcher.Mongodb().NewMongoEntry().InsertMany("algorithm", fmt.Sprintf("parametric-formula-switch-%s", strings.ToLower(project)), pfsList, indexOption...)
	if err != nil && !strings.Contains(err.Error(), "duplicate key error collection") {
		log.CtxLog(c).Errorf("fail to insert yesterday parametric formula switch, err: %v", err)
		return
	}
	return
}

func (a *alg) getParametricFormulaSwitchMap(c *gin.Context, project string, day int64, usePfs bool) (pfsMap map[int]map[string]struct{}, err error) {
	pfsMap = make(map[int]map[string]struct{}) // {1: {"PS-NIO-device1": {}, "PS-NIO-device2": {}}}
	if !usePfs {
		return
	}

	yesterdayPfsMap, err := a.watcher.Mongodb().GetParametricFormulaSwitch(project, day)
	if err != nil {
		log.CtxLog(c).Errorf("fail to get parametric formula switch yesterday, err: %v", err)
		return
	}
	if len(yesterdayPfsMap) == 0 {
		yesterdayPfsMap, err = a.calculateParametricFormulaSwitch(c, project, day)
		if err != nil {
			return
		}
	}

	dayBeforeYesterdayPfsMap, err := a.watcher.Mongodb().GetParametricFormulaSwitch(project, util.TimeDay(day).AddDate(0, 0, -1).UnixMilli())
	if err != nil {
		log.CtxLog(c).Errorf("fail to get parametric formula switch day before yesterday, err: %v", err)
		return
	}
	if len(dayBeforeYesterdayPfsMap) == 0 {
		dayBeforeYesterdayPfsMap, err = a.calculateParametricFormulaSwitch(c, project, util.TimeDay(day).AddDate(0, 0, -1).UnixMilli())
		if err != nil {
			return
		}
	}

	for algId, devicePfsMap := range yesterdayPfsMap {
		if dayBeforeYesterdayPfsMap[algId] == nil || len(dayBeforeYesterdayPfsMap[algId]) == 0 {
			pfsMap[model.AiAlgorithmRealIdMap[project][algId]] = yesterdayPfsMap[algId]
			continue
		}
		pfsMap[model.AiAlgorithmRealIdMap[project][algId]] = make(map[string]struct{})
		for deviceId := range devicePfsMap {
			if _, ok := dayBeforeYesterdayPfsMap[algId][deviceId]; ok {
				pfsMap[model.AiAlgorithmRealIdMap[project][algId]][deviceId] = struct{}{}
			}
		}
	}
	if len(pfsMap[1]) > 0 {
		pfsMap[3] = pfsMap[1]
	}
	return
}

func (a *alg) CalculateFTT(c *gin.Context, project string, day int64, fttAlgList []int, usePfs bool) (fttInfo umw.MongoFTT, err error) {
	fttInfo.Day = day
	fttInfo.Project = project
	fttInfo.UpdateTs = time.Now().UnixMilli()
	fttInfo.Date = time.UnixMilli(day)
	fttInfo.UsePfs = usePfs

	pfsMap, err := a.getParametricFormulaSwitchMap(c, project, day, usePfs)
	if err != nil {
		return
	}

	g := ucmd.NewErrGroup(c, 5)
	// 计算总ftt
	g.GoRecover(func() error {
		_, ftt, gErr := a.calculateFTT(c, project, day, fttAlgList, pfsMap, usePfs)
		if gErr != nil {
			return gErr
		}
		fttInfo.FTT = ftt
		return nil
	})

	// 设备ftt
	byteData, err := a.watcher.Mongodb().NewMongoEntry(bson.D{{"project", project}}).ListAll(umw.OAuthDB, umw.DeviceBaseInfo, client.Ordered{})
	//byteData, err := a.watcher.Mongodb().NewMongoEntry(bson.D{{"project", project}}).ListAll(umw.OAuthDB, "device_basic_info_prod", client.Ordered{})
	if err != nil {
		log.CtxLog(c).Errorf("fail to get device list, err: %v", err)
		return
	}
	var deviceList []umw.DeviceBase
	if err = json.Unmarshal(byteData, &deviceList); err != nil {
		log.CtxLog(c).Errorf("unmarshal device list err: %v", err)
		return
	}
	deviceFttHeap := util.NewDeviceFTTHeap(10)
	mu := sync.Mutex{}
	for i := range deviceList {
		deviceCopy := deviceList[i]
		g.GoRecover(func() error {
			total, ftt, gErr := a.calculateFTT(c, project, day, fttAlgList, pfsMap, usePfs, deviceCopy.DeviceId)
			if gErr != nil {
				return gErr
			}
			if total == 0 {
				return nil
			}
			deviceFTT := umw.DeviceFTT{
				DeviceId:    deviceCopy.DeviceId,
				Description: deviceCopy.Description,
				FTT:         ftt,
				Total:       total,
			}
			mu.Lock()
			defer mu.Unlock()
			deviceFttHeap.Push(deviceFTT)
			return nil
		})
	}
	if err = g.Wait(); err != nil {
		log.CtxLog(c).Errorf("goroutine err: %v", err)
		return
	}
	deviceFttList := deviceFttHeap.PopAll()
	// 计算ftt最低的10个站点的算法通过率
	for i := range deviceFttList {
		deviceFttList[i].AlgorithmSuccessRates = make([]umw.AlgorithmSuccessRate, len(fttAlgList))
		for j, algId := range fttAlgList {
			algIdCopy := algId
			jCopy := j
			g.GoRecover(func() error {
				opts := model.AlgorithmSuccessRateRequest{
					AlgorithmId: algIdCopy,
					StartTime:   day,
					DeviceId:    deviceFttList[i].DeviceId,
				}
				total, successRate, gErr, extraData := a.watcher.Mongodb().GetAlgorithmSuccessRate(fmt.Sprintf("algorithm-daily-report-%s", strings.ToLower(project)), project, opts, pfsMap)
				if gErr != nil {
					log.CtxLog(c).Errorf("fail to get algorithm success rate, err: %v, opts: %v", gErr, opts)
					return gErr
				}
				name := umw.IntAlgorithmMap[algIdCopy]
				if algIdCopy == 6 || algIdCopy == 16 {
					if len(extraData) > 0 && total > 0 {
						successRate = float64(extraData[0].(int64)) / float64(total)
					} else {
						successRate = 0
					}
					if algIdCopy == 6 {
						name = model.BSAService
					} else if algIdCopy == 16 {
						name = model.RSDSOpen
					}
				}
				deviceFttList[i].AlgorithmSuccessRates[jCopy] = umw.AlgorithmSuccessRate{
					Name:        name,
					SuccessRate: successRate,
				}
				return nil
			})
		}
		if err = g.Wait(); err != nil {
			log.CtxLog(c).Errorf("goroutine err: %v", err)
			return
		}
		sort.Slice(deviceFttList[i].AlgorithmSuccessRates, func(m, n int) bool {
			s1 := deviceFttList[i].AlgorithmSuccessRates[m]
			s2 := deviceFttList[i].AlgorithmSuccessRates[n]
			return (s1.SuccessRate < s2.SuccessRate) || (s1.SuccessRate == s2.SuccessRate && s1.Name < s2.Name)
		})
	}
	fttInfo.DeviceFTT = deviceFttList
	return
}

type WLItem struct {
	EndTime  int64
	Success  bool
	DeviceId string
}

func (a *alg) calculateFTT(c *gin.Context, project string, day int64, fttAlgList []int, parametricFormulaSwitch map[int]map[string]struct{}, usePfs bool, deviceId ...string) (total int64, ftt float64, err error) {
	// usePfs为true时只计算参数配方打开的站点，usePfs为false时全量计算
	// 总订单
	serviceMap := &sync.Map{}

	// 计算ftt成功的订单数
	g := ucmd.NewErrGroup(c, 5)
	dbName := fmt.Sprintf("algorithm-daily-report-%s", strings.ToLower(project))

	algServiceIdSuccessMap := make(map[int]map[string]model.AlgServiceSuccess) // 6: {"PS-NIO-xxx": true}
	mu := sync.Mutex{}
	startDay := time.UnixMilli(day)
	for _, algId := range fttAlgList {
		// WL算法无订单ID，通过特殊方法计算
		if algId == 1 {
			g.GoRecover(func() error {
				filter := bson.D{
					{"date", bson.M{"$gte": startDay, "$lt": startDay.AddDate(0, 0, 1)}},
					{"errorcode", 0},
				}
				if len(deviceId) > 0 {
					filter = append(filter, bson.E{Key: "device_id", Value: deviceId[0]})
				}
				// 获取第一次BSA算法的全量排序数组
				sortedBSA, gErr := a.watcher.Mongodb().GetBSASortedList(dbName, filter, startDay)
				if gErr != nil {
					log.CtxLog(c).Errorf("get bsa sorted list, err: %v", gErr)
					return gErr
				}
				// 获取WL算法数据（无service_id）
				byteData, gErr := a.watcher.Mongodb().NewMongoEntry(filter).ListAll(dbName, fmt.Sprintf("WL_%s", strconv.Itoa(int(startDay.Month()))), client.Ordered{})
				var wlData []umw.MongoAlgorithmDailyReport
				if gErr = json.Unmarshal(byteData, &wlData); gErr != nil {
					log.CtxLog(c).Errorf("fail to unmarshal wl list, err: %v", gErr)
					return gErr
				}
				wlMap := make(map[string]WLItem)
				for _, wlRecord := range wlData {
					idx := sort.Search(len(sortedBSA), func(i int) bool {
						return sortedBSA[i].EndTime >= wlRecord.EndTime
					})
					if idx >= 0 && idx < len(sortedBSA) {
						//fmt.Println(*wlRecord.OutputValue, sortedBSA[idx].ServiceId, idx)
						item, ok := wlMap[sortedBSA[idx].ServiceId]
						if (ok && item.EndTime < wlRecord.EndTime) || !ok {
							wlMap[sortedBSA[idx].ServiceId] = WLItem{
								EndTime:  wlRecord.EndTime,
								Success:  *wlRecord.OutputValue == 1,
								DeviceId: wlRecord.DeviceId,
							}
						}
					}
				}
				wlSuccessMap := make(map[string]model.AlgServiceSuccess)
				for sid, item := range wlMap {
					wlSuccessMap[sid] = model.AlgServiceSuccess{
						DeviceId: item.DeviceId,
						Success:  item.Success,
					}
					serviceMap.Store(sid, struct{}{})
				}
				mu.Lock()
				defer mu.Unlock()
				algServiceIdSuccessMap[1] = wlSuccessMap
				return nil
			})
			continue
		}
		// VOR暂无数据，跳过
		if algId == 4 {
			continue
		}
		// VIP目前没有订单ID，跳过
		if algId == 2 {
			continue
		}
		// WPV目前没有订单ID，之后会有
		if algId == 15 {
			continue
		}
		currAlgId := algId
		g.GoRecover(func() error {
			mp, gErr := a.watcher.Mongodb().GetServiceIdSuccessMap(dbName, day, currAlgId, serviceMap, deviceId...)
			if gErr != nil {
				return gErr
			}
			mu.Lock()
			defer mu.Unlock()
			algServiceIdSuccessMap[currAlgId] = mp
			return nil
		})
	}

	if err = g.Wait(); err != nil {
		log.CtxLog(c).Errorf("goroutine err: %v", err)
		return
	}

	successMap := make(map[string]struct{})
	//failReasonMap := make(map[int]int)
	//countMap := make(map[int]int)
	//algMap := make(map[int]int)
	serviceMap.Range(func(key, value interface{}) bool {
		total += 1
		serviceId := fmt.Sprintf("%v", key)
		serviceIdSuccess := true
		if !usePfs {
			for _, mp := range algServiceIdSuccessMap {
				// 只有所有算法的map中的该订单id都为成功才说明该订单id为成功
				if info, ok := mp[serviceId]; ok && !info.Success {
					//failReasonMap[algId]++
					//fmt.Printf("algId:%d,service id:%s,device id:%s,flag:%t\n", algId, serviceId, info.DeviceId, info.Success)
					serviceIdSuccess = false
					break
				}
			}
			if serviceIdSuccess {
				successMap[serviceId] = struct{}{}
			}
		} else {
			for algId, mp := range algServiceIdSuccessMap {
				if info, ok := mp[serviceId]; ok {
					//algMap[algId] ++
					_, flag := parametricFormulaSwitch[algId][info.DeviceId]
					// PiP、RSDV_PiP、RSDV_VLSV 三种算法无赤兔开关
					if algId == 9 || algId == 11 || algId == 12 {
						flag = true
					}
					//if flag {
					//	countMap[algId] ++
					//}
					if !info.Success && flag {
						serviceIdSuccess = false
						break
					}
				}
			}
			if serviceIdSuccess {
				successMap[serviceId] = struct{}{}
			}
		}
		return true
	})

	// 避免出现NaN
	if total == 0 {
		return
	}
	//fmt.Println("fail map:", failReasonMap)
	//fmt.Println("total:", total)
	//fmt.Println("pfs count map:", countMap)
	//fmt.Println("pfs alg map:", algMap)
	ftt = float64(len(successMap)) / float64(total)
	return
}

func (a *alg) GetAecStatus() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response struct {
			um.Base
			CpuUsed map[int]umw.ResourceStatus `json:"cpu_used"`
		}

		log.CtxLog(c).Named("GetAecStatus")
		project := c.Param("project")
		if project != umw.PUS3 && project != umw.PowerSwap && project != umw.PowerSwap2 {
			log.CtxLog(c).Errorf("invalid project: %s", project)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("invalid project: %s", project))
			return
		}
		request := struct {
			Update bool  `form:"update"`
			Day    int64 `form:"day"`
		}{}
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("invalid query params, err: %s", err.Error())
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if request.Day == 0 {
			request.Day = util.TimeDay(time.Now().AddDate(0, 0, -1).UnixMilli()).UnixMilli()
		}

		var record umw.MongoAecStatus
		var renew bool
		if !request.Update {
			rawData, err := a.watcher.Mongodb().NewMongoEntry(bson.D{
				{"date", time.UnixMilli(request.Day)},
				{"project", project}}).GetOne(umw.Algorithm, umw.AecStatus)
			if err != nil {
				log.CtxLog(c).Errorf("fail to get aec status, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			if rawData != nil {
				if err = bson.Unmarshal(rawData, &record); err != nil {
					log.CtxLog(c).Errorf("fail to unmarshal aec status, err: %v", err)
					um.FailWithInternalServerError(c, &response, err.Error())
					return
				}
			} else {
				renew = true
			}
		} else {
			renew = true
		}
		if renew {
			cpuUsedMap := make(map[string][]float64)
			for _, algName := range umw.IntAlgorithmMap {
				cpuUsed, err := a.watcher.Mongodb().CalculateAecCpuUsage(fmt.Sprintf("algorithm-daily-report-%s", strings.ToLower(project)), algName, request.Day)
				if err != nil {
					log.CtxLog(c).Errorf("fail to calculate aec cpu usage, algName: %s, day: %d", algName, request.Day)
					um.FailWithInternalServerError(c, &response, err.Error())
					return
				}
				cpuUsedMap[algName] = cpuUsed
			}
			record = umw.MongoAecStatus{
				Day:     request.Day,
				Project: project,
				CpuUsed: cpuUsedMap,
				Date:    time.UnixMilli(request.Day),
			}
			err := a.watcher.Mongodb().NewMongoEntry(bson.D{
				{"date", record.Date},
				{"project", project}}).ReplaceOne(umw.Algorithm, umw.AecStatus, record, true, client.IndexOption{
				Name:        "expire_date",
				Fields:      bson.D{{"date", -1}},
				ExpiredTime: 180 * 24 * 3600,
			})
			if err != nil {
				log.CtxLog(c).Errorf("fail to insert aec status, err: %v, project: %s, day: %d", err, project, request.Day)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
		}

		response.CpuUsed = make(map[int]umw.ResourceStatus)
		for stage, algos := range umw.StageAlgorithmMap {
			var max, sum float64
			for _, algo := range algos {
				cpuUsed := record.CpuUsed[algo]
				if len(cpuUsed) == 0 || algo == "BSA" && len(cpuUsed) < 2 {
					log.CtxLog(c).Warnf("fail to get cpu status, algorithm: %s", algo)
					continue
				}
				record := cpuUsed[0]
				if algo == "BSA" && stage == 6 {
					record = cpuUsed[1]
				}
				sum += record
				if record > max {
					max = record
				}
			}
			response.CpuUsed[stage] = umw.ResourceStatus{
				Max: max,
				Avg: sum / float64(len(algos)),
			}
		}
		um.SuccessWithMessageForGin(c, &response, "", http.StatusOK)
	}
}

func (a *alg) LPRDataReport() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response um.Base
		)

		log.CtxLog(c).Named("LPR Data Report")
		var requestData struct {
			Plateno    string `json:"plateno" binding:"required"`
			DeviceId   string `json:"device_id" binding:"required"`
			UploadTime int64  `json:"upload_time" binding:"required"`
			ServiceId  string `json:"service_id" binding:"required"`
		}
		project := c.Param("project")
		if project != umw.PowerSwap2 && project != umw.PUS3 && project != umw.PUS4 {
			log.CtxLog(c).Errorf("`project`: %s is invalid", project)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("`project`: %s is invalid", project))
			return
		}
		if err := c.BindJSON(&requestData); err != nil {
			log.CtxLog(c).Errorf("parse lpr request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if requestData.ServiceId == "no_service_id_" {
			log.CtxLog(c).Warnf("invalid lpr service id, request: %+v", requestData)
			um.SuccessWithBadRequest(c, &response, "invalid lpr service id")
			return
		}
		log.CtxLog(c).Infof("plateno: %s, device_id: %s, upload_time: %d, service_id: %s", requestData.Plateno, requestData.DeviceId, requestData.UploadTime, requestData.ServiceId)

		// get service info
		var serviceInfo umw.MongoServiceInfo
		dbName := fmt.Sprintf("%s-%s", umw.ServiceInfo, ucmd.RenameProjectDB(project))

		err := retry.Do(func() error {
			for collection := range util.ParseTimeRangeList(requestData.UploadTime-600, requestData.UploadTime+600) {
				rawData, err := a.watcher.Mongodb().NewMongoEntry(bson.D{{"service_id", requestData.ServiceId}}).GetOne(dbName, collection)
				if err != nil {
					log.CtxLog(c).Warnf("fail to get lpr service info, err: %v, request: %v", err, ucmd.ToJsonStrIgnoreErr(requestData))
					return err
				}
				if rawData != nil {
					if err = bson.Unmarshal(rawData, &serviceInfo); err != nil {
						log.CtxLog(c).Errorf("fail to unmarshal lpr service info, err: %v, request: %v", err, ucmd.ToJsonStrIgnoreErr(requestData))
						return err
					}
					break
				}
			}
			if serviceInfo.EvId == "" {
				msg := fmt.Sprintf("fail to get lpr vehicle id from service info, request: %s, service_info: %s", ucmd.ToJsonStrIgnoreErr(requestData), ucmd.ToJsonStrIgnoreErr(serviceInfo))
				log.CtxLog(c).Warn(msg)
				return fmt.Errorf(msg)
			}
			return nil
		}, []retry.Option{
			retry.Delay(time.Second),
			retry.Attempts(3),
			retry.LastErrorOnly(true),
		}...)
		if err != nil {
			log.CtxLog(c).Errorf("fail to retry: %v", err)
			um.SuccessWithInternalServerError(c, &response, err.Error())
			return
		}

		// get datasight data
		var dsRecord umw.MongoVehicleInfo
		var record umw.MongoVehicleInfo
		// get license data
		rawData, err := a.watcher.Mongodb().NewMongoEntry(bson.D{{"vehicle_id", serviceInfo.EvId}}).GetOne(umw.VehicleManagement, umw.DsLicense)
		if err != nil {
			log.CtxLog(c).Errorf("fail to get lpr license data, err: %v, service_id: %s, vehicle_id: %s", err, requestData.ServiceId, serviceInfo.EvId)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if rawData == nil {
			msg := fmt.Sprintf("get empty lpr license data, request: %v, vehicle_id: %s", requestData, serviceInfo.EvId)
			log.CtxLog(c).Infof(msg)
			um.SuccessWithMessageForGin(c, &response, msg, http.StatusOK)
			return
		} else {
			if err = bson.Unmarshal(rawData, &record); err != nil {
				log.CtxLog(c).Errorf("fail to unmarshal lpr license data, err: %v, request: %v", err, requestData)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
		}
		record.DsPlateno = strings.TrimSpace(record.DsPlateno)
		if record.DsPlateno == requestData.Plateno {
			msg := fmt.Sprintf("lpr plateno not change, service_id: %s, vehicle_id: %s", requestData.ServiceId, serviceInfo.EvId)
			log.CtxLog(c).Infof(msg)
			um.SuccessWithMessageForGin(c, &response, msg, http.StatusOK)
			return
		}
		dsRecord.VehicleId = record.VehicleId
		dsRecord.DsPlateno = record.DsPlateno
		dsRecord.LprPlateno = requestData.Plateno
		dsRecord.Dateoflicensed = record.Dateoflicensed

		// get old lpr-record data
		rawData, err = a.watcher.Mongodb().NewMongoEntry(bson.D{
			{"vehicle_id", record.VehicleId},
		}).GetOne(umw.VehicleManagement, umw.LprRecord, client.MongoOptions{Projection: &bson.M{"_id": 0, "lpr_plateno": 1}})
		if err != nil {
			msg := fmt.Sprintf("fail to get lpr record, err: %v, record: %v", err, dsRecord)
			log.CtxLog(c).Errorf(msg)
			um.FailWithInternalServerError(c, &response, msg)
			return
		}
		var oldDsRecord umw.MongoVehicleInfo
		if rawData != nil {
			if err = bson.Unmarshal(rawData, &oldDsRecord); err != nil {
				log.CtxLog(c).Errorf("fail to unmarshal lpr old ds record, err: %v, rawData: %v", err, rawData)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			if requestData.Plateno == oldDsRecord.LprPlateno {
				log.CtxLog(c).Infof("lpr plateno is same with old ds record, dsRecord: %+v", dsRecord)
				um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
				return
			}
		}

		// get equity data
		rawData, err = a.watcher.Mongodb().NewMongoEntry(bson.D{{"vehicle_id", serviceInfo.EvId}}).GetOne(umw.VehicleManagement, umw.DsEquity)
		if err != nil {
			log.CtxLog(c).Errorf("fail to get lpr equity data, err: %v, service_id: %s, vehicle_id: %s", err, requestData.ServiceId, serviceInfo.EvId)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if rawData != nil {
			if err = bson.Unmarshal(rawData, &record); err != nil {
				log.CtxLog(c).Errorf("fail to unmarshal lpr equity data, err: %v, request: %v", err, requestData)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			dsRecord.SpecificEquityName = record.SpecificEquityName
			dsRecord.EquityStockStatus = record.EquityStockStatus
		}
		// get status data
		byteData, err := a.watcher.Mongodb().NewMongoEntry(bson.D{{"vehicle_id", serviceInfo.EvId}}).ListAll(umw.VehicleManagement, umw.DsStatus, client.Ordered{Key: "update_time", Descending: true})
		if err != nil {
			log.CtxLog(c).Errorf("fail to get lpr status data, err: %v, service_id: %s, vehicle_id: %s", err, requestData.ServiceId, serviceInfo.EvId)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}

		var statusData []struct {
			VehicleId   string `json:"vehicle_id" bson:"vehicle_id"`
			OwnerStatus int    `json:"owner_status" bson:"owner_status"`
		}
		if err = json.Unmarshal(byteData, &statusData); err != nil {
			log.CtxLog(c).Errorf("fail to unmarshal lpr status data, err: %v, request: %v", err, requestData)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if len(statusData) > 0 {
			dsRecord.OwnerStatus = statusData[0].OwnerStatus
		}

		// write mongo
		dsRecord.UploadTs = requestData.UploadTime
		dsRecord.ServiceId = requestData.ServiceId
		dsRecord.Project = project
		dsRecord.DeviceId = requestData.DeviceId
		dsRecord.InsertTs = time.Now().UnixMilli()
		dsRecord.Date = time.Now()
		if dsRecord.DeviceId != "" {
			conn := udao.NewRedisConn(a.watcher.Redis())
			tags, tagErr := client.GetDeviceTag(conn, a.watcher.Mongodb(), dsRecord.DeviceId)
			conn.Close()
			if tagErr != nil {
				log.CtxLog(c).Errorf("cannot find the device name from mongo, device_id: %s, err: %v", dsRecord.DeviceId, tagErr)
			} else {
				dsRecord.Description = tags.Description
			}
		}
		indexOptions := []client.IndexOption{
			{Name: "service_id", Fields: bson.D{{"service_id", 1}}, Unique: true},
			{Name: "vehicle_id", Fields: bson.D{{"vehicle_id", 1}}},
			{Name: "plateno", Fields: bson.D{{"plateno", 1}}},
			{Name: "date", Fields: bson.D{{"date", -1}}, ExpiredTime: 12 * 30 * 3600 * 24},
		}
		if err = a.watcher.Mongodb().NewMongoEntry(bson.D{{"service_id", dsRecord.ServiceId}}).UpdateOne(umw.VehicleManagement, umw.LprRecord, bson.M{"$set": dsRecord}, true, indexOptions...); err != nil {
			msg := fmt.Sprintf("fail to insert lpr record, err: %v, record: %v", err, dsRecord)
			log.CtxLog(c).Errorf(msg)
			um.FailWithInternalServerError(c, &response, msg)
			return
		}

		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) GetLPRDataReport() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.GetLprRequest
			response model.GetLprResponse
		)
		log.CtxLog(c).Named("GetLPRDataReport")
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if request.Page == 0 {
			request.Page = 1
		}
		if request.Size == 0 {
			request.Size = 10
		}

		filter := make(bson.D, 0)
		if request.DsPlateno != nil {
			filter = append(filter, bson.E{Key: "ds_plateno", Value: *request.DsPlateno})
		}
		if request.LprPlateno != nil {
			filter = append(filter, bson.E{Key: "lpr_plateno", Value: *request.LprPlateno})
		}
		if request.VehicleId != nil {
			filter = append(filter, bson.E{Key: "vehicle_id", Value: *request.VehicleId})
		}
		if request.OwnerStatus != nil {
			var statusList []int
			for _, s := range strings.Split(*request.OwnerStatus, ",") {
				status, err := strconv.Atoi(s)
				if err != nil {
					log.CtxLog(c).Errorf("uri parameter owner status should be int, err: %v", err)
					um.FailWithBadRequest(c, &response, err.Error())
					return
				}
				statusList = append(statusList, status)
			}
			filter = append(filter, bson.E{Key: "owner_status", Value: bson.M{"$in": statusList}})
		}
		if request.EquityStockStatus != nil {
			filter = append(filter, bson.E{Key: "equity_stock_status", Value: bson.M{"$in": strings.Split(*request.EquityStockStatus, ",")}})
		}

		getDeviceDescription := func(deviceId string) string {
			var description string
			if deviceId != "" {
				conn := udao.NewRedisConn(a.watcher.Redis())
				tags, err := client.GetDeviceTag(conn, a.watcher.Mongodb(), deviceId)
				conn.Close()
				if err != nil {
					log.CtxLog(c).Errorf("cannot find the device name from mongo, device_id: %s, err: %v", deviceId, err)
				} else {
					description = tags.Description
				}
			}
			return description
		}

		// return value: {service_id: [start_time, end_time]}, example: {"PS-NIO-3e8d86f7-59924bcf6262fe2c9ad14ffba80042ea8a570d611704038433019": [1704038433019, 1704038434000]}
		getServiceInfo := func(records []umw.VehicleInfo) (map[string][2]int64, error) {
			// structure: {project: {collectionName: [service_id]}}, example: {PUS3: {3_4: [PS-NIO-1, PS-NIO-2]}, PowerSwap2: {1_2: [PS-NIO-22]}}
			serviceMap := make(map[string]map[string][]string)
			for _, r := range records {
				if serviceMap[r.Project] == nil {
					serviceMap[r.Project] = make(map[string][]string)
				}
				collections := util.ParseTimeRangeList(r.UploadTs-600, r.UploadTs+600)
				for collection := range collections {
					serviceMap[r.Project][collection] = append(serviceMap[r.Project][collection], r.ServiceId)
				}
			}
			serviceInfoMap := make(map[string][2]int64)
			for project, collInfo := range serviceMap {
				dbName := fmt.Sprintf("%s-%s", umw.ServiceInfo, ucmd.RenameProjectDB(project))
				for collectionName, servList := range collInfo {
					byteData, err := a.watcher.Mongodb().NewMongoEntry(bson.D{{"service_id", bson.M{"$in": servList}}}).ListAll(dbName, collectionName, client.Ordered{})
					if err != nil {
						log.CtxLog(c).Errorf("fail to get lpr service info, err: %v, project: %v, collection name: %s", err, project, collectionName)
						return nil, err
					}
					var serviceInfoList []umw.MongoServiceInfo
					if err = json.Unmarshal(byteData, &serviceInfoList); err != nil {
						log.CtxLog(c).Errorf("fail to unmarshal lpr service info, err: %v, project: %v, collection name: %s", err, project, collectionName)
						return nil, err
					}
					for _, record := range serviceInfoList {
						serviceInfoMap[record.ServiceId] = [2]int64{record.StartTime, record.EndTime}
					}
				}
			}
			return serviceInfoMap, nil
		}

		if request.Download {
			byteData, err := a.watcher.Mongodb().NewMongoEntry(filter).ListAll(umw.VehicleManagement, umw.LprRecord, client.Ordered{Key: "date", Descending: true})
			if err != nil {
				log.CtxLog(c).Errorf("fail to list all lpr records, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			var records []umw.VehicleInfo
			if err = json.Unmarshal(byteData, &records); err != nil {
				log.CtxLog(c).Errorf("fail to unmarshal lpr record, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}

			serviceInfoMap, err := getServiceInfo(records)
			if err != nil {
				log.CtxLog(c).Errorf("fail to get lpr service info map, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}

			fileName := fmt.Sprintf("license-info-%d.csv", time.Now().UnixMilli())
			csvRecords := make([][]string, len(records)+1)
			csvRecords[0] = []string{"车辆ID", "换电站检测车牌", "数仓记录车牌", "权益名称", "权益状态", "购车方式", "上牌时间", "服务ID", "设备ID", "设备名称", "设备类型", "换电开始时间", "换电结束时间"}
			for i, r := range records {
				csvRecords[i+1] = []string{
					r.VehicleId,
					r.LprPlateno,
					r.DsPlateno,
					r.SpecificEquityName,
					model.LprEquityMap[r.EquityStockStatus],
					model.LprStatusMap[r.OwnerStatus],
					util.TsString(r.Dateoflicensed),
					r.ServiceId,
					r.DeviceId,
					r.Description,
					r.Project,
					util.TsString(serviceInfoMap[r.ServiceId][0]),
					util.TsString(serviceInfoMap[r.ServiceId][1]),
				}
			}

			buffer := &bytes.Buffer{}
			buffer.WriteString("\xEF\xBB\xBF")
			cw := csv.NewWriter(buffer)
			if err = cw.WriteAll(csvRecords); err != nil {
				log.CtxLog(c).Errorf("fail to write to csv, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			c.Writer.Header().Set("Content-Type", "text/csv")
			util.Download(c, fileName, buffer.Bytes())
			return
		}

		byteData, total, err := a.watcher.Mongodb().NewMongoEntry(filter).ListByPagination(umw.VehicleManagement, umw.LprRecord,
			client.Pagination{Limit: int64(request.Size), Offset: int64((request.Page - 1) * request.Size)},
			client.Ordered{Key: "date", Descending: true})
		if err != nil {
			log.CtxLog(c).Errorf("fail to list lpr record, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Total = total
		var records []umw.VehicleInfo
		if err = json.Unmarshal(byteData, &records); err != nil {
			log.CtxLog(c).Errorf("fail to unmarshal lpr record, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		serviceInfoMap, err := getServiceInfo(records)
		if err != nil {
			log.CtxLog(c).Errorf("fail to get lpr service info map, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}

		for i, r := range records {
			records[i].Description = getDeviceDescription(r.DeviceId)
			records[i].ServiceStartTime = serviceInfoMap[r.ServiceId][0]
			records[i].ServiceEndTime = serviceInfoMap[r.ServiceId][1]
		}
		response.Data = records
		um.SuccessWithMessageForGin(c, &response, "OK", http.StatusOK)
		return
	}
}

type OPAlgorithmResult struct {
	BatteryType      string    `json:"battery_type" bson:"battery_type"`
	Datetime         string    `json:"datetime" bson:"datetime"`
	ResourceID       string    `json:"resource_id" bson:"resource_id"`
	StartHour        int64     `json:"start_hour" bson:"start_hour"`
	DeviceModel      string    `json:"device_model" bson:"device_model"`
	DeviceName       string    `json:"device_name" bson:"device_name"`
	ElecInterval     string    `json:"elec_interval" bson:"elec_interval"`
	EndHour          int64     `json:"end_hour" bson:"end_hour"`
	IsWeekend        string    `json:"is_weekend" bson:"is_weekend"`
	PriceTag         string    `json:"price_tag" bson:"price_tag"`
	ProbabilityArray []float64 `json:"probability_array" bson:"probability_array"`
	ServiceCnt       float64   `json:"service_cnt" bson:"service_cnt"`
	Hour             int       `json:"hour" bson:"hour"`
}

func (a *alg) GetCMSOrderInfo() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.CMSOrderInfoRequest
			response model.CMSOrderInfoResponse
		)
		project := c.Param("project")
		if project != umw.PowerSwap2 && project != umw.PUS3 && project != umw.PUS4 {
			msg := fmt.Sprintf("get cms order info, `project` %s is invalid", project)
			log.CtxLog(c).Errorf(msg)
			um.FailWithNotFound(c, &response, msg)
			return
		}
		if err := c.BindJSON(&request); err != nil {
			msg := fmt.Sprintf("get cms order info, parameters are incorrect: %v", err)
			log.CtxLog(c).Errorf(msg)
			um.FailWithBadRequest(c, &response, msg)
			return
		}
		deviceId := request.DeviceId
		deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(deviceId)
		if !found {
			um.FailWithNotFound(c, &response, "device id not found")
			return
		}
		// 虚拟设备id
		resourceId := deviceInfo.ResourceId

		// 实时累计换电订单数据
		modelTriggerTime := time.UnixMilli(request.ModelTriggerTime)
		day := strings.Split(modelTriggerTime.Format("20060102 15:04:05"), " ")

		pipeline := mongo.Pipeline{
			{{"$match", bson.M{
				"device_id": request.DeviceId,
				"day":       day[0],
				"hour":      modelTriggerTime.Hour(),
			}}},
			{{"$group", bson.M{
				"_id":   "$battery_type",
				"count": bson.M{"$sum": 1},
			}}},
			{{"$project", bson.M{
				"_id":          0,
				"battery_type": "$_id",
				"count":        "$count",
			}}},
		}
		var res []struct {
			BatteryType string `bson:"battery_type"`
			Count       int    `bson:"count"`
		}
		if err := a.watcher.PLCMongodb().NewMongoEntry().Aggregate("daily-battery-services", "services", pipeline, &res); err != nil {
			msg := fmt.Sprintf("get cms order info, fail to get daily battery services, request: %+v, err: %v", request, err)
			log.CtxLog(c).Errorf(msg)
			um.FailWithInternalServerError(c, &response, msg)
			return
		}
		resMap := make(map[string]int)
		for _, record := range res {
			resMap[record.BatteryType] = record.Count
		}
		response.Data.ServedService.Num70 = []int{resMap["70"]}
		response.Data.ServedService.Num100 = []int{resMap["100"]}

		// 电价数据
		deviceDO := domain_device.Device{}
		priceTag, err := deviceDO.GetDeviceElectricityPriceHalfHour(c, domain_device.DeviceElectricityPriceCond{DeviceId: deviceId, Day: modelTriggerTime.Format("2006-01-02")})
		if err != nil {
			log.CtxLog(c).Errorf("get cms order info, fail to GetDeviceElectricityPrice: %v, request: %s", err, ucmd.ToJsonStrIgnoreErr(request))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		type PriceRecord struct {
			ElectricityPriceInfo  []umw.ElectricityPriceInfo `bson:"map_hourly_electricity_price"`
			ElectricityPriceModel string                     `bson:"electricity_price_model"`
		}
		// 增加默认值
		var priceRecord = PriceRecord{
			ElectricityPriceInfo:  make([]umw.ElectricityPriceInfo, 0),
			ElectricityPriceModel: "one_price",
		}
		priceTag.PriceDetail
		response.Data.ElectricityPriceInfo = priceRecord.ElectricityPriceInfo
		response.Data.ElectricityPriceModel = priceRecord.ElectricityPriceModel

		// 订单信息
		randomDelay := func(n uint, err error, config *retry.Config) time.Duration {
			var interval int64 = 20000
			base := 5000 + int64(n)*interval
			sleepDuration := rand.Int63n(interval) + base
			return time.Duration(sleepDuration) * time.Millisecond
		}
		err = retry.Do(func() error {
			start := time.Now()
			requestBody := map[string]interface{}{
				"entity_code":             "swap_station",
				"entity_ids":              resourceId,
				"category_code":           "",
				"feature_codes":           "swap_station-swap_queue_info,swap_station-swap_navigation_info,swap_station-swap_serving_info",
				"response_exclude_fields": []string{"cal_time", "feature_validity"},
			}
			ct := ucmd.NewHttpClient(ucmd.HttpClient{
				URL:    fmt.Sprintf("%s/galaxy/feature/v1/feature-data/query?app_code=pe_overview&app_id=102096", a.oss.URL),
				Method: "POST",
				Header: map[string]string{
					"Content-Type": "application/json;charset=utf-8",
				},
				RequestBody: requestBody,
			})
			body, statusCode, err := ct.Do()
			if err != nil {
				log.CtxLog(c).Errorf("get cms order info, fail to get serving info, request: %+v, err: %v", request, err)
				return err
			}
			defer body.Close()
			data, _ := io.ReadAll(body)
			var servingResponse struct {
				RequestID  string `json:"request_id"`
				ServerTime int64  `json:"server_time"`
				ResultCode string `json:"result_code"`
				Data       struct {
					ResultList []struct {
						SwapStationSwapServingInfo    umw.SwapServingInfo `json:"swap_station-swap_serving_info"`
						SwapStationSwapNavigationInfo []string            `json:"swap_station-swap_navigation_info"`
						SwapStationSwapQueueInfo      []string            `json:"swap_station-swap_queue_info"`
					} `json:"result_list"`
				} `json:"data"`
			}
			if err = json.Unmarshal(data, &servingResponse); err != nil {
				msg := fmt.Sprintf("get cms order info, fail to unmarshal serving info response, request: %+v, err: %v, response: %s", request, err, string(data))
				log.CtxLog(c).Errorf(msg)
				return err
			}
			if statusCode != 200 {
				msg := fmt.Sprintf("get cms order info, serving info, status code: %d, request: %+v, err: %v, response: %s", statusCode, request, err, string(data))
				log.CtxLog(c).Errorf(msg)
				return fmt.Errorf(msg)
			}
			if servingResponse.ResultCode != "success" {
				msg := fmt.Sprintf("get cms order info, serving info, response result code: %s, request: %+v, err: %v, response: %s", servingResponse.ResultCode, request, err, string(data))
				log.CtxLog(c).Errorf(msg)
				return fmt.Errorf(msg)
			}
			if len(servingResponse.Data.ResultList) != 0 {
				response.Data.SwapServingInfo = servingResponse.Data.ResultList[0].SwapStationSwapServingInfo
				navs, err := a.navigationMutilProcess(servingResponse.Data.ResultList[0].SwapStationSwapNavigationInfo)
				if err != nil {
					msg := fmt.Sprintf("navigationMutilProcess err. err:%v", err)
					log.CtxLog(c).Errorf(msg)
					return err
				}
				response.Data.SwapNavigationInfo = navs
				response.Data.SwapQueueInfo = servingResponse.Data.ResultList[0].SwapStationSwapQueueInfo
			}
			log.CtxLog(c).Infof("query galaxy order time: %v", time.Since(start))
			return nil
		}, []retry.Option{
			retry.DelayType(randomDelay),
			retry.Attempts(6),
			retry.LastErrorOnly(true),
		}...)
		if err != nil {
			log.CtxLog(c).Errorf("get cms order info, fail to query galaxy order, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}

		// 订单预测
		// power-ai服务顶不住大qps，也不愿意加机器加缓存。只能我们加一个10-30分钟的缓存来保证服务可用，但不能保证数据时效性
		conn := udao.NewRedisConn(client.GetWatcher().Redis())
		defer conn.Close()
		redisKey := fmt.Sprintf("welkin/eps/order_predict/%s", deviceId)
		getRedis := func() error {
			jsonData, err := redis.String(conn.Do("GET", redisKey))
			if err != nil {
				return err
			}
			var predictData map[string]interface{}
			err = json.Unmarshal([]byte(jsonData), &predictData)
			if err != nil {
				return err
			}
			response.Data.PredictedService = predictData
			log.CtxLog(c).Infof("power ai predict from redis: %s", redisKey)
			return nil
		}
		setRedis := func() error {
			jsonData, err := json.Marshal(response.Data.PredictedService)
			if err != nil {
				return err
			}
			expireTime := 600 + rand.Intn(1200)
			if _, err = conn.Do("SETEX", redisKey, expireTime, jsonData); err != nil {
				return err
			}
			return nil
		}
		rErr := getRedis()
		if rErr != nil {
			log.CtxLog(c).Warnf("get cms order info, fail to get redis data, err: %v, request: %s", rErr, ucmd.ToJsonStrIgnoreErr(request))
			err = retry.Do(func() error {
				start := time.Now()
				requestBody := map[string]interface{}{
					"current_time":          fmt.Sprintf("%d", request.ModelTriggerTime/1000),
					"need_battery_type_sep": true,
					"need_prob":             true,
					"data": []map[string]string{
						{"device_id": resourceId},
					},
				}
				ct := ucmd.NewHttpClient(ucmd.HttpClient{
					URL:    fmt.Sprintf("%s/swap-hourly-order-pred/predict", a.oss.AiURL),
					Method: "POST",
					Header: map[string]string{
						"Content-Type": "application/json;charset=utf-8",
					},
					RequestBody: requestBody,
				})
				ct.Client.Timeout = time.Second * 5
				body, statusCode, err := ct.Do()
				if err != nil {
					log.CtxLog(c).Errorf("get cms order info, fail to get predict order, request: %+v, err: %v", request, err)
					return err
				}
				defer body.Close()
				data, _ := io.ReadAll(body)
				var predictResponse struct {
					ServerTime int64  `json:"server_time"`
					ResultCode string `json:"result_code"`
					Data       struct {
						Outputs []struct {
							PredictedService map[string]interface{} `json:"demand_num"`
						} `json:"outputs"`
					} `json:"data"`
				}
				if err = json.Unmarshal(data, &predictResponse); err != nil {
					msg := fmt.Sprintf("get cms order info, fail to unmarshal predict order response, request: %+v, err: %v, response: %s", request, err, string(data))
					log.CtxLog(c).Errorf(msg)
					return err
				}
				if statusCode != 200 {
					msg := fmt.Sprintf("get cms order info, predict order status code: %d, request: %+v, err: %v, response: %s", statusCode, request, err, string(data))
					log.CtxLog(c).Errorf(msg)
					return fmt.Errorf(msg)
				}
				if predictResponse.ResultCode != "success" {
					msg := fmt.Sprintf("get cms order info, predict order response result code: %s, request: %+v, err: %v, response: %s", predictResponse.ResultCode, request, err, string(data))
					log.CtxLog(c).Errorf(msg)
					return fmt.Errorf(msg)
				}
				if len(predictResponse.Data.Outputs) != 0 {
					response.Data.PredictedService = predictResponse.Data.Outputs[0].PredictedService
				}
				log.CtxLog(c).Infof("power ai predict time: %v", time.Since(start))
				return nil
			}, []retry.Option{
				retry.DelayType(randomDelay),
				retry.Attempts(10),
				retry.LastErrorOnly(true),
			}...)

			if err != nil {
				log.CtxLog(c).Errorf("get cms order info, fail to query power ai, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			} else {
				// 写入redis
				rErr := setRedis()
				if rErr != nil {
					log.Logger.Errorf("set cms order info key, fail to setex redis key: %s, err: %v", redisKey, err)
				}
			}
		}

		getOpResult := func() error {
			// 只拿最近一天的数据，且mongo纪录数量是：电池种类数量*24
			batteryTypes := []string{"70", "100"}
			var lastResult []struct {
				Datetime string `bson:"_id"`
			}
			pipeline := mongo.Pipeline{
				bson.D{{"$match", bson.D{{"resource_id", resourceId}}}},
				bson.D{{"$group", bson.M{"_id": "$datetime", "count": bson.M{"$sum": 1}}}},
				bson.D{{"$match", bson.D{{"count", len(batteryTypes) * 24}}}},
				bson.D{{"$sort", bson.D{{"_id", -1}}}},
				bson.D{{"$limit", 1}},
			}
			mErr := client.GetWatcher().Mongodb().NewMongoEntry().Aggregate(umw.Algorithm, "op_model_result_v2", pipeline, &lastResult)
			if mErr != nil || len(lastResult) == 0 {
				log.CtxLog(c).Errorf("get cms order info, fail to get last op model result, err: %v, lastResult: %s, pipeline: %s", mErr, ucmd.ToJsonStrIgnoreErr(lastResult), ucmd.ToJsonStrIgnoreErr(pipeline))
				return mErr
			}
			var opResult []OPAlgorithmResult
			count, mErr := client.GetWatcher().Mongodb().NewMongoEntry(bson.D{{"resource_id", resourceId}, {"datetime", lastResult[0].Datetime}}).FindMany(umw.Algorithm, "op_model_result_v2", nil, &opResult)
			if mErr != nil || count == 0 {
				log.CtxLog(c).Errorf("get cms order info, fail to get op model result, err: %v, count: %d", mErr, count)
				return mErr
			}
			response.Data.PredictedService = map[string]interface{}{
				"total":      []int{},
				"total_prob": []float64{},
			}
			battery70 := make([]OPAlgorithmResult, 0)
			battery100 := make([]OPAlgorithmResult, 0)
			for _, v := range opResult {
				if v.BatteryType == "70" {
					battery70 = append(battery70, v)
				} else if v.BatteryType == "100" {
					battery100 = append(battery100, v)
				}
			}
			hour := time.UnixMilli(request.ModelTriggerTime).Hour()

			process := func(batteryData []OPAlgorithmResult) ([]int, []float64) {
				// model_trigger_time所在的小时排在第一个
				sort.Slice(batteryData, func(i, j int) bool {
					adjustHour := func(h int) int {
						if h >= hour {
							return h - hour
						}
						return h + (24 - hour)
					}
					return adjustHour(batteryData[i].Hour) < adjustHour(batteryData[j].Hour)
				})
				predictedService := make([]int, 0)
				predictedServiceProb := make([]float64, 0)
				for _, v := range batteryData {
					predictedService = append(predictedService, int(math.Round(v.ServiceCnt)))
					predictedServiceProb = append(predictedServiceProb, v.ProbabilityArray...)
				}
				return predictedService, predictedServiceProb
			}

			predictedService70, predictedService70Prob := process(battery70)
			response.Data.PredictedService["70"] = predictedService70
			response.Data.PredictedService["70_prob"] = predictedService70Prob

			predictedService100, predictedService100Prob := process(battery100)
			response.Data.PredictedService["100"] = predictedService100
			response.Data.PredictedService["100_prob"] = predictedService100Prob

			response.Data.PredictedServiceSource = "welkin"
			// fmt.Printf("PredictedService: %s\nlen: 70: %d, 70prob: %d, 100: %d, 100prob: %d\n", ucmd.ToJsonStrIgnoreErr(response.Data.PredictedService), len(response.Data.PredictedService.Num70), len(response.Data.PredictedService.Num70Prob), len(response.Data.PredictedService.Num100), len(response.Data.PredictedService.Num100Prob))
			return nil
		}
		// 对于新版本的算法，新增predicted_service_source字段
		// OrderPredictType: 1自动选择，2统计模型，3OSS模型
		if request.OrderPredictType == 1 {
			response.Data.PredictedServiceSource = "oss"
			opSwitch := true
			if project == umw.PowerSwap2 {
				opSwitch = (config.Cfg.Welkin.Algorithm.PowerSwap2.CMSOrderPredictVersion == 1)
			} else if project == umw.PUS3 {
				opSwitch = (config.Cfg.Welkin.Algorithm.PUS3.CMSOrderPredictVersion == 1)
			} else if project == umw.PUS4 {
				opSwitch = (config.Cfg.Welkin.Algorithm.PUS4.CMSOrderPredictVersion == 1)
			}
			// 云端开关打开时，工作日使用op算法的结果（默认打开）
			if opSwitch && cache.DateInfoCache.IsDateWorkday(time.Now().Format("20060102")) {
				if gErr := getOpResult(); gErr != nil {
					log.CtxLog(c).Errorf("get cms order info, fail to get op model result, err: %v", gErr)
				}
			}
		} else if request.OrderPredictType == 2 {
			if gErr := getOpResult(); gErr != nil {
				log.CtxLog(c).Errorf("get cms order info, fail to get op model result, err: %v", gErr)
			}
		} else if request.OrderPredictType == 3 {
			response.Data.PredictedServiceSource = "oss"
		}

		log.CtxLog(c).Infof("succeed to get cms order info, request: %+v", request)

		// 非线上环境用mock data
		if ucmd.GetEnv() != "prod" && !cache.DateInfoCache.IsDateWorkday(time.Now().Format("20060102")) {
			response.Data.PredictedService = config.TestData.CmsPredictedService
			if request.OrderPredictType == 1 {
				response.Data.PredictedServiceSource = "welkin"
			}
		}

		// 存储特征数据
		orderInfo := umw.MongoCMSOrderInfo{
			RequestId:              request.RequestId,
			Project:                project,
			DeviceId:               request.DeviceId,
			ModelTriggerTime:       request.ModelTriggerTime,
			Date:                   time.Now(),
			ServedService:          response.Data.ServedService,
			SwapServingInfo:        response.Data.SwapServingInfo,
			SwapNavigationInfo:     response.Data.SwapNavigationInfo,
			SwapQueueInfo:          response.Data.SwapQueueInfo,
			PredictedService:       response.Data.PredictedService,
			PredictedServiceSource: response.Data.PredictedServiceSource,
		}
		update := bson.M{"$set": orderInfo}
		err = a.watcher.PLCMongodb().NewMongoEntry(bson.D{{"request_id", request.RequestId}}).UpdateOne("algorithm", "cms-order-info", update, true,
			[]client.IndexOption{
				{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 30 * 24 * 3600},
				{Name: "request_id", Fields: bson.D{{"request_id", 1}}, Unique: true},
			}...,
		)
		if err != nil {
			msg := fmt.Sprintf("fail to insert order info, err: %v, request: %+v, response: %+v", err, request, response)
			log.CtxLog(c).Errorf(msg)
			um.FailWithInternalServerError(c, &response, msg)
			return
		}

		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) navigationMutilProcess(jsonStrs []string) ([]string, error) {
	res := []string{}
	for _, jsonStr := range jsonStrs {
		str, err := a.navigationProcess(jsonStr)
		if err != nil {
			return nil, err
		}
		res = append(res, str)
	}
	return res, nil
}

func (a *alg) navigationProcess(jsonStr string) (string, error) {
	var data map[string]interface{}
	err := json.Unmarshal([]byte(jsonStr), &data)
	if err != nil {
		return "", err
	}
	_, found := data["longitude"]
	if found {
		if data["longitude"] == nil {
			data["longitude"] = 0.00
		}
	}
	_, found = data["latitude"]
	if found {
		if data["latitude"] == nil {
			data["latitude"] = 0.00
		}
	}
	bytess, err := json.Marshal(data)
	if err != nil {
		return "", err
	}
	return string(bytess), nil
}

func (a *alg) StoreCMSResult() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  []map[string]any
			response struct {
				um.Base
				ModelTriggerTime []int64 `json:"model_trigger_time"`
			}
		)
		project := c.Param("project")
		if project != umw.PowerSwap2 && project != umw.PUS3 && project != umw.PUS4 {
			msg := fmt.Sprintf("store cms result, `project` %s is invalid", project)
			log.CtxLog(c).Warn(msg)
			um.FailWithBadRequest(c, &response, msg)
			return
		}
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Warnf("store cms result, parse request body, err: %s", err.Error())
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		var failRequestId []string
		for _, record := range request {
			modelTriggerTime := int64(util.ParseInt(record["model_trigger_time"]))
			if modelTriggerTime <= 0 {
				log.CtxLog(c).Errorf("store cms result, `model_trigger_time` %d is invalid, type: %T", modelTriggerTime, record["model_trigger_time"])
				um.FailWithBadRequest(c, &response, fmt.Sprintf("`model_trigger_time` %d is invalid", modelTriggerTime))
				return
			}
			response.ModelTriggerTime = append(response.ModelTriggerTime, modelTriggerTime)
			record["date"] = time.Now()
			requestId := fmt.Sprintf("%v", record["request_id"])
			err := a.watcher.PLCMongodb().NewMongoEntry(
				bson.D{{"request_id", requestId}},
			).ReplaceOne("algorithm", fmt.Sprintf("cms-op-record-%s", strings.ToLower(project)), record, true, []client.IndexOption{
				{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 30 * 24 * 3600},
				{Name: "device_ts_combine", Fields: bson.D{{"device_id", 1}, {"model_trigger_time", -1}}},
				{Name: "request_id", Fields: bson.D{{"request_id", 1}}, Unique: true},
			}...)
			if err != nil {
				log.CtxLog(c).Errorf("fail to store cms result, err: %v, record: %+v", err, record)
				failRequestId = append(failRequestId, requestId)
			}
		}
		if len(failRequestId) != 0 {
			log.CtxLog(c).Errorf("fail to store cms results, failed request_id: %v", failRequestId)
			um.FailWithInternalServerError(c, &response, fmt.Sprintf("fail to store cms results, failed request_id: %v", failRequestId))
			return
		}

		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (a *alg) ListCMSData() gin.HandlerFunc {
	return func(c *gin.Context) {
		var request struct {
			StartTime int64  `json:"start_time" form:"start_time" binding:"required"`
			EndTime   int64  `json:"end_time" form:"end_time" binding:"required"`
			QueryType string `json:"query_type" form:"query_type" binding:"required"`
		}
		var response model.AddTotalResponse
		deviceId := c.Param("device_id")
		project := c.Param("project")
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if request.EndTime-request.StartTime > 24*time.Hour.Milliseconds() {
			log.CtxLog(c).Errorf("time range too large, request: %s", ucmd.ToJsonStrIgnoreErr(request))
			um.FailWithBadRequest(c, &response, "time range too large")
			return
		}
		switch request.QueryType {
		case "cloud_op_record":
			filter := bson.D{
				{"device_id", deviceId},
				{"model_trigger_time", bson.M{"$gte": request.StartTime, "$lt": request.EndTime}},
			}
			var res []bson.M
			total, err := client.GetWatcher().PLCMongodb().NewMongoEntry(filter).FindMany(umw.Algorithm, fmt.Sprintf("cms-operation-record-%s", strings.ToLower(project)), options.Find(), &res)
			if err != nil {
				log.CtxLog(c).Errorf("fail to get cms operation record, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			response.Data = res
			response.Total = int(total)
		case "local_op_record":
			filter := bson.D{
				{"device_id", deviceId},
				{"model_trigger_time", bson.M{"$gte": request.StartTime, "$lt": request.EndTime}},
			}
			var res []bson.M
			total, err := client.GetWatcher().PLCMongodb().NewMongoEntry(filter).FindMany(umw.Algorithm, fmt.Sprintf("cms-op-record-%s", strings.ToLower(project)), options.Find(), &res)
			if err != nil {
				log.CtxLog(c).Errorf("fail to get cms op record, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			response.Data = res
			response.Total = int(total)
		case "local_order_info":
			filter := bson.D{
				{"date", bson.M{"$gte": time.UnixMilli(request.StartTime), "$lt": time.UnixMilli(request.EndTime)}},
				{"device_id", deviceId},
			}
			var res []bson.M
			total, err := client.GetWatcher().PLCMongodb().NewMongoEntry(filter).FindMany(umw.Algorithm, "cms-order-info", options.Find(), &res)
			if err != nil {
				log.CtxLog(c).Errorf("fail to get cms order info, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			response.Data = res
			response.Total = int(total)
		default:
			log.CtxLog(c).Errorf("invalid query_type, request: %s", ucmd.ToJsonStrIgnoreErr(request))
			um.FailWithBadRequest(c, &response, "invalid query_type")
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) ListEPSData() gin.HandlerFunc {
	return func(c *gin.Context) {
		var request struct {
			StartTime int64  `json:"start_time" form:"start_time"`
			EndTime   int64  `json:"end_time" form:"end_time"`
			QueryType string `json:"query_type" form:"query_type" binding:"required"`
			DeviceId  string `json:"device_id" form:"device_id"`
			RequestId string `json:"request_id" form:"request_id"`
		}
		var response model.AddTotalResponse
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if request.QueryType != "eps_input" && request.QueryType != "eps_output" {
			log.CtxLog(c).Errorf("invalid query_type, request: %s", ucmd.ToJsonStrIgnoreErr(request))
			um.FailWithBadRequest(c, &response, "invalid query_type")
			return
		}
		filter := bson.D{}
		if request.RequestId != "" {
			filter = append(filter, bson.E{"request_id", request.RequestId})
		}
		if request.DeviceId != "" && request.EndTime-request.StartTime < 24*time.Hour.Milliseconds() {
			filter = append(filter, bson.E{"device_id", request.DeviceId}, bson.E{"model_trigger_time", bson.M{"$gte": request.StartTime, "$lt": request.EndTime}})
		}
		if len(filter) == 0 {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %s", ucmd.ToJsonStrIgnoreErr(request))
			um.FailWithBadRequest(c, &response, "uri parameters are incorrect")
			return
		}
		var res []map[string]interface{}
		total, err := client.GetWatcher().Mongodb().NewMongoEntry(filter).FindMany("device_upload", request.QueryType, nil, &res)
		if err != nil {
			log.CtxLog(c).Errorf("fail to get eps data, err: %v, request: %s", err, ucmd.ToJsonStrIgnoreErr(request))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Total = int(total)
		response.Data = res
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) ListPsosTasks() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			requestData model.ListPsosTasksRequest
			response    psos.ListPsosTasksResponse
		)
		err := c.BindQuery(&requestData)
		if err != nil {
			log.CtxLog(c).Errorf("fail to parse request: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		queryCond := psos.ListTaskQueryCond{}
		if requestData.TaskId != "" {
			queryCond.TaskId = requestData.TaskId
		}
		if requestData.TaskName != "" {
			queryCond.Name = requestData.TaskName
		}
		if requestData.Status != "" {
			queryCond.Status = []string{requestData.Status}
		}
		if requestData.Creator != "" {
			queryCond.Creator = requestData.Creator
		}
		if requestData.Sort != "" {
			queryCond.OrderFieldName = requestData.Sort
			queryCond.Descending = requestData.Descending
		}
		queryCond.Limit = int64(requestData.Size)
		queryCond.Offset = int64((requestData.Page - 1) * requestData.Size)

		taskDO := psos.TaskDO{}
		taskDOs, total, err := taskDO.ListTasks(c, queryCond)
		if err != nil {
			log.CtxLog(c).Errorf("fail to list tasks: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		userIdSet := map[string]struct{}{}
		for _, task := range taskDOs {
			userIdSet[task.Creator] = struct{}{}
		}
		userAvatars, err := util.GetUserAvatar(config.Cfg, userIdSet)
		if err != nil {
			log.CtxLog(c).Errorf("fail to get user avatar: %v, user id: %s", err, ucmd.ToJsonStrIgnoreErr(userIdSet))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		taskVOs := []psos.PsosTaskVO{}
		for _, task := range taskDOs {
			taskVOs = append(taskVOs, convertPsosTaskDO2VO(task, userAvatars))
		}
		response.Data = taskVOs
		response.Total = total
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (a *alg) QueryPsosTaskName() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.Response
		)
		taskName := c.Query("task_name")
		filter := bson.D{bson.E{Key: "name", Value: bson.M{"$regex": fmt.Sprintf(".*%v.*", taskName)}}}
		taskByteData, err := client.GetWatcher().Mongodb().NewMongoEntry(filter).ListAll(umw.Algorithm, psos.CollectionTasks, client.Ordered{Key: "create_ts", Descending: true})
		if err != nil {
			log.CtxLog(c).Errorf("fail to list psos tasks: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var tasks []mongo_model.MongoPsosTask
		if err = json.Unmarshal(taskByteData, &tasks); err != nil {
			log.CtxLog(c).Errorf("fail to unmarshal psos task: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		nameList := []string{}
		for _, task := range tasks {
			nameList = append(nameList, task.Name)
		}
		response.Data = nameList
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (a *alg) QueryPsosConfigId() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.Response
		)
		filter := bson.D{}
		taskId := c.Query("task_id")
		if taskId != "" {
			filter = append(filter, bson.E{Key: "task_id", Value: bson.M{"$in": []string{taskId}}})
		}
		fuzzyConfigId := c.Query("fuzzy_config_id")
		cursor, err := client.GetWatcher().Mongodb().Client.Database(umw.Algorithm).Collection(psos.CollectionSimulations).Aggregate(c, mongo.Pipeline{
			bson.D{{"$match", filter}},
			bson.D{{"$group", bson.M{
				"_id": "$config_id",
			}}},
			bson.D{{"$project", bson.M{
				"config_id": "$_id",
			}}},
			bson.D{{"$match", bson.D{bson.E{Key: "config_id", Value: bson.M{"$regex": fmt.Sprintf(".*%v.*", fuzzyConfigId)}}}}},
		})
		if err != nil {
			log.CtxLog(c).Errorf("fail to get config id: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var aggreValues []struct {
			ConfigId string `json:"config_id" bson:"config_id"`
		}
		if err = cursor.All(c, &aggreValues); err != nil {
			log.CtxLog(c).Errorf("fail to get config id: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		res := []string{}
		for _, aggreValue := range aggreValues {
			res = append(res, aggreValue.ConfigId)
		}
		response.Data = res
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) ListPsosSimulations() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			requestData model.ListPsosSimulationRequest
			response    psos.ListPsosSimulationResponse
		)
		err := c.BindQuery(&requestData)
		if err != nil {
			log.CtxLog(c).Errorf("fail to ListPsosSimulations: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		cond := psos.ListSimulationQueryCond{}
		if requestData.SimulationId != "" {
			cond.SimulationId = requestData.SimulationId
		}
		if requestData.TaskId != "" {
			cond.TaskId = requestData.TaskId
		}
		if requestData.ConfigId != "" {
			cond.ConfigId = requestData.ConfigId
		}
		if requestData.Sort != "" {
			cond.OrderFieldName = requestData.Sort
			cond.Descending = requestData.Descending
		}
		if requestData.Size <= 0 || requestData.Size > 100 {
			requestData.Size = 100
		}
		if requestData.Page <= 0 {
			requestData.Page = 1
		}
		cond.Limit = int64(requestData.Size)
		cond.Offset = int64((requestData.Page - 1) * requestData.Size)
		simulationDO := psos.SimulationDO{}
		simulationDOs, total, err := simulationDO.ListSimulation(c, cond)
		if err != nil {
			log.CtxLog(c).Errorf("fail to ListPsosSimulations: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		simulationVOs := []psos.PsosSimulationVO{}
		for _, simulation := range simulationDOs {
			simulationVOs = append(simulationVOs, convertPsosSimulationDO2VO(simulation))
		}
		response.Data = simulationVOs
		response.Total = total
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (a *alg) GetPsosSimulationById() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response psos.GetPsosSimulationByIdResponse
		)
		simulationId := c.Param("simulation_id")
		simulationDO := psos.SimulationDO{}
		simulation, err := simulationDO.GetSimulationById(c, simulationId)
		if err != nil {
			log.CtxLog(c).Errorf("fail to GetSimulationById: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		simulationVO := convertPsosSimulationDO2VO(simulation)
		response.Data = simulationVO
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (a *alg) GetPsosTaskGraphData() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response psos.GetPsosTaskGraphDataResponse
			err      error
		)
		taskId := c.Param("task_id")
		configId := c.Query("config_id")

		if configId != "" {
			response, err = a.getPsosTaskGraphDataByConfigId(c, taskId, configId)
		} else {
			response, err = a.getPsosTaskGraphData(c, taskId)
		}
		if err != nil {
			log.CtxLog(c).Errorf("fail to getPsosTaskGraphData: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (a *alg) getPsosTaskGraphDataByConfigId(c *gin.Context, taskId, configId string) (psos.GetPsosTaskGraphDataResponse, error) {
	var (
		response psos.GetPsosTaskGraphDataResponse
	)
	taskDO := psos.TaskDO{}
	task, err := taskDO.GetTaskById(c, taskId)
	if err != nil {
		log.CtxLog(c).Errorf("fail to GetTaskById: %v", err)
		return response, err
	}

	simulationDO := psos.SimulationDO{}
	simulations, _, err := simulationDO.ListSimulation(c, psos.ListSimulationQueryCond{
		TaskId:   taskId,
		ConfigId: configId,
		Limit:    math.MaxInt,
	})
	if err != nil {
		log.CtxLog(c).Errorf("fail to ListSimulation: %v", err)
		return response, err
	}
	baseQueueTime := float64(0)
	queueTimeData := []psos.PsosTaskGraphFloatData{}
	baseChargCost := float64(0)
	chargCostData := []psos.PsosTaskGraphFloatData{}
	baseCapacityUtilization := float64(0)
	capacityUtilizationData := []psos.PsosTaskGraphFloatData{}
	for _, simulation := range simulations {
		if simulation.IsBaseConfig {
			baseQueueTime = util.Second2Min(simulation.UserExperience.SwapService.AvgSwappingQueuetime)
			baseChargCost = simulation.BusinessCalculation.Cost.BatteryElectricityCost
			baseCapacityUtilization = simulation.DevicePerformance.Power.CapacityFactor
		}
		simulationVO := convertPsosSimulationDO2VO(simulation)
		xValue := fmt.Sprintf("[%v,%v,%v,%v,%v,%v]", simulationVO.Battery50KwCount, simulationVO.Battery75KwCount, simulationVO.Battery100KwCount, simulationVO.Battery150KwCount, simulationVO.Battery60KwCount, simulationVO.Battery85KwCount)
		queueTimeData = append(queueTimeData, psos.PsosTaskGraphFloatData{
			xValue, util.Second2Min(simulationVO.AvgSwappingQueueTime),
		})
		chargCostData = append(chargCostData, psos.PsosTaskGraphFloatData{
			xValue, simulationVO.BatteryElectricityCost,
		})
		capacityUtilizationData = append(capacityUtilizationData, psos.PsosTaskGraphFloatData{
			xValue, simulationVO.AvgCapacityUtilization,
		})
	}
	sort.Slice(queueTimeData, func(i, j int) bool {
		return queueTimeData[i].YValue < queueTimeData[j].YValue
	})
	sort.Slice(chargCostData, func(i, j int) bool {
		return chargCostData[i].YValue < chargCostData[j].YValue
	})
	sort.Slice(capacityUtilizationData, func(i, j int) bool {
		return capacityUtilizationData[i].YValue > capacityUtilizationData[j].YValue
	})
	MaxLen := 50
	if len(queueTimeData) > MaxLen {
		queueTimeData = queueTimeData[0:MaxLen]
	}
	if len(chargCostData) > MaxLen {
		chargCostData = chargCostData[0:MaxLen]
	}
	if len(capacityUtilizationData) > MaxLen {
		capacityUtilizationData = capacityUtilizationData[0:MaxLen]
	}

	taskGraphData := psos.PsosTaskGraphData{
		BaseQueueTime:               baseQueueTime,
		BaseChargeCost:              baseChargCost,
		BaseCapacityUtilization:     baseCapacityUtilization,
		AvgQueueTimeGraph:           queueTimeData,
		ChargeCostGraph:             chargCostData,
		AvgCapacityUtilizationGraph: capacityUtilizationData,
	}

	// 组装fms 压缩情况
	_, found := task.FmsCompressTaskInfo.ConfigFmsCompressTaskInfo[configId]
	if found {
		fmsTaskIds := []string{
			task.FmsCompressTaskInfo.ConfigFmsCompressTaskInfo[configId].ServiceCompressTaskId,
			task.FmsCompressTaskInfo.ConfigFmsCompressTaskInfo[configId].DeviceCompressTaskId,
			task.FmsCompressTaskInfo.ConfigFmsCompressTaskInfo[configId].BatteryCompressTaskId,
		}
		fliter := bson.D{bson.E{Key: "task_id", Value: bson.M{"$in": fmsTaskIds}}}
		allBytedata, err := client.GetWatcher().Mongodb().NewMongoEntry(fliter).ListAll(mongo_model.FmsDataBaseName, mongo_model.FmsTaskStatusCollectionName, client.Ordered{})
		if err != nil {
			log.CtxLog(c).Errorf("fail to ListAll fms status: %v", err)
			return response, err
		}
		var fmsCompressTaskInfo []mongo_model.FmsTaskStatus
		err = json.Unmarshal(allBytedata, &fmsCompressTaskInfo)
		if err != nil {
			log.CtxLog(c).Errorf("fail to json.Unmarshal: %v", err)
			return response, err
		}
		fmsTaskIdStatusMap := map[string]string{}
		for _, compressTaskInfo := range fmsCompressTaskInfo {
			fmsTaskIdStatusMap[compressTaskInfo.TaskId] = compressTaskInfo.Status
		}
		configFms := task.FmsCompressTaskInfo.ConfigFmsCompressTaskInfo[configId]
		status, ok := fmsTaskIdStatusMap[configFms.DeviceCompressTaskId]
		if ok {
			taskGraphData.FmsCompressInfo.DeviceCompressTaskStatus = status
			taskGraphData.FmsCompressInfo.DeviceResultUrl = task.FmsCompressTaskInfo.ConfigFmsCompressTaskInfo[configId].DeviceResultUrl
		}
		status, ok = fmsTaskIdStatusMap[configFms.ServiceCompressTaskId]
		if ok {
			taskGraphData.FmsCompressInfo.ServiceCompressTaskStatus = status
			taskGraphData.FmsCompressInfo.ServiceResultUrl = task.FmsCompressTaskInfo.ConfigFmsCompressTaskInfo[configId].ServiceResultUrl
		}
		status, ok = fmsTaskIdStatusMap[configFms.BatteryCompressTaskId]
		if ok {
			taskGraphData.FmsCompressInfo.BatteryCompressTaskStatus = status
			taskGraphData.FmsCompressInfo.BatteryResultUrl = task.FmsCompressTaskInfo.ConfigFmsCompressTaskInfo[configId].BatteryResultUrl
		}
	}

	response.Data = taskGraphData
	return response, nil
}

func (a *alg) getPsosTaskGraphData(c *gin.Context, taskId string) (psos.GetPsosTaskGraphDataResponse, error) {
	var (
		response psos.GetPsosTaskGraphDataResponse
	)
	simulationDO := psos.SimulationDO{}
	simulations, _, err := simulationDO.ListSimulation(c, psos.ListSimulationQueryCond{
		TaskId: taskId,
		Limit:  math.MaxInt,
	})
	if err != nil {
		log.CtxLog(c).Errorf("fail to ListSimulation: %v", err)
		return response, err
	}

	queueTimeData := []psos.PsosTaskGraphFloatData{}
	chargCostData := []psos.PsosTaskGraphFloatData{}
	capacityUtilizationData := []psos.PsosTaskGraphFloatData{}
	for _, simulation := range simulations {
		if !simulation.IsBaseConfig {
			continue
		}
		simulationVO := convertPsosSimulationDO2VO(simulation)
		xValue := simulation.ConfigId
		queueTimeData = append(queueTimeData, psos.PsosTaskGraphFloatData{
			xValue, util.Second2Min(simulationVO.AvgSwappingQueueTime),
		})
		chargCostData = append(chargCostData, psos.PsosTaskGraphFloatData{
			xValue, simulationVO.BatteryElectricityCost,
		})
		capacityUtilizationData = append(capacityUtilizationData, psos.PsosTaskGraphFloatData{
			xValue, simulationVO.AvgCapacityUtilization,
		})
	}
	sort.Slice(queueTimeData, func(i, j int) bool {
		return queueTimeData[i].YValue < queueTimeData[j].YValue
	})
	sort.Slice(chargCostData, func(i, j int) bool {
		return chargCostData[i].YValue < chargCostData[j].YValue
	})
	sort.Slice(capacityUtilizationData, func(i, j int) bool {
		return capacityUtilizationData[i].YValue > capacityUtilizationData[j].YValue
	})
	MaxLen := 50
	if len(queueTimeData) > MaxLen {
		queueTimeData = queueTimeData[0:MaxLen]
	}
	if len(chargCostData) > MaxLen {
		chargCostData = chargCostData[0:MaxLen]
	}
	if len(capacityUtilizationData) > MaxLen {
		capacityUtilizationData = capacityUtilizationData[0:MaxLen]
	}
	taskGraphData := psos.PsosTaskGraphData{
		AvgQueueTimeGraph:           queueTimeData,
		ChargeCostGraph:             chargCostData,
		AvgCapacityUtilizationGraph: capacityUtilizationData,
	}
	response.Data = taskGraphData
	return response, nil
}

func (a *alg) StopPsosTask() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			requestData model.StopPsosTaskRequest
			response    model.Response
		)
		err := c.BindJSON(&requestData)
		if err != nil {
			log.CtxLog(c).Errorf("fail to StopPsosTask: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		userId := ""
		userId, err = c.Cookie("user_id")
		if err != nil {
			userId = c.GetHeader("X-User-ID")
		}

		taskDO := psos.TaskDO{
			Id: requestData.TaskId,
		}
		task, err := taskDO.GetTaskById(c, requestData.TaskId)
		if err != nil {
			log.CtxLog(c).Errorf("fail to StopPsosTask: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if task.Creator != userId {
			log.CtxLog(c).Errorf("you are %v, can not stop %v's task", userId, task.Creator)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("you are %v, can not stop %v's task", userId, task.Creator))
			return
		}
		err = taskDO.StopTask(c)
		if err != nil {
			log.CtxLog(c).Errorf("fail to StopPsosTask: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func convertPsosSimulationDO2VO(do *psos.SimulationDO) psos.PsosSimulationVO {
	battery50KwCount := int64(0)
	battery75KwCount := int64(0)
	battery100KwCount := int64(0)
	battery150KwCount := int64(0)
	battery60KwCount := int64(0)
	battery85KwCount := int64(0)
	deviceId := do.DeviceInfo.DeviceId
	description := ""
	if deviceId != "" {
		deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(deviceId)
		if found {
			description = deviceInfo.Description
		}
	}
	if len(do.DeviceInfo.OperationStrategyInfo.BatteryTypeConf) >= 4 {
		battery50KwCount = int64(do.DeviceInfo.OperationStrategyInfo.BatteryTypeConf[0])
		battery75KwCount = int64(do.DeviceInfo.OperationStrategyInfo.BatteryTypeConf[1])
		battery100KwCount = int64(do.DeviceInfo.OperationStrategyInfo.BatteryTypeConf[2])
		battery150KwCount = int64(do.DeviceInfo.OperationStrategyInfo.BatteryTypeConf[3])
	}
	if len(do.DeviceInfo.OperationStrategyInfo.BatteryTypeConf) >= 6 {
		battery60KwCount = int64(do.DeviceInfo.OperationStrategyInfo.BatteryTypeConf[4])
		battery85KwCount = int64(do.DeviceInfo.OperationStrategyInfo.BatteryTypeConf[5])
	}
	psosSimulationConfigDetail := psos.ConfigDetail{
		IsRealDevice:       do.IsRealDevice,
		Description:        description,
		StartTs:            do.SimulationInfo.SimulationStartTime,
		EndTs:              do.SimulationInfo.SimulationStartTime + do.SimulationInfo.SimulationPeriod,
		StationCapacity:    do.DeviceInfo.OperationStrategyInfo.PowerDistributionCapacity,
		CmsSwitch:          int(do.DeviceInfo.OperationStrategyInfo.CmsStrategySwitch.SwitchValue),
		OperationStartHour: do.DeviceInfo.OperationStrategyInfo.OperationTime[0],
		OperationEndHour:   do.DeviceInfo.OperationStrategyInfo.OperationTime[1],
		NotfullySwapSwitch: psos.NotfullySwapSwitch{
			SwitchValue:   int(do.DeviceInfo.OperationStrategyInfo.NotfullySwapSwitch.SwitchValue),
			SocLowerLimit: do.DeviceInfo.OperationStrategyInfo.BatterySocLowerLimit,
			SocUpperLimit: do.DeviceInfo.OperationStrategyInfo.BatterySocUpperLimit,
		},
		SilentModeSwitch:      int(do.DeviceInfo.OperationStrategyInfo.SilentModeSwitch.SwitchValue),
		BatteryExchangeSwitch: int(do.DeviceInfo.OperationStrategyInfo.BatteryExchangeSwitch.SwitchValue),
		BatteryRestSwitch: psos.BatteryRestSwitch{
			SwitchValue:              int(do.DeviceInfo.OperationStrategyInfo.BatteryRestSwitch.SwitchValue),
			DefaultRestCurrent:       do.DeviceInfo.OperationStrategyInfo.BatteryRestSwitch.DefaultRestCurrent,
			DefaultHangingDuration:   do.DeviceInfo.OperationStrategyInfo.BatteryRestSwitch.DefaultHangingDuration,
			DefaultHangingStep:       do.DeviceInfo.OperationStrategyInfo.BatteryRestSwitch.DefaultHangingStep,
			DefaultHangingCurrentMax: do.DeviceInfo.OperationStrategyInfo.BatteryRestSwitch.DefaultHangingCurrentMax,
		},
	}
	if do.DeviceInfo.Platform == psos.PlatformPS2 {
		psosSimulationConfigDetail.Project = umw.PowerSwap2
	} else if do.DeviceInfo.Platform == psos.PlatformPUS3 {
		psosSimulationConfigDetail.Project = umw.PUS3
	} else if do.DeviceInfo.Platform == psos.PlatformPUS4 {
		psosSimulationConfigDetail.Project = umw.PUS4
	}

	if len(do.DeviceInfo.OperationStrategyInfo.CircuitDistributionCapacity) >= 2 {
		psosSimulationConfigDetail.Circuit1Capacity = do.DeviceInfo.OperationStrategyInfo.CircuitDistributionCapacity[0]
		psosSimulationConfigDetail.Circuit2Capacity = do.DeviceInfo.OperationStrategyInfo.CircuitDistributionCapacity[1]
	}

	psosBatteryMongoInfos := []mongo_model.PsosBatteryInfo{}
	for _, info := range do.DeviceInfo.OperationStrategyInfo.BatteryInfo {
		psosBatteryMongoInfos = append(psosBatteryMongoInfos, mongo_model.PsosBatteryInfo{
			SlotId:             info.SlotId,
			BatteryId:          info.BatteryId,
			BatteryRatedKwh:    info.BatteryRatedKwh,
			BatterySoc:         info.BatterySoc,
			ChargingStopSoc:    info.ChargingStopSoc,
			PackMaxTemperature: info.PackMaxTemperature,
			BatteryOwnership:   info.BatteryOwnership,
		})
	}
	psosSimulationConfigDetail.BatteryInfo = psosBatteryMongoInfos

	mongoPsosServiceInfos := []mongo_model.SwappingUser{}
	for _, swappingUser := range do.ServiceInfo.SwappingUserList {
		mongoPsosServiceInfos = append(mongoPsosServiceInfos, mongo_model.SwappingUser{
			ServiceId:             swappingUser.ServiceId,
			VehicleId:             swappingUser.VehicleId,
			UserArrivalTime:       swappingUser.UserArrivalTime,
			TargetBatteryRatedKwh: swappingUser.TargetBatteryRatedKwh,
			BatteryId:             swappingUser.BatteryId,
			BatterySoc:            swappingUser.BatterySoc,
			BatteryRatedKwh:       swappingUser.BatteryRatedKwh,
			PackMaxTemperature:    swappingUser.PackMaxTemperature,
			BatteryOwnership:      swappingUser.BatteryOwnership,
			UserOwnership:         swappingUser.UserOwnership,
			BatteryRestLabel:      swappingUser.BatteryRestLabel,
		})
	}
	psosSimulationConfigDetail.ServiceList = mongoPsosServiceInfos

	psosSimulationConfigDetail.ElectricityPriceModel = do.ScenarioInfo.ElectricityPriceModel

	electricityDetailUserConfigs := []mongo_model.ElectricityDetailUserConfig{}
	for _, userConfig := range do.ScenarioInfo.ElectricityDetailUser {
		electricityDetailUserConfigs = append(electricityDetailUserConfigs, mongo_model.ElectricityDetailUserConfig{
			Start: userConfig.Start,
			End:   userConfig.End,
			Price: userConfig.Price,
		})
	}
	psosSimulationConfigDetail.ElectricityDetailList = electricityDetailUserConfigs

	psosSimulationReport := psos.PsosSimulationReport{
		AvgSwappingQueueTime:   util.Second2Min(do.UserExperience.SwapService.AvgSwappingQueuetime),
		BatteryElectricityCost: do.BusinessCalculation.Cost.BatteryElectricityCost,
		AvgCapacityUtilization: do.DevicePerformance.Power.CapacityFactor,
		CmsSwitch:              int(do.DeviceInfo.OperationStrategyInfo.CmsStrategySwitch.SwitchValue),
		ElectricityPriceModel:  do.ScenarioInfo.ElectricityPriceModel,
		OperationStartHour:     do.DeviceInfo.OperationStrategyInfo.OperationTime[0],
		OperationEndHour:       do.DeviceInfo.OperationStrategyInfo.OperationTime[1],
		Project:                psosSimulationConfigDetail.Project,
		ContributionMargin:     do.BusinessCalculation.MarginalContributionMargin,
		SimulationDuration:     do.SimulationInfo.SimulationPeriod,
	}
	UserReport := psos.PsosUserReport{
		DownloadUrl: do.ServiceResultUrl,
	}
	UserReport.PowerSwap = psos.PowerSwap{
		ActualServiceCount:   int(do.UserExperience.SwapService.SwappedServiceNums),
		EstimateServiceCount: int(do.UserExperience.SwapService.SwappingServiceNums),
		WaitTime: map[string]psos.WaitTime{
			"avg": {
				TotalWaitTime:   util.Second2Min(do.UserExperience.SwapService.AvgSwappingQueuetime),
				WaitBatteryTime: util.Second2Min(do.UserExperience.SwapService.AvgSwappingQueuetimeBattery),
				WaitUserTime:    util.Second2Min(do.UserExperience.SwapService.AvgSwappingQueuetimeUser),
			},
			"p_90": {
				TotalWaitTime:   util.Second2Min(do.UserExperience.SwapService.SwappingQueuetime),
				WaitBatteryTime: util.Second2Min(do.UserExperience.SwapService.SwappingQueuetimeBattery),
				WaitUserTime:    util.Second2Min(do.UserExperience.SwapService.SwappingQueuetimeUser),
			},
		},
	}
	UserReport.Charge = psos.Charge{
		ActualServiceCount:   0,
		EstimateServiceCount: 0,
		WaitTime: map[string]psos.WaitTime{
			"avg":  {},
			"p_90": {},
		},
	}

	userGraphData := []psos.UserExperienceGraph{}
	if len(do.UserExperience.SwapService.HourlySwappingServiceNums) == 24 &&
		len(do.UserExperience.SwapService.HourlyAvgSwappingQueuetime) == 24 {
		for i, serviceNum := range do.UserExperience.SwapService.HourlySwappingServiceNums {
			userGraphData = append(userGraphData, psos.UserExperienceGraph{
				Hour:         i,
				ServiceNum:   int(serviceNum),
				AvgQueueTime: util.Second2Min(do.UserExperience.SwapService.HourlyAvgSwappingQueuetime[i]),
			})
		}
	}
	UserReport.GraphData = userGraphData

	swapStationBatteryReport := psos.SwapStationBatteryReport{
		DownloadUrl: do.BatteryResultUrl,
		Cost: psos.Cost{
			TotalCost:                        do.BusinessCalculation.Cost.TotalCost,
			StationInnerBatteryChargeCost:    do.BusinessCalculation.Cost.BatteryElectricityCost,
			StationOutChargingPileChargeCost: 0,
			PowerConsumptionLoopCost:         do.BusinessCalculation.Cost.NonChargingElectricityCost,
		},
		Income: psos.Income{
			TotalIncome:            do.BusinessCalculation.Income.TotalIncome,
			PowerSwapServiceIncome: do.BusinessCalculation.Income.SwapServiceIncome,
			ChargeServiceIncome:    0,
			GridIncome:             do.BusinessCalculation.Income.OffPeakIncome,
		},
	}
	batteryGraphData := []psos.BatteryGraph{}
	if len(do.BusinessCalculation.Cost.HourlyBatteryChargingNums) == 24 &&
		len(do.BusinessCalculation.Cost.HourlyBatteryElectricityCost) == 24 {
		for i, chargingNum := range do.BusinessCalculation.Cost.HourlyBatteryChargingNums {
			batteryGraphData = append(batteryGraphData, psos.BatteryGraph{
				Hour:       i,
				ChargeNum:  chargingNum,
				ChargeCost: do.BusinessCalculation.Cost.HourlyBatteryElectricityCost[i],
			})
		}
	}
	swapStationBatteryReport.GraphData = batteryGraphData

	deviceReport := psos.DeviceReport{
		DownloadUrl: do.DeviceResultUrl,
		EnergyEfficiency: psos.EnergyEfficiency{
			Efficiency:                      do.DevicePerformance.EnergyEfficiency.EnergyEfficiency,
			TotalDeviceEfficiency:           do.DevicePerformance.EnergyEfficiency.DeviceElectricityConsumption,
			PowerDistributionLoopEfficiency: do.DevicePerformance.EnergyEfficiency.NonChargingElectricityConsumption,
			StationBatteryChargeAmount:      do.DevicePerformance.EnergyEfficiency.ModuleElectricityConsumption,
			SCTChargingPileChargeAmount:     do.DevicePerformance.EnergyEfficiency.SctElectricityConsumption,
		},
		Capacity: psos.Capacity{
			CapacityFactor: do.DevicePerformance.Power.CapacityFactor,
			RatedCapacity:  do.DevicePerformance.Power.RatedPower,
			LoadFactor:     do.DevicePerformance.Power.LoadFactor,
			MaxCapacity:    do.DevicePerformance.Power.MaxPower,
			AvgCapacity:    do.DevicePerformance.Power.AvgPower,
		},
		Module: psos.Module{
			ModuleRatio:              do.DevicePerformance.Module.ModuleUtilizationRatio,
			ModuleAmount:             int(do.DevicePerformance.Module.ModuleNums),
			ModuleRealRunTime:        do.DevicePerformance.Module.SingleModuleActualWorkTime,
			ModuleTheoreticalRunTime: do.DevicePerformance.Module.SingleModuleRatedWorkTime,
			ModulePower:              do.DevicePerformance.Module.SingleModulePowerLimit,
		},
	}
	deviceGraphData := []psos.DeviceGraph{}
	if len(do.DevicePerformance.Power.HourlyCapacityFactor) == 24 &&
		len(do.DevicePerformance.Power.HourlyLoadFactor) == 24 {
		for i, f := range do.DevicePerformance.Power.HourlyCapacityFactor {
			deviceGraph := psos.DeviceGraph{
				Hour:           i,
				CapacityFactor: f,
				LoadFactor:     do.DevicePerformance.Power.HourlyLoadFactor[i],
			}
			if len(do.DevicePerformance.Module.HourlyModuleUtilizationRatio) == len(do.DevicePerformance.Power.HourlyCapacityFactor) {
				deviceGraph.ModuleRate = do.DevicePerformance.Module.HourlyModuleUtilizationRatio[i]
			}
			deviceGraphData = append(deviceGraphData, deviceGraph)
		}
	}
	deviceReport.GraphData = deviceGraphData

	psosSimulationReport.UserReport = UserReport
	psosSimulationReport.SwapStationBatteryReport = swapStationBatteryReport
	psosSimulationReport.DeviceReport = deviceReport
	vo := psos.PsosSimulationVO{
		SimulationId:           do.Id,
		TaskId:                 do.TaskId,
		ConfigId:               do.ConfigId,
		Status:                 do.Status,
		IsRealDevice:           do.IsRealDevice,
		Description:            description,
		DeviceResultUrl:        do.DeviceResultUrl,
		BatteryResultUrl:       do.BatteryResultUrl,
		ServiceResultUrl:       do.ServiceResultUrl,
		AvgSwappingQueueTime:   math.Round(do.UserExperience.SwapService.AvgSwappingQueuetime),
		AvgBatteryChargingTime: math.Round(do.BusinessCalculation.Cost.AvgBatteryChargingtime),
		AvgCapacityUtilization: do.DevicePerformance.Power.CapacityFactor,
		BatteryElectricityCost: do.BusinessCalculation.Cost.BatteryElectricityCost,
		SimulationConfig:       psosSimulationConfigDetail,
		SimulationReport:       psosSimulationReport,
		ServiceCount:           int64(do.ServiceInfo.SwappingUserNum),
		Battery50KwCount:       battery50KwCount,
		Battery75KwCount:       battery75KwCount,
		Battery100KwCount:      battery100KwCount,
		Battery150KwCount:      battery150KwCount,
		Battery60KwCount:       battery60KwCount,
		Battery85KwCount:       battery85KwCount,
		ProgressRate:           do.ProgressRate,
		StartRunTimestamp:      do.StartRunTimestamp,
		CreateTimestamp:        do.CreateTimestamp,
		UpdateTimestamp:        do.UpdateTimestamp,
	}
	return vo
}

func convertPsosTaskDO2VO(do *psos.TaskDO, userAvatars map[string]string) psos.PsosTaskVO {
	vo := psos.PsosTaskVO{
		TaskId:        do.Id,
		TaskName:      do.Name,
		Remark:        do.Remark,
		Project:       do.Project,
		Status:        do.Status,
		Creator:       do.Creator,
		CreatorAvatar: userAvatars[do.Creator],
		StatusDetail: psos.PsosSimulationTaskStatusStat{
			CreateCount:  do.CreateSimulationCount,
			RunCount:     do.RunSimulationCount,
			SuccessCount: do.SuccessSimulationCount,
			FailCount:    do.FailSimulationCount,
		},
		CreateTs: do.CreateTimestamp,
		UpdateTs: do.UpdateTimestamp,
	}
	return vo
}

func (a *alg) StorePsosResult() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			requestData model.UploadPsosResultRequest
			response    model.UploadPsosResultResponse
		)
		if err := c.ShouldBind(&requestData); err != nil {
			log.CtxLog(c).Errorf("bind param err, err: %s", err.Error())
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		response = model.UploadPsosResultResponse{
			RequestId: requestData.RequestId,
		}
		// power_swap_event_file 换电事件文件
		// charge_event_file 充电事件文件
		// charge_event_detail_file 充电明细文件
		fileURL, err := a.storePsosResultFileByFieldName(c, "device_data", requestData)
		if err != nil {
			log.CtxLog(c).Errorf("fail to store power_swap_event_file result, failed request_id: %v", requestData.RequestId)
			um.FailWithInternalServerError(c, &response, fmt.Sprintf("fail to store power_swap_event_file result, failed request_id: %v", requestData.RequestId))
			return
		}
		response.DeviceData = fileURL
		fileURL, err = a.storePsosResultFileByFieldName(c, "battery_data", requestData)
		if err != nil {
			log.CtxLog(c).Errorf("fail to store power_swap_event_file result, failed request_id: %v", requestData.RequestId)
			um.FailWithInternalServerError(c, &response, fmt.Sprintf("fail to store charge_event_file result, failed request_id: %v", requestData.RequestId))
			return
		}
		response.BatteryData = fileURL
		fileURL, err = a.storePsosResultFileByFieldName(c, "service_data", requestData)
		if err != nil {
			log.CtxLog(c).Errorf("fail to store power_swap_event_file result, failed request_id: %v", requestData.RequestId)
			um.FailWithInternalServerError(c, &response, fmt.Sprintf("fail to store charge_event_detail_file result, failed request_id: %v", requestData.RequestId))
			return
		}
		response.ServiceData = fileURL

		dto := PsosRunInfo{
			RequestId:                requestData.RequestId,
			ModelTriggerTimestamp:    requestData.ModelTriggerTimestamp,
			Status:                   "done",
			PowerSwapEventFileURL:    response.DeviceData,
			ChargeEventFileURL:       response.BatteryData,
			ChargeEventDetailFileURL: response.ServiceData,
		}
		filter := make(bson.M)
		filter["request_id"] = requestData.RequestId
		a.watcher.Mongodb().Client.Database("algorithm").Collection("psos_run_info").ReplaceOne(c, filter, dto, options.Replace().SetUpsert(true))

		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (a *alg) PsosSimulationHeartbeat() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			requestData model.PsosSimulationHearRequest
			response    model.Response
		)
		if err := c.ShouldBind(&requestData); err != nil {
			log.CtxLog(c).Errorf("bind param err, err: %s", err.Error())
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		// 仿真结果数据导出的自动任务，跳过心跳
		if strings.HasPrefix(requestData.TaskId, device_simulation.PsosAutoTask) {
			um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
			return
		}
		psosScheduler := psos_scheduler.PsosSchedulerDO{
			Watcher: a.watcher,
			Fms:     a.fms,
		}
		err := psosScheduler.ProcessHeartbeat(c, requestData.SimulationId, requestData.Progress)
		if err != nil {
			log.CtxLog(c).Errorf("process psos heart beat err, err: %s", err.Error())
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) PsosSimulationGenReport() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.Response
		simulationId := c.Query("simulation_id")
		simulationDO, err := (&psos.SimulationDO{}).GetSimulationById(c, simulationId)
		if err != nil {
			log.CtxLog(c).Errorf("fail to GetSimulationById,  err:%v simulationId: %v", err, simulationId)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		err = simulationDO.GenReport(c)
		if err != nil {
			log.CtxLog(c).Errorf("simulationDO.GenReport err,  err:%v simulationId: %v", err, simulationId)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

// TODO 移下位置
type PsosRunInfo struct {
	RequestId             string `bson:"request_id" json:"request_id"`
	Status                string `bson:"status" json:"status"`
	ModelTriggerTimestamp int64  `bson:"model_trigger_timestamp" json:"model_trigger_timestamp"`

	PowerSwapEventFileURL    string `bson:"power_swap_event_file_url" json:"power_swap_event_file_url"`
	ChargeEventFileURL       string `bson:"charge_event_file_url" json:"charge_event_file_url"`
	ChargeEventDetailFileURL string `bson:"charge_event_detail_file_url" json:"charge_event_detail_file_url"`
}

func (a *alg) storePsosResultFileByFieldName(c *gin.Context, fieldName string, req model.UploadPsosResultRequest) (string, error) {
	f, fh, err := c.Request.FormFile(fieldName)
	if err != nil {
		return "", err
	}
	fileURL, err := a.storePsosResultFile(f, fh, req)
	if err != nil {
		return "", err
	}
	return fileURL, nil
}

func (a *alg) storePsosResultFile(f multipart.File, fh *multipart.FileHeader, req model.UploadPsosResultRequest) (string, error) {
	defer f.Close()
	// 文件大小限制
	//if fh.Size > model.MAXIMAGESIZE || fh.Size < model.MINIMAGESIZE {
	//	log.CtxLog(c).Errorf("upload %s image, image_type: %v, service_id: %s, file size mismatched", appType)
	//	um.FailWithBadRequest(c, &response, "file size mismatched")
	//	return
	//}
	buffer := bytes.NewBuffer(nil)
	if _, err := io.Copy(buffer, f); err != nil {
		//larkNotify(true, fmt.Sprintf("upload alarm log, fail to write buffer, project: %s, device_id: %s, request: %+v, err: %s", project, deviceId, request, err.Error()), notifyList.Dev)
		return "", err
	}
	//date := strings.Split(util.DecodeTime(time.UnixMilli(request.AlarmTs)), " ")
	fmsFileDir := fmt.Sprintf("/algorithm/psos/")
	fmsFileName := fmt.Sprintf("%s-%s", req.RequestId, fh.Filename)
	tokenRes, tokenErr := a.fms.GetFileUploadToken(fmsFileDir, fmsFileName, model.NeedPublic, buffer.String(), ucmd.GetArea())
	if tokenErr != nil {
		return "", tokenErr
	}
	rd := tokenRes.ResultData
	rd.SupplierHttp.Header["Content-Type"] = "application/octet-stream"
	if err := a.fms.UploadFile(rd.SupplierHttp.URL, rd.SupplierHttp.Header, buffer); err != nil {
		return "", err
	}
	fileURL := ""
	for _, item := range rd.DomainInfoList {
		if item.DomainAttr.CDN {
			fileURL = fmt.Sprintf("%s://%s/%s", item.Protocol, item.Domain, rd.FileKey)
			break
		}
	}
	if fileURL == "" {
		return fileURL, errors.New("gen empty file url")
	}
	return fileURL, nil
}

func (a *alg) SAPAAlarmList() gin.HandlerFunc {
	return func(c *gin.Context) {
		response := model.SAPAAlarmListResponse{}
		deviceID := c.Query("device_id")
		pageNoStr := c.Query("page")
		pageSizeStr := c.Query("size")
		startTimeStr := c.Query("start_time")
		endTimeStr := c.Query("end_time")
		startTime, err := strconv.ParseInt(startTimeStr, 10, 64)
		if err != nil {
			log.CtxLog(c).Errorf("fail to SAPAAlarmList: %v", err)
			um.FailWithBadRequest(c, &response, "start_time invalid")
			return
		}
		endTime, err := strconv.ParseInt(endTimeStr, 10, 64)
		if err != nil {
			log.CtxLog(c).Errorf("fail to SAPAAlarmList: %v", err)
			um.FailWithBadRequest(c, &response, "end_time invalid")
			return
		}
		pageNo, err := strconv.ParseInt(pageNoStr, 10, 64)
		if err != nil {
			log.CtxLog(c).Errorf("fail to SAPAAlarmList: %v", err)
			um.FailWithBadRequest(c, &response, "page invalid")
			return
		}
		pageSize, err := strconv.ParseInt(pageSizeStr, 10, 64)
		if err != nil {
			log.CtxLog(c).Errorf("fail to SAPAAlarmList: %v", err)
			um.FailWithBadRequest(c, &response, "size invalid")
			return
		}
		deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(deviceID)
		if !found {
			log.CtxLog(c).Errorf("device id not found")
			um.FailWithBadRequest(c, &response, "device id not found")
			return
		}
		dbName := ""
		if deviceInfo.Project == "PowerSwap2" {
			dbName = "imageinfo-pus2"
		} else if deviceInfo.Project == "PUS3" {
			dbName = "imageinfo-pus3"
		} else {
			log.CtxLog(c).Errorf("this device is not pus2/pus3")
			um.FailWithBadRequest(c, &response, "this device is not pus2/pus3")
			return
		}
		filter := bson.D{
			bson.E{Key: "image_gen_time", Value: bson.M{"$gte": startTime, "$lte": endTime}},
			bson.E{Key: "image_type", Value: 21},
			bson.E{Key: "abnormal", Value: true},
		}

		byteData, totalCount, err := a.watcher.Mongodb().NewMongoEntry(filter).ListByPagination(dbName, deviceID,
			client.Pagination{
				Limit:  pageSize,
				Offset: (pageNo - 1) * pageSize,
			},
			client.Ordered{
				Key:        "image_gen_time",
				Descending: true,
			})
		if err != nil {
			log.CtxLog(c).Errorf("fail to SAPAAlarmList: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var imageInfos []umw.MongoImageInfo
		if err = json.Unmarshal(byteData, &imageInfos); err != nil {
			log.CtxLog(c).Errorf("fail to SAPAAlarmList: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		sAPAAlarmItems := []model.SAPAAlarmItem{}
		for _, info := range imageInfos {
			sAPAAlarmItems = append(sAPAAlarmItems, model.SAPAAlarmItem{
				AlarmTimeStamp: info.ImageGenTime,
				DeviceId:       info.DeviceId,
				DeviceName:     deviceInfo.Description,
				ImageUrl:       info.ImageURL,
				CityCompany:    deviceInfo.CityCompany,
			})
		}
		response.Data = sAPAAlarmItems
		response.Total = totalCount
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) DownloadSAPAAlarm() gin.HandlerFunc {
	return func(c *gin.Context) {
		deviceID := c.Query("device_id")
		pageNoStr := c.Query("page")
		pageSizeStr := c.Query("size")
		startTimeStr := c.Query("start_time")
		endTimeStr := c.Query("end_time")
		startTime, err := strconv.ParseInt(startTimeStr, 10, 64)
		if err != nil {
			log.CtxLog(c).Errorf("fail to DownloadSAPAAlarm: %v", err)
			um.FailWithBadRequest(c, &model.Response{}, "start_time invalid")
			return
		}
		endTime, err := strconv.ParseInt(endTimeStr, 10, 64)
		if err != nil {
			log.CtxLog(c).Errorf("fail to DownloadSAPAAlarm: %v", err)
			um.FailWithBadRequest(c, &model.Response{}, "end_time invalid")
			return
		}
		pageNo, err := strconv.ParseInt(pageNoStr, 10, 64)
		if err != nil {
			log.CtxLog(c).Errorf("fail to DownloadSAPAAlarm: %v", err)
			um.FailWithBadRequest(c, &model.Response{}, "page invalid")
			return
		}
		pageSize, err := strconv.ParseInt(pageSizeStr, 10, 64)
		if err != nil {
			log.CtxLog(c).Errorf("fail to DownloadSAPAAlarm: %v", err)
			um.FailWithBadRequest(c, &model.Response{}, "size invalid")
			return
		}
		deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(deviceID)
		if !found {
			log.CtxLog(c).Errorf("device id not found")
			um.FailWithBadRequest(c, &model.Response{}, "device id not found")
			return
		}
		dbName := ""
		if deviceInfo.Project == "PowerSwap2" {
			dbName = "imageinfo-pus2"
		} else if deviceInfo.Project == "PUS3" {
			dbName = "imageinfo-pus3"
		} else {
			log.CtxLog(c).Errorf("this device is not pus2/pus3")
			um.FailWithBadRequest(c, &model.Response{}, "this device is not pus2/pus3")
			return
		}
		filter := bson.D{
			bson.E{Key: "image_gen_time", Value: bson.M{"$gte": startTime, "$lte": endTime}},
			bson.E{Key: "image_type", Value: 21},
			bson.E{Key: "abnormal", Value: true},
		}

		byteData, _, err := a.watcher.Mongodb().NewMongoEntry(filter).ListByPagination(dbName, deviceID,
			client.Pagination{
				Limit:  pageSize,
				Offset: (pageNo - 1) * pageSize,
			},
			client.Ordered{
				Key:        "image_gen_time",
				Descending: true,
			})
		if err != nil {
			log.CtxLog(c).Errorf("fail to DownloadSAPAAlarm: %v", err)
			um.FailWithInternalServerError(c, &model.Response{}, err.Error())
			return
		}
		var imageInfos []umw.MongoImageInfo
		if err = json.Unmarshal(byteData, &imageInfos); err != nil {
			log.CtxLog(c).Errorf("fail to DownloadSAPAAlarm: %v", err)
			um.FailWithInternalServerError(c, &model.Response{}, err.Error())
			return
		}
		sort.Slice(imageInfos, func(i, j int) bool {
			return imageInfos[i].ImageGenTime > imageInfos[j].ImageGenTime
		})

		f := excelize.NewFile()
		index, _ := f.NewSheet("Sheet1")
		// 告警时间	城市公司	设备名称	设备ID	告警类型	图片
		f.SetCellValue("Sheet1", "A1", "告警时间")
		f.SetCellValue("Sheet1", "B1", "城市公司")
		f.SetCellValue("Sheet1", "C1", "设备名称")
		f.SetCellValue("Sheet1", "D1", "站点ID")
		f.SetCellValue("Sheet1", "E1", "图片")

		loc, _ := time.LoadLocation("Asia/Shanghai")
		for i, imageInfo := range imageInfos {
			f.SetCellValue("Sheet1", fmt.Sprintf("A%v", i+2), time.UnixMilli(imageInfo.ImageGenTime).In(loc).Format("2006-01-02 15:04:05"))
			f.SetCellValue("Sheet1", fmt.Sprintf("B%v", i+2), deviceInfo.CityCompany)
			f.SetCellValue("Sheet1", fmt.Sprintf("C%v", i+2), deviceInfo.Description)
			f.SetCellValue("Sheet1", fmt.Sprintf("D%v", i+2), deviceID)
			f.SetCellValue("Sheet1", fmt.Sprintf("E%v", i+2), imageInfo.ImageURL)
		}

		f.SetActiveSheet(index)

		c.Header("Content-Type", "application/octet-stream")
		c.Header("Content-Disposition", "attachment; filename="+fmt.Sprintf("parking_alarm_detail.xlsx"))
		c.Header("Content-Transfer-Encoding", "binary")
		f.Write(c.Writer)
	}
}

func (a *alg) SAPAStatPanel() gin.HandlerFunc {
	return func(c *gin.Context) {
		response := model.SAPAStatPanelResponse{}
		deviceID := c.Query("device_id")
		startTimeStr := c.Query("start_time")
		endTimeStr := c.Query("end_time")
		startTimestamp, err := strconv.ParseInt(startTimeStr, 10, 64)
		if err != nil {
			log.CtxLog(c).Errorf("fail to SAPAStatPanel: %v", err)
			um.FailWithBadRequest(c, &response, "start_time invalid")
			return
		}
		endTimestamp, err := strconv.ParseInt(endTimeStr, 10, 64)
		if err != nil {
			log.CtxLog(c).Errorf("fail to SAPAStatPanel: %v", err)
			um.FailWithBadRequest(c, &response, "end_time invalid")
			return
		}
		_, found := cache.PowerSwapCache.GetSingleDevice(deviceID)
		if !found {
			log.CtxLog(c).Errorf("device id not found")
			um.FailWithBadRequest(c, &response, "device id not found")
			return
		}
		timezone := "Asia/Shanghai"
		loc, err := time.LoadLocation(timezone)
		if err != nil {
			log.CtxLog(c).Errorf("fail to SAPAStatPanel: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		startTime := time.UnixMilli(startTimestamp).In(loc)
		endTime := time.UnixMilli(endTimestamp).In(loc)

		filter := bson.D{
			bson.E{Key: "device_id", Value: deviceID},
			bson.E{Key: "service_day", Value: bson.M{"$gte": startTime.Format("2006-01-02"), "$lte": endTime.Format("2006-01-02")}},
		}
		byteData, err := a.watcher.PLCMongodb().NewMongoEntry(filter).ListAll("algorithm", "sapa_parking_occupation", client.Ordered{})
		if err != nil {
			log.CtxLog(c).Errorf("fail to SAPAStatPanel: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var sapaParkingOccupationPOs []model.SapaParkingOccupationPO
		if err = json.Unmarshal(byteData, &sapaParkingOccupationPOs); err != nil {
			log.CtxLog(c).Errorf("fail to SAPAStatPanel: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		busyRateMap := map[int][]float64{}
		totalSAPACount := map[int][]int64{}
		predictOccupantCount := map[int][]int64{}
		totalOrderNum := map[int][]int64{}
		cancelOrderNum := map[int][]int64{}
		finishOrderNum := map[int][]int64{}
		interruptNum := map[int][]int64{}
		vocNum := map[int][]int64{}
		for i := 0; i <= 23; i++ {
			busyRateMap[i] = []float64{}
			totalSAPACount[i] = []int64{}
			predictOccupantCount[i] = []int64{}
			totalOrderNum[i] = []int64{}
			cancelOrderNum[i] = []int64{}
			finishOrderNum[i] = []int64{}
			interruptNum[i] = []int64{}
			vocNum[i] = []int64{}
		}

		for _, sapaParkingOccupationPO := range sapaParkingOccupationPOs {
			busyRateMap[sapaParkingOccupationPO.Hour] = append(busyRateMap[sapaParkingOccupationPO.Hour], sapaParkingOccupationPO.BusyRate)
			totalSAPACount[sapaParkingOccupationPO.Hour] = append(totalSAPACount[sapaParkingOccupationPO.Hour], sapaParkingOccupationPO.TotalSAPACount)
			predictOccupantCount[sapaParkingOccupationPO.Hour] = append(predictOccupantCount[sapaParkingOccupationPO.Hour], sapaParkingOccupationPO.SAPAPredictOccupantCount)
			totalOrderNum[sapaParkingOccupationPO.Hour] = append(totalOrderNum[sapaParkingOccupationPO.Hour], sapaParkingOccupationPO.TotalOrderNum)
			cancelOrderNum[sapaParkingOccupationPO.Hour] = append(cancelOrderNum[sapaParkingOccupationPO.Hour], sapaParkingOccupationPO.CancelOrderNum)
			finishOrderNum[sapaParkingOccupationPO.Hour] = append(finishOrderNum[sapaParkingOccupationPO.Hour], sapaParkingOccupationPO.FinishOrderNum)
			interruptNum[sapaParkingOccupationPO.Hour] = append(interruptNum[sapaParkingOccupationPO.Hour], sapaParkingOccupationPO.InterruptNum)
			vocNum[sapaParkingOccupationPO.Hour] = append(vocNum[sapaParkingOccupationPO.Hour], sapaParkingOccupationPO.VocNum)
		}
		stats := []model.SAPAStat{}
		for i := 0; i <= 23; i++ {
			busyRate := float64(0)
			if len(busyRateMap[i]) != 0 {
				busyRate = sumFloat64(busyRateMap[i]) / float64(len(busyRateMap[i]))
			}
			occupation := float64(0)
			sumSAPACount := sumInt64(totalSAPACount[i])
			if len(totalSAPACount[i]) != 0 && sumSAPACount != 0 {
				occupation = float64(sumInt64(predictOccupantCount[i])) / float64(sumInt64(totalSAPACount[i]))
			}
			totalOrder := sumInt64(totalOrderNum[i])
			cancelOrder := sumInt64(cancelOrderNum[i])
			finishOrder := sumInt64(finishOrderNum[i])
			interrupt := sumInt64(interruptNum[i])
			voc := sumInt64(vocNum[i])
			stats = append(stats, model.SAPAStat{
				Interval:       fmt.Sprintf("%v-%v", i, i+1),
				Order:          -(float64(int64(busyRate*100)) / 100),
				Occupation:     float64(int64(occupation*100)) / 100,
				TotalOrderNum:  totalOrder,
				CancelOrderNum: cancelOrder,
				FinishOrderNum: finishOrder,
				InterruptNum:   interrupt,
				VocNum:         voc,
			})
		}
		totalVocNum := int64(0)
		for _, stat := range stats {
			totalVocNum = totalVocNum + stat.VocNum
		}
		response.Data.HourlyDetail = stats
		response.Data.TotalVocNum = totalVocNum
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) DownloadSAPAStat() gin.HandlerFunc {
	return func(c *gin.Context) {
		deviceID := c.Query("device_id")
		startTimeStr := c.Query("start_time")
		endTimeStr := c.Query("end_time")
		startTimestamp, err := strconv.ParseInt(startTimeStr, 10, 64)
		if err != nil {
			log.CtxLog(c).Errorf("fail to DownloadSAPAStat: %v", err)
			um.FailWithBadRequest(c, &model.Response{}, "start_time invalid")
			return
		}
		endTimestamp, err := strconv.ParseInt(endTimeStr, 10, 64)
		if err != nil {
			log.CtxLog(c).Errorf("fail to DownloadSAPAStat: %v", err)
			um.FailWithBadRequest(c, &model.Response{}, "end_time invalid")
			return
		}
		deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(deviceID)
		if !found {
			log.CtxLog(c).Errorf("fail to DownloadSAPAStat: device id not found")
			um.FailWithBadRequest(c, &model.Response{}, "device id not found")
			return
		}
		timezone := "Asia/Shanghai"
		loc, err := time.LoadLocation(timezone)
		if err != nil {
			log.CtxLog(c).Errorf("fail to DownloadSAPAStat: %v", err)
			um.FailWithInternalServerError(c, &model.Response{}, err.Error())
			return
		}
		startTime := time.UnixMilli(startTimestamp).In(loc)
		endTime := time.UnixMilli(endTimestamp).In(loc)

		filter := bson.D{
			bson.E{Key: "device_id", Value: deviceID},
			bson.E{Key: "service_day", Value: bson.M{"$gte": startTime.Format("2006-01-02"), "$lte": endTime.Format("2006-01-02")}},
		}
		byteData, err := a.watcher.PLCMongodb().NewMongoEntry(filter).ListAll("algorithm", "sapa_parking_occupation", client.Ordered{})
		if err != nil {
			log.CtxLog(c).Errorf("fail to DownloadSAPAStat: %v", err)
			um.FailWithInternalServerError(c, &model.Response{}, err.Error())
			return
		}
		var sapaParkingOccupationPOs []model.SapaParkingOccupationPO
		if err = json.Unmarshal(byteData, &sapaParkingOccupationPOs); err != nil {
			log.CtxLog(c).Errorf("fail to DownloadSAPAStat: %v", err)
			um.FailWithInternalServerError(c, &model.Response{}, err.Error())
			return
		}
		sort.Slice(sapaParkingOccupationPOs, func(i, j int) bool {
			return fmt.Sprintf("%v%09d", sapaParkingOccupationPOs[i].ServiceDay, sapaParkingOccupationPOs[i].Hour) > fmt.Sprintf("%v%09d", sapaParkingOccupationPOs[j].ServiceDay, sapaParkingOccupationPOs[j].Hour)
		})

		f := excelize.NewFile()
		index, _ := f.NewSheet("Sheet1")
		// 日期	时间	城市公司	站点ID	站点名称	繁忙占比	总单量	取消单量	已完成单量	占位情况	泊车中断次数
		f.SetCellValue("Sheet1", "A1", "日期")
		f.SetCellValue("Sheet1", "B1", "时间")
		f.SetCellValue("Sheet1", "C1", "城市公司")
		f.SetCellValue("Sheet1", "D1", "站点ID")
		f.SetCellValue("Sheet1", "E1", "站点名称")
		f.SetCellValue("Sheet1", "F1", "繁忙占比")
		f.SetCellValue("Sheet1", "G1", "总单量")
		f.SetCellValue("Sheet1", "H1", "取消单量")
		f.SetCellValue("Sheet1", "I1", "已完成单量")
		f.SetCellValue("Sheet1", "J1", "占位情况")
		f.SetCellValue("Sheet1", "K1", "泊车中断次数")

		for i, parkingOccupationPO := range sapaParkingOccupationPOs {
			occupation := float64(0)
			if parkingOccupationPO.TotalSAPACount != 0 {
				occupation = float64(parkingOccupationPO.SAPAPredictOccupantCount) / float64(parkingOccupationPO.TotalSAPACount)
			}
			f.SetCellValue("Sheet1", fmt.Sprintf("A%v", i+2), parkingOccupationPO.ServiceDay)
			f.SetCellValue("Sheet1", fmt.Sprintf("B%v", i+2), parkingOccupationPO.Hour)
			f.SetCellValue("Sheet1", fmt.Sprintf("C%v", i+2), deviceInfo.CityCompany)
			f.SetCellValue("Sheet1", fmt.Sprintf("D%v", i+2), deviceID)
			f.SetCellValue("Sheet1", fmt.Sprintf("E%v", i+2), deviceInfo.Description)
			f.SetCellValue("Sheet1", fmt.Sprintf("F%v", i+2), parkingOccupationPO.BusyRate)
			f.SetCellValue("Sheet1", fmt.Sprintf("G%v", i+2), parkingOccupationPO.TotalOrderNum)
			f.SetCellValue("Sheet1", fmt.Sprintf("H%v", i+2), parkingOccupationPO.CancelOrderNum)
			f.SetCellValue("Sheet1", fmt.Sprintf("I%v", i+2), parkingOccupationPO.FinishOrderNum)
			f.SetCellValue("Sheet1", fmt.Sprintf("J%v", i+2), occupation)
			f.SetCellValue("Sheet1", fmt.Sprintf("K%v", i+2), parkingOccupationPO.InterruptNum)
		}

		f.SetActiveSheet(index)

		c.Header("Content-Type", "application/octet-stream")
		c.Header("Content-Disposition", "attachment; filename="+fmt.Sprintf("parking_analysis_detail.xlsx"))
		c.Header("Content-Transfer-Encoding", "binary")
		//c.File(filepath)
		f.Write(c.Writer)
	}
}

func sumFloat64(arr []float64) float64 {
	res := float64(0)
	for _, item := range arr {
		res = res + item
	}
	return res
}

func sumInt64(arr []int64) int64 {
	res := int64(0)
	for _, item := range arr {
		res = res + item
	}
	return res
}

func sumInt(arr []int) int {
	res := 0
	for _, item := range arr {
		res = res + item
	}
	return res
}

func (a *alg) BatteryEnum() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.AddTotalResponse
		project := c.Query("project")
		if project != umw.PowerSwap2 && project != umw.PUS3 && project != umw.PUS4 {
			log.CtxLog(c).Errorf("invalid project: %v", project)
			um.FailWithBadRequest(c, &response, "invalid project")
			return
		}
		lowerLimits := util.TurnStrArrToIntArr(strings.Split(c.Query("lower_limit"), ","))
		upperLimits := util.TurnStrArrToIntArr(strings.Split(c.Query("upper_limit"), ","))
		simulationBatteryNumber := len(psos.SimulateBatteryList[project])
		if len(lowerLimits) != simulationBatteryNumber || len(upperLimits) != simulationBatteryNumber {
			log.CtxLog(c).Errorf("invalid limit, lower: %s, upper: %s, project: %s", c.Query("lower_limit"), c.Query("upper_limit"), project)
			um.FailWithBadRequest(c, &response, "invalid limit")
			return
		}
		batteryConfig := util.TurnStrArrToFloatArr(strings.Split(c.Query("battery_config"), ","))
		if len(batteryConfig) < simulationBatteryNumber {
			log.CtxLog(c).Errorf("invalid battery config: %v", c.Query("battery_config"))
			um.FailWithBadRequest(c, &response, "invalid battery config")
			return
		}
		var slotCount float64
		for _, bc := range batteryConfig {
			slotCount += bc
		}
		//configCount, _ := strconv.Atoi(c.Query("config_count"))
		//if configCount == 0 {
		//	log.CtxLog(c).Errorf("invalid config count: %v", c.Query("config_count"))
		//	um.FailWithBadRequest(c, &response, "invalid config count")
		//	return
		//}
		// 获取笛卡尔积
		var batteryConfigs [][]int
		for i := range lowerLimits {
			var curr []int
			for num := lowerLimits[i]; num <= upperLimits[i]; num++ {
				curr = append(curr, num)
			}
			batteryConfigs = append(batteryConfigs, curr)
		}
		res := util.CartesianProduct(batteryConfigs...)
		// 根据生成组合和平均电池配比的加权距离排序
		sort.SliceStable(res, func(i, j int) bool {
			var sumI, sumJ float64
			for k := range batteryConfig {
				var base float64
				if slotCount == 0 {
					base = 1
				} else {
					base = batteryConfig[k] / slotCount
				}
				resI := float64(res[i][k])
				resJ := float64(res[j][k])
				sumI += math.Abs(batteryConfig[k]-resI) * base
				sumJ += math.Abs(batteryConfig[k]-resJ) * base
				//fmt.Printf("base: %f, batteryConfig: %f, resI: %f, resJ: %f, sumI: %f, sumJ: %f\n", base, batteryConfig[k], resI, resJ, sumI, sumJ)
			}
			return sumI < sumJ
		})
		resData := make([]map[string]interface{}, 0)
		idx := 1
		for _, item := range res {
			if sumInt(item) > psos.BatterySlotBatteryNum[project] {
				continue
			}
			resData = append(resData, map[string]interface{}{
				"id":             idx,
				"battery_config": item,
			})
			idx += 1
		}
		batteryConfigCount := psos.SimulationLimit
		if len(resData) < batteryConfigCount {
			batteryConfigCount = len(resData)
		}
		response.Total = batteryConfigCount
		response.Data = resData[:batteryConfigCount]
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) CalculateBatteryConfig() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.Response
		source := c.Param("source")
		project := c.Query("project")
		//if project != umw.PowerSwap2 && project != umw.PUS3 {
		//	log.CtxLog(c).Errorf("invalid project: %s", project)
		//	um.FailWithBadRequest(c, &response, "invalid project")
		//	return
		//}
		psosHandler := psos.NewHandler(a.watcher)

		var batteryConfig []float64
		var err error
		if source == "device" {
			// 通过真实设备模拟
			batteryConfig, err = psosHandler.HandleDeviceBatteryConfig(c, project)
		} else if source == "base" {
			// 通过已有配方
			configList := strings.Split(c.Query("configs"), ",")
			batteryConfig, err = psosHandler.HandleConfigBatteryConfig(c, configList, project)
		} else {
			log.CtxLog(c).Errorf("invalid source: %v", source)
			um.FailWithBadRequest(c, &response, "invalid source")
			return
		}
		if err != nil {
			log.CtxLog(c).Errorf("fail to handle battery config, err: %v, source: %s", err, source)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = batteryConfig
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) PsosSingleConfig() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  psos.PsosSingleConfigRequest
			response um.Base
		)
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("fail to get json params, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		userId := c.GetHeader("X-User-ID")
		if userId == "" {
			log.CtxLog(c).Errorf("empty user id")
			um.FailWithBadRequest(c, &response, "empty user id")
			return
		}
		configDO := psos.ConvertSingleConfigVO2DO(c, request, userId)
		ok, err := configDO.CheckConfigName(c)
		if err != nil {
			log.CtxLog(c).Errorf("fail to check config name, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if !ok {
			log.CtxLog(c).Warnf("duplicated config name, config: %s", ucmd.ToJsonStrIgnoreErr(configDO))
			um.SuccessWithErrCode(c, &response, "duplicated config name", -1)
			return
		}
		if err = configDO.GenerateSingleConfig(c); err != nil {
			log.CtxLog(c).Errorf("fail to generate single config, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) PsosBatchConfig() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  psos.PsosBatchConfigRequest
			response um.Base
		)
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("fail to get json params, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		userId := c.GetHeader("X-User-ID")
		if userId == "" {
			log.CtxLog(c).Errorf("empty user id")
			um.FailWithBadRequest(c, &response, "empty user id")
			return
		}
		configDO := psos.ConvertBatchConfigVO2DO(c, request, userId)
		ok, err := configDO.CheckConfigName(c)
		if err != nil {
			log.CtxLog(c).Errorf("fail to check config name, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if !ok {
			log.CtxLog(c).Warnf("duplicated config name, config: %s", ucmd.ToJsonStrIgnoreErr(configDO))
			um.SuccessWithErrCode(c, &response, "duplicated config name", -1)
			return
		}
		if err = configDO.GenerateBatchConfig(c); err != nil {
			log.CtxLog(c).Errorf("fail to generate batch config, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) PsosBatteryInfo() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  psos.GenerateRandomRequest
			response model.Response
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		configDO := psos.ConvertGenerateRandomVO2DO(c, request)
		batteryConfig := psos.ConvertSimulateBattery2AlgorithmBatteryList(request.Project, util.TurnStrArrToFloatArr(strings.Split(request.BatteryConfig, ",")))
		batteryInfo, err := configDO.RandomBatteryInfo(c, request.DeviceId, batteryConfig)
		if err != nil {
			log.CtxLog(c).Errorf("fail to get random battery info, err: %v, config: %s", err, ucmd.ToJsonStrIgnoreErr(configDO))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = batteryInfo
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) PsosServiceList() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  psos.GenerateRandomRequest
			response model.AddTotalResponse
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		configDO := psos.ConvertGenerateRandomVO2DO(c, request)
		batteryConfig := psos.ConvertSimulateBattery2AlgorithmBatteryList(request.Project, util.TurnStrArrToFloatArr(strings.Split(request.BatteryConfig, ",")))
		serviceCount := psos.ConvertSimulateBattery2AlgorithmBatteryList(request.Project, util.TurnStrArrToIntArr(strings.Split(request.ServiceCount, ",")))
		swapUsers, err := configDO.RandomService(c, request.DeviceId, batteryConfig, serviceCount)
		if err != nil {
			log.CtxLog(c).Errorf("fail to get random service, err: %v, config: %s", err, ucmd.ToJsonStrIgnoreErr(configDO))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = swapUsers
		response.Total = len(swapUsers)
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) ListPsosConfigs() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  psos.ListPsosConfigsRequest
			response psos.ListPsosConfigsResponse
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("parse uri params, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		util.SetURIParamDefault(&request.CommonUriParam)
		filter := make(bson.D, 0)
		if request.Creator != nil {
			filter = append(filter, bson.E{Key: "creator", Value: *request.Creator})
		}
		if request.ConfigId != nil {
			filter = append(filter, bson.E{Key: "_id", Value: bson.M{"$regex": fmt.Sprintf(".*%v.*", *request.ConfigId)}})
		}
		if request.ConfigName != nil {
			filter = append(filter, bson.E{Key: "config_name", Value: bson.M{"$regex": fmt.Sprintf(".*%v.*", *request.ConfigName)}})
		}
		if request.Project != nil {
			filter = append(filter, bson.E{Key: "project", Value: *request.Project})
		}
		opts := &options.FindOptions{}
		projection := bson.M{
			"_id":            1,
			"config_name":    1,
			"creator":        1,
			"remark":         1,
			"project":        1,
			"create_ts":      1,
			"update_ts":      1,
			"is_real_device": 1,
			"device_info":    1,
		}
		opts.SetLimit(int64(request.Size)).SetSkip(int64((request.Page - 1) * request.Size)).SetProjection(projection).SetSort(bson.D{{"date", -1}})
		var configData []psos.ConfigInfo
		total, err := a.watcher.Mongodb().NewMongoEntry(filter).FindMany(umw.Algorithm, psos.CollectionConfigs, opts, &configData)
		if err != nil {
			log.CtxLog(c).Errorf("fail to find psos configs, err: %v, filter: %s", err, ucmd.ToJsonStrIgnoreErr(filter))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		userIdSet := map[string]struct{}{}
		for _, item := range configData {
			userIdSet[item.Creator] = struct{}{}
		}
		userAvatars, err := util.GetUserAvatar(config.Cfg, userIdSet)
		if err != nil {
			log.CtxLog(c).Errorf("fail to get user avatar: %v, user id: %s", err, ucmd.ToJsonStrIgnoreErr(userIdSet))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		for i := range configData {
			configData[i].CreatorAvatar = userAvatars[configData[i].Creator]
			configData[i].DeviceId = configData[i].DeviceInfo.DeviceId
			// 返回给前端的配置名称，去掉最后的device_id
			cutoff := len(configData[i].ConfigName) - len(configData[i].DeviceInfo.DeviceId) - 1
			if cutoff > 0 {
				configData[i].ConfigName = configData[i].ConfigName[:cutoff]
			}
			if !configData[i].IsRealDevice {
				continue
			}
			deviceInfo, ok := cache.PowerSwapCache.GetSingleDevice(configData[i].DeviceInfo.DeviceId)
			if ok {
				configData[i].Description = deviceInfo.Description
			}
		}
		response.Total = total
		response.Data = configData
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) DeletePsosConfig() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response um.Base
		configId := c.Param("config_id")
		if configId == "" {
			log.CtxLog(c).Errorf("empty config id")
			um.FailWithBadRequest(c, &response, "empty config id")
			return
		}
		filter := bson.D{{"_id", configId}}
		if err := a.watcher.Mongodb().NewMongoEntry(filter).DeleteOne(umw.Algorithm, psos.CollectionConfigs); err != nil {
			log.CtxLog(c).Errorf("fail to delete psos config, err: %v, id: %s", err, configId)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) GetPsosDeviceParam() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response psos.GetConfigResponse
		project := c.Param("project")
		deviceId := c.Param("device_id")
		if project != umw.PUS3 && project != umw.PowerSwap2 && project != umw.PUS4 {
			log.CtxLog(c).Errorf("invalid project: %s", project)
			um.FailWithBadRequest(c, &response, "invalid project")
			return
		}
		if deviceId == "" {
			log.CtxLog(c).Errorf("invalid device id: %s", deviceId)
			um.FailWithBadRequest(c, &response, "invalid device id")
			return
		}
		startTs, _ := strconv.ParseInt(c.Query("start_ts"), 10, 64)
		endTs, _ := strconv.ParseInt(c.Query("end_ts"), 10, 64)
		if err := util.CheckTimeRange(startTs, endTs); err != nil {
			log.CtxLog(c).Errorf("get psos device param, invalid time range: %v, start_ts: %v, end_ts: %v", err, startTs, endTs)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		configDO := &psos.ConfigDO{
			Project: project,
			Devices: []string{deviceId},
			StartTs: startTs,
			EndTs:   endTs,
			Rng:     &util.Rand{Rand: rand.New(rand.NewSource(time.Now().UnixNano()))},
		}
		configMap, err := configDO.GetDeviceConfig(c)
		if err != nil {
			log.CtxLog(c).Errorf("get psos device param, fail to get device config, err: %v, config: %s", err, ucmd.ToJsonStrIgnoreErr(configDO))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		//deviceConfig := configDO.PrepareAutoParams(c, configMap[deviceId])
		deviceConfig := configMap[deviceId]
		// 补全电价信息
		if len(deviceConfig.ScenarioInfo.ElectricityDetailUser) == 0 {
			res, eErr := psos.CalculateElectricityList(deviceConfig.ScenarioInfo.ElectricityDetails)
			if eErr == nil {
				deviceConfig.ScenarioInfo.ElectricityDetailUser = res
			} else {
				log.CtxLog(c).Errorf("fail to calculate electricity list, err: %v, details: %s", err, ucmd.ToJsonStrIgnoreErr(deviceConfig.ScenarioInfo.ElectricityDetails))
			}
		}
		response.Data = *psos.ConvertConfigDetailPO2VO(c, &deviceConfig)
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) NewTaskByConfig() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  psos.NewTaskByConfigRequest
			response psos.NewTaskResponse
		)
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("fail to get json params, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		request.UserId = c.GetHeader("X-User-ID")
		if request.UserId == "" {
			log.CtxLog(c).Errorf("empty user id")
			um.FailWithBadRequest(c, &response, "empty user id")
			return
		}
		taskId := "task_" + xid.New().String()
		response.TaskId = taskId
		psosHandler := psos.NewHandler(a.watcher)
		ok, checkErr := psosHandler.CheckTaskName(c, request.UserId, request.TaskName)
		if checkErr != nil {
			log.CtxLog(c).Errorf("fail to check task name, err: %v", checkErr)
			um.FailWithInternalServerError(c, &response, checkErr.Error())
			return
		}
		if !ok {
			log.CtxLog(c).Warnf("duplicated task name, request: %s", ucmd.ToJsonStrIgnoreErr(request))
			um.SuccessWithErrCode(c, &response, "duplicated task name", -1)
			return
		}
		if err := psosHandler.NewPsosTaskByConfig(c, request, taskId); err != nil {
			log.CtxLog(c).Errorf("fail to run NewTaskByConfig, err: %v, request: %s, taskId: %s", err, ucmd.ToJsonStrIgnoreErr(request), taskId)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}

		// 异步启动任务
		go func() {
			defer ucmd.RecoverPanic()
			taskDO := &psos.TaskDO{}
			taskDO, err := taskDO.GetTaskById(c, taskId)
			if err != nil {
				log.CtxLog(c).Errorf("fail to get task. task id:%v err:%v", taskId, err)
				return
			}
			psosSchedulerDO := psos_scheduler.PsosSchedulerDO{
				Watcher: a.watcher,
				Fms:     a.fms,
			}
			err = psosSchedulerDO.StartTask(c, taskDO)
			if err != nil {
				log.CtxLog(c).Errorf("psosSchedulerDO.StartTask err. task id:%v err:%v", taskId, err)
			}
		}()
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) GetPsosConfig() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response psos.GetConfigResponse
		configId := c.Param("config_id")
		rawData, err := a.watcher.Mongodb().NewMongoEntry(bson.D{{"_id", configId}}).GetOne(umw.Algorithm, psos.CollectionConfigs)
		if err != nil {
			log.CtxLog(c).Errorf("fail to get psos config, err: %v, config id: %s", err, configId)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if rawData == nil {
			log.CtxLog(c).Errorf("empty psos config, config id: %s", configId)
			um.FailWithBadRequest(c, &response, "empty psos config")
			return
		}
		var psosConfig mongo_model.MongoPsosConfig
		if err = bson.Unmarshal(rawData, &psosConfig); err != nil {
			log.CtxLog(c).Errorf("fail to unmarshal psos config, err: %v, data: %s", err, string(rawData))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = *psos.ConvertConfigDetailPO2VO(c, &psosConfig)
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) PsosWatchDog() gin.HandlerFunc {
	return func(c *gin.Context) {
		psosSchedulerDO := psos_scheduler.PsosSchedulerDO{
			Watcher: client.GetWatcher(),
			Fms:     *service.GetFMS(),
		}
		go func() {
			start := time.Now()
			err := psosSchedulerDO.WatchDog(c)
			log.CtxLog(c).Infof("psosSchedulerDO.WatchDog cost:%v err:%v", time.Since(start), err)
		}()
		um.SuccessWithMessageForGin(c, &model.Response{}, "ok", http.StatusOK)
		return
	}
}

func (a *alg) PsosRunAllDevices() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  psos.SimulationAllDevicesRequest
			response model.Response
		)
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("PsosRunAllDevices, fail to get json params, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		excludeDevices := make([]string, 0)
		if request.FromCheckpoint {
			// 获取已经跑完仿真的设备列表
			day := time.UnixMilli(request.StartTs).Format("20060102")
			var res []mongo_model.PsosResult
			filter := bson.D{{"day", day}, {"charge_strategy", request.ChargeStrategy}, {"project", request.Project}}
			_, err := client.GetWatcher().Mongodb().NewMongoEntry(filter).FindMany(umw.Algorithm, psos.CollectionResult, options.Find().SetProjection(bson.M{"device_id": 1}), &res)
			if err != nil {
				log.CtxLog(c).Errorf("PsosRunAllDevices, fail to find psos result, err: %v, filter: %s", err, ucmd.ToJsonStrIgnoreErr(filter))
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			for _, record := range res {
				excludeDevices = append(excludeDevices, record.DeviceId)
			}
		}
		log.CtxLog(c).Infof("PsosRunAllDevices, exclude devices count: %d, request: %s", len(excludeDevices), ucmd.ToJsonStrIgnoreErr(request))
		cCopy := c.Copy()
		go func() {
			defer ucmd.RecoverPanic()
			cond := device_simulation.SimulationAllDevicesCond{
				Project:        request.Project,
				ChargeStrategy: request.ChargeStrategy,
				StartTs:        request.StartTs,
				EndTs:          request.EndTs,
				Limit:          request.Limit,
				ExcludeDevices: excludeDevices,
				IncludeDevices: request.DeviceIds,
			}
			switchOn, switchOff := 1, 0
			if request.ChargeStrategy == device_simulation.ChargeStrategyOnlyEps {
				cond.CmsSwitch = &switchOn
				cond.BatteryRestSwitch = &switchOff
			} else if request.ChargeStrategy == device_simulation.ChargeStrategyEpsSilent {
				cond.CmsSwitch = &switchOn
				cond.BatteryRestSwitch = &switchOn
			} else if request.ChargeStrategy == device_simulation.ChargeStrategyNonStra {
				cond.CmsSwitch = &switchOff
				cond.BatteryRestSwitch = &switchOff
			}
			domain_service.GetDeviceSimulation().StartSimulationAllDevices(cCopy, cond)
		}()
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) CalculateActivityStats() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  activity.CalculateActivityRequest
			response um.Base
		)
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("fail to get json params, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if err := util.CheckTimeRange(request.StartTime, request.EndTime); err != nil {
			log.CtxLog(c).Errorf("invalid time range, err: %v, request: %s", err, ucmd.ToJsonStrIgnoreErr(request))
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		activityDO := &activity.ActivityDO{
			StartTime: request.StartTime,
			EndTime:   request.EndTime,
			Projects:  request.Projects,
		}
		go activityDO.CalculateActivity(c)
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) ListActivityStats() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  activity.ListActivityStatsRequest
			response activity.ListActivityStatsResponse
		)
		project := c.Param("project")
		if project != umw.PUS3 && project != umw.PowerSwap2 && project != umw.PUS4 {
			log.CtxLog(c).Errorf("invalid project: %s", project)
			um.FailWithBadRequest(c, &response, "invalid project")
			return
		}
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("parse uri params, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		activityDO := &activity.ActivityDO{
			StartTime: request.StartTime,
			EndTime:   request.EndTime,
			Projects:  []string{project},
		}
		data, err := activityDO.ListActivityStats(c, request.FailureValue)
		if err != nil {
			log.CtxLog(c).Errorf("fail to list activity stats, err: %v, activityDO: %s", err, ucmd.ToJsonStrIgnoreErr(activityDO))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = data
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) ListActivityDevices() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.CommonUriInTimeRangeParam
			response activity.ListActivityDevicesResponse
		)
		project := c.Param("project")
		if project != umw.PUS3 && project != umw.PowerSwap2 && project != umw.PUS4 {
			log.CtxLog(c).Errorf("invalid project: %s", project)
			um.FailWithBadRequest(c, &response, "invalid project")
			return
		}
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("parse uri params, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		util.SetURIParamDefault(&request.CommonUriParam)
		var day int64
		if request.StartTime != 0 {
			day = request.StartTime
		} else {
			// 默认昨天0点
			now := time.Now()
			today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
			day = today.Add(-time.Hour * 24).UnixMilli()
		}

		filter := bson.D{
			{"project", project},
			{"day", day},
			{"duration_day", bson.M{"$gt": 0}},
		}
		opts := options.Find().SetLimit(int64(request.Size)).SetSkip(int64((request.Page - 1) * request.Size)).SetSort(bson.M{"duration_day": -1})
		var res []mongo_model.ActivityDevices
		total, err := client.GetWatcher().Mongodb().NewMongoEntry(filter).FindMany(umw.Algorithm, activity.CollectionDevices, opts, &res)
		if err != nil {
			log.CtxLog(c).Errorf("fail to list activity devices, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Total = total
		response.Data = activity.ConvertActivityDevicesDO2VO(c, res, 1+(request.Page-1)*request.Size)
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (a *alg) ListDailyReport() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.ListAlgorithmDailyReportRequest
			response model.AddTotalResponse
		)
		project := c.Param("project")
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("ListDailyReport, parse uri params, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		// 请求的时间范围不能跨月
		startTime, endTime := time.UnixMilli(request.StartTime), time.UnixMilli(request.EndTime)
		if startTime.Year() != endTime.Year() || startTime.Month() != endTime.Month() {
			log.CtxLog(c).Errorf("ListDailyReport, invalid time range, start: %v, end: %v", request.StartTime, request.EndTime)
			um.FailWithBadRequest(c, &response, "time range can't cross month")
			return
		}
		// 查询的算法是否存在
		if _, ok := algorithmIntMap[request.Algorithm]; !ok {
			log.CtxLog(c).Errorf("ListDailyReport, invalid algorithm: %s", request.Algorithm)
			um.FailWithBadRequest(c, &response, "invalid algorithm")
			return
		}
		if request.Page == 0 {
			request.Page = 1
		}
		if request.Size == 0 {
			request.Size = 9999
		}

		algorithmId := algorithmIntMap[request.Algorithm]
		dbName := fmt.Sprintf("%s-%s", umw.AIDailyReport, strings.ToLower(project))
		collName := fmt.Sprintf("%s_%d", request.Algorithm, startTime.Month())
		filter := bson.D{
			{"device_id", request.DeviceId},
			{"name", algorithmId},
			{"start_time", bson.M{"$gte": request.StartTime, "$lte": request.EndTime}},
		}
		opts := options.Find().SetLimit(int64(request.Size)).SetSkip(int64((request.Page - 1) * request.Size)).SetSort(bson.M{"start_time": -1})
		var res []umw.MongoAlgorithmDailyReport
		total, err := client.GetWatcher().Mongodb().NewMongoEntry(filter).FindMany(dbName, collName, opts, &res)
		if err != nil {
			log.CtxLog(c).Errorf("ListDailyReport, fail to list daily report, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Total = int(total)
		response.Data = res
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}
