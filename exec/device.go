package exec

import (
	"archive/zip"
	"bufio"
	"bytes"
	"context"
	"encoding/base64"
	"encoding/csv"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"math/rand"
	"net/http"
	"path"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rs/xid"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/regions"
	tke "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tke/v20180525"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	udao "git.nevint.com/golang-libs/common-utils/dao"
	um "git.nevint.com/golang-libs/common-utils/model"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	uprom "git.nevint.com/golang-libs/common-utils/prometheus"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	"git.nevint.com/welkin2/welkin-backend/domain/alarm"
	domain_common "git.nevint.com/welkin2/welkin-backend/domain/common"
	domain_device "git.nevint.com/welkin2/welkin-backend/domain/device"
	"git.nevint.com/welkin2/welkin-backend/domain/energy"
	"git.nevint.com/welkin2/welkin-backend/domain/oplog"
	"git.nevint.com/welkin2/welkin-backend/domain/order"
	"git.nevint.com/welkin2/welkin-backend/domain/powercharger_event"
	"git.nevint.com/welkin2/welkin-backend/domain/powercharger_order"
	"git.nevint.com/welkin2/welkin-backend/domain/psos"
	"git.nevint.com/welkin2/welkin-backend/domain/revenue"
	"git.nevint.com/welkin2/welkin-backend/domain_service"
	"git.nevint.com/welkin2/welkin-backend/domain_service/bidirectional_device"
	"git.nevint.com/welkin2/welkin-backend/domain_service/device_model"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"
	"git.nevint.com/welkin2/welkin-backend/service"
	"git.nevint.com/welkin2/welkin-backend/util"
)

const (
	maxSize = 2 * 1024 * 1024 * 1024 // 2GB
)

type Device interface {
	UploadImageV1(string, *uprom.Prometheus) gin.HandlerFunc
	UploadData(string, *uprom.Prometheus) gin.HandlerFunc
	UploadDataList(string, *uprom.Prometheus) gin.HandlerFunc
	PushMetric() gin.HandlerFunc
	PushEvent() gin.HandlerFunc
	PostJoinEdgeScriptHandler() gin.HandlerFunc
	ServiceInfoList() gin.HandlerFunc
	ServiceInfoDetails() gin.HandlerFunc
	TankTransferRecordList(area string) gin.HandlerFunc
	TankTransferRecordDetails(area string) gin.HandlerFunc
	BatteryRefresh() gin.HandlerFunc
	Login() gin.HandlerFunc
	Logout() gin.HandlerFunc
	LoggedDevices() gin.HandlerFunc
	UpdateOfflineAccount() gin.HandlerFunc
	GetPassportQR() gin.HandlerFunc
	AccountUpgradeCallback() gin.HandlerFunc
	GetDeviceList() gin.HandlerFunc
	GetFactoryDeviceList() gin.HandlerFunc
	GetDeviceListMapping() gin.HandlerFunc
	GetCityCompanyListMapping() gin.HandlerFunc
	ListAllDevices() gin.HandlerFunc
	UploadDevicesCsv() gin.HandlerFunc
	GetDeviceStatus(scene ...string) gin.HandlerFunc
	UpdateDeviceFactoryStatus() gin.HandlerFunc

	NotifyRemoteCommand() gin.HandlerFunc
	UploadLogFile(area string) gin.HandlerFunc
	SyncLogFilePath() gin.HandlerFunc
	DownloadAuthURL() gin.HandlerFunc
	DownloadApproval() gin.HandlerFunc
	DownloadApprovalCallback() gin.HandlerFunc
	DownloadApprovalHistoryList() gin.HandlerFunc
	GetUploadHistoryList() gin.HandlerFunc
	GetDirectoryTreeList() gin.HandlerFunc
	GetSnapshotList(*uprom.Prometheus) gin.HandlerFunc
	GetSnapshotNameMapping() gin.HandlerFunc
	SendSnapshotInfo() gin.HandlerFunc
	DownloadFile() gin.HandlerFunc
	UpdateFavorite() gin.HandlerFunc
	GetFavoriteList() gin.HandlerFunc
	FilterImageByBatteryInfo() gin.HandlerFunc
	DownloadImageByBatteryInfo() gin.HandlerFunc
	FilterServiceByBatteryInfo() gin.HandlerFunc
	FilterSlotByBatteryInfo() gin.HandlerFunc
	V2GRemoteControl() gin.HandlerFunc
	ListServiceVisual() gin.HandlerFunc
	GetServiceVisual() gin.HandlerFunc
	GetServiceVisualV2() gin.HandlerFunc
	GetModelOverview() gin.HandlerFunc
	GetEnergyOverview() gin.HandlerFunc
	GetEnergyList() gin.HandlerFunc
	GetRevenueOverview() gin.HandlerFunc
	GetRevenueList() gin.HandlerFunc
	GetRevenueSingleDevice() gin.HandlerFunc
	GetEnergySingleDevice() gin.HandlerFunc
	GetPowerChargerEnum() gin.HandlerFunc
	ListPowerChargerOrders() gin.HandlerFunc
	GetPowerChargerOrderByOrderId() gin.HandlerFunc
	GetPowerChargerOrderEvents() gin.HandlerFunc
	GetPowerChargerOrderRealtime() gin.HandlerFunc
	GetDeviceVersionOverview() gin.HandlerFunc
	GetDeviceVersionFilterOption() gin.HandlerFunc
	ListDeviceVersion() gin.HandlerFunc
	ChangeDeviceVersionBlacklist() gin.HandlerFunc
	ListDeviceVersionBlacklist() gin.HandlerFunc
	CheckDeviceVersion() gin.HandlerFunc
	IssueDeviceVersionWorksheet() gin.HandlerFunc
	GetDeviceProdPlm() gin.HandlerFunc
	ListBidirectionalDevices() gin.HandlerFunc
	GetBidirectionalSubscription() gin.HandlerFunc
	UpdateBidirectionalSubscription() gin.HandlerFunc
	GetBidirectionalInfo() gin.HandlerFunc
	DownloadBidirectionalInfo() gin.HandlerFunc

	GetMqttUploadData() gin.HandlerFunc
}

type device struct {
	config  *ucfg.Config
	watcher client.Watcher
	logger  *zap.SugaredLogger

	OSS service.OSS
	FMS service.FMS
	WF  service.WF
}

func NewDeviceHandler(watcher client.Watcher, conf *ucfg.Config) Device {
	handler := &device{
		config:  conf,
		watcher: watcher,
		logger:  log.Logger.Named(model.DEVICE),
		OSS: service.OSS{
			URL:       conf.OSS.PowUrl,
			BrokerURL: fmt.Sprintf("%s/plc/broker", conf.Welkin.BackendUrl),
			AppId:     conf.Sentry.AppId,
			AppSecret: conf.Sentry.AppSecret,
			Header: map[string]string{
				"Content-Type": "application/json;charset=UTF-8",
			},
			Logger: log.Logger.Named("OSS"),
		},
		WF: service.WF{
			URL:       conf.Workflow.Url,
			AppId:     conf.Sentry.AppId,
			AppSecret: conf.Sentry.AppSecret,
			FlowCode:  conf.Workflow.LogExportFlowCode,
			Header: map[string]string{
				"Content-Type": "application/json;charset=UTF-8",
			},
			Logger: log.Logger.Named("WorkFlow"),
		},
		FMS: service.FMS{
			URL:          conf.FMS.Url,
			AppId:        conf.Sentry.AppId,
			AppSecret:    conf.Sentry.AppSecret,
			ClientId:     conf.FMS.ClientId,
			ClientSecret: conf.FMS.ClientSecret,
			PriBucketKey: conf.FMS.PriBucketKey,
			PubBucketKey: conf.FMS.PubBucketKey,
			Header: map[string]string{
				"Content-Type": "application/json;charset=UTF-8",
			},
			Logger: log.Logger.Named("FMS"),
		},
	}
	return handler
}

func (d *device) PostJoinEdgeScriptHandler() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		var request model.TKEEdgeRequest
		var set int
		err := ctx.ShouldBindJSON(&request)
		if err != nil {
			ctx.JSON(http.StatusOK, gin.H{"err_code": 1, "message": err.Error()})
			return
		}
		response, err := d.watcher.Mongodb().GetDownloadCommand()
		if err != nil {
			if err.Error() != "mongo: no documents in result" {
				ctx.JSON(http.StatusOK, gin.H{"err_code": 1, "message": err.Error()})
				return
			}
			set = 1
		}
		installCommand := "./edgectl install -n " + request.Project + "-" + request.DeviceId + "-" + request.SubSystem + " -i " + request.Interface
		if time.Now().UnixMilli()-response.CreateTs <= 3600000 && set == 0 {
			ctx.JSON(http.StatusOK, gin.H{"err_code": 0, "download_command": response.DownloadCommand, "install_command": installCommand})
			_ = d.watcher.Mongodb().SaveTkeEdgeJoinInfo(request, response.DownloadCommand)
			return
		}
		credential := common.NewCredential("AKIDe9UjeVKxMWP8oTzSpDVs79yd7a3UyNs7", "5z2DTZcXVQ9etshNwEdmeHfpjPt7PdiZ")
		cpf := profile.NewClientProfile()
		cpf.HttpProfile.ReqMethod = "POST"
		cpf.HttpProfile.ReqTimeout = 30
		cpf.HttpProfile.Endpoint = "tke.tencentcloudapi.com"
		cli, err := tke.NewClient(credential, regions.Beijing, cpf)
		if err != nil {
			ctx.JSON(http.StatusOK, gin.H{"err_code": 1, "message": err.Error()})
			return
		}
		req := tke.NewDescribeTKEEdgeScriptRequest()
		clusterId := "cls-adh4296f"
		nodeName := ""
		config := "{\"Interface\":\"\", \"RuntimePath\":\"/warehouse/tke\", \"EdgeClusterVersion\":\"2.2\", \"TTL\":\"24h\", \"PreStartHook\":\"bWtkaXIgL3dhcmVob3VzZS90a2UKYXB0IHJlbW92ZSBwb2RtYW4=\"}"
		Interface := ""
		req.ClusterId = &clusterId
		req.NodeName = &nodeName
		req.Config = &config
		req.Interface = &Interface
		res, err := cli.DescribeTKEEdgeScript(req)
		if err != nil {
			ctx.JSON(http.StatusOK, gin.H{"err_code": 11, "message": err.Error()})
			return
		}
		data := []byte(*res.Response.Command)
		downloadCommand := base64.StdEncoding.EncodeToString(data)
		if set == 0 {
			err = d.watcher.Mongodb().UpdateTkeEdgeDownloadCommand(response.DownloadCommand, downloadCommand)
			if err != nil {
				ctx.JSON(http.StatusOK, gin.H{"err_code": 111, "message": err.Error()})
				return
			}
		} else {
			err = d.watcher.Mongodb().InsertTkeEdgeDownloadCommand(downloadCommand)
			if err != nil {
				ctx.JSON(http.StatusOK, gin.H{"err_code": 111, "message": err.Error()})
				return
			}
		}
		ctx.JSON(http.StatusOK, gin.H{"err_code": 0, "download_command": downloadCommand, "install_command": installCommand})
		_ = d.watcher.Mongodb().SaveTkeEdgeJoinInfo(request, downloadCommand)
	}
}

func (d *device) ServiceInfoList() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			uriParam model.ServiceInfoParam
			response model.ServiceInfoResponse
		)
		project := c.Param("project")
		if err := c.BindQuery(&uriParam); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		util.SetURIParamDefault(&uriParam.CommonUriParam)
		response = model.ServiceInfoResponse{
			Page:      uriParam.Page,
			Size:      uriParam.Size,
			StartTime: uriParam.StartTime,
			EndTime:   uriParam.EndTime,
			Project:   project,
		}
		if uriParam.StartTime == 0 || uriParam.EndTime == 0 {
			log.CtxLog(c).Errorf("err: `start_time` and `end_time` are required!")
			um.FailWithBadRequest(c, &response, "err: `start_time` and `end_time` are required!")
			return
		}
		if err := util.CheckTimeRange(uriParam.StartTime, uriParam.EndTime); err != nil {
			log.CtxLog(c).Errorf("`start_time` or `end_time` is incorrect, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		//// 最多查询跨度3个月的数据
		//if uriParam.EndTime-uriParam.StartTime > 90*24*time.Hour.Milliseconds() {
		//	log.CtxLog(c).Warnf("max time range between `start_time` and `end_time` is 90 days")
		//	um.FailWithBadRequest(c, &response, "max time range between `start_time` and `end_time` is 90 days")
		//	return
		//}
		dbName := fmt.Sprintf("%s-%s", umw.ServiceInfo, ucmd.RenameProjectDB(project))
		filter := bson.D{util.SelectedTimeDuration("date", uriParam.StartTime, uriParam.EndTime)}
		if uriParam.DeviceId != "" {
			filter = append(filter, bson.E{Key: "device_id", Value: uriParam.DeviceId})
		} else if uriParam.DeviceIds != "" {
			filter = append(filter, bson.E{Key: "device_id", Value: bson.M{"$in": strings.Split(uriParam.DeviceIds, ",")}})
		}
		if uriParam.ServiceId != "" {
			filter = append(filter, bson.E{Key: "service_id", Value: uriParam.ServiceId})
		}
		if uriParam.BatteryId != "" {
			filter = append(filter, bson.E{Key: "$or", Value: bson.A{
				bson.D{{"ev_battery_id", uriParam.BatteryId}},
				bson.D{{"service_battery_id", uriParam.BatteryId}},
			}})
		}
		if uriParam.EvId != "" {
			filter = append(filter, bson.E{Key: "ev_id", Value: uriParam.EvId})
		}
		if uriParam.FinishResult != nil {
			filter = append(filter, bson.E{Key: "finish_result", Value: uriParam.FinishResult})
		}
		if uriParam.IsStuck != nil {
			filter = append(filter, bson.E{Key: "is_stuck", Value: uriParam.IsStuck})
		}
		if uriParam.EvBrand != "" {
			evBrands := strings.Split(uriParam.EvBrand, ",")
			evTypes := make([]string, 0)
			for _, brand := range evBrands {
				evTypes = append(evTypes, model.VehicleBrandMap[brand]...)
			}
			filter = append(filter, bson.E{Key: "ev_type", Value: bson.M{"$in": evTypes}})
		}
		if uriParam.EvType != "" {
			filter = append(filter, bson.E{Key: "ev_type", Value: bson.M{"$in": strings.Split(uriParam.EvType, ",")}})
		}

		startTime := util.ConvertTime(uriParam.StartTime)
		endTime := util.ConvertTime(uriParam.EndTime)
		colNameList := make([]CrossCollection, 0)
		lastColName := ""
		for year := startTime.Year(); year <= endTime.Year(); year++ {
			startMonth := 1
			endMonth := 12
			if year == startTime.Year() {
				startMonth = int(startTime.Month())
			}
			if year == endTime.Year() {
				endMonth = int(endTime.Month())
			}
			for month := startMonth; month <= endMonth; month++ {
				colName := util.EncodeMonth(month)
				if colName == lastColName {
					continue
				}
				colNameList = append(colNameList, CrossCollection{
					ColName:   colName,
					YearMonth: fmt.Sprintf("%d-%d", year, month),
				})
				lastColName = colName
			}
		}
		sort.Slice(colNameList, func(i, j int) bool { return colNameList[i].YearMonth > colNameList[j].YearMonth })
		records := make([]mmgo.MongoServiceInfo, 0)
		globalSkip := int64((uriParam.Page - 1) * uriParam.Size)
		crossCollection := false
		for _, colName := range colNameList {
			year, _ := strconv.Atoi(colName.YearMonth[:4])
			startOfYear := time.Date(year, time.January, 1, 0, 0, 0, 0, time.Local).UnixMilli()
			endOfYear := time.Date(year+1, time.January, 1, 0, 0, 0, 0, time.Local).UnixMilli()
			newFilter := append(filter, util.SelectedTimeDuration("date", startOfYear, endOfYear))
			count, err := d.watcher.Mongodb().NewMongoEntry(newFilter).Count(dbName, colName.ColName)
			if err != nil {
				log.CtxLog(c).Errorf("failed to get service info of %s, err: %v", colName, err)
				continue
			}
			response.Total += int(count)
			if len(records) >= uriParam.Size {
				// 不能直接break，在跨多个表的时候，response.Total还需要累加后面几个表的total
				continue
			}
			// globalSkip超过这个表的记录总数，跳过该表；若跨表搜索，不能跳过
			if !crossCollection && globalSkip >= count {
				globalSkip -= count
				continue
			}
			var serviceInfos []mmgo.MongoServiceInfo
			skip := globalSkip
			if crossCollection {
				skip = 0
			}
			mongoOpt := options.Find().SetSkip(skip).SetLimit(int64(uriParam.Size - len(records))).SetSort(bson.D{{"date", -1}})
			_, err = d.watcher.Mongodb().NewMongoEntry(newFilter).FindMany(dbName, colName.ColName, mongoOpt, &serviceInfos)
			if err != nil {
				log.CtxLog(c).Errorf("failed to get service info of %s, err: %v", colName, err)
				continue
			}
			records = append(records, serviceInfos...)
			if len(records) < uriParam.Size {
				// 在第一个表中数据没找全，说明当前页是跨表搜的
				crossCollection = true
			}
		}
		if len(records) >= uriParam.Size {
			records = records[:uriParam.Size]
		}
		results := make([]model.ServiceInfoData, 0)
		for _, item := range records {
			result := model.ServiceInfoData{
				DeviceId:         item.DeviceId,
				ServiceId:        item.ServiceId,
				EVBatteryId:      item.EVBatteryId,
				ServiceBatteryId: item.ServiceBatteryId,
				EvId:             item.EvId,
				HasNormalImage:   false,
				HasAbnormalImage: false,
				IsStuck:          item.IsStuck,
				StartTime:        item.StartTime,
				EndTime:          item.EndTime,
				UpdatedTime:      item.UpdatedTime,
				FinishResult:     item.FinishResult,
				EvType:           item.EvType,
				EvBrand:          model.VehicleTypeBrandMap[item.EvType],
			}
			deviceInfo, _ := cache.PowerSwapCache.GetSingleDevice(item.DeviceId)
			if deviceInfo != nil {
				result.Description = deviceInfo.Description
			}
			abnormalErr, normalErr := d.watcher.Mongodb().NewMongoEntry().ImagesIsNormal(fmt.Sprintf("%s-%s", umw.ImageInfo, ucmd.RenameProjectDB(project)), item.DeviceId, item.ServiceId)
			if normalErr == nil {
				result.HasNormalImage = true
			}
			if abnormalErr == nil {
				result.HasAbnormalImage = true
			}
			if item.EndTime == 0 {
				result.FinishResult = -1
			}
			results = append(results, result)
		}
		response.ServiceInfoData = results
		log.CtxLog(c).Infof("succeeded to get all service info data, filter: %v", filter)
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (d *device) ServiceInfoDetails() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.ServiceInfoResponse
		)
		project := c.Param("project")
		deviceId := c.Param("device_id")
		serviceId := c.Param("service_id")
		startTime := c.Query("start_time")
		endTime := c.Query("end_time")
		if startTime == "" || endTime == "" {
			log.CtxLog(c).Errorf("err: `start_time` and `end_time` are required!")
			um.FailWithMessageForGin(c, &response, "err: `start_time` and `end_time` are required!", 1, http.StatusBadRequest)
			return
		}
		from, err := strconv.ParseInt(startTime, 10, 64)
		if err != nil {
			log.CtxLog(c).Errorf("failed to parse start_time, err: %s", err.Error())
			um.FailWithMessageForGin(c, &response, err.Error(), 1, http.StatusBadRequest)
			return
		}
		to, err := strconv.ParseInt(endTime, 10, 64)
		if err != nil {
			log.CtxLog(c).Errorf("failed to parse end_time, err: %s", err.Error())
			um.FailWithMessageForGin(c, &response, err.Error(), 1, http.StatusBadRequest)
			return
		}
		if err := util.CheckTimeRange(from, to); err != nil {
			log.CtxLog(c).Errorf("the time range is incorrect, err: %v", err)
			um.FailWithMessageForGin(c, &response, err.Error(), 1, http.StatusBadRequest)
			return
		}
		response = model.ServiceInfoResponse{
			StartTime: from,
			EndTime:   to,
			Project:   project,
		}

		var success bool
		var listErr error
		dbName := fmt.Sprintf("%s-%s", umw.ServiceInfo, ucmd.RenameProjectDB(project))
		for collection := range util.ParseTimeRangeList(from, to) {
			byteData, err := d.watcher.Mongodb().NewMongoEntry(bson.D{
				bson.E{Key: "device_id", Value: deviceId},
				bson.E{Key: "service_id", Value: serviceId}}).ListAll(dbName, collection, client.Ordered{})
			if err != nil {
				listErr = err
				continue
			}
			var records []mmgo.MongoServiceInfo
			if err = json.Unmarshal(byteData, &records); err != nil {
				log.CtxLog(c).Errorf("failed to unmarshal services info, err: %v", err)
				continue
			}
			success = true
			if len(records) != 0 {
				conn := udao.NewRedisConn(d.watcher.Redis())
				tags, err := client.GetDeviceTag(conn, d.watcher.Mongodb(), deviceId)
				conn.Close()
				if err != nil {
					log.CtxLog(c).Errorf("failed to get device description, err: %s", err)
				}
				finishResult := records[0].FinishResult
				if records[0].EndTime == 0 {
					finishResult = -1
				}
				response.ServiceInfoData = append(response.ServiceInfoData, model.ServiceInfoData{
					Description:      tags.Description,
					DeviceId:         records[0].DeviceId,
					ServiceId:        records[0].ServiceId,
					EVBatteryId:      records[0].EVBatteryId,
					ServiceBatteryId: records[0].ServiceBatteryId,
					EvId:             records[0].EvId,
					IsStuck:          records[0].IsStuck,
					FinishResult:     finishResult,
					StartTime:        records[0].StartTime,
					EndTime:          records[0].EndTime,
					UpdatedTime:      records[0].UpdatedTime,
					EvType:           records[0].EvType,
					EvBrand:          model.VehicleTypeBrandMap[records[0].EvType],
				})
				response.Total = 1
				log.CtxLog(c).Infof("succeeded to get serviceinfo details, start_time: %d end_time: %d database: %s, collection: %s",
					from, to, dbName, collection)
				break
			}
		}
		if success {
			um.SuccessWithMessageForGin(c, &response, "succeeded to get serviceinfo details", http.StatusOK)
		} else {
			log.CtxLog(c).Errorf("service_id: %s, device_id: %s, get details, err: %s", serviceId, deviceId, listErr.Error())
			um.FailWithMessageForGin(c, &response, listErr.Error(), 2, http.StatusInternalServerError)
		}
	}
}

func (d *device) Login() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			requestData model.DeviceLoginLogoutParams
			response    model.OAuthResponse
		)
		if err := c.BindJSON(&requestData); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithMessageForGin(c, &response, err.Error(), -1, http.StatusBadRequest)
			return
		}

		pwd := requestData.Password
		mw := &service.MongoWatcher{
			Client: d.watcher.Mongodb(),
			UserId: requestData.UserId,
		}
		if requestData.RequestType == "" {
			mw.RequestType = um.Welkin
		} else {
			mw.RequestType = requestData.RequestType
		}
		if mw.RequestType == um.BrownDragon {
			// response definition: https://nio.feishu.cn/wiki/wikcnqzfjMnhRR3ziuBJjSuA8nf#FUuydqCcyoEiEMxgJYZcQRUCnNg
			identifier := mw.CheckUserIdentifier(d.config, pwd)
			if identifier.Err != nil {
				log.CtxLog(c).Errorf("failed to check user identifier, err: %v", identifier.Err)
				um.FailWithMessageForGin(c, &response, identifier.Err.Error(), -3, http.StatusInternalServerError)
				return
			}
			if identifier.Code <= 0 {
				if strings.HasPrefix(identifier.Msg, "sso unauthorized") {
					log.CtxLog(c).Errorf("failed to check user sso identifier, msg: %v", identifier.Msg)
					um.SuccessWithErrCode(c, &response, identifier.Msg, 1)
				} else {
					response.TargetURL = fmt.Sprintf("%s/brown-dragon#/user-approval?user_id=%s",
						d.config.Welkin.GateUrl, requestData.UserId)
					log.CtxLog(c).Warnf("the user: %s has no permission", requestData.UserId)
					um.SuccessWithErrCode(c, &response, identifier.Msg, 2)
				}
				return
			}

			response.Role = identifier.Code
			// the user has permission, verify the role is whether 5 or 6
			if response.Role != 5 && response.Role != 6 {
				log.CtxLog(c).Warnf("the user: %s has no permission for brown dragon, role: %d", requestData.UserId, response.Role)
				msg := fmt.Sprintf("your role is %d, please apply 5 or 6", response.Role)
				um.SuccessWithErrCode(c, &response, msg, 3)
				return
			}

			rawData, err := d.watcher.Mongodb().NewMongoEntry(
				bson.D{bson.E{Key: "device_identifier", Value: requestData.DeviceIdentifier}}).GetOne(
				umw.FactoryData, umw.BrowndragonDeviceInfo)
			if err != nil {
				log.CtxLog(c).Errorf("failed to check device identifier: %s, err: %v", requestData.DeviceIdentifier, err)
				um.FailWithMessageForGin(c, &response, err.Error(), -3, http.StatusInternalServerError)
			} else if rawData == nil {
				response.TargetURL = fmt.Sprintf("%s/brown-dragon#/device-approval?user_id=%s&device_identifier=%s",
					d.config.Welkin.GateUrl, requestData.UserId, requestData.DeviceIdentifier)
				log.CtxLog(c).Warnf("the device identifier: %s needed to be registried!", requestData.DeviceIdentifier)
				um.SuccessWithErrCode(c, &response, "please registry the device", 4)
			} else {
				mw.DeviceId = requestData.DeviceIdentifier
				if err = mw.InsertNewLoginHistory(identifier.User.Username, response.Role, identifier.User.Factory); err != nil {
					if strings.HasPrefix(err.Error(), "forbidden") {
						um.SuccessWithErrCode(c, &response, err.Error(), 5)
					} else {
						response.Role = 0
						log.CtxLog(c).Errorf("failed to insert brown dragon login history into db, err: %v", err)
						um.FailWithMessageForGin(c, &response, err.Error(), -3, http.StatusInternalServerError)
					}
					return
				}
				response.Data = map[string]interface{}{
					"factory":  identifier.User.Factory,
					"type":     identifier.User.Type, // 设备大类
					"projects": identifier.User.Projects,
				}
				log.CtxLog(c).Infof("the user: %s has permission, role: %d, the device: %s has registried, login success",
					requestData.UserId, response.Role, requestData.DeviceIdentifier)
				um.SuccessWithErrCode(c, &response, "login success", 0)
			}
			return
		}

		response = model.OAuthResponse{
			Project:  requestData.Project,
			DeviceId: requestData.DeviceId,
			UserId:   requestData.UserId,
		}
		record, err := d.getDeviceKeys(c, um.Welkin, umw.OAuthDB, umw.DeviceBaseInfo, response.DeviceId)
		if err != nil {
			log.CtxLog(c).Errorf("failed to query device info, err: %v", err)
			if err.Error() == "the device is not found" {
				um.FailWithMessageForGin(c, &response, err.Error(), -2, http.StatusNotFound)
			} else {
				um.FailWithMessageForGin(c, &response, err.Error(), -3, http.StatusInternalServerError)
			}
			return
		}

		if response.UserId == "admin" && pwd == "admin" {
			response.Role = model.ADMIN
			if record.isActive {
				response.Role = model.NOTALLOWED
				um.SuccessWithErrCode(c, &response, "login forbidden", 1)
			} else {
				mw.UserId = "admin"
				mw.DeviceId = response.DeviceId
				if err = mw.InsertNewLoginHistory("出厂用户", model.ADMIN, 0, response.Project); err != nil {
					log.CtxLog(c).Errorf("failed to insert login history into db, err: %v", err.Error())
					um.FailWithMessageForGin(c, &response, err.Error(), -3, http.StatusInternalServerError)
				} else {
					um.SuccessWithErrCode(c, &response, "login success", 0)
				}
			}
			return
		}

		// the user is not admin, authorize continue!!!
		identifier := mw.CheckUserIdentifier(d.config, pwd)
		response.Role = identifier.Code
		if identifier.Err != nil {
			log.CtxLog(c).Errorf("failed to check user identifier, err: %v", identifier.Err)
			um.FailWithMessageForGin(c, &response, identifier.Err.Error(), -3, http.StatusInternalServerError)
			return
		}
		if response.Role <= 0 {
			log.CtxLog(c).Errorf("failed to check user identifier, msg: %v", identifier.Msg)
			um.SuccessWithErrCode(c, &response, identifier.Msg, 1)
			return
		} else if response.Role >= 5 {
			log.CtxLog(c).Warnf("the user: %s has no permission, role: %d", response.UserId, response.Role)
			um.SuccessWithErrCode(c, &response, "the role mismatched", 1)
			return
		}

		// insert login history and update device active status
		mu := sync.Mutex{}
		mu.Lock()
		err = d.watcher.Mongodb().UpdateDeviceStatus(umw.OAuthDB, umw.DeviceBaseInfo, umw.MongoDeviceInfo{
			Id:       record.id,
			IsActive: true,
			IsLogin:  true,
		})
		mu.Unlock()
		if err != nil {
			log.CtxLog(c).Errorf("failed to update the device login status into db, err: %v", err.Error())
			um.FailWithMessageForGin(c, &response, err.Error(), -3, http.StatusInternalServerError)
			return
		}
		mw.DeviceId = response.DeviceId
		if err = mw.InsertNewLoginHistory(identifier.User.Username, response.Role, 0, response.Project); err != nil {
			log.CtxLog(c).Errorf("failed to insert login history into db, err: %v", err.Error())
			um.FailWithMessageForGin(c, &response, err.Error(), -3, http.StatusInternalServerError)
			return
		}
		um.SuccessWithMessageForGin(c, &response, "login success", http.StatusOK)
		return
	}
}

func (d *device) Logout() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			requestData model.DeviceLoginLogoutParams
			response    um.Base
		)

		if err := c.BindJSON(&requestData); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithMessageForGin(c, &response, err.Error(), -1, http.StatusBadRequest)
			return
		}
		mw := &service.MongoWatcher{Client: d.watcher.Mongodb()}
		if requestData.RequestType == um.BrownDragon {
			mw.RequestType = um.BrownDragon
			mw.DeviceId = requestData.DeviceIdentifier
			if err := mw.MakeUserLogout(); err != nil {
				log.CtxLog(c).Errorf("failed to update logout history into db, err: %v", err)
				um.FailWithMessageForGin(c, &response, err.Error(), -3, http.StatusInternalServerError)
				return
			}
			log.CtxLog(c).Infof("succeeded to logout, device_identifier: %s", requestData.DeviceIdentifier)
			um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
			return
		}
		record, err := d.getDeviceKeys(c, um.Welkin, umw.OAuthDB, umw.DeviceBaseInfo, requestData.DeviceId)
		if err != nil {
			log.CtxLog(c).Errorf("failed to get device info, err: %v", err)
			if err.Error() == "the device is not found" {
				um.FailWithMessageForGin(c, &response, err.Error(), -2, http.StatusNotFound)
			} else {
				um.FailWithMessageForGin(c, &response, err.Error(), -3, http.StatusInternalServerError)
			}
			return
		}
		if record.isLogin {
			mu := sync.Mutex{}
			mu.Lock()
			err = d.watcher.Mongodb().UpdateDeviceStatus(umw.OAuthDB, umw.DeviceBaseInfo, umw.MongoDeviceInfo{
				Id:      record.id,
				IsLogin: false,
			})
			mu.Unlock()
			if err != nil {
				log.CtxLog(c).Errorf("failed to update the device logout status into db, err: %v", err)
				um.FailWithMessageForGin(c, &response, err.Error(), -3, http.StatusInternalServerError)
				return
			}
		}
		mw.RequestType = um.Welkin
		mw.DeviceId = requestData.DeviceId
		if err = mw.MakeUserLogout(); err != nil {
			log.CtxLog(c).Errorf("failed to update logout history into db, err: %v", err)
			um.FailWithMessageForGin(c, &response, err.Error(), -3, http.StatusInternalServerError)
			return
		}
		log.CtxLog(c).Infof("succeeded to logout, user_id: %s, device_id: %s", requestData.UserId, requestData.DeviceId)
		um.SuccessWithMessageForGin(c, &response, "logout success", http.StatusOK)
		return
	}
}

func (d *device) LoggedDevices() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			err      error
			response model.DeviceLoggedData
		)
		requestType := c.Query("request_type")
		userId := c.Query("user_id")

		if requestType == um.BrownDragon {
			ch := &client.BrownDragonLoginHistory{Client: d.watcher.Mongodb().Client, UserId: userId}
			response.Data, err = ch.GetLatestLoginInfo()
			if err != nil {
				log.CtxLog(c).Errorf("failed to get latest device login info: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
		} else {
			ch := &client.CommonLoginHistory{Client: d.watcher.Mongodb().Client, UserId: userId}
			response.Data, err = ch.GetLatestLoginInfo()
			if err != nil {
				log.CtxLog(c).Errorf("failed to get latest device login info: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
		}
		log.CtxLog(c).Infof("succeeded to get latest device login info")
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (d *device) GetPassportQR() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			requestData model.QRParams
			response    model.QRResponse
		)
		if err := c.BindJSON(&requestData); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithMessageForGin(c, &response, fmt.Sprintf("parse request body, err: %v", err.Error()), -1, http.StatusBadRequest)
			return
		}
		cryted, err := ucmd.AesEncrypt(fmt.Sprintf("project=%s&device_id=%s", requestData.Project, requestData.DeviceId), d.config.Welkin.AesKey)
		if err != nil {
			log.CtxLog(c).Errorf("failed to encrypt `project` and `device_id` as param, err: %v", err)
			um.FailWithMessageForGin(c, &response, fmt.Sprintf("failed to encrypt `project` and `device_id` as param, err: %s", err.Error()), -1, http.StatusBadRequest)
			return
		}
		response.Data = fmt.Sprintf("%s/feishu/passport/authorize-login?param=%s", d.config.Welkin.GateUrl, cryted)
		um.SuccessWithMessageForGin(c, &response, "succeeded to get feishu passport qr", http.StatusOK)
		return
	}
}

func (d *device) AccountUpgradeCallback() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request UserUpgradeWorkflowCallbackRequest
			//response    model.QRResponse
		)
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"result_code": "fail", "message": err.Error()})
			return
		}

		orignalRole, err := strconv.ParseInt(request.Context.OriginalRole, 10, 64)
		if err != nil {
			log.CtxLog(c).Errorf("strconv.ParseInt, err: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"result_code": "fail", "message": err.Error()})
			return
		}
		upgradeRole, err := strconv.ParseInt(request.Context.UpgradeRole, 10, 64)
		if err != nil {
			log.CtxLog(c).Errorf("strconv.ParseInt, err: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"result_code": "fail", "message": err.Error()})
			return
		}

		durationHour, err := strconv.ParseInt(request.Context.Duration, 10, 64)
		durationMillisecond := durationHour * 60 * 60 * 1000
		if err != nil {
			log.CtxLog(c).Errorf("strconv.ParseInt, err: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"result_code": "fail", "message": err.Error()})
			return
		}
		if ucmd.GetEnv() != "prod" {
			durationMillisecond = config.TestData.AccountUpgrade.DurationMillisecond
		}

		expireTimestamp := time.Now().UnixMilli() + durationMillisecond
		reqid := xid.New().String()
		header := map[string]string{
			"X-Request-ID": reqid,
			"Content-Type": "application/json;charset=UTF-8",
		}
		body := map[string]interface{}{}
		if request.Status == service.WorkflowStatusSuccess {
			body = map[string]interface{}{
				"ability_operates": []map[string]interface{}{
					{"ability_code": "user_role_upgrade_workflow_result",
						"ability_params": []map[string]interface{}{
							{"param_code": "result_code", "param_value": 0},
							{"param_code": "user_id", "param_value": request.Context.UserId},
							{"param_code": "original_role", "param_value": orignalRole},
							{"param_code": "upgrade_role", "param_value": upgradeRole},
							{"param_code": "expire_timestamp", "param_value": expireTimestamp},
						}},
				},
			}
			userUpgradeInfo := mmgo.MongoUserUpgradeInfo{
				UserId:          request.Context.UserId,
				OriginalRole:    int(orignalRole),
				UpgradeRole:     int(upgradeRole),
				ExpireTimestamp: expireTimestamp,
				UpdateTimestamp: time.Now().UnixMilli(),
				CreateTimestamp: time.Now().UnixMilli(),
			}

			err = d.watcher.Mongodb().NewMongoEntry().InsertMany(umw.OAuthDB, "user_upgrade_info", []interface{}{userUpgradeInfo}, []client.IndexOption{
				{Name: "user_id_expire_time", Fields: bson.D{{"user_id", 1}, {"expire_timestamp", 1}}, Unique: false},
			}...)
			if err != nil {
				log.CtxLog(c).Errorf("mongodb insert fail,userUpgradeInfo:%v err: %v", ucmd.ToJsonStrIgnoreErr(userUpgradeInfo), err)
				c.JSON(http.StatusInternalServerError, gin.H{"result_code": "fail", "message": err.Error()})
				return
			}
		} else if request.Status == service.WorkflowStatusDeny {
			body = map[string]interface{}{
				"ability_operates": []map[string]interface{}{
					{"ability_code": "user_role_upgrade_workflow_result",
						"ability_params": []map[string]interface{}{
							{"param_code": "result_code", "param_value": 1000},
							{"param_code": "user_id", "param_value": request.Context.UserId},
							{"param_code": "original_role", "param_value": orignalRole},
							{"param_code": "upgrade_role", "param_value": upgradeRole},
							{"param_code": "expire_timestamp", "param_value": expireTimestamp},
						}},
				},
			}
		} else {
			body = map[string]interface{}{
				"ability_operates": []map[string]interface{}{
					{"ability_code": "user_role_upgrade_workflow_result",
						"ability_params": []map[string]interface{}{
							{"param_code": "result_code", "param_value": 2000},
							{"param_code": "user_id", "param_value": request.Context.UserId},
							{"param_code": "original_role", "param_value": orignalRole},
							{"param_code": "upgrade_role", "param_value": upgradeRole},
							{"param_code": "expire_timestamp", "param_value": expireTimestamp},
						}},
				},
			}
		}
		resourceId := request.Context.DeviceId
		if request.Context.Project == umw.PUS3 || request.Context.Project == umw.PUS4 {
			deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(request.Context.DeviceId)
			if !found {
				log.CtxLog(c).Errorf("device not found")
				um.FailWithBadRequest(c, &model.Response{}, "device not found")
				return
			}
			resourceId = deviceInfo.ResourceId
		}
		_, err = service.SendControlCommand2Device(c, resourceId, header, body)
		if err != nil {
			log.CtxLog(c).Errorf("service.SendControlCommand2Device, err: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"result_code": "fail", "message": err.Error()})
			return
		}

		copiedCtx := c.Copy()
		// 往kafka推送，让下游需要感知的系统去消费
		go func(c context.Context) {
			defer ucmd.RecoverPanic()
			queryWorkflowInstanceResp, err := service.QueryWorkflowInstance(c, request.FlowInstanceId, request.Context.WorkflowCreator)
			if err != nil {
				log.CtxLog(c).Errorf("service.QueryWorkflowInstance err. err:%v", err)
				return
			}
			userUpgradeInfo2Kafka := UserUpgradeInfo2Kafka{
				Project:      request.Context.Project,
				Duration:     request.Context.Duration,
				DeviceId:     request.Context.DeviceId,
				ApplyUserId:  request.Context.UserId,
				OriginalRole: request.Context.OriginalRole,
				UpgradeRole:  request.Context.UpgradeRole,
				ApplyTime:    request.Context.WorkflowCreatedTime,
				TaskId:       request.Context.TaskId,
				Status:       request.Status,
			}
			deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(request.Context.DeviceId)
			if found {
				userUpgradeInfo2Kafka.DeviceName = deviceInfo.Description
				userUpgradeInfo2Kafka.DeviceCityCompany = deviceInfo.CityCompany
				userUpgradeInfo2Kafka.ResourceId = deviceInfo.ResourceId
			}
			for _, node := range queryWorkflowInstanceResp.Data.Nodes {
				if node.FlowInstanceNodeTitle == "审批" {
					for _, task := range node.Tasks {
						if task.Status != "" {
							userUpgradeInfo2Kafka.ApproverUserId = task.Operator.WorkerUserId
							userUpgradeInfo2Kafka.ApproveTime = fmt.Sprintf("%v", task.EndTime)
						}
					}
				}
			}
			userUpgradeInfo2KafkaByteData, err := json.Marshal(userUpgradeInfo2Kafka)
			if err != nil {
				log.CtxLog(c).Errorf("json.Marshal err. err:%v", err)
				return
			}
			log.CtxLog(c).Infof("sendToKafka data:%v", string(userUpgradeInfo2KafkaByteData))
			var reqData map[string]interface{}
			err = json.Unmarshal(userUpgradeInfo2KafkaByteData, &reqData)
			if err != nil {
				log.CtxLog(c).Errorf("json.Unmarshal err. err:%v", err)
				return
			}
			data := map[string]interface{}{
				"msg_type":      "user_upgrade",
				"msg_timestamp": time.Now().UnixMilli(),
			}
			data["data"] = reqData

			err = client.Send2Broker(c, data, "user_upgrade")
			if err != nil {
				log.CtxLog(c).Errorf("client.Send2Broker err. err:%v", err)
				return
			}
			log.CtxLog(c).Infof("sendToKafka data:%v", data)
		}(copiedCtx)
		c.JSON(http.StatusOK, gin.H{"result_code": "success", "message": "ok"})
	}
}

type UserUpgradeInfo2Kafka struct {
	Project           string `json:"project"`
	Duration          string `json:"duration"`
	DeviceId          string `json:"device_id"`
	ResourceId        string `json:"resource_id"`
	DeviceName        string `json:"device_name"`
	DeviceCityCompany string `json:"device_city_company"`
	ApplyUserId       string `json:"apply_user_id"`
	OriginalRole      string `json:"original_role"`
	UpgradeRole       string `json:"upgrade_role"`
	ApplyTime         string `json:"apply_time"`
	TaskId            string `json:"task_id"`
	ApproverUserId    string `json:"approver_user_id"`
	ApproveTime       string `json:"approve_time"`
	Status            string `json:"status"`
}

type UserUpgradeWorkflowCallbackRequest struct {
	Context struct {
		Project                     string `json:"project"`
		Duration                    string `json:"duration"`
		DeviceId                    string `json:"device_id"`
		UserId                      string `json:"user_id"`
		OriginalRole                string `json:"original_role"`
		UpgradeRole                 string `json:"upgrade_role"`
		WorkflowCreator             string `json:"workflow_creator"`
		WorkflowCreatedTime         string `json:"workflow_created_time"`
		WorkflowCostCenterId        string `json:"workflow_cost_center_id"`
		WorkflowCreatorCompany      string `json:"workflow_creator_company"`
		WorkflowCreatorDepartmentCn string `json:"workflow_creator_department_cn"`
		WorkflowCreatorDepartmentEn string `json:"workflow_creator_department_en"`
		TaskId                      string `json:"task_id"`
	} `json:"context"`
	BizCode                        string `json:"biz_code"`
	Status                         string `json:"status"`
	CurrentNodeName                string `json:"current_node_name"`
	FlowInstanceNodeName           string `json:"flow_instance_node_name"`
	PreviousNodeName               string `json:"previous_node_name"`
	LastStatusFlowInstanceNodeName string `json:"last_status_flow_instance_node_name"`
	FlowInstanceId                 string `json:"flow_instance_id"`
	IsTestForRelease               bool   `json:"is_test_for_release"`
}

func (d *device) GetDeviceList() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.GetDeviceListResponse

		project := c.Param("project")
		deviceId := c.Query("device")
		fuzzyDeviceId := c.Query("fuzzy_device_id")
		fuzzyDeviceIdOrName := c.Query("fuzzy_device_id_or_name")
		province := c.Query("province")
		city := c.Query("city")
		pageSize := c.Query("page_size")
		area := c.Query("area")
		pageSizeInt, _ := strconv.ParseInt(pageSize, 10, 64)

		pageNo := c.Query("page_no")
		pageNoInt, _ := strconv.ParseInt(pageNo, 10, 64)

		deviceList, err, total := d.watcher.Mongodb().GetDeviceList(project, deviceId, fuzzyDeviceId, fuzzyDeviceIdOrName, province, city, area, pageSizeInt, pageNoInt)
		if err != nil {
			log.CtxLog(c).Errorf("fail to GetDeviceList, err: %v", err)
			um.FailWithMessageForGin(c, &response, err.Error(), 1, http.StatusInternalServerError)
			return
		}
		response.Total = total

		userId := c.GetHeader("X-User-ID")
		for _, idr := range deviceList {
			deviceDetail := model.DeviceDetail{
				MongoDeviceInfo: idr,
			}
			if userId == "" {
				response.Data = append(response.Data, deviceDetail)
				continue
			}
			rawData, err := d.watcher.Mongodb().NewMongoEntry(bson.D{
				bson.E{Key: "user_id", Value: userId},
				bson.E{Key: "device_id", Value: idr.DeviceId}}).GetOne(umw.Favorites, umw.Devices)
			if err != nil || rawData == nil {
				if err != nil {
					log.CtxLog(c).Warnf("cannot check whether the device %s is the user %s favorite or not, err: %v", idr.DeviceId, userId, err)
				}
				response.Data = append(response.Data, deviceDetail)
				continue
			}
			var res umw.MongoFavoriteDevice
			if err = bson.Unmarshal(rawData, &res); err != nil {
				log.CtxLog(c).Warnf("cannot check whether the device %s is the user %s favorite or not, err: %v", idr.DeviceId, userId, err)
				response.Data = append(response.Data, deviceDetail)
				continue
			}
			deviceDetail.Favorite = res.Favorite
			response.Data = append(response.Data, deviceDetail)
		}

		log.CtxLog(c).Infof("succeed to get device list")
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *device) GetFactoryDeviceList() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.AddTotalResponse
		url := fmt.Sprintf("%s/device/v1/devices/list_all?name=%s&project=%s", config.Cfg.Welkin.BackendStgUrl, c.Query("name"), c.Query("project"))
		limt := c.Query("limit")
		if limt != "" {
			url = fmt.Sprintf("%v&limit=%v", url, limt)
		}
		ct := ucmd.NewHttpClient(ucmd.HttpClient{
			URL:    url,
			Method: "GET",
		})
		body, statusCode, err := ct.Do()
		if err != nil {
			log.CtxLog(c).Errorf("fail to request factory device list, err: %v, url: %s", err, ct.URL)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		defer body.Close()
		data, err := io.ReadAll(body)
		if err != nil {
			log.CtxLog(c).Errorf("fail to read body factory device list, err: %v, url: %s", err, ct.URL)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if statusCode != http.StatusOK {
			errMsg := fmt.Sprintf("fail to request factory device list, status code: %d, url: %s", statusCode, ct.URL)
			log.CtxLog(c).Errorf(errMsg)
			um.FailWithInternalServerError(c, &response, errMsg)
			return
		}
		var allDevices model.ListAllDevicesResponse
		if err = json.Unmarshal(data, &allDevices); err != nil {
			log.CtxLog(c).Errorf("fail to unmarshal devices, err: %v, url: %s", err, ct.URL)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Total = len(allDevices.Data)
		response.Data = allDevices.Data
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *device) GetDeviceListMapping() gin.HandlerFunc {
	return func(c *gin.Context) {
		project := c.Param("project")
		version := c.Query("_version")
		results := make([]map[string]string, 0)
		if version == "factory" {
			devicesData, err := d.watcher.Mongodb().GetDeviceFactoryStatus(project)
			if err != nil {
				log.CtxLog(c).Errorf("failed to get device factory status, err: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{"error_code": 1, "message": err})
				return
			}
			devicesMap := make(map[string]struct{})
			for _, item := range devicesData {
				if _, has := devicesMap[item.DeviceId]; !has {
					results = append(results, map[string]string{"device_id": item.DeviceId, "description": item.Description})
					devicesMap[item.DeviceId] = struct{}{}
				}
			}
			c.JSON(http.StatusOK, gin.H{"error_code": 0, "data": results, "message": "ok", "total": len(devicesData)})
			return
		} else {
			devicesData, err := d.watcher.Mongodb().GetDeviceListMapping(project)
			if err != nil {
				log.CtxLog(c).Errorf("failed to get device list mapping, err: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{"error_code": 1, "message": err})
			}

			for _, item := range devicesData {
				if ucmd.GetEnv() != "prod" || item.IsActive || util.DeviceIsPowerCharger(project) {
					results = append(results, map[string]string{"device_id": item.DeviceId, "description": item.Description})
				}
			}
			c.JSON(http.StatusOK, gin.H{"error_code": 0, "data": results, "message": "ok", "total": len(results)})
			return
		}
	}
}

func (d *device) GetCityCompanyListMapping() gin.HandlerFunc {
	return func(c *gin.Context) {
		projectSet := map[string]bool{}
		projectsStr := c.Query("projects")
		if projectsStr != "" {
			projects := strings.Split(strings.TrimSpace(projectsStr), ",")
			for _, project := range projects {
				projectSet[project] = true
			}
		}
		resp := model.CityCompanyMappingResponse{}
		cityCompanyMap := map[string][]model.DeviceInfoVO{}
		devices := cache.PowerSwapCache.GetAllDevices()
		for _, deviceInfo := range devices {
			cityCompany := deviceInfo.CityCompany
			if cityCompany == "" {
				continue
			}
			if len(projectSet) != 0 {
				_, found := projectSet[deviceInfo.Project]
				if !found {
					continue
				}
			}
			if cityCompany != "" {
				cityCompanyMap[cityCompany] = append(cityCompanyMap[cityCompany], model.DeviceInfoVO{
					DeviceId:    deviceInfo.DeviceId,
					ResourceId:  deviceInfo.ResourceId,
					Description: deviceInfo.Description,
					Project:     deviceInfo.Project,
					CityCompany: deviceInfo.CityCompany,
					UpdatedTime: deviceInfo.UpdatedTime,
				})
			}
		}
		resp.Data = cityCompanyMap
		um.SuccessWithMessageForGin(c, &resp, "ok", http.StatusOK)
	}
}

func (d *device) ListAllDevices() gin.HandlerFunc {
	return func(c *gin.Context) {
		name := c.Query("name")
		deviceIdStrs := c.Query("device_ids")
		cityCompaniesStrs := c.Query("city_companies")
		areasStrs := c.Query("areas")
		deviceIds := map[string]bool{}
		cityCompanies := map[string]bool{}
		areas := map[string]bool{}
		if len(deviceIdStrs) != 0 {
			for _, deviceId := range strings.Split(strings.TrimSpace(deviceIdStrs), ",") {
				deviceIds[deviceId] = true
			}
		}
		if len(cityCompaniesStrs) != 0 {
			for _, cityCompany := range strings.Split(strings.TrimSpace(cityCompaniesStrs), ",") {
				cityCompanies[cityCompany] = true
			}
		}
		if len(areasStrs) != 0 {
			for _, area := range strings.Split(strings.TrimSpace(areasStrs), ",") {
				areas[area] = true
			}
		}
		projects := map[string]bool{}
		if c.Query("project") != "" {
			for _, project := range strings.Split(c.Query("project"), ",") {
				projects[project] = true
			}
		}
		resp := model.ListAllDevicesResponse{}
		allDevices := []model.DeviceInfoVO{}
		devices := cache.PowerSwapCache.GetAllDevices()
		devices = append(devices, cache.ChargerCache.GetAllDevices()...)
		for _, deviceInfo := range devices {
			if deviceInfo.DeviceId == "" {
				continue
			}
			if len(deviceIds) != 0 {
				if _, ok := deviceIds[deviceInfo.DeviceId]; !ok {
					continue
				}
			}
			if len(cityCompanies) != 0 {
				if _, ok := cityCompanies[deviceInfo.CityCompany]; !ok {
					continue
				}
			}
			if len(areas) != 0 {
				if _, ok := areas[deviceInfo.Area]; !ok {
					continue
				}
			}
			if name != "" && !strings.Contains(deviceInfo.DeviceId, name) && !strings.Contains(deviceInfo.Description, name) {
				continue
			}
			if len(projects) != 0 {
				if _, ok := projects[deviceInfo.Project]; !ok {
					continue
				}
			}
			allDevices = append(allDevices, model.DeviceInfoVO{
				DeviceId:    deviceInfo.DeviceId,
				ResourceId:  deviceInfo.ResourceId,
				Description: deviceInfo.Description,
				Project:     deviceInfo.Project,
				CityCompany: deviceInfo.CityCompany,
				Area:        deviceInfo.Area,
				UpdatedTime: deviceInfo.UpdatedTime,
			})
		}
		sort.Slice(allDevices, func(i, j int) bool {
			return allDevices[i].DeviceId < allDevices[j].DeviceId
		})
		limit := c.Query("limit")
		if limitInt, _ := strconv.Atoi(limit); limitInt > 0 && len(allDevices) > limitInt {
			allDevices = allDevices[:limitInt]
		}
		resp.Data = allDevices
		um.SuccessWithMessageForGin(c, &resp, "ok", http.StatusOK)
	}
}

func (d *device) UploadDevicesCsv() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.Response
		f, fh, err := c.Request.FormFile("file")
		if err != nil {
			log.CtxLog(c).Errorf("failed to upload devices csv, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		defer f.Close()
		if fh.Size > maxSize {
			log.CtxLog(c).Errorf("file size is limit to 2GB, get size: %d", fh.Size)
			um.FailWithBadRequest(c, &response, "file size is limit to 2GB")
			return
		}
		project := c.PostForm("project")
		reader := csv.NewReader(f)
		success := true
		errDevices := make([]string, 0)
		devices := make([]map[string]string, 0)
		for {
			record, rErr := reader.Read()
			if rErr == io.EOF {
				break
			}
			if rErr != nil {
				log.CtxLog(c).Errorf("fail to read csv, err: %v", rErr)
				um.FailWithBadRequest(c, &response, err.Error())
				return
			}
			if len(record) == 0 || record[0] == "" || record[0] == "device_id" {
				continue
			}
			deviceInfo, ok := cache.PowerSwapCache.GetSingleDevice(record[0])
			if !ok || deviceInfo == nil || deviceInfo.Project != project {
				success = false
				errDevices = append(errDevices, record[0])
				continue
			}
			devices = append(devices, map[string]string{
				"device_id":   deviceInfo.DeviceId,
				"description": deviceInfo.Description,
			})
		}
		if len(devices) > psos.SimulationLimit {
			err = fmt.Errorf("device count exceeds limit %d, upload devices %d", psos.SimulationLimit, len(devices))
			log.CtxLog(c).Error(err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		response.Data = devices
		if !success {
			log.CtxLog(c).Warnf("not all devices are valid, invalid devices: %s", strings.Join(errDevices, ", "))
			um.SuccessWithErrCode(c, &response, fmt.Sprintf("not all devices are valid, invalid devices: %s", strings.Join(errDevices, ", ")), -1)
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *device) UpdateOfflineAccount() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			requestData model.OfflineAccountParam
			response    model.OfflineAccountResponse
		)

		if err := c.BindJSON(&requestData); err != nil {
			log.CtxLog(c).Errorf("update offline account, parse request body, err: %v", err)
			um.FailWithMessageForGin(c, &response, err.Error(), -1, http.StatusBadRequest)
			return
		}
		table, ok := util.ConvertMapKey(requestData.AccountId)
		if !ok {
			log.CtxLog(c).Errorf("update offline account, parse request body, err: `account_id` is incorrect!")
			um.FailWithMessageForGin(c, &response, "update offline account, parse request body, err: `account_id` is incorrect!", -1, http.StatusBadRequest)
			return
		}
		account0 := umw.MongoAccountDetails{
			Id:          "assistant",
			Password:    fmt.Sprintf("%06v", rand.New(rand.NewSource(time.Now().UnixNano())).Int31n(1000000)),
			Description: "专员",
			Role:        1,
			UpdatedTime: time.Now(),
		}
		account1 := umw.MongoAccountDetails{
			Id:          "devops",
			Password:    fmt.Sprintf("%06v", rand.New(rand.NewSource(time.Now().UnixNano())).Int31n(1000000)),
			Description: "维护",
			Role:        2,
			UpdatedTime: time.Now(),
		}
		account2 := umw.MongoAccountDetails{
			Id:          "develop",
			Password:    fmt.Sprintf("%06v", rand.New(rand.NewSource(time.Now().UnixNano())).Int31n(1000000)),
			Description: "研发",
			Role:        4,
			UpdatedTime: time.Now(),
		}

		record := umw.MongoOfflineAccount{
			Project:  requestData.Project,
			DeviceId: requestData.DeviceId,
			Accounts: []umw.MongoAccountDetails{account0, account1, account2},
		}
		err := d.watcher.Mongodb().UpdateOfflineAccountInfo(umw.OAuthDB, umw.OfflineAccountInfo, record)
		if err != nil {
			um.SuccessWithErrCode(c, &response, fmt.Sprintf("get offline account password, err: %s", err.Error()), 1)
			return
		}
		for _, i := range record.Accounts {
			if _, has := table[i.Id]; has {
				response.Data = append(response.Data, model.OfflineAccountData{
					AccountId: i.Id,
					Password:  i.Password,
				})
			}
		}
		if len(response.Data) == 0 {
			um.SuccessWithErrCode(c, &response, "no matched offline accounts found", 1)
			return
		}
		um.SuccessWithMessageForGin(c, &response, "succeeded to get offline accounts", 0)
		return
	}
}

func (d *device) GetDeviceStatus(scene ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.DeviceStatusResponse
		)

		deviceId := c.Query("device_id")
		project := c.Param("project")
		if len(scene) == 0 {
			devicesData, err := d.watcher.Mongodb().GetDeviceListMapping(project, deviceId)
			if err != nil {
				log.CtxLog(c).Errorf("failed to get device list mapping, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			for _, item := range devicesData {
				response.Data = append(response.Data, map[string]interface{}{
					"device_id": item.DeviceId,
					"is_active": item.IsActive,
				})
			}
			response.Total = len(devicesData)
		} else if scene[0] == "factory" {
			if deviceId == "" {
				log.CtxLog(c).Errorf("failed to get device factory status, err: `device_id` is empty")
				um.FailWithBadRequest(c, &response, "`device_id` is empty")
				return
			}

			devicesData, err := d.watcher.Mongodb().GetDeviceFactoryStatus(project, deviceId)
			if err != nil {
				log.CtxLog(c).Errorf("failed to get device factory status, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			if len(devicesData) == 0 {
				conn := udao.NewRedisConn(d.watcher.Redis())
				tags, err := client.GetDeviceTag(conn, d.watcher.Mongodb(), deviceId)
				conn.Close()
				if err != nil {
					log.CtxLog(c).Errorf("failed to get device description, err: %s", err)
				}
				response.Data = append(response.Data, map[string]interface{}{
					"device_id":   deviceId,
					"description": tags.Description,
					"is_factory":  false, // to be deprecated
					"state":       1,     // 未出厂
				})
			} else {
				response.Data = append(response.Data, map[string]interface{}{
					"device_id":   deviceId,
					"description": devicesData[0].Description,
					"is_factory":  devicesData[0].IsFactory,
					"state":       devicesData[0].State,
				})
			}
			response.Total = 1
		}
		log.CtxLog(c).Infof("succeeded to get factory device status, device_id: %s", deviceId)
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *device) UpdateDeviceFactoryStatus() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			requestData model.FactoryDeviceStatus
			response    um.Base
		)

		project := c.Param("project")
		userId := c.GetHeader("X-User-ID")
		if userId == "" {
			log.CtxLog(c).Errorf("need header: X-User-ID")
			um.FailWithBadRequest(c, &response, "need header: X-User-ID")
			return
		}
		if err := c.BindJSON(&requestData); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		conn := udao.NewRedisConn(d.watcher.Redis())
		tags, err := client.GetDeviceTag(conn, d.watcher.Mongodb(), requestData.DeviceId)
		conn.Close()
		if err != nil {
			log.CtxLog(c).Errorf("cannot find the device name from mongo, device_id: %s, err: %v", requestData.DeviceId, err)
		}
		if requestData.IsFactory == nil && requestData.State == nil || (requestData.IsFactory != nil && requestData.State != nil) {
			log.CtxLog(c).Errorf("`is_factory` or `state`, you must specify one")
			um.FailWithBadRequest(c, &response, "`is_factory` or `state`, you must specify one")
			return
		}
		// to be deprecated
		factory, state := false, 1 // 未出厂
		if requestData.IsFactory != nil {
			if *requestData.IsFactory {
				factory, state = true, 2 // 已出厂
			}
		} else {
			state = *requestData.State
			if state > 1 {
				factory = true
			}
		}
		err = d.watcher.Mongodb().NewMongoEntry().ChangeFactoryDeviceStatus(requestData.DeviceId, state, umw.MongoDeviceFactoryInfo{
			Project:     project,
			DeviceId:    requestData.DeviceId,
			Description: tags.Description,
			Operator:    userId,
			IsFactory:   factory,
			State:       state,
			UpdatedTime: time.Now().UnixMilli(),
		})
		if err != nil {
			log.CtxLog(c).Errorf("failed to update device factory status, err: %v", err)
			if strings.Contains(err.Error(), "no changed") {
				um.FailWithBadRequest(c, &response, err.Error())
			} else {
				um.FailWithInternalServerError(c, &response, err.Error())
			}
			return
		}

		log.CtxLog(c).Infof("succeeded to update factory device status, device_id: %s", requestData.DeviceId)
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *device) TankTransferRecordList(area string) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			uriParam model.TankTransRecordParam
			response model.AddTotalResponse
		)
		project := c.Param("project")
		deviceId := c.Param("device_id")

		if err := c.BindQuery(&uriParam); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		// check parameters
		if err := util.CheckTimeRange(uriParam.StartTime, uriParam.EndTime); err != nil {
			log.CtxLog(c).Errorf("err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		util.SetURIParamDefault(&uriParam.CommonUriParam)

		tdw := &client.TDWatcher{
			TDClient: d.watcher.TDEngine(),
			DeviceId: deviceId,
			StartTs:  &uriParam.StartTime,
			EndTs:    &uriParam.EndTime,
			Logger:   log.CtxLog(c),
		}

		records, err := tdw.GetTankTransferDataList(uriParam.Slot, uriParam.TransId)
		if err != nil {
			log.CtxLog(c).Errorf("query tank transfer data, get error: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if uriParam.Descending {
			sort.Slice(records, func(i, j int) bool { return records[i].TransStartTS > records[j].TransStartTS })
		} else {
			sort.Slice(records, func(i, j int) bool { return records[i].TransStartTS < records[j].TransStartTS })
		}

		var total int
		results := make([]model.TankTransData, 0)
		skip, limit := (uriParam.Page-1)*uriParam.Size, uriParam.Page*uriParam.Size
		for _, r := range records {
			total++
			if total <= skip || total > limit {
				continue
			}
			if r.DrianedSlot == "A0" || r.DrianedSlot == "C0" {
				// A0,C0代表亏电仓为空
				r.DrianedSlot = ""
			}
			results = append(results, r)
		}

		go d.watcher.Mongodb().UpdateTankTransServiceInfo(project, deviceId, results)

		response.Data = results
		response.Total = total

		log.CtxLog(c).Infof("succeeded to get tank transfer data, start_time: %d end_time: %d, total: %d", uriParam.StartTime, uriParam.EndTime, total)
		um.SuccessWithMessageForGin(c, &response, "success", http.StatusOK)
		return
	}
}

func (d *device) TankTransferRecordDetails(area string) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			uriParam model.TankTransDetailParam
			response model.TankTransResponse
		)
		project := c.Param("project")
		deviceId := c.Param("device_id")
		transId := c.Param("trans_id")

		if area == um.Europe || project != umw.PUS3 {
			log.CtxLog(c).Warnf("project: %s, area: %s, query tank transfer record not supported", project, area)
			um.FailWithNotFound(c, &response, fmt.Sprintf("project: %s, area: Europe, query tank transfer record not supported", project))
			return
		}
		if err := c.BindQuery(&uriParam); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		// check parameters
		if err := util.CheckTimeRange(uriParam.StartTime, uriParam.EndTime); err != nil {
			log.CtxLog(c).Errorf("err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		record, err := d.watcher.Mongodb().FindTankTransDetail(transId)
		if err != nil {
			log.CtxLog(c).Errorf("failed to get tank transfer base info, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}

		response = model.TankTransResponse{
			TankTransData: model.TankTransData{
				TransId:         transId,
				TransStartTS:    record.TransStartTS,
				TransEndTS:      record.TransEndTS,
				FullChargedSlot: record.FullChargedSlot,
				DrianedSlot:     record.DrianedSlot,
				EmptySlot:       record.EmptySlot,
			},
		}
		if uriParam.StepNum == nil || uriParam.Axis == "" {
			log.CtxLog(c).Infof("succeeded to get tank transfer base info")
			um.SuccessWithMessageForGin(c, &response, "success", http.StatusOK)
			return
		}

		tdw := &client.TDWatcher{
			TDClient: d.watcher.TDEngine(),
			DeviceId: deviceId,
			StartTs:  &uriParam.StartTime,
			EndTs:    &uriParam.EndTime,
			Logger:   log.CtxLog(c),
		}

		result := make(map[string][]model.TTPSData)
		records, err := tdw.GetTankTransferDetail(transId, uriParam.StepNum)
		if err != nil {
			log.CtxLog(c).Errorf("query tank transfer data details, get error: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}

		for _, ax := range strings.Split(uriParam.Axis, ",") {
			if _, has := result[ax]; has {
				continue
			}
			result[ax] = records[ax]
			sort.Slice(result[ax], func(i, j int) bool { return result[ax][i].Timestamp < result[ax][j].Timestamp })
		}

		response.Data = result
		log.CtxLog(c).Infof("succeeded to get tank transfer data details, trans_id: %s", transId)
		um.SuccessWithMessageForGin(c, &response, "success", http.StatusOK)
		return
	}
}

func (d *device) BatteryRefresh() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.BatteryRefreshListResponse
		project := c.Param("project")
		deviceId := c.Param("device_id")

		if project == umw.PowerSwap2 {
			var request model.BatteryRefreshListRequest
			if err := c.BindQuery(&request); err != nil {
				log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
				um.FailWithBadRequest(c, &response, err.Error())
				return
			}
			filter := bson.D{bson.E{Key: "device_id", Value: deviceId}}
			if request.StartTime != 0 {
				filter = append(filter, bson.E{Key: "context.3209", Value: bson.M{"$gte": fmt.Sprintf("%v", request.StartTime), "$lte": fmt.Sprintf("%v", request.EndTime)}})
			}
			if request.BatteryId != "" {
				filter = append(filter, bson.E{Key: "context.3202", Value: request.BatteryId})
			}
			if request.RefreshResult != "" {
				filter = append(filter, bson.E{Key: "context.3200", Value: request.RefreshResult})
			}
			if request.RefreshType != "" {
				filter = append(filter, bson.E{Key: "context.3207", Value: request.RefreshType})
			}
			if request.BatteryCapacity != "" {
				filter = append(filter, bson.E{Key: "context.3204", Value: request.BatteryCapacity})
			}
			if request.RefreshTargetVersion != "" {
				filter = append(filter, bson.E{Key: "context.3203", Value: request.RefreshTargetVersion})
			}
			byteData, total, err := d.watcher.Mongodb().NewMongoEntry(filter).ListByPagination("device_event", fmt.Sprintf("battery_refresh_%v", strings.ToLower(project)),
				client.Pagination{Limit: int64(request.Size), Offset: int64((request.Page - 1) * request.Size)},
				client.Ordered{Key: "context.3209", Descending: true})
			if err != nil {
				log.CtxLog(c).Errorf("list device_event, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			var mongoDeviceEvents []MongoDeviceEvent
			if err = json.Unmarshal(byteData, &mongoDeviceEvents); err != nil {
				log.CtxLog(c).Errorf("get mongoDeviceEvents, fail to unmarshal mongoDeviceEvents, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			var batteryRefreshVOs []model.BatteryRefreshVO
			for _, mongoDeviceEvent := range mongoDeviceEvents {
				vo := d.convertBatteryRefreshEventPO2VO(mongoDeviceEvent)
				batteryRefreshVOs = append(batteryRefreshVOs, vo)
			}
			response.Total = int(total)
			response.Data = batteryRefreshVOs
		} else {
			var request model.BatteryRefreshListV2Request
			if err := c.BindQuery(&request); err != nil {
				log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
				um.FailWithBadRequest(c, &response, err.Error())
				return
			}
			filter := bson.D{
				{"project", project},
				{"device_id", deviceId},
				{"start_time", bson.M{"$gte": request.StartTime, "$lte": request.EndTime}},
			}
			if request.BatteryId != "" {
				filter = append(filter, bson.E{Key: "battery_id", Value: request.BatteryId})
			}
			if request.RefreshResult != nil {
				filter = append(filter, bson.E{Key: "refresh_result", Value: *request.RefreshResult})
			}
			if request.RefreshType != nil {
				filter = append(filter, bson.E{Key: "refresh_type", Value: *request.RefreshType})
			}
			if request.BatteryCapacity != nil {
				batteryOriginList := domain_common.GetBatteryOriginType(*request.BatteryCapacity)
				filter = append(filter, bson.E{Key: "battery_capacity", Value: bson.M{"$in": batteryOriginList}})
			}
			if request.RefreshTargetVersion != "" {
				filter = append(filter, bson.E{Key: "refresh_target_version", Value: request.RefreshTargetVersion})
			}
			opts := options.Find().SetSort(bson.D{{"start_time", -1}})
			var res []mmgo.BatteryRefreshEvent
			total, err := d.watcher.Mongodb().NewMongoEntry(filter).FindMany(mmgo.DBDeviceEvent, fmt.Sprintf("%s_%s", mmgo.CollectionBatteryRefresh, strings.ToLower(project)), opts, &res)
			if err != nil {
				log.CtxLog(c).Errorf("BatteryRefresh, list battery refresh event, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			response.Total = int(total)
			for _, po := range res {
				response.Data = append(response.Data, convertBatteryRefreshEventPO2VOv2(po))
			}
		}

		um.SuccessWithMessageForGin(c, &response, "success", http.StatusOK)
		return
	}
}

func convertBatteryRefreshEventPO2VOv2(po mmgo.BatteryRefreshEvent) model.BatteryRefreshVO {
	vo := model.BatteryRefreshVO{
		DeviceId:                  po.DeviceId,
		StartTime:                 po.StartTime,
		EndTime:                   po.EndTime,
		BatteryId:                 po.BatteryId,
		RefreshType:               fmt.Sprintf("%d", po.RefreshType),
		RefreshResult:             fmt.Sprintf("%d", po.RefreshResult),
		RefreshTargetVersion:      po.RefreshTargetVersion,
		BatteryCapacity:           po.BatteryCapacity,
		BatteryRoad:               fmt.Sprintf("%d", po.BatteryRoad),
		OssRequestId:              po.OssRequestId,
		OssCommandBatteryId:       po.OssCommandBatteryId,
		OssCommandBatteryCapacity: po.OssCommandBatteryCapacity,
		AuthenticateTimestamp:     po.AuthenticateTimestamp,
		OrderId:                   po.Rid,
	}
	batteryCapacity := int32(util.ParseInt(po.BatteryCapacity))
	userType := domain_common.ConvertBatteryUserType(&batteryCapacity)
	if userType != nil {
		vo.BatteryCapacity = fmt.Sprintf("%d", *userType)
	}
	return vo
}

func (d *device) convertBatteryRefreshEventPO2VO(PO MongoDeviceEvent) model.BatteryRefreshVO {
	startTime := int64(0)
	endTime := int64(0)
	batteryId := ""
	refreshType := ""
	refreshResult := ""
	batteryCapacity := ""
	refreshTargetVersion := ""
	batteryRoad := ""

	value, found := PO.Context["3209"]
	if found {
		timestamp, err := strconv.ParseInt(value, 10, 64)
		if err == nil {
			startTime = timestamp
		}
	}
	value, found = PO.Context["3210"]
	if found {
		timestamp, err := strconv.ParseInt(value, 10, 64)
		if err == nil {
			endTime = timestamp
		}
	}
	value, found = PO.Context["3202"]
	if found {
		batteryId = value
	}
	value, found = PO.Context["3207"]
	if found {
		refreshType = value
	}
	value, found = PO.Context["3200"]
	if found {
		refreshResult = value
	}
	value, found = PO.Context["3204"]
	if found {
		batteryCapacity = value
	}
	value, found = PO.Context["3203"]
	if found {
		refreshTargetVersion = value
	}
	value, found = PO.Context["3208"]
	if found {
		batteryRoad = value
	}

	vo := model.BatteryRefreshVO{
		DeviceId:             PO.DeviceId,
		StartTime:            startTime,
		EndTime:              endTime,
		BatteryId:            batteryId,
		RefreshType:          refreshType,
		RefreshResult:        refreshResult,
		BatteryCapacity:      batteryCapacity,
		RefreshTargetVersion: refreshTargetVersion,
		BatteryRoad:          batteryRoad,
	}
	ossRequestId, found := PO.Context["3201"]
	if found && PO.Context["3201"] != "" {
		vo.OssRequestId = &ossRequestId
	}
	ossCommandBatteryId, found := PO.Context["3205"]
	if found && PO.Context["3205"] != "" {
		vo.OssCommandBatteryId = &ossCommandBatteryId
	}
	ossCommandBatteryCapacity, found := PO.Context["3206"]
	if found && PO.Context["3206"] != "" {
		vo.OssCommandBatteryCapacity = &ossCommandBatteryCapacity
	}
	authenticateTimestampStr, found := PO.Context["3211"]
	if found && PO.Context["3211"] != "" {
		timestamp, err := strconv.ParseInt(authenticateTimestampStr, 10, 64)
		if err == nil {
			(&vo).AuthenticateTimestamp = &timestamp
		}
	}
	orderId, found := PO.Context["3212"]
	if found && PO.Context["3212"] != "" {
		vo.OrderId = &orderId
	}
	return vo
}

type MongoDeviceEvent struct {
	DeviceId string            `json:"device_id" bson:"device_id"`
	Context  map[string]string `json:"context" bson:"context"`
	CreateTs int64             `json:"create_ts" bson:"create_ts"`
	MsgTs    int64             `json:"msg_ts" bson:"msg_ts"`
}

func (d *device) DownloadFile() gin.HandlerFunc {
	return func(c *gin.Context) {
		url := c.Query("image_url")
		if strings.Contains(url, "minio") {
			url = strings.Replace(url, "https://", "http://", 1)
		}
		size, _ := strconv.Atoi(c.Query("image_size"))
		fileName := path.Base(url)
		res, err := http.Get(url)
		if err != nil {
			log.CtxLog(c).Errorf("http.Get err: %v", err)
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error_code": 1, "message": err.Error()})
			return
		}
		defer res.Body.Close()
		reader := bufio.NewReaderSize(res.Body, size)
		imageByte, _ := ioutil.ReadAll(reader)

		c.Writer.Header().Set("Content-Type", "image/jpeg")

		util.Download(c, fileName, imageByte)
	}
}

func (d *device) UpdateFavorite() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  umw.MongoFavoriteDevice
			response um.Base
		)
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		request.Id = primitive.ObjectID{}
		userId := c.GetHeader("X-User-ID")
		if userId == "" {
			log.CtxLog(c).Errorf("need header: X-User-ID")
			um.FailWithBadRequest(c, &response, "need header: X-User-ID")
			return
		}
		request.UserId = userId

		err := d.watcher.Mongodb().NewMongoEntry(bson.D{
			bson.E{Key: "user_id", Value: userId},
			bson.E{Key: "device_id", Value: request.DeviceId},
		}).ReplaceOne(umw.Favorites, umw.Devices, request, true, client.IndexOption{
			Name:   "userId_deviceId_combine_unique",
			Fields: bson.D{{"user_id", 1}, {"device_id", 1}},
			Unique: true,
		})
		if err != nil {
			log.CtxLog(c).Errorf("fail to update device favorite list, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		log.CtxLog(c).Infof("succeed to update device favorite")
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *device) FilterImageByBatteryInfo() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.AddTotalResponse
		)
		var uriParam struct {
			model.CommonUriInTimeRangeParam
			BatteryId string  `json:"battery_id" form:"battery_id" uri:"battery_id"`
			ImageType *string `json:"image_type" form:"image_type" uri:"image_type"`
			Abnormal  *int    `json:"abnormal" form:"abnormal" uri:"abnormal"`
		}
		if err := c.BindQuery(&uriParam); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if uriParam.StartTime == 0 || uriParam.EndTime == 0 {
			log.CtxLog(c).Errorf("`start_time` and `end_time` are required!")
			um.FailWithBadRequest(c, &response, "`start_time` and `end_time` are required!")
			return
		}
		if uriParam.BatteryId == "" {
			log.CtxLog(c).Errorf("`battery_id` is required!")
			um.FailWithBadRequest(c, &response, "`battery_id` is required!")
			return
		}
		util.SetURIParamDefault(&uriParam.CommonUriParam)

		filter := bson.D{bson.E{Key: "battery_id", Value: uriParam.BatteryId}, bson.E{
			Key: "image_gen_time", Value: bson.M{"$gte": uriParam.StartTime, "$lte": uriParam.EndTime},
		}}
		if uriParam.Abnormal != nil {
			if *uriParam.Abnormal == 1 {
				filter = append(filter, bson.E{Key: "abnormal", Value: true})
			} else if *uriParam.Abnormal == 0 {
				filter = append(filter, bson.E{Key: "abnormal", Value: false})
			} else {
				log.CtxLog(c).Errorf("`abnormal`: %d is invalid!", *uriParam.Abnormal)
				um.FailWithBadRequest(c, &response, "`abnormal` can only be set 0 or 1!")
				return
			}
		}
		if uriParam.ImageType != nil {
			imageTypeList := make([]int, 0)
			for _, item := range strings.Split(*uriParam.ImageType, ",") {
				imgType, err := strconv.Atoi(strings.Trim(item, " "))
				if err != nil {
					log.CtxLog(c).Errorf("failed to parse image type: %s for battery history: %v", item, err)
					continue
				}
				imageTypeList = append(imageTypeList, imgType)
			}
			filter = append(filter, bson.E{Key: "image_type", Value: bson.M{"$in": imageTypeList}})
		}
		byteData, total, err := d.watcher.Mongodb().NewMongoEntry(filter).ListByPagination(umw.BatteryManagement, "image",
			client.Pagination{Limit: int64(uriParam.Size), Offset: int64((uriParam.Page - 1) * uriParam.Size)},
			client.Ordered{Key: "image_gen_time", Descending: uriParam.Descending})
		if err != nil {
			log.CtxLog(c).Errorf("failed to filter images by battery id, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var records []model.AlgorithmImageInfo
		if err = json.Unmarshal(byteData, &records); err != nil {
			log.CtxLog(c).Errorf("failed to unmarshal data filtered images by battery id, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Total = int(total)
		mu := sync.Mutex{}
		devicesMap := make(map[string]client.Tags)
		readDeviceDescription := func(i int) {
			if v, has := devicesMap[records[i].DeviceId]; !has {
				conn := udao.NewRedisConn(d.watcher.Redis())
				tag, err := client.GetDeviceTag(conn, d.watcher.Mongodb(), records[i].DeviceId)
				conn.Close()
				if err != nil {
					log.CtxLog(c).Errorf("cannot find the device %s description, err: %v", records[i].DeviceId, err)
				} else {
					records[i].Project = tag.Project
					records[i].Description = tag.Description
					mu.Lock()
					devicesMap[records[i].DeviceId] = tag
					mu.Unlock()
				}
			} else {
				records[i].Project = v.Project
				records[i].Description = v.Description
			}
		}
		ucmd.ParallelizeExec(len(records), readDeviceDescription, 500)
		response.Data = records
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (d *device) DownloadImageByBatteryInfo() gin.HandlerFunc {
	return func(c *gin.Context) {
		var uriParam struct {
			StartTime int64   `form:"start_time" json:"start_time" uri:"start_time"`
			EndTime   int64   `form:"end_time" json:"end_time" uri:"end_time"`
			BatteryId string  `json:"battery_id" form:"battery_id" uri:"battery_id"`
			ImageType *string `json:"image_type" form:"image_type" uri:"image_type"`
			Abnormal  *int    `json:"abnormal" form:"abnormal" uri:"abnormal"`
			RecordId  string  `json:"record_id" form:"record_id" uri:"record_id"`
		}
		var response um.Base
		if err := c.BindQuery(&uriParam); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if uriParam.RecordId == "" {
			log.CtxLog(c).Errorf("`record_id` is required!")
			um.FailWithBadRequest(c, &response, "`record_id` is required!")
			return
		}

		if uriParam.RecordId == "all" {
			if uriParam.StartTime == 0 || uriParam.EndTime == 0 {
				log.CtxLog(c).Errorf("`start_time` and `end_time` are required!")
				um.FailWithBadRequest(c, &response, "`start_time` and `end_time` are required!")
				return
			}
			if uriParam.BatteryId == "" {
				log.CtxLog(c).Errorf("`battery_id` is required!")
				um.FailWithBadRequest(c, &response, "`battery_id` is required!")
				return
			}
			filter := bson.D{bson.E{Key: "battery_id", Value: uriParam.BatteryId}, bson.E{
				Key: "image_gen_time", Value: bson.M{"$gte": uriParam.StartTime, "$lte": uriParam.EndTime},
			}}
			if uriParam.Abnormal != nil {
				if *uriParam.Abnormal == 1 {
					filter = append(filter, bson.E{Key: "abnormal", Value: true})
				} else if *uriParam.Abnormal == 0 {
					filter = append(filter, bson.E{Key: "abnormal", Value: false})
				} else {
					log.CtxLog(c).Errorf("`abnormal`: %d is invalid!", *uriParam.Abnormal)
					um.FailWithBadRequest(c, &response, "`abnormal` can only be set 0 or 1!")
					return
				}
			}
			if uriParam.ImageType != nil {
				imageTypeList := make([]int, 0)
				for _, item := range strings.Split(*uriParam.ImageType, ",") {
					imgType, err := strconv.Atoi(strings.Trim(item, " "))
					if err != nil {
						log.CtxLog(c).Errorf("failed to parse image type: %s for battery history: %v", item, err)
						continue
					}
					imageTypeList = append(imageTypeList, imgType)
				}
				filter = append(filter, bson.E{Key: "image_type", Value: bson.M{"$in": imageTypeList}})
			}
			byteData, err := d.watcher.Mongodb().NewMongoEntry(filter).ListAll(
				umw.BatteryManagement, "image", client.Ordered{})
			if err != nil {
				log.CtxLog(c).Errorf("failed to filter images by battery id, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			var records []model.AlgorithmImageInfo
			if err = json.Unmarshal(byteData, &records); err != nil {
				log.CtxLog(c).Errorf("failed to unmarshal data filtered images by battery id, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			// 拼接压缩包名字
			zipName := fmt.Sprintf("%s-%s-%s.zip", uriParam.BatteryId, util.DecodeTime(time.UnixMilli(uriParam.StartTime)),
				util.DecodeTime(time.UnixMilli(uriParam.EndTime)))

			// 设置rw的header信息中的content-type，对于zip可选以下两种
			// rw.Header().Set("Content-Type", "application/octet-stream")
			rw := c.Writer
			rw.Header().Set("Content-Type", "application/zip")
			// 设置rw的header信息中的Content-Disposition为attachment类型
			rw.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", zipName))
			zipW := zip.NewWriter(rw)
			defer zipW.Close()

			mu := sync.Mutex{}
			downloadExec := func(i int) {
				if records[i].ImageURL == "" {
					return
				}
				res, err := http.Get(records[i].ImageURL)
				if err != nil {
					log.CtxLog(c).Errorf("http.Get err: %v", err)
					return
				}
				imageByte, err := io.ReadAll(res.Body)
				if err != nil {
					log.CtxLog(c).Errorf("failed to read http body, err: %v", err)
					return
				}
				res.Body.Close()

				mu.Lock()
				defer mu.Unlock()
				// path.Base 取以斜杠为界限的最后一个元素
				f, err := zipW.Create(records[i].DeviceId + "/" + records[i].ImageName)
				if err != nil {
					log.CtxLog(c).Errorf("zipW.Create err: %v", err)
					return
				}
				if _, err = f.Write(imageByte); err != nil {
					log.CtxLog(c).Errorf("f.Write err: %v", err)
					return
				}
			}
			ucmd.ParallelizeExec(len(records), downloadExec, 100)
		} else {
			objID, err := primitive.ObjectIDFromHex(uriParam.RecordId)
			if err != nil {
				log.CtxLog(c).Errorf("failed to parse record id in battery management, err: %v", err)
				um.FailWithBadRequest(c, &response, err.Error())
				return
			}
			rawData, err := d.watcher.Mongodb().NewMongoEntry(bson.D{bson.E{Key: "_id", Value: objID}}).GetOne(
				umw.BatteryManagement, "image")
			if err != nil {
				log.CtxLog(c).Errorf("failed to get one image by record id, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			if rawData != nil {
				var record model.AlgorithmImageInfo
				if err = bson.Unmarshal(rawData, &record); err != nil {
					log.CtxLog(c).Errorf("failed to parse one image by record id, err: %v", err)
					um.FailWithInternalServerError(c, &response, err.Error())
					return
				}
				res, err := http.Get(record.ImageURL)
				if err != nil {
					log.CtxLog(c).Errorf("http.Get err: %v", err)
				} else {
					imageByte, err := io.ReadAll(res.Body)
					if err != nil {
						log.CtxLog(c).Errorf("failed to read http body, err: %v", err)
						return
					}
					res.Body.Close()
					util.Download(c, record.ImageName, imageByte)
				}
			}
		}
	}
}

func (d *device) FilterServiceByBatteryInfo() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.AddTotalResponse
		)
		var uriParam struct {
			model.CommonUriParam
			StartTime       string `json:"start_time" form:"start_time" uri:"start_time"`
			EndTime         string `json:"end_time" form:"end_time" uri:"end_time"`
			BatteryId       string `json:"battery_id" form:"battery_id" uri:"battery_id"`
			EvId            string `json:"ev_id" form:"ev_id" uri:"ev_id"`
			ImageStatusCode *int   `json:"image_status_code" form:"image_status_code" uri:"image_status_code"`
			FinishResult    *int   `json:"finish_result" form:"finish_result" uri:"finish_result"`
		}
		if err := c.BindQuery(&uriParam); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if uriParam.StartTime == "" || uriParam.EndTime == "" {
			log.CtxLog(c).Errorf("`start_time` and `end_time` are required!")
			um.FailWithBadRequest(c, &response, "`start_time` and `end_time` are required!")
			return
		}
		if uriParam.BatteryId == "" && uriParam.EvId == "" {
			log.CtxLog(c).Errorf("`battery_id` or `ev_id` is required!")
			um.FailWithBadRequest(c, &response, "`battery_id` or `ev_id` is required!")
			return
		}
		util.SetURIParamDefault(&uriParam.CommonUriParam)

		filter := bson.D{}
		if uriParam.BatteryId != "" {
			filter = append(filter, bson.E{Key: "$or", Value: bson.A{
				bson.D{{"ev_battery_id", uriParam.BatteryId}},
				bson.D{{"service_battery_id", uriParam.BatteryId}},
			}})
		}
		if uriParam.EvId != "" {
			filter = append(filter, bson.E{Key: "ev_id", Value: uriParam.EvId})
		}
		filter = append(filter, bson.E{Key: "create_time", Value: bson.M{"$gte": uriParam.StartTime, "$lte": uriParam.EndTime}})

		byteData, err := d.watcher.Mongodb().NewMongoEntry(filter).ListAll(umw.BatteryManagement, "service2",
			client.Ordered{Key: "create_time", Descending: true})
		if err != nil {
			log.CtxLog(c).Errorf("failed to filter services from battery management, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var records []umw.MongoBatteryServiceInfo
		if err = json.Unmarshal(byteData, &records); err != nil {
			log.CtxLog(c).Errorf("failed to unmarshal data filtered services from battery management, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		// e.g. {"PUS3": {"11_12": ["PS-NIO-ad1100b5-98123ce66262fe2c9ad14ffba80042ea8a570d611698768897244"]}}
		toFindDocs := make(map[string]map[string][]string)
		serviceUniqMap := make(map[string]struct{})
		for _, record := range records {
			if toFindDocs[record.Project] == nil {
				toFindDocs[record.Project] = make(map[string][]string)
			}
			if toFindDocs[record.Project][util.EncodeDate(record.CreateTime)] == nil {
				toFindDocs[record.Project][util.EncodeDate(record.CreateTime)] = make([]string, 0)
			}
			if _, has := serviceUniqMap[record.ServiceId]; !has {
				toFindDocs[record.Project][util.EncodeDate(record.CreateTime)] = append(
					toFindDocs[record.Project][util.EncodeDate(record.CreateTime)], record.ServiceId)
			}
		}
		servicesData := make([]umw.MongoServiceInfo, 0)
		mu := sync.Mutex{}
		wg := sync.WaitGroup{}
		wg.Add(len(toFindDocs))
		for project := range toFindDocs {
			go func(project string) {
				defer wg.Done()
				colList := make([]string, 0)
				for col := range toFindDocs[project] {
					colList = append(colList, col)
				}
				dbName := fmt.Sprintf("serviceinfo-%s", ucmd.RenameProjectDB(project))
				findServices := func(i int) {
					colByteData, colErr := d.watcher.Mongodb().NewMongoEntry(bson.D{bson.E{Key: "service_id",
						Value: bson.M{"$in": toFindDocs[project][colList[i]]}}}).ListAll(dbName, colList[i],
						client.Ordered{Key: "date", Descending: uriParam.Descending})
					if colErr != nil {
						log.CtxLog(c).Errorf("failed to filter services, db: %s, col: %s, filter: %v", dbName, colList[i], toFindDocs[project][colList[i]])
					} else {
						var colRecords []umw.MongoServiceInfo
						if colErr = json.Unmarshal(colByteData, &colRecords); colErr != nil {
							log.CtxLog(c).Errorf("failed to unmarshal services info, err: %v", err)
						} else {
							mu.Lock()
							servicesData = append(servicesData, colRecords...)
							mu.Unlock()
						}
					}
				}
				ucmd.ParallelizeExec(len(colList), findServices)
			}(project)
		}
		wg.Wait()
		if uriParam.Descending {
			sort.Slice(servicesData, func(i, j int) bool { return servicesData[i].Date.UnixMilli() > servicesData[j].Date.UnixMilli() })
		} else {
			sort.Slice(servicesData, func(i, j int) bool { return servicesData[i].Date.UnixMilli() < servicesData[j].Date.UnixMilli() })
		}

		var total int
		results := make([]model.ServiceHistoryInfoData, 0)
		startIndex, endIndex := (uriParam.Page-1)*uriParam.Size, uriParam.Page*uriParam.Size
		for _, item := range servicesData {
			conn := udao.NewRedisConn(d.watcher.Redis())
			tags, err := client.GetDeviceTag(conn, d.watcher.Mongodb(), item.DeviceId)
			conn.Close()
			if err != nil {
				log.CtxLog(c).Errorf("cannot find the device name from mongo, device_id: %s, err: %v", item.DeviceId, err)
			}
			if uriParam.ImageStatusCode != nil {
				dbName := fmt.Sprintf("%s-%s", umw.ImageInfo, ucmd.RenameProjectDB(tags.Project))
				abnormalErr, normalErr := d.watcher.Mongodb().NewMongoEntry().ImagesIsNormal(dbName, item.DeviceId, item.ServiceId)
				if *uriParam.ImageStatusCode == 0 && normalErr != nil || (*uriParam.ImageStatusCode == 1 && abnormalErr != nil) {
					// image_status_code为0，但不存在正常图片则跳过
					// image_status_code为1，但不存在异常图片则跳过
					continue
				}
			}
			if uriParam.FinishResult != nil {
				if (*uriParam.FinishResult == 0 && item.FinishResult != 0) || (*uriParam.FinishResult == 1 && item.FinishResult != 1) {
					// finish_result为0，表示异常，但订单正常结束则跳过
					// finish_result为1，表示正常，但订单非正常结束则跳过
					continue
				}
			}
			total++
			if total <= startIndex || total > endIndex {
				continue
			}
			results = append(results, model.ServiceHistoryInfoData{
				Description:      tags.Description,
				Project:          tags.Project,
				DeviceId:         item.DeviceId,
				ServiceId:        item.ServiceId,
				EVBatteryId:      item.EVBatteryId,
				ServiceBatteryId: item.ServiceBatteryId,
				EvId:             item.EvId,
				FinishResult:     item.FinishResult,
				StartTime:        item.StartTime,
				EndTime:          item.EndTime,
			})
		}
		response.Total = total
		response.Data = results
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (d *device) FilterSlotByBatteryInfo() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.AddTotalResponse
		)
		var uriParam struct {
			model.CommonUriInTimeRangeParam
			BatteryId string `json:"battery_id" form:"battery_id" uri:"battery_id"`
			SlotId    string `json:"slot_id" form:"slot_id" uri:"slot_id"`
			EventId   string `json:"event_id" form:"event_id" uri:"event_id"`
			DeviceId  string `json:"device_id" form:"device_id" uri:"device_id"`
		}
		if err := c.BindQuery(&uriParam); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if uriParam.StartTime == 0 || uriParam.EndTime == 0 {
			log.CtxLog(c).Errorf("`start_time` and `end_time` are required!")
			um.FailWithBadRequest(c, &response, "`start_time` and `end_time` are required!")
			return
		}
		util.SetURIParamDefault(&uriParam.CommonUriParam)

		filter := bson.D{bson.E{Key: "timestamp", Value: bson.M{"$gte": uriParam.StartTime, "$lte": uriParam.EndTime}}}
		if uriParam.BatteryId != "" {
			filter = append(filter, bson.E{Key: "battery_id", Value: uriParam.BatteryId})
		}
		if uriParam.DeviceId != "" {
			filter = append(filter, bson.E{Key: "device_id", Value: uriParam.DeviceId})
		}
		if uriParam.SlotId != "" {
			filter = append(filter, bson.E{Key: "slot_id", Value: uriParam.SlotId})
		}
		if uriParam.EventId != "" {
			eventIdList := make([]float64, 0)
			for _, item := range strings.Split(uriParam.EventId, ",") {
				eventId, err := strconv.ParseFloat(item, 64)
				if err != nil {
					log.CtxLog(c).Errorf("invalid event id: %s, err: %v", item, err)
					continue
				}
				eventIdList = append(eventIdList, eventId)
			}
			filter = append(filter, bson.E{Key: "reason.event_id", Value: bson.M{"$in": eventIdList}})
		}
		byteData, total, err := d.watcher.Mongodb().NewMongoEntry(filter).ListByPagination(umw.BatteryManagement, "slot",
			client.Pagination{Limit: int64(uriParam.Size), Offset: int64((uriParam.Page - 1) * uriParam.Size)},
			client.Ordered{Key: "timestamp", Descending: uriParam.Descending})
		if err != nil {
			log.CtxLog(c).Errorf("failed to filter images by battery id, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var records []struct {
			Project   string                 `json:"project" bson:"project"`
			DeviceId  string                 `json:"device_id" bson:"device_id"`
			Type      int32                  `json:"type" bson:"type"`
			BatteryId string                 `json:"battery_id" bson:"battery_id"`
			Timestamp int64                  `json:"timestamp" bson:"timestamp"`
			SlotId    string                 `json:"slot_id" bson:"slot_id"`
			Reason    map[string]interface{} `json:"reason" bson:"reason"`
		}
		if err = json.Unmarshal(byteData, &records); err != nil {
			log.CtxLog(c).Errorf("failed to unmarshal data, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Total = int(total)
		mu := sync.Mutex{}
		results := make([]model.SlotBatteryInfo, len(records))
		devicesMap := make(map[string]client.Tags)
		readDeviceDescription := func(i int) {
			results[i].EventId = records[i].Reason["event_id"]
			results[i].SlotId = records[i].SlotId
			results[i].BatteryId = records[i].BatteryId
			results[i].Project = records[i].Project
			results[i].DeviceId = records[i].DeviceId
			results[i].Type = records[i].Type
			results[i].Timestamp = records[i].Timestamp
			results[i].Related = records[i].Reason
			delete(records[i].Reason, "event_id")
			if v, has := devicesMap[results[i].DeviceId]; !has {
				conn := udao.NewRedisConn(d.watcher.Redis())
				tag, err := client.GetDeviceTag(conn, d.watcher.Mongodb(), results[i].DeviceId)
				conn.Close()
				if err != nil {
					log.CtxLog(c).Errorf("cannot find the device %s description, err: %v", results[i].DeviceId, err)
				} else {
					results[i].Description = tag.Description
					mu.Lock()
					devicesMap[results[i].DeviceId] = tag
					mu.Unlock()
				}
			} else {
				results[i].Description = v.Description
			}
		}
		ucmd.ParallelizeExec(len(records), readDeviceDescription, 500)
		response.Data = results
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (d *device) GetFavoriteList() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.CommonUriParam
			response model.DeviceFavoriteResponse
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if request.Page == 0 {
			request.Page = 1
		}
		if request.Size == 0 {
			request.Size = 10
		}
		userId := c.GetHeader("X-User-ID")
		if userId == "" {
			log.CtxLog(c).Errorf("need header: X-User-ID")
			um.FailWithBadRequest(c, &response, "need header: X-User-ID")
			return
		}

		byteData, total, err := d.watcher.Mongodb().NewMongoEntry(bson.D{
			bson.E{Key: "user_id", Value: userId},
			bson.E{Key: "favorite", Value: true}}).ListByPagination(umw.Favorites, umw.Devices,
			client.Pagination{Limit: int64(request.Size), Offset: int64((request.Page - 1) * request.Size)}, client.Ordered{})
		if err != nil {
			log.CtxLog(c).Errorf("failed to get favorite devices, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if err = json.Unmarshal(byteData, &response.Data); err != nil {
			log.CtxLog(c).Errorf("failed to unmarshal favorite devices, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Total = int(total)
		projectMap := map[string]int{
			umw.PowerSwap:  1,
			umw.PowerSwap2: 2,
			umw.PUS3:       3,
		}
		sort.Slice(response.Data, func(i, j int) bool {
			d1, d2 := response.Data[i], response.Data[j]
			return (projectMap[d1.Project] > projectMap[d2.Project]) || (projectMap[d1.Project] == projectMap[d2.Project] && d1.DeviceId < d2.DeviceId)
		})
		log.CtxLog(c).Infof("succeed to get favorite devices")
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

type mDeviceKey struct {
	id          primitive.ObjectID
	description string
	resourceId  string
	isActive    bool
	isLogin     bool
}

func (d *device) getDeviceKeys(c *gin.Context, requestType, dbName, colName, identifier string) (mDeviceKey, error) {
	var res mDeviceKey
	rawData, err := d.watcher.Mongodb().NewMongoEntry(
		bson.D{bson.E{Key: "$or", Value: bson.A{
			bson.D{{"device_id", identifier}},
			bson.D{{"device_identifier", identifier}},
		}}}).GetOne(dbName, colName)
	if err != nil {
		log.CtxLog(c).Errorf("failed to get device %s info, err: %v", identifier, err)
		return res, err
	}
	if rawData == nil {
		log.CtxLog(c).Warnf("the device %s is not found", identifier)
		return res, errors.New("the device is not found")
	}

	switch requestType {
	case um.BrownDragon:
	default:
		var deviceData umw.MongoDeviceInfo
		if err = bson.Unmarshal(rawData, &deviceData); err != nil {
			log.CtxLog(c).Errorf("failed to unmarshal device %s info, err: %v", identifier, err)
			return res, err
		}
		res = mDeviceKey{
			id:          deviceData.Id,
			description: deviceData.Description,
			resourceId:  deviceData.ResourceId,
			isLogin:     deviceData.IsLogin,
			isActive:    deviceData.IsActive,
		}
	}
	return res, nil
}

func (d *device) V2GRemoteControl() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.V2GRemoteControlRequest
			response um.Base
		)
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		userId := c.GetHeader("X-User-ID")
		if userId == "" {
			log.CtxLog(c).Errorf("need user id")
			um.FailWithBadRequest(c, &response, "need user id")
			return
		}

		g := ucmd.NewErrGroup(c, 100)
		for i := range request.DeviceList {
			v2gDevice := request.DeviceList[i]
			g.GoRecover(func() error {
				paramValues := map[string]interface{}{
					"connector_id": v2gDevice.ConnectorId,
					"work_mode":    request.WorkMode,
				}
				if request.WorkType == "4" && request.WorkMode == "1" {
					if request.DischargeTime != nil {
						paramValues["discharge_time"] = *request.DischargeTime
					} else {
						paramValues["discharge_time"] = "0"
					}
					if request.StopSoc != nil {
						paramValues["stop_soc"] = *request.StopSoc
					} else {
						paramValues["stop_soc"] = "20"
					}
				}
				res, ok := d.OSS.SendV2GControlCommand(userId, v2gDevice.DeviceId, request.WorkType, paramValues)
				if !ok {
					log.CtxLog(c).Errorf("fail to send v2g control command, response: %s", ucmd.ToJsonStrIgnoreErr(res))
					return fmt.Errorf("fail to send v2g control command, response: %s", ucmd.ToJsonStrIgnoreErr(res))
				}
				return nil
			})
		}
		if err := g.Wait(); err != nil {
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *device) ListServiceVisual() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.ListServiceVisualRequest
			response model.ListServiceVisualResponse
		)
		err := c.BindQuery(&request)
		if err != nil {
			log.CtxLog(c).Errorf("invalid data: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		project := c.Param("project")
		cond := order.ListOrderCond{
			Project: project,
		}
		//	DeviceId        string `json:"device_id" form:"device_id"`
		//	VehicleId       string `json:"vehicle_id" form:"vehicle_id"`
		if request.OrderId != "" {
			cond.OrderId = request.OrderId
		}
		if request.DeviceId != "" {
			cond.DeviceId = request.DeviceId
		}
		if request.VehiclePlatform != "" {
			cond.CarPlatforms = []string{request.VehiclePlatform}
		}
		if request.VehicleTypes != "" {
			vehicleTypes := strings.Split(request.VehicleTypes, ",")
			cond.CarModelTypes = vehicleTypes
		}
		if request.VehicleBrands != "" {
			vehicleBrands := strings.Split(request.VehicleBrands, ",")
			cond.CarBrands = vehicleBrands
		}
		if request.VehicleId != "" {
			cond.VehicleId = request.VehicleId
		}
		if request.StartTime != 0 || request.EndTime != 0 {
			cond.StartTs = request.StartTime
			cond.EndTs = request.EndTime
		}
		if request.ServiceResults != "" {
			serviceResults := strings.Split(request.ServiceResults, ",")
			for _, result := range serviceResults {
				cond.Status = append(cond.Status, (&order.OrderDO{}).GetServiceVisualResult2OrderStatus(result)...)
			}
		}
		if request.Sort != "" {
			//cond.OrderFieldName = request.Sort
		}
		cond.Descending = request.Descending
		if request.Size <= 0 || request.Size > 100 {
			request.Size = 100
		}
		if request.Page <= 0 {
			request.Page = 1
		}
		cond.Limit = int64(request.Size)
		cond.Offset = int64((request.Page - 1) * request.Size)
		orderVOs, total, err := domain_service.GetServiceVisualService().ListOrder(c, cond)
		if err != nil {
			log.CtxLog(c).Errorf("domain_service.GetServiceVisualService().ListOrder err. err:%v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = orderVOs
		response.Total = total
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (d *device) GetServiceVisual() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
		//request  model.GetServiceVisualRequest
		//response model.GetServiceVisualResponse
		)
		project := c.Param("project")
		orderId := c.Param("order_id")
		response, err := domain_service.GetServiceVisualService().GetServiceVisualByOrderId(c, project, orderId)
		if err != nil {
			log.CtxLog(c).Errorf("domain_service.GetServiceVisualService().GetServiceVisualByOrderId err. err:%v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (d *device) GetServiceVisualV2() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
		//request  model.GetServiceVisualRequest
		//response model.GetServiceVisualResponse
		)
		project := c.Param("project")
		orderId := c.Param("order_id")
		response, err := domain_service.GetServiceVisualService().GetServiceVisualByOrderIdV2(c, project, orderId)
		if err != nil {
			log.CtxLog(c).Errorf("domain_service.GetServiceVisualService().GetServiceVisualByOrderId err. err:%v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (d *device) GetModelOverview() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.Response
		vo, err := domain_service.GetDeviceModelService().GetOverviewYTD(c)
		if err != nil {
			log.CtxLog(c).Errorf("fail to get ytd overview: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = vo
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *device) GetEnergyOverview() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  device_model.DeviceModelRequest
			response model.Response
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		topN := util.ParseInt(c.Query("top_n"))
		if topN == -1 {
			topN = 10
		}
		cond := energy.AggregateEnergyCond{
			StartTime:   request.StartTime,
			EndTime:     request.EndTime,
			Project:     request.Project,
			CityCompany: request.CityCompany,
			DeviceId:    request.DeviceId,
		}
		vo, err := domain_service.GetDeviceModelService().GetEnergyOverview(c, cond, topN)
		if err != nil {
			log.CtxLog(c).Errorf("fail to get energy overview: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = vo
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *device) GetEnergyList() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  device_model.DeviceModelRequest
			response model.AddTotalResponse
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if request.Download {
			request.Page = 1
			request.Size = 99999
		}
		results, total, err := domain_service.GetDeviceModelService().ListEnergy(c, energy.ListEnergyCond{
			CommonCond: model.CommonCond{
				Page: int64(request.Page),
				Size: int64(request.Size),
			},
			StartTime:   request.StartTime,
			EndTime:     request.EndTime,
			Project:     request.Project,
			CityCompany: request.CityCompany,
			DeviceId:    request.DeviceId,
		})
		if err != nil {
			log.CtxLog(c).Errorf("fail to list energy: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Total = int(total)
		response.Data = results
		if !request.Download {
			um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
			return
		}
		// 下载csv
		fileName := fmt.Sprintf("energy_%s_%s_%d.csv", util.TsString(request.StartTime), util.TsString(request.EndTime), time.Now().UnixMilli())
		csvRecords := make([][]string, len(results)+1)
		csvRecords[0] = []string{"设备ID", "设备名称", "设备类型", "能效(%)", "总耗能", "充电耗能", "水冷耗能", "运营耗能", "机械耗能", "灯光耗能", "UPS耗能", "日期"}
		for i, r := range results {
			csvRecords[i+1] = []string{
				r.DeviceId,
				r.Description,
				r.Project,
				util.PtrToString(r.Energy),
				util.PtrToString(r.TotalConsumption),
				util.PtrToString(r.ChargeConsumption),
				util.PtrToString(r.WaterConsumption),
				util.PtrToString(r.OperationConsumption),
				util.PtrToString(r.MechanicalConsumption),
				util.PtrToString(r.LightConsumption),
				util.PtrToString(r.UpsConsumption),
				util.TsString(r.Day),
			}
		}
		buffer := &bytes.Buffer{}
		buffer.WriteString("\xEF\xBB\xBF")
		cw := csv.NewWriter(buffer)
		if err = cw.WriteAll(csvRecords); err != nil {
			log.CtxLog(c).Errorf("fail to write to csv, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		c.Writer.Header().Set("Content-Type", "text/csv")
		util.Download(c, fileName, buffer.Bytes())
	}
}

func (d *device) GetRevenueOverview() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  device_model.DeviceModelRequest
			response model.Response
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		topN := util.ParseInt(c.Query("top_n"))
		if topN == -1 {
			topN = 10
		}
		cond := revenue.AggregateRevenueCond{
			StartTime:   request.StartTime,
			EndTime:     request.EndTime,
			Project:     request.Project,
			CityCompany: request.CityCompany,
			DeviceId:    request.DeviceId,
		}
		vo, err := domain_service.GetDeviceModelService().GetRevenueOverview(c, cond, topN)
		if err != nil {
			log.CtxLog(c).Errorf("fail to get revenue overview: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = vo
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *device) GetRevenueList() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  device_model.DeviceModelRequest
			response model.AddTotalResponse
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if request.Download {
			request.Page = 1
			request.Size = 99999
		}
		results, total, err := domain_service.GetDeviceModelService().ListRevenue(c, revenue.ListRevenueCond{
			CommonCond: model.CommonCond{
				Page: int64(request.Page),
				Size: int64(request.Size),
			},
			StartTime:   request.StartTime,
			EndTime:     request.EndTime,
			Project:     request.Project,
			CityCompany: request.CityCompany,
			DeviceId:    request.DeviceId,
		})
		if err != nil {
			log.CtxLog(c).Errorf("fail to list revenue: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Total = int(total)
		response.Data = results
		if !request.Download {
			um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
			return
		}
		// 下载csv
		fileName := fmt.Sprintf("revenue_%s_%s_%d.csv", util.TsString(request.StartTime), util.TsString(request.EndTime), time.Now().UnixMilli())
		csvRecords := make([][]string, len(results)+1)
		csvRecords[0] = []string{"设备ID", "设备名称", "设备类型", "极限收益达成率", "总收益", "错峰收益", "能效收益", "电池保养收益", "电池保养块数", "日期"}
		for i, r := range results {
			csvRecords[i+1] = []string{
				r.DeviceId,
				r.Description,
				r.Project,
				util.PtrToString(r.MaxRevenueRate),
				util.PtrToString(r.TotalRevenue),
				util.PtrToString(r.OffPeakRevenue),
				util.PtrToString(r.EnergyRevenue),
				util.PtrToString(r.BatteryMaintenanceRevenue),
				util.PtrToString(r.BatteryMaintenanceTimes),
				util.TsString(r.Day),
			}
		}
		buffer := &bytes.Buffer{}
		buffer.WriteString("\xEF\xBB\xBF")
		cw := csv.NewWriter(buffer)
		if err = cw.WriteAll(csvRecords); err != nil {
			log.CtxLog(c).Errorf("fail to write to csv, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		c.Writer.Header().Set("Content-Type", "text/csv")
		util.Download(c, fileName, buffer.Bytes())
	}
}

func (d *device) GetRevenueSingleDevice() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  device_model.SingleDeviceRequest
			response model.Response
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("GetRevenueSingleDevice, parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		project := c.Param("project")
		deviceId := c.Param("device_id")
		cond := revenue.AggregateRevenueCond{
			StartTime: request.StartTime,
			EndTime:   request.EndTime,
			Project:   project,
			DeviceId:  deviceId,
		}
		vo, err := domain_service.GetDeviceModelService().GetSingleDeviceRevenue(c, cond, request.Day)
		if err != nil {
			log.CtxLog(c).Errorf("GetRevenueSingleDevice, fail to get single device revenue: %v, request: %s", err, ucmd.ToJsonStrIgnoreErr(request))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = vo
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *device) GetEnergySingleDevice() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  device_model.SingleDeviceRequest
			response model.Response
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("GetEnergySingleDevice, parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		project := c.Param("project")
		deviceId := c.Param("device_id")
		cond := energy.AggregateEnergyCond{
			StartTime: request.StartTime,
			EndTime:   request.EndTime,
			Project:   project,
			DeviceId:  deviceId,
		}
		vo, err := domain_service.GetDeviceModelService().GetSingleDeviceEnergy(c, cond, request.Day)
		if err != nil {
			log.CtxLog(c).Errorf("GetEnergySingleDevice, fail to get single device energy: %v, request: %s", err, ucmd.ToJsonStrIgnoreErr(request))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = vo
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *device) GetPowerChargerEnum() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.Response
		)

		response.Data = map[string]map[int64]string{
			"service_stop_reason": powercharger_order.ServiceStopReason,
			"order_status":        powercharger_order.OrderStatus,
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *device) ListPowerChargerOrders() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.ListPowerChargeOrdersRequest
			response model.ListPowerChargerOrdersResponse
		)
		err := c.BindQuery(&request)
		if err != nil {
			log.CtxLog(c).Errorf("invalid data: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		project := c.Param("project")
		cond := powercharger_order.ListPowerChargerOrderCond{
			Project: project,
		}
		if request.OrderId != "" {
			cond.OrderId = request.OrderId
		}
		if request.DeviceId != "" {
			cond.DeviceId = request.DeviceId
		}
		if request.ResourceId != "" {
			cond.ResourceId = request.ResourceId
		}
		if request.ServiceStopReasons != "" {
			serviceStopReasonsStr := strings.Split(request.ServiceStopReasons, ",")
			serviceStopReasonsInt64 := []int64{}
			for _, str := range serviceStopReasonsStr {
				v, err := strconv.ParseInt(str, 10, 64)
				if err != nil {
					log.CtxLog(c).Errorf("invalid service_stop_reasons: %v", err)
					um.FailWithBadRequest(c, &response, err.Error())
					return
				}
				serviceStopReasonsInt64 = append(serviceStopReasonsInt64, v)
			}
			cond.ServiceStopReason = serviceStopReasonsInt64
		}
		if request.OrderStatus != "" {
			orderStatusStr := strings.Split(request.OrderStatus, ",")
			orderStatusInt64 := []int64{}
			for _, str := range orderStatusStr {
				v, err := strconv.ParseInt(str, 10, 64)
				if err != nil {
					log.CtxLog(c).Errorf("invalid order_status: %v", err)
					um.FailWithBadRequest(c, &response, err.Error())
					return
				}
				orderStatusInt64 = append(orderStatusInt64, v)
			}
			cond.OrderStatus = orderStatusInt64
		}

		if request.StartTime != 0 || request.EndTime != 0 {
			cond.StartTs = request.StartTime
			cond.EndTs = request.EndTime
		}

		if request.Sort != "" {
			//cond.OrderFieldName = request.Sort
		}
		cond.Descending = request.Descending
		if request.Size <= 0 || request.Size > 100 {
			request.Size = 100
		}
		if request.Page <= 0 {
			request.Page = 1
		}
		cond.Limit = int64(request.Size)
		cond.Offset = int64((request.Page - 1) * request.Size)
		orderDOs, total, err := (&powercharger_order.PowerChargerOrderDO{}).ListPowerChargerOrder(c, cond)
		if err != nil {
			log.CtxLog(c).Errorf("domain ListPowerChargerOrder err. err:%v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		vos := []model.ListPowerChargerOrderVO{}
		for _, orderDO := range orderDOs {
			vos = append(vos, convertListPowerChargerOrderDO2VO(orderDO))
		}
		response.Data = vos
		response.Total = total
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func convertListPowerChargerOrderDO2VO(do *powercharger_order.PowerChargerOrderDO) model.ListPowerChargerOrderVO {
	vo := model.ListPowerChargerOrderVO{
		DeviceId:                        do.DeviceId,
		ResourceId:                      do.ResourceId,
		OrderId:                         do.OrderId,
		ConnectorId:                     do.ConnectorId,
		OrderStartTimestamp:             do.OrderCreateTimestamp,
		OrderEndTimestamp:               do.GetOrderEndTimestamp(),
		OrderStatus:                     do.OrderStatus,
		ServiceId:                       do.ServiceId,
		ServiceStartTimestamp:           do.ServiceStartTimestamp,
		ServiceEndTimestamp:             do.ServiceEndTimestamp,
		ChargedEnergyTotal:              do.ChargedEnergyTotal,
		CalibratedRealtimeChargedEnergy: do.CalibratedRealtimeChargedEnergy,
		EffectiveChargedEnergy:          do.EffectiveChargedEnergy,
		VehicleVin:                      do.Vin,
		EnergyTotalStart:                do.EnergyTotalStart,
		EnergyTotal:                     do.EnergyTotal,
		ServiceStopReason:               do.ServiceStopReason,
		ServiceFinishReason:             do.ServiceFinishReason,
		VehiclePlatform:                 do.CarPlatform,
		VehicleType:                     do.CarModelType,
		CarPackagePartNumber:            do.CarPackagePartNumber,
		CarPackageGlobalVersion:         do.CarPackageGlobalVersion,
		Channel:                         do.Channel,
		Client:                          do.Client,
		OrderSource:                     do.OrderSource,
		OrderType:                       do.OrderType,
		ShamanOrderId:                   do.ShamanOrderId,
		ChargerOrderId:                  do.ChargerOrderId,
		UserId:                          do.UserId,
		OwnerId:                         do.OwnerId,
		VehicleId:                       do.VehicleId,
		Rid:                             do.Rid,
		GroupId:                         do.GroupId,
		RefundStatus:                    do.RefundStatus,
		OperatorId:                      do.OperatorId,
		EquipmentOwner:                  do.EquipmentOwner,
		RightsType:                      do.RightsType,
		PaymentType:                     do.PaymentType,
		PriceType:                       do.PriceType,
		OriginalPrice:                   do.OriginalPrice,
		OriginalServicePrice:            do.OriginalServicePrice,
		OriginalElectricityPrice:        do.OriginalElectricityPrice,
		ActualPrice:                     do.ActualPrice,
		CostPrice:                       do.CostPrice,
		ServicePrice:                    do.ServicePrice,
		ElectricityPrice:                do.ElectricityPrice,
		ExceptionType:                   do.ExceptionType,
		IsPaid:                          do.IsPaid,
		ActivityId:                      do.ActivityId,
		ShouldPrice:                     do.ShouldPrice,
		ShouldElectricityPrice:          do.ShouldElectricityPrice,
		ShouldServicePrice:              do.ShouldServicePrice,
		StartCommandId:                  do.StartCommandId,
		StartCommandStatus:              do.StartCommandStatus,
		StartCommandTime:                do.StartCommandTime,
		StopCommandId:                   do.StopCommandId,
		StopCommandStatus:               do.StopCommandStatus,
		StopCommandTime:                 do.StopCommandTime,
		Latitude:                        do.Latitude,
		Longitude:                       do.Longitude,
		PayTime:                         do.PayTime,
		ForceCloseSource:                do.ForceCloseSource,
		OssForceCloseReason:             do.OssForceCloseReason,
		AutoPayChannel:                  do.AutoPayChannel,
	}
	deviceInfo, found := cache.ChargerCache.GetDeviceByResourceId(do.ResourceId)
	if found {
		vo.Description = deviceInfo.Description
		vo.Project = deviceInfo.Project
	}
	return vo
}

func (d *device) GetPowerChargerOrderByOrderId() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.GetPowerChargerOrderResponse
		)
		project := c.Param("project")
		orderId := c.Param("order_id")
		powerChargerOrderDO, err := (&powercharger_order.PowerChargerOrderDO{}).GetPowerChargerOrderByOrderID(c, project, orderId)
		if err != nil {
			log.CtxLog(c).Errorf("domain GetPowerChargerOrderByOrderID err. err:%v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = convertListPowerChargerOrderDO2VO(powerChargerOrderDO)
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (d *device) GetPowerChargerOrderEvents() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.GetPowerChargerOrderEventsResponse
		)
		project := c.Param("project")
		orderId := c.Param("order_id")
		powerChargerOrderDO, err := (&powercharger_order.PowerChargerOrderDO{}).GetPowerChargerOrderByOrderID(c, project, orderId)
		if err != nil {
			log.CtxLog(c).Errorf("domain GetPowerChargerOrderByOrderID err. err:%v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		startTs := powerChargerOrderDO.OrderCreateTimestamp
		endTs := int64(9999999999999)
		if powerChargerOrderDO.GetOrderEndTimestamp() != nil {
			endTs = *powerChargerOrderDO.GetOrderEndTimestamp()
		} else if powerChargerOrderDO.GetOrderEndTimestamp() == nil && powerChargerOrderDO.ServiceEndTimestamp != nil {
			endTs = *powerChargerOrderDO.ServiceEndTimestamp
		}

		powerChargerEventDOs, _, err := (&powercharger_event.PowerChargerEventDO{}).ListServiceEvents(c, powercharger_event.ListPowerChargerEventsCond{
			Project:    project,
			ResourceId: powerChargerOrderDO.ResourceId,
			StartTs:    startTs,
			EndTs:      endTs,
		})
		if err != nil {
			log.CtxLog(c).Errorf("domain ListServiceEvents err. err:%v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		powerSwapProjct := "PUS3"
		remoteOpLogs, _, err := (&oplog.CloudOpLogDO{}).ListCloudOpLog(c, oplog.ListCloudOpLogCond{
			Project:   powerSwapProjct,
			DeviceId:  powerChargerOrderDO.DeviceId,
			StartTime: startTs,
			EndTime:   endTs,
		})
		if err != nil {
			log.CtxLog(c).Errorf("domain ListCloudOpLog err. err:%v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		log.CtxLog(c).Infof("ListCloudOpLog res:%v", ucmd.ToJsonStrIgnoreErr(remoteOpLogs))
		for _, remoteOpLog := range remoteOpLogs {
			context := map[string]string{}
			for _, param := range remoteOpLog.AbilityParams {
				context[param.ParamCode] = fmt.Sprintf("%v", param.ParamValue)
			}
			for _, param := range remoteOpLog.AbilityParams {
				if param.ParamCode == "connector_id" && fmt.Sprintf("%v", param.ParamValue) == powerChargerOrderDO.ConnectorId {
					powerChargerEventDOs = append(powerChargerEventDOs, &powercharger_event.PowerChargerEventDO{
						DeviceId:       powerChargerOrderDO.DeviceId,
						ResourceId:     powerChargerOrderDO.ResourceId,
						EventSource:    "oss_remote",
						EventId:        remoteOpLog.AbilityCode,
						EventName:      fmt.Sprintf("云端指令:%v", remoteOpLog.AbilityCode),
						EventTimestamp: remoteOpLog.Timestamp,
						Context:        context,
					})
				}
			}
		}
		sort.Slice(powerChargerEventDOs, func(i, j int) bool {
			return powerChargerEventDOs[i].EventTimestamp < powerChargerEventDOs[j].EventTimestamp
		})
		alarmDOs, _, err := (&alarm.AlarmDO{}).ListAlarms(c, alarm.ListAlarmCond{
			Project:  project,
			DeviceId: &powerChargerOrderDO.ResourceId,
			StartTs:  startTs,
			EndTs:    endTs,
		})
		if err != nil {
			log.CtxLog(c).Errorf("domain ListAlarms err. err:%v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		sort.Slice(alarmDOs, func(i, j int) bool {
			return alarmDOs[i].CreateTs < alarmDOs[j].CreateTs
		})
		powerChargerOrderEventVOs := []model.PowerChargerOrderEventVO{}
		for i, _ := range powerChargerEventDOs {
			powerChargerOrderEventVO := model.PowerChargerOrderEventVO{
				EventTimestamp: powerChargerEventDOs[i].EventTimestamp,
				EventId:        powerChargerEventDOs[i].EventId,
				EventName:      powerChargerEventDOs[i].EventName,
				Context:        powerChargerEventDOs[i].Context,
			}

			alarmDetails := []model.AlarmDetailVO{}
			if i == len(powerChargerEventDOs)-1 {
				for _, alarmDO := range alarmDOs {
					if alarmDO.CreateTs >= powerChargerEventDOs[i].EventTimestamp {
						alarmDetails = append(alarmDetails, model.AlarmDetailVO{
							AlarmCreateTs:    alarmDO.CreateTs,
							AlarmId:          alarmDO.DataId,
							AlarmDescription: alarmDO.DataIdDescription,
						})
					}
				}
			} else {
				for _, alarmDO := range alarmDOs {
					if alarmDO.CreateTs >= powerChargerEventDOs[i].EventTimestamp && alarmDO.CreateTs <= powerChargerEventDOs[i+1].EventTimestamp {
						alarmDetails = append(alarmDetails, model.AlarmDetailVO{
							AlarmCreateTs:    alarmDO.CreateTs,
							AlarmId:          alarmDO.DataId,
							AlarmDescription: alarmDO.DataIdDescription,
						})
					}
				}
			}

			if len(alarmDetails) != 0 {
				powerChargerOrderEventVO.AlarmLevel = 2
				powerChargerOrderEventVO.AlarmDetails = alarmDetails
			}
			powerChargerOrderEventVOs = append(powerChargerOrderEventVOs, powerChargerOrderEventVO)
		}
		response.Data = powerChargerOrderEventVOs
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (d *device) GetPowerChargerOrderRealtime() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.GetPowerChargerOrderRealtimeResponse
		)
		project := c.Param("project")
		orderId := c.Param("order_id")
		powerChargerOrderDO, err := (&powercharger_order.PowerChargerOrderDO{}).GetPowerChargerOrderByOrderID(c, project, orderId)
		if err != nil {
			log.CtxLog(c).Errorf("domain GetPowerChargerOrderByOrderID err. err:%v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if powerChargerOrderDO.ServiceId == nil {
			log.CtxLog(c).Warnf("GetPowerChargerOrderRealtime service id nil. project:%v order id:%v", project, orderId)
			um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
			return
		}
		serviceId := *powerChargerOrderDO.ServiceId
		collectionName := fmt.Sprintf("%v_service", ucmd.RenameProjectDB(project))
		byteData, err := client.GetWatcher().MongodbSupportTimeseries().NewMongoEntry(bson.D{bson.E{Key: "metadata.service_id", Value: serviceId}}).ListAll("power_charge_realtime", collectionName, client.Ordered{
			Key:        "date",
			Descending: false,
		})
		if err != nil {
			log.CtxLog(c).Errorf("client.GetWatcher().MongodbSupportTimeseries() ListAll err. err:%v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var mongoPowerChargerServiceRealTimes []umw.MongoPowerChargerServiceRealTime
		if err = json.Unmarshal(byteData, &mongoPowerChargerServiceRealTimes); err != nil {
			log.CtxLog(c).Errorf("json.Unmarshal err. err:%v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		powerChargerOrderRealtimeVOs := []model.PowerChargerOrderRealtimeVO{}
		for _, mongoPowerChargerServiceRealTime := range mongoPowerChargerServiceRealTimes {
			powerChargerOrderRealtimeVOs = append(powerChargerOrderRealtimeVOs, model.PowerChargerOrderRealtimeVO{
				Timestamp:                       mongoPowerChargerServiceRealTime.Date.UnixMilli(),
				DeviceId:                        mongoPowerChargerServiceRealTime.Metadata.DeviceId,
				ResourceId:                      mongoPowerChargerServiceRealTime.Metadata.ResourceId,
				OrderId:                         orderId,
				ServiceId:                       mongoPowerChargerServiceRealTime.Metadata.ServiceId,
				ChargedEnergy:                   mongoPowerChargerServiceRealTime.ChargedEnergy,
				Soc:                             mongoPowerChargerServiceRealTime.Soc,
				OutputCurrent:                   mongoPowerChargerServiceRealTime.OutputCurrent,
				CalibratedRealtimeChargedEnergy: mongoPowerChargerServiceRealTime.CalibratedRealtimeChargedEnergy,
				EnergyTotal:                     mongoPowerChargerServiceRealTime.EnergyTotal,
				BmsRequestChargeVoltage:         mongoPowerChargerServiceRealTime.BmsRequestChargeVoltage,
				EnergyTotalStart:                mongoPowerChargerServiceRealTime.EnergyTotalStart,
				OutputPower:                     mongoPowerChargerServiceRealTime.OutputPower,
				BmsRequestChargeCurrent:         mongoPowerChargerServiceRealTime.BmsRequestChargeCurrent,
				EffectiveChargedEnergy:          mongoPowerChargerServiceRealTime.EffectiveChargedEnergy,
				OutputVoltage:                   mongoPowerChargerServiceRealTime.OutputVoltage,
			})
		}
		response.Data = powerChargerOrderRealtimeVOs
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (d *device) GetDeviceVersionOverview() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.Response
		project := c.Param("project")
		if project != umw.PowerSwap2 && project != umw.PUS3 && project != umw.PUS4 {
			log.CtxLog(c).Errorf("GetDeviceVersionOverview, invalid project: %s", project)
			um.FailWithBadRequest(c, &response, "invalid project")
			return
		}
		dd := domain_device.Device{Project: project}
		blacklistBool := true
		blacklist, err := dd.ListDeviceBlacklist(c, domain_device.DeviceBlacklistCond{Project: project, BlacklistType: domain_device.BlacklistTypeVersionWorksheet, Blacklist: &blacklistBool})
		if err != nil {
			log.CtxLog(c).Errorf("GetDeviceVersionOverview, fail to get device blacklist, err: %v, project: %s", err, project)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		blacklistDevices := make([]string, 0)
		for _, item := range blacklist {
			blacklistDevices = append(blacklistDevices, item.DeviceId)
		}
		match := bson.M{"release_version": bson.M{"$exists": true, "$ne": ""}, "is_active": true, "_id": bson.M{"$nin": blacklistDevices}}
		if ucmd.GetEnv() != "prod" {
			match["name"] = bson.M{"$exists": true}
		}
		pipeline := mongo.Pipeline{
			{{"$match", match}},
			{{"$group", bson.M{"_id": "$release_version", "count": bson.M{"$sum": 1}}}},
			{{"$project", bson.M{"_id": 0, "version": "$_id", "count": 1}}},
			{{"$sort", bson.M{"version": 1}}},
		}
		var res []bson.M
		err = client.GetWatcher().RbMongodb().NewMongoEntry().Aggregate(project, "devices", pipeline, &res)
		if err != nil {
			log.CtxLog(c).Errorf("GetDeviceVersionOverview, fail to aggregate device version: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = res
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *device) GetDeviceVersionFilterOption() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.Response
		project := c.Param("project")
		if project != umw.PowerSwap2 && project != umw.PUS3 && project != umw.PUS4 {
			log.CtxLog(c).Errorf("GetDeviceVersionFilterOption, invalid project: %s", project)
			um.FailWithBadRequest(c, &response, "invalid project")
			return
		}
		dd := domain_device.Device{Project: project}
		res, err := dd.GetDeviceVersionHeader(c)
		if err != nil {
			log.CtxLog(c).Errorf("GetDeviceVersionFilterOption, fail to get device version: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = res
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *device) ListDeviceVersion() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.Response
		project := c.Param("project")
		if project != umw.PowerSwap2 && project != umw.PUS3 && project != umw.PUS4 {
			log.CtxLog(c).Errorf("ListDeviceVersion, invalid project: %s", project)
			um.FailWithBadRequest(c, &response, "invalid project")
			return
		}
		request := make(map[string]interface{})
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("ListDeviceVersion, parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		download, _ := request["download"].(bool)
		filter := make(map[string]interface{})
		if !download {
			filter = request
		}
		dd := domain_device.Device{Project: project}
		res, err := dd.ListDeviceVersion(c, filter)
		if err != nil {
			log.CtxLog(c).Errorf("ListDeviceVersion, fail to list device version: %v, request: %s", err, ucmd.ToJsonStrIgnoreErr(request))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		sort.Slice(res, func(i, j int) bool {
			deviceId1, _ := res[i]["device_id"].(string)
			deviceId2, _ := res[j]["device_id"].(string)
			return deviceId1 < deviceId2
		})
		if !download {
			response.Data = res
			um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
			return
		}
		// 下载csv
		header := []string{"device_id", "description", "is_active", "online", "in_blacklist"}
		headerData, err := dd.GetDeviceVersionHeader(c)
		if err != nil {
			log.CtxLog(c).Errorf("ListDeviceVersion, fail to get device version filter: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		for _, item := range headerData {
			if item.Key == "is_active" || item.Key == "online" || item.Key == "in_blacklist" {
				continue
			}
			header = append(header, item.Key)
		}
		fileName := fmt.Sprintf("device-version-%s-%d.csv", project, time.Now().UnixMilli())
		csvRecords := make([][]string, len(res)+1)
		csvRecords[0] = header
		for i, r := range res {
			record := make([]string, len(header))
			for j, field := range header {
				if r[field] == nil {
					record[j] = "-"
				} else {
					record[j] = fmt.Sprintf("%v", r[field])
				}
			}
			csvRecords[i+1] = record
		}
		buffer := &bytes.Buffer{}
		buffer.WriteString("\xEF\xBB\xBF")
		cw := csv.NewWriter(buffer)
		if err = cw.WriteAll(csvRecords); err != nil {
			log.CtxLog(c).Errorf("ListDeviceVersion, fail to write to csv, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		c.Writer.Header().Set("Content-Type", "text/csv")
		util.Download(c, fileName, buffer.Bytes())
	}
}

func (d *device) ChangeDeviceVersionBlacklist() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.DeviceVersionBlacklistRequest
			response model.Response
		)
		project := c.Param("project")
		if project != umw.PowerSwap2 && project != umw.PUS3 && project != umw.PUS4 {
			log.CtxLog(c).Errorf("ChangeDeviceVersionBlacklist, invalid project: %s", project)
			um.FailWithBadRequest(c, &response, "invalid project")
			return
		}
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("ChangeDeviceVersionBlacklist, parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		userId := c.GetHeader("X-User-ID")
		dd := domain_device.Device{}
		cond := domain_device.DeviceBlacklistCond{
			Devices:       request.DeviceList,
			Project:       project,
			BlacklistType: domain_device.BlacklistTypeVersionWorksheet,
			Operator:      userId,
			Blacklist:     &request.Blacklist,
		}
		err := dd.UpdateDeviceBlacklist(c, cond)
		if err != nil {
			log.CtxLog(c).Errorf("ChangeDeviceVersionBlacklist, fail to change device blacklist: %v, request: %s", err, ucmd.ToJsonStrIgnoreErr(request))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *device) ListDeviceVersionBlacklist() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.DeviceVersionBlacklistRequest
			response model.Response
		)
		project := c.Param("project")
		if project != umw.PowerSwap2 && project != umw.PUS3 && project != umw.PUS4 {
			log.CtxLog(c).Errorf("ListDeviceVersionBlacklist, invalid project: %s", project)
			um.FailWithBadRequest(c, &response, "invalid project")
			return
		}
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("ListDeviceVersionBlacklist, parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		blacklist := true
		cond := domain_device.DeviceBlacklistCond{
			Devices:       request.DeviceList,
			Project:       project,
			BlacklistType: domain_device.BlacklistTypeVersionWorksheet,
			Blacklist:     &blacklist,
		}
		dd := domain_device.Device{}
		res, err := dd.ListDeviceBlacklist(c, cond)
		if err != nil {
			log.CtxLog(c).Errorf("ListDeviceVersionBlacklist, fail to list device blacklist: %v, request: %s", err, ucmd.ToJsonStrIgnoreErr(request))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = map[string]interface{}{
			"total_count":     len(request.DeviceList),
			"blacklist_count": len(res),
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func checkDeviceVersion(ctx context.Context, project string, deviceList []string, targetVersion string) (blacklistDevices []mmgo.DeviceBlacklist, ignoreDevices []mmgo.IgnoreDevices, err error) {
	blacklist := true
	cond := domain_device.DeviceBlacklistCond{
		Devices:       deviceList,
		Project:       project,
		BlacklistType: domain_device.BlacklistTypeVersionWorksheet,
		Blacklist:     &blacklist,
	}
	dd := domain_device.Device{}
	blacklistDevices, err = dd.ListDeviceBlacklist(ctx, cond)
	if err != nil {
		log.CtxLog(ctx).Errorf("checkDeviceVersion, fail to list device blacklist: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	blacklistMap := make(map[string]bool)
	for _, record := range blacklistDevices {
		blacklistMap[record.DeviceId] = true
	}
	devices := make([]string, 0)
	for _, deviceId := range deviceList {
		if !blacklistMap[deviceId] {
			devices = append(devices, deviceId)
		}
	}
	projection := bson.M{"device_id": "$_id", "description": "$name", "is_active": 1, "online": 1}
	_, err = client.GetWatcher().RbMongodb().NewMongoEntry(bson.D{{"_id", bson.M{"$in": devices}}, {"release_version", targetVersion}}).FindMany(project, "devices", options.Find().SetProjection(projection).SetSort(bson.M{"_id": 1}), &ignoreDevices)
	if err != nil {
		log.CtxLog(ctx).Errorf("checkDeviceVersion, fail to list device version: %v, devices: %s, targetVersion: %s", err, ucmd.ToJsonStrIgnoreErr(devices), targetVersion)
		return
	}
	return
}

func (d *device) CheckDeviceVersion() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.DeviceVersionWorksheetRequest
			response model.Response
		)
		project := c.Param("project")
		if project != umw.PowerSwap2 && project != umw.PUS3 && project != umw.PUS4 {
			log.CtxLog(c).Errorf("CheckDeviceVersion, invalid project: %s", project)
			um.FailWithBadRequest(c, &response, "invalid project")
			return
		}
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("CheckDeviceVersion, parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		_, ignoreDevices, err := checkDeviceVersion(c, project, request.DeviceList, request.TargetVersion)
		if err != nil {
			log.CtxLog(c).Errorf("CheckDeviceVersion, fail to check device version: %v, request: %s", err, ucmd.ToJsonStrIgnoreErr(request))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = map[string]interface{}{
			"ignore_devices": ignoreDevices,
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *device) IssueDeviceVersionWorksheet() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.DeviceVersionWorksheetRequest
			response model.Response
		)
		project := c.Param("project")
		if project != umw.PowerSwap2 && project != umw.PUS3 && project != umw.PUS4 {
			log.CtxLog(c).Errorf("IssueDeviceVersionWorksheet, invalid project: %s", project)
			um.FailWithBadRequest(c, &response, "invalid project")
			return
		}
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("IssueDeviceVersionWorksheet, parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		userId := c.GetHeader("X-User-ID")
		blacklistDevices, ignoreDevices, err := checkDeviceVersion(c, project, request.DeviceList, request.TargetVersion)
		if err != nil {
			log.CtxLog(c).Errorf("IssueDeviceVersionWorksheet, fail to check device version: %v, request: %s", err, ucmd.ToJsonStrIgnoreErr(request))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		ignoreDeviceMap := make(map[string]bool)
		for _, record := range ignoreDevices {
			ignoreDeviceMap[record.DeviceId] = true
		}
		for _, record := range blacklistDevices {
			ignoreDeviceMap[record.DeviceId] = true
		}
		issueDevices := make([]string, 0)
		for _, deviceId := range request.DeviceList {
			if !ignoreDeviceMap[deviceId] {
				// 用虚拟id发工单
				deviceInfo, _ := cache.PowerSwapCache.GetSingleDevice(deviceId)
				if deviceInfo != nil && deviceInfo.ResourceId != "" {
					issueDevices = append(issueDevices, deviceInfo.ResourceId)
				}
			}
		}
		log.CtxLog(c).Infof("IssueDeviceVersionWorksheet, project: %s, issueDevices: %s, ignoreDevices: %s", project, ucmd.ToJsonStrIgnoreErr(issueDevices), ucmd.ToJsonStrIgnoreErr(ignoreDeviceMap))
		if len(issueDevices) == 0 {
			um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
			return
		}
		adapter := config.Cfg.WorksheetAdapter[domain_common.WorksheetAdapterDeviceVersion]
		if adapter.RuleCode == "" {
			log.CtxLog(c).Warnf("IssueDeviceVersionWorksheet, fail to get device version worksheet adapter: %s", domain_common.WorksheetAdapterDeviceVersion)
			um.FailWithInternalServerError(c, &response, "unsupported worksheet")
			return
		}
		worksheetContent := make([]map[string]interface{}, 0)
		for _, resourceId := range issueDevices {
			worksheetContent = append(worksheetContent, map[string]interface{}{
				"device_id":             resourceId,
				"worksheet_name":        adapter.WorksheetName,
				"worksheet_description": request.WorksheetDescription,
				"sop":                   adapter.SOPUrl,
			})
		}
		err = domain_common.IssueWorksheetByAdapter(c, adapter.RuleCode, userId, worksheetContent)
		if err != nil {
			log.CtxLog(c).Errorf("IssueDeviceVersionWorksheet, fail to issue device version worksheet: %v, worksheetContent: %s", err, ucmd.ToJsonStrIgnoreErr(worksheetContent))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *device) GetDeviceProdPlm() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.AddTotalResponse
		project := c.Param("project")
		if project != umw.PowerSwap2 && project != umw.PUS3 && project != umw.PUS4 {
			log.CtxLog(c).Errorf("GetDeviceProdPlm, invalid project: %s", project)
			um.FailWithBadRequest(c, &response, "invalid project")
			return
		}
		var deviceReleaseName mmgo.DeviceReleaseName
		err := client.GetWatcher().RbMongodb().NewMongoEntry(bson.D{{"name", bson.M{"$regex": "配方并发"}}}).FindOne(project, "releases", nil, &deviceReleaseName)
		if err != nil {
			log.CtxLog(c).Errorf("GetDeviceProdPlm, fail to get device release name: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var deviceReleaseVersion []mmgo.DeviceReleaseVersion
		opts := options.Find().SetSort(bson.M{"_id": -1}).SetLimit(100)
		// 查找生产版本
		total, err := client.GetWatcher().RbMongodb().NewMongoEntry(bson.D{{"type", 0}}).FindMany(project, fmt.Sprintf("release_%d", deviceReleaseName.Id), opts, &deviceReleaseVersion)
		if err != nil {
			log.CtxLog(c).Errorf("GetDeviceProdPlm, fail to get device release version: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		versions := make([]string, 0)
		versionSet := make(map[string]bool)
		for _, record := range deviceReleaseVersion {
			if versionSet[record.Version] {
				continue
			}
			versionSet[record.Version] = true
			versions = append(versions, record.Version)
		}
		response.Total = int(total)
		response.Data = versions
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *device) ListBidirectionalDevices() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.Response
		name := c.Query("name")
		bs := domain_service.GetBidirectionalDeviceService()
		g := ucmd.NewErrGroup(c)
		var biPUS3, biPUS4 []model.DeviceInfoVO
		g.GoRecover(func() error {
			var gErr error
			biPUS3, gErr = bs.DeviceDO.ListBidirectionalDevices(c, umw.PUS3)
			if gErr != nil {
				um.FailWithInternalServerError(c, nil, gErr.Error())
				return gErr
			}
			return nil
		})
		g.GoRecover(func() error {
			var gErr error
			biPUS4, gErr = bs.DeviceDO.ListBidirectionalDevices(c, umw.PUS4)
			if gErr != nil {
				um.FailWithInternalServerError(c, nil, gErr.Error())
				return gErr
			}
			return nil
		})
		if err := g.Wait(); err != nil {
			log.CtxLog(c).Errorf("ListBidirectionalDevices, fail to list bidirectional devices: %v", err)
			um.FailWithInternalServerError(c, nil, err.Error())
			return
		}
		allDevices := append(biPUS3, biPUS4...)
		if name != "" {
			filteredDevices := make([]model.DeviceInfoVO, 0)
			for _, device := range allDevices {
				if strings.Contains(device.DeviceId, name) || strings.Contains(device.Description, name) || strings.Contains(device.ResourceId, name) {
					filteredDevices = append(filteredDevices, device)
				}
			}
			allDevices = filteredDevices
		}
		limit := c.Query("limit")
		if limitInt, _ := strconv.Atoi(limit); limitInt > 0 && len(allDevices) > limitInt {
			allDevices = allDevices[:limitInt]
		}
		response.Data = allDevices
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *device) GetBidirectionalSubscription() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.Response
		userId := c.GetHeader("X-User-ID")
		if userId == "" {
			log.CtxLog(c).Errorf("GetBidirectionalSubscription, user_id is empty")
			um.FailWithBadRequest(c, &response, "user_id is empty")
			return
		}
		var res mmgo.MongoUserSubscribeModule
		err := client.GetWatcher().Mongodb().NewMongoEntry(bson.D{{"_id", userId}}).FindOne("favorites", "bidirectional_device", nil, &res)
		if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
			log.CtxLog(c).Errorf("GetBidirectionalSubscription, fail to get bidirectional subscription: %v, user_id: %s", err, userId)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = res.SubscribeModule
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *device) UpdateBidirectionalSubscription() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  mmgo.MongoUserSubscribeModule
			response model.Response
		)
		userId := c.GetHeader("X-User-ID")
		if userId == "" {
			log.CtxLog(c).Errorf("UpdateBidirectionalSubscription, user_id is empty")
			um.FailWithBadRequest(c, &response, "user_id is empty")
			return
		}
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("UpdateBidirectionalSubscription, parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		request.UserId = userId
		request.UpdateTs = time.Now().UnixMilli()
		err := client.GetWatcher().Mongodb().NewMongoEntry(bson.D{{"_id", userId}}).UpdateOne("favorites", "bidirectional_device", bson.M{"$set": request}, true)
		if err != nil {
			log.CtxLog(c).Errorf("UpdateBidirectionalSubscription, fail to update bidirectional subscription: %v, request: %s", err, ucmd.ToJsonStrIgnoreErr(request))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *device) GetBidirectionalInfo() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  bidirectional_device.BidirectionalDeviceRequest
			response model.Response
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("GetBidirectionalInfo, parse request query, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		// 时间范围小于31天
		if request.EndTime-request.StartTime > 31*24*3600*1000 {
			log.CtxLog(c).Errorf("GetBidirectionalInfo, time range is too large, request: %s", ucmd.ToJsonStrIgnoreErr(request))
			um.FailWithBadRequest(c, &response, "time range is too large")
			return
		}
		bd := domain_service.GetBidirectionalDeviceService()
		res, err := bd.GetBidirectionalInfo(c, request)
		if err != nil {
			log.CtxLog(c).Errorf("GetBidirectionalInfo, fail to get bidirectional info: %v, request: %s", err, ucmd.ToJsonStrIgnoreErr(request))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = res
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *device) DownloadBidirectionalInfo() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  bidirectional_device.BidirectionalDeviceRequest
			response model.Response
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("DownloadBidirectionalInfo, parse request query, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		// 时间范围小于31天
		if request.EndTime-request.StartTime > 31*24*3600*1000 {
			log.CtxLog(c).Errorf("DownloadBidirectionalInfo, time range is too large, request: %s", ucmd.ToJsonStrIgnoreErr(request))
			um.FailWithBadRequest(c, &response, "time range is too large")
			return
		}
		bd := domain_service.GetBidirectionalDeviceService()
		err := bd.DownloadBidirectionalInfo(c, request)
		if err != nil {
			log.CtxLog(c).Errorf("DownloadBidirectionalInfo, fail to download bidirectional info: %v, request: %s", err, ucmd.ToJsonStrIgnoreErr(request))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
	}
}

type MetaData struct {
	DeviceId string `json:"device_id" bson:"device_id"`
}

type DeviceUploadDebugRecord struct {
	Metadata    MetaData  `json:"metadata" bson:"metadata"`
	DataJsonStr string    `json:"data_json_str" bson:"data_json_str"`
	Ts          time.Time `json:"ts" bson:"ts"`
	Timestamp   int64     `json:"timestamp" bson:"timestamp"`
}

func (d *device) GetMqttUploadData() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		var (
			response model.Response
		)
		deviceId := ctx.Query("device_id")
		limitStr := ctx.Query("limit")
		limit, err := strconv.ParseInt(limitStr, 10, 64)
		if err != nil {
			log.CtxLog(ctx).Errorf("strconv.ParseInt err. err: %v", err)
		}
		startTimestampStr := ctx.Query("start_timestamp")
		startTimestamp, err := strconv.ParseInt(startTimestampStr, 10, 64)
		if err != nil {
			log.CtxLog(ctx).Errorf("strconv.ParseInt err. err: %v", err)
		}
		endTimestampStr := ctx.Query("end_timestamp")
		endTimestamp, err := strconv.ParseInt(endTimestampStr, 10, 64)
		if err != nil {
			log.CtxLog(ctx).Errorf("strconv.ParseInt err. err: %v", err)
		}
		filter := bson.D{}
		if deviceId != "" {
			filter = append(filter, bson.E{Key: "metadata.device_id", Value: deviceId})
		}
		if startTimestamp > 0 {
			filter = append(filter, bson.E{"ts", bson.M{"$gte": time.UnixMilli(startTimestamp)}})
		}
		if endTimestamp > 0 && endTimestamp >= startTimestamp {
			filter = append(filter, bson.E{"ts", bson.M{"$lte": time.UnixMilli(endTimestamp)}})
		}
		opts := options.Find().SetSort(bson.M{"ts": -1}).SetLimit(10)
		if limit > 0 && limit <= 100 {
			opts = opts.SetLimit(limit)
		}
		cur, err := d.watcher.MongodbSupportTimeseries().Client.Database("ts_mqtt").Collection("device_upload_debug").Find(ctx, filter, opts)
		if err != nil {
			log.CtxLog(ctx).Errorf("db find fail. err: %v", err)
			um.FailWithInternalServerError(ctx, &response, err.Error())
			return
		}
		var res []DeviceUploadDebugRecord
		if err = cur.All(ctx, &res); err != nil {
			log.CtxLog(ctx).Errorf("decode from cursor err. err: %v", err)
			um.FailWithInternalServerError(ctx, &response, err.Error())
			return
		}
		for i, _ := range res {
			res[i].Timestamp = res[i].Ts.UnixMilli()
		}
		response.Data = res
		um.SuccessWithMessageForGin(ctx, &response, "ok", http.StatusOK)
		return
	}
}
