package exec

import (
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"
	"time"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	udao "git.nevint.com/golang-libs/common-utils/dao"
	"git.nevint.com/golang-libs/common-utils/lanka"
	um "git.nevint.com/golang-libs/common-utils/model"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	uwf "git.nevint.com/golang-libs/common-utils/workflow"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	"git.nevint.com/welkin2/welkin-backend/model/mongo"
	"git.nevint.com/welkin2/welkin-backend/service"
	"git.nevint.com/welkin2/welkin-backend/util"
)

type System interface {
	GetUserList() gin.HandlerFunc
	CreateNewUser() gin.HandlerFunc
	UpdateUser() gin.HandlerFunc
	DeleteUser() gin.HandlerFunc
	SsoAccessToken() gin.HandlerFunc
	GetMenuList() gin.HandlerFunc
	BrownDragonUserApprovalCallback() gin.HandlerFunc

	GetUserById() gin.HandlerFunc
	UserUpgradeApply() gin.HandlerFunc
	UserUpgradeRollback() gin.HandlerFunc
	UserUpgradeExpandTime() gin.HandlerFunc
}

type system struct {
	sso     *ucfg.SSOConfig
	sentry  *ucfg.SentryConfig
	welkin  *ucfg.WelkinConfig
	watcher client.Watcher
	logger  *zap.SugaredLogger
}

func NewSystemHandler(watcher client.Watcher, conf *ucfg.Config) System {
	return &system{
		sso:     &conf.SSO,
		sentry:  &conf.Sentry,
		welkin:  &conf.Welkin,
		watcher: watcher,
		logger:  log.Logger.Named(model.SYSTEM),
	}
}

func (s *system) GetUserList() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			uriParam model.UserListParams
			response model.UsersListResponse
		)
		project := c.Param("project")
		if err := c.BindQuery(&uriParam); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		util.SetURIParamDefault(&uriParam.CommonUriParam)

		response = model.UsersListResponse{
			Project: project,
			Page:    uriParam.Page,
			Size:    uriParam.Size,
		}

		opts := bson.D{}
		if uriParam.Username != "" {
			opts = append(opts, bson.E{Key: "username", Value: uriParam.Username})
		}
		if uriParam.UserId != "" {
			opts = append(opts, bson.E{Key: "user_id", Value: uriParam.UserId})
		}
		if uriParam.Role != nil {
			opts = append(opts, bson.E{Key: "role", Value: *uriParam.Role})
		}
		byteData, err := s.watcher.Mongodb().NewMongoEntry(opts).ListAll(umw.OAuthDB, umw.UserBaseInfo,
			client.Ordered{Key: "created_time", Descending: uriParam.Descending})
		if err != nil {
			log.CtxLog(c).Errorf("failed to get users info, db: %s, col: %s, filter: %v, err: %v",
				umw.OAuthDB, umw.UserBaseInfo, opts, err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var usersData []umw.MongoUserInfo
		if err = json.Unmarshal(byteData, &usersData); err != nil {
			log.CtxLog(c).Errorf("failed to unmarshal users info, db: %s, col: %s, filter: %v, err: %v",
				umw.OAuthDB, umw.UserBaseInfo, opts, err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		log.CtxLog(c).Infof("succeeded to get users info, db: %s, col: %s, filter: %v, total: %d",
			umw.OAuthDB, umw.UserBaseInfo, opts, len(usersData))
		var count int
		for _, r := range usersData {
			count++
			if count <= (uriParam.Page-1)*uriParam.Size {
				continue
			}
			if count > uriParam.Page*uriParam.Size {
				break
			}
			response.Data = append(response.Data, model.UsersData{
				Username:    r.Username,
				UserId:      r.UserId,
				Role:        r.Role,
				CreatedTime: r.CreatedTime,
			})
		}
		response.Total = len(usersData)
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (s *system) GetUserById() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.GetUserByIdResponse
		)
		project := c.Param("project")
		deviceId := c.Param("device_id")
		userId := c.Param("user_id")
		if project == "" || deviceId == "" || userId == "" {
			log.CtxLog(c).Errorf("project or device_id or user_id is empty")
			um.FailWithBadRequest(c, &response, "project or device_id or user_id is empty")
			return
		}
		filter := bson.D{bson.E{Key: "user_id", Value: userId}}
		userBsonRawData, err := s.watcher.Mongodb().NewMongoEntry(filter).GetOne(umw.OAuthDB, umw.UserBaseInfo)
		if err != nil {
			log.CtxLog(c).Errorf("failed to get users info, db: %s, col: %s, filter: %v, err: %v",
				umw.OAuthDB, umw.UserBaseInfo, filter, err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		var userMongo umw.MongoUserInfo
		if userBsonRawData == nil {
			um.SuccessWithNotFound(c, &response, "user id not found")
			return
		}
		err = bson.Unmarshal(userBsonRawData, &userMongo)
		if err != nil {
			log.CtxLog(c).Errorf("failed to bson.Unmarshal err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		user := model.User{
			Username:    userMongo.Username,
			UserId:      userMongo.UserId,
			CreatedTime: userMongo.CreatedTime,
		}
		if len(userMongo.Role) != 0 {
			user.Role = userMongo.Role[0]
		}

		userUpgradData, _, err := s.watcher.Mongodb().NewMongoEntry(filter).ListByPagination(umw.OAuthDB, "user_upgrade_info", client.Pagination{
			Limit:  int64(1),
			Offset: int64(0),
		},
			client.Ordered{
				Key:        "expire_timestamp",
				Descending: true,
			})
		if err != nil {
			log.CtxLog(c).Errorf("failed to get users info, db: %s, col: %s, filter: %v, err: %v",
				umw.OAuthDB, "user_upgrade_info", filter, err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		var mongoUserUpgradeInfo []mongo.MongoUserUpgradeInfo
		userUpgradeInfo := model.UserUpgradeInfo{}
		err = json.Unmarshal(userUpgradData, &mongoUserUpgradeInfo)
		if err != nil {
			log.CtxLog(c).Errorf("failed to bson.Unmarshal, err: %s", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if len(mongoUserUpgradeInfo) == 0 {
			userUpgradeInfo.IsUpgrade = false
		} else {
			if mongoUserUpgradeInfo[0].ExpireTimestamp > time.Now().UnixMilli() {
				userUpgradeInfo.IsUpgrade = true
				user.Role = mongoUserUpgradeInfo[0].UpgradeRole
				userUpgradeInfo.OriginalRole = mongoUserUpgradeInfo[0].OriginalRole
				userUpgradeInfo.UpgradeRole = mongoUserUpgradeInfo[0].UpgradeRole
				userUpgradeInfo.ExpireTimestamp = mongoUserUpgradeInfo[0].ExpireTimestamp
			}
		}

		user.UpgradeInfo = userUpgradeInfo
		response.User = user
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (s *system) UserUpgradeApply() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.UserUpgradeApplyRequest
			response model.Response
		)
		project := c.Param("project")
		deviceId := c.Param("device_id")
		userId := c.Param("user_id")
		if project == "" || deviceId == "" || userId == "" {
			log.CtxLog(c).Errorf("project or device_id or user_id is empty")
			um.FailWithBadRequest(c, &response, "project or device_id or user_id is empty")
			return
		}
		if project != umw.PowerSwap && project != umw.PowerSwap2 && project != umw.PUS3 && project != umw.PUS4 {
			log.CtxLog(c).Errorf("project invalid")
			um.FailWithBadRequest(c, &response, "project invalid")
			return
		}
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		_, found := model.Role2CnName[request.UpgradeRole]
		if !found {
			log.CtxLog(c).Errorf("UpgradeRole invaild, UpgradeRole: %v", request.UpgradeRole)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("UpgradeRole invaild, UpgradeRole: %v", request.UpgradeRole))
			return
		}
		_, found = model.Role2CnName[request.OriginalRole]
		if !found {
			log.CtxLog(c).Errorf("OriginalRole invaild, UpgradeRole: %v", request.OriginalRole)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("OriginalRole invaild, OriginalRole: %v", request.OriginalRole))
			return
		}

		filter := bson.D{bson.E{Key: "user_id", Value: userId}}
		userBsonRawData, err := s.watcher.Mongodb().NewMongoEntry(filter).GetOne(umw.OAuthDB, umw.UserBaseInfo)
		if err != nil {
			log.CtxLog(c).Errorf("failed to get users info, db: %s, col: %s, filter: %v, err: %v",
				umw.OAuthDB, umw.UserBaseInfo, filter, err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		var userMongo umw.MongoUserInfo
		if userBsonRawData == nil {
			um.SuccessWithNotFound(c, &response, "user id not found")
			return
		}
		err = bson.Unmarshal(userBsonRawData, &userMongo)
		if err != nil {
			log.CtxLog(c).Errorf("failed to bson.Unmarshal err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if len(userMongo.Role) != 0 && request.UpgradeRole <= userMongo.Role[0] {
			log.CtxLog(c).Infof("user already exist upgrade, info:%v", ucmd.ToJsonStrIgnoreErr(request))
			um.SuccessWithErrCode(c, &response, "user upgrade role not allow", 1000)
			return
		}

		user := model.User{
			Username:    userMongo.Username,
			UserId:      userMongo.UserId,
			CreatedTime: userMongo.CreatedTime,
		}
		if len(userMongo.Role) != 0 {
			user.Role = userMongo.Role[0]
		}

		userUpgradData, _, err := s.watcher.Mongodb().NewMongoEntry(filter).ListByPagination(umw.OAuthDB, "user_upgrade_info", client.Pagination{
			Limit:  int64(1),
			Offset: int64(0),
		},
			client.Ordered{
				Key:        "expire_timestamp",
				Descending: true,
			})
		if err != nil {
			log.CtxLog(c).Errorf("failed to get users info, db: %s, col: %s, filter: %v, err: %v",
				umw.OAuthDB, "user_upgrade_info", filter, err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var mongoUserUpgradeInfo []mongo.MongoUserUpgradeInfo
		err = json.Unmarshal(userUpgradData, &mongoUserUpgradeInfo)
		if err != nil {
			log.CtxLog(c).Errorf("failed to bson.Unmarshal, err: %s", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}

		userUpgradeInfo := model.UserUpgradeInfo{}
		if len(mongoUserUpgradeInfo) == 0 {
			userUpgradeInfo.IsUpgrade = false
		} else {
			if mongoUserUpgradeInfo[0].ExpireTimestamp > time.Now().UnixMilli() {
				userUpgradeInfo.IsUpgrade = true
				user.Role = mongoUserUpgradeInfo[0].UpgradeRole
				userUpgradeInfo.OriginalRole = mongoUserUpgradeInfo[0].OriginalRole
				userUpgradeInfo.UpgradeRole = mongoUserUpgradeInfo[0].UpgradeRole
				userUpgradeInfo.ExpireTimestamp = mongoUserUpgradeInfo[0].ExpireTimestamp
			}
		}
		user.UpgradeInfo = userUpgradeInfo
		response.Data = user

		if len(mongoUserUpgradeInfo) != 0 {
			if mongoUserUpgradeInfo[0].ExpireTimestamp > time.Now().UnixMilli() {
				log.CtxLog(c).Infof("user already exist upgrade, info:%v", ucmd.ToJsonStrIgnoreErr(request))
				um.SuccessWithErrCode(c, &response, "user already exist upgrade", 4000)
				return
			}
		}

		// 获取远程运维人员
		occRemoteUser, err := service.GetOneOccRemoteUser(c)
		if err != nil {
			log.CtxLog(c).Errorf("service.GetOneOccRemoteUser err. err:%v", err)
		}
		// 获取换电站的站长和负责人
		resourceId := deviceId
		deviceInfo, exist := cache.PowerSwapCache.GetSingleDevice(deviceId)
		if !exist {
			log.CtxLog(c).Errorf("device not found. deviceId:%v", deviceId)
		} else {
			resourceId = deviceInfo.ResourceId
		}
		deviceSupervior, deviceAgent, err := service.GetDeviceAgentAndSupervisor(c, resourceId)
		if err != nil {
			log.CtxLog(c).Errorf("service.GetDeviceAgentAndSupervisor err. err:%v", err)
		}

		if deviceAgent == "" && deviceSupervior == "" {
			log.CtxLog(c).Errorf("deviceAgent and deviceSupervior is empty")
			deviceAgent = "shawn.wu1"
			deviceSupervior = "shawn.wu1"
		}

		if occRemoteUser == "" && deviceAgent == "" && deviceSupervior == "" {
			occRemoteUser = "shawn.wu1"
			deviceAgent = "shawn.wu1"
			deviceSupervior = "shawn.wu1"
		}
		if ucmd.GetEnv() != "prod" {
			occRemoteUser = config.TestData.AccountUpgrade.OnlineOperator
			deviceAgent = config.TestData.AccountUpgrade.DeviceOwner
			deviceSupervior = config.TestData.AccountUpgrade.DeviceManager
		}

		// 创建工单
		_, err = service.CreateWorkflowInstance(c, config.Cfg.Workflow.UserRoleUpgradeFlowCode, userId, map[string]string{
			"project":          project,
			"device_id":        deviceId,
			"duration":         "2",
			"device_owner":     deviceAgent,
			"device_manager":   deviceSupervior,
			"online_operator":  occRemoteUser,
			"user_id":          userId,
			"original_role":    fmt.Sprintf("%v", request.OriginalRole),
			"upgrade_role":     fmt.Sprintf("%v", request.UpgradeRole),
			"original_role_cn": model.Role2CnName[request.OriginalRole],
			"upgrade_role_cn":  model.Role2CnName[request.UpgradeRole],
		})
		if err != nil {
			log.CtxLog(c).Infof("service.CreateWorkflowInstance err, err:%v", err)
			um.SuccessWithErrCode(c, &response, "Workflow create fail", 3000)
			return
		}

		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

type T struct {
	RequestId   string `json:"request_id"`
	ServerTime  int64  `json:"server_time"`
	ResultCode  string `json:"result_code"`
	EncryptType int    `json:"encrypt_type"`
	Data        struct {
		TotalResults int `json:"total_results"`
		ResultList   []struct {
			Id            int    `json:"id"`
			DomainAccount string `json:"domain_account"`
			Ready         int    `json:"ready"`
		} `json:"result_list"`
	} `json:"data"`
}

func (s *system) UserUpgradeRollback() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.Response
		)
		project := c.Param("project")
		deviceId := c.Param("device_id")
		userId := c.Param("user_id")
		if project == "" || deviceId == "" || userId == "" {
			log.CtxLog(c).Errorf("project or device_id or user_id is empty")
			um.FailWithBadRequest(c, &response, "project or device_id or user_id is empty")
			return
		}

		filter := bson.D{bson.E{Key: "user_id", Value: userId}}
		userUpgradeBsonRawData, _, err := s.watcher.Mongodb().NewMongoEntry(filter).ListByPagination(umw.OAuthDB, "user_upgrade_info", client.Pagination{
			Limit:  int64(1),
			Offset: int64(0),
		},
			client.Ordered{
				Key:        "expire_timestamp",
				Descending: true,
			})
		if err != nil {
			log.CtxLog(c).Errorf("failed to get users info, db: %s, col: %s, filter: %v, err: %v",
				umw.OAuthDB, "user_upgrade_info", filter, err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		var mongoUserUpgradeInfo []mongo.MongoUserUpgradeInfo
		err = json.Unmarshal(userUpgradeBsonRawData, &mongoUserUpgradeInfo)
		if err != nil {
			log.CtxLog(c).Errorf("failed to bson.Unmarshal, err: %s", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if len(mongoUserUpgradeInfo) == 0 {
			log.CtxLog(c).Infof("user not exist upgrade, user:%v", userId)
			um.SuccessWithErrCode(c, &response, "user not exist upgrade", 1000)
			return
		}
		if mongoUserUpgradeInfo[0].ExpireTimestamp < time.Now().UnixMilli() {
			log.CtxLog(c).Infof("user not exist upgrade, user:%v", userId)
			um.SuccessWithErrCode(c, &response, "user not exist upgrade", 1000)
			return
		}

		update := bson.M{
			"expire_timestamp": 0,
			"update_timestamp": time.Now().UnixMilli(),
		}
		err = s.watcher.Mongodb().NewMongoEntry(filter).UpdateMany(umw.OAuthDB, "user_upgrade_info", bson.M{"$set": update})
		//err = s.watcher.Mongodb().NewMongoEntry(filter).UpdateById(umw.OAuthDB, "user_upgrade_info", mongoUserUpgradeInfo[0].Id, bson.M{"$set": update})
		if err != nil {
			log.CtxLog(c).Errorf("failed to update mongodb, err: %s", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (s *system) UserUpgradeExpandTime() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.UserUpgradeExpandTimeRequest
			response model.Response
		)
		project := c.Param("project")
		deviceId := c.Param("device_id")
		userId := c.Param("user_id")
		if project == "" || deviceId == "" || userId == "" {
			log.CtxLog(c).Errorf("project or device_id or user_id is empty")
			um.FailWithBadRequest(c, &response, "project or device_id or user_id is empty")
			return
		}
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		filter := bson.D{bson.E{Key: "user_id", Value: userId}}
		userUpgradData, _, err := s.watcher.Mongodb().NewMongoEntry(filter).ListByPagination(umw.OAuthDB, "user_upgrade_info", client.Pagination{
			Limit:  int64(1),
			Offset: int64(0),
		},
			client.Ordered{
				Key:        "expire_timestamp",
				Descending: true,
			})
		if err != nil {
			log.CtxLog(c).Errorf("failed to get users info, db: %s, col: %s, filter: %v, err: %v",
				umw.OAuthDB, "user_upgrade_info", filter, err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		var mongoUserUpgradeInfo []mongo.MongoUserUpgradeInfo
		err = json.Unmarshal(userUpgradData, &mongoUserUpgradeInfo)
		if err != nil {
			log.CtxLog(c).Errorf("failed to bson.Unmarshal, err: %s", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if len(mongoUserUpgradeInfo) == 0 {
			log.CtxLog(c).Infof("not exist upgrade user:%v", userId)
			um.SuccessWithErrCode(c, &response, "not exist upgrade", 1000)
			return
		}
		var data struct {
			UserId          string `json:"user_id"`
			OriginalRole    int    `json:"original_role"`
			UpgradeRole     int    `json:"upgrade_role"`
			ExpireTimestamp int64  `json:"expire_timestamp"`
		}
		data.ExpireTimestamp = mongoUserUpgradeInfo[0].ExpireTimestamp
		data.UserId = mongoUserUpgradeInfo[0].UserId
		data.OriginalRole = mongoUserUpgradeInfo[0].OriginalRole
		data.UpgradeRole = mongoUserUpgradeInfo[0].UpgradeRole
		response.Data = data

		if mongoUserUpgradeInfo[0].ExpireTimestamp < time.Now().UnixMilli() {
			log.CtxLog(c).Infof("user upgrade upgrade already expire user:%v", userId)
			um.SuccessWithErrCode(c, &response, "user upgrade upgrade already expire", 1000)
			return
		}

		newExpireTimestamp := mongoUserUpgradeInfo[0].ExpireTimestamp + request.ExpandTime
		update := bson.M{
			"expire_timestamp": newExpireTimestamp,
			"update_timestamp": time.Now().UnixMilli(),
		}
		err = s.watcher.Mongodb().NewMongoEntry(filter).UpdateById(umw.OAuthDB, "user_upgrade_info", mongoUserUpgradeInfo[0].Id, bson.M{"$set": update})
		if err != nil {
			log.CtxLog(c).Errorf("failed to update mongodb, err: %s", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		data.ExpireTimestamp = newExpireTimestamp
		response.Data = data

		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (s *system) CreateNewUser() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			requestData model.UserAddParams
			response    um.Base
		)
		project := c.Param("project")
		userId := c.Param("user_id")
		if project != umw.PowerSwap && project != umw.PowerSwap2 && project != umw.PUS3 {
			log.CtxLog(c).Errorf("`project` is invalid: %s", project)
			um.FailWithBadRequest(c, &response, "`project` is invalid")
			return
		}
		if err := c.BindJSON(&requestData); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		err := s.watcher.Mongodb().NewMongoEntry(bson.D{bson.E{Key: "user_id", Value: userId}}).InsertOne(
			umw.OAuthDB, umw.UserBaseInfo, umw.MongoUserInfo{
				Username:    requestData.Username,
				UserId:      userId,
				Email:       fmt.Sprintf("%<EMAIL>", userId),
				Role:        requestData.Role,
				CreatedTime: time.Now().UnixMilli(),
				UpdatedTime: time.Now().UnixMilli(),
			},
			client.IndexOption{Name: "user_id", Fields: bson.D{{"user_id", 1}}, Unique: true},
		)
		if err != nil {
			log.CtxLog(c).Errorf("failed to insert the user, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		log.CtxLog(c).Infof("succeeded to insert the user: %s", userId)
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (s *system) UpdateUser() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			requestData model.UserUpdateParams
			response    um.Base
		)
		project := c.Param("project")
		userId := c.Param("user_id")
		if project != umw.PowerSwap && project != umw.PowerSwap2 && project != umw.PUS3 {
			log.CtxLog(c).Errorf("`project` is invalid: %s", project)
			um.FailWithBadRequest(c, &response, "`project` is invalid")
			return
		}
		if err := c.BindJSON(&requestData); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if err := s.watcher.Mongodb().NewMongoEntry().UpdateUserRole(userId, requestData.Role[0]); err != nil {
			log.CtxLog(c).Errorf("failed to update user %s role %d, err: %v", userId, requestData.Role[0], err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		log.CtxLog(c).Errorf("succeeded to update user %s role %d", userId, requestData.Role[0])
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (s *system) DeleteUser() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response um.Base
		)
		project := c.Param("project")
		userId := c.Param("user_id")
		if project != umw.PowerSwap && project != umw.PowerSwap2 && project != umw.PUS3 {
			log.CtxLog(c).Errorf("`project` is invalid: %s", project)
			um.FailWithBadRequest(c, &response, "`project` is invalid")
			return
		}
		if err := s.watcher.Mongodb().NewMongoEntry(bson.D{bson.E{Key: "user_id", Value: userId}}).DeleteOne(
			umw.OAuthDB, umw.UserBaseInfo); err != nil {
			log.CtxLog(c).Errorf("failed to delete the user: %s, err: %v", userId, err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		log.CtxLog(c).Infof("succeeded to delete the user: %s", userId)
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (s *system) SsoAccessToken() gin.HandlerFunc {
	return func(c *gin.Context) {
		code := c.Query("code")
		if code == "" {
			c.JSON(http.StatusOK, gin.H{"err_code": 1, "message": "缺少必要参数code"})
			return
		}
		ct := ucmd.NewHttpClient(ucmd.HttpClient{
			URL: fmt.Sprintf("%s/oauth2/accessToken?code=%s&client_id=%s&client_secret=%s&redirect_uri=%s",
				s.sso.SignUrl, code, s.sentry.AppId, s.sentry.AppSecret, s.welkin.FrontendUrl),
			Method: "GET",
		})
		body, _, err := ct.Do()
		if err != nil {
			log.CtxLog(c).Errorf("sso access, err: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"err_code": 3, "message": err.Error()})
			return
		}
		defer body.Close()
		data, err := ioutil.ReadAll(body)
		if err != nil {
			log.CtxLog(c).Errorf("sso access, err: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"err_code": 3, "message": err.Error()})
			return
		}
		accessToken := strings.Split(strings.Split(string(data), "&")[0], "=")[1]
		ct = ucmd.NewHttpClient(ucmd.HttpClient{
			URL:    fmt.Sprintf("%s/oauth2/profile?access_token=%s", s.sso.SignUrl, accessToken),
			Method: "GET",
		})
		body, _, err = ct.Do()
		if err != nil {
			log.CtxLog(c).Errorf("sso access, err: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"err_code": 3, "message": err.Error()})
			return
		}
		defer body.Close()
		data, err = ioutil.ReadAll(body)
		if err != nil {
			log.CtxLog(c).Errorf("sso access, err: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"err_code": 3, "message": err.Error()})
			return
		}

		var result map[string]interface{}
		err = json.Unmarshal(data, &result)
		if err != nil {
			log.CtxLog(c).Errorf("sso access, err: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"err_code": 3, "message": err.Error()})
			return
		}
		userId, ok := result["id"].(string)
		if !ok {
			log.CtxLog(c).Errorf("sso access, err: user_id is nil")
			c.JSON(http.StatusInternalServerError, gin.H{"err_code": 3, "message": "user_id is nil"})
			return
		}
		conn := udao.NewRedisConn(s.watcher.Redis())
		defer conn.Close()
		key := fmt.Sprintf("welkin/users/%s", userId)
		reply, err := conn.Do("set", key, accessToken)
		if err != nil || reply == nil {
			log.CtxLog(c).Errorf("set sso access to redis fail, err: %v", err)
			c.JSON(http.StatusOK, gin.H{"err_code": 1, "message": err.Error()})
			return
		}
		// 过期时间设置为7天
		reply, err = conn.Do("expire", key, 7*24*3600)
		if err != nil || reply == nil {
			log.CtxLog(c).Errorf("set sso access expire time fail, err: %v", err)
			c.JSON(http.StatusOK, gin.H{"err_code": 1, "message": err.Error()})
			return
		}
		c.JSON(http.StatusOK, gin.H{"err_code": 0, "message": "", "user_id": userId})
		c.SetCookie("user_id", userId, model.AccessTokenTime, "/", s.welkin.FrontendUrl, false, true)
		return
	}
}

func (s *system) GetMenuList() gin.HandlerFunc {
	return func(c *gin.Context) {
		session := sessions.Default(c)
		user_id := session.Get("user_id").(string)
		data, ok := c.Get("lanka_data")
		if ok {
			d := data.(lanka.Data)
			c.JSON(http.StatusOK, gin.H{"err_code": 0, "data": d})
			return
		}
		errRaw, ok := c.Get("lanka_error")
		if !ok {
			c.JSON(http.StatusInternalServerError, gin.H{
				"err_code": 1,
				"message":  "lanka data not exist, and lack of of lanka_error",
			})
			return
		}
		err := errRaw.(error)
		switch {
		case errors.Is(err, lanka.ErrAccessTokenInvaild):
			c.JSON(http.StatusUnauthorized, gin.H{
				"message": "access token is invalid",
			})
			return
		case errors.Is(err, lanka.ErrAuthorizationVerification):
			//env := os.Getenv("K8S_ENV")
			env := ucmd.GetEnv()
			if env == "" {
				env = "dev"
			}
			region := ucmd.GetArea()
			// call CreateApplication, send the http post to application service, url: pp-authority{-env}.nioint.com
			redirect_url, ierr := lanka.CreateApplication(s.sentry.AppId, user_id, env, region, nil)
			if ierr != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"err_code": 1, "message": ierr.Error()})
				return
			}
			// code 4011 is the protocol code with frontend,
			// means the user need to create an authority application for the platform
			c.JSON(http.StatusOK, gin.H{
				"err_code":     4011,
				"message":      err.Error(),
				"redirect_url": redirect_url,
			})
			return
		case errors.Is(err, lanka.ErrQueryAdminFail):
			c.JSON(http.StatusInternalServerError, gin.H{"err_code": 1, "message": "[QueryAdminFail]" + err.Error()})
			return
		default:
			c.JSON(http.StatusInternalServerError, gin.H{"err_code": 1, "message": err.Error()})
			return
		}

		/*
			session := sessions.Default(c)
			user := session.Get("user_id").(string)
			if user == "" {
				c.JSON(http.StatusOK, gin.H{"err_code": 1, "message": "用户信息为空~"})
				return
			}
			accessToken := session.Get("access_token").(string)
			if accessToken == "" {
				c.JSON(http.StatusOK, gin.H{"err_code": 1, "message": "用户信息为空,前端去跳转授权地址"})
				return
			}
			ts := time.Now().Unix()
			accessToken = fmt.Sprintf("Bearer %s", accessToken)
			path := "/api/v1/administrator"
			sign := ucmd.Sign{
				Timestamp:   ts,
				AppId:       s.sentry.AppId,
				AppSecret:   s.sentry.AppSecret,
				Method:      "GET",
				Path:        path,
				AccessToken: accessToken,
				URLParams:   map[string]interface{}{"hash_type": "sha256"},
			}
			sn := sign.Generate()
			if sn == "" {
				log.CtxLog(c).Errorf("get sso access token, err: `sign` is empty")
				c.JSON(http.StatusInternalServerError, gin.H{"err": "`sign` is empty"})
				return
			}
			ct := ucmd.NewHttpClient(ucmd.HttpClient{
				URL: fmt.Sprintf("%s%s?app_id=%s&sign=%s&timestamp=%d&hash_type=sha256",
					s.sso.LankaUrl, path, s.sentry.AppId, sn, ts),
				Method: "GET",
				Header: map[string]string{
					"Authorization": accessToken,
				},
			})
			body, _, err := ct.Do()
			if err != nil {
				log.CtxLog(c).Errorf("get administrator, err: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{"err_code": 3, "message": err.Error()})
				return
			}
			defer body.Close()
			data, err := io.ReadAll(body)
			if err != nil {
				log.CtxLog(c).Errorf("get administrator, err: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{"err_code": 3, "message": err.Error()})
				return
			}
			administratorResponseMap := lanka.LankaResponse{}
			err = json.Unmarshal(data, &administratorResponseMap)
			if err != nil {
				log.CtxLog(c).Errorf("get administrator, err: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{"err_code": 3, "message": err.Error()})
			}
			datares := administratorResponseMap.Data
			if datares.ID == 0 {
				if strings.Contains(administratorResponseMap.Message, "authorization verification failed，please contact administrator") {
					err = lanka.ErrAuthorizationVerification
					log.CtxLog(c).Errorf("get administrator, err: %v", err)
					c.JSON(http.StatusOK, gin.H{"err_code": 4011, "message": err.Error()})
					return
				} else if strings.Contains(administratorResponseMap.Message, "disable") {
					log.CtxLog(c).Errorf("get administrator, err: %v", administratorResponseMap.Message)
					c.JSON(http.StatusOK, gin.H{"err_code": 4011, "message": administratorResponseMap.Message})
				}
				err = fmt.Errorf("administrator response data is nil, message: %v", administratorResponseMap.Message)
				log.CtxLog(c).Errorf("get administrator, err: %v", err)
				c.JSON(http.StatusUnauthorized, gin.H{"err_code": 1, "message": err.Error()})
				return
			}
			c.JSON(http.StatusOK, gin.H{"data": datares, "err_code": 0})
			return
		*/
	}
}

func (s *system) BrownDragonUserApprovalCallback() gin.HandlerFunc {
	return func(c *gin.Context) {
		var request uwf.ApprovalCallback
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{"result_code": "fail", "message": err.Error()})
			return
		}

		rawData, err := s.watcher.Mongodb().NewMongoEntry(
			bson.D{bson.E{Key: "flow_instance_id", Value: request.FlowInstanceId}}).UpdateAndGetOne(
			umw.OAuthDB, umw.UserAuthApproval, bson.D{
				{"$set", bson.D{
					{"status", request.Status},
					{"current_node_name", request.CurrentNodeName},
					{"previous_node_name", request.PreviousNodeName},
					{"update_ts", time.Now().UnixMilli()},
				}},
			})
		if err != nil {
			log.CtxLog(c).Errorf("update user approval history err: %s", err.Error())
			c.JSON(http.StatusInternalServerError, gin.H{"result_code": "fail", "message": err.Error()})
			return
		}
		if rawData == nil {
			log.CtxLog(c).Warnf("get user approval history err: record not found")
			c.JSON(http.StatusInternalServerError, gin.H{"result_code": "fail", "message": "record not found"})
			return
		}
		if request.Status == "success" {
			var history umw.MongoUserAuthApproval
			if err = bson.Unmarshal(rawData, &history); err != nil {
				log.CtxLog(c).Warnf("decode user approval history err: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{"result_code": "fail", "message": err.Error()})
				return
			}
			err = s.watcher.Mongodb().NewMongoEntry().UpsertBrownDragonUser(umw.MongoUserInfo{
				Username: history.UserName,
				UserId:   request.Context["workflow_creator"],
				Email:    fmt.Sprintf("%<EMAIL>", request.Context["workflow_creator"]),
				Role:     []int{history.Role},
				Factory:  history.Factory,
				Type:     history.Type,
				Projects: history.Projects,
				AuthEnv:  history.AuthEnv,
			})
			if err != nil {
				log.CtxLog(c).Errorf("upsert user info for brown dragon, err: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{"result_code": "fail", "message": err.Error()})
				return
			}
		}

		log.CtxLog(c).Infof("succeed to handle user approval callback")
		c.JSON(http.StatusOK, gin.H{"result_code": "success", "message": "ok"})
	}
}
