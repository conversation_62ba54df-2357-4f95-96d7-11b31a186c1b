package exec

import (
	"fmt"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"net/http/httptest"

	ucfg "git.nevint.com/golang-libs/common-utils/config"
	ulog "git.nevint.com/golang-libs/common-utils/logger"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	"git.nevint.com/welkin2/welkin-backend/client"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	"git.nevint.com/welkin2/welkin-backend/service"
)

var a *alg

func init() {
	os.Setenv("APOLLO_META", "https://apollo-config-cn-stg.nioint.com")
	os.Setenv("ENV", "stg")
	cfg = &ucfg.Config{}
	ucfg.GetConfigApollo("config.json", true, cfg, ucfg.NeedValidate(true), ucfg.ServiceName("ppd-welkin-backend"), ucfg.Cluster(ucfg.GetWelkinApolloCluster()))
	defer ulog.Final()
	cfg.Log.DirPath = "/tmp/logs"
	log.Init(cfg.Log, true)
	logger := log.Logger.Named("CronJob")
	w = client.NewWatcherByParam(cfg, logger)
	InitCronJobs(cfg)
	ossCfg := cfg.OSS
	a = &alg{
		watcher: w,
		oss: service.OSS{
			URL:    ossCfg.PowUrl,
			NMP:    ossCfg.NMPUrl,
			AiURL:  ossCfg.AiUrl,
			AppId:  cfg.Sentry.AppId,
			Logger: log.Logger.Named("OSS"),
		},
		backendUrl: cfg.Welkin.BackendUrl,
		config: struct {
			PowerSwap2 ucfg.AlgConfigDetail
			PUS3       ucfg.AlgConfigDetail
		}{cfg.Welkin.Algorithm.PowerSwap2, cfg.Welkin.Algorithm.PUS3},
		logger: log.Logger.Named(model.ALGORITHM),
		fms: service.FMS{
			URL:          cfg.FMS.Url,
			AppId:        cfg.Sentry.AppId,
			AppSecret:    cfg.Sentry.AppSecret,
			ClientId:     cfg.FMS.ClientId,
			ClientSecret: cfg.FMS.ClientSecret,
			PriBucketKey: cfg.FMS.PriBucketKey,
			PubBucketKey: cfg.FMS.PubBucketKey,
			Header: map[string]string{
				"Content-Type": "application/json;charset=UTF-8",
			},
			Logger: log.Logger.Named("FMS"),
		},
	}
	NewRealtimeHandler(w)
}

func Test_BatteryEnum(t *testing.T) {
	a := NewAlgorithmHandler(w, cfg.Sentry.AppId, cfg.OSS, cfg)
	r := gin.Default()
	r.GET("/test/battery-enum", a.BatteryEnum())
	go func() {
		r.Run(":8080")
	}()

	recorder := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "http://localhost:8080/test/battery-enum?lower_limit=1,2,3,4&upper_limit=5,6,7,8&battery_config=2,3,4,5&config_count=40&project=PowerSwap2", nil)

	r.ServeHTTP(recorder, req)
	if recorder.Code != http.StatusOK {
		t.Fatalf("Expected to get status %d but instead got %d\n", http.StatusOK, recorder.Code)
	}
	responseBody := recorder.Body.String()
	t.Log(responseBody)
}

func Test_CalculateBatteryConfig(t *testing.T) {
	r := gin.Default()
	r.GET("/test/:source", a.CalculateBatteryConfig())
	go func() {
		r.Run(":8080")
	}()

	recorder := httptest.NewRecorder()
	project := umw.PUS3
	devices := "PS-NIO-3285ff15-7f564f27,PS-NIO-0b9e44c5-6f8e7ec2"
	startTime := int64(1717948800000)
	req, _ := http.NewRequest("GET", fmt.Sprintf("http://localhost:8080/test/device?project=%s&devices=%s&start_time=%d", project, devices, startTime), nil)

	r.ServeHTTP(recorder, req)
	if recorder.Code != http.StatusOK {
		t.Fatalf("Expected to get status %d but instead got %d\n", http.StatusOK, recorder.Code)
	}
	responseBody := recorder.Body.String()
	t.Log(responseBody)
}

func newTestAlgorithm() *alg {
	// Note: to fix the compile error
	//cfg := ucfg.GetWelkinConfigApollo(true)
	cfg := &ucfg.Config{}
	defer ulog.Final()
	log.Init(cfg.Log, true)
	testAlg := &alg{
		watcher: client.NewWatcher(gin.New(), cfg, true),
		oss: service.OSS{
			NMP:    "https://pe-nmp-welkin-test.nioint.com",
			AppId:  "101021",
			Logger: log.Logger.Named("OSS"),
		},
		logger: log.Logger.Named(model.ALGORITHM),
	}
	return testAlg
}

func TestAlgCount(t *testing.T) {
	a := newTestAlgorithm()
	responseMap := &algorithmVisualDataMap{mp: make(map[string]*umw.AlgorithmVisualData)}
	opts := model.AlgorithmSuccessRateRequest{
		StartTime: 1681401600000,
		EndTime:   1681488000000,
	}
	err := a.getAlgorithmCount(&gin.Context{}, "PowerSwap2", responseMap, opts)
	if err != nil {
		t.Fatalf("getAlgorithmCount err: %v", err)
	}
	for k, v := range responseMap.mp {
		t.Log(k, v)
	}
}

/*
func TestAlgSuccessRate(t *testing.T) {
	a := newTestAlgorithm()
	responseMap := &algorithmVisualDataMap{mp: make(map[string]*umw.AlgorithmVisualData)}
	opts := model.AlgorithmSuccessRateRequest{
		StartTime: 1682438400000,
	}
	start := time.Now()
	err := a.getAlgorithmSuccessRate("PowerSwap2", responseMap, opts)
	t.Log(time.Now().Sub(start))
	if err != nil {
		t.Fatalf("getAlgorithmSuccessRate err: %v", err)
	}
	for k, v := range responseMap.mp {
		t.Log(k, v)
	}
}
*/

func TestAlgAecData(t *testing.T) {
	a := newTestAlgorithm()
	responseMap := &algorithmVisualDataMap{mp: make(map[string]*umw.AlgorithmVisualData)}
	opts := model.AlgorithmSuccessRateRequest{
		StartTime: 1681401600000,
		EndTime:   1681488000000,
	}
	start := time.Now()
	err := a.getAlgorithmAecData(&gin.Context{}, "PowerSwap2", responseMap, opts)
	t.Log(time.Now().Sub(start))
	if err != nil {
		t.Fatalf("getAlgorithmSuccessRate err: %v", err)
	}
	for k, v := range responseMap.mp {
		t.Log(k, v)
	}
}

/*
func TestCalculateFTT(t *testing.T) {
	a := newTestAlgorithm()
	project := "PowerSwap2"
	day := int64(1686412800000)
	var fttAlgorithms []int
	if err := a.getFTTMapOnce(project); err != nil {
		t.Fatalf("fail to get ftt map, err: %v", err)
	}
	for algorithm, isFTT := range algorithmFTTMap {
		if isFTT {
			fmt.Printf("%s(%d) ", algorithm, algorithmIntMap[algorithm])
			fttAlgorithms = append(fttAlgorithms, algorithmIntMap[algorithm])
		}
	}
	fmt.Println()

	fttInfo, err := a.CalculateFTT(project, day, fttAlgorithms)
	if err != nil {
		t.Fatalf("fail to CalculateFTT, err: %v", err)
	}
	//t.Logf("%+v\n", fttInfo)
	for _, d := range fttInfo.DeviceFTT {
		fmt.Printf("%v,", d.DeviceId)
	}
}
*/

/*
func Test_calculateFTT(t *testing.T) {
	a := newTestAlgorithm()
	project := "PowerSwap2"
	day := int64(1686412800000)
	var fttAlgorithms []int
	if err := a.getFTTMapOnce(project); err != nil {
		t.Fatalf("fail to get ftt map, err: %v", err)
	}
	for algorithm, isFTT := range algorithmFTTMap {
		if isFTT {
			fmt.Printf("%s(%d) ", algorithm, algorithmIntMap[algorithm])
			fttAlgorithms = append(fttAlgorithms, algorithmIntMap[algorithm])
		}
	}
	fmt.Println()

	total, ftt, err := a.calculateFTT(project, day, fttAlgorithms, "PS-NIO-ca4dcfa6-7fcff33f")
	if err != nil {
		t.Fatalf("fail to calculateFTT, err: %v", err)
	}
	t.Logf("total: %v\tftt: %v\n", total, ftt)
}
*/

//func TestIssueCommandToStation(t *testing.T) {
//	requestData := umw.MongoCmsOperationRecord{
//		RequestId: xid.New().String(),
//		ModelTriggerTime: time.Now().UnixMilli(),
//		DeviceId: "PS-NIO-31da205c-64ba4b36",
//		ModuleType: "UU",
//		BatteryInfo: []umw.BatteryInfo{
//			{
//				SlotId: 1,
//				BatteryCapacity: 200,
//				BatteryId: "P0079340AT199210061100071B00037",
//				BatteryRealSoc: 19,
//				BatteryType: 3,
//				BatteryUserSoc: 16,
//				ChargingCurrent: 0,
//				ChargingStatus: 0,
//				InitialBatteryTemp: 0,
//			},
//			{
//				SlotId: 2,
//				BatteryCapacity: 200,
//				BatteryId: "P0079340AO301200061300001A10036",
//				BatteryRealSoc: 88,
//				BatteryType: 3,
//				BatteryUserSoc: 93,
//				ChargingCurrent: 0,
//				ChargingStatus: 0,
//				InitialBatteryTemp: 0,
//			},
//			{
//				SlotId: 3,
//				BatteryCapacity: 280,
//				BatteryId: "P0212542AE22621V218952L12B00152",
//				BatteryRealSoc: 42,
//				BatteryType: 6,
//				BatteryUserSoc: 42,
//				ChargingCurrent: -109.6,
//				ChargingStatus: 0,
//				InitialBatteryTemp: 55,
//			},
//			{
//				SlotId: 4,
//				BatteryCapacity: 195,
//				BatteryId: "P0223981BA26522V218952L15B00062",
//				BatteryRealSoc: 93,
//				BatteryType: 8,
//				BatteryUserSoc: 93,
//				ChargingCurrent: 0,
//				ChargingStatus: 0,
//				InitialBatteryTemp: 0,
//			},
//			{
//				SlotId: 5,
//				BatteryCapacity: 280,
//				BatteryId: "P0205908AK00922V218952L12A00193",
//				BatteryRealSoc: 89,
//				BatteryType: 6,
//				BatteryUserSoc: 93,
//				ChargingCurrent: 0,
//				ChargingStatus: 0,
//				InitialBatteryTemp: 55,
//			},
//			{
//				SlotId: 6,
//				BatteryCapacity: 200,
//				BatteryId: "P0079340AR05821006110X051B00118",
//				BatteryRealSoc: 88,
//				BatteryType: 3,
//				BatteryUserSoc: 93,
//				ChargingCurrent: 0,
//				ChargingStatus: 0,
//				InitialBatteryTemp: 0,
//			},
//			{
//				SlotId: 7,
//				BatteryCapacity: 200,
//				BatteryId: "P0079340AR081210061100071B00053",
//				BatteryRealSoc: 25,
//				BatteryType: 3,
//				BatteryUserSoc: 22,
//				ChargingCurrent: -130,
//				ChargingStatus: 0,
//				InitialBatteryTemp: 55,
//			},
//			{
//				SlotId: 8,
//				BatteryCapacity: 280,
//				BatteryId: "P0205908AQ13622V218952L12B00156",
//				BatteryRealSoc: 91,
//				BatteryType: 6,
//				BatteryUserSoc: 93,
//				ChargingCurrent: 0,
//				ChargingStatus: 1,
//				InitialBatteryTemp: 0,
//			},
//			{
//				SlotId: 9,
//				BatteryCapacity: 195,
//				BatteryId: "P0223981BD14723V218952L19B00119",
//				BatteryRealSoc: 97,
//				BatteryType: 8,
//				BatteryUserSoc: 93,
//				ChargingCurrent: 0,
//				ChargingStatus: 0,
//				InitialBatteryTemp: 0,
//			},
//			{
//				SlotId: 10,
//				BatteryCapacity: 200,
//				BatteryId: "P0079340AK152200061300001A00029",
//				BatteryRealSoc: 88,
//				BatteryType: 3,
//				BatteryUserSoc: 93,
//				ChargingCurrent: 0,
//				ChargingStatus: 0,
//				InitialBatteryTemp: 0,
//			},
//			{
//				SlotId: 11,
//				BatteryCapacity: 200,
//				BatteryId: "P0079340AD314180061300001B00044",
//				BatteryRealSoc: 18,
//				BatteryType: 1,
//				BatteryUserSoc: 15,
//				ChargingCurrent: -129.7,
//				ChargingStatus: 0,
//				InitialBatteryTemp: 55,
//			},
//			{
//				SlotId: 12,
//				BatteryCapacity: 280,
//				BatteryId: "P0205908AN02622V218952L12B00240",
//				BatteryRealSoc: 89,
//				BatteryType: 6,
//				BatteryUserSoc: 93,
//				ChargingCurrent: 0,
//				ChargingStatus: 1,
//				InitialBatteryTemp: 0,
//			},
//			{
//				SlotId: 13,
//				BatteryCapacity: 0,
//				BatteryId: "",
//				BatteryRealSoc: 0,
//				BatteryType: -1,
//				BatteryUserSoc: 0,
//				ChargingCurrent: 0,
//				ChargingStatus: 0,
//				InitialBatteryTemp: 55,
//			},
//		},
//		logger: log.Logger.Named(model.ALGORITHM),
//	}
//	return testAlg
//}
//
//func TestAlgCount(t *testing.T) {
//	a := newTestAlgorithm()
//	responseMap := &algorithmVisualDataMap{mp: make(map[string]*umw.AlgorithmVisualData)}
//	opts := model.AlgorithmSuccessRateRequest{
//		StartTime: 1681401600000,
//		EndTime:   1681488000000,
//	}
//	err := a.getAlgorithmCount("PowerSwap2", responseMap, opts)
//	if err != nil {
//		t.Fatalf("getAlgorithmCount err: %v", err)
//	}
//	for k, v := range responseMap.mp {
//		t.Log(k, v)
//	}
//}
//
//func TestAlgSuccessRate(t *testing.T) {
//	a := newTestAlgorithm()
//	responseMap := &algorithmVisualDataMap{mp: make(map[string]*umw.AlgorithmVisualData)}
//	opts := model.AlgorithmSuccessRateRequest{
//		StartTime: 1682438400000,
//	}
//	start := time.Now()
//	err := a.getAlgorithmSuccessRate("PowerSwap2", responseMap, opts)
//	t.Log(time.Now().Sub(start))
//	if err != nil {
//		t.Fatalf("getAlgorithmSuccessRate err: %v", err)
//	}
//	for k, v := range responseMap.mp {
//		t.Log(k, v)
//	}
//}
//
//func TestAlgAecData(t *testing.T) {
//	a := newTestAlgorithm()
//	responseMap := &algorithmVisualDataMap{mp: make(map[string]*umw.AlgorithmVisualData)}
//	opts := model.AlgorithmSuccessRateRequest{
//		StartTime: 1681401600000,
//		EndTime:   1681488000000,
//	}
//	start := time.Now()
//	err := a.getAlgorithmAecData("PowerSwap2", responseMap, opts)
//	t.Log(time.Now().Sub(start))
//	if err != nil {
//		t.Fatalf("getAlgorithmSuccessRate err: %v", err)
//	}
//	for k, v := range responseMap.mp {
//		t.Log(k, v)
//	}
//}
//
//func TestCalculateFTT(t *testing.T) {
//	a := newTestAlgorithm()
//	project := "PowerSwap2"
//	day := int64(1686412800000)
//	var fttAlgorithms []int
//	if err := a.getFTTMapOnce(project); err != nil {
//		t.Fatalf("fail to get ftt map, err: %v", err)
//	}
//	for algorithm, isFTT := range algorithmFTTMap {
//		if isFTT {
//			fmt.Printf("%s(%d) ", algorithm, algorithmIntMap[algorithm])
//			fttAlgorithms = append(fttAlgorithms, algorithmIntMap[algorithm])
//		}
//	}
//	fmt.Println()
//
//	fttInfo, err := a.CalculateFTT(project, day, fttAlgorithms)
//	if err != nil {
//		t.Fatalf("fail to CalculateFTT, err: %v", err)
//	}
//	//t.Logf("%+v\n", fttInfo)
//	for _, d := range fttInfo.DeviceFTT {
//		fmt.Printf("%v,", d.DeviceId)
//	}
//}
//
//func Test_calculateFTT(t *testing.T) {
//	a := newTestAlgorithm()
//	project := "PowerSwap2"
//	day := int64(1686412800000)
//	var fttAlgorithms []int
//	if err := a.getFTTMapOnce(project); err != nil {
//		t.Fatalf("fail to get ftt map, err: %v", err)
//	}
//	for algorithm, isFTT := range algorithmFTTMap {
//		if isFTT {
//			fmt.Printf("%s(%d) ", algorithm, algorithmIntMap[algorithm])
//			fttAlgorithms = append(fttAlgorithms, algorithmIntMap[algorithm])
//		}
//	}
//	fmt.Println()
//
//	total, ftt, err := a.calculateFTT(project, day, fttAlgorithms, "PS-NIO-ca4dcfa6-7fcff33f")
//	if err != nil {
//		t.Fatalf("fail to calculateFTT, err: %v", err)
//	}
//	t.Logf("total: %v\tftt: %v\n", total, ftt)
//}
//
////func TestIssueCommandToStation(t *testing.T) {
////	requestData := umw.MongoCmsOperationRecord{
////		RequestId: xid.New().String(),
////		ModelTriggerTime: time.Now().UnixMilli(),
////		DeviceId: "PS-NIO-31da205c-64ba4b36",
////		ModuleType: "UU",
////		BatteryInfo: []umw.BatteryInfo{
////			{
////				SlotId: 1,
////				BatteryCapacity: 200,
////				BatteryId: "P0079340AT199210061100071B00037",
////				BatteryRealSoc: 19,
////				BatteryType: 3,
////				BatteryUserSoc: 16,
////				ChargingCurrent: 0,
////				ChargingStatus: 0,
////				InitialBatteryTemp: 0,
////			},
////			{
////				SlotId: 2,
////				BatteryCapacity: 200,
////				BatteryId: "P0079340AO301200061300001A10036",
////				BatteryRealSoc: 88,
////				BatteryType: 3,
////				BatteryUserSoc: 93,
////				ChargingCurrent: 0,
////				ChargingStatus: 0,
////				InitialBatteryTemp: 0,
////			},
////			{
////				SlotId: 3,
////				BatteryCapacity: 280,
////				BatteryId: "P0212542AE22621V218952L12B00152",
////				BatteryRealSoc: 42,
////				BatteryType: 6,
////				BatteryUserSoc: 42,
////				ChargingCurrent: -109.6,
////				ChargingStatus: 0,
////				InitialBatteryTemp: 55,
////			},
////			{
////				SlotId: 4,
////				BatteryCapacity: 195,
////				BatteryId: "P0223981BA26522V218952L15B00062",
////				BatteryRealSoc: 93,
////				BatteryType: 8,
////				BatteryUserSoc: 93,
////				ChargingCurrent: 0,
////				ChargingStatus: 0,
////				InitialBatteryTemp: 0,
////			},
////			{
////				SlotId: 5,
////				BatteryCapacity: 280,
////				BatteryId: "P0205908AK00922V218952L12A00193",
////				BatteryRealSoc: 89,
////				BatteryType: 6,
////				BatteryUserSoc: 93,
////				ChargingCurrent: 0,
////				ChargingStatus: 0,
////				InitialBatteryTemp: 55,
////			},
////			{
////				SlotId: 6,
////				BatteryCapacity: 200,
////				BatteryId: "P0079340AR05821006110X051B00118",
////				BatteryRealSoc: 88,
////				BatteryType: 3,
////				BatteryUserSoc: 93,
////				ChargingCurrent: 0,
////				ChargingStatus: 0,
////				InitialBatteryTemp: 0,
////			},
////			{
////				SlotId: 7,
////				BatteryCapacity: 200,
////				BatteryId: "P0079340AR081210061100071B00053",
////				BatteryRealSoc: 25,
////				BatteryType: 3,
////				BatteryUserSoc: 22,
////				ChargingCurrent: -130,
////				ChargingStatus: 0,
////				InitialBatteryTemp: 55,
////			},
////			{
////				SlotId: 8,
////				BatteryCapacity: 280,
////				BatteryId: "P0205908AQ13622V218952L12B00156",
////				BatteryRealSoc: 91,
////				BatteryType: 6,
////				BatteryUserSoc: 93,
////				ChargingCurrent: 0,
////				ChargingStatus: 1,
////				InitialBatteryTemp: 0,
////			},
////			{
////				SlotId: 9,
////				BatteryCapacity: 195,
////				BatteryId: "P0223981BD14723V218952L19B00119",
////				BatteryRealSoc: 97,
////				BatteryType: 8,
////				BatteryUserSoc: 93,
////				ChargingCurrent: 0,
////				ChargingStatus: 0,
////				InitialBatteryTemp: 0,
////			},
////			{
////				SlotId: 10,
////				BatteryCapacity: 200,
////				BatteryId: "P0079340AK152200061300001A00029",
////				BatteryRealSoc: 88,
////				BatteryType: 3,
////				BatteryUserSoc: 93,
////				ChargingCurrent: 0,
////				ChargingStatus: 0,
////				InitialBatteryTemp: 0,
////			},
////			{
////				SlotId: 11,
////				BatteryCapacity: 200,
////				BatteryId: "P0079340AD314180061300001B00044",
////				BatteryRealSoc: 18,
////				BatteryType: 1,
////				BatteryUserSoc: 15,
////				ChargingCurrent: -129.7,
////				ChargingStatus: 0,
////				InitialBatteryTemp: 55,
////			},
////			{
////				SlotId: 12,
////				BatteryCapacity: 280,
////				BatteryId: "P0205908AN02622V218952L12B00240",
////				BatteryRealSoc: 89,
////				BatteryType: 6,
////				BatteryUserSoc: 93,
////				ChargingCurrent: 0,
////				ChargingStatus: 1,
////				InitialBatteryTemp: 0,
////			},
////			{
////				SlotId: 13,
////				BatteryCapacity: 0,
////				BatteryId: "",
////				BatteryRealSoc: 0,
////				BatteryType: -1,
////				BatteryUserSoc: 0,
////				ChargingCurrent: 0,
////				ChargingStatus: 0,
////				InitialBatteryTemp: 55,
////			},
////		},
////	}
////
////	cmsResponse := model.CMSCalculationResults{
////		Version: "1.0.0",
////		BatteryInfo: make(map[int32]model.CMSResult, len(requestData.BatteryInfo)),
////		BatteryDemand: []umw.BatteryDemand{
////			{Hour: 4, Battery100: 10, Battery70: 30},
////		},
////	}
////	for i := 1; i <= 13; i++ {
////		cmsResponse.BatteryInfo[int32(i)] = model.CMSResult{
////			PowerDistributionCapacity: 0,
////			BranchCircuitCurrentLimit: -250,
////			Circuit01DistributionCapacity: 20,
////			Circuit02DistributionCapacity: 20,
////			MaxAllowedModulePower1: 20,
////			MaxAllowedModulePower2: 20,
////			OptimizationLabel: 0,
////			BeforeSwitchCurrent: 10.1,
////			SwitchSoc: 0.1,
////			AfterSwitchCurrent: 11.1,
////			ChargingMode: "upstairs",
////			SwitchMoment: 0.2,
////			CurrentCurrent: -116.0,
////		}
////	}
////	a := newTestAlgorithm()
////	mw := &service.MongoWatcher{Client: a.watcher.Mongodb()}
////	insertTs := time.Now().UnixMilli()
////	cmsResponse.ErrCode = 1001
////	record := umw.MongoCmsOperationRecord{
////		RequestId: requestData.RequestId,
////		ModelTriggerTime: requestData.ModelTriggerTime,
////		DeviceId: requestData.DeviceId,
////		ModuleType: requestData.ModuleType,
////		InsertTS: insertTs,
////		Date: util.ConvertTime(insertTs),
////		OpRecord: umw.CMSOpRecord{
////			CmsVersion: cmsResponse.Version,
////			CmsErrCode: int32(cmsResponse.ErrCode),
////			CmsMessage: cmsResponse.Message,
////		},
////		BatteryInfo: make([]umw.BatteryInfo, len(requestData.BatteryInfo)),
////		BatteryDemand: cmsResponse.BatteryDemand,
////	}
////	for _, item := range requestData.BatteryInfo {
////		record.BatteryInfo[item.SlotId-1] = umw.BatteryInfo{
////			SlotId:    item.SlotId,
////			BatteryId: item.BatteryId,
////			BatteryType: item.BatteryType,
////			BatteryCapacity: item.BatteryCapacity,
////			ChargingStatus: item.ChargingStatus,
////			BatteryUserSoc: item.BatteryUserSoc,
////			BatteryRealSoc: item.BatteryRealSoc,
////			PowerDistributionCapacity: cmsResponse.BatteryInfo[item.SlotId].PowerDistributionCapacity,
////			Circuit01DistributionCapacity: cmsResponse.BatteryInfo[item.SlotId].Circuit01DistributionCapacity,
////			Circuit02DistributionCapacity: cmsResponse.BatteryInfo[item.SlotId].Circuit02DistributionCapacity,
////			BranchCircuitCurrentLimit: cmsResponse.BatteryInfo[item.SlotId].BranchCircuitCurrentLimit,
////			MaxAllowedModulePower1: cmsResponse.BatteryInfo[item.SlotId].MaxAllowedModulePower1,
////			MaxAllowedModulePower2: cmsResponse.BatteryInfo[item.SlotId].MaxAllowedModulePower2,
////			ChargingCurrent: item.ChargingCurrent,
////			InitialBatteryTemp: item.InitialBatteryTemp,
////			OptimizationLabel: cmsResponse.BatteryInfo[item.SlotId].OptimizationLabel,
////			ChargingMode: cmsResponse.BatteryInfo[item.SlotId].ChargingMode,
////			SwitchSoc: cmsResponse.BatteryInfo[item.SlotId].SwitchSoc,
////			SwitchMoment: cmsResponse.BatteryInfo[item.SlotId].SwitchMoment,
////			BeforeSwitchCurrent: cmsResponse.BatteryInfo[item.SlotId].BeforeSwitchCurrent,
////			AfterSwitchCurrent: cmsResponse.BatteryInfo[item.SlotId].AfterSwitchCurrent,
////			CurrentCurrent: cmsResponse.BatteryInfo[item.SlotId].CurrentCurrent,
////		}
////	}
////	t.Log(record)
////	if record.OpRecord.CmsErrCode != 0 {
////		// save cms operation record to mongodb
////		record.OpRecord.StrategyPushCode = -1
////		record.OpRecord.StrategyExecCode = -1
////		if err := mw.InsertCmsOperationRecord(requestData.RequestId, record); err != nil {
////			a.logger.Errorf("failed to insert new cms operation record, device_id: %s, err: %v", requestData.DeviceId, err)
////		} else {
////			a.logger.Infof("cms calculation failed, do not need to issue command, device_id: %s", requestData.DeviceId)
////		}
////		return
////	}
////	// issue command by oss mqtt
////	maxCurrentList := make([]map[string]interface{}, 13)
////	for _, item := range record.BatteryInfo {
////		maxCurrentList[item.SlotId-1] = map[string]interface{}{
////			"slot_id": item.SlotId,
////			"battery_id": item.BatteryId,
////			"current": item.CurrentCurrent,
////			"expired_seconds": 20 * 60, // 20min
////		}
////	}
////	record.OpRecord.StrategyPushTime = time.Now().UnixMilli()
////	err := retry.Do(
////		func() error {
////			err := a.oss.IssueCommand(requestData.DeviceId, "CMSBatteryMaxCurrent", maxCurrentList)
////			if err != nil {
////				return err
////			}
////			return nil
////		}, []retry.Option{
////			retry.Delay(100 * time.Millisecond),
////			retry.Attempts(3),
////			retry.LastErrorOnly(true),
////		}...)
////	if err != nil {
////		record.OpRecord.StrategyPushCode = 1
////		a.logger.Errorf("failed to push strategy to station, err: %v", err)
////	} else {
////		a.logger.Infof("succeeded to push strategy to station, device_id: %s, request_id: %s", requestData.DeviceId, requestData.RequestId)
////	}
////	if err = mw.InsertCmsOperationRecord(requestData.RequestId, record); err != nil {
////		a.logger.Errorf("failed to insert new cms operation record, err: %v", err)
////	} else {
////		a.logger.Infof("succeeded to insert new cms operation record, device_id: %s, request_id: %s", requestData.DeviceId, requestData.RequestId)
////	}
////	return
////}
//
//func TestCMSCalculation(t *testing.T) {
//	n := 200
//	//a := newTestAlgorithm()
//	//cursor, err := a.watcher.Mongodb().Client.Database(umw.OAuthDB).Collection(umw.DeviceBaseInfo).Find(context.Background(), bson.D{}, options.Find().SetLimit(int64(n)))
//	//if err != nil {
//	//	t.Fatalf("fail to get device list, err: %v", err)
//	//}
//	//var res []umw.MongoDeviceInfo
//	//if err = cursor.All(context.Background(), &res); err != nil {
//	//	t.Fatalf("fail to parse cursor, err: %v", err)
//	//}
//	wg := sync.WaitGroup{}
//	wg.Add(n)
//	sendOne := func(deviceId string) {
//		defer wg.Done()
//
//		modelTriggerTime := time.Now().UnixMilli()
//		requestId := xid.New().String()
//		request := fmt.Sprintf(`curl https://api-welkin-backend-stg.nioint.com/algorithm/v1/grpc/cms-calculation -X POST --header "Content-Type: application/json" -d '{
//    "request_id": "%s",
//    "model_trigger_time": %d,
//    "device_id": "%s",
//    "module_type": "YFY",
//    "battery_info": [{
//            "battery_id": "P0205908AI31221V218952L12B00055",
//            "slot_id": 1,
//            "battery_type": 6,
//            "battery_capacity": 280,
//            "charging_status": 0,
//            "battery_user_soc": 3.0,
//            "battery_real_soc": 4.0,
//            "charging_current": -90.0,
//            "initial_battery_temp": 25.4
//        }, {
//            "battery_id": "P0205908AI31221V218952L12B00056",
//            "slot_id": 2,
//            "battery_type": 6,
//            "battery_capacity": 280,
//            "charging_status": 0,
//            "battery_user_soc": 3.0,
//            "battery_real_soc": 4.0,
//            "charging_current": -80.0,
//            "initial_battery_temp": 35.4
//        }, {
//            "battery_id": "P0205908AI31221V218952L12B00057",
//            "slot_id": 3,
//            "battery_type": 3,
//            "battery_capacity": 200,
//            "charging_status": 0,
//            "battery_user_soc": 14.0,
//            "battery_real_soc": 16.0,
//            "charging_current": -110.4,
//            "initial_battery_temp": 27.0
//        }, {
//            "battery_id": "P0205908AI31221V218952L12B00058",
//            "slot_id": 4,
//            "battery_type": 6,
//            "battery_capacity": 280,
//            "charging_status": 0,
//            "battery_user_soc": 2.0,
//            "battery_real_soc": 3.0,
//            "charging_current": -90.0,
//            "initial_battery_temp": 22.3
//        }, {
//            "battery_id": "P0205908AI31221V218952L12B00059",
//            "slot_id": 5,
//            "battery_type": 1,
//            "battery_capacity": 200,
//            "charging_status": 0,
//            "battery_user_soc": 34.0,
//            "battery_real_soc": 33.0,
//            "charging_current": -100.4,
//            "initial_battery_temp": 35.0
//        }, {
//            "battery_id": "P0205908AI31221V218952L12B00060",
//            "slot_id": 6,
//            "battery_type": 1,
//            "battery_capacity": 200,
//            "charging_status": 0,
//            "battery_user_soc": 45.0,
//            "battery_real_soc": 46.0,
//            "charging_current": -90.0,
//            "initial_battery_temp": 27.8
//        }, {
//            "battery_id": "P0205908AI31221V218952L12B00061",
//            "slot_id": 7,
//            "battery_type": 3,
//            "battery_capacity": 200,
//            "charging_status": 0,
//            "battery_user_soc": 60.0,
//            "battery_real_soc": 56.0,
//            "charging_current": -110.4,
//            "initial_battery_temp": 36.0
//        }, {
//            "battery_id": "P0205908AI31221V218952L12B00062",
//            "slot_id": 8,
//            "battery_type": 8,
//            "battery_capacity": 195,
//            "charging_status": 0,
//            "battery_user_soc": 10.0,
//            "battery_real_soc": 12.0,
//            "charging_current": -110.4,
//            "initial_battery_temp": 39.5
//        }, {
//            "battery_id": "P0205908AI31221V218952L12B00063",
//            "slot_id": 9,
//            "battery_type": 8,
//            "battery_capacity": 195,
//            "charging_status": 0,
//            "battery_user_soc": 66.0,
//            "battery_real_soc": 64.0,
//            "charging_current": -100.0,
//            "initial_battery_temp": 30.0
//        }, {
//            "battery_id": "P0205908AI31221V218952L12B00064",
//            "slot_id": 10,
//            "battery_type": 3,
//            "battery_capacity": 200,
//            "charging_status": 0,
//            "battery_user_soc": 20.0,
//            "battery_real_soc": 22.0,
//            "charging_current": -110.4,
//            "initial_battery_temp": 25.4
//        }, {
//            "battery_id": "P0205908AI31221V218952L12B00065",
//            "slot_id": 11,
//            "battery_type": 3,
//            "battery_capacity": 200,
//            "charging_status": 0,
//            "battery_user_soc": 3.0,
//            "battery_real_soc": 4.0,
//            "charging_current": -90.0,
//            "initial_battery_temp": 26.5
//        }, {
//            "battery_id": "P0205908AI31221V218952L12B00066",
//            "slot_id": 12,
//            "battery_type": 3,
//            "battery_capacity": 200,
//            "charging_status": 0,
//            "battery_user_soc": 3.0,
//            "battery_real_soc": 4.0,
//            "charging_current": -110.4,
//            "initial_battery_temp": 25.4
//        }, {
//            "battery_id": "P0205908AI31221V218952L12B00067",
//            "slot_id": 13,
//            "battery_type": 3,
//            "battery_capacity": 200,
//            "charging_status": 0,
//            "battery_user_soc": 1.0,
//            "battery_real_soc": 2.0,
//            "charging_current": -90.0,
//            "initial_battery_temp": 25.4
//        }
//    ]
//}'`, requestId, modelTriggerTime, deviceId)
//		cmd := exec.Command("bash", "-c", request)
//		out, err := cmd.CombinedOutput()
//		if err != nil {
//			fmt.Printf("combined out:\n%s, err: %v", string(out), err)
//			return
//		}
//		//requestData := umw.MongoCmsOperationRecord{
//		//	RequestId: xid.New().String(),
//		//	ModelTriggerTime: time.Now().UnixMilli(),
//		//	DeviceId: "PS-NIO-31da205c-64ba4b36",
//		//	ModuleType: "UU",
//		//	BatteryInfo: []umw.BatteryInfo{
//		//		{
//		//			SlotId: 1,
//		//			BatteryCapacity: 200,
//		//			BatteryId: "P0079340AT199210061100071B00037",
//		//			BatteryRealSoc: 19,
//		//			BatteryType: 3,
//		//			BatteryUserSoc: 16,
//		//			ChargingCurrent: 0,
//		//			ChargingStatus: 0,
//		//			InitialBatteryTemp: 0,
//		//		},
//		//		{
//		//			SlotId: 2,
//		//			BatteryCapacity: 200,
//		//			BatteryId: "P0079340AO301200061300001A10036",
//		//			BatteryRealSoc: 88,
//		//			BatteryType: 3,
//		//			BatteryUserSoc: 93,
//		//			ChargingCurrent: 0,
//		//			ChargingStatus: 0,
//		//			InitialBatteryTemp: 0,
//		//		},
//		//		{
//		//			SlotId: 3,
//		//			BatteryCapacity: 280,
//		//			BatteryId: "P0212542AE22621V218952L12B00152",
//		//			BatteryRealSoc: 42,
//		//			BatteryType: 6,
//		//			BatteryUserSoc: 42,
//		//			ChargingCurrent: -109.6,
//		//			ChargingStatus: 0,
//		//			InitialBatteryTemp: 55,
//		//		},
//		//		{
//		//			SlotId: 4,
//		//			BatteryCapacity: 195,
//		//			BatteryId: "P0223981BA26522V218952L15B00062",
//		//			BatteryRealSoc: 93,
//		//			BatteryType: 8,
//		//			BatteryUserSoc: 93,
//		//			ChargingCurrent: 0,
//		//			ChargingStatus: 0,
//		//			InitialBatteryTemp: 0,
//		//		},
//		//		{
//		//			SlotId: 5,
//		//			BatteryCapacity: 280,
//		//			BatteryId: "P0205908AK00922V218952L12A00193",
//		//			BatteryRealSoc: 89,
//		//			BatteryType: 6,
//		//			BatteryUserSoc: 93,
//		//			ChargingCurrent: 0,
//		//			ChargingStatus: 0,
//		//			InitialBatteryTemp: 55,
//		//		},
//		//		{
//		//			SlotId: 6,
//		//			BatteryCapacity: 200,
//		//			BatteryId: "P0079340AR05821006110X051B00118",
//		//			BatteryRealSoc: 88,
//		//			BatteryType: 3,
//		//			BatteryUserSoc: 93,
//		//			ChargingCurrent: 0,
//		//			ChargingStatus: 0,
//		//			InitialBatteryTemp: 0,
//		//		},
//		//		{
//		//			SlotId: 7,
//		//			BatteryCapacity: 200,
//		//			BatteryId: "P0079340AR081210061100071B00053",
//		//			BatteryRealSoc: 25,
//		//			BatteryType: 3,
//		//			BatteryUserSoc: 22,
//		//			ChargingCurrent: -130,
//		//			ChargingStatus: 0,
//		//			InitialBatteryTemp: 55,
//		//		},
//		//		{
//		//			SlotId: 8,
//		//			BatteryCapacity: 280,
//		//			BatteryId: "P0205908AQ13622V218952L12B00156",
//		//			BatteryRealSoc: 91,
//		//			BatteryType: 6,
//		//			BatteryUserSoc: 93,
//		//			ChargingCurrent: 0,
//		//			ChargingStatus: 1,
//		//			InitialBatteryTemp: 0,
//		//		},
//		//		{
//		//			SlotId: 9,
//		//			BatteryCapacity: 195,
//		//			BatteryId: "P0223981BD14723V218952L19B00119",
//		//			BatteryRealSoc: 97,
//		//			BatteryType: 8,
//		//			BatteryUserSoc: 93,
//		//			ChargingCurrent: 0,
//		//			ChargingStatus: 0,
//		//			InitialBatteryTemp: 0,
//		//		},
//		//		{
//		//			SlotId: 10,
//		//			BatteryCapacity: 200,
//		//			BatteryId: "P0079340AK152200061300001A00029",
//		//			BatteryRealSoc: 88,
//		//			BatteryType: 3,
//		//			BatteryUserSoc: 93,
//		//			ChargingCurrent: 0,
//		//			ChargingStatus: 0,
//		//			InitialBatteryTemp: 0,
//		//		},
//		//		{
//		//			SlotId: 11,
//		//			BatteryCapacity: 200,
//		//			BatteryId: "P0079340AD314180061300001B00044",
//		//			BatteryRealSoc: 18,
//		//			BatteryType: 1,
//		//			BatteryUserSoc: 15,
//		//			ChargingCurrent: -129.7,
//		//			ChargingStatus: 0,
//		//			InitialBatteryTemp: 55,
//		//		},
//		//		{
//		//			SlotId: 12,
//		//			BatteryCapacity: 280,
//		//			BatteryId: "P0205908AN02622V218952L12B00240",
//		//			BatteryRealSoc: 89,
//		//			BatteryType: 6,
//		//			BatteryUserSoc: 93,
//		//			ChargingCurrent: 0,
//		//			ChargingStatus: 1,
//		//			InitialBatteryTemp: 0,
//		//		},
//		//		{
//		//			SlotId: 13,
//		//			BatteryCapacity: 0,
//		//			BatteryId: "",
//		//			BatteryRealSoc: 0,
//		//			BatteryType: -1,
//		//			BatteryUserSoc: 0,
//		//			ChargingCurrent: 0,
//		//			ChargingStatus: 0,
//		//			InitialBatteryTemp: 55,
//		//		},
//		//	},
//		//}
//		//byteData, err := json.Marshal(requestData)
//		//if err != nil {
//		//	t.Fatalf("fail to marshal, err: %v", err)
//		//}
//		//var requestBody map[string]interface{}
//		//if err = json.Unmarshal(byteData, &requestBody); err != nil {
//		//	t.Fatalf("fail to unmarshal, err: %v", err)
//		//}
//		//fmt.Println(requestBody)
//		//ct := ucmd.NewHttpClient(ucmd.HttpClient{
//		//	URL: "https://api-welkin-backend-stg.nioint.com/algorithm/v1/grpc/cms-calculation",
//		//	Method: "POST",
//		//	Header: map[string]string{
//		//		"Content-Type": "application/json",
//		//	},
//		//	RequestBody: requestBody,
//		//})
//		//body, statusCode, err := ct.Do()
//		//if err != nil {
//		//	t.Fatalf("ct.Do err: %v", err)
//		//}
//		//defer body.Close()
//		//_, dErr := ioutil.ReadAll(body)
//		//if dErr != nil {
//		//	t.Fatalf("read body err: %v", err)
//		//}
//		//if statusCode != http.StatusOK {
//		//	t.Fatalf("status code not 200: %d", statusCode)
//		//}
//	}
//	for i := 0; i < n; i++ {
//		//deviceId := res[i].DeviceId
//		deviceId := "PS-NIO-31da205c-64ba4b36"
//		go sendOne(deviceId)
//		time.Sleep(time.Millisecond * 100)
//	}
//
//	wg.Wait()
//}
