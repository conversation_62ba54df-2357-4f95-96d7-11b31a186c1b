package exec

import (
	"bytes"
	"context"
	"encoding/csv"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"math"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"github.com/rs/xid"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	udao "git.nevint.com/golang-libs/common-utils/dao"
	"git.nevint.com/golang-libs/common-utils/larkservice"
	ulock "git.nevint.com/golang-libs/common-utils/lock"
	um "git.nevint.com/golang-libs/common-utils/model"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	"git.nevint.com/welkin2/welkin-backend/domain/common"
	"git.nevint.com/welkin2/welkin-backend/domain/health"
	domain_service "git.nevint.com/welkin2/welkin-backend/domain/service"
	"git.nevint.com/welkin2/welkin-backend/domain/service_event"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	mongo_model "git.nevint.com/welkin2/welkin-backend/model/mongo"
	"git.nevint.com/welkin2/welkin-backend/service"
	"git.nevint.com/welkin2/welkin-backend/util"
)

type Diagnosis interface {
	TroubleEventsList() gin.HandlerFunc
	GetSnapshotDiagnosis() gin.HandlerFunc
	UpdateSnapshotDiagnosisResult() gin.HandlerFunc
	GetCheckedParams() gin.HandlerFunc
	AlarmIdList() gin.HandlerFunc
	AlarmList() gin.HandlerFunc
	AlarmSectionAnalysis() gin.HandlerFunc
	AlarmListByDevice() gin.HandlerFunc
	RemoteOperationCommand() gin.HandlerFunc
	ListLogAnalysis() gin.HandlerFunc
	UploadAlarmLog(area string) gin.HandlerFunc
	ListProcessStatus() gin.HandlerFunc
	ListFireAlarm() gin.HandlerFunc
	ListGroot() gin.HandlerFunc
	ListGrootDevice() gin.HandlerFunc
	GetOperationLog() gin.HandlerFunc
	GetBluetoothDisconnectStatPanel() gin.HandlerFunc
	ListDeviceBluetoothDisconnectStat() gin.HandlerFunc
	GetBluetoothDisconnectAlarmInfo() gin.HandlerFunc
	GetBluetoothDisconnectAlarmDistribution() gin.HandlerFunc
	GetStuckStatPanel() gin.HandlerFunc
	ListDeviceStuckStat() gin.HandlerFunc
	GetStuckServiceStepAnalysis() gin.HandlerFunc
	ListStuckService() gin.HandlerFunc
	UpdateStuckService() gin.HandlerFunc
	ListStuckAlarmId() gin.HandlerFunc
	ListJira() gin.HandlerFunc
	ListStuckAlarmInfo() gin.HandlerFunc
	ListStuckAlarms() gin.HandlerFunc
	ListStuckAlarmDistribution() gin.HandlerFunc
	AsynExcuteHiveStuckService() gin.HandlerFunc
	AsynExcuteStuckServiceDailyStat() gin.HandlerFunc
	GetHealthWeeklyData() gin.HandlerFunc
	GetHealthTailDevices() gin.HandlerFunc
	GetHealthDetail() gin.HandlerFunc
	GetSingleDeviceHealthTrend() gin.HandlerFunc
	GetSingleDeviceServoHealth() gin.HandlerFunc
	GetSingleDeviceSensorHealth() gin.HandlerFunc
	GetSingleDeviceChargeHealth() gin.HandlerFunc
	ListHealthWorksheet() gin.HandlerFunc
	GetHealthWorksheetStatistics() gin.HandlerFunc
	ListSatisfyData() gin.HandlerFunc
	ListSatisfyDataV2() gin.HandlerFunc
	DownloadListSatisfyData() gin.HandlerFunc
	GetServiceInfoByOrderId() gin.HandlerFunc
	GetSatisfyDiagnoseResult() gin.HandlerFunc
	GetSatisfyDetail() gin.HandlerFunc
	GetSatisfySwapLog() gin.HandlerFunc
	SyncSatisfyData() gin.HandlerFunc
	ListUserTags() gin.HandlerFunc
	ListDiagnosisTags() gin.HandlerFunc
	ListLabel() gin.HandlerFunc
	GetSatisfyReport() gin.HandlerFunc
	DownloadSatisfyReport() gin.HandlerFunc
	UpdateReportStatus() gin.HandlerFunc
	AsynExcuteDiyLabel() gin.HandlerFunc
}

type diagnosis struct {
	config  *ucfg.Config
	watcher client.Watcher
	oss     service.OSS
	fms     service.FMS
	logger  *zap.SugaredLogger
}

func NewDiagnosisHandler(watcher client.Watcher, conf *ucfg.Config) Diagnosis {
	logger := log.Logger.Named(model.DIAGNOSIS)
	return &diagnosis{
		config:  conf,
		watcher: watcher,
		oss: service.OSS{
			NMP:    conf.OSS.NMPUrl,
			AppId:  conf.Sentry.AppId,
			Logger: log.Logger.Named("OSS"),
		},
		fms: service.FMS{
			URL:          conf.FMS.Url,
			AppId:        conf.Sentry.AppId,
			AppSecret:    conf.Sentry.AppSecret,
			ClientId:     conf.FMS.ClientId,
			ClientSecret: conf.FMS.ClientSecret,
			PriBucketKey: conf.FMS.PriBucketKey,
			PubBucketKey: conf.FMS.PubBucketKey,
			Header: map[string]string{
				"Content-Type": "application/json;charset=UTF-8",
			},
			Logger: log.Logger.Named("FMS"),
		},
		logger: logger,
	}
}

func (d *diagnosis) TroubleEventsList() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			uriParam model.DiagnosisListRequest
			response model.DiagnosisListResponse
		)
		if err := c.BindQuery(&uriParam); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if err := util.CheckTimeRange(uriParam.StartTime, uriParam.EndTime); err != nil {
			log.CtxLog(c).Errorf("check time range err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		util.SetURIParamDefault(&uriParam.CommonUriParam)

		project := c.Param("project")
		response = model.DiagnosisListResponse{
			Page:      uriParam.Page,
			Size:      uriParam.Size,
			StartTime: uriParam.StartTime,
			EndTime:   uriParam.EndTime,
		}

		filter := bson.D{util.SelectedTimeDuration("timestamp", uriParam.StartTime, uriParam.EndTime)}
		if uriParam.DeviceId != "" {
			filter = append(filter, bson.E{Key: "device_id", Value: uriParam.DeviceId})
		}
		if uriParam.Module != "" {
			filter = append(filter, bson.E{Key: "module", Value: uriParam.Module})
		}
		if uriParam.Type != nil {
			filter = append(filter, bson.E{Key: "type", Value: *uriParam.Type})
		}
		records, err := d.watcher.Mongodb().NewMongoEntry().ListTroubleEvents(project, filter)
		if err != nil {
			log.CtxLog(c).Errorf("get trouble events, err: %s", err.Error())
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}

		reqIdList := make([]string, 0)
		for requestId := range records {
			reqIdList = append(reqIdList, requestId)
		}
		sort.Slice(reqIdList, func(i, j int) bool { return reqIdList[i][1:] > reqIdList[j][1:] })

		var total int
		startIndex, endIndex := (response.Page-1)*response.Size, response.Page*response.Size
		for _, reqId := range reqIdList {
			for diagnosisType := range records[reqId] {
				for deviceId := range records[reqId][diagnosisType] {
					total += 1
					if total <= startIndex || total > endIndex {
						continue
					}
					ts, _ := strconv.ParseInt(reqId[len(reqId)-10:], 10, 64)
					result := strings.Split(records[reqId][diagnosisType][deviceId], "#")
					var canCauseShutdown bool
					if result[1] == "true" {
						canCauseShutdown = true
					}
					response.Data = append(response.Data, model.TroubleShootingList{
						DeviceId:         deviceId,
						RequestId:        reqId,
						Module:           result[0],
						CanCauseShutdown: canCauseShutdown,
						Timestamp:        ts,
						Type:             int(diagnosisType),
					})
				}
			}
		}

		response.Total = total
		log.CtxLog(c).Infof("succeed to get trouble events")
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) GetSnapshotDiagnosis() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			err      error
			response model.SnapShotDiagnosisResponse
		)
		deviceId := c.Param("device_id")
		reqId := c.Param("request_id")
		project := c.Param("project")

		response, err = d.watcher.Mongodb().NewMongoEntry().GetSnapshotRealtime(project, deviceId, reqId)
		if err != nil {
			log.CtxLog(c).Errorf("get snapshot diagnosis details, err: %s", err.Error())
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}

		log.CtxLog(c).Infof("succeed to get snapshot diagnosis details")
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) UpdateSnapshotDiagnosisResult() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			requestData umw.MongoSnapshotRealtime
			response    um.Base
		)
		if err := c.BindJSON(&requestData); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		project := c.Param("project")
		deviceId := c.Param("device_id")
		objId, err := primitive.ObjectIDFromHex(c.Param("_id"))
		if err != nil {
			log.CtxLog(c).Errorf("cannot parse _id to hex, device_id: %s, err: %v", deviceId, err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		err = d.watcher.Mongodb().UpdateSnapshotDiagnosisResultById(objId, project, requestData.DiagnosisResult, requestData.DeviceLog)
		if err != nil {
			log.CtxLog(c).Errorf("update snapshot diagnosis data, device_id: %s, err: %v", deviceId, err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}

		log.CtxLog(c).Infof("succeed to update snapshot diagnosis data, device_id: %s", deviceId)
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) GetCheckedParams() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.CheckedParamsResponse
		project := c.Query("project")
		deviceId := c.Query("device_id")
		if project != "" && project != umw.PowerSwap2 && project != umw.PUS3 && project != umw.PUS4 {
			log.CtxLog(c).Errorf("`project` is invalid: %s", project)
			um.FailWithBadRequest(c, &response, "`project` is invalid")
			return
		}
		if project == "" {
			project = umw.PowerSwap2
		}
		checkedParams, err := d.watcher.RbMongodb().GetCheckedParams(project, deviceId)
		if err != nil {
			log.CtxLog(c).Errorf("fail to get checked params, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Total = len(checkedParams)
		response.Data = checkedParams
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

type CrossCollection struct {
	ColName   string
	YearMonth string
}

func (d *diagnosis) AlarmList() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			uriParam model.AlarmListRequest
			response model.AddTotalResponse
		)
		if err := c.BindQuery(&uriParam); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if err := util.CheckTimeRange(uriParam.StartTime, uriParam.EndTime); err != nil {
			log.CtxLog(c).Errorf("check time range err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		util.SetURIParamDefault(&uriParam.CommonUriParam)
		if uriParam.Download {
			uriParam.Page = 1
			uriParam.Size = 99999
		}

		project := c.Param("project")
		alarmData := make(map[string]cache.DeviceAlarmDataId)
		if project != umw.PowerSwap2 && project != umw.PUS3 && project != umw.PUS4 && project != umw.PSC4 && project != umw.FYPUS1 {
			log.CtxLog(c).Errorf("`project` is invalid: %s", project)
			um.FailWithBadRequest(c, &response, "`project` is invalid")
			return
		}
		alarms := cache.DeviceAlarmInfoCache.GetAllDeviceAlarms(project)
		for _, alarm := range alarms {
			alarmData[alarm.DataId] = cache.DeviceAlarmDataId{
				DataId:     alarm.DataId,
				VarCnName:  alarm.VarCnName,
				VarEnName:  alarm.VarEnName,
				AlarmLevel: alarm.AlarmLevel,
			}
		}

		filter := bson.D{util.SelectedTimeDuration("create_ts", uriParam.StartTime, uriParam.EndTime)}
		if uriParam.DeviceId != "" {
			deviceIds := strings.Split(uriParam.DeviceId, ",")
			filter = append(filter, bson.E{Key: "device_id", Value: bson.M{"$in": deviceIds}})
		}
		if uriParam.AlarmType != nil {
			filter = append(filter, bson.E{Key: "alarm_type", Value: *uriParam.AlarmType})
		}
		if uriParam.EvBrand != "" {
			filter = append(filter, bson.E{Key: "car_brand", Value: uriParam.EvBrand})
		}
		if uriParam.EvType != "" {
			filter = append(filter, bson.E{Key: "car_model_type", Value: bson.M{"$in": strings.Split(uriParam.EvType, ",")}})
		}
		if uriParam.CarPlatform != "" {
			filter = append(filter, bson.E{Key: "car_platform", Value: uriParam.CarPlatform})
		}
		if uriParam.AlarmLevel != nil {
			dataIdList := make([]string, 0)
			for dataId, val := range alarmData {
				if val.AlarmLevel != int32(*uriParam.AlarmLevel) {
					continue
				}
				if uriParam.DataId != "" && uriParam.DataId != dataId {
					continue
				}
				dataIdList = append(dataIdList, dataId)
			}
			filter = append(filter, bson.E{Key: "data_id", Value: bson.M{"$in": dataIdList}})
		} else if uriParam.DataId != "" {
			dataIds := strings.Split(uriParam.DataId, ",")
			filter = append(filter, bson.E{Key: "data_id", Value: bson.M{"$in": dataIds}})
		}
		if uriParam.State != nil {
			filter = append(filter, bson.E{Key: "state", Value: *uriParam.State})
		}
		if uriParam.BatteryId != "" {
			filter = append(filter, bson.E{Key: "battery_id", Value: uriParam.BatteryId})
		}
		startTime := util.ConvertTime(uriParam.StartTime)
		endTime := util.ConvertTime(uriParam.EndTime)
		colNameList := make([]CrossCollection, 0)
		for year := startTime.Year(); year <= endTime.Year(); year++ {
			startMonth := 1
			endMonth := 12
			if year == startTime.Year() {
				startMonth = int(startTime.Month())
			}
			if year == endTime.Year() {
				endMonth = int(endTime.Month())
			}
			for month := startMonth; month <= endMonth; month++ {
				colNameList = append(colNameList, CrossCollection{
					ColName:   fmt.Sprintf("%s_%d", strings.ToLower(project), month),
					YearMonth: fmt.Sprintf("%d-%d", year, month),
				})
			}
		}
		if uriParam.Descending && len(colNameList) > 1 {
			sort.Slice(colNameList, func(i, j int) bool { return colNameList[i].YearMonth > colNameList[j].YearMonth })
		}
		sortVal := -1
		if !uriParam.Descending {
			sortVal = 1
		}

		records := make([]umw.MongoAlarmRecord, 0)
		globalSkip := int64((uriParam.Page - 1) * uriParam.Size)
		crossCollection := false
		for _, colName := range colNameList {
			year, _ := strconv.Atoi(colName.YearMonth[:4])
			startOfYear := time.Date(year, time.January, 1, 0, 0, 0, 0, time.Local).UnixMilli()
			endOfYear := time.Date(year+1, time.January, 1, 0, 0, 0, 0, time.Local).UnixMilli()
			newFilter := append(filter, util.SelectedTimeDuration("create_ts", startOfYear, endOfYear))
			count, err := d.watcher.Mongodb().NewMongoEntry(newFilter).Count(umw.AlarmInfo, colName.ColName)
			if err != nil {
				log.CtxLog(c).Errorf("failed to get alarm info of %s, err: %v", colName, err)
				continue
			}
			response.Total += int(count)
			if len(records) >= uriParam.Size {
				// 不能直接break，在跨多个表的时候，response.Total还需要累加后面几个表的total
				continue
			}
			// globalSkip超过这个表的记录总数，跳过该表；若跨表搜索，不能跳过
			if !crossCollection && globalSkip >= count {
				globalSkip -= count
				continue
			}
			var alarmRecords []umw.MongoAlarmRecord
			skip := globalSkip
			if crossCollection {
				skip = 0
			}
			mongoOpt := options.Find().SetSkip(skip).SetLimit(int64(uriParam.Size - len(records))).SetSort(bson.D{{"create_ts", sortVal}})
			_, err = d.watcher.Mongodb().NewMongoEntry(newFilter).FindMany(umw.AlarmInfo, colName.ColName, mongoOpt, &alarmRecords)
			if err != nil {
				log.CtxLog(c).Errorf("failed to get alarm info of %s, err: %v", colName, err)
				continue
			}
			records = append(records, alarmRecords...)
			if len(records) < uriParam.Size {
				// 在第一个表中数据没找全，说明当前页是跨表搜的
				crossCollection = true
			}
		}
		if len(records) >= uriParam.Size {
			records = records[:uriParam.Size]
		}
		lang := c.Query("lang")
		results := make([]model.AlarmInfo, 0)
		deviceCache := cache.PowerSwapCache
		if util.DeviceIsPowerCharger(project) {
			deviceCache = cache.ChargerCache
		}
		for _, item := range records {
			var dataIdDescription, deviceName string
			if lang == "en" {
				dataIdDescription = alarmData[item.DataId].VarEnName
			} else {
				dataIdDescription = alarmData[item.DataId].VarCnName
			}

			deviceInfo, exist := deviceCache.GetSingleDevice(item.DeviceId)
			if exist {
				deviceName = deviceInfo.Description
			}

			result := model.AlarmInfo{
				AlarmType:         item.AlarmType,
				DataId:            item.DataId,
				AlarmLevel:        alarmData[item.DataId].AlarmLevel,
				DataIdDescription: dataIdDescription,
				DeviceId:          item.DeviceId,
				DeviceName:        deviceName,
				CreateTS:          item.CreateTS,
				ClearTS:           item.ClearTS,
				UploadTS:          item.UploadTS,
				InsertTS:          item.InsertTS,
				BatteryId:         item.BatteryId,
				State:             item.State,
				EvBrand:           item.CarBrand,
				EvType:            item.CarModelType,
				CarPlatform:       item.CarPlatform,
			}
			// 三代站伺服告警
			for k, v := range item.Context {
				servoFault, err := ConvertServoFaultCode(umw.PUS3, k, v, lang)
				if err != nil || servoFault == nil {
					log.CtxLog(c).Warnf("invalid servo context, err: %v, id: %s, code: %v", err, k, v)
					continue
				}
				result.ServoFaultList = append(result.ServoFaultList, *servoFault)
			}
			results = append(results, result)
		}
		response.Data = results
		if !uriParam.Download {
			um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
			return
		}
		// 下载csv
		fileName := fmt.Sprintf("alarm-%s-%d.csv", project, time.Now().UnixMilli())
		csvRecords := make([][]string, len(results)+1)
		csvRecords[0] = []string{"告警描述", "告警ID", "告警类型", "告警级别", "告警状态", "告警产生时间", "告警消除时间", "设备名称", "设备ID"}
		for i, r := range results {
			csvRecords[i+1] = []string{
				r.DataIdDescription,
				r.DataId,
				model.AlarmTypeMap[r.AlarmType],
				model.AlarmLevelMap[r.AlarmLevel],
				model.AlarmStateMap[r.State],
				util.TsString(r.CreateTS),
				util.TsString(r.ClearTS),
				r.DeviceName,
				r.DeviceId,
			}
		}
		buffer := &bytes.Buffer{}
		buffer.WriteString("\xEF\xBB\xBF")
		cw := csv.NewWriter(buffer)
		if err := cw.WriteAll(csvRecords); err != nil {
			log.CtxLog(c).Errorf("fail to write to csv, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		c.Writer.Header().Set("Content-Type", "text/csv")
		util.Download(c, fileName, buffer.Bytes())
	}
}

// AlarmSectionAnalysis 不同时间区间的告警分析
func (d *diagnosis) AlarmSectionAnalysis() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.AlarmSectionAnalysisRequest
			response model.AlarmSectionAnalysisResponse
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		project := c.Param("project")
		alarmData := make(map[string]cache.DeviceAlarmDataId)
		if project != umw.PowerSwap2 && project != umw.PUS3 && project != umw.PUS4 && project != umw.PSC4 && project != umw.FYPUS1 {
			log.CtxLog(c).Errorf("`project` is invalid: %s", project)
			um.FailWithBadRequest(c, &response, "`project` is invalid")
			return
		}
		alarms := cache.DeviceAlarmInfoCache.GetAllDeviceAlarms(project)
		for _, alarm := range alarms {
			alarmData[alarm.DataId] = cache.DeviceAlarmDataId{
				DataId:     alarm.DataId,
				VarCnName:  alarm.VarCnName,
				VarEnName:  alarm.VarEnName,
				AlarmLevel: alarm.AlarmLevel,
			}
		}
		if request.Page <= 0 {
			request.Page = 1
		}
		if request.Size <= 0 {
			request.Size = 20
		}

		section1StartTimestamp := request.Section1StartTimestamp
		section1EndTimestamp := request.Section1EndTimestamp
		section2StartTimestamp := request.Section2StartTimestamp
		section2EndTimestamp := request.Section2EndTimestamp

		filter := bson.D{}
		if request.DeviceIds != "" {
			deviceIds := strings.Split(request.DeviceIds, ",")
			filter = append(filter, bson.E{"device_id", bson.M{"$in": deviceIds}})
		}
		if request.InService != nil {
			if *request.InService {
				filter = append(filter, bson.E{Key: "in_service", Value: true})
			} else {
				filter = append(filter, bson.E{Key: "in_service", Value: bson.M{"$exists": false}})
			}
		}
		filter = append(filter, bson.E{Key: "$or", Value: bson.A{
			bson.M{"create_ts": bson.M{"$gte": section1StartTimestamp, "$lte": section1EndTimestamp}},
			bson.M{"create_ts": bson.M{"$gte": section2StartTimestamp, "$lte": section2EndTimestamp}},
		}})

		sort := "section_2_dive_section_1"
		desc := -1
		if request.Sort != "" {
			sort = request.Sort
		}
		if !request.Descending {
			desc = 1
		}

		postFilter := bson.D{}
		if request.HappenTimes != nil {
			postFilter = append(postFilter, bson.E{"section_1_count", bson.M{"$gte": request.HappenTimes}})
			postFilter = append(postFilter, bson.E{"section_2_count", bson.M{"$gte": request.HappenTimes}})
		}
		if request.DataIds != "" {
			dataIds := strings.Split(request.DataIds, ",")
			postFilter = append(postFilter, bson.E{"_id", bson.M{"$in": dataIds}})
		}
		if request.AlarmLevels != "" {
			alarmLevels := strings.Split(request.AlarmLevels, "")
			alarmLevelMap := map[int64]bool{}
			dataIds := []string{}
			for _, alarmLevel := range alarmLevels {
				alarmLevelInt, err := strconv.ParseInt(alarmLevel, 10, 64)
				if err != nil {
					log.CtxLog(c).Errorf("AlarmLevels is invalid: %s", request.AlarmLevels)
					um.FailWithBadRequest(c, &response, "`project` is invalid")
					return
				}
				alarmLevelMap[alarmLevelInt] = true
			}
			for dataId, alarmDataId := range alarmData {
				_, found := alarmLevelMap[int64(alarmDataId.AlarmLevel)]
				if found {
					dataIds = append(dataIds, dataId)
				}
			}
			postFilter = append(postFilter, bson.E{"_id", bson.M{"$in": dataIds}})
		}
		cursor, err := d.watcher.Mongodb().Client.Database("alarminfo").Collection(strings.ToLower(project)).Aggregate(c, mongo.Pipeline{
			bson.D{{"$match", filter}},
			bson.D{{"$group", bson.M{
				"_id":        "$data_id",
				"alarm_type": bson.M{"$first": "$alarm_type"},
				"count":      bson.M{"$sum": 1},
				"section_1_count": bson.M{
					"$sum": bson.M{
						"$cond": bson.A{
							bson.M{
								"$and": bson.A{
									bson.M{
										"$gte": bson.A{"$create_ts", section1StartTimestamp},
									},
									bson.M{
										"$lte": bson.A{"$create_ts", section1EndTimestamp},
									},
								},
							},
							1,
							0,
						},
					},
				},
				"section_2_count": bson.M{
					"$sum": bson.M{
						"$cond": bson.A{
							bson.M{
								"$and": bson.A{
									bson.M{
										"$gte": bson.A{"$create_ts", section2StartTimestamp},
									},
									bson.M{
										"$lte": bson.A{"$create_ts", section2EndTimestamp},
									},
								},
							},
							1,
							0,
						},
					},
				},
			}}},
			bson.D{{"$project", bson.M{
				"_id":             1,
				"alarm_type":      1,
				"count":           1,
				"section_1_count": 1,
				"section_2_count": 1,
				"section_2_dive_section_1": bson.M{
					"$cond": bson.A{bson.M{"$gt": bson.A{"$section_1_count", 0}}, bson.M{"$divide": bson.A{"$section_2_count", "$section_1_count"}}, 0},
				},
			}}},
			bson.D{{"$match", postFilter}},
			bson.D{{"$sort", bson.D{{sort, desc}}}},
			bson.D{{"$skip", (request.Page - 1) * request.Size}},
			bson.D{{"$limit", request.Size}},
		})
		if err != nil {
			log.CtxLog(c).Errorf("fail to query mongo: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		totalCursor, err := d.watcher.Mongodb().Client.Database("alarminfo").Collection(strings.ToLower(project)).Aggregate(c, mongo.Pipeline{
			bson.D{{"$match", filter}},
			bson.D{{"$group", bson.M{
				"_id":        "$data_id",
				"alarm_type": bson.M{"$first": "$alarm_type"},
				"count":      bson.M{"$sum": 1},
				"section_1_count": bson.M{
					"$sum": bson.M{
						"$cond": bson.A{
							bson.M{
								"$and": bson.A{
									bson.M{
										"$gte": bson.A{"$create_ts", section1StartTimestamp},
									},
									bson.M{
										"$lte": bson.A{"$create_ts", section1EndTimestamp},
									},
								},
							},
							1,
							0,
						},
					},
				},
				"section_2_count": bson.M{
					"$sum": bson.M{
						"$cond": bson.A{
							bson.M{
								"$and": bson.A{
									bson.M{
										"$gte": bson.A{"$create_ts", section2StartTimestamp},
									},
									bson.M{
										"$lte": bson.A{"$create_ts", section2EndTimestamp},
									},
								},
							},
							1,
							0,
						},
					},
				},
			}}},
			bson.D{{"$project", bson.M{
				"_id":             1,
				"alarm_type":      1,
				"count":           1,
				"section_1_count": 1,
				"section_2_count": 1,
				"section_2_dive_section_1": bson.M{
					"$cond": bson.A{bson.M{"$gt": bson.A{"$section_1_count", 0}}, bson.M{"$divide": bson.A{"$section_2_count", "$section_1_count"}}, 0},
				},
			}}},
			bson.D{{"$match", postFilter}},
			bson.D{{"$count", "total"}},
		})
		if err != nil {
			log.CtxLog(c).Errorf("fail to query mongo: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var res []struct {
			Id                string  `json:"_id" bson:"_id"`
			AlarmType         int     `json:"alarm_type" bson:"alarm_type"`
			Count             int     `json:"count" bson:"count"`
			SectionOne        int     `json:"section_1_count" bson:"section_1_count"`
			SectionTwo        int     `json:"section_2_count" bson:"section_2_count"`
			SectionTwoDiveOne float64 `json:"section_2_dive_section_1" bson:"section_2_dive_section_1"`
		}
		if err = cursor.All(c, &res); err != nil {
			log.CtxLog(c).Errorf("fail to cursor.All: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}

		lang := c.Query("lang")
		data := []model.AlarmSectionAnalysisVO{}
		for _, value := range res {
			dataIdDescription := ""
			alarmLevel := 0
			v, found := alarmData[value.Id]
			if found {
				dataIdDescription = v.VarCnName
				alarmLevel = int(v.AlarmLevel)
			}

			if lang == "en" {
				dataIdDescription = alarmData[value.Id].VarEnName
			} else {
				dataIdDescription = alarmData[value.Id].VarCnName
			}
			data = append(data, model.AlarmSectionAnalysisVO{
				DataId:               value.Id,
				DataIdDescription:    dataIdDescription,
				AlarmLevel:           alarmLevel,
				AlarmType:            value.AlarmType,
				Section1Count:        value.SectionOne,
				Section2Count:        value.SectionTwo,
				Section2DiveSection1: value.SectionTwoDiveOne,
			})
		}
		response.Data = data
		var total []AggregateTotalRes
		if err = totalCursor.All(c, &total); err != nil {
			log.CtxLog(c).Errorf("fail to cursor.All: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if len(total) == 1 {
			response.Total = total[0].Total
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) AlarmListByDevice() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			uriParam model.AlarmListRequest
			response model.AddTotalResponse
		)
		if err := c.BindQuery(&uriParam); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if err := util.CheckTimeRange(uriParam.StartTime, uriParam.EndTime); err != nil {
			log.CtxLog(c).Errorf("check time range err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		util.SetURIParamDefault(&uriParam.CommonUriParam)
		if uriParam.Download {
			uriParam.Page = 1
			uriParam.Size = 99999
		}

		project := c.Param("project")
		alarmData := make(map[string]cache.DeviceAlarmDataId)
		if project != umw.PowerSwap2 && project != umw.PUS3 && project != umw.PUS4 && project != umw.PSC4 && project != umw.FYPUS1 {
			log.CtxLog(c).Errorf("`project` is invalid: %s", project)
			um.FailWithBadRequest(c, &response, "`project` is invalid")
			return
		}
		alarms := cache.DeviceAlarmInfoCache.GetAllDeviceAlarms(project)
		for _, alarm := range alarms {
			alarmData[alarm.DataId] = cache.DeviceAlarmDataId{
				DataId:     alarm.DataId,
				VarCnName:  alarm.VarCnName,
				VarEnName:  alarm.VarEnName,
				AlarmLevel: alarm.AlarmLevel,
			}
		}
		filter := bson.D{bson.E{Key: "device_id", Value: c.Param("device_id")},
			util.SelectedTimeDuration("create_ts", uriParam.StartTime, uriParam.EndTime)}
		if uriParam.AlarmType != nil {
			filter = append(filter, bson.E{Key: "alarm_type", Value: *uriParam.AlarmType})
		}
		if uriParam.AlarmLevel != nil {
			dataIdList := make([]string, 0)
			for dataId, val := range alarmData {
				if val.AlarmLevel != int32(*uriParam.AlarmLevel) {
					continue
				}
				if uriParam.DataId != "" && uriParam.DataId != dataId {
					continue
				}
				dataIdList = append(dataIdList, dataId)
			}
			filter = append(filter, bson.E{Key: "data_id", Value: bson.M{"$in": dataIdList}})
		} else if uriParam.DataId != "" {
			dataIds := strings.Split(uriParam.DataId, ",")
			filter = append(filter, bson.E{Key: "data_id", Value: bson.M{"$in": dataIds}})
		}
		if uriParam.State != nil {
			filter = append(filter, bson.E{Key: "state", Value: *uriParam.State})
		}
		if uriParam.BatteryId != "" {
			filter = append(filter, bson.E{Key: "battery_id", Value: uriParam.BatteryId})
		}

		startTime := util.ConvertTime(uriParam.StartTime)
		endTime := util.ConvertTime(uriParam.EndTime)
		colNameList := make([]CrossCollection, 0)
		for year := startTime.Year(); year <= endTime.Year(); year++ {
			startMonth := 1
			endMonth := 12
			if year == startTime.Year() {
				startMonth = int(startTime.Month())
			}
			if year == endTime.Year() {
				endMonth = int(endTime.Month())
			}
			for month := startMonth; month <= endMonth; month++ {
				colNameList = append(colNameList, CrossCollection{
					ColName:   fmt.Sprintf("%s_%d", strings.ToLower(project), month),
					YearMonth: fmt.Sprintf("%d-%d", year, month),
				})
			}
		}
		if uriParam.Descending && len(colNameList) > 1 {
			sort.Slice(colNameList, func(i, j int) bool { return colNameList[i].YearMonth > colNameList[j].YearMonth })
		}
		sortVal := -1
		if !uriParam.Descending {
			sortVal = 1
		}
		if cache.StuckAlarmInfoCache == nil {
			log.CtxLog(c).Errorf("list alarm by device, get empty cache")
			um.FailWithInternalServerError(c, &response, "list alarm by device, get empty cache")
			return
		}

		records := make([]umw.MongoAlarmRecord, 0)
		globalSkip := int64((uriParam.Page - 1) * uriParam.Size)
		crossCollection := false
		for _, colName := range colNameList {
			year, _ := strconv.Atoi(colName.YearMonth[:4])
			startOfYear := time.Date(year, time.January, 1, 0, 0, 0, 0, time.Local).UnixMilli()
			endOfYear := time.Date(year+1, time.January, 1, 0, 0, 0, 0, time.Local).UnixMilli()
			newFilter := append(filter, util.SelectedTimeDuration("create_ts", startOfYear, endOfYear))
			count, err := d.watcher.Mongodb().NewMongoEntry(newFilter).Count(umw.AlarmInfo, colName.ColName)
			if err != nil {
				log.CtxLog(c).Errorf("failed to get alarm info of %s, err: %v", colName, err)
				continue
			}
			response.Total += int(count)
			if len(records) >= uriParam.Size {
				// 不能直接break，在跨多个表的时候，response.Total还需要累加后面几个表的total
				continue
			}
			// globalSkip超过这个表的记录总数，跳过该表；若跨表搜索，不能跳过
			if !crossCollection && globalSkip >= count {
				globalSkip -= count
				continue
			}
			var alarmRecords []umw.MongoAlarmRecord
			skip := globalSkip
			if crossCollection {
				skip = 0
			}
			mongoOpt := options.Find().SetSkip(skip).SetLimit(int64(uriParam.Size - len(records))).SetSort(bson.D{{"create_ts", sortVal}})
			_, err = d.watcher.Mongodb().NewMongoEntry(newFilter).FindMany(umw.AlarmInfo, colName.ColName, mongoOpt, &alarmRecords)
			if err != nil {
				log.CtxLog(c).Errorf("failed to get alarm info of %s, err: %v", colName, err)
				continue
			}
			records = append(records, alarmRecords...)
			if len(records) < uriParam.Size {
				// 在第一个表中数据没找全，说明当前页是跨表搜的
				crossCollection = true
			}
		}
		if len(records) >= uriParam.Size {
			records = records[:uriParam.Size]
		}
		results := make([]model.AlarmInfo, 0)
		for _, item := range records {
			var dataIdDescription string
			if uriParam.Lang == "en" && alarmData[item.DataId].VarEnName != "" {
				dataIdDescription = alarmData[item.DataId].VarEnName
			} else {
				dataIdDescription = alarmData[item.DataId].VarCnName
			}
			_, found := cache.StuckAlarmInfoCache.GetSingleStuckAlarm(project, item.DataId)
			result := model.AlarmInfo{
				AlarmType:         item.AlarmType,
				DataId:            item.DataId,
				AlarmLevel:        alarmData[item.DataId].AlarmLevel,
				DataIdDescription: dataIdDescription,
				CreateTS:          item.CreateTS,
				ClearTS:           item.ClearTS,
				UploadTS:          item.UploadTS,
				InsertTS:          item.InsertTS,
				BatteryId:         item.BatteryId,
				State:             item.State,
				IsStuck:           found,
			}
			// 三代站伺服告警
			for k, v := range item.Context {
				servoFault, err := ConvertServoFaultCode(umw.PUS3, k, v, uriParam.Lang)
				if err != nil || servoFault == nil {
					log.CtxLog(c).Warnf("invalid servo context, err: %v, id: %s, code: %v", err, k, v)
					continue
				}
				result.ServoFaultList = append(result.ServoFaultList, *servoFault)
			}
			results = append(results, result)
		}
		response.Data = results
		if !uriParam.Download {
			um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
			return
		}
		// 下载csv
		fileName := fmt.Sprintf("alarm-%s-%s-%d.csv", project, c.Param("device_id"), time.Now().UnixMilli())
		csvRecords := make([][]string, len(results)+1)
		csvRecords[0] = []string{"告警描述", "告警ID", "告警类型", "告警级别", "告警状态", "告警产生时间", "告警消除时间"}
		for i, r := range results {
			csvRecords[i+1] = []string{
				r.DataIdDescription,
				r.DataId,
				model.AlarmTypeMap[r.AlarmType],
				model.AlarmLevelMap[r.AlarmLevel],
				model.AlarmStateMap[r.State],
				util.TsString(r.CreateTS),
				util.TsString(r.ClearTS),
			}
		}
		buffer := &bytes.Buffer{}
		buffer.WriteString("\xEF\xBB\xBF")
		cw := csv.NewWriter(buffer)
		if err := cw.WriteAll(csvRecords); err != nil {
			log.CtxLog(c).Errorf("fail to write to csv, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		c.Writer.Header().Set("Content-Type", "text/csv")
		util.Download(c, fileName, buffer.Bytes())
	}
}

// ConvertServoFaultCode 转换伺服故障码
func ConvertServoFaultCode(project, id string, code interface{}, lang string) (servoFault *model.ServoFault, err error) {
	if _, found := model.ServoFaultPUS3[id]; project == umw.PUS3 && !found {
		return nil, fmt.Errorf("pus3 id %s is not servo fault id", id)
	}
	if _, found := model.ServoFaultPS2[id]; project == umw.PowerSwap2 && !found {
		return nil, fmt.Errorf("powerswap2 id %s is not servo fault id", id)
	}
	codeStr, ok := code.(string)
	if !ok {
		return nil, fmt.Errorf("error code type, expect string, get %T", code)
	}
	servoFault = &model.ServoFault{
		Id:   id,
		Code: codeStr,
	}
	if codeStr == "0" {
		servoFault.RealCode = "0"
		return
	}
	codeInt, err := strconv.ParseInt(codeStr, 10, 64)
	if err != nil {
		return
	}
	hex := strings.ToUpper(strconv.FormatInt(codeInt, 16))
	if len(hex) == 3 {
		hex = "0" + hex
	}
	if len(hex) != 4 {
		return servoFault, fmt.Errorf("unexpected hex len: %s", hex)
	}
	servoFault.RealCode = fmt.Sprintf("E%s.%s", hex[1:], hex[0:1])
	// todo:国际化
	lang = "zh"
	servoFault.CodeName = model.ServoFaultMap[lang][servoFault.RealCode]
	return
}

func (d *diagnosis) AlarmIdList() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.AddTotalResponse
		)
		project := c.Param("project")

		alarmData := make(map[string]cache.DeviceAlarmDataId)
		if project != umw.PowerSwap2 && project != umw.PUS3 && project != umw.PUS4 && project != umw.PSC4 && project != umw.FYPUS1 {
			log.CtxLog(c).Errorf("`project` is invalid: %s", project)
			um.FailWithBadRequest(c, &response, "`project` is invalid")
			return
		}
		alarms := cache.DeviceAlarmInfoCache.GetAllDeviceAlarms(project)
		alarmDataList := make([]string, 0)
		for _, alarm := range alarms {
			alarmData[alarm.DataId] = cache.DeviceAlarmDataId{
				DataId:     alarm.DataId,
				VarCnName:  alarm.VarCnName,
				VarEnName:  alarm.VarEnName,
				AlarmLevel: alarm.AlarmLevel,
			}
			alarmDataList = append(alarmDataList, alarm.DataId)
		}

		results := make(map[string]string)
		description := c.Query("description")
		dataIdsStr := c.Query("data_ids")
		dataIds := []string{}
		if dataIdsStr != "" {
			dataIds = strings.Split(dataIdsStr, ",")
		}
		dataIdMap := map[string]bool{}
		for _, dataId := range dataIds {
			dataIdMap[dataId] = true
		}
		if description != "" {
			mu := sync.Mutex{}
			execFunc := func(i int) {
				mu.Lock()
				if c.Query("lang") == "en" {
					if strings.Contains(alarmData[alarmDataList[i]].VarEnName, description) || strings.Contains(alarmData[alarmDataList[i]].DataId, description) {
						results[alarmData[alarmDataList[i]].DataId] = alarmData[alarmDataList[i]].VarEnName
					}
				} else {
					if strings.Contains(alarmData[alarmDataList[i]].VarCnName, description) || strings.Contains(alarmData[alarmDataList[i]].DataId, description) {
						results[alarmData[alarmDataList[i]].DataId] = alarmData[alarmDataList[i]].VarCnName
					}
				}
				mu.Unlock()
			}
			ucmd.ParallelizeExec(len(alarmDataList), execFunc)
		}
		for dataId, value := range alarmData {
			_, found := dataIdMap[dataId]
			if found {
				if c.Query("lang") == "en" {
					results[dataId] = value.VarEnName
				} else {
					results[dataId] = value.VarCnName
				}
			}
		}
		response.Total = len(results)
		response.Data = results
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) RemoteOperationCommand() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response um.Base
		)
		requestData := struct {
			Project   string `json:"project" binding:"required"`
			Key       string `json:"key" binding:"required"`
			DeviceId  string `json:"device_id" binding:"required"`
			Events    []int  `json:"events" binding:"required"`
			Type      int    `json:"type" binding:"required"`
			StartTime int64  `json:"start_time" binding:"required"`
			EndTime   int64  `json:"end_time" binding:"required"`
		}{}
		if err := c.BindJSON(&requestData); err != nil {
			log.CtxLog(c).Errorf("failed to parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		requestId := xid.New().String()
		err := d.oss.IssueCommand(30002, requestData.DeviceId, requestId, requestData.Key, map[string]interface{}{
			"events":     requestData.Events,
			"type":       requestData.Type,
			"start_time": requestData.StartTime,
			"end_time":   requestData.EndTime,
		})

		update := bson.M{
			"request_id":            requestId,
			"project":               requestData.Project,
			"device_id":             requestData.DeviceId,
			"events":                requestData.Events,
			"type":                  requestData.Type,
			"strategy_push_code":    -1, // -1: 推送中，0: 推送成功，1：推送失败
			"strategy_push_message": "",
			"strategy_push_time":    time.Now().UnixMilli(),
			"query_start_time":      requestData.StartTime,
			"query_end_time":        requestData.EndTime,
		}
		if err != nil {
			update["strategy_push_code"] = 1
			update["strategy_push_message"] = err.Error()
		}
		go d.watcher.Mongodb().NewMongoEntry(bson.D{bson.E{Key: "request_id", Value: requestId}}).UpdateOne(
			"diagnosis-management", "log-analysis", bson.M{"$set": update}, true, []client.IndexOption{
				{
					Name: "request_id",
					Fields: bson.D{
						{"request_id", 1},
					},
					Unique: true,
				},
				{
					Name: "strategy_push_time",
					Fields: bson.D{
						{"strategy_push_time", -1},
					},
				},
			}...)
		if err != nil {
			log.CtxLog(c).Errorf("failed to issue command to station, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (d *diagnosis) ListLogAnalysis() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.AddTotalResponse
		)
		project := c.Param("project")
		if project != umw.PowerSwap2 && project != umw.PUS3 {
			log.CtxLog(c).Errorf("`project` is invalid: %s", project)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("`project`: %s is invalid", project))
			return
		}
		uriParam := struct {
			model.CommonUriInTimeRangeParam
			DeviceId string `form:"device_id" json:"device_id" uri:"device_id"`
		}{}
		if err := c.BindQuery(&uriParam); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		filter := bson.D{}
		if uriParam.StartTime != 0 || uriParam.EndTime != 0 {
			filter = bson.D{util.SelectedTimeDuration("strategy_push_time", uriParam.StartTime, uriParam.EndTime)}
		}
		if uriParam.DeviceId != "" {
			filter = append(filter, bson.E{Key: "device_id", Value: uriParam.DeviceId})
		}
		filter = append(filter, bson.E{Key: "project", Value: project})
		byteData, total, err := d.watcher.Mongodb().NewMongoEntry(filter).ListByPagination("diagnosis-management", "log-analysis",
			client.Pagination{Limit: int64(uriParam.Size), Offset: int64((uriParam.Page - 1) * uriParam.Size)},
			client.Ordered{Key: "strategy_push_time", Descending: uriParam.Descending})
		if err != nil {
			log.CtxLog(c).Errorf("failed to get log analysis records, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var records []struct {
			DeviceId    string `json:"device_id" bson:"device_id"`
			Description string `json:"description" bson:"-"`
			Events      []int  `json:"events" bson:"events"`
			PushCode    int    `json:"strategy_push_code" bson:"strategy_push_code"`
			PushTime    int64  `json:"strategy_push_time" bson:"strategy_push_time"`
			PushMessage string `json:"strategy_push_message" bson:"strategy_push_message"`
			Logs        []struct {
				Logtime string `json:"logtime" bson:"logtime"`
				Log     string `json:"log" bson:"log"`
			} `json:"logs"`
		}
		if err = json.Unmarshal(byteData, &records); err != nil {
			log.CtxLog(c).Errorf("failed to unmarshal log analysis records, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}

		mu := sync.Mutex{}
		devicesMap := make(map[string]client.Tags)
		readDeviceDescription := func(i int) {
			if v, has := devicesMap[records[i].DeviceId]; !has {
				conn := udao.NewRedisConn(d.watcher.Redis())
				tag, err := client.GetDeviceTag(conn, d.watcher.Mongodb(), records[i].DeviceId)
				conn.Close()
				if err != nil {
					log.CtxLog(c).Errorf("cannot find the device %s description, err: %v", records[i].DeviceId, err)
				} else {
					records[i].Description = tag.Description
					mu.Lock()
					devicesMap[records[i].DeviceId] = tag
					mu.Unlock()
				}
			} else {
				records[i].Description = v.Description
			}
		}
		ucmd.ParallelizeExec(len(records), readDeviceDescription, 500)
		response.Data = records
		response.Total = int(total)
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (d *diagnosis) UploadAlarmLog(area string) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.AlarmLogResponse
		)
		project := c.Param("project")
		if project != umw.PowerSwap2 && project != umw.PUS3 && project != umw.PUS4 {
			log.CtxLog(c).Errorf("upload alarm log, `project`: %s is invalid", project)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("`project`: %s is invalid", project))
			return
		}
		deviceId := c.Param("device_id")
		md5 := c.PostForm("md5")
		total := c.PostForm("total")
		partId := c.PostForm("part_id")
		alarmTs := c.PostForm("alarm_ts")
		if deviceId == "" || md5 == "" {
			msg := fmt.Sprintf("upload alarm log, lack required parameters, device_id: %s, md5: %s", deviceId, md5)
			log.CtxLog(c).Errorf(msg)
			um.FailWithBadRequest(c, &response, msg)
			return
		}
		partIdInt, err := strconv.Atoi(partId)
		if err != nil {
			log.CtxLog(c).Errorf("upload alarm log, fail to parse part_id: %s, device_id: %s, err: %v", partId, deviceId, err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		totalInt, err := strconv.Atoi(total)
		if err != nil {
			log.CtxLog(c).Errorf("upload alarm log, fail to parse total: %s, device_id: %s, err: %v", total, deviceId, err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		tsInt64, err := strconv.ParseInt(alarmTs, 10, 64)
		if err != nil {
			log.CtxLog(c).Errorf("upload alarm log, fail to parse alarm_ts: %s, device_id: %s, err: %v", alarmTs, deviceId, err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		request := model.AlarmLogRequest{
			Md5:     md5,
			Total:   totalInt,
			PartId:  partIdInt,
			AlarmTs: tsInt64,
		}
		response.PartId = request.PartId

		// 获取飞书通知人列表
		dbName, collectionName := umw.DiagnosisManagement, umw.FireAlarm
		rawData, err := d.watcher.Mongodb().NewMongoEntry(bson.D{{"config", true}}).GetOne(dbName, collectionName)
		var notifyList struct {
			PowerSwap2 []string `bson:"powerswap2"`
			PUS3       []string `bson:"pus3"`
			PUS4       []string `bson:"pus4"`
			Dev        []string `bson:"dev"`
		}
		if err != nil {
			log.CtxLog(c).Errorf("upload alarm log, fail to get lark user, device_id: %s, request: %+v, err: %v", deviceId, request, err)
		} else {
			if rawData == nil {
				log.CtxLog(c).Errorf("upload alarm log, empty lark user, device_id: %s, request: %+v", deviceId, request)
			} else {
				if err = bson.Unmarshal(rawData, &notifyList); err != nil {
					log.CtxLog(c).Errorf("upload alarm log, fail to unmarshal notify list, device_id: %s, request: %+v, err: %v", deviceId, request, err)
				}
			}
		}
		frontUrl := fmt.Sprintf("%s/fault-diagnosis/fire-alarm?device_id=%s", d.config.Welkin.FrontendUrl, deviceId)
		larkNotify := func(fail bool, msg string, notify []string, url ...string) {
			if len(notify) > 0 {
				receiver := larkservice.Receiver{
					Type:       larkim.ReceiveIdTypeEmail,
					ReceiveIds: notify,
				}
				params := larkservice.FireAlarmParams{
					DeviceId: deviceId,
					Project:  project,
					AlarmTs:  request.AlarmTs,
					Total:    request.Total,
					ErrorMsg: msg,
				}
				if len(url) > 0 {
					params.ShowUrl = true
					params.Url = url[0]
				}
				cardContent, cardErr := larkservice.NewInfoCard().MakeFireAlarm(params).Build()
				if cardErr != nil {
					log.CtxLog(c).Errorf("upload alarm log, make card err: %s", err.Error())
				} else {
					if err = larkservice.SendCard(cardContent, receiver); err != nil {
						log.CtxLog(c).Errorf("upload alarm log, send card err: %s", err.Error())
					}
				}
			}

			if fail {
				log.CtxLog(c).Errorf(msg)
				um.FailWithInternalServerError(c, &response, msg)
			}
		}
		// 第一个抢到锁的上传进程，发送飞书通知，并启动30分钟计时
		go func() {
			expire := 30 * 60
			workFunc := func() error {
				var notify []string
				switch project {
				case umw.PowerSwap2:
					notify = notifyList.PowerSwap2
				case umw.PUS3:
					notify = notifyList.PUS3
				case umw.PUS4:
					notify = notifyList.PUS4
				}
				jumpUrl := fmt.Sprintf("%s&start_time=%d&end_time=%d", frontUrl, request.AlarmTs-10000, request.AlarmTs+10000)
				larkNotify(false, fmt.Sprintf("【%s】开始上传消防告警日志，当前上传分片：%d", project, request.PartId), notify, jumpUrl)
				ctx, cancel := context.WithTimeout(context.Background(), time.Duration(expire*10e8))
				defer cancel()
				ticker := time.NewTicker(time.Second * 10)
				defer ticker.Stop()
				filter := bson.D{{"device_id", deviceId}, {"alarm_ts", request.AlarmTs}}
				for {
					select {
					case <-ctx.Done():
						byteData, lErr := d.watcher.Mongodb().NewMongoEntry(filter).ListAll(dbName, collectionName, client.Ordered{
							Key:        "part_id",
							Descending: false,
						})
						var remainParts []string
						if lErr != nil {
							log.CtxLog(c).Errorf("upload alarm log, fail to list records, filter: %+v, request: %+v, err: %v", filter, request, lErr)
						} else {
							var records []umw.MongoFireAlarm
							if uErr := json.Unmarshal(byteData, &records); uErr != nil {
								log.CtxLog(c).Errorf("upload alarm log, fail to unmarshal record, filter: %+v, request: %+v, err: %v", filter, request, lErr)
							} else {
								i := 0
								for expected := 0; expected < request.Total; expected++ {
									if i >= len(records) || records[i].PartId != expected {
										remainParts = append(remainParts, fmt.Sprintf("%d", expected))
									} else {
										i++
									}
								}
							}
						}
						larkNotify(false, fmt.Sprintf("【%s】消防告警日志在30分钟内未全部上传成功，未上传分片：%s", project, strings.Join(remainParts, "、")), notify, jumpUrl)
						return nil
					case <-ticker.C:
						cnt, cErr := d.watcher.Mongodb().NewMongoEntry(filter).Count(dbName, collectionName)
						if cErr != nil {
							log.CtxLog(c).Errorf("upload alarm log, fail to count records, filter: %+v, request: %+v, err: %v", filter, request, cErr)
							continue
						}
						if int(cnt) == request.Total {
							larkNotify(false, fmt.Sprintf("【%s】消防告警日志上传成功，请前往天宫平台查看", project), notify, jumpUrl)
							return nil
						}
					}
				}
			}
			if err = ulock.EnsureOnce(d.watcher.Redis(), fmt.Sprintf("%s_%d", deviceId, request.AlarmTs), workFunc, expire); err != nil {
				if errors.Is(err, um.LockFail) {
					log.CtxLog(c).Warnf("upload alarm log, %v", err)
				} else {
					log.CtxLog(c).Errorf("upload alarm log, fail to do with redis lock, err: %v", err)
				}
			}
		}()

		f, fh, err := c.Request.FormFile("file")
		if err != nil {
			log.CtxLog(c).Errorf("upload alarm log, fail to get form file, project: %s, device_id: %s, request: %+v, err: %v", project, deviceId, request, err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if err = util.CheckFileMD5(fh, request.Md5); err != nil {
			log.CtxLog(c).Errorf("upload alarm log, fail to check md5, project: %s, device_id: %s, request: %+v, err: %v", project, deviceId, request, err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		buffer := bytes.NewBuffer(nil)
		if _, err = io.Copy(buffer, f); err != nil {
			larkNotify(true, fmt.Sprintf("upload alarm log, fail to write buffer, project: %s, device_id: %s, request: %+v, err: %s", project, deviceId, request, err.Error()), notifyList.Dev)
			return
		}
		date := strings.Split(util.DecodeTime(time.UnixMilli(request.AlarmTs)), " ")
		fmsFileDir := fmt.Sprintf("/fire-alarm/%s/%s/%s/", project, deviceId, date[0])
		fmsFileName := fmt.Sprintf("%d-%s", request.PartId, fh.Filename)
		tokenRes, tokenErr := d.fms.GetFileUploadToken(fmsFileDir, fmsFileName, model.FILE, buffer.String(), area)
		if tokenErr != nil {
			larkNotify(true, fmt.Sprintf("upload alarm log, fail to get fms upload token, file dir: %s, file name: %s, request: %+v, err: %v", fmsFileDir, fmsFileName, request, tokenErr), notifyList.Dev)
			return
		}
		rd := tokenRes.ResultData
		rd.SupplierHttp.Header["Content-Type"] = "application/octet-stream"
		if err = d.fms.UploadFile(rd.SupplierHttp.URL, rd.SupplierHttp.Header, buffer); err != nil {
			larkNotify(true, fmt.Sprintf("upload alarm log, fail to upload file, file dir: %s, file name: %s, request: %+v, err: %v", fmsFileDir, fmsFileName, request, err), notifyList.Dev)
			return
		}
		fireAlarm := umw.MongoFireAlarm{
			DeviceId:   deviceId,
			AlarmTs:    request.AlarmTs,
			Project:    project,
			Md5:        request.Md5,
			PartId:     request.PartId,
			TotalParts: request.Total,
			InsertTs:   time.Now().UnixMilli(),
		}
		for _, item := range rd.DomainInfoList {
			if item.DomainAttr.CDN {
				fireAlarm.FileUrl = fmt.Sprintf("%s://%s/%s", item.Protocol, item.Domain, rd.FileKey)
				break
			}
		}
		filter := bson.D{
			{"alarm_ts", request.AlarmTs},
			{"device_id", deviceId},
			{"part_id", request.PartId},
		}
		if err = d.watcher.Mongodb().NewMongoEntry(filter).ReplaceOne(dbName, collectionName, fireAlarm, true, []client.IndexOption{
			{
				Name: "ts_deviceId_part_unique",
				Fields: bson.D{
					{"alarm_ts", -1},
					{"device_id", 1},
					{"part_id", 1},
				},
				Unique: true,
			},
		}...); err != nil {
			msg := fmt.Sprintf("upload alarm log, fail to write mongo, filter: %v, record: %v, err: %v", filter, fireAlarm, err)
			log.CtxLog(c).Errorf(msg)
			larkNotify(false, msg, notifyList.Dev)
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) ListProcessStatus() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.ProcessStatusRequest
			response model.ProcessStatusResponse
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if request.Page == 0 {
			request.Page = 1
		}
		if request.Size == 0 {
			request.Size = 10
		}
		filter := bson.D{{"config", bson.M{"$ne": true}}}
		if request.Project != nil {
			filter = append(filter, bson.E{Key: "project", Value: *request.Project})
		}
		if request.DeviceId != nil {
			filter = append(filter, bson.E{Key: "device_id", Value: *request.DeviceId})
		}
		if request.StartTime != 0 && request.EndTime != 0 {
			if err := util.CheckTimeRange(request.StartTime, request.EndTime); err != nil {
				log.CtxLog(c).Errorf("check time range err: %v, request: %+v", err, request)
				um.FailWithBadRequest(c, &response, err.Error())
				return
			}
			filter = append(filter, bson.E{Key: "ts", Value: bson.M{"$gte": request.StartTime, "$lt": request.EndTime}})
		}
		if request.Reason != nil {
			filter = append(filter, bson.E{Key: "reason", Value: *request.Reason})
		}
		if request.Level != nil {
			filter = append(filter, bson.E{Key: "level", Value: *request.Level})
		}
		if request.ProcessId != nil {
			filter = append(filter, bson.E{Key: "process_id", Value: bson.M{"$regex": fmt.Sprintf(".*%s.*", *request.ProcessId)}})
		}
		if request.ProcessName != nil {
			filter = append(filter, bson.E{Key: "process_name", Value: bson.M{"$regex": fmt.Sprintf(".*%s.*", *request.ProcessName)}})
		}

		byteData, cnt, err := d.watcher.Mongodb().NewMongoEntry(filter).ListByPagination(
			umw.DiagnosisManagement,
			umw.ProcessStatus,
			client.Pagination{
				Limit:  int64(request.Size),
				Offset: int64((request.Page - 1) * request.Size),
			},
			client.Ordered{
				Key:        "ts",
				Descending: true,
			},
		)
		if err != nil {
			log.CtxLog(c).Errorf("fail to list process status records, err: %v, filter: %+v", err, filter)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var records []umw.MongoProcessStatus
		if err = json.Unmarshal(byteData, &records); err != nil {
			log.CtxLog(c).Errorf("fail to unmarshal process status records, err: %v, filter: %+v, data: %s", err, filter, string(byteData))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Total = cnt
		for _, r := range records {
			var description string
			if r.DeviceId != "" {
				conn := udao.NewRedisConn(d.watcher.Redis())
				tags, gErr := client.GetDeviceTag(conn, d.watcher.Mongodb(), r.DeviceId)
				conn.Close()
				if gErr != nil {
					log.CtxLog(c).Errorf("cannot find the device name from mongo, device_id: %s, err: %v", r.DeviceId, gErr)
				} else {
					description = tags.Description
				}
			}
			response.Data = append(response.Data, model.ProcessStatusData{
				DeviceId:    r.DeviceId,
				Description: description,
				Project:     r.Project,
				Module:      r.From,
				ProcessId:   r.ProcessId,
				ProcessName: r.ProcessName,
				Ts:          r.Ts,
				Reason:      r.Reason,
				Level:       r.Level,
			})
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) ListFireAlarm() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.FireAlarmRequest
			response model.FireAlarmResponse
		)
		origins := d.config.Websocket.Origins
		upgrader := websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				origin := r.Header.Get("Origin")
				for _, allowOrigin := range origins {
					if allowOrigin == origin {
						return true
					}
				}
				return false
			},
		}
		conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
		if err != nil {
			log.CtxLog(c).Errorf("upgrade to websocket err: %s", err.Error())
			return
		}
		ws := ucmd.NewWsClient(conn)
		go ws.Send()
		go ws.Receive()
		if err = c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("parse query parameters, err: %v", err)
			if err = ws.FailMessage(&response, err.Error(), 1); err != nil {
				log.CtxLog(c).Error(err)
			}
			return
		}
		var page, size int
		if request.Page == 0 {
			page = 1
		} else {
			page = request.Page
		}
		if request.Size == 0 {
			size = 10
		} else {
			size = request.Size
		}

		mu := sync.Mutex{}
		updateNow := make(chan struct{})
		go func() {
			for {
				select {
				case <-ws.Done():
					return
				default:
					data, wsErr := ws.ReceiveJson()
					if wsErr != nil {
						if wsErr = ws.FailMessage(&response, fmt.Sprintf("receive json message err: %v", data), 1); wsErr != nil {
							log.CtxLog(c).Error(wsErr)
						}
						return
					}
					var msg model.CommonUriParam
					if err = json.Unmarshal(data, &msg); err != nil {
						if err = ws.FailMessage(&response, fmt.Sprintf("parse message err: %v", data), 1); err != nil {
							log.CtxLog(c).Error(err)
						}
						return
					}
					if msg.Page <= 0 {
						msg.Page = 1
					}
					if msg.Size <= 0 {
						msg.Size = 10
					}
					if msg.Page != page || msg.Size != size {
						updateNow <- struct{}{}
					}
					mu.Lock()
					page = msg.Page
					size = msg.Size
					mu.Unlock()
				}
			}
		}()

		ticker := time.NewTicker(time.Second * 5)
		defer ticker.Stop()
		for {
			if err = util.CheckTimeRange(request.StartTime, request.EndTime); err != nil {
				log.CtxLog(c).Errorf("request ts err: %s", err.Error())
				if err = ws.FailMessage(&response, err.Error(), 1); err != nil {
					log.CtxLog(c).Error(err)
				}
				return
			}
			filter := bson.D{{"alarm_ts", bson.M{"$gte": request.StartTime, "$lt": request.EndTime}}}
			if request.Project != nil {
				filter = append(filter, bson.E{Key: "project", Value: *request.Project})
			}
			if request.DeviceId != nil {
				filter = append(filter, bson.E{Key: "device_id", Value: *request.DeviceId})
			}
			response, err = d.watcher.Mongodb().NewMongoEntry().ListFireAlarmRecords(
				filter,
				client.Pagination{
					Limit:  int64(size),
					Offset: int64((page - 1) * size),
				},
				client.Ordered{
					Key:        "alarm_ts",
					Descending: true,
				},
			)
			for i, record := range response.Data {
				redisConnection := udao.NewRedisConn(d.watcher.Redis())
				tags, tagErr := client.GetDeviceTag(redisConnection, d.watcher.Mongodb(), record.DeviceId)
				redisConnection.Close()
				if tagErr != nil {
					log.CtxLog(c).Errorf("cannot find the device name from mongo, device_id: %s, err: %v", request.DeviceId, err)
				}
				response.Data[i].Description = tags.Description
			}

			if err = ws.SuccessMessage(&response, "ok"); err != nil {
				log.CtxLog(c).Error(err)
				return
			}
			select {
			case <-ticker.C:
				continue
			case <-updateNow:
				time.Sleep(time.Millisecond * 100)
				continue
			case <-ws.Done():
				log.CtxLog(c).Infof("websocket closed")
				return
			}
		}
	}
}

func (d *diagnosis) ListGroot() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.GrootDiagnosisRequest
			response model.GrootDiagnosisResponse
		)

		err := c.BindQuery(&request)
		if err != nil {
			log.CtxLog(c).Errorf("parse query parameters, err: %v", err)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("parse query parameters error"))
			return
		}

		if request.Page == 0 {
			request.Page = 1
		}
		if request.Size == 0 {
			request.Size = 10
		}

		filter := bson.D{util.SelectedTimeDuration("timestamp", request.StartTime, request.EndTime)}
		if request.DeviceId != "" {
			filter = append(filter, bson.E{Key: "device_id", Value: request.DeviceId})
		}
		levels := parse2Int64(request.Levels, ",")
		if len(levels) != 0 {
			filter = append(filter, bson.E{Key: "level", Value: bson.M{"$in": levels}})
		}
		modes := parse2Int64(request.Modes, ",")
		if len(modes) != 0 {
			filter = append(filter, bson.E{Key: "mode", Value: bson.M{"$in": modes}})
		}
		diagnosisTypes := []string{}
		if request.DiagnosisType != "" {
			diagnosisTypes = strings.Split(request.DiagnosisType, ",")
		}
		if len(diagnosisTypes) != 0 {
			for _, diagnosisType := range diagnosisTypes {
				switch diagnosisType {
				case "timeout_command":
					filter = append(filter, bson.E{Key: "exist_timeout_command", Value: true})
				case "scheduled_time_not_start":
					filter = append(filter, bson.E{Key: "exist_scheduled_time_not_start", Value: true})
				case "fail_uploading_service":
					filter = append(filter, bson.E{Key: "exist_fail_uploading_service", Value: true})
				case "start_without_s2_close":
					filter = append(filter, bson.E{Key: "exist_start_without_s2_close", Value: true})
				case "cP_status":
					filter = append(filter, bson.E{Key: "exist_cP_status", Value: true})
				case "text":
					filter = append(filter, bson.E{Key: "exist_text", Value: true})
				}
			}
		}

		byteData, cnt, err := d.watcher.Mongodb().NewMongoEntry(filter).ListByPagination(
			umw.DiagnosisManagement,
			"groot",
			client.Pagination{
				Limit:  int64(request.Size),
				Offset: int64((request.Page - 1) * request.Size),
			},
			client.Ordered{
				Key:        "timestamp",
				Descending: true,
			},
		)
		if err != nil {
			log.CtxLog(c).Errorf("fail to list groot diagnosis records, err: %v, filter: %+v", err, filter)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var records []umw.GrootDiagnoseData
		if err = json.Unmarshal(byteData, &records); err != nil {
			log.CtxLog(c).Errorf("fail to unmarshal GrootDiagnoseData records, err: %v, filter: %+v, data: %s", err, filter, string(byteData))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		result := []model.GrootDiagnoseVO{}
		for _, record := range records {
			result = append(result, convertGrootDiagnosisPO2VO(record))
		}
		response.Total = cnt
		response.Data = result
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func parse2Int64(str, split string) []int64 {
	if len(str) == 0 {
		return nil
	}
	res := []int64{}
	strs := strings.Split(str, split)
	for _, s := range strs {
		value, err := strconv.ParseInt(s, 10, 64)
		if err != nil {
			continue
		}
		res = append(res, value)
	}
	return res
}

func convertGrootDiagnosisPO2VO(po umw.GrootDiagnoseData) model.GrootDiagnoseVO {
	vo := model.GrootDiagnoseVO{
		DeviceId:  po.DeviceId,
		Level:     po.Level,
		Timestamp: po.Timestamp,
		ModeType:  po.ModeType,
	}
	diagnosisTypes := []string{}
	if po.ExistTimeoutCommand {
		vo.TimeoutCommandProperties = model.TimeoutCommandPropertiesVO{
			RemoteControlCommand: po.TimeoutCommandProperties.RemoteControlCommand,
			OrderId:              po.TimeoutCommandProperties.OrderId,
			CommandTimestamp:     po.TimeoutCommandProperties.CommandTimestamp,
			RebootCount:          po.TimeoutCommandProperties.RebootCount,
			RebootTimestamp:      po.TimeoutCommandProperties.RebootTimestamp,
			Result:               po.TimeoutCommandProperties.Result,
		}
		diagnosisTypes = append(diagnosisTypes, "timeout_command")
	}
	if po.ExistScheduledTimeNotStart {
		vo.ScheduledTimeNotStartProperties = model.ScheduledTimeNotStartPropertiesVO{
			IsOrderModeCharging: po.ScheduledTimeNotStartProperties.IsOrderModeCharging,
			AuthType:            po.ScheduledTimeNotStartProperties.AuthType,
			HasAuthMsg:          po.ScheduledTimeNotStartProperties.HasAuthMsg,
			AuthOkTimestamp:     po.ScheduledTimeNotStartProperties.AuthOkTimestamp,
			Result:              po.ScheduledTimeNotStartProperties.Result,
		}
		diagnosisTypes = append(diagnosisTypes, "scheduled_time_not_start")
	}
	if po.ExistFailUploadingService {
		vo.FailUploadingServiceProperties = model.FailUploadingServicePropertiesVO{
			StartReportFinishTimestamp: po.FailUploadingServiceProperties.StartReportFinishTimestamp,
			OverReportFinishTimestamp:  po.FailUploadingServiceProperties.OverReportFinishTimestamp,
			Result:                     po.FailUploadingServiceProperties.Result,
		}
		diagnosisTypes = append(diagnosisTypes, "fail_uploading_service")
	}
	if po.ExistStartWithoutS2Close {
		vo.StartWithoutS2CloseProperties = model.StartWithoutS2ClosePropertiesVO{
			IsPwmOut:         po.StartWithoutS2CloseProperties.IsPwmOut,
			PwmOutTimestamp:  po.StartWithoutS2CloseProperties.PwmOutTimestamp,
			IsS2Close:        po.StartWithoutS2CloseProperties.IsS2Close,
			S2CloseTimestamp: po.StartWithoutS2CloseProperties.S2CloseTimestamp,
			Result:           po.StartWithoutS2CloseProperties.Result,
		}
		diagnosisTypes = append(diagnosisTypes, "start_without_s2_close")
	}
	if po.ExistCPStatus {
		vo.CPStatusProperties = model.CPStatusPropertiesVO{
			CpStatus: po.CPStatusProperties.CpStatus,
			Result:   po.CPStatusProperties.Result,
		}
		diagnosisTypes = append(diagnosisTypes, "cP_status")
	}
	if po.ExistText {
		text := ""
		if po.TextProperties.Text1 != "" {
			text = text + po.TextProperties.Text1
		}
		if po.TextProperties.Text2 != "" {
			text = text + po.TextProperties.Text2
		}
		if po.TextProperties.Text3 != "" {
			text = text + po.TextProperties.Text3
		}
		if po.TextProperties.Text4 != "" {
			text = text + po.TextProperties.Text4
		}
		if po.TextProperties.Text5 != "" {
			text = text + po.TextProperties.Text5
		}
		if po.TextProperties.Text6 != "" {
			text = text + po.TextProperties.Text6
		}
		if po.TextProperties.Text7 != "" {
			text = text + po.TextProperties.Text7
		}
		if po.TextProperties.Text8 != "" {
			text = text + po.TextProperties.Text8
		}
		vo.TextProperties = model.TextPropertiesVO{
			Text: text,
		}
		diagnosisTypes = append(diagnosisTypes, "text")
	}
	vo.DiagnosisType = diagnosisTypes
	return vo
}

func (d *diagnosis) ListGrootDevice() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.GetGrootDeviceRequest
			response model.GetGrootDeviceResponse
		)
		err := c.BindQuery(&request)
		if err != nil {
			log.CtxLog(c).Errorf("parse query parameters, err: %v", err)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("parse query parameters error"))
			return
		}
		if request.Keyword == "" {
			um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
			return
		}
		ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
		defer cancel()
		var res []model.PowerGrootDevice
		filter := bson.D{}
		filter = append(filter, bson.E{Key: "_id", Value: bson.M{"$regex": primitive.Regex{Pattern: ".*" + request.Keyword + ".*", Options: "i"}}})
		cursor, err := d.watcher.RbMongodb().Client.Database("PowerGroot").Collection("devices").Find(ctx, filter, options.Find().SetLimit(request.Limit))
		if err != nil {
			log.CtxLog(c).Errorf("fail to get Groot device records, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if err = cursor.All(ctx, &res); err != nil {
			log.CtxLog(c).Errorf("decode from cursor err Groot device records, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		deviceIds := []string{}
		for _, re := range res {
			deviceIds = append(deviceIds, re.Id)
		}
		response.Data = deviceIds
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

// GetCustomOperationFilter 获取操作日志查询的过滤条件
func (d *diagnosis) GetCustomOperationFilter(c *gin.Context, project string, request model.GetOperationLogRequest) (res bson.D) {
	if request.Type == 2 {
		return
	}
	dbName := fmt.Sprintf("%s-%s", umw.OperationLog, ucmd.RenameProjectDB(project))
	filter := bson.D{
		{"type", request.Type},
	}
	needSearch := false
	// todo:国际化
	if request.OperationDescription != nil {
		filter = append(filter, bson.E{Key: "button_cn_name", Value: bson.M{"$regex": *request.OperationDescription}})
		needSearch = true
	}
	if request.OperationInterface != nil {
		filter = append(filter, bson.E{Key: "page_cn_name", Value: bson.M{"$regex": *request.OperationInterface}})
		needSearch = true
	}
	opList := []int64{-1}
	if needSearch {
		var opTable []model.MPCOperationTable
		_, err := d.watcher.Mongodb().NewMongoEntry(filter).FindMany(dbName, "operation_table", options.Find(), &opTable)
		if err != nil {
			log.CtxLog(c).Errorf("get operation log, fail to list operation table, err: %v, db: %s, request: %s", err, dbName, ucmd.ToJsonStrIgnoreErr(request))
			return
		}
		for _, op := range opTable {
			opList = append(opList, op.Operation)
		}
		res = append(res, bson.E{Key: "operation", Value: bson.M{"$in": opList}})
	}

	if request.Operator != nil {
		res = append(res, bson.E{Key: "user_id", Value: *request.Operator})
	}
	return
}

func (d *diagnosis) GetOperationLog() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.GetOperationLogRequest
			response model.CommonResponse
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("get operation log, parse query parameters, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		util.SetURIParamDefault(&request.CommonUriParam)
		if err := util.CheckTimeRange(request.StartTime, request.EndTime); err != nil {
			log.CtxLog(c).Errorf("get operation log, invalid start_time and end_time, err: %v, request: %+v", err, request)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		project := c.Param("project")
		deviceId := c.Param("device_id")
		if project == "" || deviceId == "" {
			errMsg := fmt.Sprintf("get operation log, invalid project or device id, project: %s, device id: %s", project, deviceId)
			log.CtxLog(c).Errorf(errMsg)
			um.FailWithBadRequest(c, &response, errMsg)
			return
		}

		dbName := fmt.Sprintf("%s-%s", umw.OperationLog, ucmd.RenameProjectDB(project))
		tableData, err := d.watcher.Mongodb().NewMongoEntry(bson.D{{"type", request.Type}}).ListAll(dbName, "operation_table", client.Ordered{})
		if err != nil {
			log.CtxLog(c).Errorf("get operation log, fail to list operation table, err: %v, db: %s, request: %+v", err, dbName, request)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var opLog []byte
		filter := bson.D{
			{"timestamp", bson.M{"$gte": request.StartTime, "$lte": request.EndTime}},
			{"type", request.Type},
		}
		customFilter := d.GetCustomOperationFilter(c, project, request)
		filter = append(filter, customFilter...)
		opLog, response.Total, err = d.watcher.Mongodb().NewMongoEntry(filter).ListByPagination(dbName, deviceId,
			client.Pagination{Limit: int64(request.Size), Offset: int64((request.Page - 1) * request.Size)},
			client.Ordered{Key: "timestamp", Descending: true})
		if err != nil {
			log.CtxLog(c).Errorf("get operation log, fail to list operation log, err: %v, db: %s, request: %+v", err, dbName, request)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if request.Type == 1 {
			// 用户屏操作日志
			var (
				operationTable []model.MPCOperationTable
				operationMap   = make(map[int64]model.MPCOperationTable)
				operationLogs  []umw.MongoOperationLogV2
			)
			if err = json.Unmarshal(tableData, &operationTable); err != nil {
				log.CtxLog(c).Errorf("get operation log, fail to unmarshal operation table, err: %v, db: %s, request: %+v", err, dbName, request)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			for _, op := range operationTable {
				operationMap[op.Operation] = op
			}
			if err = json.Unmarshal(opLog, &operationLogs); err != nil {
				log.CtxLog(c).Errorf("get operation log, fail to unmarshal operation logs, err: %v, db: %s, request: %+v", err, dbName, request)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			for _, op := range operationLogs {
				record := model.MPCOperationLog{
					Timestamp: op.Timestamp,
					UserId:    op.UserId,
					Module:    op.Module,
					Operation: op.Operation,
				}
				if request.Lang == "cn" {
					record.Page = operationMap[op.Operation].PageCnName
					record.Button = operationMap[op.Operation].ButtonCnName
					record.Action = operationMap[op.Operation].ActionCnName
					record.Remark = operationMap[op.Operation].RemarksCnName
					if operationMap[op.Operation].ArgsCnName != "" {
						record.Args = fmt.Sprintf(operationMap[op.Operation].ArgsCnName, util.ConvertStringArray(strings.Split(op.Args, ";"))...)
					}
				} else {
					// todo:国际化
					record.Page = operationMap[op.Operation].PageCnName
					record.Button = operationMap[op.Operation].ButtonCnName
					record.Action = operationMap[op.Operation].ActionCnName
					record.Remark = operationMap[op.Operation].RemarksCnName
					if operationMap[op.Operation].ArgsCnName != "" {
						record.Args = fmt.Sprintf(operationMap[op.Operation].ArgsCnName, util.ConvertStringArray(strings.Split(op.Args, ";"))...)
					}
				}
				response.Data = append(response.Data, record)
			}
		} else if request.Type == 2 {
			// OSS远程指令操作日志
			var (
				operationTable []model.OSSOperationTable
				operationMap   = make(map[string]model.OSSOperationTable)
				operationLogs  []umw.MongoOSSOperationLog
			)
			if err = json.Unmarshal(tableData, &operationTable); err != nil {
				log.CtxLog(c).Errorf("get operation log, fail to unmarshal operation table, err: %v, db: %s, request: %+v", err, dbName, request)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			for _, op := range operationTable {
				operationMap[op.Command] = op
			}
			if err = json.Unmarshal(opLog, &operationLogs); err != nil {
				log.CtxLog(c).Errorf("get operation log, fail to unmarshal operation logs, err: %v, db: %s, request: %+v", err, dbName, request)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			for _, op := range operationLogs {
				record := model.OSSOperationLog{
					Timestamp:     op.Timestamp,
					IsFail:        op.IsFail,
					FailureReason: op.FailureReason,
					AbilityParams: op.AbilityParams,
				}
				if request.Lang == "cn" {
					record.AbilityCode = operationMap[op.AbilityCode].DescriptionCn
				} else {
					// todo: 国际化
					record.AbilityCode = operationMap[op.AbilityCode].DescriptionCn
				}
				if record.AbilityCode == "" {
					record.AbilityCode = op.AbilityCode
				}
				response.Data = append(response.Data, record)
			}
		} else {
			errMsg := fmt.Sprintf("get operation log, invalid request type, request: %+v", request)
			log.CtxLog(c).Errorf(errMsg)
			um.FailWithBadRequest(c, &response, errMsg)
			return
		}

		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) GetStuckStatPanel() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.GetStuckStatPanelRequest
			response model.GetStuckStatPanelResponse
		)
		err := c.BindQuery(&request)
		if err != nil {
			log.CtxLog(c).Errorf("fail to GetStuckStatPanel: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		startTime := time.UnixMilli(request.StartTime)
		startTimeStr := startTime.Format("2006-01-02")
		endTime := time.UnixMilli(request.EndTime)
		endTimeStr := endTime.Format("2006-01-02")
		filter := bson.D{
			bson.E{Key: "stat_day", Value: bson.M{"$gte": startTimeStr, "$lte": endTimeStr}},
		}
		if request.Project != "" {
			filter = append(filter, bson.E{"project", request.Project})
		}
		if request.DeviceId != "" {
			deviceIds := strings.Split(request.DeviceId, ",")
			filter = append(filter, bson.E{"device_id", bson.M{"$in": deviceIds}})
		}
		cursor, err := d.watcher.Mongodb().Client.Database("diagnosis-management").Collection("daily-stuck-service-stat").Aggregate(c, mongo.Pipeline{
			bson.D{{"$match", filter}},
			bson.D{{"$group", bson.M{
				"_id":                 "$stat_day",
				"total_service_count": bson.M{"$sum": "$total_servie_count"},
				"stuck_count":         bson.M{"$sum": "$stuck_service_count"},
			}}},
			bson.D{{"$project", bson.M{
				"stat_day":            "$_id",
				"total_service_count": 1,
				"stuck_count":         1,
				"stuck_rate":          bson.M{"$cond": bson.A{bson.M{"$eq": bson.A{"$total_service_count", 0}}, 0, bson.M{"$divide": bson.A{"$stuck_count", "$total_service_count"}}}},
			}}},
			bson.D{{"$sort", bson.D{{"stat_day", 1}}}},
		})
		if err != nil {
			log.CtxLog(c).Errorf("fail to GetStuckStatPanel: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var res []struct {
			StatDay           string  `json:"stat_day" bson:"stat_day"`
			TotalServiceCount int64   `json:"total_service_count" bson:"total_service_count"`
			StuckCount        int64   `json:"stuck_count" bson:"stuck_count"`
			StuckRate         float64 `json:"stuck_rate" bson:"stuck_rate"`
		}
		if err = cursor.All(c, &res); err != nil {
			log.CtxLog(c).Errorf("fail to GetStuckStatPanel: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		selectDays := 0
		totalService := int64(0)
		totalStuckService := int64(0)
		dailyDetails := []model.DailyStuckStat{}
		for _, dailyStatData := range res {
			selectDays++
			totalService = totalService + dailyStatData.TotalServiceCount
			totalStuckService = totalStuckService + dailyStatData.StuckCount
			dailyDetails = append(dailyDetails, model.DailyStuckStat{
				Day:               dailyStatData.StatDay,
				TotalServieCount:  dailyStatData.TotalServiceCount,
				StuckServiceCount: dailyStatData.StuckCount,
				StuckRate:         dailyStatData.StuckRate,
			})
		}
		averageStuckRate := float64(0)
		if totalService != 0 {
			averageStuckRate = float64(totalStuckService) / float64(totalService)
		}
		response.Data = model.StuckStatPanelInfo{
			AverageStuckRate:  averageStuckRate,
			TotalServieCount:  totalService,
			StuckServiceCount: totalStuckService,
			StatDayNums:       int64(selectDays),
			DailyDetails:      dailyDetails,
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) GetBluetoothDisconnectStatPanel() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.GetBluetoothDisconnectPanelRequest
			response model.GetBluetoothDisconnectPanelResponse
		)
		err := c.BindQuery(&request)
		if err != nil {
			log.CtxLog(c).Errorf("fail to GetStuckStatPanel: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		startTime := time.UnixMilli(request.StartTime)
		startTimeStr := startTime.Format("2006-01-02")
		endTime := time.UnixMilli(request.EndTime)
		endTimeStr := endTime.Format("2006-01-02")
		dateStrList, err := util.GetDateStrList(startTimeStr, endTimeStr)
		if err != nil {
			log.CtxLog(c).Errorf("util.GetDateStrList err. err:%v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		filter := bson.D{
			bson.E{Key: "stat_day", Value: bson.M{"$gte": startTimeStr, "$lte": endTimeStr}},
		}
		if request.DeviceIds != "" {
			deviceIdStrs := strings.Split(request.DeviceIds, ",")
			filter = append(filter, bson.E{Key: "device_id", Value: bson.M{"$in": deviceIdStrs}})
		}

		cursor, err := d.watcher.Mongodb().Client.Database("diagnosis-management").Collection("daily_bluetooth_disconnect_stat").Aggregate(c, mongo.Pipeline{
			bson.D{{"$match", filter}},
			bson.D{{"$group", bson.M{
				"_id":                        "$stat_day",
				"alarm_count":                bson.M{"$sum": "$alarm_count"},
				"alarm_in_service_count":     bson.M{"$sum": "$alarm_in_service_count"},
				"alarm_not_in_service_count": bson.M{"$sum": "$alarm_not_in_service_count"},
				"active_device_count":        bson.M{"$max": "$active_device_count"},
			}}},
			bson.D{{"$project", bson.M{
				"stat_day":                   "$_id",
				"alarm_count":                1,
				"alarm_in_service_count":     1,
				"alarm_not_in_service_count": 1,
				"active_device_count":        1,
			}}},
			bson.D{{"$sort", bson.D{{"stat_day", 1}}}},
		})
		if err != nil {
			log.CtxLog(c).Errorf("fail to daily_bluetooth_disconnect_stat: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var res []BluetoothDisconnectStatDailyAggre
		if err = cursor.All(c, &res); err != nil {
			log.CtxLog(c).Errorf("fail to daily_bluetooth_disconnect_stat: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		dateMongoDailyStat := map[string]BluetoothDisconnectStatDailyAggre{}
		for _, aggre := range res {
			dateMongoDailyStat[aggre.StatDay] = aggre
		}

		dailyBluetoothDisconnectStatVOs := []model.DailyBluetoothDisconnectStat{}
		totalAlarmCount := int64(0)
		totalAverageBluetoothDisconnectCount := float64(0)
		for _, dateStr := range dateStrList {
			mongoBluetoothDisconnectDailyStat, found := dateMongoDailyStat[dateStr]
			if !found {
				dailyBluetoothDisconnectStatVOs = append(dailyBluetoothDisconnectStatVOs, model.DailyBluetoothDisconnectStat{
					Day:                             dateStr,
					AverageBluetoothDisconnectCount: 0,
					TotalAlarmCount:                 0,
					ActiveDeviceCount:               0,
				})
				continue
			}
			alarmCount := mongoBluetoothDisconnectDailyStat.AlarmCount
			if request.IsService != nil && *request.IsService == true {
				alarmCount = mongoBluetoothDisconnectDailyStat.AlarmInServiceCount
			}
			if request.IsService != nil && *request.IsService == false {
				alarmCount = mongoBluetoothDisconnectDailyStat.AlarmNotInServiceCount
			}
			averageBluetoothDisconnectCount := float64(0)
			if mongoBluetoothDisconnectDailyStat.ActiveDeviceCount != 0 {
				if request.DeviceIds != "" {
					deviceIdStrs := strings.Split(request.DeviceIds, ",")
					averageBluetoothDisconnectCount = float64(alarmCount) / float64(len(deviceIdStrs))
				} else {
					averageBluetoothDisconnectCount = float64(alarmCount) / float64(mongoBluetoothDisconnectDailyStat.ActiveDeviceCount)
				}
			}

			totalAverageBluetoothDisconnectCount = totalAverageBluetoothDisconnectCount + averageBluetoothDisconnectCount
			totalAlarmCount = totalAlarmCount + alarmCount

			// 保利两位小数
			averageBluetoothDisconnectCount = math.Round(averageBluetoothDisconnectCount*100) / 100
			dailyBluetoothDisconnectStatVOs = append(dailyBluetoothDisconnectStatVOs, model.DailyBluetoothDisconnectStat{
				Day:                             mongoBluetoothDisconnectDailyStat.StatDay,
				AverageBluetoothDisconnectCount: averageBluetoothDisconnectCount,
				TotalAlarmCount:                 alarmCount,
				ActiveDeviceCount:               mongoBluetoothDisconnectDailyStat.ActiveDeviceCount,
			})
		}
		averageBluetoothDisconnectCount := float64(0)
		if len(dateStrList) != 0 {
			averageBluetoothDisconnectCount = math.Round((totalAverageBluetoothDisconnectCount/float64(len(dateStrList)))*100) / 100
		}
		data := model.BluetoothDisconnectStatPanelInfo{
			AverageBluetoothDisconnectCount: averageBluetoothDisconnectCount,
			TotalAlarmCount:                 totalAlarmCount,
			StatDayNums:                     int64(len(dateStrList)),
			DailyDetails:                    dailyBluetoothDisconnectStatVOs,
		}
		response.Data = data
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

type BluetoothDisconnectStatDailyAggre struct {
	StatDay                string `json:"stat_day" bson:"stat_day"`
	AlarmCount             int64  `json:"alarm_count" bson:"alarm_count"`
	AlarmInServiceCount    int64  `json:"alarm_in_service_count" bson:"alarm_in_service_count"`
	AlarmNotInServiceCount int64  `json:"alarm_not_in_service_count" bson:"alarm_not_in_service_count"`
	ActiveDeviceCount      int64  `json:"active_device_count" bson:"active_device_count"`
}

func (d *diagnosis) ListDeviceBluetoothDisconnectStat() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.ListDeviceBluetoothDisconnectStatRequest
			response model.ListBluetoothDisconnectStatResponse
		)
		err := c.BindQuery(&request)
		if err != nil {
			log.CtxLog(c).Errorf("bind josn err. err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if request.Page <= 0 {
			request.Page = 1
		}
		if request.Size == 0 {
			request.Size = 20
		}
		sort := -1
		if !request.Descending {
			sort = 1
		}

		filter := bson.D{
			bson.E{Key: "create_ts", Value: bson.M{"$gte": request.StartTime, "$lte": request.EndTime}},
		}
		if request.IsService != nil {
			filter = append(filter, bson.E{"is_happen_in_service", request.IsService})
		}
		if request.DeviceIds != "" {
			deviceIdStrs := strings.Split(request.DeviceIds, ",")
			filter = append(filter, bson.E{Key: "device_id", Value: bson.M{"$in": deviceIdStrs}})
		}

		cursor, err := d.watcher.Mongodb().Client.Database("diagnosis-management").Collection("bluetooth_disconnect_alarm").Aggregate(c, mongo.Pipeline{
			bson.D{{"$match", filter}},
			bson.D{{"$group", bson.M{
				"_id":               "$device_id",
				"total_alarm_count": bson.M{"$sum": 1},
			}}},
			bson.D{{"$project", bson.M{
				"device_id":         "$_id",
				"total_alarm_count": 1,
			}}},
			bson.D{{"$sort", bson.D{{"total_alarm_count", sort}, {"device_id", sort}}}},
			bson.D{{"$skip", (request.Page - 1) * request.Size}},
			bson.D{{"$limit", request.Size}},
		})
		if err != nil {
			log.CtxLog(c).Errorf("fail to bluetooth_disconnect: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var res []AggregateDeviceBluetoothDisconnectInfo
		if err = cursor.All(c, &res); err != nil {
			log.CtxLog(c).Errorf("fail to bluetooth_disconnect: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		data := []*model.DeviceBluetoothDisconnectStat{}
		for _, aggregateDeviceStuckInfo := range res {
			data = append(data, convertAggregateDeviceBluetoothDisconnect2VO(aggregateDeviceStuckInfo))
		}
		index := (request.Page - 1) * request.Size
		for _, deviceStuckStat := range data {
			index++
			deviceStuckStat.No = index
		}

		response.Data = data

		// 获取总数
		cursor, err = d.watcher.Mongodb().Client.Database("diagnosis-management").Collection("bluetooth_disconnect_alarm").Aggregate(c, mongo.Pipeline{
			bson.D{{"$match", filter}},
			bson.D{{"$group", bson.M{
				"_id":               "$device_id",
				"total_alarm_count": bson.M{"$sum": 1},
			}}},
			bson.D{{"$project", bson.M{
				"device_id":         "$_id",
				"total_alarm_count": 1,
			}}},
			bson.D{{"$count", "total"}},
		})
		if err != nil {
			log.CtxLog(c).Errorf("fail to bluetooth_disconnect: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var totalRes []AggregateTotalRes
		if err = cursor.All(c, &totalRes); err != nil {
			log.CtxLog(c).Errorf("fail to bluetooth_disconnect: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Total = 0
		if len(totalRes) != 0 {
			response.Total = totalRes[0].Total
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

type AggregateDeviceBluetoothDisconnectInfo struct {
	DeviceId        string `json:"device_id" bson:"device_id"`
	TotalAlarmCount int64  `json:"total_alarm_count" bson:"total_alarm_count"`
}

func (d *diagnosis) GetBluetoothDisconnectAlarmInfo() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.GetBluetoothDisconnectAlarmRequest
			response model.GetBluetoothDisconnectAlarmInfoResponse
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("fail to GetBluetoothDisconnectAlarmInfo: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		project := c.Param("project")
		util.SetURIParamDefault(&request.CommonUriParam)

		filter := bson.M{
			"project":   project,
			"create_ts": bson.M{"$gte": request.StartTime, "$lte": request.EndTime},
		}
		if request.DeviceIds != "" {
			filter["device_id"] = bson.M{"$in": strings.Split(request.DeviceIds, ",")}
		}
		pipeline := mongo.Pipeline{
			bson.D{{"$match", filter}},
			bson.D{{"$group", bson.M{
				"_id":         "$alarm_id",
				"total_times": bson.M{"$sum": 1},
				"total_service_times": bson.M{"$sum": bson.M{"$cond": bson.A{
					bson.M{"$eq": bson.A{"$is_happen_in_service", true}},
					1,
					0,
				}}},
			}}},
			bson.D{{"$project", bson.M{
				"alarm_id":            "$_id",
				"total_times":         1,
				"total_service_times": 1,
			}}},
			bson.D{{"$facet", bson.M{
				"total": bson.A{bson.M{"$group": bson.M{"_id": primitive.Null{}, "count": bson.M{"$sum": 1}}}},
				"data": bson.A{
					bson.D{{"$sort", bson.M{"alarm_id": 1}}},
					bson.D{{"$skip", (request.Page - 1) * request.Size}},
					bson.D{{"$limit", request.Size}},
				},
			}}},
		}

		var res []mongo_model.BluetoothDisconnectAlarmInfo
		err := d.watcher.Mongodb().NewMongoEntry().Aggregate(umw.DiagnosisManagement, "bluetooth_disconnect_alarm", pipeline, &res)
		if err != nil {
			log.CtxLog(c).Errorf("GetBluetoothDisconnectAlarmInfo, fail to aggregate, err: %v, pipeline: %s", err, ucmd.ToJsonStrIgnoreErr(pipeline))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if len(res) == 0 {
			log.CtxLog(c).Errorf("GetBluetoothDisconnectAlarmInfo, empty alarm info, pipeline: %s", ucmd.ToJsonStrIgnoreErr(pipeline))
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if len(res[0].Total) == 0 {
			log.CtxLog(c).Infof("GetBluetoothDisconnectAlarmInfo, total 0")
			um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
			return
		}
		response.Total = res[0].Total[0].Count
		startNo := (request.Page - 1) * request.Size
		for i, record := range res[0].Data {
			alarmInfo := model.BluetoothDisconnectAlarmInfo{
				No:                startNo + i + 1,
				AlarmId:           record.AlarmId,
				TotalTimes:        record.TotalTimes,
				TotalServiceTimes: record.TotalServiceTimes,
			}
			alarmCache, _ := cache.DeviceAlarmInfoCache.GetSingleDeviceAlarm(project, record.AlarmId)
			if alarmCache != nil {
				alarmInfo.AlarmIdDescription = alarmCache.VarCnName
			}
			response.Data = append(response.Data, alarmInfo)
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

type BluetoothDisconnectDistributionData struct {
	TotalTimes        int64                           `json:"total_times" bson:"total_times"`
	TotalServiceTimes int64                           `json:"total_service_times" bson:"total_service_times"`
	Devices           []model.DeviceStuckServiceCount `json:"devices" bson:"devices"`
}

func (d *diagnosis) GetBluetoothDisconnectAlarmDistribution() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.GetBluetoothDisconnectAlarmRequest
			response model.GetBluetoothDisconnectAlarmDistributionResponse
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("fail to GetBluetoothDisconnectAlarmDistribution: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		project := c.Param("project")
		alarmId := c.Param("alarm_id")
		alarmCache, _ := cache.DeviceAlarmInfoCache.GetSingleDeviceAlarm(project, alarmId)
		if alarmCache == nil {
			log.CtxLog(c).Errorf("GetBluetoothDisconnectAlarmDistribution, empty alarm info")
			um.FailWithInternalServerError(c, &response, "empty alarm info")
			return
		}
		response.AlarmId = alarmId
		response.AlarmIdDescription = alarmCache.VarCnName

		filter := bson.D{
			{"project", project},
			{"create_ts", bson.M{"$gte": request.StartTime, "$lte": request.EndTime}},
			{"alarm_id", alarmId},
		}
		total, err := client.GetWatcher().Mongodb().NewMongoEntry(filter).Count(umw.DiagnosisManagement, "bluetooth_disconnect_alarm")
		if err != nil {
			log.CtxLog(c).Errorf("GetBluetoothDisconnectAlarmDistribution, fail to count total: %v, filter: %s", err, ucmd.ToJsonStrIgnoreErr(filter))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.TotalTimes = total

		filter = append(filter, bson.E{Key: "is_happen_in_service", Value: true})
		pipeline := mongo.Pipeline{
			bson.D{{"$match", filter}},
			bson.D{{"$group", bson.M{
				"_id": "$device_id",
				"service_count": bson.M{"$sum": bson.M{"$cond": bson.A{
					bson.M{"$eq": bson.A{"$is_happen_in_service", true}},
					1,
					0,
				}}},
				"alarm_count": bson.M{"$sum": 1},
			}}},
			bson.D{{"$group", bson.M{
				"_id":                 primitive.Null{},
				"total_times":         bson.M{"$sum": "$alarm_count"},
				"total_service_times": bson.M{"$sum": "$service_count"},
				"devices": bson.M{
					"$push": bson.M{
						"device_id":   "$_id",
						"total_times": "$alarm_count",
						"stuck_times": "$service_count",
					},
				},
			}}},
			bson.D{{"$project", bson.M{
				"_id":                 0,
				"total_times":         "$total_times",
				"total_service_times": "$total_service_times",
				"devices": bson.M{
					"$map": bson.M{
						"input": "$devices",
						"as":    "device",
						"in": bson.M{
							"device_id":   "$$device.device_id",
							"total_times": "$$device.total_times",
							"stuck_times": "$$device.stuck_times",
							"proportion": bson.M{
								"$cond": bson.A{
									bson.M{"$eq": bson.A{"$total_service_times", 0}},
									0,
									bson.M{"$divide": bson.A{"$$device.stuck_times", "$total_service_times"}},
								},
							},
						},
					},
				},
			}}},
		}
		var resList []BluetoothDisconnectDistributionData
		err = d.watcher.Mongodb().NewMongoEntry().Aggregate(umw.DiagnosisManagement, "bluetooth_disconnect_alarm", pipeline, &resList)
		if err != nil {
			log.CtxLog(c).Errorf("GetBluetoothDisconnectAlarmDistribution, fail to aggregate, err: %v, pipeline: %s", err, ucmd.ToJsonStrIgnoreErr(pipeline))
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if len(resList) == 0 {
			log.CtxLog(c).Info("GetBluetoothDisconnectAlarmDistribution, empty alarm distribution data, pipeline: %s", ucmd.ToJsonStrIgnoreErr(pipeline))
			um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
			return
		}
		res := resList[0]
		response.TotalServiceTimes = res.TotalServiceTimes
		sort.Slice(res.Devices, func(i, j int) bool {
			if res.Devices[i].StuckTimes == res.Devices[j].StuckTimes {
				return res.Devices[i].DeviceId < res.Devices[j].DeviceId
			}
			return res.Devices[i].StuckTimes > res.Devices[j].StuckTimes
		})
		var distributionData []model.StuckAlarmDistributionData
		deviceMap := partitionByBoundaries(res.Devices)
		for i := len(model.StuckAlarmProportionKeys) - 1; i >= 0; i-- {
			key := model.StuckAlarmProportionKeys[i]
			distributionData = append(distributionData, model.StuckAlarmDistributionData{
				Rate:        key,
				DeviceCount: int64(len(deviceMap[key])),
				Devices:     deviceMap[key],
			})
		}
		response.Data = distributionData

		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

type AggregateDeviceStuckInfo struct {
	DeviceId          string  `json:"device_id" bson:"device_id"`
	TotalServiceCount int64   `json:"total_service_count" bson:"total_service_count"`
	StuckCount        int64   `json:"stuck_count" bson:"stuck_count"`
	StuckRate         float64 `json:"stuck_rate" bson:"stuck_rate"`
}

type AggregateTotalRes struct {
	Total int64 `json:"total" bson:"total"`
}

func (d *diagnosis) ListDeviceStuckStat() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.ListDeviceStuckStatRequest
			response model.ListDeviceStuckStatResponse
		)
		err := c.BindQuery(&request)
		if err != nil {
			log.CtxLog(c).Errorf("fail to ListDeviceStuckStat: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if request.Page <= 0 {
			request.Page = 1
		}
		if request.Size == 0 {
			request.Size = 20
		}
		sort := -1
		if !request.Descending {
			sort = 1
		}

		startTime := time.UnixMilli(request.StartTime)
		startTimeStr := startTime.Format("2006-01-02")
		endTime := time.UnixMilli(request.EndTime)
		endTimeStr := endTime.Format("2006-01-02")
		filter := bson.D{
			bson.E{Key: "stat_day", Value: bson.M{"$gte": startTimeStr, "$lte": endTimeStr}},
		}
		if request.Project != "" {
			filter = append(filter, bson.E{"project", request.Project})
		}
		if request.DeviceId != "" {
			deviceIds := strings.Split(request.DeviceId, ",")
			filter = append(filter, bson.E{"device_id", bson.M{"$in": deviceIds}})
		}
		cursor, err := d.watcher.Mongodb().Client.Database("diagnosis-management").Collection("daily-stuck-service-stat").Aggregate(c, mongo.Pipeline{
			bson.D{{"$match", filter}},
			bson.D{{"$group", bson.M{
				"_id":                 "$device_id",
				"total_service_count": bson.M{"$sum": "$total_servie_count"},
				"stuck_count":         bson.M{"$sum": "$stuck_service_count"},
			}}},
			bson.D{{"$project", bson.M{
				"device_id":           "$_id",
				"total_service_count": 1,
				"stuck_count":         1,
				"stuck_rate":          bson.M{"$cond": bson.A{bson.M{"$eq": bson.A{"$total_service_count", 0}}, 0, bson.M{"$divide": bson.A{"$stuck_count", "$total_service_count"}}}},
			}}},
			bson.D{{"$sort", bson.D{{"stuck_rate", sort}, {"device_id", sort}}}},
			bson.D{{"$skip", (request.Page - 1) * request.Size}},
			bson.D{{"$limit", request.Size}},
		})
		if err != nil {
			log.CtxLog(c).Errorf("fail to ListDeviceStuckStat: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var res []AggregateDeviceStuckInfo
		if err = cursor.All(c, &res); err != nil {
			log.CtxLog(c).Errorf("fail to ListDeviceStuckStat: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		data := []*model.DeviceStuckStat{}
		for _, aggregateDeviceStuckInfo := range res {
			data = append(data, convertAggregateDeviceStuckInfo2VO(aggregateDeviceStuckInfo))
		}
		ownerIds := map[string]struct{}{}
		index := (request.Page - 1) * request.Size
		for _, deviceStuckStat := range data {
			index++
			deviceStuckStat.No = index
			if deviceStuckStat.Owner != "" {
				ownerIds[deviceStuckStat.Owner] = struct{}{}
			}
		}

		userAvatars, err := util.GetUserAvatar(d.config, ownerIds)
		if err != nil {
			log.CtxLog(c).Errorf("fail to ListDeviceStuckStat: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		for _, deviceStuckStat := range data {
			deviceStuckStat.OwnerAvatarUrl = userAvatars[deviceStuckStat.Owner]
		}

		response.Data = data

		// 获取总数
		cursor, err = d.watcher.Mongodb().Client.Database("diagnosis-management").Collection("daily-stuck-service-stat").Aggregate(c, mongo.Pipeline{
			bson.D{{"$match", filter}},
			bson.D{{"$group", bson.M{
				"_id":                 "$device_id",
				"total_service_count": bson.M{"$sum": "$total_servie_count"},
				"stuck_count":         bson.M{"$sum": "$stuck_service_count"},
			}}},
			bson.D{{"$project", bson.M{
				"device_id":           "$_id",
				"total_device_count":  1,
				"total_service_count": 1,
				"stuck_count":         1,
			}}},
			bson.D{{"$count", "total"}},
		})
		if err != nil {
			log.CtxLog(c).Errorf("fail to ListDeviceStuckStat: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var totalRes []AggregateTotalRes
		if err = cursor.All(c, &totalRes); err != nil {
			log.CtxLog(c).Errorf("fail to ListDeviceStuckStat: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Total = 0
		if len(totalRes) != 0 {
			response.Total = totalRes[0].Total
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func convertAggregateDeviceStuckInfo2VO(PO AggregateDeviceStuckInfo) *model.DeviceStuckStat {
	deviceInfo, exist := cache.PowerSwapCache.GetSingleDevice(PO.DeviceId)
	if !exist {
		return &model.DeviceStuckStat{}
	}
	deviceStuckStat := &model.DeviceStuckStat{
		DeviceId:          PO.DeviceId,
		Description:       deviceInfo.Description,
		StuckRate:         PO.StuckRate,
		StuckServiceCount: PO.StuckCount,
		ServiceCount:      PO.TotalServiceCount,
		Owner:             deviceInfo.DeviceSupervisor,
	}
	return deviceStuckStat
}

func convertAggregateDeviceBluetoothDisconnect2VO(PO AggregateDeviceBluetoothDisconnectInfo) *model.DeviceBluetoothDisconnectStat {
	deviceInfo, exist := cache.PowerSwapCache.GetSingleDevice(PO.DeviceId)
	if !exist {
		return &model.DeviceBluetoothDisconnectStat{}
	}
	deviceStuckStat := &model.DeviceBluetoothDisconnectStat{
		DeviceId:        PO.DeviceId,
		Description:     deviceInfo.Description,
		TotalAlarmCount: PO.TotalAlarmCount,
	}
	return deviceStuckStat
}

type ServiceStep struct {
	StepCode string
	StepName string
}

var PUS2Steps = []ServiceStep{
	{"1202", "启动换电按键使能"},
	{"1203", "BMS刷写中"},
	{"1204", "服务电池出仓"},
	{"1205", "服务电池到提升机接驳位"},
	{"1206", "四轮推杆到工作"},
	{"1207", "开合门打开"},
	{"1208", "车辆举升到工作"},
	{"1209", "电池拆卸，RGV举升"},
	{"1210", "电池拆卸，解锁"},
	{"1211", "电池拆卸，RGV下降"},
	{"1212", "车辆下降"},
	{"1213", "亏电电池到缓存位"},
	{"1214", "服务电池转运到停车平台"},
	{"1215", "车辆水平调整"},
	{"1216", "电池安装，RGV举"},
	{"1217", "电池安装，加锁"},
	{"1218", "电池安装，RGV下降"},
	{"1219", "车辆下降"},
	{"1220", "亏电电池从缓存位到提升机"},
	{"1221", "亏电电池进电池仓"},
	{"1222", "亏电电池启动充电"},
	{"1223", "推杆回中"},
	{"1224", "开合门关闭"},
	{"1225", "车辆自检"},
	{"1226", "车辆上高压"},
	{"1227", "换电完成"},
	{"1228", "换电完成车辆驶离"},
}
var PUS3Steps = []ServiceStep{
	{"802002", "启动换电按键使能"},
	{"802003", "BMS刷写中"},
	{"802004", "推杆一次定位"},
	{"802005", "推杆二次定位&开合门打开"},
	{"802006", "RGV举升至工作位"},
	{"802007", "电磁铁吸合&解锁"},
	{"802008", "电磁铁释放&RGV下降"},
	{"802009", "开合门关闭&RGV平移至电池对接位"},
	{"802010", "前后导向条伸出"},
	{"802011", "电池从停车平台流转至左缓存位"},
	{"802012", "接驳机下降零点位&RGV挡块伸出&右缓存位挡块缩回"},
	{"802013", "电池从接驳机流转至停车平台"},
	{"802014", "前后导向条缩回&开合门打开"},
	{"802015", "RGV平移至加解锁位"},
	{"802016", "RGV举升至卡销位"},
	{"802017", "RGV举升至销子位&车身定位销伸出&平台阻挡块缩回"},
	{"802018", "RGV举升至工作位&车身定位销缩回"},
	{"802019", "电磁铁吸合&加锁"},
	{"802020", "电磁铁释放&RGV下降至原点位&车身定位销缩回"},
	{"802021", "推杆至零点位&开合门关闭&RGV平移至电池流转位"},
	{"802022", "水电插头缩回&堆垛机至目标仓对接位&接驳机举升"},
	{"802023", "货叉目标仓伸出&堆垛机升至高位&货叉缩回"},
	{"802024", "堆垛机至接驳机对接位"},
	{"802025", "货叉接驳机伸出&接驳机升至工作位&货叉缩回"},
	{"802026", "接驳机升至原点&平台挡块缩回"},
	{"802027", "前后导向条伸出&电池从左缓存位至接驳机"},
	{"802028", "接驳机举升至工作位"},
	{"802029", "货叉接驳机伸出&接驳机至原点位&货叉缩回"},
	{"802030", "堆垛机至目标仓&货叉高位伸出&堆垛机至低位&货叉缩回"},
	{"802031", "水电插头伸出"},
	{"802032", "堆垛机从目标仓至初始位"},
	{"802033", "车辆自检"},
	{"802034", "车辆上高压"},
	{"802035", "换电完成"},
	{"802036", "换电完成车辆驶离"},
}

var PUS4Steps = []ServiceStep{
	{"982003", "启动换电按键使能"},
	{"982005", "推杆一次定位"},
	{"982023", "水电插头缩回&堆垛机至目标仓对接位&接驳机举升"},
	{"982006", "推杆二次定位&开合门打开"},
	{"982007", "RGV举升至工作位"},
	{"982024", "货叉目标仓伸出&堆垛机升至高位&货叉缩回"},
	{"982008", "电磁铁吸合&解锁"},
	{"982025", "堆垛机至接驳机对接位"},
	{"982026", "货叉接驳机伸出&接驳机升至工作位&货叉缩回"},
	{"982027", "接驳机升至原点&平台挡块缩回"},
	{"982009", "电磁铁释放&RGV下降"},
	{"982010", "开合门关闭&RGV平移至电池对接位"},
	{"982011", "前后导向条伸出"},
	{"982012", "电池从停车平台流转至左缓存位"},
	{"982014", "电池从接驳机流转至停车平台"},
	{"982015", "前后导向条缩回&开合门打开"},
	{"982016", "RGV平移至加解锁位"},
	{"982017", "RGV举升至卡销位"},
	{"982018", "RGV举升至销子位&车身定位销伸出&平台阻挡块缩回"},
	{"982019", "RGV举升至工作位&车身定位销缩回"},
	{"982020", "电磁铁吸合&加锁"},
	{"982034", "车辆自检"},
	{"982021", "电磁铁释放&RGV下降至原点位&车身定位销缩回"},
	{"982022", "推杆至零点位&开合门关闭&RGV平移至电池流转位"},
	{"982035", "车辆上高压"},
	{"981002", "换电服务小结结束"},
	{"982036", "换电完成"},
	{"982028", "前后导向条伸出&电池从左缓存位至接驳机"},
	{"981003", "换电服务小结车辆驶离"},
	{"982037", "换电完成车辆驶离"},
}

type StepInfoStat struct {
	AlarmIdSet   map[string]bool
	ServiceIdSet map[string]bool
}

func initStepInfoStat(project string) (map[string]bool, map[string]StepInfoStat) {
	stepIds := map[string]bool{}
	stepInfoStat := map[string]StepInfoStat{}
	if project == "PowerSwap2" {
		for _, PUS2Step := range PUS2Steps {
			stepInfoStat[PUS2Step.StepCode] = StepInfoStat{
				AlarmIdSet:   map[string]bool{},
				ServiceIdSet: map[string]bool{},
			}
			stepIds[PUS2Step.StepCode] = true
		}
	}
	if project == "PUS3" {
		for _, PUS3Step := range PUS3Steps {
			stepInfoStat[PUS3Step.StepCode] = StepInfoStat{
				AlarmIdSet:   map[string]bool{},
				ServiceIdSet: map[string]bool{},
			}
			stepIds[PUS3Step.StepCode] = true
		}
	}
	if project == "PUS4" {
		for _, PUS4Step := range PUS4Steps {
			stepInfoStat[PUS4Step.StepCode] = StepInfoStat{
				AlarmIdSet:   map[string]bool{},
				ServiceIdSet: map[string]bool{},
			}
			stepIds[PUS4Step.StepCode] = true
		}
	}
	return stepIds, stepInfoStat
}

func (d *diagnosis) GetStuckServiceStepAnalysis() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.GetStuckServiceStepAnalysisRequest
			response model.Response
		)
		err := c.BindQuery(&request)
		if err != nil {
			log.CtxLog(c).Errorf("fail to GetStuckServiceStepAnalysis: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		stepIds, stepInfoStat := initStepInfoStat(request.Project)
		stepIdSlice := []string{}
		for stepId, _ := range stepIds {
			stepIdSlice = append(stepIdSlice, stepId)
		}

		filter := bson.D{util.SelectedTimeDuration("service_start_time", request.StartTime, request.EndTime)}
		filter = append(filter, bson.E{Key: "event_code", Value: bson.M{"$in": stepIdSlice}})
		if request.DeviceId != "" {
			deviceIds := strings.Split(request.DeviceId, ",")
			filter = append(filter, bson.E{"device_id", bson.M{"$in": deviceIds}})
		}
		cursor, err := d.watcher.Mongodb().Client.Database("diagnosis-management").Collection("stuck-step").Aggregate(c, mongo.Pipeline{
			bson.D{{"$match", filter}},
			bson.D{{"$group", bson.M{
				"_id":         "$event_code",
				"alarm_ids":   bson.M{"$addToSet": "$alarm_id"},
				"service_ids": bson.M{"$addToSet": "$service_id"},
			}}},
			bson.D{{"$project", bson.M{
				"event_code":  "$_id",
				"alarm_ids":   1,
				"service_ids": 1,
			}}},
		})
		if err != nil {
			log.CtxLog(c).Errorf("fail to GetStuckServiceStepAnalysis: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var aggreDatas []struct {
			EventCode  string   `json:"event_code" bson:"event_code"`
			AlarmIds   []string `json:"alarm_ids" bson:"alarm_ids"`
			ServiceIds []string `json:"service_ids" bson:"service_ids"`
		}
		if err = cursor.All(c, &aggreDatas); err != nil {
			log.CtxLog(c).Errorf("fail to GetStuckServiceStepAnalysis: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}

		for _, aggreData := range aggreDatas {
			for _, serviceId := range aggreData.ServiceIds {
				stepInfoStat[aggreData.EventCode].ServiceIdSet[serviceId] = true
			}
			for _, alarmId := range aggreData.AlarmIds {
				stepInfoStat[aggreData.EventCode].AlarmIdSet[alarmId] = true
			}
		}

		ct := ucmd.NewHttpClient(ucmd.HttpClient{
			URL: fmt.Sprintf("%s/device/v1/service-info/%v/list?start_time=%v&end_time=%v&page=1&size=1&descending=true",
				d.config.Welkin.BackendUrl, request.Project, request.StartTime, request.EndTime),
			Method: "GET",
		})
		body, statusCode, err := ct.Do()
		if err != nil {
			log.CtxLog(c).Errorf("fail to GetStuckServiceStepAnalysis: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		defer body.Close()
		data, err := io.ReadAll(body)
		if err != nil {
			log.CtxLog(c).Errorf("fail to GetStuckServiceStepAnalysis: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if statusCode != http.StatusOK {
			log.CtxLog(c).Errorf("fail to GetStuckServiceStepAnalysis: %v", err)
			um.FailWithInternalServerError(c, &response, "xx")
			return
		}
		var resp struct {
			ErrorCode int64  `json:"err_code"`
			Message   string `json:"message"`
			Total     int64  `json:"total"`
		}
		if err = json.Unmarshal(data, &resp); err != nil {
			log.CtxLog(c).Errorf("fail to GetStuckServiceStepAnalysis: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if resp.ErrorCode != 0 {
			log.CtxLog(c).Errorf("fail to GetStuckServiceStepAnalysis: %v", err)
			um.FailWithInternalServerError(c, &response, "xx")
			return
		}
		serviceCount := resp.Total
		steps := []ServiceStep{}
		if request.Project == "PowerSwap2" {
			steps = PUS2Steps
		}
		if request.Project == "PUS3" {
			steps = PUS3Steps
		}
		if request.Project == "PUS4" {
			steps = PUS4Steps
		}
		res := []model.StuckServiceStep{}
		for i, step := range steps {
			alarmIds := []string{}
			stuckServiceRate := float64(0)
			stuckServiceCount := len(stepInfoStat[step.StepCode].ServiceIdSet)
			if serviceCount != 0 {
				stuckServiceRate = float64(stuckServiceCount) / float64(serviceCount)
			}
			for alarmId, _ := range stepInfoStat[step.StepCode].AlarmIdSet {
				alarmIds = append(alarmIds, alarmId)
			}
			res = append(res, model.StuckServiceStep{
				StepNum:       i + 1,
				StepCode:      step.StepCode,
				StepName:      step.StepName,
				StepAlarmIds:  alarmIds,
				StepStuckRate: stuckServiceRate,
			})
		}
		response.Data = res
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) ListStuckService() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.ListStuckServiceRequest
			response model.ListStuckServiceResponse
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("list stuck service, parse query parameters, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		filter := bson.D{util.SelectedTimeDuration("service_start_time", request.StartTime, request.EndTime)}
		if request.DeviceId != "" {
			deviceIds := strings.Split(request.DeviceId, ",")
			filter = append(filter, bson.E{"device_id", bson.M{"$in": deviceIds}})
		}
		if request.Project != "" {
			filter = append(filter, bson.E{"project", request.Project})
		}
		if request.VehicleId != "" {
			filter = append(filter, bson.E{"vehicle_id", request.VehicleId})
		}
		if request.AnalysisStatus != "" {
			analysusStatus, err := strconv.ParseInt(request.AnalysisStatus, 10, 64)
			if err != nil {
				log.CtxLog(c).Errorf("list stuck service, err: %v", err)
				um.FailWithBadRequest(c, &response, "invalid analysus_status")
				return
			}
			cond := bson.E{"analysis_status", analysusStatus}
			if analysusStatus == 0 {
				cond = bson.E{"analysis_status", bson.M{"$in": []interface{}{nil, 0}}}
			}
			filter = append(filter, cond)
		}
		if request.AnalysisUser != "" {
			filter = append(filter, bson.E{"analysis_user", request.AnalysisUser})
		}
		if request.EvBrand != "" {
			evBrands := strings.Split(request.EvBrand, ",")
			evTypes := make([]string, 0)
			for _, brand := range evBrands {
				evTypes = append(evTypes, model.VehicleBrandMap[brand]...)
			}
			filter = append(filter, bson.E{Key: "ev_type", Value: bson.M{"$in": evTypes}})
		}
		if request.EvType != "" {
			filter = append(filter, bson.E{Key: "ev_type", Value: bson.M{"$in": strings.Split(request.EvType, ",")}})
		}
		byteData, total, err := d.watcher.Mongodb().NewMongoEntry(filter).ListByPagination("diagnosis-management", "stuck-service",
			client.Pagination{Limit: int64(request.Size), Offset: int64((request.Page - 1) * request.Size)},
			client.Ordered{Key: "service_start_time", Descending: request.Descending},
		)
		if err != nil {
			log.CtxLog(c).Errorf("list stuck service, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var records []umw.MongoStuckService
		if err = json.Unmarshal(byteData, &records); err != nil {
			log.CtxLog(c).Errorf("failed to unmarshal log file upload history, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		userIds := map[string]struct{}{}
		stuckServiceVOs := []*model.StuckServiceVO{}
		for _, record := range records {
			stuckServiceVO := convertStuckServicePO2VO(record)
			if stuckServiceVO.OnDutyPerson != "" {
				userIds[stuckServiceVO.OnDutyPerson] = struct{}{}
			}
			if stuckServiceVO.AnalysisUser != "" {
				userIds[stuckServiceVO.AnalysisUser] = struct{}{}
			}
			if stuckServiceVO.DeviceManager != "" {
				userIds[stuckServiceVO.DeviceManager] = struct{}{}
			}
			stuckServiceVOs = append(stuckServiceVOs, stuckServiceVO)
		}
		userAvatars, err := util.GetUserAvatar(d.config, userIds)
		if err != nil {
			log.CtxLog(c).Errorf("list stuck service, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		for _, stuckServiceVO := range stuckServiceVOs {
			stuckServiceVO.OnDutyPersonAvatarUrl = userAvatars[stuckServiceVO.OnDutyPerson]
			stuckServiceVO.AnalysisUserAvatarUrl = userAvatars[stuckServiceVO.AnalysisUser]
			stuckServiceVO.DeviceManagerAvatarUrl = userAvatars[stuckServiceVO.DeviceManager]
		}
		response.Data = stuckServiceVOs
		response.Total = total
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func calculateStuckStep(stuckService umw.MongoStuckService) []string {
	if len(stuckService.StuckAlarms) == 0 || len(stuckService.ServiceSteps) == 0 {
		return nil
	}
	res := []string{}
	stuckStepSet := map[string]bool{}
	for i, serviceStep := range stuckService.ServiceSteps {
		if i == 0 {
			continue
		}
		currentStep := stuckService.ServiceSteps[i-1]
		stepStartTime := stuckService.ServiceSteps[i-1].EventTimestamp
		stepEndTime := serviceStep.EventTimestamp
		for _, stuckAlarm := range stuckService.StuckAlarms {
			if stuckAlarm.CreateTs >= stepStartTime && stuckAlarm.CreateTs <= stepEndTime {
				_, found := stuckStepSet[currentStep.EventName]
				if found {
					continue
				}
				res = append(res, currentStep.EventName)
				stuckStepSet[currentStep.EventName] = true
			}
		}
	}
	return res
}

func convertStuckServicePO2VO(PO umw.MongoStuckService) *model.StuckServiceVO {
	deviceInfo, exist := cache.PowerSwapCache.GetSingleDevice(PO.DeviceId)
	if !exist {
		return &model.StuckServiceVO{}
	}
	stuckSteps := calculateStuckStep(PO)
	stuckServiceVO := &model.StuckServiceVO{
		ServiceId:        PO.ServiceId,
		VehicleId:        PO.VehicleId,
		EvType:           PO.EvType,
		EvBrand:          model.VehicleTypeBrandMap[PO.EvType],
		ServiceStartTime: PO.ServiceStartTime,
		ServiceEndTime:   PO.ServiceEndTime,
		DeviceId:         PO.DeviceId,
		Description:      deviceInfo.Description,
		Project:          deviceInfo.Project,
		//OnDuty:
		AnalysisStatus: int(PO.AnalysisStatus),
		AnalysisUser:   PO.AnalysisUser,
		//SoftwareVersion
		//CarLeaveTime
		//CarBrand
		//CarModel
		//CarPlatform
		//OnDutyPerson
		DeviceManager: deviceInfo.DeviceSupervisor,
		// TODO need add
		//AlarmLocation
		//AlarmStep: strings.Join(stuckSteps, ","),
		AlarmStepName: strings.Join(stuckSteps, " , "),
		//HardwareUrl
		//SoftwareUrl
		Aware:   PO.Aware,
		BugUrl:  PO.BugUrl,
		JiraUrl: PO.JiraUrl,
		Result:  PO.Result,
	}
	return stuckServiceVO
}

func (d *diagnosis) UpdateStuckService() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.UpdateStuckServiceRequest
			response model.Response
		)
		err := c.BindJSON(&request)
		if err != nil {
			log.CtxLog(c).Errorf("UpdateStuckService, err: %v", err)
			um.FailWithBadRequest(c, &response, "invalid data")
			return
		}
		filter := bson.D{
			bson.E{"service_id", request.ServiceId},
		}
		err = d.watcher.Mongodb().NewMongoEntry(filter).UpdateOne("diagnosis-management", "stuck-service", bson.M{"$set": bson.M{
			"bug_url":         request.BugUrl,
			"jira_url":        request.JiraUrl,
			"aware":           request.Aware,
			"result":          request.Result,
			"analysis_status": request.AnalysisStatus,
			"analysis_user":   request.AnalysisUser,
		}},
			false)
		if err != nil {
			log.CtxLog(c).Errorf("UpdateStuckService, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) ListStuckAlarmId() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.Response
		)
		project := c.Param("project")
		alarmData := cache.StuckAlarmInfoCache.GetAllStuckAlarms(project)
		if len(alarmData) == 0 {
			errMsg := fmt.Sprintf("list stuck alarm id, fail to get all stuck alarms, project: %s", project)
			log.CtxLog(c).Errorf(errMsg)
			um.FailWithBadRequest(c, &response, errMsg)
			return
		}
		results := make(map[string]string)
		description := c.Query("description")
		mu := sync.Mutex{}
		execFunc := func(i int) {
			mu.Lock()
			defer mu.Unlock()
			// 匹配前端传入的字符串
			if c.Query("lang") == "en" {
				if strings.Contains(alarmData[i].OccAlarmDescriptionEn, description) || strings.Contains(alarmData[i].AlarmId, description) {
					if alarmData[i].OccAlarmDescriptionEn == "" {
						results[alarmData[i].AlarmId] = alarmData[i].Description
					} else {
						results[alarmData[i].AlarmId] = alarmData[i].OccAlarmDescriptionEn
					}
				}
			} else {
				if strings.Contains(alarmData[i].Description, description) || strings.Contains(alarmData[i].AlarmId, description) {
					results[alarmData[i].AlarmId] = alarmData[i].Description
				}
			}
		}
		if description != "" {
			ucmd.ParallelizeExec(len(alarmData), execFunc)
		}
		response.Data = results
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) ListJira() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.ListJiraRequest
			response model.Response
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("list jira, parse query parameters, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		queryPangea := func(queryKey string, res map[string]model.JiraInfo) error {
			url := fmt.Sprintf("%s/pangea/v1/transaction-management/issue/list?type=%s&%s=%s&page_no=1&page_size=999999", d.config.Welkin.PangeaUrl, request.Type, queryKey, request.Key)
			ct := ucmd.NewHttpClient(ucmd.HttpClient{
				URL:    url,
				Method: "GET",
			})
			body, _, err := ct.Do()
			if err != nil {
				log.CtxLog(c).Errorf("list jira, fail to query pangea, err: %v, url: %s", err, url)
				return err
			}
			defer body.Close()
			data, err := io.ReadAll(body)
			if err != nil {
				log.CtxLog(c).Errorf("list jira, fail to read body, err: %v, url: %s", err, url)
				return err
			}
			var resp struct {
				um.Base
				Data []model.JiraInfo `json:"data"`
			}
			if err = json.Unmarshal(data, &resp); err != nil {
				log.CtxLog(c).Errorf("list jira, fail to read body, err: %v, url: %s, resp data: %s", err, url, string(data))
				return err
			}
			log.CtxLog(c).Infof("url: %s, resp: %s", url, ucmd.ToJsonStrIgnoreErr(resp))
			for _, item := range resp.Data {
				res[item.JiraId] = item
			}

			return nil
		}

		jiraMap := make(map[string]model.JiraInfo)
		if err := queryPangea("jira_id", jiraMap); err != nil {
			log.CtxLog(c).Errorf("list jira, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if err := queryPangea("jira_name", jiraMap); err != nil {
			log.CtxLog(c).Errorf("list jira, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var jiraList []model.JiraInfo
		for _, item := range jiraMap {
			jiraList = append(jiraList, item)
		}
		response.Data = jiraList
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) ListStuckAlarmInfo() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.ListStuckAlarmRequest
			response model.ListStuckAlarmInfoResponse
		)
		project := c.Param("project")
		if project != umw.PowerSwap2 && project != umw.PUS3 && project != umw.PUS4 {
			log.CtxLog(c).Errorf("list stuck alarm info, invalid project: %s", project)
			um.FailWithBadRequest(c, &response, "invalid project")
			return
		}
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("list stuck alarm info, parse query parameters, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		util.SetURIParamDefault(&request.CommonUriParam)

		responseData, avatars, err := d.calculateStuckAlarms(c, project, request)
		if err != nil {
			log.CtxLog(c).Errorf("list stuck alarm info, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		delete(avatars, "")
		delete(avatars, "/")
		response.Avatar = avatars

		// 排序分页
		sort.Slice(responseData, func(i, j int) bool {
			if responseData[i].AlarmRate == responseData[j].AlarmRate {
				return responseData[i].AlarmId < responseData[j].AlarmId
			}
			return responseData[i].AlarmRate > responseData[j].AlarmRate
		})
		response.Total = len(responseData)
		start := (request.Page - 1) * request.Size
		end := request.Page * request.Size
		if end > len(responseData) {
			end = len(responseData)
		}
		index := (request.Page - 1) * request.Size
		for _, info := range responseData[start:end] {
			index++
			info.No = index
		}
		response.Data = responseData[start:end]

		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

// 各个挂车告警id的总告警次数和挂车订单数
func (d *diagnosis) countStuckTimes(c *gin.Context, project string, request model.ListStuckAlarmRequest) (map[string]model.StuckAlarmInfo, error) {
	filter := bson.M{
		"project":   project,
		"create_ts": bson.M{"$gte": request.StartTime, "$lte": request.EndTime},
	}
	if request.DeviceId != nil {
		deviceIds := strings.Split(*request.DeviceId, ",")
		filter["device_id"] = bson.M{"$in": deviceIds}
	}
	pipeline := mongo.Pipeline{
		bson.D{{"$match", filter}},
		bson.D{{"$group", bson.M{
			"_id":         "$alarm_id",
			"total_times": bson.M{"$sum": 1},
			"service_ids": bson.M{"$addToSet": bson.M{
				"$cond": bson.M{
					"if":   bson.M{"$ne": bson.A{"$service_id", ""}},
					"then": "$service_id",
					"else": "$$REMOVE",
				},
			}},
		}}},
		bson.D{{"$project", bson.M{
			"alarm_id":    "$_id",
			"total_times": 1,
			"stuck_times": bson.M{"$size": "$service_ids"},
		}}},
	}
	var resList []model.StuckAlarmInfo
	err := d.watcher.Mongodb().NewMongoEntry().Aggregate(umw.DiagnosisManagement, "stuck-alarm", pipeline, &resList)
	if err != nil {
		log.CtxLog(c).Errorf("calculate stuck alarms, fail to aggregate, err: %v, pipeline: %s", err, ucmd.ToJsonStrIgnoreErr(pipeline))
		return nil, err
	}
	res := make(map[string]model.StuckAlarmInfo)
	for _, item := range resList {
		res[item.AlarmId] = item
	}
	return res, nil
}

// 计算总告警次数、挂车订单数、挂车率
func (d *diagnosis) calculateStuckAlarms(c *gin.Context, project string, request model.ListStuckAlarmRequest) ([]*model.StuckAlarmInfo, map[string]string, error) {
	var stuckAlarmInfoList []*umw.MongoStuckAlarmInfo
	if request.AlarmId != nil {
		for _, alarmId := range strings.Split(*request.AlarmId, ",") {
			info, found := cache.StuckAlarmInfoCache.GetSingleStuckAlarm(project, alarmId)
			if !found {
				log.CtxLog(c).Warnf("calculate stuck alarms, invalid alarm id: %s", alarmId)
				continue
			}
			stuckAlarmInfoList = append(stuckAlarmInfoList, info)
		}
	} else {
		stuckAlarmInfoList = cache.StuckAlarmInfoCache.GetAllStuckAlarms(project)
	}
	if request.AlarmLevel != nil {
		alarmList := make([]*umw.MongoStuckAlarmInfo, 0)
		for _, item := range stuckAlarmInfoList {
			if item.AlarmLevel != *request.AlarmLevel {
				continue
			}
			alarmList = append(alarmList, item)
		}
		stuckAlarmInfoList = alarmList
	}

	// 总告警次数和挂车订单数
	calculateAlarmTotalMap, err := d.countStuckTimes(c, project, request)
	if err != nil {
		return nil, nil, err
	}

	// 挂车率
	// 计算订单数
	filter := make(bson.M)
	if request.DeviceId != nil {
		deviceIds := strings.Split(*request.DeviceId, ",")
		filter["device_id"] = bson.M{"$in": deviceIds}
	}
	serviceCnt, err := d.watcher.Mongodb().CountServices(project, request.StartTime, request.EndTime, filter)
	if err != nil {
		log.CtxLog(c).Errorf("calculate stuck alarms, %v", err.Error())
		return nil, nil, err
	}
	g := ucmd.NewErrGroup(c, 1000)
	responseData := make([]*model.StuckAlarmInfo, len(stuckAlarmInfoList))
	userList := make(map[string]struct{})
	for i := range stuckAlarmInfoList {
		alarmInfo := stuckAlarmInfoList[i]
		userList[alarmInfo.OwnerSoftware] = struct{}{}
		userList[alarmInfo.OwnerHardware] = struct{}{}
		idx := i
		g.GoRecover(func() error {
			item := &model.StuckAlarmInfo{
				AlarmIdDescription: alarmInfo.Description,
				AlarmId:            alarmInfo.AlarmId,
				AlarmType:          alarmInfo.AlarmType,
				AlarmLevel:         alarmInfo.AlarmLevel,
				StuckTimes:         calculateAlarmTotalMap[alarmInfo.AlarmId].StuckTimes,
				TotalTimes:         calculateAlarmTotalMap[alarmInfo.AlarmId].TotalTimes,
				OwnerSoftware:      alarmInfo.OwnerSoftware,
				OwnerHardware:      alarmInfo.OwnerHardware,
			}
			if request.Lang == "en" && alarmInfo.OccAlarmDescriptionEn != "" {
				item.AlarmIdDescription = alarmInfo.OccAlarmDescriptionEn
			} else {
				item.AlarmIdDescription = alarmInfo.Description
			}
			if serviceCnt > 0 {
				item.AlarmRate = float64(item.StuckTimes) / float64(serviceCnt)
			}
			responseData[idx] = item
			return nil
		})
	}
	if err = g.Wait(); err != nil {
		log.CtxLog(c).Errorf("calculate stuck alarms, goroutine err: %v", err)
		return nil, nil, err
	}
	userAvatars, err := util.GetUserAvatar(d.config, userList)
	if err != nil {
		log.CtxLog(c).Errorf("calculate stuck alarms, fail to get user avatars, err: %v", err)
		return responseData, nil, err
	}
	return responseData, userAvatars, nil
}

func (d *diagnosis) ListStuckAlarms() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.ListStuckAlarmRequest
			response model.AddTotalResponse
		)
		project := c.Param("project")
		if project != umw.PowerSwap2 && project != umw.PUS3 && project != umw.PUS4 {
			log.CtxLog(c).Errorf("list stuck alarms, invalid project: %s", project)
			um.FailWithBadRequest(c, &response, "invalid project")
			return
		}
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("list stuck alarms, parse query parameters, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		util.SetURIParamDefault(&request.CommonUriParam)

		stuckAlarmRecords, total, err := d.ListStuckAlarmRecords(c, project, request)
		if err != nil {
			log.CtxLog(c).Errorf("list stuck alarms, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}

		index := (request.Page - 1) * request.Size
		for _, record := range stuckAlarmRecords {
			index++
			record.No = index
		}

		response.Total = int(total)
		response.Data = stuckAlarmRecords
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) ListStuckAlarmRecords(c *gin.Context, project string, request model.ListStuckAlarmRequest) (res []*model.StuckAlarmRecord, total int64, err error) {
	filter := bson.D{
		{"project", project},
		{"create_ts", bson.M{"$gte": request.StartTime, "$lte": request.EndTime}},
		{"alarm_id", bson.M{"$nin": []string{"715521", "300403"}}},
		{"service_id", bson.M{"$ne": ""}},
	}
	if request.DeviceId != nil {
		deviceIds := strings.Split(*request.DeviceId, ",")
		filter = append(filter, bson.E{"device_id", bson.M{"$in": deviceIds}})
	}
	if request.AlarmId != nil {
		filter = append(filter, bson.E{Key: "alarm_id", Value: *request.AlarmId})
	}
	if request.ServiceId != nil {
		filter = append(filter, bson.E{Key: "service_id", Value: *request.ServiceId})
	}
	var byteData []byte
	byteData, total, err = client.GetWatcher().Mongodb().NewMongoEntry(filter).ListByPagination(
		umw.DiagnosisManagement,
		"stuck-alarm",
		client.Pagination{Limit: int64(request.Size), Offset: int64((request.Page - 1) * request.Size)},
		client.Ordered{Key: "create_ts", Descending: true},
	)
	if err != nil {
		log.CtxLog(c).Errorf("fail to list mongo records, err: %v, filter: %s", err, ucmd.ToJsonStrIgnoreErr(filter))
		return
	}
	var mongoRecords []umw.MongoStuckAlarmRecord
	if err = json.Unmarshal(byteData, &mongoRecords); err != nil {
		log.CtxLog(c).Errorf("fail to unmarshal mongo records, err: %v, data: %s", err, string(byteData))
		return
	}
	if len(mongoRecords) == 0 {
		return
	}
	var serviceIdList []string
	for _, item := range mongoRecords {
		serviceIdList = append(serviceIdList, item.ServiceId)
	}
	services, err := client.GetWatcher().Mongodb().GetServiceTime(umw.DiagnosisManagement, "stuck-service", serviceIdList)
	if err != nil {
		log.CtxLog(c).Errorf("fail to get service time, err: %v", err)
		return
	}
	res = make([]*model.StuckAlarmRecord, len(mongoRecords))
	for i := range mongoRecords {
		item := &model.StuckAlarmRecord{
			AlarmId:   mongoRecords[i].AlarmId,
			DeviceId:  mongoRecords[i].DeviceId,
			AlarmType: mongoRecords[i].AlarmType,
			CreateTs:  mongoRecords[i].CreateTs,
			ClearTs:   mongoRecords[i].ClearTs,
			ServiceId: mongoRecords[i].ServiceId,
		}
		alarmItem, ok := cache.StuckAlarmInfoCache.GetSingleStuckAlarm(project, item.AlarmId)
		if !ok {
			log.CtxLog(c).Warnf("invalid stuck alarm id, item: %s", ucmd.ToJsonStrIgnoreErr(*item))
		} else {
			if request.Lang == "en" && alarmItem.OccAlarmDescriptionEn != "" {
				item.AlarmIdDescription = alarmItem.OccAlarmDescriptionEn
			} else {
				item.AlarmIdDescription = alarmItem.Description
			}
			item.AlarmLevel = alarmItem.AlarmLevel
		}
		deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(item.DeviceId)
		if !found {
			log.CtxLog(c).Warnf("invalid device id, item: %s", ucmd.ToJsonStrIgnoreErr(*item))
		} else {
			item.Description = deviceInfo.Description
			item.ServiceStartTime = services[item.ServiceId].StartTime
			item.ServiceEndTime = services[item.ServiceId].EndTime
		}
		if item.ClearTs != 0 {
			// 告警清除
			item.State = 1
		} else {
			// 告警产生
			item.State = 2
		}
		res[i] = item
	}
	return
}

func (d *diagnosis) ListStuckAlarmDistribution() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.CommonUriInTimeRangeParam
			response model.ListStuckAlarmDistributionResponse
		)
		alarmId := c.Param("alarm_id")
		project := c.Param("project")
		if project != umw.PowerSwap2 && project != umw.PUS3 && project != umw.PUS4 {
			log.CtxLog(c).Errorf("list stuck alarm distribution, invalid project: %s", project)
			um.FailWithBadRequest(c, &response, "invalid project")
			return
		}
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("list stuck alarm distribution, parse query parameters, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		res, err := d.calculateStuckAlarmDistribution(c, request.StartTime, request.EndTime, alarmId)
		if err != nil {
			log.CtxLog(c).Errorf("list stuck alarm distribution, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}

		response.StuckTimes = res.StuckTimes
		response.TotalTimes = res.TotalTimes
		response.AlarmId = alarmId
		alarmInfo, found := cache.StuckAlarmInfoCache.GetSingleStuckAlarm(project, alarmId)
		if found {
			if request.Lang == "en" && alarmInfo.OccAlarmDescriptionEn != "" {
				response.AlarmIdDescription = alarmInfo.OccAlarmDescriptionEn
			} else {
				response.AlarmIdDescription = alarmInfo.Description
			}
		}
		var distributionData []model.StuckAlarmDistributionData
		deviceMap := partitionByBoundaries(res.Devices)
		for i := len(model.StuckAlarmProportionKeys) - 1; i >= 0; i-- {
			key := model.StuckAlarmProportionKeys[i]
			distributionData = append(distributionData, model.StuckAlarmDistributionData{
				Rate:        key,
				DeviceCount: int64(len(deviceMap[key])),
				Devices:     deviceMap[key],
			})
		}
		response.Data = distributionData

		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) AsynExcuteHiveStuckService() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.Response
		var requestData struct {
			Date        string `json:"date"`
			DoDailyStat bool   `json:"do_daily_stat"`
		}
		if err := c.BindJSON(&requestData); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		copyedGinCtx := c.Copy()
		go func() {
			defer ucmd.RecoverPanic()
			stuckDiagnosisHandler := StuckDiagnosisHandler{
				Watcher:      client.GetWatcher(),
				Cfg:          *config.Cfg,
				Projects:     []string{"pus2", "pus3", "pus4"},
				TimeLocation: util.GetTimeLoc(),
			}
			err := stuckDiagnosisHandler.CalculateUseHivedata(copyedGinCtx, requestData.Date, requestData.DoDailyStat)
			if err != nil {
				log.CtxLog(copyedGinCtx).Errorf("stuckDiagnosisHandler.CalculateUseHivedata err. err:%v", err)
				return
			}
		}()
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) AsynExcuteStuckServiceDailyStat() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.Response
		var requestData struct {
			Date string `json:"date"`
		}
		if err := c.BindJSON(&requestData); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		copyedGinCtx := c.Copy()
		go func() {
			defer ucmd.RecoverPanic()
			dailyStuckDiagnosisHandler := DailyStuckDiagnosisHandler{
				Watcher:      client.GetWatcher(),
				TimeLocation: util.GetTimeLoc(),
				Cfg:          *config.Cfg,
			}
			err := dailyStuckDiagnosisHandler.Process(copyedGinCtx, requestData.Date)
			if err != nil {
				log.CtxLog(copyedGinCtx).Errorf("dailyStuckDiagnosisHandler.Process err. err:%v", err)
				return
			}
		}()
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

// 计算所有站的针对某个告警id的挂车单量和占比
func (d *diagnosis) calculateStuckAlarmDistribution(c *gin.Context, startTs, endTs int64, alarmId string) (model.DeviceStuckServiceData, error) {
	pipeline := mongo.Pipeline{
		bson.D{{"$match", bson.M{
			"create_ts":  bson.M{"$gte": startTs, "$lte": endTs},
			"alarm_id":   alarmId,
			"service_id": bson.M{"$ne": ""},
		}}},
		bson.D{{"$group", bson.M{
			"_id": "$device_id",
			"service_ids": bson.M{"$addToSet": bson.M{
				"$cond": bson.M{
					"if":   bson.M{"$ne": bson.A{"$service_id", ""}},
					"then": "$service_id",
					"else": "$$REMOVE",
				},
			}},
			"alarm_count": bson.M{"$sum": 1},
		}}},
		bson.D{{"$project", bson.M{
			"_id":           1,
			"alarm_count":   1,
			"service_count": bson.M{"$size": "$service_ids"},
		}}},
		bson.D{{"$group", bson.M{
			"_id":         primitive.Null{},
			"total_times": bson.M{"$sum": "$alarm_count"},
			"stuck_times": bson.M{"$sum": "$service_count"},
			"devices": bson.M{
				"$push": bson.M{
					"device_id":   "$_id",
					"total_times": "$alarm_count",
					"stuck_times": "$service_count",
				},
			},
		}}},
		bson.D{{"$project", bson.M{
			"_id":         0,
			"total_times": "$total_times",
			"stuck_times": "$stuck_times",
			"devices": bson.M{
				"$map": bson.M{
					"input": "$devices",
					"as":    "device",
					"in": bson.M{
						"device_id":   "$$device.device_id",
						"total_times": "$$device.total_times",
						"stuck_times": "$$device.stuck_times",
						"proportion": bson.M{
							"$cond": bson.A{
								bson.M{"$eq": bson.A{"$stuck_times", 0}},
								0,
								bson.M{"$divide": bson.A{"$$device.stuck_times", "$stuck_times"}},
							},
						},
					},
				},
			},
		}}},
	}
	var resList []model.DeviceStuckServiceData
	var res model.DeviceStuckServiceData
	err := d.watcher.Mongodb().NewMongoEntry().Aggregate(umw.DiagnosisManagement, "stuck-alarm", pipeline, &resList)
	if err != nil {
		log.CtxLog(c).Errorf("calculate stuck alarm distribution, fail to aggregate, err: %v, pipeline: %s", err, ucmd.ToJsonStrIgnoreErr(pipeline))
		return res, err
	}
	if len(resList) > 0 {
		res = resList[0]
	}
	devices := res.Devices
	sort.Slice(devices, func(i, j int) bool {
		if devices[i].StuckTimes == devices[j].StuckTimes {
			return devices[i].DeviceId < devices[j].DeviceId
		}
		return devices[i].StuckTimes > devices[j].StuckTimes
	})
	res.Devices = devices
	return res, nil
}

// 划分不同挂车单量占比区间的站
func partitionByBoundaries(devices []model.DeviceStuckServiceCount) (res map[string][]model.DeviceStuckServiceCount) {
	res = make(map[string][]model.DeviceStuckServiceCount)
	idx := 0
	deviceIdx := 0
	existDevices := make(map[string]struct{})
	// 因为要给每条记录加上description，所以只能遍历
	for deviceIdx < len(devices) && idx < len(model.StuckAlarmProportion) {
		item := devices[deviceIdx]
		if item.Proportion < model.StuckAlarmProportion[idx] {
			idx += 1
			continue
		}
		existDevices[item.DeviceId] = struct{}{}
		deviceIdx += 1
		deviceInfo, ok := cache.PowerSwapCache.GetSingleDevice(item.DeviceId)
		if ok {
			item.Description = deviceInfo.Description
		} else {
			continue
		}
		res[model.StuckAlarmProportionKeys[idx]] = append(res[model.StuckAlarmProportionKeys[idx]], item)
	}
	//// 将挂车订单数为0的站也写入
	//lastKey := model.StuckAlarmProportionKeys[len(model.StuckAlarmProportionKeys)-1]
	//zeroDevices := make([]model.DeviceStuckServiceCount, 0)
	//for _, deviceInfo := range deviceInfoCache.GetAllDevices() {
	//	if deviceInfo.Description == "" || strings.HasPrefix(deviceInfo.Description, "设备名称") {
	//		continue
	//	}
	//	if _, found := existDevices[deviceInfo.DeviceId]; found {
	//		continue
	//	}
	//	zeroDevices = append(zeroDevices, model.DeviceStuckServiceCount{
	//		DeviceId:    deviceInfo.DeviceId,
	//		Description: deviceInfo.Description,
	//		StuckTimes:  0,
	//		Proportion:  0,
	//	})
	//}
	//sort.Slice(zeroDevices, func(i, j int) bool {
	//	return zeroDevices[i].DeviceId < zeroDevices[j].DeviceId
	//})
	//res[lastKey] = append(res[lastKey], zeroDevices...)
	return
}

func (d *diagnosis) GetHealthWeeklyData() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  health.DeviceHealthRequest
			response health.GetHealthWeeklyDataResponse
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("parse query parameters, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		project := c.Param("project")
		if project != umw.PUS3 {
			log.CtxLog(c).Errorf("invalid project: %s", project)
			um.FailWithBadRequest(c, &response, "invalid project")
			return
		}
		h, err := health.NewDeviceHealth(project)
		if err != nil {
			log.CtxLog(c).Errorf("fail to NewDeviceHealth, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response, err = h.PrepareHealthWeeklyData(c, request.StartTime, request.EndTime)
		if err != nil {
			log.CtxLog(c).Errorf("fail to PrepareHealthWeeklyData, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) GetHealthTailDevices() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  health.DeviceHealthRequest
			response health.GetHealthTailDevicesResponse
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("parse query parameters, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		project := c.Param("project")
		if project != umw.PUS3 {
			log.CtxLog(c).Errorf("invalid project: %s", project)
			um.FailWithBadRequest(c, &response, "invalid project")
			return
		}
		h, err := health.NewDeviceHealth(project)
		if err != nil {
			log.CtxLog(c).Errorf("fail to NewDeviceHealth, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		data, total, err := h.PrepareHealthTailDevices(c, health.DeviceHealthCond{
			CommonUriParam: request.CommonUriParam,
			StartTime:      request.StartTime,
			EndTime:        request.EndTime,
			DeviceId:       request.DeviceId,
			IsPatchOrder:   request.IsPatchOrder,
		})
		if err != nil {
			log.CtxLog(c).Errorf("fail to PrepareHealthTailDevices, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Total = total
		response.Data = data
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) GetHealthDetail() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  health.GetHealthDetailRequest
			response health.GetHealthDetailResponse
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("parse query parameters, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		project := c.Param("project")
		if project != umw.PUS3 {
			log.CtxLog(c).Errorf("invalid project: %s", project)
			um.FailWithBadRequest(c, &response, "invalid project")
			return
		}
		h, err := health.NewDeviceHealth(project)
		if err != nil {
			log.CtxLog(c).Errorf("fail to NewDeviceHealth, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response, err = h.PrepareHealthDetail(c, request)
		if err != nil {
			log.CtxLog(c).Errorf("fail to PrepareHealthTailDevices, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if !request.Download {
			um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
			return
		}
		fileName := fmt.Sprintf("device-health-%d.csv", time.Now().UnixMilli())
		csvRecords := make([][]string, len(response.Data)+1)
		csvRecords[0] = []string{"设备ID", "设备名称", "设备类型", "值守状态", "总体健康度", "传感器健康度", "伺服健康度", "充电模块健康度", "日期"}
		for i, r := range response.Data {
			csvRecords[i+1] = []string{
				r.DeviceId,
				r.Description,
				r.Project,
				r.OnDuty,
				fmt.Sprintf("%v", r.HealthScore),
				fmt.Sprintf("%v", r.SensorHealthScore),
				fmt.Sprintf("%v", r.ServoHealthScore),
				fmt.Sprintf("%v", r.ChargeHealthScore),
				util.TsString(r.Day),
			}
		}
		buffer := &bytes.Buffer{}
		buffer.WriteString("\xEF\xBB\xBF")
		cw := csv.NewWriter(buffer)
		if err = cw.WriteAll(csvRecords); err != nil {
			log.CtxLog(c).Errorf("fail to write to csv, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		c.Writer.Header().Set("Content-Type", "text/csv")
		util.Download(c, fileName, buffer.Bytes())
	}
}

func (d *diagnosis) GetSingleDeviceHealthTrend() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.CommonUriInTimeRangeParam
			response health.GetSingleDeviceHealthTrendResponse
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("parse query parameters, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		deviceId := c.Param("device_id")
		project := c.Param("project")
		if project != umw.PUS3 {
			log.CtxLog(c).Errorf("invalid project: %s", project)
			um.FailWithBadRequest(c, &response, "invalid project")
			return
		}
		h, err := health.NewDeviceHealth(project)
		if err != nil {
			log.CtxLog(c).Errorf("fail to NewDeviceHealth, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		h.SetTimeRange(request.StartTime, request.EndTime)
		response, err = h.GetSingleDeviceHealthTrend(c, deviceId)
		if err != nil {
			log.CtxLog(c).Errorf("fail to GetSingleDeviceHealthTrend, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) GetSingleDeviceServoHealth() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  health.GetSingleDeviceHealthRequest
			response health.GetSingleDeviceServoHealthResponse
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("parse query parameters, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		deviceId := c.Param("device_id")
		project := c.Param("project")
		if project != umw.PUS3 {
			log.CtxLog(c).Errorf("invalid project: %s", project)
			um.FailWithBadRequest(c, &response, "invalid project")
			return
		}
		h, err := health.NewDeviceHealth(project)
		if err != nil {
			log.CtxLog(c).Errorf("fail to NewDeviceHealth, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		h.SetTimeRange(request.StartTime, request.EndTime)
		servoDetail, err := h.GetSingleDeviceServo(c, deviceId, request.Day)
		if err != nil {
			log.CtxLog(c).Errorf("fail to GetSingleDeviceServo, %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = servoDetail
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) GetSingleDeviceSensorHealth() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  health.GetSingleDeviceHealthRequest
			response health.GetSingleDeviceSensorHealthResponse
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("parse query parameters, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		deviceId := c.Param("device_id")
		project := c.Param("project")
		if project != umw.PUS3 {
			log.CtxLog(c).Errorf("invalid project: %s", project)
			um.FailWithBadRequest(c, &response, "invalid project")
			return
		}
		h, err := health.NewDeviceHealth(project)
		if err != nil {
			log.CtxLog(c).Errorf("fail to NewDeviceHealth, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		sensorDetail, err := h.GetSingleDeviceSensor(c, deviceId, request.Day)
		if err != nil {
			log.CtxLog(c).Errorf("fail to GetSingleDeviceSensor, %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = sensorDetail
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) GetSingleDeviceChargeHealth() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  health.GetSingleDeviceHealthRequest
			response health.GetSingleDeviceChargeHealthResponse
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("parse query parameters, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		deviceId := c.Param("device_id")
		project := c.Param("project")
		if project != umw.PUS3 {
			log.CtxLog(c).Errorf("invalid project: %s", project)
			um.FailWithBadRequest(c, &response, "invalid project")
			return
		}
		h, err := health.NewDeviceHealth(project)
		if err != nil {
			log.CtxLog(c).Errorf("fail to NewDeviceHealth, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		chargeDetail, err := h.GetSingleDeviceCharge(c, deviceId, request.Day, request.GroupId)
		if err != nil {
			log.CtxLog(c).Errorf("fail to GetSingleDeviceCharge, %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = chargeDetail
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) ListHealthWorksheet() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  health.WorksheetRequest
			response health.ListHealthWorksheetResponse
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("ListHealthWorksheet, parse query parameters, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		w := &health.Worksheet{}
		util.SetURIParamDefault(&request.CommonUriParam)
		res, total, err := w.ListHealthWorksheet(c, health.WorksheetCond{
			StartTs:         request.StartTime,
			EndTs:           request.EndTime,
			Project:         c.Param("project"),
			DeviceId:        request.DeviceId,
			WorksheetStatus: request.WorksheetStatus,
			CommonCond: model.CommonCond{
				Page: int64(request.Page),
				Size: int64(request.Size),
			},
		})
		if err != nil {
			log.CtxLog(c).Errorf("ListHealthWorksheet, fail to list worksheet: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = health.ConvertWorksheetDO2VO(res)
		response.Total = total
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) GetHealthWorksheetStatistics() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  health.WorksheetRequest
			response model.Response
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("GetHealthWorksheetStatistics, parse query parameters, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		w := &health.Worksheet{}
		res, err := w.GetHealthWorksheetStatistics(c, health.WorksheetCond{
			StartTs:         request.StartTime,
			EndTs:           request.EndTime,
			Project:         c.Param("project"),
			DeviceId:        request.DeviceId,
			WorksheetStatus: request.WorksheetStatus,
		})
		if err != nil {
			log.CtxLog(c).Errorf("GetHealthWorksheetStatistics, fail to get worksheet statistics: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = res
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) ListSatisfyData() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  domain_service.ListSatisfyRequest
			response domain_service.ListSatisfyResponse
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("ListSatisfyData, parse query parameters, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if err := util.CheckTimeRange(request.StartTime, request.EndTime); err != nil {
			log.CtxLog(c).Errorf("ListSatisfyData, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		util.SetURIParamDefault(&request.CommonUriParam)
		satisfy := &domain_service.Satisfy{}
		satisfyData, total, err := satisfy.ListSatisfyData(c, request)
		if err != nil {
			log.CtxLog(c).Errorf("ListSatisfyData, fail to list satisfy data: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		// 设备类型->服务列表
		serviceList := make(map[string][]string)
		for _, record := range satisfyData {
			serviceList[record.Project] = append(serviceList[record.Project], record.ServiceId)
		}
		serviceMap := make(map[string]domain_service.Service)
		for project, serviceIds := range serviceList {
			srv := domain_service.Service{}
			var services []domain_service.Service
			services, _, err = srv.ListServiceForSatisfy(c, domain_service.ListServiceForSatisfyCond{
				Project:   project,
				ServiceId: serviceIds,
			})
			if err != nil {
				log.CtxLog(c).Errorf("ListSatisfyData, fail to list service data: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			for _, record := range services {
				serviceMap[record.ServiceId] = record
			}
		}
		for _, record := range satisfyData {
			reportStatus := "adopt"
			if record.ReportStatus != "" {
				reportStatus = record.ReportStatus
			}
			response.Data = append(response.Data, domain_service.ListSatisfyData{
				CommentId:        record.CommentId,
				CommentTime:      record.CommentTime,
				DiagnosisTag:     record.DiagnosisTag,
				UserTag:          record.UserTag,
				SwapDuration:     serviceMap[record.ServiceId].ServiceEndTime - serviceMap[record.ServiceId].ServiceStartTime,
				QueueDuration:    serviceMap[record.ServiceId].ServiceStartTime - serviceMap[record.ServiceId].OrderStartTime,
				OrderStartTime:   serviceMap[record.ServiceId].OrderStartTime,
				ServiceStartTime: serviceMap[record.ServiceId].ServiceStartTime,
				ServiceEndTime:   serviceMap[record.ServiceId].ServiceEndTime,
				Score:            record.Score,
				DeviceId:         record.DeviceId,
				Description:      record.Description,
				IsValid:          record.IsValid,
				ServiceId:        record.ServiceId,
				OrderId:          record.OrderId,
				VehicleId:        serviceMap[record.ServiceId].VehicleId,
				ServiceBatteryId: serviceMap[record.ServiceId].ServiceBatteryId,
				VehicleBatteryId: serviceMap[record.ServiceId].VehicleBatteryId,
				EvType:           serviceMap[record.ServiceId].EvType,
				EvBrand:          serviceMap[record.ServiceId].EvBrand,
				Project:          record.Project,
				L1Label:          record.FinalL1Label,
				L2Label:          record.FinalL2Label,
				L3Label:          record.FinalL3Label,
				CityCompany:      record.CityCompany,
				CityCompanyGroup: record.CityCompanyGroup,
				CarPlatform:      record.CarPlatform,
				ReportStatus:     reportStatus,
			})
		}
		response.Total = total
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) ListSatisfyDataV2() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  domain_service.ListSatisfyRequest
			response domain_service.ListSatisfyResponse
		)
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("ListSatisfyData, parse query parameters, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if err := util.CheckTimeRange(request.StartTime, request.EndTime); err != nil {
			log.CtxLog(c).Errorf("ListSatisfyData, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		util.SetURIParamDefault(&request.CommonUriParam)
		satisfy := &domain_service.Satisfy{}
		satisfyData, total, err := satisfy.ListSatisfyData(c, request)
		if err != nil {
			log.CtxLog(c).Errorf("ListSatisfyData, fail to list satisfy data: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		// 设备类型->服务列表
		serviceList := make(map[string][]string)
		for _, record := range satisfyData {
			serviceList[record.Project] = append(serviceList[record.Project], record.ServiceId)
		}
		serviceMap := make(map[string]domain_service.Service)
		for project, serviceIds := range serviceList {
			srv := domain_service.Service{}
			var services []domain_service.Service
			services, _, err = srv.ListServiceForSatisfy(c, domain_service.ListServiceForSatisfyCond{
				Project:   project,
				ServiceId: serviceIds,
			})
			if err != nil {
				log.CtxLog(c).Errorf("ListSatisfyData, fail to list service data: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			for _, record := range services {
				serviceMap[record.ServiceId] = record
			}
		}
		for _, record := range satisfyData {
			reportStatus := "adopt"
			if record.ReportStatus != "" {
				reportStatus = record.ReportStatus
			}
			response.Data = append(response.Data, domain_service.ListSatisfyData{
				CommentId:        record.CommentId,
				CommentTime:      record.CommentTime,
				DiagnosisTag:     record.DiagnosisTag,
				UserTag:          record.UserTag,
				SwapDuration:     serviceMap[record.ServiceId].ServiceEndTime - serviceMap[record.ServiceId].ServiceStartTime,
				QueueDuration:    serviceMap[record.ServiceId].ServiceStartTime - serviceMap[record.ServiceId].OrderStartTime,
				OrderStartTime:   serviceMap[record.ServiceId].OrderStartTime,
				ServiceStartTime: serviceMap[record.ServiceId].ServiceStartTime,
				ServiceEndTime:   serviceMap[record.ServiceId].ServiceEndTime,
				Score:            record.Score,
				DeviceId:         record.DeviceId,
				Description:      record.Description,
				IsValid:          record.IsValid,
				ServiceId:        record.ServiceId,
				OrderId:          record.OrderId,
				VehicleId:        serviceMap[record.ServiceId].VehicleId,
				ServiceBatteryId: serviceMap[record.ServiceId].ServiceBatteryId,
				VehicleBatteryId: serviceMap[record.ServiceId].VehicleBatteryId,
				EvType:           serviceMap[record.ServiceId].EvType,
				EvBrand:          serviceMap[record.ServiceId].EvBrand,
				Project:          record.Project,
				L1Label:          record.FinalL1Label,
				L2Label:          record.FinalL2Label,
				L3Label:          record.FinalL3Label,
				CityCompany:      record.CityCompany,
				CityCompanyGroup: record.CityCompanyGroup,
				CarPlatform:      record.CarPlatform,
				ReportStatus:     reportStatus,
			})
		}
		response.Total = total
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) DownloadListSatisfyData() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  domain_service.ListSatisfyRequest
			response domain_service.ListSatisfyResponse
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("ListSatisfyData, parse query parameters, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if err := util.CheckTimeRange(request.StartTime, request.EndTime); err != nil {
			log.CtxLog(c).Errorf("ListSatisfyData, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		// 最多只能下载2w条
		request.Size = 20000
		util.SetURIParamDefault(&request.CommonUriParam)
		satisfy := &domain_service.Satisfy{}
		satisfyData, _, err := satisfy.ListSatisfyData(c, request)
		if err != nil {
			log.CtxLog(c).Errorf("ListSatisfyData, fail to list satisfy data: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		// 设备类型->服务列表
		serviceList := make(map[string][]string)
		for _, record := range satisfyData {
			serviceList[record.Project] = append(serviceList[record.Project], record.ServiceId)
		}
		serviceMap := make(map[string]domain_service.Service)
		for project, serviceIds := range serviceList {
			srv := domain_service.Service{}
			var services []domain_service.Service
			services, _, err = srv.ListServiceForSatisfy(c, domain_service.ListServiceForSatisfyCond{
				Project:   project,
				ServiceId: serviceIds,
			})
			if err != nil {
				log.CtxLog(c).Errorf("ListSatisfyData, fail to list service data: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			for _, record := range services {
				serviceMap[record.ServiceId] = record
			}
		}

		f := excelize.NewFile()
		index, _ := f.NewSheet("Sheet1")
		// 告警时间	城市公司	设备名称	设备ID	告警类型	图片
		f.SetCellValue("Sheet1", "A1", "评价时间")
		f.SetCellValue("Sheet1", "B1", "诊断标签")
		f.SetCellValue("Sheet1", "C1", "用户标签")
		f.SetCellValue("Sheet1", "D1", "换电时长")
		f.SetCellValue("Sheet1", "E1", "排队时长")
		f.SetCellValue("Sheet1", "F1", "评分")
		f.SetCellValue("Sheet1", "G1", "设备名称")
		f.SetCellValue("Sheet1", "H1", "车辆品牌")
		f.SetCellValue("Sheet1", "I1", "车辆ID")
		f.SetCellValue("Sheet1", "J1", "服务电池ID")
		f.SetCellValue("Sheet1", "K1", "车辆电池ID")
		f.SetCellValue("Sheet1", "L1", "是否有效")
		f.SetCellValue("Sheet1", "M1", "L3-低分问题")
		f.SetCellValue("Sheet1", "N1", "L2-相关产品")
		f.SetCellValue("Sheet1", "O1", "L1-相关业务")
		f.SetCellValue("Sheet1", "P1", "是否在报告内采用")

		loc, _ := time.LoadLocation("Asia/Shanghai")
		for i, record := range satisfyData {
			reportStatus := "adopt"
			if record.ReportStatus != "" {
				reportStatus = record.ReportStatus
			}
			f.SetCellValue("Sheet1", fmt.Sprintf("A%v", i+2), time.UnixMilli(record.CommentTime).In(loc).Format("2006-01-02 15:04:05"))
			f.SetCellValue("Sheet1", fmt.Sprintf("B%v", i+2), record.DiagnosisTag)
			f.SetCellValue("Sheet1", fmt.Sprintf("C%v", i+2), record.UserTag)
			f.SetCellValue("Sheet1", fmt.Sprintf("D%v", i+2), serviceMap[record.ServiceId].ServiceEndTime-serviceMap[record.ServiceId].ServiceStartTime)
			f.SetCellValue("Sheet1", fmt.Sprintf("E%v", i+2), serviceMap[record.ServiceId].ServiceStartTime-serviceMap[record.ServiceId].OrderStartTime)
			f.SetCellValue("Sheet1", fmt.Sprintf("F%v", i+2), record.Score)
			f.SetCellValue("Sheet1", fmt.Sprintf("G%v", i+2), record.Description)
			f.SetCellValue("Sheet1", fmt.Sprintf("H%v", i+2), serviceMap[record.ServiceId].EvBrand)
			f.SetCellValue("Sheet1", fmt.Sprintf("I%v", i+2), serviceMap[record.ServiceId].VehicleId)
			f.SetCellValue("Sheet1", fmt.Sprintf("J%v", i+2), serviceMap[record.ServiceId].ServiceBatteryId)
			f.SetCellValue("Sheet1", fmt.Sprintf("K%v", i+2), serviceMap[record.ServiceId].VehicleBatteryId)
			f.SetCellValue("Sheet1", fmt.Sprintf("L%v", i+2), record.IsValid)
			f.SetCellValue("Sheet1", fmt.Sprintf("M%v", i+2), record.FinalL3Label)
			f.SetCellValue("Sheet1", fmt.Sprintf("N%v", i+2), record.FinalL2Label)
			f.SetCellValue("Sheet1", fmt.Sprintf("O%v", i+2), record.FinalL1Label)
			f.SetCellValue("Sheet1", fmt.Sprintf("P%v", i+2), reportStatus)
		}

		f.SetActiveSheet(index)
		c.Header("Content-Type", "application/octet-stream")
		c.Header("Content-Disposition", "attachment; filename="+fmt.Sprintf("parking_alarm_detail.xlsx"))
		c.Header("Content-Transfer-Encoding", "binary")
		f.Write(c.Writer)
	}
}

func (d *diagnosis) GetServiceInfoByOrderId() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.Response
		orderId := c.Param("order_id")
		project := c.Param("project")
		if orderId == "" || project == "" {
			err := fmt.Errorf("order_id and project is needed")
			log.CtxLog(c).Errorf("GetServiceInfoByOrderId, %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		srv := &domain_service.Service{}
		serviceInfos, _, err := srv.ListServiceForSatisfy(c, domain_service.ListServiceForSatisfyCond{
			Project: project,
			OrderId: []string{orderId},
		})
		if err != nil {
			log.CtxLog(c).Errorf("GetServiceInfoByOrderId, fail to get service info: %v, order id: %s, project: %s", err, orderId, project)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if len(serviceInfos) == 0 {
			log.CtxLog(c).Errorf("GetServiceInfoByOrderId, empty service info, order id: %s, project: %s", orderId, project)
			um.FailWithInternalServerError(c, &response, "empty service info")
			return
		}
		res, _ := srv.GetSatisfyDiagnoseConfig(project)
		serviceInfo := serviceInfos[0]
		basicServiceInfo := domain_service.BasicServiceInfo{
			DeviceId:             serviceInfo.DeviceId,
			Description:          serviceInfo.Description,
			ServiceId:            serviceInfo.ServiceId,
			Project:              serviceInfo.Project,
			ServiceStartTime:     serviceInfo.ServiceStartTime,
			ServiceEndTime:       serviceInfo.ServiceEndTime,
			OrderTimeThreshold:   res.OrderTimeThreshold,
			ServiceTimeThreshold: res.ServiceTimeThreshold,
			VehicleId:            serviceInfo.VehicleId,
		}
		response.Data = basicServiceInfo
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) GetSatisfyDiagnoseResult() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response domain_service.GetSatisfyDiagnoseResultResponse
		orderId := c.Query("order_id")
		project := c.Query("project")
		if orderId == "" || project == "" {
			err := fmt.Errorf("order_id and project is needed")
			log.CtxLog(c).Errorf("GetSatisfyDiagnoseResult, %v, order_id: %s, project: %s", err, orderId, project)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		srv := &domain_service.Service{
			Project: project,
			OrderId: orderId,
		}
		res, _, err := srv.GetSatisfyDiagnoseResult(c)
		if err != nil {
			log.CtxLog(c).Errorf("GetSatisfyDiagnoseResult, fail to get result: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if res != nil {
			response.Data = *res
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) GetSatisfyDetail() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response domain_service.GetSatisfyDetailResponse
		orderId := c.Query("order_id")
		project := c.Query("project")
		if orderId == "" || project == "" {
			err := fmt.Errorf("order_id and project is needed")
			log.CtxLog(c).Errorf("GetSatisfyDetail, %v, order_id: %s, project: %s", err, orderId, project)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		satisfy := &domain_service.Satisfy{OrderId: orderId}
		commentInfo, err := satisfy.GetSatisfyDataById(c)
		if err != nil {
			log.CtxLog(c).Errorf("GetSatisfyDetail, fail to get comment info: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}

		srv := &domain_service.Service{}
		serviceInfos, _, err := srv.ListServiceForSatisfy(c, domain_service.ListServiceForSatisfyCond{
			Project: project,
			OrderId: []string{orderId},
		})
		if err != nil {
			log.CtxLog(c).Errorf("GetSatisfyDetail, fail to get service info: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if len(serviceInfos) == 0 {
			err = fmt.Errorf("cannot find service info by order id")
			log.CtxLog(c).Errorf("GetSatisfyDetail, fail to get service info: %v, order_id: %s", err, orderId)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		serviceInfo := serviceInfos[0]

		response.Data.ServiceInfo = domain_service.Convert2SatisfyDetailServiceInfo(serviceInfo, commentInfo)
		response.Data.UserExperienceInfo = domain_service.Convert2UserExperienceInfo(serviceInfo)
		if commentInfo != nil {
			vo := domain_service.Convert2CommentInfo(commentInfo)
			response.Data.CommentInfo = &vo
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) GetSatisfySwapLog() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response domain_service.GetSatisfySwapLogResponse
		serviceId := c.Query("service_id")
		project := c.Query("project")
		if serviceId == "" || project == "" {
			log.CtxLog(c).Errorf("GetSatisfySwapLog, query not complete, service_id: %s, project: %s", serviceId, project)
			um.FailWithBadRequest(c, &response, "query not complete")
			return
		}
		srvEvent := &service_event.ServiceEventDO{}
		events, _, err := srvEvent.ListServiceEvents(c, service_event.ListServiceEventsCond{
			ServiceId: serviceId,
			Project:   project,
		})
		if err != nil {
			log.CtxLog(c).Errorf("GetSatisfySwapLog, fail to get swap events: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		for _, event := range events {
			response.Data = append(response.Data, domain_service.EventInfo{
				Event:     event.EventName,
				Timestamp: event.EventTs,
			})
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) SyncSatisfyData() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response um.Base
		request := struct {
			Day     int64  `json:"day"`
			OrderId string `json:"order_id"`
		}{}
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("SyncSatisfyData, parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		satisfy := &domain_service.Satisfy{OrderId: request.OrderId}
		g := ucmd.NewErrGroup(c.Copy())
		g.GoRecover(func() error {
			satisfy.SyncSatisfyData(c.Copy(), request.Day)
			return nil
		})
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) ListUserTags() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.Response
		res, err := client.GetWatcher().PLCMongodb().NewMongoEntry(bson.D{}).Distinct(umw.ServiceInfo, domain_service.CollectionSatisfyData, "user_tag")
		if err != nil {
			log.CtxLog(c).Errorf("ListUserTags, fail to get user tag: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var resData []string
		for _, item := range res {
			tag, ok := item.(string)
			if !ok {
				continue
			}
			resData = append(resData, tag)
		}
		sort.Slice(resData, func(i, j int) bool {
			return resData[i] < resData[j]
		})
		response.Data = resData
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) ListDiagnosisTags() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.Response
		respData := make([]map[string]string, 0)
		for tag, description := range domain_service.SatisfyDiagnosisTags {
			respData = append(respData, map[string]string{
				"tag":         tag,
				"description": description,
			})
		}
		sort.Slice(respData, func(i, j int) bool {
			return respData[i]["tag"] < respData[j]["tag"]
		})
		response.Data = respData
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) ListLabel() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.Response
		data, err := d.getDiyLabel(c)
		if err != nil {
			log.CtxLog(c).Errorf("d.getDiyLabel err. err:%v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		response.Data = data
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) getDiyLabel(ctx context.Context) (map[string]map[string][]string, error) {
	url := fmt.Sprintf("%v/satisfaction_label/label", config.Cfg.Welkin.AlgorithmUrl)
	resp, err := http.Get(url)
	if err != nil {
		log.CtxLog(ctx).Errorf("http.Get err. url:%v err:%v", url, err)
		return nil, err
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.CtxLog(ctx).Errorf("ioutil.ReadAll err. url:%v err:%v", url, err)
		return nil, err
	}
	var data map[string]map[string][]string
	err = json.Unmarshal(body, &data)
	if err != nil {
		log.CtxLog(ctx).Errorf("json.Unmarshal. err:%v", err)
		return nil, err
	}
	return data, nil
}

func (d *diagnosis) UpdateReportStatus() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.Response
		var requestData struct {
			CommentId    string `json:"comment_id"`
			ReportStatus string `json:"report_status"`
		}
		if err := c.BindJSON(&requestData); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		filter := bson.D{
			bson.E{"_id", requestData.CommentId},
		}
		err := d.watcher.PLCMongodb().NewMongoEntry(filter).UpdateOne(umw.ServiceInfo, "satisfy_data", bson.M{"$set": bson.M{
			"report_status": requestData.ReportStatus,
		}},
			false)
		if err != nil {
			log.CtxLog(c).Errorf("UpdateOne db, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) GetSatisfyReport() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  domain_service.ListSatisfyRequest
			response model.GetGetSatisfyReportResponse
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("ListSatisfyData, parse query parameters, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if err := util.CheckTimeRange(request.StartTime, request.EndTime); err != nil {
			log.CtxLog(c).Errorf("ListSatisfyData, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		util.SetURIParamDefault(&request.CommonUriParam)
		satisfy := &domain_service.Satisfy{}
		l1Stat, err := satisfy.GetSatisfyDiyLabelStat(c, request, "L1")
		if err != nil {
			log.CtxLog(c).Errorf("GetSatisfyDiyLabelStat, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		l2Stat, err := satisfy.GetSatisfyDiyLabelStat(c, request, "L2")
		if err != nil {
			log.CtxLog(c).Errorf("GetSatisfyDiyLabelStat, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		l1DiyLabelCountStat := map[string]model.DiyLabelCountStat{}
		l1DiyLabelAvgScore := map[string]float64{}
		l1TotalCount := int64(0)
		for _, labelStst := range l1Stat {
			l1TotalCount = l1TotalCount + labelStst.Count
		}
		for label, labelStst := range l1Stat {
			l1DiyLabelCountStat[label] = model.DiyLabelCountStat{
				Count:      labelStst.Count,
				Proportion: util.GetTwoDecimal(float64(labelStst.Count) / float64(l1TotalCount)),
			}
			l1DiyLabelAvgScore[label] = util.GetTwoDecimal(float64(labelStst.TotalScore) / float64(labelStst.Count))
		}

		l2DiyLabelCountStat := map[string]model.DiyLabelCountStat{}
		l2DiyLabelAvgScore := map[string]float64{}
		l2TotalCount := int64(0)
		for _, labelStst := range l2Stat {
			l2TotalCount = l2TotalCount + labelStst.Count
		}
		for label, labelStst := range l2Stat {
			l2DiyLabelCountStat[label] = model.DiyLabelCountStat{
				Count:      labelStst.Count,
				Proportion: util.GetTwoDecimal(float64(labelStst.Count) / float64(l2TotalCount)),
			}
			l2DiyLabelAvgScore[label] = util.GetTwoDecimal(float64(labelStst.TotalScore) / float64(labelStst.Count))
		}

		l3Stat, err := satisfy.GetSatisfyDiyLabelStatV2(c, request)
		if err != nil {
			log.CtxLog(c).Errorf("GetSatisfyDiyLabelStatV2, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		type ScoreCount struct {
			Score1Count int64
			Score2Count int64
			Score3Count int64
			Score4Count int64
			Score5Count int64
		}
		l1ScoreCount := map[string]*ScoreCount{}
		l2ScoreCount := map[string]*ScoreCount{}
		l3ScoreCount := map[string]*ScoreCount{}
		l32L2Map := map[string]string{}
		l1TotalCountMap := map[string]int{}
		l2TotalCountMap := map[string]int{}
		l3TotalCountMap := map[string]int{}
		totalCount := 0
		for l3, stst := range l3Stat {
			l32L2Map[l3] = stst.FinalL2Label
			l1TotalCountMap[stst.FinalL1Label] += int(stst.Count)
			l2TotalCountMap[stst.FinalL2Label] += int(stst.Count)
			l3TotalCountMap[l3] += int(stst.Count)
			totalCount += int(stst.Count)
			_, found := l1ScoreCount[stst.FinalL1Label]
			if !found {
				l1ScoreCount[stst.FinalL1Label] = &ScoreCount{}
			}
			_, found = l2ScoreCount[stst.FinalL2Label]
			if !found {
				l2ScoreCount[stst.FinalL2Label] = &ScoreCount{}
			}
			_, found = l3ScoreCount[l3]
			if !found {
				l3ScoreCount[l3] = &ScoreCount{}
			}
			l1ScoreCount[stst.FinalL1Label].Score1Count = l1ScoreCount[stst.FinalL1Label].Score1Count + stst.Score1Count
			l1ScoreCount[stst.FinalL1Label].Score2Count = l1ScoreCount[stst.FinalL1Label].Score2Count + stst.Score2Count
			l1ScoreCount[stst.FinalL1Label].Score3Count = l1ScoreCount[stst.FinalL1Label].Score3Count + stst.Score3Count
			l1ScoreCount[stst.FinalL1Label].Score4Count = l1ScoreCount[stst.FinalL1Label].Score4Count + stst.Score4Count
			l1ScoreCount[stst.FinalL1Label].Score5Count = l1ScoreCount[stst.FinalL1Label].Score5Count + stst.Score5Count

			l2ScoreCount[stst.FinalL2Label].Score1Count = l2ScoreCount[stst.FinalL2Label].Score1Count + stst.Score1Count
			l2ScoreCount[stst.FinalL2Label].Score2Count = l2ScoreCount[stst.FinalL2Label].Score2Count + stst.Score2Count
			l2ScoreCount[stst.FinalL2Label].Score3Count = l2ScoreCount[stst.FinalL2Label].Score3Count + stst.Score3Count
			l2ScoreCount[stst.FinalL2Label].Score4Count = l2ScoreCount[stst.FinalL2Label].Score4Count + stst.Score4Count
			l2ScoreCount[stst.FinalL2Label].Score5Count = l2ScoreCount[stst.FinalL2Label].Score5Count + stst.Score5Count

			l3ScoreCount[l3].Score1Count = l3ScoreCount[l3].Score1Count + stst.Score1Count
			l3ScoreCount[l3].Score2Count = l3ScoreCount[l3].Score2Count + stst.Score2Count
			l3ScoreCount[l3].Score3Count = l3ScoreCount[l3].Score3Count + stst.Score3Count
			l3ScoreCount[l3].Score4Count = l3ScoreCount[l3].Score4Count + stst.Score4Count
			l3ScoreCount[l3].Score5Count = l3ScoreCount[l3].Score5Count + stst.Score5Count
		}

		l1Count := []model.CountStat{}
		l1Avg := []model.CountStat{}
		for l1, scoreCount := range l1ScoreCount {
			l1Count = append(l1Count, model.CountStat{
				Name:  l1,
				Value: float64(scoreCount.Score1Count + scoreCount.Score2Count + scoreCount.Score3Count + scoreCount.Score4Count + scoreCount.Score5Count),
			})
			l1Avg = append(l1Avg, model.CountStat{
				Name:  l1,
				Value: util.GetTwoDecimal(float64(scoreCount.Score1Count+scoreCount.Score2Count*2+scoreCount.Score3Count*3+scoreCount.Score4Count*4+scoreCount.Score5Count*5) / float64(l1TotalCountMap[l1])),
			})
		}
		l2CountMap := map[string]*model.CountStat{}
		l2AvgMap := map[string]*model.CountStat{}
		for l2, scoreCount := range l2ScoreCount {
			l2CountMap[l2] = &model.CountStat{
				Name:  l2,
				Value: util.GetTwoDecimal(float64(scoreCount.Score1Count + scoreCount.Score2Count + scoreCount.Score3Count + scoreCount.Score4Count + scoreCount.Score5Count)),
			}
			l2AvgMap[l2] = &model.CountStat{
				Name:  l2,
				Value: util.GetTwoDecimal(float64(scoreCount.Score1Count+scoreCount.Score2Count*2+scoreCount.Score3Count*3+scoreCount.Score4Count*4+scoreCount.Score5Count*5) / float64(l2TotalCountMap[l2])),
			}
		}
		for l3, scoreCount := range l3ScoreCount {
			if l2CountMap[l32L2Map[l3]] == nil {
				continue
			}
			l2CountMap[l32L2Map[l3]].L3LabelDistribution = append(l2CountMap[l32L2Map[l3]].L3LabelDistribution, model.CountStat{
				Name:       l3,
				Value:      util.GetTwoDecimal(float64(scoreCount.Score1Count + scoreCount.Score2Count + scoreCount.Score3Count + scoreCount.Score4Count + scoreCount.Score5Count)),
				Proportion: util.GetTwoDecimal(float64(scoreCount.Score1Count+scoreCount.Score2Count+scoreCount.Score3Count+scoreCount.Score4Count+scoreCount.Score5Count) / float64(l2TotalCountMap[l32L2Map[l3]])),
			})
			l2AvgMap[l32L2Map[l3]].L3LabelDistribution = append(l2AvgMap[l32L2Map[l3]].L3LabelDistribution, model.CountStat{
				Name:       l3,
				Value:      util.GetTwoDecimal(float64(scoreCount.Score1Count+scoreCount.Score2Count*2+scoreCount.Score3Count*3+scoreCount.Score4Count*4+scoreCount.Score5Count*5) / float64(l3TotalCountMap[l3])),
				Proportion: util.GetTwoDecimal(float64(scoreCount.Score1Count+scoreCount.Score2Count+scoreCount.Score3Count+scoreCount.Score4Count+scoreCount.Score5Count) / float64(l2TotalCountMap[l32L2Map[l3]])),
			})
		}

		l1Loss := map[string]float64{}
		l1LossModel := []model.CountStat{}
		for label, scoreCount := range l1ScoreCount {
			a := float64(scoreCount.Score1Count*1) + float64(scoreCount.Score2Count*2) + float64(scoreCount.Score3Count*3) + float64(scoreCount.Score4Count*4) + float64(scoreCount.Score5Count*5)
			b := float64(scoreCount.Score1Count*5) + float64(scoreCount.Score2Count*4) + float64(scoreCount.Score3Count*3) + float64(scoreCount.Score4Count*2) + float64(scoreCount.Score5Count*1)
			v1 := a / b
			totalCount := scoreCount.Score1Count + scoreCount.Score2Count + scoreCount.Score3Count + scoreCount.Score4Count + scoreCount.Score5Count
			v := math.Floor(float64(totalCount) * 0.13)
			for i := 0; i < int(v); i++ {
				scoreCount.Score5Count++
				if scoreCount.Score1Count > 0 {
					scoreCount.Score1Count--
					continue
				}
				if scoreCount.Score2Count > 0 {
					scoreCount.Score2Count--
					continue
				}
				if scoreCount.Score3Count > 0 {
					scoreCount.Score3Count--
					continue
				}
				if scoreCount.Score4Count > 0 {
					scoreCount.Score4Count--
					continue
				}
				if scoreCount.Score5Count > 0 {
					scoreCount.Score5Count--
					continue
				}
			}

			a = float64(scoreCount.Score1Count*1) + float64(scoreCount.Score2Count*2) + float64(scoreCount.Score3Count*3) + float64(scoreCount.Score4Count*4) + float64(scoreCount.Score5Count*5)
			b = float64(scoreCount.Score1Count*5) + float64(scoreCount.Score2Count*4) + float64(scoreCount.Score3Count*3) + float64(scoreCount.Score4Count*2) + float64(scoreCount.Score5Count*1)
			v2 := a / b
			l1Loss[label] = v2 - v1
			l1LossModel = append(l1LossModel, model.CountStat{
				Name:  label,
				Value: util.GetTwoDecimal(v2 - v1),
			})
		}

		l2Loss := map[string]float64{}
		l2LossModel := []*model.CountStat{}
		l2LossModelMap := map[string]*model.CountStat{}
		for label, scoreCount := range l2ScoreCount {
			a := float64(scoreCount.Score1Count*1) + float64(scoreCount.Score2Count*2) + float64(scoreCount.Score3Count*3) + float64(scoreCount.Score4Count*4) + float64(scoreCount.Score5Count*5)
			b := float64(scoreCount.Score1Count*5) + float64(scoreCount.Score2Count*4) + float64(scoreCount.Score3Count*3) + float64(scoreCount.Score4Count*2) + float64(scoreCount.Score5Count*1)
			v1 := a / b
			totalCount := scoreCount.Score1Count + scoreCount.Score2Count + scoreCount.Score3Count + scoreCount.Score4Count + scoreCount.Score5Count
			v := math.Floor(float64(totalCount) * 0.13)
			for i := 0; i < int(v); i++ {
				scoreCount.Score5Count++
				if scoreCount.Score1Count > 0 {
					scoreCount.Score1Count--
					continue
				}
				if scoreCount.Score2Count > 0 {
					scoreCount.Score2Count--
					continue
				}
				if scoreCount.Score3Count > 0 {
					scoreCount.Score3Count--
					continue
				}
				if scoreCount.Score4Count > 0 {
					scoreCount.Score4Count--
					continue
				}
				if scoreCount.Score5Count > 0 {
					scoreCount.Score5Count--
					continue
				}
			}

			a = float64(scoreCount.Score1Count*1) + float64(scoreCount.Score2Count*2) + float64(scoreCount.Score3Count*3) + float64(scoreCount.Score4Count*4) + float64(scoreCount.Score5Count*5)
			b = float64(scoreCount.Score1Count*5) + float64(scoreCount.Score2Count*4) + float64(scoreCount.Score3Count*3) + float64(scoreCount.Score4Count*2) + float64(scoreCount.Score5Count*1)
			v2 := a / b
			l2Loss[label] = v2 - v1
			countStat := &model.CountStat{
				Name:  label,
				Value: util.GetTwoDecimal(v2 - v1),
			}
			l2LossModel = append(l2LossModel, countStat)
			l2LossModelMap[label] = countStat
		}

		l3Loss := map[string]float64{}
		l3LossModel := []model.CountStat{}
		for label, scoreCount := range l3ScoreCount {
			a := float64(scoreCount.Score1Count*1) + float64(scoreCount.Score2Count*2) + float64(scoreCount.Score3Count*3) + float64(scoreCount.Score4Count*4) + float64(scoreCount.Score5Count*5)
			b := float64(scoreCount.Score1Count*5) + float64(scoreCount.Score2Count*4) + float64(scoreCount.Score3Count*3) + float64(scoreCount.Score4Count*2) + float64(scoreCount.Score5Count*1)
			v1 := a / b
			totalCount := scoreCount.Score1Count + scoreCount.Score2Count + scoreCount.Score3Count + scoreCount.Score4Count + scoreCount.Score5Count
			v := math.Floor(float64(totalCount) * 0.13)
			for i := 0; i < int(v); i++ {
				scoreCount.Score5Count++
				if scoreCount.Score1Count > 0 {
					scoreCount.Score1Count--
					continue
				}
				if scoreCount.Score2Count > 0 {
					scoreCount.Score2Count--
					continue
				}
				if scoreCount.Score3Count > 0 {
					scoreCount.Score3Count--
					continue
				}
				if scoreCount.Score4Count > 0 {
					scoreCount.Score4Count--
					continue
				}
				if scoreCount.Score5Count > 0 {
					scoreCount.Score5Count--
					continue
				}
			}

			a = float64(scoreCount.Score1Count*1) + float64(scoreCount.Score2Count*2) + float64(scoreCount.Score3Count*3) + float64(scoreCount.Score4Count*4) + float64(scoreCount.Score5Count*5)
			b = float64(scoreCount.Score1Count*5) + float64(scoreCount.Score2Count*4) + float64(scoreCount.Score3Count*3) + float64(scoreCount.Score4Count*2) + float64(scoreCount.Score5Count*1)
			v2 := a / b
			l3Loss[label] = v2 - v1
			statCount := model.CountStat{
				Name:       label,
				Value:      util.GetTwoDecimal(v2 - v1),
				Proportion: util.GetTwoDecimal(float64(scoreCount.Score1Count+scoreCount.Score2Count+scoreCount.Score3Count+scoreCount.Score4Count+scoreCount.Score5Count) / float64(l2TotalCountMap[l32L2Map[label]])),
			}
			l3LossModel = append(l3LossModel, statCount)
			if l2LossModelMap[l32L2Map[label]] == nil {
				log.CtxLog(c).Errorf("not konw l3 to l2. l3:%v l32L2Map:%v data:%v", label, l32L2Map, ucmd.ToJsonStrIgnoreErr(l3Stat))
				continue
			}
			if l2LossModelMap[l32L2Map[label]].L3LabelDistribution == nil {
				l2LossModelMap[l32L2Map[label]].L3LabelDistribution = []model.CountStat{}
			}
			l2LossModelMap[l32L2Map[label]].L3LabelDistribution = append(l2LossModelMap[l32L2Map[label]].L3LabelDistribution, statCount)
		}
		l2LossModels := []model.CountStat{}
		for _, stat := range l2LossModel {
			l2LossModels = append(l2LossModels, *stat)
		}
		l2CountModels := []model.CountStat{}
		for _, countStat := range l2CountMap {
			l2CountModels = append(l2CountModels, *countStat)
		}
		l2AvgModels := []model.CountStat{}
		for _, avgModel := range l2AvgMap {
			l2AvgModels = append(l2AvgModels, *avgModel)
		}

		sort.Slice(l1LossModel, func(i, j int) bool {
			return l1LossModel[i].Value < l1LossModel[j].Value
		})
		sort.Slice(l2LossModels, func(i, j int) bool {
			return l2LossModels[i].Value < l2LossModels[j].Value
		})
		sort.Slice(l1Count, func(i, j int) bool {
			return l1Count[i].Value < l1Count[j].Value
		})
		sort.Slice(l2CountModels, func(i, j int) bool {
			return l2CountModels[i].Value < l2CountModels[j].Value
		})
		sort.Slice(l1Avg, func(i, j int) bool {
			return l1Avg[i].Value < l1Avg[j].Value
		})
		sort.Slice(l2AvgModels, func(i, j int) bool {
			return l2AvgModels[i].Value < l2AvgModels[j].Value
		})
		data := model.DiyLabelStat{
			L1LabelSatisfyCount: l1Count,
			L2LabelSatisfyCount: l2CountModels,
			L1LabelSatisfyAvg:   l1Avg,
			L2LabelSatisfyAvg:   l2AvgModels,
			L1LabelSatisfyLoss:  l1LossModel,
			L2LabelSatisfyLoss:  l2LossModels,
		}
		if len(l1DiyLabelCountStat) != 0 {
			data.L1LabelCount = &l1DiyLabelCountStat
		}
		if len(l1DiyLabelAvgScore) != 0 {
			data.L1LabelAvg = &l1DiyLabelAvgScore
		}
		if len(l2DiyLabelCountStat) != 0 {
			data.L2LabelCount = &l2DiyLabelCountStat
		}
		if len(l2DiyLabelAvgScore) != 0 {
			data.L2LabelAvg = &l2DiyLabelAvgScore
		}
		response.DiyLabelStat = data
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) DownloadSatisfyReport() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  domain_service.ListSatisfyRequest
			response model.GetGetSatisfyReportResponse
		)
		if err := c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("ListSatisfyData, parse query parameters, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if err := util.CheckTimeRange(request.StartTime, request.EndTime); err != nil {
			log.CtxLog(c).Errorf("ListSatisfyData, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		util.SetURIParamDefault(&request.CommonUriParam)
		satisfy := &domain_service.Satisfy{}
		l1Stat, err := satisfy.GetSatisfyDiyLabelStat(c, request, "L1")
		if err != nil {
			log.CtxLog(c).Errorf("GetSatisfyDiyLabelStat, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		l2Stat, err := satisfy.GetSatisfyDiyLabelStat(c, request, "L2")
		if err != nil {
			log.CtxLog(c).Errorf("GetSatisfyDiyLabelStat, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		l1DiyLabelCountStat := map[string]model.DiyLabelCountStat{}
		l1DiyLabelAvgScore := map[string]float64{}
		l1TotalCount := int64(0)
		for _, labelStst := range l1Stat {
			l1TotalCount = l1TotalCount + labelStst.Count
		}
		for label, labelStst := range l1Stat {
			l1DiyLabelCountStat[label] = model.DiyLabelCountStat{
				Count:      labelStst.Count,
				Proportion: float64(labelStst.Count) / float64(l1TotalCount),
			}
			l1DiyLabelAvgScore[label] = float64(labelStst.TotalScore) / float64(labelStst.Count)
		}

		l2DiyLabelCountStat := map[string]model.DiyLabelCountStat{}
		l2DiyLabelAvgScore := map[string]float64{}
		l2TotalCount := int64(0)
		for _, labelStst := range l2Stat {
			l2TotalCount = l2TotalCount + labelStst.Count
		}
		for label, labelStst := range l2Stat {
			l2DiyLabelCountStat[label] = model.DiyLabelCountStat{
				Count:      labelStst.Count,
				Proportion: float64(labelStst.Count) / float64(l2TotalCount),
			}
			l2DiyLabelAvgScore[label] = float64(labelStst.TotalScore) / float64(labelStst.Count)
		}
		data := model.DiyLabelStat{}
		if len(l1DiyLabelCountStat) != 0 {
			data.L1LabelCount = &l1DiyLabelCountStat
		}
		if len(l1DiyLabelAvgScore) != 0 {
			data.L1LabelAvg = &l1DiyLabelAvgScore
		}
		if len(l2DiyLabelCountStat) != 0 {
			data.L2LabelCount = &l2DiyLabelCountStat
		}
		if len(l2DiyLabelAvgScore) != 0 {
			data.L2LabelAvg = &l2DiyLabelAvgScore
		}
		response.DiyLabelStat = data

		f := excelize.NewFile()
		index, _ := f.NewSheet("一级标签个数")
		_, _ = f.NewSheet("一级标签均分")
		_, _ = f.NewSheet("二级标签个数")
		_, _ = f.NewSheet("二级标签均分")
		f.SetCellValue("一级标签个数", "A1", "标签名")
		f.SetCellValue("一级标签个数", "B1", "个数")
		f.SetCellValue("一级标签个数", "C1", "占比")
		i := 0
		for label, stat := range l1DiyLabelCountStat {
			f.SetCellValue("一级标签个数", fmt.Sprintf("A%v", i+2), label)
			f.SetCellValue("一级标签个数", fmt.Sprintf("B%v", i+2), stat.Count)
			f.SetCellValue("一级标签个数", fmt.Sprintf("C%v", i+2), util.GetTwoDecimal(stat.Proportion))
			i++
		}

		f.SetCellValue("一级标签均分", "A1", "标签名")
		f.SetCellValue("一级标签均分", "B1", "均分")
		i = 0
		for label, value := range l1DiyLabelAvgScore {
			f.SetCellValue("一级标签均分", fmt.Sprintf("A%v", i+2), label)
			f.SetCellValue("一级标签均分", fmt.Sprintf("B%v", i+2), util.GetTwoDecimal(value))
			i++
		}

		f.SetCellValue("二级标签个数", "A1", "标签名")
		f.SetCellValue("二级标签个数", "B1", "个数")
		f.SetCellValue("二级标签个数", "C1", "占比")
		i = 0
		for label, stat := range l2DiyLabelCountStat {
			f.SetCellValue("二级标签个数", fmt.Sprintf("A%v", i+2), label)
			f.SetCellValue("二级标签个数", fmt.Sprintf("B%v", i+2), stat.Count)
			f.SetCellValue("二级标签个数", fmt.Sprintf("C%v", i+2), util.GetTwoDecimal(stat.Proportion))
			i++
		}

		f.SetCellValue("二级标签均分", "A1", "标签名")
		f.SetCellValue("二级标签均分", "B1", "均分")
		i = 0
		for label, value := range l2DiyLabelAvgScore {
			f.SetCellValue("二级标签均分", fmt.Sprintf("A%v", i+2), label)
			f.SetCellValue("二级标签均分", fmt.Sprintf("B%v", i+2), util.GetTwoDecimal(value))
			i++
		}

		f.SetActiveSheet(index)

		c.Header("Content-Type", "application/octet-stream")
		c.Header("Content-Disposition", "attachment; filename="+fmt.Sprintf("parking_alarm_detail.xlsx"))
		c.Header("Content-Transfer-Encoding", "binary")
		f.Write(c.Writer)
	}
}

func (d *diagnosis) AsynExcuteDiyLabel() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.Response
		var requestData struct {
			Date string `json:"date"`
		}
		if err := c.BindJSON(&requestData); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		copyedGinCtx := c.Copy()

		go func() {
			defer ucmd.RecoverPanic()
			err := d.LoadDiyLabel(copyedGinCtx, requestData.Date)
			if err != nil {
				log.CtxLog(c).Errorf("LoadDiyLabel err. err:%v", err)
				return
			}
		}()
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *diagnosis) LoadDiyLabel(ctx context.Context, date string) error {
	log.CtxLog(ctx).Infof("start excute diy label date:%v", date)
	startExcuteTime := time.Now()
	startTime, err := time.ParseInLocation("2006-01-02", date, util.GetTimeLoc())
	if err != nil {
		log.CtxLog(ctx).Errorf("time.ParseInLocation err. err:%v", err)
		return err
	}
	endTime := startTime.Add(time.Hour * 24)

	// 遍历出所有评论
	filter := bson.D{
		bson.E{Key: "comment_time", Value: bson.M{"$gte": startTime.UnixMilli(), "$lte": endTime.UnixMilli()}},
	}
	log.CtxLog(ctx).Infof("list all comment query:%v", ucmd.ToJsonStrIgnoreErr(filter))
	Limit := int64(100)
	Offset := int64(0)
	count := 0
	for {
		cur, err := d.watcher.PLCMongodb().Client.Database(umw.ServiceInfo).Collection("satisfy_data").Find(ctx, filter, options.Find().SetSort(bson.M{"comment_time": -1}).SetSkip(Offset).SetLimit(Limit))
		if err != nil {
			log.CtxLog(ctx).Errorf("mongodb get data err. err:%v", err)
			return err
		}
		var satisfyData []mongo_model.SwapSatisfyData
		if err = cur.All(ctx, &satisfyData); err != nil {
			log.CtxLog(ctx).Errorf("cur.All err. err:%v", err)
			return err
		}
		for _, satisfyDatum := range satisfyData {
			err = d.processDiyLabel(ctx, satisfyDatum)
			log.CtxLog(ctx).Infof("processDiyLabel. orderId:%v count:%v", satisfyDatum.OrderId, count)
			if err != nil {
				log.CtxLog(ctx).Warnf("processDiyLabel err. err:%v", err)
				//return err
			}
			count++
		}

		if len(satisfyData) < int(Limit) {
			break
		}
		Offset = Offset + int64(len(satisfyData))
	}
	log.CtxLog(ctx).Infof("finish excute diy label, count:%v time cost:%v", count, time.Since(startExcuteTime))
	return nil
}

func (d *diagnosis) processDiyLabel(ctx context.Context, swapSatisfyData mongo_model.SwapSatisfyData) error {
	satisfy := &domain_service.Satisfy{OrderId: swapSatisfyData.OrderId}
	satisfy, err := satisfy.GetSatisfyDataById(ctx)
	if err != nil {
		log.CtxLog(ctx).Errorf("GetSatisfyDetail, fail to get comment info: %v", err)
		return err
	}

	srv := &domain_service.Service{}
	serviceInfos, _, err := srv.ListServiceForSatisfy(ctx, domain_service.ListServiceForSatisfyCond{
		Project: swapSatisfyData.Project,
		OrderId: []string{swapSatisfyData.OrderId},
	})
	if len(serviceInfos) == 0 {
		err = fmt.Errorf("cannot find service info by order id")
		log.CtxLog(ctx).Warnf("GetSatisfyDetail, fail to get service info: %v, order_id: %s", err, swapSatisfyData.OrderId)
		return err
	}
	serviceInfo := serviceInfos[0]
	diagnoseResult, _, err := (&serviceInfo).GetSatisfyDiagnoseResult(ctx)
	if err != nil {
		log.CtxLog(ctx).Errorf("GetSatisfyDiagnoseResult, fail to get result: %v", err)
		return err
	}
	firstLevelThreeWarning := ""
	if diagnoseResult != nil && diagnoseResult.SwapFail != nil && len(diagnoseResult.SwapFail.AlarmList) != 0 {
		for i := len(diagnoseResult.SwapFail.AlarmList) - 1; i >= 0; i-- {
			if diagnoseResult.SwapFail.AlarmList[i].AlarmLevel == 3 {
				firstLevelThreeWarning = diagnoseResult.SwapFail.AlarmList[i].DataIdDescription
				break
			}
		}
	}

	diyLabelCalculateReq := DiyLabelCalculateReq{}
	diyLabelCalculateReq.CommentInfo.Content = satisfy.Comment
	diyLabelCalculateReq.CommentInfo.LowReason = satisfy.Reason
	diyLabelCalculateReq.CommentInfo.Solution = satisfy.Solution
	evBatteryCapacity := int32(0)
	evBatteryPhysicalCapacity := int32(0)
	if serviceInfo.EvOriginalBatteryCapacity != nil {
		physicalType, userType := common.ConvertBattery2PhysicalAndUserCapacity(serviceInfo.EvOriginalBatteryCapacity)
		if userType != nil {
			evBatteryCapacity = *userType
		}
		if physicalType != nil {
			evBatteryPhysicalCapacity = *physicalType
		}
	}
	evBatterySoc := float32(0)
	if serviceInfo.EvBatterySoc != nil {
		evBatterySoc = *serviceInfo.EvBatterySoc
	}
	serviceBatteryCapacity := int32(0)
	serviceBatteryPhysicalCapacity := int32(0)
	if serviceInfo.ServiceOriginalBatteryCapacity != nil {
		physicalType, userType := common.ConvertBattery2PhysicalAndUserCapacity(serviceInfo.ServiceOriginalBatteryCapacity)
		if userType != nil {
			serviceBatteryCapacity = *userType
		}
		if physicalType != nil {
			serviceBatteryPhysicalCapacity = *physicalType
		}
	}
	serviceBatterySoc := float32(0)
	if serviceInfo.ServiceBatterySoc != nil {
		serviceBatterySoc = *serviceInfo.ServiceBatterySoc
	}
	diyLabelCalculateReq.LinkedOrderInfo.OldBatteryCapacity = evBatteryCapacity
	diyLabelCalculateReq.LinkedOrderInfo.OldBatteryPhysicalCapacity = evBatteryPhysicalCapacity
	diyLabelCalculateReq.LinkedOrderInfo.OldBatterySoc = evBatterySoc
	diyLabelCalculateReq.LinkedOrderInfo.NewBatteryCapacity = serviceBatteryCapacity
	diyLabelCalculateReq.LinkedOrderInfo.NewBatteryPhysicalCapacity = serviceBatteryPhysicalCapacity
	diyLabelCalculateReq.LinkedOrderInfo.NewBatterySoc = serviceBatterySoc
	diyLabelCalculateReq.LinkedOrderInfo.FirstLevelThreeWarning = firstLevelThreeWarning
	diyLabelCalculateReq.LinkedOrderInfo.OrderStartTime = serviceInfo.OrderStartTime
	diyLabelCalculateReq.LinkedOrderInfo.ServiceStartTime = serviceInfo.ServiceStartTime
	diyLabelCalculateReq.LinkedOrderInfo.ServiceEndTime = serviceInfo.ServiceEndTime
	diyLabelCalculateReq.LinkedOrderInfo.OrderEndTime = serviceInfo.OrderEndTime
	l3Labels, err := d.calculateDiyL3Label(ctx, diyLabelCalculateReq)
	if err != nil {
		log.CtxLog(ctx).Errorf("d.calculateDiyL3Label err. err: %v", err)
		return err
	}
	log.CtxLog(ctx).Infof("processDiyLabel. orderId:%v diyLabelCalculateReq:%v resp:%v", swapSatisfyData.OrderId, ucmd.ToJsonStrIgnoreErr(diyLabelCalculateReq), ucmd.ToJsonStrIgnoreErr(l3Labels))
	l1, l2, l3, err := d.judegeFinalLabel(ctx, l3Labels)
	if err != nil {
		log.CtxLog(ctx).Errorf("d.judegeFinalLabel err. err: %v", err)
		return err
	}
	filter := bson.D{
		bson.E{"order_id", swapSatisfyData.OrderId},
	}
	err = d.watcher.PLCMongodb().NewMongoEntry(filter).UpdateOne(umw.ServiceInfo, "satisfy_data", bson.M{"$set": bson.M{
		"l3_labels":           l3Labels,
		"final_l1_label":      l1,
		"final_l2_label":      l2,
		"final_l3_label":      l3,
		"diy_label_update_ts": time.Now().UnixMilli(),
	}},
		false)
	if err != nil {
		log.CtxLog(ctx).Errorf("UpdateOne db, err: %v", err)
		return err
	}
	return nil
}

func (d *diagnosis) calculateDiyL3Label(ctx context.Context, diyLabelCalculateReq DiyLabelCalculateReq) (map[string]int64, error) {
	data, err := json.Marshal(&diyLabelCalculateReq)
	if err != nil {
		log.CtxLog(ctx).Errorf("json.Marshal err. err:%v", err)
		return nil, err
	}
	var reqbody map[string]interface{}
	err = json.Unmarshal(data, &reqbody)
	if err != nil {
		log.CtxLog(ctx).Errorf("json.Unmarshal err. err:%v", err)
		return nil, err
	}
	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:    fmt.Sprintf("%v/satisfaction_label/calculate", config.Cfg.Welkin.AlgorithmUrl),
		Method: "POST",
		Header: map[string]string{
			"Content-Type": "application/json",
		},
		RequestBody: reqbody,
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		log.Logger.Errorf("http /satisfaction_label/calculate err: %v, request body: %s", err, ucmd.ToJsonStrIgnoreErr(reqbody))
		return nil, err
	}
	if statusCode != http.StatusOK {
		return nil, errors.New(fmt.Sprintf("http status not 200. code:%v", statusCode))
	}
	defer body.Close()
	data, dErr := io.ReadAll(body)
	if dErr != nil {
		log.Logger.Errorf("SendOperationImageAlarm, send bbsa image alarm to kafka, err: %s, request body: %s", dErr, ucmd.ToJsonStrIgnoreErr(reqbody))
		return nil, dErr
	}
	var response map[string]int64
	if err = json.Unmarshal(data, &response); err != nil {
		return nil, err
	}
	return response, nil
}

func (d *diagnosis) judegeFinalLabel(ctx context.Context, req map[string]int64) (string, string, string, error) {
	data, err := json.Marshal(&req)
	if err != nil {
		log.CtxLog(ctx).Errorf("json.Marshal err. err:%v", err)
		return "", "", "", err
	}
	var reqbody map[string]interface{}
	err = json.Unmarshal(data, &reqbody)
	if err != nil {
		log.CtxLog(ctx).Errorf("json.Unmarshal err. err:%v", err)
		return "", "", "", err
	}
	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:    fmt.Sprintf("%v/satisfaction_label/judge", config.Cfg.Welkin.AlgorithmUrl),
		Method: "POST",
		Header: map[string]string{
			"Content-Type": "application/json",
		},
		RequestBody: reqbody,
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		log.Logger.Errorf("http /satisfaction_label/judge err: %v, request body: %s", err, ucmd.ToJsonStrIgnoreErr(reqbody))
		return "", "", "", err
	}
	if statusCode != http.StatusOK {
		return "", "", "", errors.New(fmt.Sprintf("http status not 200. code:%v", statusCode))
	}
	defer body.Close()
	data, dErr := io.ReadAll(body)
	if dErr != nil {
		log.Logger.Errorf("SendOperationImageAlarm, send bbsa image alarm to kafka, err: %s, request body: %s", dErr, ucmd.ToJsonStrIgnoreErr(reqbody))
		return "", "", "", dErr
	}

	var response struct {
		Label1 string `json:"label1"`
		Label2 string `json:"label2"`
		Label3 string `json:"label3"`
	}
	if err = json.Unmarshal(data, &response); err != nil {
		return "", "", "", err
	}
	return response.Label1, response.Label2, response.Label3, nil
}

type DiyLabelCalculateReq struct {
	CommentInfo struct {
		Content   string `json:"content"`
		LowReason string `json:"low_reason"`
		Solution  string `json:"solution"`
	} `json:"comment_info"`
	LinkedOrderInfo struct {
		OldBatteryCapacity         int32   `json:"old_battery_capacity"`
		OldBatteryPhysicalCapacity int32   `json:"old_battery_physical_capacity"`
		OldBatterySoc              float32 `json:"old_battery_soc"`
		NewBatteryCapacity         int32   `json:"new_battery_capacity"`
		NewBatteryPhysicalCapacity int32   `json:"new_battery_physical_capacity"`
		NewBatterySoc              float32 `json:"new_battery_soc"`
		FirstLevelThreeWarning     string  `json:"first_level_three_warning"`
		OrderStartTime             int64   `json:"order_start_time"`
		ServiceStartTime           int64   `json:"service_start_time"`
		ServiceEndTime             int64   `json:"service_end_time"`
		OrderEndTime               int64   `json:"order_end_time"`
	} `json:"linked_order_info"`
}
