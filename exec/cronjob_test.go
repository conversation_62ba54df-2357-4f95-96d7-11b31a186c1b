package exec

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strconv"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	gocache "github.com/patrickmn/go-cache"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	ulog "git.nevint.com/golang-libs/common-utils/logger"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	log "git.nevint.com/welkin2/welkin-backend/logger"
)

var clogger *zap.SugaredLogger

func cronjobInit() {
	os.Setenv("APOLLO_META", "https://apollo-config-cn-stg.nioint.com")
	os.Setenv("K8S_ENV", "stg")
	cfg = &ucfg.Config{}
	ucfg.GetConfigApollo("config.json", true, cfg, ucfg.NeedValidate(true), ucfg.ServiceName("ppd-welkin-backend"), ucfg.Cluster(ucfg.GetWelkinApolloCluster()))
	defer ulog.Final()
	cfg.Log.DirPath = "/tmp/logs"
	log.Init(cfg.Log, true)
	clogger = log.Logger.Named("CronJob")
	w = client.NewWatcherByParam(cfg, clogger)
	cache.PowerSwapCache.RefreshDeviceInfoCache(w.Mongodb().Client)
	InitCronJobs(cfg)
}

var ctx *gin.Context

func init() {
	os.Setenv("APOLLO_META", "https://apollo-config-cn-stg.nioint.com")
	os.Setenv("ENV", "stg")
	cfg := &ucfg.Config{}
	ucfg.GetConfigApollo("config.json", true, cfg, ucfg.NeedValidate(true), ucfg.ServiceName("ppd-welkin-backend"), ucfg.Cluster(ucfg.GetWelkinApolloCluster()))
	defer ulog.Final()
	cfg.Log.DirPath = "/tmp/logs"
	config.Cfg = cfg
	log.Init(cfg.Log, true)
	logger := log.Logger.Named("activity")
	w = client.NewWatcherByParam(config.Cfg, logger)
	var err error
	if err != nil {
		panic(err)
	}
	cache.CacheRealtimeDataID(w.Mongodb().Client)
	cache.PowerSwapCache = &cache.DeviceInfoCache{
		Cache:         gocache.New(gocache.NoExpiration, gocache.NoExpiration),
		ResourceCache: gocache.New(gocache.NoExpiration, gocache.NoExpiration),
	}
	cache.PowerSwapCache.RefreshDeviceInfoCache(w.Mongodb().Client)
	ctx = &gin.Context{Request: &http.Request{Header: make(http.Header)}}
	ctx.Request.Header.Set("X-Request-ID", "test")
}

func TestServiceStatisticsHandler_Process(t *testing.T) {
	cronjobInit()
	testFunc := func(timeStr string) {
		endTime, _ := time.ParseInLocation("2006-01-02 15:04:05", timeStr, time.Local)
		startTime := endTime.Add(-time.Hour)
		handler := ServiceStatisticsHandler{
			Watcher:   w,
			Projects:  []string{umw.PowerSwap, umw.PowerSwap2, umw.PUS3},
			StartTime: startTime,
			EndTime:   endTime,
			Logger:    clogger,
		}
		ctx, cancel := context.WithTimeout(context.Background(), time.Second*30)
		defer cancel()
		err := handler.ProcessHourly(ctx)
		if err != nil {
			panic(err)
		}
	}

	day := 31

	for i := 1; i < 10; i++ {
		timeStr := fmt.Sprintf("2024-05-%d 0%d:00:00", day, i)
		fmt.Println(timeStr)
		testFunc(timeStr)
	}
	for i := 10; i < 24; i++ {
		timeStr := fmt.Sprintf("2024-05-%d %d:00:00", day, i)
		fmt.Println(timeStr)
		testFunc(timeStr)
	}

	//timeStr := fmt.Sprintf("2024-05-%d 0%d:00:00", day+1, 0)
	//fmt.Println(timeStr)
	//testFunc(timeStr)
}

type OSSAlarm struct {
	AlarmId          string    `json:"alarm_id" bson:"alarm_id"`
	AlarmDescription string    `json:"alarm_description" bson:"alarm_description"`
	DeviceId         string    `json:"device_id" bson:"device_id"`
	AlarmRaiseTime   time.Time `json:"alarm_raise_time" bson:"alarm_raise_time"`
	AlarmClearTime   time.Time `json:"alarm_clear_time" bson:"alarm_clear_time"`
	AlarmState       int32     `json:"alarm_state" bson:"alarm_state"`
	AlarmLevel       string    `json:"alarm_level" bson:"alarm_level"`
}

func Test_Alarm(t *testing.T) {
	cronjobInit()
	batchSize := 50000
	offset := 0
	for {
		byteData, cnt, err := w.Mongodb().NewMongoEntry().ListByPagination("diagnosis-management", "alarm_test", client.Pagination{Limit: int64(batchSize), Offset: int64(offset)}, client.Ordered{Key: "alarm_raise_time", Descending: true})
		if err != nil {
			panic(err)
		}
		offset += batchSize
		var res []OSSAlarm
		if err = json.Unmarshal(byteData, &res); err != nil {
			panic(err)
		}

		deviceAlarms52 := make([]interface{}, 0)
		deviceAlarms53 := make([]interface{}, 0)
		deviceAlarms62 := make([]interface{}, 0)
		deviceAlarms63 := make([]interface{}, 0)
		stuckAlarms := make([]interface{}, 0)
		for _, item := range res {
			deviceInfo, ok := cache.PowerSwapCache.GetSingleDevice(item.DeviceId)
			if !ok {
				fmt.Printf("cannot find device: %s\n", ucmd.ToJsonStrIgnoreErr(item))
				continue
			}
			project := deviceInfo.Project
			if project == umw.PowerSwap || project == umw.PUS4 {
				continue
			}

			alarmIdInt, atoiErr := strconv.Atoi(item.AlarmId)
			if atoiErr != nil {
				panic(atoiErr)
			}
			// 二代站告警id偏移量1000000
			if project == umw.PowerSwap2 {
				alarmIdInt -= 1000000
			}
			// 三代站告警id偏移量4000000
			if project == umw.PUS3 {
				alarmIdInt -= 4000000
			}
			// 四代站告警id偏移量10000000
			if project == umw.PUS4 {
				alarmIdInt -= 10000000
			}
			if alarmIdInt < 0 {
				continue
			}
			alarmId := strconv.Itoa(alarmIdInt)

			// 所有告警
			now := time.Now()
			record := umw.MongoAlarmRecord{
				AlarmType: 1,
				DataId:    alarmId,
				DeviceId:  deviceInfo.DeviceId,
				InsertTS:  now.UnixMilli(),
				CreateTS:  item.AlarmRaiseTime.UnixMilli() - 28800000,
				ClearTS:   item.AlarmClearTime.UnixMilli() - 28800000,
				UploadTS:  item.AlarmRaiseTime.UnixMilli() - 28800000,
				Date:      now,
			}
			if record.ClearTS > record.CreateTS {
				record.State = 1
			} else {
				record.State = 2
			}
			month := item.AlarmRaiseTime.Month()
			if month == 5 {
				if project == umw.PowerSwap2 {
					deviceAlarms52 = append(deviceAlarms52, record)
				} else if project == umw.PUS3 {
					deviceAlarms53 = append(deviceAlarms53, record)
				}
			} else if month == 6 {
				if project == umw.PowerSwap2 {
					deviceAlarms62 = append(deviceAlarms62, record)
				} else if project == umw.PUS3 {
					deviceAlarms63 = append(deviceAlarms63, record)
				}
			}

			// 挂车告警
			isSpecialStuckAlarm := false
			if alarmId == "715521" || alarmId == "300403" {
				isSpecialStuckAlarm = true
			}
			_, ok = cache.StuckAlarmInfoCache.GetSingleStuckAlarm(project, alarmId)
			if !ok && !isSpecialStuckAlarm {
				continue
			}
			stuckRecord := umw.MongoStuckAlarmRecord{
				DeviceId:  deviceInfo.DeviceId,
				Project:   project,
				AlarmId:   alarmId,
				AlarmType: 1,
				CreateTs:  item.AlarmRaiseTime.UnixMilli(),
				ClearTs:   item.AlarmClearTime.UnixMilli(),
				InsertTs:  now.UnixMilli(),
				Date:      now,
			}
			stuckAlarms = append(stuckAlarms, stuckRecord)
		}

		if err = w.Mongodb().NewMongoEntry(bson.D{{}}).InsertMany("alarminfo", "powerswap2_5_test", deviceAlarms52); err != nil {
			fmt.Println("fail to insert powerswap2_5_test, offset:", offset, err)
		}
		if err = w.Mongodb().NewMongoEntry(bson.D{{}}).InsertMany("alarminfo", "powerswap2_6_test", deviceAlarms62); err != nil {
			fmt.Println("fail to insert powerswap2_6_test, offset:", offset, err)
		}
		if err = w.Mongodb().NewMongoEntry(bson.D{{}}).InsertMany("alarminfo", "pus3_5_test", deviceAlarms53); err != nil {
			fmt.Println("fail to insert pus3_5_test, offset:", offset, err)
		}
		if err = w.Mongodb().NewMongoEntry(bson.D{{}}).InsertMany("alarminfo", "pus3_6_test", deviceAlarms63); err != nil {
			fmt.Println("fail to insert pus3_6_test, offset:", offset, err)
		}
		if err = w.Mongodb().NewMongoEntry(bson.D{{}}).InsertMany("diagnosis-management", "alarm_stuck_test", stuckAlarms); err != nil {
			fmt.Println("fail to insert alarm_stuck_test, offset:", offset, err)
		}

		if cnt < int64(offset) {
			break
		}
	}

}

func Test_PushRedRabbitParams(t *testing.T) {
	err := PushRedRabbitParams()
	if err != nil {
		t.Fatal(err)
	}
}

func TestInternalHandler_PushMazuParams(t *testing.T) {
	err := PushMazuParams()
	if err != nil {
		t.Fatal(err)
	}
}
