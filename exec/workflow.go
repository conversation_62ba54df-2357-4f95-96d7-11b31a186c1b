package exec

import (
	"fmt"
	"git.nevint.com/welkin2/welkin-backend/util"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"

	ucfg "git.nevint.com/golang-libs/common-utils/config"
	"git.nevint.com/golang-libs/common-utils/larkservice"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	"git.nevint.com/welkin2/welkin-backend/client"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
)

type WFConfig struct {
	ucfg.WFConfig
	AppId     string
	AppSecret string
}

func ConvertContext(wfContext map[string]interface{}) (ctx map[string]string, uploadFile []map[string]string, err error) {
	ctx = make(map[string]string)
	for k, v := range wfContext {
		if k == "Local_File" {
			fList, ok := v.([]interface{})
			if !ok {
				err = fmt.Errorf("Local_File should be []map, get: %+v", v)
				return
			}
			uploadFile = make([]map[string]string, len(fList))
			for i, f := range fList {
				m := make(map[string]string)
				localFile, ok := f.(map[string]interface{})
				if !ok {
					err = fmt.Errorf("Local_File item should be map, get: %+v", f)
					return
				}
				for lk, lv := range localFile {
					if str, ok := lv.(string); ok {
						m[lk] = str
					} else {
						err = fmt.Errorf("invalid value, key: Local_File.%s, context: %+v", lk, wfContext)
						return
					}
				}
				uploadFile[i] = m
			}
		} else {
			if str, ok := v.(string); ok {
				ctx[k] = str
			} else {
				err = fmt.Errorf("invalid value, key: %s, context: %+v", k, wfContext)
				return
			}
		}
	}
	return
}

type Workflow interface {
	LarkMoveWikiCallback() gin.HandlerFunc
}

type workflow struct {
	WFConfig    WFConfig
	WikiBot     ucfg.WikiBotConfig
	MongoClient *client.MongoClient
	Logger      *zap.SugaredLogger
}

func NewWorkflowHandler(conf *ucfg.Config) Workflow {
	return &workflow{
		WFConfig: WFConfig{
			conf.Workflow,
			conf.Sentry.AppId,
			conf.Sentry.AppSecret,
		},
		WikiBot:     conf.WikiBot,
		MongoClient: client.GetMongoClient(conf.Mongodb.Backend, log.Logger.Named("MongoDBClient")),
		Logger:      log.Logger.Named("Workflow"),
	}
}

var FileClassMap = map[string]string{
	"1": "流程&程序文件",
	"2": "指导书&SOP",
	"3": "表单&模板",
}

var ObjTypeMap = map[string]string{
	"docs":   "doc",
	"docx":   "docx",
	"sheets": "sheet",
	"base":   "bitable",
	"file":   "file",
}

func (w *workflow) LarkMoveWikiCallback() gin.HandlerFunc {
	return func(c *gin.Context) {
		var err error
		var request model.ApprovalCallback
		// 发送飞书通知到指定人
		defer func() {
			if err != nil {
				var applicant string
				if request.Context["workflow_creator"] != nil {
					if creator, ok := request.Context["workflow_creator"].(string); ok {
						applicant = creator
					}
				}
				params := larkservice.WikiErrorParams{
					ErrorMsg:       err.Error(),
					Applicant:      applicant,
					FlowInstanceId: request.FlowInstanceId,
				}
				cardContent, cErr := larkservice.NewInfoCard().MakeWikiError(params).Build()
				if cErr != nil {
					log.CtxLog(c).Errorf("make card err: %s", cErr.Error())
					return
				}
				receiver := larkservice.Receiver{
					Type:       larkim.ReceiveIdTypeEmail,
					ReceiveIds: w.WikiBot.Notify,
				}
				if cErr = larkservice.SendCard(cardContent, receiver); cErr != nil {
					log.CtxLog(c).Errorf("send card err: %s", cErr.Error())
					return
				}
			}
		}()
		if err = c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{"result_code": "fail", "message": err.Error()})
			return
		}
		log.CtxLog(c).Infof("get wf callback request: %+v", request)

		wfContext, uploadFileList, err := ConvertContext(request.Context)
		if err != nil {
			log.CtxLog(c).Errorf("workflow callback err: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{"result_code": "fail", "message": err.Error()})
			return
		}

		wikiClient := client.NewWikiClient(w.WikiBot.AppId, w.WikiBot.AppSecret).SpaceId(w.WikiBot.SpaceId).ShareFolder(w.WikiBot.ShareFolder)

		record := umw.MongoLarkWikiApproval{
			FlowInstanceId:   request.FlowInstanceId,
			UserId:           wfContext["workflow_creator"],
			Owner:            wfContext["Owner"],
			Description:      wfContext["Description"],
			UploadMode:       wfContext["Upload_mod"],
			FileUrl:          wfContext["Approve_File"],
			WikiType:         FileClassMap[wfContext["File_Type"]],
			Status:           request.Status,
			CurrentNodeName:  request.CurrentNodeName,
			PreviousNodeName: request.PreviousNodeName,
			CreatedTime:      time.Now().UnixMilli(),
			UpdatedTime:      time.Now().UnixMilli(),
		}
		if len(uploadFileList) > 0 {
			record.FileDownloadUrl = uploadFileList[0]["url"]
			record.FileName = uploadFileList[0]["name"]
		}
		if wfContext["First_or_not"] == "1" {
			record.FirstRelease = true
		} else if wfContext["First_or_not"] == "2" {
			record.OriginFileUrl = wfContext["Original_File_address"]
		} else {
			record.OriginFileUrl = record.FileUrl
		}
		if record.UploadMode == "1" {
			record.ObjType = "file"
		} else {
			fileToken, _ := util.GetUrlToken(record.FileUrl)
			_, objType, _, gErr := wikiClient.GetWikiNodeInfo(fileToken)
			if gErr != nil {
				err = fmt.Errorf("%v, flowInstanceId: %s", gErr, request.FlowInstanceId)
				log.CtxLog(c).Error(err)
				c.JSON(http.StatusInternalServerError, gin.H{"result_code": "fail", "message": err.Error()})
				return
			}
			record.FileToken = fileToken
			record.ObjType = objType
		}

		// 获取父节点token
		var parentToken string
		parentToken, err = wikiClient.GetParentNode(wfContext["Process_group"], record.WikiType)
		if err != nil {
			log.CtxLog(c).Errorf("%v, flowInstanceId: %s", err, request.FlowInstanceId)
			c.JSON(http.StatusInternalServerError, gin.H{"result_code": "fail", "message": err.Error()})
			return
		}
		// 首次调用接口，进行检查
		if c.Query("check") == "1" {
			// 若首次上传，则不做校验
			if record.FirstRelease {
				c.JSON(http.StatusOK, gin.H{"result_code": "success", "message": "ok"})
				return
			}
			// 检查上传方式是否与之前一致
			originWikiToken, _ := util.GetUrlToken(record.OriginFileUrl)
			_, objType, originParentToken, gErr := wikiClient.GetWikiNodeInfo(originWikiToken)
			if gErr != nil {
				err = fmt.Errorf("fail to get origin wiki node info, err: %v, originFileUrl: %s, flowInstanceId: %s", gErr, record.OriginFileUrl, request.FlowInstanceId)
				log.CtxLog(c).Error(err)
				c.JSON(http.StatusInternalServerError, gin.H{"result_code": "fail", "message": err.Error()})
				return
			}
			if objType != record.ObjType {
				err = fmt.Errorf("unexceped upload mode, expect: %s, get: %s, flowInstanceId: %s", objType, record.ObjType, request.FlowInstanceId)
				log.CtxLog(c).Error(err)
				c.JSON(http.StatusInternalServerError, gin.H{"result_code": "fail", "message": err.Error()})
				return
			}
			// 判断上传的目录与原文件所在目录是否一致
			if originParentToken != parentToken {
				err = fmt.Errorf("unexceped upload folder token, expect: %s, get: %s, flowInstanceId: %s", originParentToken, parentToken, request.FlowInstanceId)
				log.CtxLog(c).Error(err)
				c.JSON(http.StatusInternalServerError, gin.H{"result_code": "fail", "message": err.Error()})
				return
			}
			c.JSON(http.StatusOK, gin.H{"result_code": "success", "message": "ok"})
			return
		}

		// 记录wf回调数据
		err = w.MongoClient.NewMongoEntry(bson.D{{"flow_instance_id", request.FlowInstanceId}}).ReplaceOne(
			umw.OAuthDB, umw.LarkWikiApprovalHistory, record, true, client.IndexOption{
				Name:   "flow_instance_id",
				Fields: bson.D{{"flow_instance_id", 1}},
				Unique: true,
			})
		if err != nil {
			log.CtxLog(c).Errorf("insert lark wiki approval history err: %s, flowInstanceId: %s", err.Error(), request.FlowInstanceId)
			c.JSON(http.StatusInternalServerError, gin.H{"result_code": "fail", "message": err.Error()})
			return
		}

		// 在原文档上修改，不需要做任何操作
		if wfContext["First_or_not"] == "3" {
			c.JSON(http.StatusOK, gin.H{"result_code": "success", "message": "ok"})
			return
		}

		if record.Status != "success" {
			if record.UploadMode == "2" {
				// 移动文件到审批未通过目录
				_, err = wikiClient.MoveWiki(w.WikiBot.ReviewSpaceId, record.FileToken, w.WikiBot.ReviewSpaceId, w.WikiBot.ReviewRejectFolder)
				if err != nil {
					log.CtxLog(c).Errorf("fail to move rejected file, %v, flowInstanceId: %s", err, request.FlowInstanceId)
				}
			}
			log.CtxLog(c).Infof("workflow status %s, flowInstanceId: %s", record.Status, request.FlowInstanceId)
			c.JSON(http.StatusOK, gin.H{"result_code": "success", "message": "ok"})
			return
		}

		var wikiToken string
		if record.UploadMode == "1" {
			// 只支持单文件上传
			if len(uploadFileList) == 0 {
				err = fmt.Errorf("fail to get uploadFile, request: %+v", request)
				c.JSON(http.StatusBadRequest, gin.H{"result_code": "fail", "message": err.Error()})
				return
			}
			uploadFile := uploadFileList[0]
			if uploadFile["result_code"] != "success" {
				err = fmt.Errorf("workflow fail to upload file, flowInstanceId: %s", request.FlowInstanceId)
				log.CtxLog(c).Error(err)
				c.JSON(http.StatusInternalServerError, gin.H{"result_code": "fail", "message": err.Error()})
				return
			}
			// 文件上传
			record.FileToken, err = wikiClient.UploadFile(record.FileDownloadUrl, record.FileName)
			if err != nil {
				err = fmt.Errorf("%v, flowInstanceId: %s", err, request.FlowInstanceId)
				log.CtxLog(c).Error(err)
				c.JSON(http.StatusInternalServerError, gin.H{"result_code": "fail", "message": err.Error()})
				return
			}

			// 移动文件并等待移动完成
			wikiToken, err = wikiClient.MoveDocToWiki(record.FileToken, record.ObjType, parentToken)
			if err != nil {
				log.CtxLog(c).Errorf("%v, flowInstanceId: %s", err, request.FlowInstanceId)
				c.JSON(http.StatusInternalServerError, gin.H{"result_code": "fail", "message": err.Error()})
				return
			}

			// 删除待审核目录中的快捷方式
			if err = wikiClient.DeleteShortcuts(); err != nil {
				log.CtxLog(c).Errorf("%v, flowInstanceId: %s", err, request.FlowInstanceId)
			}
		} else {
			// 从待审核目录移动到指定目录
			if wikiToken, err = wikiClient.MoveWiki(w.WikiBot.ReviewSpaceId, record.FileToken, w.WikiBot.SpaceId, parentToken); err != nil {
				log.CtxLog(c).Errorf("%v, flowInstanceId: %v", err, request.FlowInstanceId)
				c.JSON(http.StatusInternalServerError, gin.H{"result_code": "fail", "message": err.Error()})
				return
			}
		}

		// 获取父节点标题
		record.WikiFolder, _, _, err = wikiClient.GetWikiNodeInfo(wfContext["Process_group"])
		if err != nil {
			log.CtxLog(c).Errorf("%v, flowInstanceId: %s", err, request.FlowInstanceId)
			c.JSON(http.StatusInternalServerError, gin.H{"result_code": "fail", "message": err.Error()})
			return
		}

		// 获取知识库文档标题
		record.WikiTitle, _, _, err = wikiClient.GetWikiNodeInfo(wikiToken)
		if err != nil {
			log.CtxLog(c).Errorf("%v, flowInstanceId: %s", err, request.FlowInstanceId)
			c.JSON(http.StatusInternalServerError, gin.H{"result_code": "fail", "message": err.Error()})
			return
		}

		// TODO:更新文件版本，当前版本实现直接删除知识库中的旧版本文件（移动到知识库回收站目录）
		if !record.FirstRelease {
			originWikiToken, _ := util.GetUrlToken(record.OriginFileUrl)
			if _, err = wikiClient.MoveWiki(w.WikiBot.SpaceId, originWikiToken, w.WikiBot.SpaceId, w.WikiBot.RmFolder); err != nil {
				log.CtxLog(c).Errorf("%v, flowInstanceId: %v", err, request.FlowInstanceId)
			}
		}

		// 更新文档权限
		permission := larkservice.LarkFilePermission{
			SecurityEntity:           "only_full_access",
			CommentEntity:            "anyone_can_edit",
			ShareEntity:              "same_tenant",
			ManageCollaboratorEntity: "collaborator_full_access",
			LinkShareEntity:          "tenant_readable",
			CopyEntity:               "anyone_can_edit",
		}
		if err = wikiClient.ChangePermission(wikiToken, "wiki", permission); err != nil {
			log.CtxLog(c).Errorf("fail to change permission, err: %v, instanceId: %s", err, request.FlowInstanceId)
		}

		_, err = w.MongoClient.NewMongoEntry(bson.D{{"flow_instance_id", request.FlowInstanceId}}).UpdateAndGetOne(
			umw.OAuthDB, umw.LarkWikiApprovalHistory, bson.D{
				{"$set", bson.M{
					"file_token":   record.FileToken,
					"wiki_node":    wikiToken,
					"wiki_title":   record.WikiTitle,
					"wiki_folder":  record.WikiFolder,
					"updated_time": time.Now().UnixMilli(),
				}},
			})
		if err != nil {
			log.CtxLog(c).Errorf("update lark wiki approval history err: %s, flowInstanceId: %s", err.Error(), request.FlowInstanceId)
			c.JSON(http.StatusInternalServerError, gin.H{"result_code": "fail", "message": err.Error()})
			return
		}
		c.JSON(http.StatusOK, gin.H{"result_code": "success", "message": "ok"})
	}
}
