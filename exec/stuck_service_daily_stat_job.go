package exec

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/logger"
)

type DailyStuckDiagnosisHandler struct {
	Watcher                      client.Watcher
	Cfg                          ucfg.Config
	TimeLocation                 *time.Location
	stuckServiceResult           map[string][]DailyStuckServiceStat // day -> daily stats
	stuckAlarmStuckServiceResult map[string][]DailyStuckAlarmStat
}

func (d *DailyStuckDiagnosisHandler) Process(ctx context.Context, dateStr string) error {
	logger.CtxLog(ctx).Infof("start DailyStuckDiagnosisHandler. date:%v", dateStr)
	startExcteTime := time.Now()
	err := d.prepare(ctx)
	if err != nil {
		return err
	}
	err = d.calculate(ctx, dateStr)
	if err != nil {
		return err
	}
	err = d.store(ctx)
	logger.CtxLog(ctx).Infof("finish DailyStuckDiagnosisHandler. date:%v cost:%v", dateStr, time.Since(startExcteTime))
	return err
}

func (d *DailyStuckDiagnosisHandler) prepare(ctx context.Context) error {
	d.stuckServiceResult = map[string][]DailyStuckServiceStat{}
	d.stuckAlarmStuckServiceResult = map[string][]DailyStuckAlarmStat{}
	return nil
}

func (d *DailyStuckDiagnosisHandler) store(ctx context.Context) error {
	for dayStr, stuckServiceStats := range d.stuckServiceResult {
		for _, stuckServiceStat := range stuckServiceStats {
			err := d.Watcher.Mongodb().UpdateOneWithIdx("diagnosis-management", "daily-stuck-service-stat", bson.D{
				bson.E{Key: "stat_day", Value: dayStr},
				bson.E{Key: "device_id", Value: stuckServiceStat.DeviceId},
			}, stuckServiceStat, true, []client.IndexOption{
				{Name: "stat_day_device_id", Fields: bson.D{
					{"stat_day", 1},
					{"device_id", 1},
				}, Unique: true},
				{Name: "stat_day_project", Fields: bson.D{
					{"stat_day", 1},
					{"project", 1},
				}, Unique: false},
				{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 180 * 24 * 3600},
			}...)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func (d *DailyStuckDiagnosisHandler) calculate(ctx context.Context, dayStr string) error {
	startTime, err := time.ParseInLocation("2006-01-02", dayStr, d.TimeLocation)
	if err != nil {
		return err
	}
	endTime := startTime.Add(time.Hour * 24)

	deviceSet := map[string]umw.MongoDeviceInfo{}
	for _, devieinfo := range cache.PowerSwapCache.GetAllDevices() {
		if devieinfo.DeviceId == "" || !(devieinfo.Project == "PUS4" || devieinfo.Project == "PUS3" || devieinfo.Project == "PowerSwap2") {
			continue
		}
		// TODO 测试环境几万个站
		if ucmd.GetEnv() != "prod" {
			if !(devieinfo.DeviceId == "PS-NIO-f787d203-1db669cd" || devieinfo.DeviceId == "PS-NIO-3285ff15-7f564f27") {
				continue
			}
		}
		deviceSet[devieinfo.DeviceId] = devieinfo
	}

	for deviceId, deviceInfo := range deviceSet {
		filter := bson.D{
			{"device_id", deviceId},
			{"service_start_time", bson.M{"$gte": startTime.UnixMilli(), "$lte": endTime.UnixMilli()}},
		}
		stuckCount, er := d.Watcher.Mongodb().Client.Database("diagnosis-management").Collection("stuck-service").CountDocuments(ctx, filter)
		if er != nil {
			return er
		}

		ct := ucmd.NewHttpClient(ucmd.HttpClient{
			URL: fmt.Sprintf("%s/device/v1/service-info/%v/list?start_time=%v&end_time=%v&page=1&size=1&descending=true&device_id=%v",
				d.Cfg.Welkin.BackendUrl, deviceInfo.Project, startTime.UnixMilli(), endTime.UnixMilli(), deviceId),
			Method: "GET",
		})
		body, statusCode, err := ct.Do()
		if err != nil {
			return err
		}
		defer body.Close()
		data, err := io.ReadAll(body)
		if err != nil {
			return err
		}
		if statusCode != http.StatusOK {
			return errors.New("http response status code not ok")
		}
		var response struct {
			ErrorCode int64  `json:"err_code"`
			Message   string `json:"message"`
			Total     int64  `json:"total"`
		}
		if err = json.Unmarshal(data, &response); err != nil {
			return errors.New(fmt.Sprintf("fail to unmarshal response, err: %v", err))
		}
		if response.ErrorCode != 0 {
			return errors.New(fmt.Sprintf("fail to get, err_code: %v, message: %v", response.ErrorCode, response.Message))
		}
		d.stuckServiceResult[dayStr] = append(d.stuckServiceResult[dayStr], DailyStuckServiceStat{
			DeviceId:          deviceId,
			Project:           deviceInfo.Project,
			StatDay:           dayStr,
			StuckServiceCount: stuckCount,
			TotalServieCount:  response.Total,
		})
	}

	filter := bson.D{
		{"create_ts", bson.M{"$gte": time.UnixMilli(startTime.UnixMilli()), "$lte": time.UnixMilli(endTime.UnixMilli())}},
	}
	Limit := int64(1000)
	Offset := int64(0)
	raiseCount := map[string]int{}
	lead2StuckCount := map[string]int{}
	for true {
		var stuckAlarmRecords []umw.MongoStuckAlarmRecord
		cur, err := d.Watcher.Mongodb().Client.Database("diagnosis-management").Collection("stuck-alarm").Find(ctx, filter, options.Find().SetSort(bson.M{"create_ts": 1}).SetSkip(Offset).SetLimit(Limit))
		if err = cur.All(ctx, &stuckAlarmRecords); err != nil {
			return err
		}

		for _, stuckAlarmRecord := range stuckAlarmRecords {
			raiseCount[stuckAlarmRecord.AlarmId]++
			if stuckAlarmRecord.ServiceId != "" {
				lead2StuckCount[stuckAlarmRecord.AlarmId]++
			}
		}

		Offset = Offset + int64(len(stuckAlarmRecords))
		if int64(len(stuckAlarmRecords)) != Limit {
			break
		}
	}

	allStuckAlarmInfo := []*umw.MongoStuckAlarmInfo{}
	allStuckAlarmInfo = append(allStuckAlarmInfo, cache.StuckAlarmInfoCache.GetAllStuckAlarms("PUS4")...)
	allStuckAlarmInfo = append(allStuckAlarmInfo, cache.StuckAlarmInfoCache.GetAllStuckAlarms("PUS3")...)
	allStuckAlarmInfo = append(allStuckAlarmInfo, cache.StuckAlarmInfoCache.GetAllStuckAlarms("PowerSwap2")...)

	for _, stuckAlarmInfo := range allStuckAlarmInfo {
		rCount := 0
		value, exist := raiseCount[stuckAlarmInfo.AlarmId]
		if exist {
			rCount = value
		}
		sCount := 0
		value, exist = lead2StuckCount[stuckAlarmInfo.AlarmId]
		if exist {
			sCount = value
		}
		d.stuckAlarmStuckServiceResult[dayStr] = append(d.stuckAlarmStuckServiceResult[dayStr], DailyStuckAlarmStat{
			AlarmId:          stuckAlarmInfo.AlarmId,
			Project:          stuckAlarmInfo.Project,
			StatDay:          dayStr,
			RaiseCount:       int64(rCount),
			LeadToStuckCount: int64(sCount),
		})
	}

	return nil
}

type DailyStuckServiceStat struct {
	DeviceId          string `json:"device_id,omitempty" bson:"device_id,omitempty"`
	Project           string `json:"project,omitempty" bson:"project,omitempty"`
	StatDay           string `json:"stat_day,omitempty" bson:"stat_day,omitempty"`
	StuckServiceCount int64  `json:"stuck_service_count,omitempty" bson:"stuck_service_count,omitempty"`
	TotalServieCount  int64  `json:"total_servie_count,omitempty" bson:"total_servie_count,omitempty"`
}

type DailyStuckAlarmStat struct {
	AlarmId          string
	Project          string
	StatDay          string // YYYY-MM-DD
	LeadToStuckCount int64
	RaiseCount       int64
}
