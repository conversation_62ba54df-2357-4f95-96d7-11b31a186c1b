package exec

import (
	"bytes"
	"git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	um "git.nevint.com/golang-libs/common-utils/model"
	"git.nevint.com/welkin2/welkin-backend/client"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	"git.nevint.com/welkin2/welkin-backend/model/mongo"
	"git.nevint.com/welkin2/welkin-backend/service"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
	"io"
	"net/http"
	"strings"
	"time"
)

type FMS interface {
	UploadOneFile() gin.HandlerFunc
	CallBack() gin.HandlerFunc
	UploadOneFileMultipart() gin.HandlerFunc
	DeleteFileList() gin.HandlerFunc
}

type fms struct {
	logger *zap.SugaredLogger
	conf   *ucfg.Config
	FMS    service.FMS
	area   string
}

func NewFMSHandler(conf *ucfg.Config, area string) FMS {
	return &fms{
		logger: log.Logger.Named("FMS"),
		conf:   conf,
		area:   area,
		FMS: service.FMS{
			URL:          conf.FMS.Url,
			AppId:        conf.Sentry.AppId,
			AppSecret:    conf.Sentry.AppSecret,
			ClientId:     conf.FMS.ClientId,
			ClientSecret: conf.FMS.ClientSecret,
			PriBucketKey: conf.FMS.PriBucketKey,
			PubBucketKey: conf.FMS.PubBucketKey,
			Header: map[string]string{
				"Content-Type": "application/json;charset=UTF-8",
			},
			Logger: log.Logger.Named("FMS"),
		},
	}
}
func (f *fms) UploadOneFile() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response um.Base
		file, fh, err := c.Request.FormFile("file")
		fileDir := c.PostForm("file_dir")
		if len(fileDir) == 0 {
			log.CtxLog(c).Errorf("no find file_dir, err: %s", err.Error())
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if !strings.HasSuffix(fileDir, "/") {
			fileDir += "/"
		}
		fileType := c.PostForm("file_type")
		area := c.PostForm("area")
		if len(area) == 0 {
			area = f.area
		}
		if err != nil {
			log.CtxLog(c).Errorf("upload image, err: %s", err.Error())
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		defer file.Close()
		if fileType == model.IMAGE && (fh.Size > model.MAXIMAGESIZE || fh.Size < model.MINIMAGESIZE) {
			log.CtxLog(c).Errorf("upload image, file size mismatched")
			um.FailWithBadRequest(c, &response, "file size mismatched")
			return
		}
		buffer := bytes.NewBuffer(nil)
		if _, err = io.Copy(buffer, file); err != nil {
			log.CtxLog(c).Errorf("upload image, err: %s", err.Error())
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		ib := &service.ImageBuffer{FMS: f.FMS, Buffer: buffer, Ctx: c}
		res, err := ib.FMS.GetFileUploadToken(fileDir, fh.Filename, fileType, ib.Buffer.String(), area)
		if err != nil {
			log.CtxLog(c).Errorf("upload file to fms, err: %s", err.Error())
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		rd := res.ResultData
		switch fileType {
		case model.IMAGE:
			rd.SupplierHttp.Header["Content-Type"] = "image/jpeg"
		case model.FILE:
			rd.SupplierHttp.Header["Content-Type"] = "application/octet-stream"
		}
		if err = ib.FMS.UploadFile(rd.SupplierHttp.URL, rd.SupplierHttp.Header, bytes.NewReader(ib.Buffer.Bytes())); err != nil {
			log.CtxLog(c).Errorf("upload file to fms, err: %s", err.Error())
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (f *fms) DeleteFileList() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response um.Base
		var request struct {
			FileUrlList []string `json:"file_url_list"`
		}
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("fail to parse request body: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if err := f.FMS.DeleteFile(c, request.FileUrlList); err != nil {
			log.CtxLog(c).Errorf("fail to delete file list: %v, files: %v", err, request.FileUrlList)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (f *fms) UploadOneFileMultipart() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response um.Base
		_, fh, err := c.Request.FormFile("file")
		fileDir := c.PostForm("file_dir")
		if len(fileDir) == 0 {
			log.CtxLog(c).Errorf("no find file_dir, err: %s", err.Error())
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if !strings.HasSuffix(fileDir, "/") {
			fileDir += "/"
		}
		if _, err = f.FMS.UploadMultipart(c, fh, fileDir); err != nil {
			log.CtxLog(c).Errorf("fail to upload multipart file, %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (f *fms) CallBack() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			req  CallBackRequest
			resp um.Base
		)
		err := c.BindJSON(&req)
		if err != nil {
			log.CtxLog(c).Errorf("invalid request. req:%v", cmd.ToJsonStrIgnoreErr(req))
			um.FailWithBadRequest(c, &resp, "invalid request")
			return
		}
		filter := bson.D{bson.E{Key: "task_id", Value: req.TaskId}}

		err = client.GetWatcher().Mongodb().NewMongoEntry(filter).UpdateOne(mongo.FmsDataBaseName, mongo.FmsTaskStatusCollectionName,
			bson.M{"$set": bson.M{
				"status":    req.Status,
				"result":    req.Result,
				"update_ts": time.Now().UnixMilli(),
			}},
			false)
		if err != nil {
			log.CtxLog(c).Errorf("db update err. req:%v err:%v", cmd.ToJsonStrIgnoreErr(req), err)
			um.FailWithInternalServerError(c, &resp, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &resp, "ok", http.StatusOK)
		return
	}
}

type CallBackRequest struct {
	TaskId string `json:"task_id" form:"task_id"`
	Status string `json:"status" form:"status"`
	Result struct {
	} `json:"result" form:"result"`
}
