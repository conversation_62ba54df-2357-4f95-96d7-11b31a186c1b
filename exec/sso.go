package exec

import (
	"time"

	ucfg "git.nevint.com/golang-libs/common-utils/config"
	"git.nevint.com/golang-libs/common-utils/lanka"
	niosso "git.nevint.com/golang-libs/common-utils/nio-sso"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
)

func NewSSO(conf *ucfg.Config) *niosso.SSO {
	rconf := conf.Redis
	rc := &niosso.RedisConf{
		Host: rconf.Address[0],
		Pass: rconf.Password,
		DB:   *rconf.DB,
	}
	return niosso.NewSSO(conf.SSO.SignUrl, conf.Sentry.AppId, conf.Sentry.AppSecret, rc, log.Logger.Named(model.SYSTEM))
}

func NewRBAC(conf *ucfg.Config) *lanka.Rbac {
	cache := lanka.NewInMemoryCache(5 * time.Second)
	return lanka.NewRbac(conf.Sentry.AppId, conf.Sentry.AppSecret, conf.SSO.LankaUrl, cache, log.Logger.Named(model.SYSTEM))
}
