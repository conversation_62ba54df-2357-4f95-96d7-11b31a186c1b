package exec

import (
	"encoding/xml"
	"fmt"
	"git.nevint.com/welkin2/welkin-backend/cache"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	um "git.nevint.com/golang-libs/common-utils/model"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/constant"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	"git.nevint.com/welkin2/welkin-backend/util"
	"github.com/gin-gonic/gin"
	"github.com/tealeg/xlsx"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

type EuroDataInfo interface {
	GetEuroPriceData() gin.HandlerFunc
	GetEuroWindSolarData() gin.HandlerFunc
	GetNetherlandsImbalancePrice() gin.HandlerFunc
	GetNetherlandsAfrr() gin.HandlerFunc
	GetFcrdPrice() gin.HandlerFunc
}

type euroDataInfo struct {
	watcher client.Watcher
	logger  *zap.SugaredLogger
	areas   []string
}

func NewEuroDataInfoHandler(watcher client.Watcher, areas []string) EuroDataInfo {
	logger := log.Logger.Named("EuroData")
	if err := cache.CacheRealtimeDataID(watcher.Mongodb().Client); err != nil {
		logger.Panicf("failed to euroData mongodb, err: %v", err)
	}
	return &euroDataInfo{
		watcher: watcher,
		logger:  logger,
		areas:   areas,
	}
}

// 每分钟更新
func (e *euroDataInfo) GetNetherlandsImbalancePrice() gin.HandlerFunc {
	return func(c *gin.Context) {
		var document model.BALANCE_DELTA
		var result model.ImbalancePrice2
		var response um.Base

		getUrl := util.GetImbalanceUrl()
		resp, err := http.Get(getUrl)
		ts := fmt.Sprintf("%v", time.Now())

		if err != nil {
			log.CtxLog(c).Errorf("Error making the request of getNetherlands ImbalancePrice: %s on %v", err, ts)
			um.FailWithBadRequest(c, &response, "err to get Url")
			return
		}

		if resp.StatusCode == http.StatusOK {

			body, err := io.ReadAll(resp.Body)

			if err != nil {
				log.CtxLog(c).Errorf("Error reading the response body: %s on %v", err, ts)
				um.FailWithBadRequest(c, &response, "error reading response body")
				return
			}
			defer resp.Body.Close()

			err = xml.Unmarshal(body, &document)
			if err != nil {
				log.CtxLog(c).Errorf("Error parsing XML: %s on %v", err, ts)
				um.FailWithBadRequest(c, &response, "error parsing xml")
				return
			}

			for _, r := range document.Records {
				if r.SQUENCE_NUMBER >= 1411 && time.Now().Hour() == 0 {
					result.PublishTs, _ = util.MinutesToTimestamp(util.GetDataUnixDayAhead(), r.Time)
				} else {
					result.PublishTs, _ = util.MinutesToTimestamp(util.GetDateUnix(), r.Time)
				}
				result.UnitPrice = "Euro"
				result.MinPrice = r.MinPrice
				result.MaxPrice = r.MaxPrice
				result.Number = r.SQUENCE_NUMBER

				// 检查数据库中是否存在具有相同 PublishTs 的记录
				existingRecord, err := e.watcher.Mongodb().NewMongoEntry(bson.D{bson.E{"publish_ts", result.PublishTs}}).GetOne(constant.FCRDManagement, constant.NetherlandsImbalancePrice)
				if err != nil {
					log.CtxLog(c).Errorf("Error querying database: %s on %v", err, ts)
					um.FailWithInternalServerError(c, &response, "error querying database")
					return
				}
				indexOptions := []client.IndexOption{
					{Name: "publish_ts", Fields: bson.D{{"publish_ts", 1}}},
				}
				if existingRecord == nil {
					err := e.watcher.Mongodb().NewMongoEntry(bson.D{bson.E{"publish_ts", result.PublishTs}}).InsertOne(constant.FCRDManagement, constant.NetherlandsImbalancePrice, result, indexOptions...)
					if err != nil {
						log.CtxLog(c).Errorf("Error inserting new record: %s on %v", err, ts)
						um.FailWithInternalServerError(c, &response, "error inserting new record")
						return
					}
					um.SuccessWithMessageForGin(c, &response, "successful fetch NetherlandsImbalancePrice", http.StatusOK)
				}
			}
		} else {
			log.CtxLog(c).Errorf("error parsing xml")
			um.FailWithBadRequest(c, &response, "error parsing xml")
		}

	}
}

// 每天更新
func (e *euroDataInfo) GetEuroPriceData() gin.HandlerFunc {
	return func(c *gin.Context) {

		areasList := make([]string, 0)
		var response um.Base

		for _, a := range e.areas {
			var document model.PublicationMarketDocument
			var record model.PointRecord

			getUrl := util.GetUrlPrice(a)
			resp, err := http.Get(getUrl)

			if err != nil {
				log.CtxLog(c).Errorf("Error making the request: %s for country: %s", err, a)
				areasList = append(areasList, a)
				continue
			}
			if resp.StatusCode == http.StatusOK {
				body, err := io.ReadAll(resp.Body)
				if err != nil {
					log.CtxLog(c).Errorf("Error reading the response body %s for country: %s", err, a)
					areasList = append(areasList, a)
					continue
				}
				defer resp.Body.Close()

				err = xml.Unmarshal(body, &document)
				if err != nil {
					log.CtxLog(c).Errorf("Error parsing XML: ", err)
					areasList = append(areasList, a)

					continue
				}

				record.Area = a
				record.PublishTs = util.GetDateUnix()
				record.PriceMeasureUnitName = document.TimeSeries[0].PriceMeasureUnitName
				record.CurrencyUnitName = document.TimeSeries[0].CurrencyUnitName

				if strings.Contains(a, "Germany") {
					for _, period := range document.TimeSeries[1].Periods {
						for _, point := range period.Points {
							point.Time = util.HoursToTimestamp(record.PublishTs, point.Time)
							record.Points = append(record.Points, point)
						}

					}
				} else {
					for _, period := range document.TimeSeries[0].Periods {
						for _, point := range period.Points {
							point.Time = util.HoursToTimestamp(record.PublishTs, point.Time)
							record.Points = append(record.Points, point)
						}
					}
				}
				indexOptions := []client.IndexOption{
					{Name: "publish_ts", Fields: bson.D{{"publish_ts", 1}}},
					{Name: "area", Fields: bson.D{{"area", 1}}},
				}

				err = e.watcher.Mongodb().NewMongoEntry((bson.D{bson.E{"publish_ts", record.PublishTs}, bson.E{"area", record.Area}})).InsertOne(
					constant.FCRDManagement, constant.EuroPriceInfo, record, indexOptions...)

				if err != nil {
					log.CtxLog(c).Errorf("Insert data failed:", err)
					areasList = append(areasList, a)
					continue
				}

			} else {
				log.CtxLog(c).Errorf("Error: %s - %s\n", resp.Status, resp.Status)
				areasList = append(areasList, a)
				continue
			}

		}

		if len(areasList) == 0 {
			um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		} else {
			areasNameList := ""
			for _, item := range areasList {
				areasNameList = item + " " + areasNameList
			}
			log.CtxLog(c).Errorf(areasNameList + " failed to fetch data on" + time.Now().Format("20160101"))
			um.FailWithBadRequest(c, &response, areasNameList+" failed to fetch data on"+time.Now().Format("20160101"))
		}

	}
}

// 每天更新
func (e *euroDataInfo) GetEuroWindSolarData() gin.HandlerFunc {
	return func(c *gin.Context) {

		areasList := make([]string, 0)
		var response um.Base

		for _, a := range e.areas {
			var document model.PublicationMarketDocument
			var record model.WindSolarForecastRecord
			getUrl := util.GetUrlWindAndSolar(a)
			resp, err := http.Get(getUrl)

			if err != nil {
				log.CtxLog(c).Errorf("Error making the request: %s", err)
				areasList = append(areasList, a)
				continue
			}
			if resp.StatusCode == http.StatusOK {
				body, err := io.ReadAll(resp.Body)
				if err != nil {
					log.CtxLog(c).Errorf("Error reading the response body:", err)
					areasList = append(areasList, a)
					continue
				}
				defer resp.Body.Close()

				err = xml.Unmarshal(body, &document)
				if err != nil {
					log.CtxLog(c).Errorf("Error parsing XML:", err)
					areasList = append(areasList, a)
					continue
				}
				record.Area = a
				record.PublishTs = util.GetDateUnix()
				record.QuantityMeasureUnitName = document.TimeSeries[0].QuantityMeasureUnitName

				for _, timeSeries := range document.TimeSeries {
					record.PsrType = timeSeries.MktPSRType.PsrType
					for _, period := range timeSeries.Periods {
						for _, point := range period.Points {
							record.Points = append(record.Points, point)
						}
					}
					indexOptions := []client.IndexOption{
						{Name: "publish_ts", Fields: bson.D{{"publish_ts", 1}}},
						{Name: "area", Fields: bson.D{{"area", 1}}},
						{Name: "psr_type", Fields: bson.D{{"psr_type", 1}}},
					}
					err = e.watcher.Mongodb().NewMongoEntry((bson.D{bson.E{"publish_ts", record.PublishTs}, bson.E{"psr_type", record.PsrType},
						bson.E{"area", record.Area}})).InsertOne(
						constant.FCRDManagement, constant.EuroWindAndSolarInfo, record, indexOptions...)
					if err != nil {
						log.CtxLog(c).Errorf("Insert data failed:", err)
						areasList = append(areasList, a)
						continue
					}
					record.Points = make([]model.Point, 0)
				}

			} else {
				log.CtxLog(c).Errorf("Error: %s - %s\n", resp.Status, resp.Status)
				areasList = append(areasList, a)
				continue
			}

		}
		if len(areasList) == 0 {
			um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		} else {
			areasNameList := ""
			for _, item := range areasList {
				areasNameList = item + " " + areasNameList
			}
			log.CtxLog(c).Errorf(areasNameList + " failed to fetch data on " + time.Now().Format("20160101"))
			um.FailWithBadRequest(c, &response, areasNameList+" failed to fetch data on "+time.Now().Format("20160101"))
		}

	}
}

// 每天更新
func (e *euroDataInfo) GetNetherlandsAfrr() gin.HandlerFunc {
	return func(c *gin.Context) {
		var document model.Afrr
		var result model.AfrrPrice
		var response um.Base

		getUrl := util.GetAfrrUrl()
		resp, err := http.Get(getUrl)

		if err != nil {
			log.CtxLog(c).Errorf("Error making the request of get Netherlands AfrrPrice: %s", err)
			um.FailWithBadRequest(c, &response, "failed to making the request of url")
			return
		}
		if resp.StatusCode == http.StatusOK {
			body, err := io.ReadAll(resp.Body)

			if err != nil {
				log.CtxLog(c).Errorf("Error reading the response body:", err)
				um.FailWithBadRequest(c, &response, "err of reading the response body")
				return
			}
			defer resp.Body.Close()

			err = xml.Unmarshal(body, &document)

			if err != nil {
				log.CtxLog(c).Errorf("Error parsing XML:", err)
				um.FailWithBadRequest(c, &response, "error parsing xml")
				return
			}

			result.PublishTs = util.GetDateUnix()
			filter := bson.D{bson.E{"publish_ts", result.PublishTs}}

			result.UnitPrice = "Euro"
			result.Records = document.Records
			indexOptions := []client.IndexOption{
				{Name: "publish_ts", Fields: bson.D{{"publish_ts", 1}}},
			}
			err = e.watcher.Mongodb().NewMongoEntry(filter).InsertOne(constant.FCRDManagement, constant.NetherlandsAfrrPrice, result, indexOptions...)
			if err != nil {
				log.CtxLog(c).Errorf("GetNetherlandsAfrr get err %v:", err)
				um.FailWithBadRequest(c, &response, "failed to insert database")
				return
			}
			um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		} else {
			log.CtxLog(c).Errorf("Error: %s - %s\n", resp.Status, resp.Status)
			um.FailWithBadRequest(c, &response, fmt.Sprintf("http status code %v", resp.StatusCode))
		}
	}

}
func (e *euroDataInfo) GetFcrdPrice() gin.HandlerFunc {
	return func(c *gin.Context) {
		var fcrdPrice model.FcrdPrice
		var response um.Base
		date := c.Query("date")
		var dt time.Time
		var err error
		if date != "" {
			dt, err = time.Parse("2006-01-02", date)
			if err != nil {
				log.CtxLog(c).Errorf("Error parsing date: %v", err)
				um.FailWithBadRequest(c, &response, "Error parsing date")
				return
			}
		} else {
			dt = time.Now()
		}
		fileURL1 := util.GetFcrdUrl1(dt)
		resp, err := http.Get(fileURL1)
		if err != nil {
			log.CtxLog(c).Errorf("Error downloading file: %v", err)
			um.FailWithBadRequest(c, &response, "Error downloading file")
			return
		}
		defer resp.Body.Close()

		excelData, err := io.ReadAll(resp.Body)
		if err != nil {
			log.CtxLog(c).Errorf("Error reading response body: %v", err)
			um.FailWithBadRequest(c, &response, "Error reading response body")
			return
		}

		xlFile, err := xlsx.OpenBinary(excelData)
		if err != nil {
			log.CtxLog(c).Errorf("Error opening Excel data: %v", err)
			um.FailWithBadRequest(c, nil, "Error opening Excel data")
			return
		}
		for _, sheet := range xlFile.Sheets {
			// 遍历每行
			t := 0
			for index, row := range sheet.Rows {
				// 遍历每个单元格
				if index == 0 || index == sheet.MaxRow || index == 26 {
					continue
				}
				for i, cell := range row.Cells {
					if i == 1 {
						text := cell.String()
						fcrdPrice.PublishTs = util.GetDateUnixV2(dt)
						fcrdPrice.Unit = "EUR/MW"
						float, _ := strconv.ParseFloat(text, 32)
						fcrdPrice.Records = append(fcrdPrice.Records, model.RecordFcrd{
							Time:             util.HoursToTimestamp2(time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), t, 0, 0, 0, time.Now().Location()).Unix(), int64(t)),
							FcrdDownD_1Price: float,
						})
						t++
					}
				}
			}
		}
		fileURL2 := util.GetFcrdUrl2(dt)
		resp, err = http.Get(fileURL2)
		if err != nil {
			log.CtxLog(c).Errorf("Error downloading file: %v", err)
			um.FailWithBadRequest(c, &response, "Error downloading file")
			return
		}
		defer resp.Body.Close()
		excelData2, err := io.ReadAll(resp.Body)
		if err != nil {
			log.CtxLog(c).Errorf("Error reading response body: %v", err)
			um.FailWithBadRequest(c, &response, "Error reading response body")
			return
		}
		xlFile2, err := xlsx.OpenBinary(excelData2)
		if err != nil {
			log.CtxLog(c).Errorf("Error opening Excel data: %v", err)
			um.FailWithBadRequest(c, &response, "Error opening Excel data")
			return
		}
		for _, sheet := range xlFile2.Sheets {

			// 遍历每行
			for index, row := range sheet.Rows {
				// 遍历每个单元格
				if index == 0 || index == sheet.MaxRow {
					continue
				}
				for i, cell := range row.Cells {
					if i == 1 {
						text := cell.String()
						float, _ := strconv.ParseFloat(text, 64)
						fcrdPrice.Records[index-1].FcrdDownD_2Price = float
					}
				}
			}
		}
		fcrdPrice.Records = fcrdPrice.Records[:24]

		indexOption := []client.IndexOption{
			{Name: "publish_ts", Fields: bson.D{{"publish_ts", 1}}},
		}
		err = e.watcher.Mongodb().NewMongoEntry(bson.D{bson.E{"publish_ts", fcrdPrice.PublishTs}}).InsertOne(constant.FCRDManagement, constant.FcrdPrice, fcrdPrice, indexOption...)

		if err != nil {
			log.CtxLog(c).Errorf("Insert data failed: %v", err)
			um.FailWithBadRequest(c, &response, "Insert data failed")
			return
		} else {
			um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		}
	}

}
