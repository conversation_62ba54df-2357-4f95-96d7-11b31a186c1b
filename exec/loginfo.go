package exec

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gomodule/redigo/redis"
	"github.com/gorilla/websocket"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"github.com/prometheus/client_golang/prometheus"
	"go.mongodb.org/mongo-driver/bson"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	udao "git.nevint.com/golang-libs/common-utils/dao"
	"git.nevint.com/golang-libs/common-utils/lanka"
	"git.nevint.com/golang-libs/common-utils/larkservice"
	"git.nevint.com/golang-libs/common-utils/lock"
	um "git.nevint.com/golang-libs/common-utils/model"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	uprom "git.nevint.com/golang-libs/common-utils/prometheus"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	"git.nevint.com/welkin2/welkin-backend/domain/common"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"
	"git.nevint.com/welkin2/welkin-backend/util"
)

// NotifyRemoteCommand send remote config to the device by OSS,
// it supports `uploadLogFile` and `syncFilePath`
func (d *device) NotifyRemoteCommand() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			requestData model.NotifyDeviceParams
			response    um.Base
		)
		userId := c.GetHeader("X-User-ID")
		if userId == "" {
			log.CtxLog(c).Errorf("NotifyRemoteCommand, need user id")
			um.FailWithMessageForGin(c, &response, "need user id", -1, http.StatusBadRequest)
			return
		}
		project := c.Param("project")
		deviceId := c.Param("device_id")

		if err := c.BindJSON(&requestData); err != nil {
			log.CtxLog(c).Errorf("NotifyRemoteCommand, parse request body, err: %v", err)
			um.FailWithMessageForGin(c, &response, fmt.Sprintf("parse request body, err: %s", err.Error()), -1, http.StatusBadRequest)
			return
		}

		resourceId := deviceId
		cf := requestData.Configuration
		if cf.Key == model.SyncFilePath {
			if cf.Value == "" || cf.Value == "/" || cf.Value == "//" {
				log.CtxLog(c).Errorf("NotifyRemoteCommand, configuration value is incorrect: %v", cf.Value)
				um.FailWithMessageForGin(
					c, &response, "configuration value is incorrect", -3, http.StatusInternalServerError)
				return
			}
			if !strings.HasPrefix(cf.Value, "/") {
				cf.Value = fmt.Sprintf("/%s", cf.Value)
			}
			if !strings.HasSuffix(cf.Value, "/") {
				cf.Value += "/"
			}

			// 前端点击同步目录树时，获取当前服务器时间存储在redis，并在查询目录树时通过比较该时间来判断是否更新过
			conn := udao.NewRedisConn(client.GetWatcher().Redis())
			defer conn.Close()
			redisKey := fmt.Sprintf("syncFilePath/commandTs/%s/%s", userId, deviceId)
			if _, err := conn.Do("SETEX", redisKey, 60*2, time.Now().UnixMilli()); err != nil {
				log.Logger.Errorf("NotifyRemoteCommand, fail to setex redis key: %s, err: %v", redisKey, err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
		}
		// 通过虚拟id发起命令
		collName := umw.DeviceBaseInfo
		if util.DeviceIsPowerCharger(project) {
			collName = "charger_basic_info"
		}
		// 需要通过虚拟id发起指令
		record, err := d.getDeviceKeys(c, um.Welkin, umw.OAuthDB, collName, deviceId)
		if err != nil {
			log.CtxLog(c).Errorf("NotifyRemoteCommand, failed to get resource id, device id is: %s, err: %v", deviceId, err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if record.resourceId == "" {
			log.CtxLog(c).Errorf("NotifyRemoteCommand, failed to get resource id, device id is: %s, err: resource id is empty", deviceId)
			um.FailWithBadRequest(c, &response, "resource id is null")
			return
		}
		resourceId = record.resourceId
		// 站上传日志文件，无法同时上传多个，又把原有的互斥限制解除了，点的多了会卡死上不来😅，云端做限制
		if cf.Key == model.UploadLogFile {
			lockKey := fmt.Sprintf("remoteConfig/%s/%s", cf.Key, resourceId)
			lockValue := deviceId
			conn := client.GetRedisConn()
			defer conn.Close()
			locker := lock.NewRedisLock(conn)

			err := locker.LockByLua(lockKey, lockValue, 3*60)
			if err != nil {
				log.CtxLog(c).Warnf("fail to LockByLua, err: %v", err)
				if !errors.Is(err, um.LockFail) {
					log.CtxLog(c).Errorf("fail to lock remoteConfig, err: %v", err)
				}
				um.SuccessWithInternalServerError(c, &response, "当前设备正在上传文件，请稍后再试")
				return
			}
			// 查询文件是否已上传，若已上传，则解锁
			go func() {
				defer ucmd.RecoverPanic()
				ticker := time.NewTicker(time.Second * 3)
				defer ticker.Stop()
				ctx, cancel := context.WithTimeout(context.Background(), time.Second*180)
				defer cancel()
				conn = client.GetRedisConn()
				defer conn.Close()
				locker = lock.NewRedisLock(conn)
				for {
					select {
					case <-ctx.Done():
						return
					case <-ticker.C:
						filter := bson.D{{"device_id", deviceId}}
						pagination := client.Pagination{
							Limit:  1,
							Offset: 0,
						}
						order := client.Ordered{
							Key:        "file_gen_time",
							Descending: true,
						}
						byteData, _, mErr := d.watcher.Mongodb().NewMongoEntry(filter).ListByPagination(umw.LogInfo, umw.UploadHistory, pagination, order)
						if mErr != nil {
							log.CtxLog(c).Errorf("fail to find upload history, err: %v", err)
							return
						}
						var res []mmgo.MongoLogFileUploadHistory
						if err = json.Unmarshal(byteData, &res); err != nil {
							log.CtxLog(c).Errorf("fail to unmarshal upload history, err: %v", err)
							return
						}
						// 判断文件是否已上传
						if len(res) > 0 && res[0].FileUploadTime > res[0].FileGenTime {
							err = locker.UnLockByLua(lockKey, lockValue)
							if err != nil {
								log.CtxLog(c).Errorf("fail to unlock, err: %v", err)
							}
							return
						}
					}
				}
			}()
		}

		res, ok := d.OSS.SendCommandForLogFile(c, userId, resourceId, cf.Key, cf.Value)
		if !ok {
			log.CtxLog(c).Errorf("NotifyRemoteCommand, fail to SendCommandForLogFile %v", ucmd.ToJsonStrIgnoreErr(res))
			errCode := -3
			if res.ResultCode == "DCC_EXISTS_REMOTE_OPERATION_WITH_SAME_DEVICE_ID" {
				errCode = -4
				um.FailWithMessageForGin(c, &response, res.Message, errCode, http.StatusForbidden)
			} else {
				um.FailWithMessageForGin(c, &response, res.Message, errCode, http.StatusInternalServerError)
			}
			return
		}

		if cf.Key == model.UploadLogFile {
			uploadHistory := mmgo.MongoLogFileUploadHistory{
				DeviceId:      deviceId,
				Project:       project,
				FileGenStatus: "生成中",
				FilePath:      cf.Value,
				Operator:      userId,
				AllowDownload: false,
			}
			err := d.watcher.Mongodb().UpdateLogFileUploadHistory(umw.LogInfo, umw.UploadHistory, model.Upload, uploadHistory)
			if err != nil {
				log.CtxLog(c).Errorf("SendCommandForLogFile, fail to UpdateLogFileUploadHistory: %v", err)
				um.FailWithMessageForGin(c, &response, err.Error(), -3, http.StatusInternalServerError)
				return
			}
			// 若为snapshot文件，需要额外存到snapshot库中
			if strings.HasPrefix(cf.Value, model.SnapshotPrefix) {
				snapshotHistory := mmgo.MongoLogFileUploadHistory{
					DeviceId:      deviceId,
					Project:       project,
					FileGenStatus: "生成中",
					FilePath:      strings.TrimPrefix(cf.Value, model.SnapshotPrefix),
					FileGenTime:   time.Now().UnixMilli(),
				}
				err = d.watcher.Mongodb().UpdateSnapshotUploadHistory(umw.Snapshot, umw.UploadHistory, snapshotHistory)
				if err != nil {
					log.CtxLog(c).Errorf("SendCommandForLogFile, fail to UpdateSnapshotUploadHistory: %v", err)
					um.FailWithMessageForGin(c, &response, err.Error(), -3, http.StatusInternalServerError)
					return
				}
			}
		}

		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (d *device) UploadLogFile(area string) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response model.LogFileResponse
		)
		f, fh, err := c.Request.FormFile("file")
		if err != nil {
			log.CtxLog(c).Errorf("failed to upload log file, err: %v", err)
			um.FailWithMessageForGin(c, &response, err.Error(), -1, http.StatusBadRequest)
			return
		}
		defer f.Close()
		if fh.Size > maxSize {
			log.CtxLog(c).Errorf("file size is limit to 2GB")
			um.FailWithMessageForGin(c, &response, "file size is limit to 2GB", -1, http.StatusBadRequest)
			return
		}
		deviceId := c.PostForm("device_id")
		project := c.PostForm("project")
		ts := c.PostForm("ts")
		filePath := c.PostForm("file_path")
		if deviceId == "" || project == "" || ts == "" || filePath == "" {
			log.CtxLog(c).Errorf("lack required parameters, device_id: %s, project: %s, ts: %s, file_path: %s", deviceId, project, ts, filePath)
			um.FailWithMessageForGin(c, &response, "`device_id`, `project`, `ts` and `file_path` are required", -1, http.StatusBadRequest)
			return
		}
		tsInt64, err := strconv.ParseInt(ts, 10, 64)
		if err != nil {
			log.CtxLog(c).Errorf("`ts` format is incorrect: %s, err: %v", ts, err)
			um.FailWithMessageForGin(c, &response, err.Error(), -3, http.StatusInternalServerError)
			return
		}
		md5 := c.PostForm("md5_hash")
		log.CtxLog(c).Infof("UploadLogFile, device_id: %s, project: %s, ts: %d, file_path: %s, md5: %s", deviceId, project, tsInt64, filePath, md5)

		// 存储文件到FMS
		date := strings.Split(util.DecodeTime(time.UnixMilli(tsInt64)), " ")
		buffer := bytes.NewBuffer(nil)
		if _, err = io.Copy(buffer, f); err != nil {
			log.CtxLog(c).Errorf("upload log file, err: %v", err)
			um.FailWithMessageForGin(c, &response, err.Error(), -3, http.StatusInternalServerError)
			return
		}
		var prefix, fileType, contentType string
		if strings.HasPrefix(filePath, model.SnapshotPrefix) {
			prefix = umw.Snapshot
			fileType = model.IMAGE
			contentType = "image/jpeg"
		} else {
			prefix = umw.LogInfo
			fileType = model.FILE
			contentType = "application/octet-stream"
		}
		fmsFileDir := fmt.Sprintf("/%s/%s/%s/%s/", prefix, project, deviceId, date[0])
		fmsFileName := fmt.Sprintf("%s-%s", date[1], fh.Filename)
		tokenRes, tokenErr := d.FMS.GetFileUploadToken(fmsFileDir, fmsFileName, fileType, buffer.String(), area)
		if tokenErr != nil {
			log.CtxLog(c).Errorf("UploadLogFile: %v", tokenErr)
			um.FailWithMessageForGin(c, &response, tokenErr.Error(), -3, http.StatusInternalServerError)
			return
		}
		rd := tokenRes.ResultData
		rd.SupplierHttp.Header["Content-Type"] = contentType
		err = d.FMS.UploadFile(rd.SupplierHttp.URL, rd.SupplierHttp.Header, buffer)
		if err != nil {
			log.CtxLog(c).Errorf("UploadLogFile: %v", err)
			um.FailWithMessageForGin(c, &response, err.Error(), -3, http.StatusInternalServerError)
			return
		}
		for _, item := range rd.DomainInfoList {
			if item.DomainAttr.CDN {
				response.LogUrl = fmt.Sprintf("%s://%s/%s", item.Protocol, item.Domain, rd.FileKey)
				break
			}
		}
		uploadHistory := mmgo.MongoLogFileUploadHistory{
			DeviceId:       deviceId,
			Project:        project,
			FileUploadTime: tsInt64,
			FileGenStatus:  "已上传",
			FilePath:       filePath,
			LogUrl:         response.LogUrl,
			MD5:            md5,
		}
		if area != um.Europe {
			uploadHistory.AllowDownload = true
		}
		if md5 != "" {
			md5CheckPass := true
			md5CheckErr := util.CheckFileMD5(fh, md5)
			if md5CheckErr != nil {
				log.CtxLog(c).Warnf("UploadLogFile, fail to check md5, err: %v", md5CheckErr)
				md5CheckPass = false
			}
			uploadHistory.MD5CheckPass = &md5CheckPass
		}
		err = d.watcher.Mongodb().UpdateLogFileUploadHistory(umw.LogInfo, umw.UploadHistory, model.Upload, uploadHistory)
		if err != nil {
			log.CtxLog(c).Errorf("UpdateLogFileUploadHistory: %v", err)
			um.FailWithMessageForGin(c, &response, err.Error(), -3, http.StatusInternalServerError)
			return
		}
		// 若为snapshot文件，需要额外存到snapshot库中
		if strings.HasPrefix(filePath, model.SnapshotPrefix) {
			uploadHistory.FilePath = strings.TrimPrefix(filePath, model.SnapshotPrefix)
			err = d.watcher.Mongodb().UpdateSnapshotUploadHistory(umw.Snapshot, umw.UploadHistory, uploadHistory)
			if err != nil {
				log.CtxLog(c).Errorf("UpdateSnapshotUploadHistory: %v", err)
				um.FailWithMessageForGin(c, &response, err.Error(), -3, http.StatusInternalServerError)
				return
			}
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (d *device) SyncLogFilePath() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			requestData model.SyncLogFilePathParams
			response    um.Base
		)
		if err := c.BindJSON(&requestData); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		log.CtxLog(c).Infof("SyncLogFilePath, project: %s, device_id: %s", requestData.Project, requestData.DeviceId)

		// 若为snapshot，需要额外写入snapshot.fileinfo
		if len(requestData.LogNode) > 0 && strings.HasPrefix(requestData.LogNode[0].Path, model.SnapshotPrefix) {
			snapshotList := make([]interface{}, 0)
			for _, node := range requestData.LogNode {
				createTs, algorithmName, _, err, isSnapshot, cameraType := util.ParseSnapshotName(node.Name)
				// 不处理非snapshot照片
				if !isSnapshot {
					continue
				}
				if err != nil {
					log.CtxLog(c).Warnf("ParseSnapshotName err: %s, name: %s, device_id: %s", err.Error(), node.Name, requestData.DeviceId)
					continue
				}
				snapshotList = append(snapshotList, mmgo.SnapshotInfo{
					DeviceId:      requestData.DeviceId,
					CameraType:    cameraType,
					CreateTs:      createTs.UnixMilli(),
					AlgorithmName: algorithmName,
					FileName:      node.Name,
					Date:          time.Now(),
				})
			}
			collectionName := fmt.Sprintf("%s-%s", umw.Fileinfo, requestData.Project)
			indexOptions := []client.IndexOption{
				{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 7 * 24 * 3600},
				{Name: "device_ts_algorithm_camera_unique_combine", Fields: bson.D{
					{"device_id", -1},
					{"create_ts", -1},
					{"algorithm_name", -1},
					{"camera_type", -1},
				}, Unique: true},
			}
			err := d.watcher.Mongodb().NewMongoEntry().InsertMany(umw.Snapshot, collectionName, snapshotList, indexOptions...)
			if err != nil {
				log.CtxLog(c).Warnf("insert snapshot info err: %s, collection: %s", err.Error(), collectionName)
			}
		}

		// TODO 想办法从产品层面解决，擦屁股做法，服务端阶段
		if len(requestData.LogNode) > 10000 {
			requestData.LogNode = requestData.LogNode[:9999]
		}
		err := d.watcher.Mongodb().UpdateLogInfoDirectoryTree(umw.LogInfo, umw.DirectoryTree, requestData)
		if err != nil {
			log.CtxLog(c).Errorf("UpdateLogInfoDirectoryTree: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (d *device) DownloadAuthURL() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			response um.Base
		)

		logUrl := c.Query("log_url")
		if logUrl == "" {
			log.CtxLog(c).Errorf("`log_url` is required")
			um.FailWithBadRequest(c, &response, "`log_url` is required")
			return
		}

		res, err := d.FMS.GetFileAuthorizeURL([]string{logUrl})
		if err != nil {
			log.CtxLog(c).Errorf("failed to get authorize url: %s", err.Error())
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		url, has := res.ResultData.URLAuthorizeMap[logUrl]
		if !has {
			log.CtxLog(c).Errorf("authorize url no found")
			um.FailWithBadRequest(c, &response, "authorize url no found")
			return
		}

		resp, err := http.Get(url)
		if err != nil {
			log.CtxLog(c).Errorf("failed to get file by http, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		data, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			log.CtxLog(c).Errorf("failed to get file by http, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}

		nameList := strings.Split(logUrl, "/")
		util.Download(c, nameList[len(nameList)-1], data)

		return
	}
}

func (d *device) GetUploadHistoryList() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			uriParam model.UploadLogHistoryParams
			response model.UploadLogHistoryResponse
		)

		project := c.Param("project")
		if err := c.BindQuery(&uriParam); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithMessageForGin(c, &response, fmt.Sprintf("uri parameters are incorrect: %s", err.Error()), 1, http.StatusBadRequest)
			return
		}
		userId := c.GetHeader("X-User-ID")
		if userId == "" {
			log.CtxLog(c).Errorf("`user_id` is required")
			um.FailWithMessageForGin(c, &response, fmt.Sprintf("`user_id` is required"), 1, http.StatusBadRequest)
			return
		}
		util.SetURIParamDefault(&uriParam.CommonUriParam)

		response = model.UploadLogHistoryResponse{
			Page: uriParam.Page,
			Size: uriParam.Size,
		}
		filter := bson.D{{"project", project}, {"operator", userId}}
		if uriParam.DeviceId != "" {
			filter = append(filter, bson.E{Key: "device_id", Value: uriParam.DeviceId})
		}
		byteData, total, err := d.watcher.Mongodb().NewMongoEntry(filter).ListByPagination(umw.LogInfo, umw.UploadHistory,
			client.Pagination{Limit: int64(uriParam.Size), Offset: int64((uriParam.Page - 1) * uriParam.Size)},
			client.Ordered{Key: "file_gen_time", Descending: uriParam.Descending})
		if err != nil {
			log.CtxLog(c).Errorf("get log file upload history, err: %s", err.Error())
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}

		var records []mmgo.MongoLogFileUploadHistory
		if err = json.Unmarshal(byteData, &records); err != nil {
			log.CtxLog(c).Errorf("failed to unmarshal log file upload history, err: %v", err)
			return
		}
		log.CtxLog(c).Infof("get log file upload history, device: %s, total: %d", uriParam.DeviceId, total)

		deviceCache := cache.PowerSwapCache
		if util.DeviceIsPowerCharger(project) {
			deviceCache = cache.ChargerCache
		}
		for _, r := range records {
			deviceInfo, ok := deviceCache.GetSingleDevice(r.DeviceId)
			if !ok {
				log.CtxLog(c).Warnf("cannot find the device name from cache, device_id: %s", r.DeviceId)
			} else {
				r.Description = deviceInfo.Description
			}
			response.Data = append(response.Data, r)
		}
		response.Total = int(total)
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (d *device) GetDirectoryTreeList() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			uriParam model.LogDirectoryTreeParams
			response model.LogDirectoryTreeResponse
		)

		project := c.Param("project")
		if err := c.BindQuery(&uriParam); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}
		if uriParam.DeviceId == "" {
			log.CtxLog(c).Errorf("`device_id` is required")
			um.FailWithBadRequest(c, &response, "`device_id` is required")
			return
		}
		util.SetURIParamDefault(&uriParam.CommonUriParam)
		userId := c.GetHeader("X-User-ID")

		response = model.LogDirectoryTreeResponse{
			Page: uriParam.Page,
			Size: uriParam.Size,
		}

		byteData, err := d.watcher.Mongodb().NewMongoEntry(
			bson.D{bson.E{Key: "device_id", Value: uriParam.DeviceId}}).ListAll(umw.LogInfo, umw.DirectoryTree, client.Ordered{})
		if err != nil {
			log.CtxLog(c).Errorf("get log file directory tree, project: %s, err: %v", project, err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if err = json.Unmarshal(byteData, &response.Data); err != nil {
			log.CtxLog(c).Errorf("failed to umarshal file directory tree, project: %s, err: %v", project, err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if len(response.Data) != 0 {
			conn := udao.NewRedisConn(client.GetWatcher().Redis())
			defer conn.Close()
			redisKey := fmt.Sprintf("syncFilePath/commandTs/%s/%s", userId, uriParam.DeviceId)
			commandTs, _ := redis.Int64(conn.Do("GET", redisKey))
			log.CtxLog(c).Infof("get redis key: %s, val: %v, sync_ts: %d", redisKey, commandTs, response.Data[0].SyncTS)
			if commandTs > 0 && response.Data[0].SyncTS > commandTs {
				response.Data[0].IsRefresh = true
			}
			sort.Slice(response.Data[0].LogNode, func(i, j int) bool {
				return response.Data[0].LogNode[i].Name > response.Data[0].LogNode[j].Name
			})
			response.Total = 1
		}

		log.CtxLog(c).Infof("get log file directory tree, device_id: %s", uriParam.DeviceId)
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (d *device) GetSnapshotNameMapping() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response model.Response
		project := c.Param("project")
		resData := make([]interface{}, 0)
		descData := make([]map[string]string, 0)
		for k, v := range config.AlgorithmCfg.SnapshotConfig[project] {
			descData = append(descData, map[string]string{
				"name":           k,
				"description":    v.Zh,
				"description_en": v.En,
			})
		}
		sort.Slice(descData, func(i, j int) bool {
			return descData[i]["name"] < descData[j]["name"]
		})
		for _, record := range descData {
			resData = append(resData, record)
		}
		response.Data = resData
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
	}
}

func (d *device) GetSnapshotList(prom *uprom.Prometheus) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  model.SnapshotListParams
			response model.SnapshotListResponse
		)
		origins := d.config.Websocket.Origins
		upgrader := websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				origin := r.Header.Get("Origin")
				for _, allowOrigin := range origins {
					if allowOrigin == origin {
						return true
					}
				}
				return false
			},
		}
		conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
		if err != nil {
			log.CtxLog(c).Errorf("upgrade to websocket err: %s", err.Error())
			return
		}
		ws := ucmd.NewWsClient(conn)
		go ws.Send()
		go ws.Receive()
		project := c.Param("project")
		failCnt := prom.MetricCollectors[util.WsUnexpectedClose.ID].(*prometheus.CounterVec)
		caller := "snapshot"
		if err = c.BindQuery(&request); err != nil {
			log.CtxLog(c).Errorf("parse query parameters, err: %v", err)
			if err = ws.FailMessage(&response, err.Error(), 1); err != nil {
				log.CtxLog(c).Error(err)
				failCnt.WithLabelValues(caller).Inc()
			}
			return
		}
		createTs, err := time.ParseInLocation("2006-01-02-15:04:05", request.Time, time.Local)
		if err != nil {
			log.CtxLog(c).Errorf("fail to parse time, err: %v, request: %+v", err, request)
			failCnt.WithLabelValues(caller).Inc()
			return
		}
		request.Ts = createTs.UnixMilli()
		var page, size int
		if request.Page == 0 {
			page = 1
		} else {
			page = request.Page
		}
		if request.Size == 0 {
			size = 10
		} else {
			size = request.Size
		}

		// 当客户端主动断开连接时，中断轮询
		mu := sync.Mutex{}
		updateNow := make(chan struct{})
		go func() {
			for {
				select {
				case <-ws.Done():
					return
				default:
					data, err := ws.ReceiveJson()
					if err != nil {
						if err = ws.FailMessage(&response, fmt.Sprintf("receive json message err: %v", data), 1); err != nil {
							log.CtxLog(c).Error(err)
							failCnt.WithLabelValues(caller).Inc()
						}
						return
					}
					var msg model.SnapshotMessage
					if err = json.Unmarshal(data, &msg); err != nil {
						if err = ws.FailMessage(&response, fmt.Sprintf("parse message err: %v", data), 1); err != nil {
							log.CtxLog(c).Error(err)
							failCnt.WithLabelValues(caller).Inc()
						}
						return
					}
					if msg.Page <= 0 {
						msg.Page = 1
					}
					if msg.Size <= 0 {
						msg.Size = 10
					}
					if msg.Update {
						updateNow <- struct{}{}
					}
					mu.Lock()
					page = msg.Page
					size = msg.Size
					mu.Unlock()
				}
			}
		}()

		ticker := time.NewTicker(time.Second * 5)
		timeout := time.After(time.Second * 50)
		defer ticker.Stop()
		redisConnection := udao.NewRedisConn(d.watcher.Redis())
		defer redisConnection.Close()
		for {
			// 先根据loginfo.directory-tree中的sync_ts判断当前是否同步成功
			rawData, err := d.watcher.Mongodb().NewMongoEntry(
				bson.D{bson.E{Key: "device_id", Value: request.DeviceId}}).GetOne(umw.LogInfo, umw.DirectoryTree)
			if err != nil {
				log.CtxLog(c).Errorf("get log file directory tree, err: %s, request: %s", err.Error(), ucmd.ToJsonStrIgnoreErr(request))
				if err = ws.FailMessage(&response, err.Error(), 3); err != nil {
					log.CtxLog(c).Error(err)
					failCnt.WithLabelValues(caller).Inc()
				}
				return
			}
			if rawData != nil {
				var record model.SyncLogFilePathParams
				if err = bson.Unmarshal(rawData, &record); err != nil {
					log.CtxLog(c).Errorf("failed to unmarshal file directory tree, err: %s, request: %s", err.Error(), ucmd.ToJsonStrIgnoreErr(request))
					if err = ws.FailMessage(&response, err.Error(), 3); err != nil {
						log.CtxLog(c).Error(err)
						failCnt.WithLabelValues(caller).Inc()
					}
					return
				}
				redisKey := fmt.Sprintf("syncFilePath/commandTs/%s/%s", request.UserId, request.DeviceId)
				commandTs, _ := redis.Int64(redisConnection.Do("GET", redisKey))
				// 若同步完成，则跳出循环
				if commandTs > 0 && record.SyncTS > commandTs {
					log.CtxLog(c).Infof("sync directory-tree finish, request: %s", ucmd.ToJsonStrIgnoreErr(request))
					break
				}
			}
			select {
			case <-ticker.C:
				continue
			case <-timeout:
				log.CtxLog(c).Errorf("sync directory-tree timeout, request: %s", ucmd.ToJsonStrIgnoreErr(request))
				if err = ws.FailMessage(&response, "sync directory-tree timeout", -1); err != nil {
					log.CtxLog(c).Error(err)
					failCnt.WithLabelValues(caller).Inc()
				}
				return
			case <-ws.Done():
				log.CtxLog(c).Infof("websocket closed")
				return
			}
		}

		collectionName := fmt.Sprintf("%s-%s", umw.Fileinfo, project)
		snapshotList, err := d.watcher.Mongodb().GetSnapshotList(umw.Snapshot, collectionName, request)
		if err != nil {
			log.CtxLog(c).Errorf("GetSnapshotList err: %s, request: %s", err.Error(), ucmd.ToJsonStrIgnoreErr(request))
			if err = ws.FailMessage(&response, err.Error(), -1); err != nil {
				log.CtxLog(c).Error(err)
				failCnt.WithLabelValues(caller).Inc()
			}
			return
		}
		// 获取device描述
		tags, err := client.GetDeviceTag(redisConnection, d.watcher.Mongodb(), request.DeviceId)
		if err != nil {
			log.CtxLog(c).Errorf("cannot find the device name from mongo, device_id: %s, err: %v", request.DeviceId, err)
		}
		// 初始化upload-history表
		snapshots := make([]string, len(snapshotList))
		for i, snapshot := range snapshotList {
			snapshots[i] = snapshot.FileName
			snapshotHistory := mmgo.MongoLogFileUploadHistory{
				DeviceId:      request.DeviceId,
				Description:   tags.Description,
				Project:       project,
				FileGenStatus: "未生成",
				FilePath:      snapshot.FileName,
				Operator:      model.BACKEND,
				Date:          time.Now(),
			}
			if err = d.watcher.Mongodb().UpdateSnapshotUploadHistory(umw.Snapshot, umw.UploadHistory, snapshotHistory); err != nil {
				log.CtxLog(c).Errorf("UpdateSnapshotUploadHistory err: %s, collection: %s, snapshotHistory: %v", err.Error(), collectionName, snapshotHistory)
				continue
			}
		}

		// 轮询upload-history表，返回数据给前端
		ticker = time.NewTicker(time.Second * 5)
		for {
			snapshotInfoList, err := d.watcher.Mongodb().GetSnapshotInfoList(umw.Snapshot, umw.UploadHistory, snapshots, client.Ordered{Key: "file_path", Descending: request.Descending})
			if err != nil {
				log.CtxLog(c).Errorf("GetSnapshotInfoList err: %s", err.Error())
				if err = ws.FailMessage(&response, err.Error(), 3); err != nil {
					log.CtxLog(c).Error(err)
					failCnt.WithLabelValues(caller).Inc()
				}
				return
			}
			response.Total = len(snapshotInfoList)
			mu.Lock()
			start := (page - 1) * size
			sizeCopy := size
			mu.Unlock()
			response.Data = make([]model.SnapshotData, sizeCopy)
			for i := 0; i < sizeCopy; i++ {
				if start+i >= len(snapshotInfoList) {
					response.Data = response.Data[:i]
					break
				}
				snapshot := snapshotInfoList[start+i]
				createTs, algorithmName, serviceId, err, _, _ := util.ParseSnapshotName(snapshot.FilePath)
				if err != nil {
					log.CtxLog(c).Errorf("parse snapshot name err: %s, name: %s", err.Error(), snapshot.FilePath)
					continue
				}
				response.Data[i] = model.SnapshotData{
					DeviceId:      snapshot.DeviceId,
					ServiceId:     serviceId,
					AlgorithmName: algorithmName,
					Description:   snapshot.Description,
					CreateTs:      createTs.UnixMilli(),
					FileGenStatus: snapshot.FileGenStatus,
					AllowDownload: snapshot.AllowDownload,
					SnapshotUrl:   strings.Replace(snapshot.LogUrl, "oss-welkin-public.nioint.com", "cdn-welkin-public.nio.com", 1),
					FilePath:      fmt.Sprintf("%s%s", model.SnapshotPrefix, snapshot.FilePath),
				}
			}
			if err = ws.SuccessMessage(&response, "ok"); err != nil {
				log.CtxLog(c).Error(err)
				failCnt.WithLabelValues(caller).Inc()
				return
			}
			select {
			case <-ticker.C:
				continue
			case <-updateNow:
				time.Sleep(time.Millisecond * 100)
				continue
			case <-ws.Done():
				log.CtxLog(c).Infof("websocket closed")
				return
			}
		}
	}
}

func (d *device) SendSnapshotInfo() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			request  larkservice.SnapshotInfoParams
			response um.Base
		)
		project := c.Param("project")
		if err := c.BindJSON(&request); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithBadRequest(c, &response, err.Error())
			return
		}

		lankaRequest := lanka.GetAdministratorsRequest{
			ConditionCode: util.RenameConditionCode(project, request.AlgorithmName),
		}
		rbac := lanka.Rbac{
			AppID:     d.config.Sentry.AppId,
			AppSecret: d.config.Sentry.AppSecret,
			LankaURL:  d.config.SSO.LankaUrl,
		}
		lankaResp, err := rbac.GetAdministrators(lankaRequest)
		if err != nil {
			log.CtxLog(c).Errorf("fail to GetAdministrators, err: %s", err.Error())
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if lankaResp.ResultCode != "success" {
			log.CtxLog(c).Errorf("fail to GetAdministrators, ResultCode is %s", lankaResp.ResultCode)
			um.FailWithInternalServerError(c, &response, fmt.Sprintf("fail to GetAdministrators, ResultCode is %s", lankaResp.ResultCode))
			return
		}
		emails := make([]string, 0)
		for _, user := range lankaResp.Data.List {
			emails = append(emails, user.Email)
		}
		receiver := larkservice.Receiver{
			Type:       larkim.ReceiveIdTypeEmail,
			ReceiveIds: emails,
		}
		cardContent, err := larkservice.NewInfoCard().MakeSnapshotInfo(request).Build()
		if err != nil {
			log.CtxLog(c).Errorf("make card err: %s", err.Error())
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if err = larkservice.SendCard(cardContent, receiver); err != nil {
			log.CtxLog(c).Errorf("send card err: %s", err.Error())
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		um.SuccessWithMessageForGin(c, &response, "succeed to SendSnapshotInfo", http.StatusOK)
		return
	}
}

// DownloadApproval status:
// processing: 审批中
// deny: 被拒绝
// success: 审核完成
// revoke: 撤销
// timeout: 超时
// error: 系统异常
// accept: 节点审批通过
func (d *device) DownloadApproval() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			requestData model.ApprovalContent
			response    um.Base
		)
		userId := c.GetHeader("X-User-ID")
		if userId == "" {
			log.CtxLog(c).Errorf("need user id")
			um.FailWithMessageForGin(c, &response, "need user id", -1, http.StatusBadRequest)
			return
		}
		if err := c.BindJSON(&requestData); err != nil {
			log.CtxLog(c).Errorf("parse request body, err: %v", err)
			um.FailWithMessageForGin(c, &response, err.Error(), -1, http.StatusBadRequest)
			return
		}
		// 若用户为欧洲用户，则跳过审批
		peopleList, _, err := common.QueryPeople(c, userId)
		if err != nil {
			log.CtxLog(c).Errorf("fail to query people, err: %v, user id: %s", err, userId)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		if len(peopleList) == 0 {
			log.CtxLog(c).Errorf("invalid people, user id: %s", userId)
			um.FailWithInternalServerError(c, &response, "invalid people")
			return
		}
		if ucmd.GetArea() == um.Europe && peopleList[0].Area == common.PeopleAreaEU {
			err = d.downloadApprovalDone(c, requestData.RequestFiles, requestData.DeviceId, userId)
			if err != nil {
				log.CtxLog(c).Errorf("failed to update mongo log file upload history, err: %v", err)
				um.FailWithInternalServerError(c, &response, err.Error())
				return
			}
			um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
			return
		}
		// 非欧洲用户，发起审批
		flowInstanceId, err := d.WF.PublishApproval(c, userId, requestData)
		if err != nil {
			um.FailWithMessageForGin(c, &response, err.Error(), -2, http.StatusInternalServerError)
			return
		}
		err = d.watcher.Mongodb().InsertApprovalHistory(umw.LogInfo, umw.ApprovalHistory, userId, flowInstanceId, requestData)
		if err != nil {
			log.CtxLog(c).Errorf("DownloadApproval: %v", err)
			um.FailWithMessageForGin(c, &response, err.Error(), -2, http.StatusInternalServerError)
			return
		}
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}

func (d *device) DownloadApprovalCallback() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			requestData model.ApprovalCallback
		)
		if err := c.BindJSON(&requestData); err != nil {
			log.CtxLog(c).Errorf("failed to parse body, err: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"result_code": "fail", "message": err.Error()})
			return
		}
		result, err := d.watcher.Mongodb().UpdateApprovalHistoryV1(umw.LogInfo, umw.ApprovalHistory, requestData)
		if err != nil {
			log.CtxLog(c).Errorf("failed to update approval history, err: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"result_code": "fail", "message": err.Error()})
			return
		}
		if requestData.Status == "success" {
			var requestFiles []model.RequestFile
			if err = json.Unmarshal([]byte(result.Details), &requestFiles); err != nil {
				log.CtxLog(c).Errorf("failed to parse details, err: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{"result_code": "fail", "message": err.Error()})
				return
			}
			if err = d.downloadApprovalDone(c, requestFiles, result.DeviceId, result.UserId); err != nil {
				log.CtxLog(c).Errorf("failed to update mongo log file upload history, err: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{"result_code": "fail", "message": err.Error()})
				return
			}
		}
		c.JSON(http.StatusOK, gin.H{"result_code": "success", "message": "ok"})
		return
	}
}

// 审批通过后，修改下载状态
func (d *device) downloadApprovalDone(ctx *gin.Context, requestFiles []model.RequestFile, deviceId, userId string) error {
	for _, f := range requestFiles {
		err := d.watcher.Mongodb().UpdateLogFileUploadHistory(umw.LogInfo, umw.UploadHistory, model.Approval,
			mmgo.MongoLogFileUploadHistory{
				DeviceId:      deviceId,
				FilePath:      f.FilePath,
				Operator:      userId,
				FileGenTime:   f.FileGenTime,
				AllowDownload: true,
			})
		if err != nil {
			log.CtxLog(ctx).Errorf("failed to update mongo log file upload history, err: %v", err)
			return err
		}
	}
	return nil
}

func (d *device) DownloadApprovalHistoryList() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			uriParam model.UploadLogHistoryParams
			response model.LogApprovalHistoryResponse
		)

		project := c.Param("project")
		if err := c.BindQuery(&uriParam); err != nil {
			log.CtxLog(c).Errorf("uri parameters are incorrect: %v", err)
			um.FailWithMessageForGin(c, &response, fmt.Sprintf("uri parameters are incorrect: %s", err.Error()), 1, http.StatusBadRequest)
			return
		}
		if uriParam.UserId == "" {
			log.CtxLog(c).Errorf("`user_id` is required")
			um.FailWithMessageForGin(c, &response, fmt.Sprintf("`user_id` is required"), 1, http.StatusBadRequest)
			return
		}
		util.SetURIParamDefault(&uriParam.CommonUriParam)

		response = model.LogApprovalHistoryResponse{
			Page: uriParam.Page,
			Size: uriParam.Size,
		}
		filter := bson.D{bson.E{Key: "project", Value: project}}
		if uriParam.DeviceId != "" {
			filter = append(filter, bson.E{Key: "device_id", Value: uriParam.DeviceId})
		}
		if uriParam.UserId != "" {
			filter = append(filter, bson.E{Key: "user_id", Value: uriParam.UserId})
		}
		byteData, total, err := d.watcher.Mongodb().NewMongoEntry(filter).ListByPagination(umw.LogInfo, umw.ApprovalHistory,
			client.Pagination{Limit: int64(uriParam.Size), Offset: int64((uriParam.Page - 1) * uriParam.Size)},
			client.Ordered{Key: "updated_time", Descending: uriParam.Descending},
		)
		if err != nil {
			log.CtxLog(c).Errorf("get log file approval history, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		var records []umw.MongoLogApprovalHistory
		if err = json.Unmarshal(byteData, &records); err != nil {
			log.CtxLog(c).Errorf("failed to unmarshal log file approval history, err: %v", err)
			um.FailWithInternalServerError(c, &response, err.Error())
			return
		}
		log.CtxLog(c).Infof("get log file approval history, device: %s, total: %d", uriParam.DeviceId, total)

		for _, r := range records {
			var details []model.RequestFile
			if err = json.Unmarshal([]byte(r.Details), &details); err != nil {
				log.CtxLog(c).Errorf("failed to parse details, err: %v", err)
				continue
			}
			response.Data = append(response.Data, model.LogApprovalHistory{
				Project:          r.Project,
				DeviceId:         r.DeviceId,
				Description:      r.Description,
				Reason:           r.Reason,
				FlowInstanceId:   r.FlowInstanceId,
				Status:           r.Status,
				UpdatedTime:      r.UpdatedTime,
				Details:          details,
				UserId:           r.UserId,
				CurrentNodeName:  r.CurrentNodeName,
				PreviousNodeName: r.PreviousNodeName,
			})
		}
		response.Total = int(total)
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}
