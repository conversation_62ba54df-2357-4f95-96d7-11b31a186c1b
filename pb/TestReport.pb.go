// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.21.6
// source: TestReport.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TestState int32

const (
	TestState_Empty      TestState = 0 //未开始
	TestState_InProgress TestState = 1 //进行中
	TestState_Finished   TestState = 2 //已完成
	TestState_Stop       TestState = 3 //已停止
	TestState_Suspend    TestState = 4 //已暂停
	TestState_Saved      TestState = 5 //已序列化后保存
)

// Enum value maps for TestState.
var (
	TestState_name = map[int32]string{
		0: "Empty",
		1: "InProgress",
		2: "Finished",
		3: "Stop",
		4: "Suspend",
		5: "Saved",
	}
	TestState_value = map[string]int32{
		"Empty":      0,
		"InProgress": 1,
		"Finished":   2,
		"Stop":       3,
		"Suspend":    4,
		"Saved":      5,
	}
)

func (x TestState) Enum() *TestState {
	p := new(TestState)
	*p = x
	return p
}

func (x TestState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TestState) Descriptor() protoreflect.EnumDescriptor {
	return file_TestReport_proto_enumTypes[0].Descriptor()
}

func (TestState) Type() protoreflect.EnumType {
	return &file_TestReport_proto_enumTypes[0]
}

func (x TestState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TestState.Descriptor instead.
func (TestState) EnumDescriptor() ([]byte, []int) {
	return file_TestReport_proto_rawDescGZIP(), []int{0}
}

type TestResult int32

const (
	TestResult_OK           TestResult = 0 //测试通过
	TestResult_NG           TestResult = 1 //测试失败
	TestResult_NoConclusion TestResult = 2
)

// Enum value maps for TestResult.
var (
	TestResult_name = map[int32]string{
		0: "OK",
		1: "NG",
		2: "NoConclusion",
	}
	TestResult_value = map[string]int32{
		"OK":           0,
		"NG":           1,
		"NoConclusion": 2,
	}
)

func (x TestResult) Enum() *TestResult {
	p := new(TestResult)
	*p = x
	return p
}

func (x TestResult) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TestResult) Descriptor() protoreflect.EnumDescriptor {
	return file_TestReport_proto_enumTypes[1].Descriptor()
}

func (TestResult) Type() protoreflect.EnumType {
	return &file_TestReport_proto_enumTypes[1]
}

func (x TestResult) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TestResult.Descriptor instead.
func (TestResult) EnumDescriptor() ([]byte, []int) {
	return file_TestReport_proto_rawDescGZIP(), []int{1}
}

type TestType int32

const (
	TestType_Manual     TestType = 0 //手动选择
	TestType_Automatic  TestType = 1 //自动获取数据
	TestType_HalfManual TestType = 2 //人工输入数据，自动判断是否符合标准
)

// Enum value maps for TestType.
var (
	TestType_name = map[int32]string{
		0: "Manual",
		1: "Automatic",
		2: "HalfManual",
	}
	TestType_value = map[string]int32{
		"Manual":     0,
		"Automatic":  1,
		"HalfManual": 2,
	}
)

func (x TestType) Enum() *TestType {
	p := new(TestType)
	*p = x
	return p
}

func (x TestType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TestType) Descriptor() protoreflect.EnumDescriptor {
	return file_TestReport_proto_enumTypes[2].Descriptor()
}

func (TestType) Type() protoreflect.EnumType {
	return &file_TestReport_proto_enumTypes[2]
}

func (x TestType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TestType.Descriptor instead.
func (TestType) EnumDescriptor() ([]byte, []int) {
	return file_TestReport_proto_rawDescGZIP(), []int{2}
}

type AllMessages struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TesterName      string         `protobuf:"bytes,1,opt,name=testerName,proto3" json:"testerName,omitempty"`
	TestDate        string         `protobuf:"bytes,2,opt,name=testDate,proto3" json:"testDate,omitempty"`
	ReviewerName    string         `protobuf:"bytes,3,opt,name=reviewerName,proto3" json:"reviewerName,omitempty"`
	ReviewDate      string         `protobuf:"bytes,4,opt,name=reviewDate,proto3" json:"reviewDate,omitempty"`
	ProductionID    string         `protobuf:"bytes,5,opt,name=productionID,proto3" json:"productionID,omitempty"`
	InspectorName   []string       `protobuf:"bytes,6,rep,name=inspectorName,proto3" json:"inspectorName,omitempty"`
	InspectDate     string         `protobuf:"bytes,7,opt,name=inspectDate,proto3" json:"inspectDate,omitempty"`
	StationQR       string         `protobuf:"bytes,8,opt,name=stationQR,proto3" json:"stationQR,omitempty"`
	ContainerID     string         `protobuf:"bytes,9,opt,name=containerID,proto3" json:"containerID,omitempty"`
	TestInformation []*TestMessage `protobuf:"bytes,10,rep,name=testInformation,proto3" json:"testInformation,omitempty"`
	StationName     string         `protobuf:"bytes,11,opt,name=stationName,proto3" json:"stationName,omitempty"`
	StationID       string         `protobuf:"bytes,12,opt,name=stationID,proto3" json:"stationID,omitempty"`
}

func (x *AllMessages) Reset() {
	*x = AllMessages{}
	if protoimpl.UnsafeEnabled {
		mi := &file_TestReport_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AllMessages) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AllMessages) ProtoMessage() {}

func (x *AllMessages) ProtoReflect() protoreflect.Message {
	mi := &file_TestReport_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AllMessages.ProtoReflect.Descriptor instead.
func (*AllMessages) Descriptor() ([]byte, []int) {
	return file_TestReport_proto_rawDescGZIP(), []int{0}
}

func (x *AllMessages) GetTesterName() string {
	if x != nil {
		return x.TesterName
	}
	return ""
}

func (x *AllMessages) GetTestDate() string {
	if x != nil {
		return x.TestDate
	}
	return ""
}

func (x *AllMessages) GetReviewerName() string {
	if x != nil {
		return x.ReviewerName
	}
	return ""
}

func (x *AllMessages) GetReviewDate() string {
	if x != nil {
		return x.ReviewDate
	}
	return ""
}

func (x *AllMessages) GetProductionID() string {
	if x != nil {
		return x.ProductionID
	}
	return ""
}

func (x *AllMessages) GetInspectorName() []string {
	if x != nil {
		return x.InspectorName
	}
	return nil
}

func (x *AllMessages) GetInspectDate() string {
	if x != nil {
		return x.InspectDate
	}
	return ""
}

func (x *AllMessages) GetStationQR() string {
	if x != nil {
		return x.StationQR
	}
	return ""
}

func (x *AllMessages) GetContainerID() string {
	if x != nil {
		return x.ContainerID
	}
	return ""
}

func (x *AllMessages) GetTestInformation() []*TestMessage {
	if x != nil {
		return x.TestInformation
	}
	return nil
}

func (x *AllMessages) GetStationName() string {
	if x != nil {
		return x.StationName
	}
	return ""
}

func (x *AllMessages) GetStationID() string {
	if x != nil {
		return x.StationID
	}
	return ""
}

type TestMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StepString    string     `protobuf:"bytes,1,opt,name=stepString,proto3" json:"stepString,omitempty"`
	Title         string     `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Description   string     `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	ResultData    []string   `protobuf:"bytes,4,rep,name=resultData,proto3" json:"resultData,omitempty"`
	TestType      TestType   `protobuf:"varint,5,opt,name=testType,proto3,enum=pb.TestType" json:"testType,omitempty"`
	TestResult    TestResult `protobuf:"varint,6,opt,name=testResult,proto3,enum=pb.TestResult" json:"testResult,omitempty"`
	TestState     TestState  `protobuf:"varint,7,opt,name=testState,proto3,enum=pb.TestState" json:"testState,omitempty"`
	NgDescription string     `protobuf:"bytes,8,opt,name=ngDescription,proto3" json:"ngDescription,omitempty"`
	Points        []*Point   `protobuf:"bytes,9,rep,name=points,proto3" json:"points,omitempty"`
}

func (x *TestMessage) Reset() {
	*x = TestMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_TestReport_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestMessage) ProtoMessage() {}

func (x *TestMessage) ProtoReflect() protoreflect.Message {
	mi := &file_TestReport_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestMessage.ProtoReflect.Descriptor instead.
func (*TestMessage) Descriptor() ([]byte, []int) {
	return file_TestReport_proto_rawDescGZIP(), []int{1}
}

func (x *TestMessage) GetStepString() string {
	if x != nil {
		return x.StepString
	}
	return ""
}

func (x *TestMessage) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *TestMessage) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *TestMessage) GetResultData() []string {
	if x != nil {
		return x.ResultData
	}
	return nil
}

func (x *TestMessage) GetTestType() TestType {
	if x != nil {
		return x.TestType
	}
	return TestType_Manual
}

func (x *TestMessage) GetTestResult() TestResult {
	if x != nil {
		return x.TestResult
	}
	return TestResult_OK
}

func (x *TestMessage) GetTestState() TestState {
	if x != nil {
		return x.TestState
	}
	return TestState_Empty
}

func (x *TestMessage) GetNgDescription() string {
	if x != nil {
		return x.NgDescription
	}
	return ""
}

func (x *TestMessage) GetPoints() []*Point {
	if x != nil {
		return x.Points
	}
	return nil
}

type Point struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X int64 `protobuf:"varint,1,opt,name=x,proto3" json:"x,omitempty"`
	Y int64 `protobuf:"varint,2,opt,name=y,proto3" json:"y,omitempty"`
}

func (x *Point) Reset() {
	*x = Point{}
	if protoimpl.UnsafeEnabled {
		mi := &file_TestReport_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Point) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Point) ProtoMessage() {}

func (x *Point) ProtoReflect() protoreflect.Message {
	mi := &file_TestReport_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Point.ProtoReflect.Descriptor instead.
func (*Point) Descriptor() ([]byte, []int) {
	return file_TestReport_proto_rawDescGZIP(), []int{2}
}

func (x *Point) GetX() int64 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *Point) GetY() int64 {
	if x != nil {
		return x.Y
	}
	return 0
}

var File_TestReport_proto protoreflect.FileDescriptor

var file_TestReport_proto_rawDesc = []byte{
	0x0a, 0x10, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x02, 0x70, 0x62, 0x22, 0xb4, 0x03, 0x0a, 0x0b, 0x41, 0x6c, 0x6c, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x65, 0x73, 0x74, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x73, 0x74,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x65, 0x73, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x73, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x44, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x44, 0x61, 0x74, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x24, 0x0a, 0x0d, 0x69, 0x6e,
	0x73, 0x70, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0d, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x44, 0x61, 0x74, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x51, 0x52, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x51, 0x52,
	0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x49, 0x44, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x49, 0x44, 0x12, 0x39, 0x0a, 0x0f, 0x74, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x62,
	0x2e, 0x54, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x0f, 0x74, 0x65,
	0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a,
	0x0b, 0x73, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x22, 0xd5, 0x02,
	0x0a, 0x0b, 0x54, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x73, 0x74, 0x65, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x73, 0x74, 0x65, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x28, 0x0a, 0x08, 0x74, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0c, 0x2e, 0x70, 0x62, 0x2e, 0x54, 0x65, 0x73,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x74, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x2e, 0x0a, 0x0a, 0x74, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x70, 0x62, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x0a, 0x74, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x2b, 0x0a, 0x09, 0x74, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x0d, 0x2e, 0x70, 0x62, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x09, 0x74, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0d,
	0x6e, 0x67, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x67, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x06, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x09, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x09, 0x2e, 0x70, 0x62, 0x2e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x06, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x73, 0x22, 0x23, 0x0a, 0x05, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x0c,
	0x0a, 0x01, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x01, 0x79, 0x2a, 0x56, 0x0a, 0x09, 0x54, 0x65,
	0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x49, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73,
	0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x10, 0x02,
	0x12, 0x08, 0x0a, 0x04, 0x53, 0x74, 0x6f, 0x70, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x75,
	0x73, 0x70, 0x65, 0x6e, 0x64, 0x10, 0x04, 0x12, 0x09, 0x0a, 0x05, 0x53, 0x61, 0x76, 0x65, 0x64,
	0x10, 0x05, 0x2a, 0x2e, 0x0a, 0x0a, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x06, 0x0a, 0x02, 0x4e, 0x47, 0x10, 0x01,
	0x12, 0x10, 0x0a, 0x0c, 0x4e, 0x6f, 0x43, 0x6f, 0x6e, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e,
	0x10, 0x02, 0x2a, 0x35, 0x0a, 0x08, 0x54, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0a,
	0x0a, 0x06, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x75,
	0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x48, 0x61, 0x6c,
	0x66, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x10, 0x02, 0x42, 0x07, 0x5a, 0x05, 0x2e, 0x2e, 0x2f,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_TestReport_proto_rawDescOnce sync.Once
	file_TestReport_proto_rawDescData = file_TestReport_proto_rawDesc
)

func file_TestReport_proto_rawDescGZIP() []byte {
	file_TestReport_proto_rawDescOnce.Do(func() {
		file_TestReport_proto_rawDescData = protoimpl.X.CompressGZIP(file_TestReport_proto_rawDescData)
	})
	return file_TestReport_proto_rawDescData
}

var file_TestReport_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_TestReport_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_TestReport_proto_goTypes = []interface{}{
	(TestState)(0),      // 0: pb.TestState
	(TestResult)(0),     // 1: pb.TestResult
	(TestType)(0),       // 2: pb.TestType
	(*AllMessages)(nil), // 3: pb.AllMessages
	(*TestMessage)(nil), // 4: pb.TestMessage
	(*Point)(nil),       // 5: pb.Point
}
var file_TestReport_proto_depIdxs = []int32{
	4, // 0: pb.AllMessages.testInformation:type_name -> pb.TestMessage
	2, // 1: pb.TestMessage.testType:type_name -> pb.TestType
	1, // 2: pb.TestMessage.testResult:type_name -> pb.TestResult
	0, // 3: pb.TestMessage.testState:type_name -> pb.TestState
	5, // 4: pb.TestMessage.points:type_name -> pb.Point
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_TestReport_proto_init() }
func file_TestReport_proto_init() {
	if File_TestReport_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_TestReport_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AllMessages); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_TestReport_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_TestReport_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Point); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_TestReport_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_TestReport_proto_goTypes,
		DependencyIndexes: file_TestReport_proto_depIdxs,
		EnumInfos:         file_TestReport_proto_enumTypes,
		MessageInfos:      file_TestReport_proto_msgTypes,
	}.Build()
	File_TestReport_proto = out.File
	file_TestReport_proto_rawDesc = nil
	file_TestReport_proto_goTypes = nil
	file_TestReport_proto_depIdxs = nil
}
