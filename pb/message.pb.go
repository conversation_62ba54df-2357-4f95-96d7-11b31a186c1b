// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.6
// source: pb/message.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Message_MessageType int32

const (
	Message_NOTIFICATION               Message_MessageType = 0 // 未使用
	Message_CONTROL_COMMAND            Message_MessageType = 1 // 控制、配置指令下发 (cloud2device）
	Message_COMMAND_RESULT             Message_MessageType = 2 // 控制、配置指令结果（device2Clould）
	Message_DATA_REPORT                Message_MessageType = 3 // 设备上报数据（device2Clould）
	Message_CLIENT_STATUS              Message_MessageType = 4 // 设备状态（device2Clould）
	Message_DATA_REPORT_HIGH_FREQUENCY Message_MessageType = 5 // 高频上报数据（device2Clould）
)

// Enum value maps for Message_MessageType.
var (
	Message_MessageType_name = map[int32]string{
		0: "NOTIFICATION",
		1: "CONTROL_COMMAND",
		2: "COMMAND_RESULT",
		3: "DATA_REPORT",
		4: "CLIENT_STATUS",
		5: "DATA_REPORT_HIGH_FREQUENCY",
	}
	Message_MessageType_value = map[string]int32{
		"NOTIFICATION":               0,
		"CONTROL_COMMAND":            1,
		"COMMAND_RESULT":             2,
		"DATA_REPORT":                3,
		"CLIENT_STATUS":              4,
		"DATA_REPORT_HIGH_FREQUENCY": 5,
	}
)

func (x Message_MessageType) Enum() *Message_MessageType {
	p := new(Message_MessageType)
	*p = x
	return p
}

func (x Message_MessageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Message_MessageType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_message_proto_enumTypes[0].Descriptor()
}

func (Message_MessageType) Type() protoreflect.EnumType {
	return &file_pb_message_proto_enumTypes[0]
}

func (x Message_MessageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Message_MessageType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Message_MessageType(num)
	return nil
}

// Deprecated: Use Message_MessageType.Descriptor instead.
func (Message_MessageType) EnumDescriptor() ([]byte, []int) {
	return file_pb_message_proto_rawDescGZIP(), []int{0, 0}
}

type Message struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version   *int32               `protobuf:"varint,1,opt,name=version" json:"version,omitempty"`                      // message protocol version (required)
	Id        *string              `protobuf:"bytes,2,opt,name=id" json:"id,omitempty"`                                 // message id (will be set by server)
	PublishTs *int64               `protobuf:"varint,3,opt,name=publish_ts,json=publishTs" json:"publish_ts,omitempty"` // message publish time (will be set by server time)
	Ttl       *int64               `protobuf:"varint,4,opt,name=ttl" json:"ttl,omitempty"`                              // message expiration (required, in second)
	Type      *Message_MessageType `protobuf:"varint,5,opt,name=type,enum=Message_MessageType" json:"type,omitempty"`   // 2-level message type (required)
	SubType   *string              `protobuf:"bytes,6,opt,name=sub_type,json=subType" json:"sub_type,omitempty"`        //(optional)
	Params    []*Message_ParamType `protobuf:"bytes,7,rep,name=params" json:"params,omitempty"`                         //generic key-value parameters (optional)
	// 云端补充数据
	ServerMsgId     *string `protobuf:"bytes,101,opt,name=server_msg_id,json=serverMsgId" json:"server_msg_id,omitempty"`              // message_id of message_server
	ServerReceiveTs *int64  `protobuf:"varint,102,opt,name=server_receive_ts,json=serverReceiveTs" json:"server_receive_ts,omitempty"` // receive_ts of message_server
	ServerPublishTs *int64  `protobuf:"varint,103,opt,name=server_publish_ts,json=serverPublishTs" json:"server_publish_ts,omitempty"` // publish_ts of message_server
}

func (x *Message) Reset() {
	*x = Message{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_message_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Message) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Message) ProtoMessage() {}

func (x *Message) ProtoReflect() protoreflect.Message {
	mi := &file_pb_message_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Message.ProtoReflect.Descriptor instead.
func (*Message) Descriptor() ([]byte, []int) {
	return file_pb_message_proto_rawDescGZIP(), []int{0}
}

func (x *Message) GetVersion() int32 {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return 0
}

func (x *Message) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *Message) GetPublishTs() int64 {
	if x != nil && x.PublishTs != nil {
		return *x.PublishTs
	}
	return 0
}

func (x *Message) GetTtl() int64 {
	if x != nil && x.Ttl != nil {
		return *x.Ttl
	}
	return 0
}

func (x *Message) GetType() Message_MessageType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return Message_NOTIFICATION
}

func (x *Message) GetSubType() string {
	if x != nil && x.SubType != nil {
		return *x.SubType
	}
	return ""
}

func (x *Message) GetParams() []*Message_ParamType {
	if x != nil {
		return x.Params
	}
	return nil
}

func (x *Message) GetServerMsgId() string {
	if x != nil && x.ServerMsgId != nil {
		return *x.ServerMsgId
	}
	return ""
}

func (x *Message) GetServerReceiveTs() int64 {
	if x != nil && x.ServerReceiveTs != nil {
		return *x.ServerReceiveTs
	}
	return 0
}

func (x *Message) GetServerPublishTs() int64 {
	if x != nil && x.ServerPublishTs != nil {
		return *x.ServerPublishTs
	}
	return 0
}

// 示例
// {"account_id": "NIO-PS-xxx", "PowerDevice": "${payload}", "device_type": "PowerSwap2"}
type Message_ParamType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 固定值：<account_id，{device_id}>
	// 设备上报数据：<{param_key}, {payload}>
	// 不同种类的设备按照key来区分。可用值如下：
	// PowerSwap 一代换电站
	// PowerSwapMessage 二代换电站
	// PowerMobile 移动充电车
	// PowerMobileTBox 移动充电车的TBOX
	// PowerMobileGB32960 移动充电车国标电池
	// ACPowerCharger NIO交流桩
	// DCPowerCharger 三方桩
	// PowerCharger NIO直流桩
	// RemoteOperation - cloud到device的控制指令
	// RemoteOperationResponse - device回应cloud的答复
	// ${PowerDeviceMessage} 通用设备
	//  设备型号
	Key   *string `protobuf:"bytes,1,opt,name=key" json:"key,omitempty"`
	Value []byte  `protobuf:"bytes,2,opt,name=value" json:"value,omitempty"`
}

func (x *Message_ParamType) Reset() {
	*x = Message_ParamType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_message_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Message_ParamType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Message_ParamType) ProtoMessage() {}

func (x *Message_ParamType) ProtoReflect() protoreflect.Message {
	mi := &file_pb_message_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Message_ParamType.ProtoReflect.Descriptor instead.
func (*Message_ParamType) Descriptor() ([]byte, []int) {
	return file_pb_message_proto_rawDescGZIP(), []int{0, 0}
}

func (x *Message_ParamType) GetKey() string {
	if x != nil && x.Key != nil {
		return *x.Key
	}
	return ""
}

func (x *Message_ParamType) GetValue() []byte {
	if x != nil {
		return x.Value
	}
	return nil
}

var File_pb_message_proto protoreflect.FileDescriptor

var file_pb_message_proto_rawDesc = []byte{
	0x0a, 0x10, 0x70, 0x62, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x95, 0x04, 0x0a, 0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x5f, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x75,
	0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x74, 0x6c, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x74, 0x74, 0x6c, 0x12, 0x28, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a,
	0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x65, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4d, 0x73, 0x67, 0x49, 0x64, 0x12, 0x2a,
	0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65,
	0x5f, 0x74, 0x73, 0x18, 0x66, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x54, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x74, 0x73, 0x18,
	0x67, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x50, 0x75, 0x62,
	0x6c, 0x69, 0x73, 0x68, 0x54, 0x73, 0x1a, 0x33, 0x0a, 0x09, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x8c, 0x01, 0x0a, 0x0b,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x4e,
	0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x00, 0x12, 0x13, 0x0a,
	0x0f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x4f, 0x4c, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x41, 0x4e, 0x44,
	0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x4f, 0x4d, 0x4d, 0x41, 0x4e, 0x44, 0x5f, 0x52, 0x45,
	0x53, 0x55, 0x4c, 0x54, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x52,
	0x45, 0x50, 0x4f, 0x52, 0x54, 0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x4c, 0x49, 0x45, 0x4e,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x04, 0x12, 0x1e, 0x0a, 0x1a, 0x44, 0x41,
	0x54, 0x41, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x48, 0x49, 0x47, 0x48, 0x5f, 0x46,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x4e, 0x43, 0x59, 0x10, 0x05, 0x42, 0x09, 0x5a, 0x07, 0x2e, 0x2f,
	0x70, 0x62, 0x3b, 0x70, 0x62,
}

var (
	file_pb_message_proto_rawDescOnce sync.Once
	file_pb_message_proto_rawDescData = file_pb_message_proto_rawDesc
)

func file_pb_message_proto_rawDescGZIP() []byte {
	file_pb_message_proto_rawDescOnce.Do(func() {
		file_pb_message_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_message_proto_rawDescData)
	})
	return file_pb_message_proto_rawDescData
}

var file_pb_message_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pb_message_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_pb_message_proto_goTypes = []interface{}{
	(Message_MessageType)(0),  // 0: Message.MessageType
	(*Message)(nil),           // 1: Message
	(*Message_ParamType)(nil), // 2: Message.ParamType
}
var file_pb_message_proto_depIdxs = []int32{
	0, // 0: Message.type:type_name -> Message.MessageType
	2, // 1: Message.params:type_name -> Message.ParamType
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_pb_message_proto_init() }
func file_pb_message_proto_init() {
	if File_pb_message_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_message_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Message); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_message_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Message_ParamType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_message_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_message_proto_goTypes,
		DependencyIndexes: file_pb_message_proto_depIdxs,
		EnumInfos:         file_pb_message_proto_enumTypes,
		MessageInfos:      file_pb_message_proto_msgTypes,
	}.Build()
	File_pb_message_proto = out.File
	file_pb_message_proto_rawDesc = nil
	file_pb_message_proto_goTypes = nil
	file_pb_message_proto_depIdxs = nil
}
