syntax = "proto3";
package pb;

option go_package = "../pb";

enum TestState{//测试状态
    Empty = 0;      //未开始
    InProgress = 1; //进行中
    Finished = 2;    //已完成
    Stop = 3;       //已停止
    Suspend = 4;     //已暂停
    Saved = 5;      //已序列化后保存
}
enum TestResult{//测试结果
    OK = 0; //测试通过
    NG = 1;  //测试失败
    NoConclusion = 2;
}

enum TestType{//测试类型
    Manual = 0;    //手动选择
    Automatic = 1;  //自动获取数据
    HalfManual = 2;  //人工输入数据，自动判断是否符合标准
}
message AllMessages
{
    string testerName = 1;
    string testDate = 2;
    string reviewerName = 3;
    string reviewDate = 4;
    string productionID = 5;
    repeated string inspectorName = 6;
    string inspectDate = 7;
    string stationQR = 8;
    string containerID = 9;
    repeated TestMessage testInformation = 10;
    string stationName = 11;
    string stationID = 12;
}
message TestMessage
{
    string stepString            = 1;
    string title                 = 2;
    string description           = 3;
    repeated string resultData   = 4;
    TestType testType            = 5;
    TestResult testResult        = 6;
    TestState testState          = 7;
    string ngDescription         = 8;
    repeated Point points        = 9;
}

message Point
{
    int64 x = 1;
    int64 y = 2;
}