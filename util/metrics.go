package util

import uprom "git.nevint.com/golang-libs/common-utils/prometheus"

var CustomMetricsList []*uprom.Metric

func init() {
	CustomMetricsList = []*uprom.Metric{
		FailSendImage,
		FailSendServiceInfo,
		FailDetectAbnormalImage,
		FailCMSCalculation,
		FailPushCMSStrategy,
		WsUnexpectedClose,
		FailCCCalculation,
	}
}

var FailSendImage = &uprom.Metric{
	ID:     "failSendImage",
	Name:   "fail_send_image_total",
	Help:   "count about fail to send image",
	Type:   "counter_vec",
	Labels: []string{"receiver", "image_type"}}

var FailSendServiceInfo = &uprom.Metric{
	ID:     "failSendServiceInfo",
	Name:   "fail_send_service_info_total",
	Help:   "count about fail to send service info for torque feature calculation",
	Type:   "counter_vec",
	Labels: []string{"project"}}

var FailDetectAbnormalImage = &uprom.Metric{
	ID:     "failDetectAbnormalImage",
	Name:   "fail_detect_abnormal_image_total",
	Help:   "count about fail to detect abnormal image",
	Type:   "counter_vec",
	Labels: []string{"device_id", "image_type"},
}

var FailCMSCalculation = &uprom.Metric{
	ID:     "failCMSCalculation",
	Name:   "fail_cms_calculation_total",
	Help:   "count about fail to calculate cms",
	Type:   "counter_vec",
	Labels: []string{"device_id"},
}

var FailPushCMSStrategy = &uprom.Metric{
	ID:     "failPushCMSStrategy",
	Name:   "fail_push_cms_strategy_total",
	Help:   "count about fail to push cms strategy to nmp",
	Type:   "counter_vec",
	Labels: []string{"device_id"},
}

var WsUnexpectedClose = &uprom.Metric{
	ID:     "wsUnexpectedClose",
	Name:   "ws_unexpected_close_total",
	Help:   "count about unexpected closed websocket connection",
	Type:   "counter_vec",
	Labels: []string{"caller"},
}

// FailCCCalculation cc算法计算失败
var FailCCCalculation = &uprom.Metric{
	ID:     "failCCCalculation",
	Name:   "fail_cc_calculation_total",
	Help:   "count about fail to calculate cc",
	Type:   "counter_vec",
	Labels: []string{"algorithm", "device_type"},
}
