package util

import (
	"bytes"
	"container/heap"
	"context"
	"crypto/md5"
	"crypto/sha256"
	"crypto/sha512"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"math/rand"
	"mime/multipart"
	"net/http"
	"net/url"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	um "git.nevint.com/golang-libs/common-utils/model"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/constant"
	"git.nevint.com/welkin2/welkin-backend/model"
	"git.nevint.com/welkin2/welkin-backend/logger"
)

const (
	timeLayout      = "2006-01-02 15:04:05"
	milliTimeLayout = "2006-01-02 15:04:05.000"
)

func ConvertTime(ts int64) time.Time {
	unix := time.Unix(ts/1000, 0)
	return unix.Local()
}

func DecodeTime(timeValue time.Time) string {
	return timeValue.Format(timeLayout)
}

func DecodeMilliTime(msec int64) string {
	return time.UnixMilli(msec).Format(milliTimeLayout)
}

func CheckLength(ts int64) bool {
	s := strconv.FormatInt(ts, 10)
	if len(s) != 13 {
		return false
	}
	return true
}

func ParseTimeRange(ts int64) (interval string) {
	unix := time.Unix(ts/1000, 0)
	return encodeDate(int(unix.Month()))
}

func EncodeDate(date string) (name string) {
	// date is like: "2023-11-28"
	month := date[5:7]
	switch month {
	case "01", "02":
		name = "1_2"
	case "03", "04":
		name = "3_4"
	case "05", "06":
		name = "5_6"
	case "07", "08":
		name = "7_8"
	case "09", "10":
		name = "9_10"
	case "11", "12":
		name = "11_12"
	}
	return
}

func EncodeMonth(month int) (name string) {
	switch month {
	case 1, 2:
		name = "1_2"
	case 3, 4:
		name = "3_4"
	case 5, 6:
		name = "5_6"
	case 7, 8:
		name = "7_8"
	case 9, 10:
		name = "9_10"
	case 11, 12:
		name = "11_12"
	}
	return
}

func ParseTimeRangeList(from, to int64) map[string]struct{} {
	fromUnix, toUnix := time.Unix(from/1000, 0), time.Unix(to/1000, 0)
	fromMonth, toMonth := int(fromUnix.Month()), int(toUnix.Month())
	if toMonth%2 != 0 {
		toMonth++
	}
	interval := make(map[string]struct{})
	if fromUnix.Year() == toUnix.Year() {
		for i := fromMonth; i <= toMonth; i = i + 2 {
			if _, has := interval[encodeDate(i)]; has {
				continue
			}
			interval[encodeDate(i)] = struct{}{}
		}
	} else {
		// from must be less than to
		for i := fromMonth; i <= 12; i = i + 2 {
			if _, has := interval[encodeDate(i)]; has {
				continue
			}
			interval[encodeDate(i)] = struct{}{}
		}
		for j := 1; j <= toMonth; j = j + 2 {
			if _, has := interval[encodeDate(j)]; has {
				continue
			}
			interval[encodeDate(j)] = struct{}{}
		}
	}
	return interval
}

func encodeDate(month int) (name string) {
	switch month {
	case 1, 2:
		name = "1_2"
	case 3, 4:
		name = "3_4"
	case 5, 6:
		name = "5_6"
	case 7, 8:
		name = "7_8"
	case 9, 10:
		name = "9_10"
	case 11, 12:
		name = "11_12"
	}
	return
}

func containNextMonth(month, day int) bool {
	var need bool
	switch month {
	case 1, 3, 5, 7, 8, 10, 12:
		if day == 31 {
			need = true
		}
	case 4, 6, 9, 11:
		if day == 30 {
			need = true
		}
	case 2:
		if day == 28 {
			need = true
		}
	}
	return need
}

func GetPossibleDB(from, to int64) map[string]struct{} {
	interval := make(map[string]struct{})
	if from == 0 && to == 0 {
		return interval
	}
	if from != 0 {
		fromUnix := time.Unix(from/1000, 0)
		fromMonth := int(fromUnix.Month())
		interval[encodeDate(fromMonth)] = struct{}{}
		// day range in [0,23]
		if fromUnix.Hour() == 23 && fromUnix.Minute() > 45 {
			need := containNextMonth(fromMonth, fromUnix.Day())
			if need {
				nextMonth := fromMonth + 1
				if nextMonth == 13 {
					nextMonth = 1
				}
				if _, has := interval[encodeDate(nextMonth)]; !has {
					interval[encodeDate(nextMonth)] = struct{}{}
				}
			}
		}
	} else {
		toUnix := time.Unix(to/1000, 0)
		toMonth := int(toUnix.Month())
		interval[encodeDate(toMonth)] = struct{}{}
		// day range in [0,23]
		if toUnix.Hour() == 0 && toUnix.Minute() < 15 && toUnix.Day() == 1 {
			preMonth := toMonth - 1
			if preMonth == 0 {
				preMonth = 12
			}
			if _, has := interval[encodeDate(preMonth)]; !has {
				interval[encodeDate(preMonth)] = struct{}{}
			}
		}
	}
	return interval
}

func GenSHA512ByString(input string) string {
	data := []byte(input)
	has := sha512.Sum512(data)
	sha512 := fmt.Sprintf("%x", has) //将[]byte转成16进制
	return sha512
}

func GenSHA512ByBytes(data []byte) string {
	has := sha512.Sum512(data)
	sha512 := fmt.Sprintf("%x", has) //将[]byte转成16进制
	return sha512
}

func GenMd5(input string) string {

	data := []byte(input)
	has := md5.Sum(data)
	md5str1 := fmt.Sprintf("%x", has) //将[]byte转成16进制
	result := strings.ToUpper(md5str1)
	return result
}

func CheckFileSHA512(fileHeader *multipart.FileHeader, expectedSHA512 string) error {
	file, err := fileHeader.Open()
	if err != nil {
		return fmt.Errorf("file open failed: %w", err)
	}
	defer file.Close()

	hash := sha512.New()
	if _, err = io.Copy(hash, file); err != nil {
		return fmt.Errorf("read file failed: %w", err)
	}

	calculatedSHA512 := hex.EncodeToString(hash.Sum(nil))
	if calculatedSHA512 != expectedSHA512 {
		return errors.New("sha512 mismatch")
	}
	return nil
}

func CheckFileMD5(fileHeader *multipart.FileHeader, fileMD5 string) error {
	fileMD5 = strings.ToLower(fileMD5)
	file, err := fileHeader.Open()
	if err != nil {
		return err
	}
	defer file.Close()

	//Open a new hash interface to write to
	hash := md5.New()

	//Copy the file in the hash interface and check for any error
	if _, err := io.Copy(hash, file); err != nil {
		return err
	}
	//Get the 16 bytes hash
	hashInBytes := hash.Sum(nil)[:16]

	//Convert the bytes to a string
	MD5String := hex.EncodeToString(hashInBytes)
	if fileMD5 != MD5String {
		return fmt.Errorf("file md5 mismatch, expect: %s, get: %s", fileMD5, MD5String)
	}
	return nil
}

// NowUnixMS 返回 Unix的时间，单位毫秒
func NowUnixMS() int64 {
	return time.Now().UnixNano() / int64(time.Millisecond)
}

func ConvertMapKey(arr []string) (map[string]struct{}, bool) {
	table := make(map[string]struct{})
	for _, i := range arr {
		if i == "" {
			return nil, false
		}
		table[i] = struct{}{}
	}
	return table, true
}

func CalculateUpdatePoint(targetVersion, referenceVersion string) string {
	if targetVersion == "" {
		return ""
	}
	if referenceVersion == "" {
		return "1,1,1"
	}
	updatePoint := []string{"1", "1", "1"}
	currentAlgorithmVersion := strings.Split(targetVersion, ".")
	lastAlgorithmVersion := strings.Split(referenceVersion, ".")
	for i := range currentAlgorithmVersion {
		if currentAlgorithmVersion[i] == lastAlgorithmVersion[i] {
			updatePoint[i] = "0"
		}
	}
	return strings.Join(updatePoint, ",")
}

func ParseTS(ts interface{}) int64 {
	var (
		resultMap map[string]string
		result    int64
	)
	b, err := json.Marshal(ts)
	if err != nil {
		return result
	}
	err = json.Unmarshal(b, &resultMap)
	if err != nil {
		return result
	}
	result, err = strconv.ParseInt(resultMap["$numberLong"], 10, 64)
	if err != nil {
		return result
	}
	return result
}

func ParseSnapshotName(name string) (createTs time.Time, algorithmName, serviceId string, err error, isSnapshot bool, cameraType string) {
	// name is like: "Snapshot-BBSS-3346-PS-NIO-891a0a7d-7e0394fa1441c784db2c468970838406300011101700409749716-BBSA-20231120_000405_580.jpg"
	isSnapshot = true
	if !strings.HasPrefix(name, "Snapshot") || len(name) <= 33 {
		isSnapshot = false
		return
	}
	if len(name) <= 33 {
		err = errors.New("invalid snapshot name")
		return
	}
	createTime := name[len(name)-23 : len(name)-4]
	name = name[9 : len(name)-24]
	if strings.Contains(name, "no_service_id") {
		serviceIdIdx := strings.Index(name, "no_service_id")
		if serviceIdIdx < 0 {
			return time.Time{}, "", "", errors.New("invalid snapshot name"), false, ""
		}
		if serviceIdIdx > 1 {
			cameraType = name[:serviceIdIdx-1]
		}
		strList := strings.Split(name[serviceIdIdx:], "-")
		if len(strList) < 2 {
			err = errors.New("invalid snapshot name")
		} else {
			serviceId = strList[0]
			algorithmName = strings.Join(strList[1:], "-")
		}
	} else if strings.Contains(name, "PS-NIO-") || strings.Contains(name, "PUS-NIO-") {
		serviceIdIdx := max(strings.Index(name, "PS-NIO-"), strings.Index(name, "PUS-NIO-"))
		if serviceIdIdx < 0 {
			return time.Time{}, "", "", errors.New("invalid snapshot name"), false, ""
		}
		if serviceIdIdx > 1 {
			cameraType = name[:serviceIdIdx-1]
		}
		strList := strings.Split(name[serviceIdIdx:], "-")
		if len(strList) < 5 {
			err = errors.New("invalid snapshot name")
		} else {
			serviceId = strings.Join(strList[:4], "-")
			algorithmName = strings.Join(strList[4:], "-")
		}
	} else {
		err = errors.New("invalid snapshot name")
	}
	if err != nil {
		return time.Time{}, "", "", err, true, ""
	}
	createTs, err = time.ParseInLocation("20060102.150405.000", strings.ReplaceAll(strings.Split(createTime, ".")[0], "_", "."), time.Local)
	return
}

func TimeDay(ts int64) time.Time {
	t := time.UnixMilli(ts)
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
}

// RenameAlgorithmName 算法可视化的算法命名映射到版本管理的命名
func RenameAlgorithmName(project, name string) string {
	var suffix, newName string
	if project == umw.PowerSwap2 {
		suffix = "2"
	} else {
		suffix = "3"
	}
	switch name {
	case "VIP":
		newName = "ViP"
	case "VOR":
		newName = "VoR"
	case "PIP":
		newName = "PiP"
	case "RSDV_PiP", "RSDV_VLSV":
		newName = "RSDV"
	default:
		newName = name
	}
	return newName + suffix
}

// RenameConditionCode 订单的算法命名映射到laputa上的condition code
func RenameConditionCode(project, name string) string {
	var suffix string
	if project == umw.PowerSwap2 {
		suffix = "2"
	} else if project == umw.PUS3 {
		suffix = "3"
	} else if project == umw.PUS4 {
		suffix = "4"
	}
	switch name {
	case "RSDV_PiP", "RSDV_VLSV2":
		name = "PiP"
	case "RSDV_ViP", "ViP2":
		name = "ViP"
	}
	return name + suffix
}

func GetDeviceArraysMixedArray(arrays [][]model.DeviceCameraInfoResponse) (mixed []string) {
	m1 := make(map[string]byte)
	if len(arrays) > 0 {
		for i := 0; i < len(arrays); i++ {
			for _, arr := range arrays[i] {
				m1[arr.DeviceId] = 0
			}
			if i == len(arrays)-1 {
				for k, _ := range m1 {
					mixed = append(mixed, k)
				}
				return mixed
			}
			for _, data := range arrays[i+1] {
				l := len(m1)
				m1[data.DeviceId] = 1
				if l == len(m1) {
					mixed = append(mixed, data.DeviceId)
				}
			}
			if i == len(arrays)-2 {
				return mixed
			}
			m2 := make(map[string]byte)
			for _, deviceId := range mixed {
				m2[deviceId] = 0
			}
			m1 = m2
			mixed = []string{}
		}
	}
	return nil
}

func TurnStrArrToIntArr(arrStr []string) (arrInt []int) {
	for _, data := range arrStr {
		Int, _ := strconv.Atoi(data)
		arrInt = append(arrInt, Int)
	}
	return
}

func TurnStrArrToFloatArr(arrStr []string) (arrFloat []float64) {
	for _, data := range arrStr {
		num, _ := strconv.ParseFloat(data, 64)
		arrFloat = append(arrFloat, num)
	}
	return
}

type DeviceFttHeap []umw.DeviceFTT

func (h DeviceFttHeap) Len() int {
	return len(h)
}

func (h DeviceFttHeap) Less(i, j int) bool {
	return (h[i].FTT > h[j].FTT) || (h[i].FTT == h[j].FTT && h[i].DeviceId > h[j].DeviceId)
}

func (h DeviceFttHeap) Swap(i, j int) {
	h[i], h[j] = h[j], h[i]
}

func (h *DeviceFttHeap) Push(x interface{}) {
	*h = append(*h, x.(umw.DeviceFTT))
}

func (h *DeviceFttHeap) Pop() interface{} {
	old := *h
	n := len(old)
	x := old[n-1]
	*h = old[0 : n-1]
	return x
}

type DeviceFTTHeap struct {
	Heap *DeviceFttHeap
	Cap  int
}

func NewDeviceFTTHeap(cap int) *DeviceFTTHeap {
	h := &DeviceFttHeap{}
	heap.Init(h)
	return &DeviceFTTHeap{
		Heap: h,
		Cap:  cap,
	}
}

func (h *DeviceFTTHeap) Push(x umw.DeviceFTT) {
	heap.Push(h.Heap, x)
	if h.Heap.Len() > h.Cap {
		heap.Pop(h.Heap)
	}
}

func (h *DeviceFTTHeap) PopAll() []umw.DeviceFTT {
	res := make([]umw.DeviceFTT, 0)
	for h.Heap.Len() > 0 {
		res = append(res, heap.Pop(h.Heap).(umw.DeviceFTT))
	}
	for i, j := 0, len(res)-1; i < j; i, j = i+1, j-1 {
		res[i], res[j] = res[j], res[i]
	}
	return res
}

func GetLang(area, lang string) string {
	if lang == "" {
		if area == "" || area == um.China {
			lang = "zh"
		} else {
			lang = "en"
		}
	}
	return lang
}

func SetURIParamDefault(param *model.CommonUriParam) {
	if param.Page == 0 {
		param.Page = 1
	}
	if param.Size == 0 {
		param.Size = 10
	}
}

func CheckTimeRange(start, end int64) error {
	if start == 0 && end == 0 {
		return errors.New("`start_time` or `end_time` is required")
	}
	if start != 0 && !CheckLength(start) {
		return errors.New("`start_time` must be 13-bit")
	}
	if end != 0 && !CheckLength(end) {
		return errors.New("`end_time` must be 13-bit")
	}
	if start != 0 && end != 0 && start > end {
		return errors.New("`start_time` must be less than `end_time`")
	}

	return nil
}

func Download(c *gin.Context, fileName string, data []byte) {
	if c.Writer.Header().Get("Content-Type") == "" {
		c.Writer.Header().Add("Content-Type", "application/octet-stream")
	}
	c.Writer.Header().Set("Content-Transfer-Encoding", "binary")
	c.Writer.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename*=utf-8''%s", url.QueryEscape(fileName)))

	c.Writer.Write(data)
}

func DownloadFile(url string) ([]byte, error) {
	response, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer response.Body.Close()
	body, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, err
	}
	return body, nil
}

func SelectedTimeDuration(timeKey string, startTime, endTime int64) (selected bson.E) {
	if startTime != 0 || endTime != 0 {
		if startTime != 0 && endTime == 0 {
			switch timeKey {
			case "date":
				selected = bson.E{Key: timeKey, Value: bson.M{"$gte": ConvertTime(startTime)}}
			default:
				selected = bson.E{Key: timeKey, Value: bson.M{"$gte": startTime}}

			}
		} else if startTime == 0 && endTime != 0 {
			switch timeKey {
			case "date":
				selected = bson.E{Key: timeKey, Value: bson.M{"$lte": ConvertTime(endTime)}}
			default:
				selected = bson.E{Key: timeKey, Value: bson.M{"$lte": endTime}}
			}
		} else {
			switch timeKey {
			case "date":
				selected = bson.E{Key: timeKey, Value: bson.M{"$gte": ConvertTime(startTime), "$lte": ConvertTime(endTime)}}
			default:
				selected = bson.E{Key: timeKey, Value: bson.M{"$gte": startTime, "$lte": endTime}}
			}
		}
	}
	return
}

// GetUrlToken 获取飞书云文档token以及type
func GetUrlToken(url string) (string, string) {
	url = strings.Split(url, "?")[0]
	url = strings.Split(url, "#")[0]
	parts := strings.Split(url, "/")
	if len(parts) < 2 {
		return "", ""
	}
	return parts[len(parts)-1], parts[len(parts)-2]
}

func JudgeParamSwitchOn(param model.ParametricFormulaInfo) (bool, error) {
	switch param.Key {
	case 870003:
		switch param.Value {
		case 1:
			return true, nil
		case 2:
			return false, nil
		default:
			return false, errors.New(fmt.Sprintf("Unknown value:%d,algorithm id:%d", param.Value, param.Key))
		}
	case 870010, 870062, 870065, 870076, 870080, 870087, 870113, 870124, 870127:
		switch param.Value {
		case 1:
			return true, nil
		case 0:
			return false, nil
		default:
			return false, errors.New(fmt.Sprintf("Unknown value:%d,algorithm id:%d", param.Value, param.Key))
		}
	}
	return true, nil
}

func SplitAlgorithm(algName string) []string {
	res := []string{algName}
	if algName == "BSA" {
		res = []string{model.BSAService, model.BSAVehicle}
	} else if algName == "RSDS" {
		res = []string{model.RSDSOpen, model.RSDSClose}
	}
	return res
}

//var IntAlgorithmMap = map[int]string{
//	1:  "WL",
//	2:  "VIP",
//	3:  "WLCC",
//	4:  "VOR",
//	5:  "SMD",
//	6:  "BSA",
//	7:  "BSCC",
//	8:  "BBCC",
//	9:  "PIP",
//	10: "PPP",
//	11: "RSDV_PiP",
//	12: "RSDV_VLSV",
//	13: "SAPA",
//	14: "BBSA",
//	15: "WPV",
//	16: "RSDS",
//}

func Partition(arr []model.Node, low, high int) int {
	pivot := arr[high]
	i := low - 1
	for j := low; j < high; j++ {
		if arr[j].Name > pivot.Name {
			i++
			arr[i], arr[j] = arr[j], arr[i]
		}
	}
	arr[i+1], arr[high] = arr[high], arr[i+1]
	return i + 1
}

func QuickSort(arr []model.Node, low, high int) {
	if low < high {
		pivot := Partition(arr, low, high)
		QuickSort(arr, low, pivot-1)
		QuickSort(arr, pivot+1, high)
	}
}

func GetUrlPrice(country string) string {
	u := constant.ApiUrl + constant.Token + constant.DocumentTypeForPrice
	if c := constant.CountryMap[country]; c != nil {
		return u + c.Encode()
	}
	return ""
}
func GetUrlWindAndSolar(country string) string {
	u := constant.ApiUrl + constant.Token + constant.DocumentTypeForWindAndSolar
	if c := constant.CountryMap[country]; c != nil {
		return u + c.Encode()
	}
	return ""
}
func GetImbalanceUrl() string {
	return "https://www.tennet.org/xml/balancedeltaprices/balans-delta.xml"
}

func GetDateUnix() int64 {
	cur := time.Now()
	date := time.Date(cur.Year(), cur.Month(), cur.Day(), 0, 0, 0, 0, cur.Location())
	return date.Unix()
}

func GetDateUnixV2(cur time.Time) int64 {
	date := time.Date(cur.Year(), cur.Month(), cur.Day(), 0, 0, 0, 0, cur.Location())
	return date.Unix()
}

func GetAfrrUrl() string {
	return "https://www.tennet.org/xml/imbalanceprice/" + time.Now().AddDate(0, 0, -2).Format("20060102") + ".xml"
}
func ReverseSlice(s []model.Record) {
	for i, j := 0, len(s)-1; i < j; i, j = i+1, j-1 {
		s[i], s[j] = s[j], s[i]
	}
}

func TsString(ts int64) string {
	if ts == 0 {
		return ""
	}
	return time.UnixMilli(ts).Format("2006-01-02 15:04:05.000")
}
func GetMinPower(soc float64, Tpy string) float32 {
	if Tpy == "battery_75" {
		if soc <= 0.87 {
			return 40.0
		} else if 0.87 < soc && soc <= 0.9 {
			var power = float32(40 - (soc-0.87)/0.03*10)
			return power
		} else {
			return 0
		}
	} else {
		if soc <= 0.9 {
			return 40
		} else {
			return 0
		}
	}

}
func HoursToTimestamp(timestamp int64, hoursToAdd int64) int64 {
	t := time.Unix(timestamp, 0)

	hoursDuration := time.Duration(hoursToAdd) * time.Hour

	newTime := t.Add(hoursDuration)

	return newTime.Unix()
}
func MinutesToTimestamp(timestampSeconds int64, minutesString string) (int64, error) {
	t := time.Unix(timestampSeconds, 0)

	parts := strings.Split(minutesString, ":")
	if len(parts) != 2 {
		return 0, fmt.Errorf("无效的分钟字符串格式")
	}

	hours, err := strconv.Atoi(parts[0])
	if err != nil {
		return 0, fmt.Errorf("无法转换小时部分: %v", err)
	}

	minutes, err := strconv.Atoi(parts[1])
	if err != nil {
		return 0, fmt.Errorf("无法转换分钟部分: %v", err)
	}

	minutesDuration := time.Duration(hours)*time.Hour + time.Duration(minutes)*time.Minute

	newTime := t.Add(minutesDuration)

	return newTime.Unix(), nil
}
func GetDataUnixDayAhead() int64 {
	cur := time.Now()
	date := time.Date(cur.Year(), cur.Month(), cur.Day()-1, 0, 0, 0, 0, cur.Location())
	return date.Unix()
}
func GetFcrdUrl1(t time.Time) string {
	currentDate := t.Format("01/02/2006")
	url := fmt.Sprintf("https://mimer.svk.se/PrimaryRegulation/DownloadExcel?periodFrom=%s%%2000%%3A00%%3A00&periodTo=%s%%2000%%3A00%%3A00&auctionTypeId=2&productTypeId=0", currentDate, currentDate)
	return url

}
func GetFcrdUrl2(t time.Time) string {
	currentDate := t.Format("01/02/2006")
	url := fmt.Sprintf("https://mimer.svk.se/PrimaryRegulation/DownloadExcel?periodFrom=%s%%2000%%3A00%%3A00&periodTo=%s%%2000%%3A00%%3A00&auctionTypeId=3&productTypeId=0", currentDate, currentDate)
	return url

}
func HoursToTimestamp2(timestamp int64, hoursToAdd int64) int64 {
	t := time.Unix(timestamp, 0)

	hoursDuration := time.Duration(hoursToAdd) * time.Hour

	newTime := t.Add(hoursDuration)

	return newTime.Unix()
}

func GetApolloCluster() (cluster string) {
	env := ucmd.GetEnv()
	if ucmd.GetArea() != um.Europe {
		cluster = fmt.Sprintf("ppd-welkin-backend-tc-tke-%s", env)
	} else {
		// TODO:change cluster
		if env == "prod" {
			cluster = "ppd-welkin-backend-eks-prod"
		} else {
			cluster = "ppd-welkin-backend-eu-aks-stg"
		}
	}
	return
}

func ConvertStringArray(arr []string) (res []any) {
	res = make([]any, len(arr))
	for i, item := range arr {
		res[i] = item
	}
	return
}

// GetUserAvatar 获取用户头像
func GetUserAvatar(conf *ucfg.Config, userIds map[string]struct{}) (map[string]string, error) {
	res := make(map[string]string)
	if len(userIds) == 0 {
		return res, nil
	}
	userIdList := make([]string, 0)
	for userId := range userIds {
		userIdList = append(userIdList, userId)
	}
	maxLen := 50
	idx := 0
	uri := fmt.Sprintf("%s/api/headImg/getFmsImgPath", conf.Welkin.EHRUrl)
	ct := ucmd.NewHttpClient(ucmd.HttpClient{})

	getAvatar := func(userList []string) error {
		byteData, err := json.Marshal(userList)
		if err != nil {
			return fmt.Errorf("fail to marshal json: %v", err)
		}
		req, err := http.NewRequest("POST", uri, bytes.NewBuffer(byteData))
		if err != nil {
			return fmt.Errorf("fail to build new request: %v", err)
		}
		req.Header.Set("Content-Type", "application/json")
		resp, err := ct.Client.Do(req)
		if err != nil {
			return fmt.Errorf("fail to send http request: %v", err)
		}
		defer resp.Body.Close()
		data, err := io.ReadAll(resp.Body)
		if err != nil {
			return fmt.Errorf("fail to read body: %v", err)
		}
		if resp.StatusCode != http.StatusOK {
			return fmt.Errorf("http response status code: %d, resp data: %s", resp.StatusCode, string(data))
		}
		var response model.GetUserAvatarResponse
		if err = json.Unmarshal(data, &response); err != nil {
			return fmt.Errorf("fail to unmarshal response, err: %v, resp data: %s", err, string(data))
		}
		if len(response.ResultData.Data) == 0 {
			return fmt.Errorf("response data is nil, resp data: %s", string(data))
		}
		for _, item := range response.ResultData.Data {
			res[item.AdAccount] = item.HeadPic
		}
		return nil
	}
	for idx < len(userIdList) {
		endIdx := idx + maxLen
		if endIdx > len(userIdList) {
			endIdx = len(userIdList)
		}
		err := getAvatar(userIdList[idx:endIdx])
		if err != nil {
			return res, err
		}
		idx = endIdx
	}

	return res, nil
}

func product(arrays [][]int, result *[][]int, current []int, index int) {
	if index == len(arrays) {
		// 当处理完所有数组时，将当前组合添加到结果集
		combination := make([]int, len(current))
		copy(combination, current)
		*result = append(*result, combination)
		return
	}
	for _, value := range arrays[index] {
		// 对于当前数组的每个元素，将其添加到当前组合中并递归处理下一个数组
		next := append(current, value)
		product(arrays, result, next, index+1)
	}
}

// CartesianProduct 递归计算多个数组的笛卡尔积
func CartesianProduct(arrays ...[]int) [][]int {
	var res [][]int
	product(arrays, &res, []int{}, 0)
	return res
}

// SetField 根据字段名设置结构体的字段值
func SetField(s interface{}, fieldName string, value interface{}) error {
	// 获取结构体的反射值对象
	rv := reflect.ValueOf(s)
	// 检查传入的是不是指针，因为我们需要通过指针来修改原始结构体
	if rv.Kind() != reflect.Ptr || rv.IsNil() {
		return fmt.Errorf("s is not a pointer or is nil")
	}
	// 获取指针指向的元素
	rv = rv.Elem()
	// 检查元素是不是结构体
	if rv.Kind() != reflect.Struct {
		return fmt.Errorf("s is not pointing to a struct")
	}
	// 获取指定的字段
	fv := rv.FieldByName(fieldName)
	// 确保这个字段存在并且可以设置值
	if !fv.IsValid() {
		return fmt.Errorf("field %s does not exist in the struct", fieldName)
	}
	if !fv.CanSet() {
		return fmt.Errorf("field %s cannot be set", fieldName)
	}
	// 获取值的反射值对象
	valRv := reflect.ValueOf(value)
	// 确保值的类型和字段的类型相匹配
	if fv.Type() != valRv.Type() {
		return fmt.Errorf("provided value type does not match field type")
	}
	// 设置字段的值
	fv.Set(valRv)
	return nil
}

func ParseInt(num interface{}) int {
	switch v := num.(type) {
	case int32:
		return int(v)
	case int64:
		return int(v)
	case int:
		return v
	case float32:
		return int(v)
	case float64:
		return int(v)
	case string:
		vFloat, err := strconv.ParseFloat(v, 64)
		if err != nil {
			return -1
		}
		return int(vFloat)
	default:
		return -1
	}
}

func ParseFloat(num interface{}) float64 {
	switch v := num.(type) {
	case int32:
		return float64(v)
	case int64:
		return float64(v)
	case int:
		return float64(v)
	case float32:
		return float64(v)
	case float64:
		return v
	case string:
		vFloat, err := strconv.ParseFloat(v, 64)
		if err != nil {
			return -1
		}
		return vFloat
	default:
		return -1
	}
}

func GetTimeLoc() *time.Location {
	// TODO 通过环境变量判断时区
	loc, _ := time.LoadLocation("Asia/Shanghai")
	return loc
}

type Rand struct {
	*rand.Rand
}

// GenerateRandomKey 根据probMap中value的概率，生成对应的key
func (rng *Rand) GenerateRandomKey(probMap map[string]float64) string {
	r := rng.Float64()
	// 累加概率，直到累加值超过随机数
	var acc float64
	for k, v := range probMap {
		acc += v
		if r < acc {
			return k
		}
	}
	return ""
}

// GenerateIntFromRange 在给定区间内生成随机数，左闭右开
func (rng *Rand) GenerateIntFromRange(input string) int {
	parts := strings.Split(strings.Trim(input, "[]()"), ",")
	if len(parts) != 2 {
		return math.MinInt
	}
	l, err1 := strconv.Atoi(parts[0])
	r, err2 := strconv.Atoi(parts[1])
	if err1 != nil || err2 != nil {
		return math.MinInt
	}
	return rng.Intn(r-l) + l
}

// StandardizeProbMap 标准化probMap中的概率，使所有value的和为1
func StandardizeProbMap(probMap map[string]float64) map[string]float64 {
	sum := 0.0
	for _, v := range probMap {
		sum += v
	}
	if sum == 0 {
		return nil
	}
	for k, v := range probMap {
		probMap[k] = v / sum
	}
	return probMap
}

func CompareFloatSlice(arr1, arr2 []float64) bool {
	if len(arr1) != len(arr2) {
		return false
	}
	for i := range arr1 {
		if int(arr1[i]) != int(arr2[i]) {
			return false
		}
	}
	return true
}

// Second2Min 秒 转 分钟，浮点型。 不考虑失精度问题
func Second2Min(second float64) float64 {
	b := int(second)
	c := second - float64(b)
	mins := b / 60
	reminder := (float64(b%60) + c) / 60
	return float64(mins) + reminder
}

// GetOperationImageType 获取所有运营照片类型
func GetOperationImageType() []int32 {
	return []int32{1, 2, 3, 4, 5, 6, 7, 8, 20, 23}
}

// DeviceIsPowerCharger 判断设备类型是否为桩
func DeviceIsPowerCharger(project string) bool {
	return project == umw.PowerThor || project == umw.PSC4 || project == umw.PowerCharger || project == umw.PowerPAC1
}

// GetDateStrList
func GetDateStrList(startDateStr, endDateStr string) ([]string, error) {
	// 将字符串转换为time.Time类型
	startDate, err := time.ParseInLocation("2006-01-02", startDateStr, time.Local)
	if err != nil {
		return nil, err
	}
	endDate, err := time.ParseInLocation("2006-01-02", endDateStr, time.Local)
	if err != nil {
		return nil, err
	}

	// 循环从开始日期到结束日期，每天增加一天
	res := []string{}
	currentDate := startDate
	for currentDate.Before(endDate) || currentDate.Equal(endDate) {
		// 格式化当前日期为字符串
		dateStr := currentDate.Format("2006-01-02")
		res = append(res, dateStr)
		// 增加一天
		currentDate = currentDate.Add(24 * time.Hour)
	}
	return res, nil
}

func GenMd5Byte(byteData []byte) string {
	data := md5.Sum(byteData)
	md5str := fmt.Sprintf("%x", data)
	return strings.ToLower(md5str)
}

func GenSha256Byte(byteData []byte) string {
	data := sha256.Sum256(byteData)
	sha256str := fmt.Sprintf("%x", data)
	return strings.ToLower(sha256str)
}

// RoundFloat float保留小数点后的precision位
func RoundFloat(val float64, precision int) float64 {
	ratio := math.Pow(10, float64(precision))
	return math.Round(val*ratio) / ratio
}

// RoundFloatPtr float指针保留小数点后的precision位
func RoundFloatPtr(val *float64, precision int) *float64 {
	if val == nil {
		return nil
	}
	ratio := math.Pow(10, float64(precision))
	res := math.Round(*val*ratio) / ratio
	return &res
}

// PtrToString 将指针转为字符串
func PtrToString[T any](ptr *T) string {
	if ptr == nil {
		return "null"
	}
	return fmt.Sprintf("%v", *ptr)
}

// GetTwoDecimal 去两位小数 四舍五入
func GetTwoDecimal(value float64) float64 {
	result := math.Round(value*100) / 100
	return result
}

// Flatten 平铺嵌套的map
func Flatten(m map[string]interface{}) map[string]interface{} {
	result := make(map[string]interface{})
	for key, value := range m {
		if child, ok := value.(map[string]interface{}); ok {
			// 递归处理子map并合并结果
			for k, v := range Flatten(child) {
				result[k] = v
			}
		} else {
			// 直接添加叶子节点
			result[key] = value
		}
	}
	return result
}

type HourlyTime struct {
	StartTime int64
	EndTime   int64
	Hour      int // 范围0～23
}

// SplitTimeByHour 将startTime和endTime之间的时间划分为小时粒度
func SplitTimeByHour(startTime, endTime int64) []HourlyTime {
	if startTime >= endTime {
		return nil
	}
	start := time.UnixMilli(startTime)
	end := time.UnixMilli(endTime)
	hourStart := time.Date(start.Year(), start.Month(), start.Day(), start.Hour(), 0, 0, 0, start.Location())
	hourEnd := time.Date(end.Year(), end.Month(), end.Day(), end.Hour(), 0, 0, 0, end.Location())
	if !end.Equal(hourEnd) {
		hourEnd = hourEnd.Add(time.Hour)
	}
	var res []HourlyTime
	res = append(res, HourlyTime{
		StartTime: startTime,
		EndTime:   hourStart.Add(time.Hour).UnixMilli(),
		Hour:      start.Hour(),
	})
	current := hourStart.Add(time.Hour)
	for current.Before(hourEnd) {
		res = append(res, HourlyTime{
			StartTime: current.UnixMilli(),
			EndTime:   current.Add(time.Hour).UnixMilli(),
			Hour:      current.Hour(),
		})
		current = current.Add(time.Hour)
	}
	if len(res) > 0 {
		res[len(res)-1].EndTime = endTime
	}
	return res
}

type DailyTime struct {
	StartTime int64
	EndTime   int64
	Day       string // 格式为2006-01-02
}

// SplitTimeByDay 将startTime和endTime之间的时间划分为天粒度
func SplitTimeByDay(startTime, endTime int64) []DailyTime {
	if startTime >= endTime {
		return nil
	}
	start := time.UnixMilli(startTime)
	end := time.UnixMilli(endTime)
	dayStart := time.Date(start.Year(), start.Month(), start.Day(), 0, 0, 0, 0, start.Location())
	dayEnd := time.Date(end.Year(), end.Month(), end.Day(), 0, 0, 0, 0, end.Location())
	if !end.Equal(dayEnd) {
		dayEnd = dayEnd.Add(24 * time.Hour)
	}
	var res []DailyTime
	res = append(res, DailyTime{
		StartTime: startTime,
		EndTime:   dayStart.Add(24 * time.Hour).UnixMilli(),
		Day:       start.Format(time.DateOnly),
	})
	current := dayStart.Add(24 * time.Hour)
	for current.Before(dayEnd) {
		res = append(res, DailyTime{
			StartTime: current.UnixMilli(),
			EndTime:   current.Add(24 * time.Hour).UnixMilli(),
			Day:       current.Format(time.DateOnly),
		})
		current = current.Add(24 * time.Hour)
	}
	if len(res) > 0 {
		res[len(res)-1].EndTime = endTime
	}
	return res
}

// ToInterfaceSlice 将[]T类型转换为[]interface{}类型
func ToInterfaceSlice[T any](slice []T) []interface{} {
	result := make([]interface{}, len(slice))
	for i, v := range slice {
		result[i] = v
	}
	return result
}

// GetHalfHourIntervals 生成一个在指定时间范围内的半小时时间点列表（左闭右开）。
// startTimeStr 和 endTimeStr 的格式应为 "15:04"。
// 特别处理 "24:00"作为一天的结束。
func GetHalfHourIntervals(ctx context.Context, startTimeStr, endTimeStr string) []string {
	const layout = "15:04"
	// 解析起始时间字符串
	startTime, err := time.Parse(layout, startTimeStr)
	if err != nil {
		logger.CtxLog(ctx).Errorf("getHalfHourIntervals, fail to parse start time, err: %v, startTime: %s", err, startTimeStr)
		return nil
	}
	// 声明结束时间变量
	var endTime time.Time
	// 对 "24:00" 进行特殊处理
	if endTimeStr == "24:00" {
		// "24:00" 被视为第二天的 "00:00"
		t, err := time.Parse(layout, "00:00")
		if err != nil {
			logger.CtxLog(ctx).Errorf("getHalfHourIntervals, fail to parse end time, err: %v, endTime: %s", err, endTimeStr)
			return nil
		}
		endTime = t.Add(24 * time.Hour) // 加上24小时
	} else {
		// 正常解析其他时间字符串
		endTime, err = time.Parse(layout, endTimeStr)
		if err != nil {
			logger.CtxLog(ctx).Errorf("getHalfHourIntervals, fail to parse end time, err: %v, endTime: %s", err, endTimeStr)
			return nil
		}
	}
	var intervals []string
	// 从起始时间开始循环，每次增加30分钟
	for t := startTime; t.Before(endTime); t = t.Add(30 * time.Minute) {
		intervals = append(intervals, t.Format(layout))
	}
	return intervals
}
