/*
 * @Author: kui.gao1 <EMAIL>
 * @Date: 2022-10-28 10:54:05
 * @LastEditors: kui.gao1
 * @LastEditTime: 2022-10-28 10:59:56
 * @FilePath: /welkin-backend/util/tool_test.go
 * @Description: test package util
 *
 * Copyright (c) 2022 by kui.gao1 <EMAIL>, All Rights Reserved.
 */
package util

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"io"
	"math/rand"
	"os"
	"reflect"
	"strconv"
	"strings"
	"testing"
	"time"
	"unsafe"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	ulog "git.nevint.com/golang-libs/common-utils/logger"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	log "git.nevint.com/welkin2/welkin-backend/logger"
)

var (
	cfg *ucfg.Config
)

func init() {
	os.Setenv("APOLLO_META", "https://apollo-config-cn-stg.nioint.com")
	os.Setenv("ENV", "stg")
	cfg = &ucfg.Config{}
	ucfg.GetConfigApollo("config.json", true, cfg, ucfg.NeedValidate(true), ucfg.ServiceName("ppd-welkin-backend"), ucfg.Cluster(ucfg.GetWelkinApolloCluster()))
	defer ulog.Final()
	cfg.Log.DirPath = "/tmp/logs"
	log.Init(cfg.Log, true)
}

func TestGenSHA512ByString(t *testing.T) {
	if ans := GenSHA512ByString(""); ans != "cf83e1357eefb8bdf1542850d66d8007d620e4050b5715dc83f4a921d36ce9ce47d0d13c5d85f2b0ff8318d2877eec2f63b931bd47417a81a538327af927da3e" {
		t.Errorf(" %s got", ans)
	}
}

func TestGetTimestamp(t *testing.T) {
	ts := time.Now().UnixMilli()
	ts = int64(1675235717428)
	t.Log(ts)
	aa := ConvertTime(ts)
	t.Log(aa)
	t.Log(aa.Day())
	t.Log(aa.Hour())
	t.Log(aa.Minute())
	tts := strconv.Itoa(int(time.Now().Month()))
	t.Log(tts)
}

func TestParseTimeRangeList(t *testing.T) {
	res := ParseTimeRangeList(int64(1667364486281), int64(1667385778802))
	t.Log(res)
}

func TestGetPossibleDB(t *testing.T) {
	res := GetPossibleDB(1718365370835, 1718426880860)
	t.Log(res)
}

func TestParseTimeA(t *testing.T) {
	ts := int64(1666764298000) - 5*60*1000
	//tsInt64, _ := strconv.ParseInt(ts, 10, 64)
	t.Log(DecodeMilliTime(ts))
}

func TestConvertObject2Byte(t *testing.T) {
	data := `[
       {
           "service_id": "",
           "start_time": 1635721467088,
           "end_time": 1635721467088,
           "success": true
       },
       {
           "service_id": "",
           "start_time": 1635721467088,
           "end_time": 1635721467088,
           "success": false
       },
       {
           "service_id": "",
           "start_time": 1635721467088,
           "end_time": 1635721467088,
           "success": false
       },
		{
           "service_id": "",
           "start_time": 1635721467088,
           "end_time": 1635721467088,
           "success": false
       },
		{
           "service_id": "",
           "start_time": 1635721467088,
           "end_time": 1635721467088,
           "success": false
       },
		{
           "service_id": "",
           "start_time": 1635721467088,
           "end_time": 1635721467088,
           "success": false
       }
   ]`
	t.Log(unsafe.Sizeof(data))
	a := base64.StdEncoding.EncodeToString([]byte(data))
	t.Log(a)
	t.Log(unsafe.Sizeof(a))
}

func TestTimeDay(t *testing.T) {
	t.Log(TimeDay(1681449152394))
}

func TestRenameConditionCode(t *testing.T) {
	t.Log(RenameConditionCode("PowerSwap2", "RSDV_ViP"))
}

func TestDeviceFttHeap(t *testing.T) {
	deviceFTT := NewDeviceFTTHeap(1)
	ftt1 := umw.DeviceFTT{}
	ftt1.DeviceId = "device1"
	ftt1.FTT = 0.5
	deviceFTT.Push(ftt1)
	ftt2 := umw.DeviceFTT{}
	ftt2.DeviceId = "device2"
	ftt2.FTT = 0.4
	deviceFTT.Push(ftt2)
	ftt3 := umw.DeviceFTT{}
	ftt3.DeviceId = "device3"
	ftt3.FTT = 0.8
	deviceFTT.Push(ftt3)
	ftt4 := umw.DeviceFTT{}
	ftt4.DeviceId = "device4"
	ftt4.FTT = 0.1
	deviceFTT.Push(ftt4)

	t.Logf("%+v\n", deviceFTT.PopAll())
}

func TestParseSnapshotName(t *testing.T) {
	//name := "PS-NIO-841944d6-ba9533a000ca7e1815214c5ea6bbfd04c2a7c1701691900865183__ds1_f.jpg"
	//name := "Snapshot-BUS-6345-PUS-NIO-841944d6-ba9533a0567e31fab19345a773447654400010201692088968154-BSA-20230815_164544_311.jpg"
	//name := "Snapshot-PLF2345-unknown-WPV-20230808_212820_257.jpg"
	name := "Snapshot-PS-NIO-841944d6-ba9533a0bf66199b2fc34ce576049202400011101689706809634-BSA-20230719_030352_213.jpg"
	createTs, algorithmName, serviceId, err, isSnapshot, cameraType := ParseSnapshotName(name)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(createTs, algorithmName, serviceId, isSnapshot, cameraType)
}

func Test_SplitName(t *testing.T) {
	name := "Snapshot-BUSS-323-no_service_id_-PPP-test-20231114_115341_505.jpg"
	t.Log(ParseSnapshotName(name))
}

func Test_GzipString(t *testing.T) {
	str := "655f090a495029d08d502b44,655f090a495029d08d502b44,655f090a495029d08d502b44,655f090a495029d08d502b44,655f090a495029d08d502b44,655f090a495029d08d502b44,655f090a495029d08d502b44,655f090a495029d08d502b44,655f090a495029d08d502b44,655f090a495029d08d502b44,655f090a495029d08d502b45,655f090a495029d08d502b46,655f090a495029d08d502b47"

	var buf bytes.Buffer
	w := gzip.NewWriter(&buf)
	w.Write([]byte(str))
	w.Close()

	aa := fmt.Sprintf("%v", buf.String())
	r, err := gzip.NewReader(bytes.NewBufferString(aa))
	if err != nil {
		t.Error(err)
		return
	}
	byteData, err := io.ReadAll(r)
	if err != nil {
		t.Error(err)
		return
	}
	fmt.Println(string(byteData))
}

func Test_DateString(t *testing.T) {
	loc, err := time.LoadLocation("Europe/Berlin")
	if err != nil {
		t.Error(err)
	}
	a := time.Now().In(loc).UnixMilli()
	date := strings.Split(DecodeTime(time.UnixMilli(a)), " ")
	t.Log(date[1])
}

func TestGetUserAvatar(t *testing.T) {
	res, err := GetUserAvatar(cfg, map[string]struct{}{"william.shen2": {}, "liu.yang4": {}})
	if err != nil {
		t.Error(err)
	}
	fmt.Println(res)
}

func TestCartesianProduct(t *testing.T) {
	a := []int{1, 2, 3}
	b := []int{4, 5}
	c := []int{6, 7}
	res := CartesianProduct(a, b, c)
	fmt.Println(len(res))
	for _, item := range res {
		fmt.Println(item)
	}
}

func TestRand_GenerateRandomKey(t *testing.T) {
	probMap := map[string]float64{
		"[25,30)": 0.1,
		"[30,35)": 0.2,
		"[40,45)": 0.3,
		"[50,65)": 0.4,
	}
	cntMap := make(map[string]int)
	rng := Rand{rand.New(rand.NewSource(time.Now().UnixNano()))}
	for range 100000 {
		cntMap[rng.GenerateRandomKey(probMap)]++
	}
	fmt.Println(cntMap)
}

func TestRand_GenerateIntFromRange(t *testing.T) {
	rng := Rand{rand.New(rand.NewSource(time.Now().UnixNano()))}
	str := "[50,55)"
	cntMap := make(map[int]int)
	for range 100000 {
		cntMap[rng.GenerateIntFromRange(str)]++
	}
	fmt.Println(cntMap)
}

func TestSecond2Min(t *testing.T) {
	println(Second2Min(float64(90.1)))
	println(Second2Min(float64(90)))
}

func TestRoundFloat(t *testing.T) {
	fmt.Println(RoundFloat(1.2232, 2))
	fmt.Println(RoundFloat(1.2262, 2))
	fmt.Println(RoundFloat(1.22, 0))
}

func TestPtrToString(t *testing.T) {
	// Test with nil pointer
	var nilPtr *int
	if result := PtrToString(nilPtr); result != "null" {
		t.Errorf("expected 'null', got '%s'", result)
	}

	// Test with non-nil pointer
	val := 42
	if result := PtrToString(&val); result != "42" {
		t.Errorf("expected '42', got '%s'", result)
	}

	// Test with string pointer
	str := "hello"
	if result := PtrToString(&str); result != "hello" {
		t.Errorf("expected 'hello', got '%s'", result)
	}
}

func TestFlatten(t *testing.T) {
	input := map[string]interface{}{
		"a": map[string]interface{}{
			"b": map[string]interface{}{
				"c": 1,
				"d": 2,
			},
		},
		"e": 3,
	}
	output := Flatten(input)
	// 输出: map[c:1 d:2 e:3]
	fmt.Println(output)
}
func TestSplitTimeByHour(t *testing.T) {
	tests := []struct {
		name      string
		startTime int64
		endTime   int64
		want      []HourlyTime
	}{
		{
			name:      "same hour",
			startTime: 1704039300000, // 2024-01-01 00:15:00 UTC+8
			endTime:   1704041940000, // 2024-01-01 00:59:00 UTC+8
			want: []HourlyTime{
				{
					StartTime: 1704039300000,
					EndTime:   1704041940000,
					Hour:      0,
				},
			},
		},
		{
			name:      "multiple hours",
			startTime: 1704213000000, // 2024-01-03 00:30:00 UTC+8
			endTime:   1704223440000, // 2024-01-03 03:24:00 UTC+8
			want: []HourlyTime{
				{
					StartTime: 1704213000000,
					EndTime:   1704214800000,
					Hour:      0,
				},
				{
					StartTime: 1704214800000,
					EndTime:   1704218400000,
					Hour:      1,
				},
				{
					StartTime: 1704218400000,
					EndTime:   1704222000000,
					Hour:      2,
				},
				{
					StartTime: 1704222000000,
					EndTime:   1704223440000,
					Hour:      3,
				},
			},
		},
		{
			name:      "invalid time range",
			startTime: 1704045600000,
			endTime:   1704038400000,
			want:      nil,
		},
		{
			name:      "equal start and end time",
			startTime: 1704038400000,
			endTime:   1704038400000,
			want:      nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := SplitTimeByHour(tt.startTime, tt.endTime)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SplitTimeByHour() = %v, want %v", got, tt.want)
			}
		})
	}

	fmt.Println(ucmd.ToJsonStrIgnoreErr(SplitTimeByHour(1742314500000, 1742400900000)))
}

func TestSplitTimeByDay(t *testing.T) {
	tests := []struct {
		name      string
		startTime int64
		endTime   int64
		want      []DailyTime
	}{
		{
			name:      "same day",
			startTime: 1704039300000, // 2024-01-01 00:15:00 UTC+8
			endTime:   1704067140000, // 2024-01-01 07:59:00 UTC+8
			want: []DailyTime{
				{
					StartTime: 1704039300000,
					EndTime:   1704067140000,
					Day:       "2024-01-01",
				},
			},
		},
		{
			name:      "multiple days",
			startTime: 1704106800000, // 2024-01-01 23:00:00 UTC+8
			endTime:   1704279600000, // 2024-01-04 00:00:00 UTC+8
			want: []DailyTime{
				{
					StartTime: 1704106800000,
					EndTime:   1704124800000, // 2024-01-02 00:00:00 UTC+8
					Day:       "2024-01-01",
				},
				{
					StartTime: 1704124800000, // 2024-01-02 00:00:00 UTC+8
					EndTime:   1704211200000, // 2024-01-03 00:00:00 UTC+8
					Day:       "2024-01-02",
				},
				{
					StartTime: 1704211200000, // 2024-01-03 00:00:00 UTC+8
					EndTime:   1704279600000, // 2024-01-04 00:00:00 UTC+8
					Day:       "2024-01-03",
				},
			},
		},
		{
			name:      "invalid time range",
			startTime: 1704211200000,
			endTime:   1704124800000,
			want:      nil,
		},
		{
			name:      "equal start and end time",
			startTime: 1704124800000,
			endTime:   1704124800000,
			want:      nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := SplitTimeByDay(tt.startTime, tt.endTime)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SplitTimeByDay() = %v, want %v", got, tt.want)
			}
		})
	}

	fmt.Println(ucmd.ToJsonStrIgnoreErr(SplitTimeByDay(1738339200000, 1740758400000)))
}

type HourlyValue struct {
	Hour  int64   `json:"hour"`
	Value float64 `json:"value"`
}

type ModuleUse struct {
	HourlyValue
	Detail []string `json:"detail"`
}
