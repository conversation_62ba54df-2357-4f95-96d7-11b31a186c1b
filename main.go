package main

import (
	"flag"
	"os"
	"time"

	clientv3 "go.etcd.io/etcd/client/v3"

	"git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	"git.nevint.com/golang-libs/common-utils/croncluster"
	ulog "git.nevint.com/golang-libs/common-utils/logger"
	"git.nevint.com/golang-libs/common-utils/xray"

	"git.nevint.com/welkin2/welkin-backend/config"
	"git.nevint.com/welkin2/welkin-backend/exec"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	"git.nevint.com/welkin2/welkin-backend/pkg/task"
	"git.nevint.com/welkin2/welkin-backend/router"
)

var (
	configPath string
	logDir     string
	verbose    bool
)

func init() {
	flag.StringVar(&configPath, "config", "", "specify local config instead of apollo")
	flag.StringVar(&logDir, "logDir", "", "specify local log directory")
	flag.BoolVar(&verbose, "verbose", false, "print log verbosely")
	flag.Parse()
}

// 程序启动顺序
// 1. 读配置
// 2. 初始化日志
// 3. 初始化定时任务
// 4. 启动http服务
func main() {
	// check the environment of the service, if it is test or dev, set the verbose to true
	// otherwise, nothing to do
	switch cmd.GetEnv() {
	case "test", "dev": // test and dev, show debug and info log
		verbose = true
	case "stg": // eu stg, show debug and info log
		area := cmd.GetArea()
		if area == "Europe" {
			verbose = true
		}
	}
	var cfg *ucfg.Config
	if configPath == "" {
		cfg = &ucfg.Config{}
		config.InitConfig()
		err := config.Apollo.LoadConfig(cfg)
		if err != nil {
			panic(err.Error())
		}
		if logDir != "" {
			cfg.Log.DirPath = logDir
		}
	} else {
		defer ucfg.Finish()
		if err := ucfg.Init(&ucfg.Config{}, configPath, verbose); err != nil {
			panic(err)
		}
		cfg = ucfg.GetWelkinConfigLocal()
	}
	config.Cfg = cfg
	model.InitConstant(cfg.ExtraConfig["evBrand"])
	_, found := os.LookupEnv("IS_LOCAL_TEST")
	if found {
		// 本地环境，获取当前工作目录
		dir, err := os.Getwd()
		if err != nil {
			panic(err.Error())
		}
		cfg.Log.DirPath = dir
	}
	if configPath == "" {
		config.Apollo.AddListener(config.ConfigChangeListener{})
	}

	var algCfg config.AlgorithmConfig
	ucfg.GetConfigApollo("algorithm.json", verbose, &algCfg)
	config.AlgorithmCfg = algCfg

	defer ulog.Final()
	log.Init(cfg.Log, verbose)

	log.Logger.Infof("init load conf:%v", cmd.ToJsonStrIgnoreErr(cfg))

	// init execlusive task manager
	etcdConf := clientv3.Config{
		Endpoints:   cfg.Etcd.Endpoints,
		DialTimeout: 5 * time.Second,
	}
	execlusiveSrv, err := croncluster.NewService(etcdConf, "/welkin-backend/croncluster/elections", log.Logger.Named("execlusive-task-manager"))
	if err != nil {
		panic(err)
	}
	cronTask := task.GetCronTask()
	execlusiveSrv.RegisterExclusiveTask(cronTask)
	go execlusiveSrv.Run()

	if os.Getenv("POD_IP") != "" {
		xraySrv := xray.NewSrv(cfg.Xray.Address, log.Logger)
		xraySrv.Start()
	}

	exec.InitCronJobs(cfg)
	router.Init(cfg, verbose)
	router.Run(cfg.HTTPServer)
}
