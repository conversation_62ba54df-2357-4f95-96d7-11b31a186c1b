package powercharger_order

import (
	"context"
	"encoding/json"
	"errors"
	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	"git.nevint.com/welkin2/welkin-backend/client"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var ServiceStopReason map[int64]string = map[int64]string{
	0:   "车端主动结束",
	1:   "云端主动结束",
	2:   "设备故障结束",
	3:   "拔枪结束",
	4:   "预约时间到结束",
	254: "其他原因",
	255: "未知原因",
	100: "设备状态异常",
	101: "SOC达设备限值",
	102: "断网结束",
	103: "设备判断车状态异常",
	104: "车检测到充电异常",
	105: "设备检测到车上传信息异常",
	106: "启动中设备检测到车异常",
	107: "启动中设备检测到自身异常",
	108: "充满结束",
	109: "其他原因",
	110: "充电中主控与充电板通信超时",
	111: "非充电完成时上传小结",
	5:   "BSM中止结束",
	6:   "BST中止充电",
	7:   "soc大于100结束充电",
	8:   "mcs结束充电",
	9:   "mpc结束充电",
	10:  "NFC刷卡结束充电",
	11:  "信用卡刷卡结束",
	12:  "复位按钮结束充电",
	25:  "用户S3按钮停止",
	26:  "云端4G停止",
	27:  "用户APP蓝牙直连停止",
	112: "桩站共建通信超时,主动结束充电",
}
var OrderStatus map[int64]string = map[int64]string{
	0: "未开始充电",
	1: "充电中",
	2: "充电结束(等待支付)",
	3: "订单完成(完成支付)",
	4: "已取消",
	5: "异常充电结束(等待支付)",
	6: "异常订单完成(完成支付)",
}

type PowerChargerOrderDO struct {
	DeviceId             string
	ResourceId           string
	OrderId              string
	ConnectorId          string
	OrderStatus          int64
	OrderCreateTimestamp int64

	ServiceId                       *string
	ServiceStartTimestamp           *int64
	ServiceEndTimestamp             *int64
	ChargedEnergyTotal              *float32
	CalibratedRealtimeChargedEnergy *float32
	EffectiveChargedEnergy          *float32
	Vin                             *string
	EnergyTotalStart                *float32
	EnergyTotal                     *float32
	ServiceStopReason               *int32
	ServiceFinishReason             *int32
	CarPlatform                     *string
	CarModelType                    *string
	CarPackagePartNumber            *string
	CarPackageGlobalVersion         *string

	Channel                  *string
	Client                   *string
	OrderSource              *int64
	OrderType                *int64
	ChargerOrderId           *string
	ShamanOrderId            *string
	UserId                   *string
	OwnerId                  *string
	VehicleId                *string
	Rid                      *string
	GroupId                  *string
	RefundStatus             *int64
	OperatorId               *string
	EquipmentOwner           *string
	RightsType               *int64
	PaymentType              *int64
	PriceType                *int64
	OriginalPrice            *int64
	OriginalServicePrice     *int64
	OriginalElectricityPrice *int64
	ActualPrice              *int64
	CostPrice                *int64
	ServicePrice             *int64
	ElectricityPrice         *int64
	ExceptionType            *int64
	IsPaid                   *bool
	ActivityId               *int64
	ShouldPrice              *int64
	ShouldElectricityPrice   *int64
	ShouldServicePrice       *int64
	StartCommandId           *string
	StartCommandStatus       *string
	StartCommandTime         *int64
	StopCommandId            *string
	StopCommandStatus        *string
	StopCommandTime          *int64
	Latitude                 *float64
	Longitude                *float64
	PayTime                  *int64
	ForceCloseSource         *string
	OssForceCloseReason      *int64
	AutoPayChannel           *int64

	Status0Timestamp *int64
	Status1Timestamp *int64
	Status2Timestamp *int64
	Status3Timestamp *int64
	Status4Timestamp *int64
	Status5Timestamp *int64
	Status6Timestamp *int64
}

type ListPowerChargerOrderCond struct {
	Project           string
	OrderId           string
	DeviceId          string
	ResourceId        string
	ServiceStopReason []int64
	OrderStatus       []int64

	StartTs        int64
	EndTs          int64
	OrderFieldName string
	Descending     bool
	Limit          int64
	Offset         int64
}

func (d *PowerChargerOrderDO) ListPowerChargerOrder(ctx context.Context, cond ListPowerChargerOrderCond) ([]*PowerChargerOrderDO, int64, error) {
	if cond.Project == "" {
		return nil, 0, errors.New("welkin project param must have!")
	}
	collectionName := ucmd.RenameProjectDB(cond.Project)
	filter := bson.D{}
	if cond.OrderId != "" {
		filter = append(filter, bson.E{Key: "order_id", Value: cond.OrderId})
	}
	if cond.DeviceId != "" {
		filter = append(filter, bson.E{Key: "device_id", Value: cond.DeviceId})
	}
	if cond.ResourceId != "" {
		filter = append(filter, bson.E{Key: "resource_id", Value: cond.DeviceId})
	}

	if cond.StartTs != 0 || cond.EndTs != 0 {
		filter = append(filter, bson.E{Key: "order_create_timestamp", Value: bson.M{"$gte": cond.StartTs, "$lt": cond.EndTs}})
	}
	if len(cond.OrderStatus) > 0 {
		filter = append(filter, bson.E{Key: "status", Value: bson.M{"$in": cond.OrderStatus}})
	}
	if len(cond.ServiceStopReason) > 0 {
		filter = append(filter, bson.E{Key: "service_stop_reason", Value: bson.M{"$in": cond.ServiceStopReason}})
	}
	sortFieldName := "order_create_timestamp"
	if cond.OrderFieldName != "" {
		sortFieldName = cond.OrderFieldName
	}
	simulationByteData, total, err := client.GetWatcher().Mongodb().NewMongoEntry(filter).ListByPagination("power_charger_order", collectionName,
		client.Pagination{Limit: cond.Limit, Offset: cond.Offset},
		client.Ordered{Key: sortFieldName, Descending: cond.Descending})
	if err != nil {
		return nil, 0, err
	}
	var orderPOs []umw.MongoPowerChargerOrder
	if err = json.Unmarshal(simulationByteData, &orderPOs); err != nil {
		return nil, 0, err
	}
	orderDOs := []*PowerChargerOrderDO{}
	for _, orderPO := range orderPOs {
		orderDOs = append(orderDOs, convertPO2DO(orderPO))
	}
	return orderDOs, total, nil
}

func (d *PowerChargerOrderDO) GetPowerChargerOrderByOrderID(ctx context.Context, project, orderId string) (*PowerChargerOrderDO, error) {
	collectionName := ucmd.RenameProjectDB(project)
	filter := bson.D{bson.E{Key: "order_id", Value: orderId}}
	var orderPO umw.MongoPowerChargerOrder
	err := client.GetWatcher().Mongodb().NewMongoEntry(filter).FindOne("power_charger_order", collectionName, options.FindOne(), &orderPO)
	if err != nil {
		return nil, err
	}
	return convertPO2DO(orderPO), nil
}

func (d *PowerChargerOrderDO) GetOrderEndTimestamp() *int64 {
	if d.Status3Timestamp != nil {
		return d.Status3Timestamp
	}
	if d.Status4Timestamp != nil {
		return d.Status4Timestamp
	}
	if d.Status6Timestamp != nil {
		return d.Status6Timestamp
	}
	return nil
}
