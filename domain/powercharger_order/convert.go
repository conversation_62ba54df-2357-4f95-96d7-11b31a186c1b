package powercharger_order

import (
	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
)

func convertPO2DO(orderPO umw.MongoPowerChargerOrder) *PowerChargerOrderDO {
	powerChargerOrderDO := &PowerChargerOrderDO{
		DeviceId:                        orderPO.DeviceId,
		ResourceId:                      orderPO.ResourceId,
		OrderId:                         orderPO.OrderId,
		ConnectorId:                     orderPO.ConnectorId,
		OrderStatus:                     ucmd.GetInt64Value(orderPO.Status),
		OrderCreateTimestamp:            ucmd.GetInt64Value(orderPO.OrderCreateTimestamp),
		ServiceId:                       orderPO.ServiceId,
		ServiceStartTimestamp:           orderPO.StartTimestamp,
		ServiceEndTimestamp:             orderPO.EndTimestamp,
		ChargedEnergyTotal:              orderPO.ChargedEnergyTotal,
		CalibratedRealtimeChargedEnergy: orderPO.CalibratedRealtimeChargedEnergy,
		EffectiveChargedEnergy:          orderPO.EffectiveChargedEnergy,
		Vin:                             orderPO.Vin,
		EnergyTotalStart:                orderPO.EnergyTotalStart,
		EnergyTotal:                     orderPO.EnergyTotal,
		ServiceStopReason:               orderPO.ServiceStopReason,
		ServiceFinishReason:             orderPO.ServiceFinishReason,
		CarPlatform:                     orderPO.CarPlatform,
		CarModelType:                    orderPO.CarModelType,
		CarPackagePartNumber:            orderPO.CarPackagePartNumber,
		CarPackageGlobalVersion:         orderPO.CarPackageGlobalVersion,
		Channel:                         orderPO.Channel,
		Client:                          orderPO.Client,
		OrderSource:                     orderPO.OrderSource,
		OrderType:                       orderPO.OrderType,
		ChargerOrderId:                  orderPO.ChargerOrderId,
		ShamanOrderId:                   orderPO.ShamanOrderId,
		UserId:                          orderPO.UserId,
		OwnerId:                         orderPO.OwnerId,
		VehicleId:                       orderPO.VehicleId,
		Rid:                             orderPO.Rid,
		GroupId:                         orderPO.GroupId,
		RefundStatus:                    orderPO.RefundStatus,
		OperatorId:                      orderPO.OperatorId,
		EquipmentOwner:                  orderPO.EquipmentOwner,
		RightsType:                      orderPO.RightsType,
		PaymentType:                     orderPO.PaymentType,
		PriceType:                       orderPO.PriceType,
		OriginalPrice:                   orderPO.OriginalPrice,
		OriginalServicePrice:            orderPO.OriginalServicePrice,
		OriginalElectricityPrice:        orderPO.OriginalElectricityPrice,
		ActualPrice:                     orderPO.ActualPrice,
		CostPrice:                       orderPO.CostPrice,
		ServicePrice:                    orderPO.ServicePrice,
		ElectricityPrice:                orderPO.ElectricityPrice,
		ExceptionType:                   orderPO.ExceptionType,
		IsPaid:                          orderPO.IsPaid,
		ActivityId:                      orderPO.ActivityId,
		ShouldPrice:                     orderPO.ShouldPrice,
		ShouldElectricityPrice:          orderPO.ShouldElectricityPrice,
		ShouldServicePrice:              orderPO.ShouldServicePrice,
		StartCommandId:                  orderPO.StartCommandId,
		StartCommandStatus:              orderPO.StartCommandStatus,
		StartCommandTime:                orderPO.StartCommandTime,
		StopCommandId:                   orderPO.StopCommandId,
		StopCommandStatus:               orderPO.StopCommandStatus,
		StopCommandTime:                 orderPO.StopCommandTime,
		Latitude:                        orderPO.Latitude,
		Longitude:                       orderPO.Longitude,
		PayTime:                         orderPO.PayTime,
		ForceCloseSource:                orderPO.ForceCloseSource,
		OssForceCloseReason:             orderPO.OssForceCloseReason,
		AutoPayChannel:                  orderPO.AutoPayChannel,
		Status0Timestamp:                orderPO.Status0Timestamp,
		Status1Timestamp:                orderPO.Status1Timestamp,
		Status2Timestamp:                orderPO.Status2Timestamp,
		Status3Timestamp:                orderPO.Status3Timestamp,
		Status4Timestamp:                orderPO.Status4Timestamp,
		Status5Timestamp:                orderPO.Status5Timestamp,
		Status6Timestamp:                orderPO.Status6Timestamp,
	}
	return powerChargerOrderDO
}
