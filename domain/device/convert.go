package device

import (
	"fmt"
	"sync"

	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"
)

type SlotBatteryInfo struct {
	SlotId int
	Field  SlotBatteryField
}

// data_id映射到对应的slot_id
var BatteryDataId2SlotPS2 = make(map[string]SlotBatteryInfo)
var BatteryDataId2SlotPUS3 = make(map[string]SlotBatteryInfo)
var BatteryDataId2SlotPUS4 = make(map[string]SlotBatteryInfo)

// 字段名映射到所有仓位的对应字段data_id
var BatteryField2DataIdPS2 = make(map[SlotBatteryField][]string)
var BatteryField2DataIdPUS3 = make(map[SlotBatteryField][]string)
var BatteryField2DataIdPUS4 = make(map[SlotBatteryField][]string)

var once sync.Once

func InitOnce() {
	once.Do(func() {
		for slot := 1; slot <= 13; slot++ {
			var dataId string
			// 电池ID
			dataId = fmt.Sprintf("%d", 500100+slot*1000)
			BatteryDataId2SlotPS2[dataId] = SlotBatteryInfo{
				SlotId: slot,
				Field:  SlotBatteryId,
			}
			BatteryField2DataIdPS2[SlotBatteryId] = append(BatteryField2DataIdPS2[SlotBatteryId], dataId)
			// 电池类型
			dataId = fmt.Sprintf("%d", 500156+slot*1000)
			BatteryDataId2SlotPS2[dataId] = SlotBatteryInfo{
				SlotId: slot,
				Field:  SlotBatteryType,
			}
			BatteryField2DataIdPS2[SlotBatteryType] = append(BatteryField2DataIdPS2[SlotBatteryType], dataId)
			// 电池用户SOC
			dataId = fmt.Sprintf("%d", 500108+slot*1000)
			BatteryDataId2SlotPS2[dataId] = SlotBatteryInfo{
				SlotId: slot,
				Field:  SlotBatteryUserSoc,
			}
			BatteryField2DataIdPS2[SlotBatteryUserSoc] = append(BatteryField2DataIdPS2[SlotBatteryUserSoc], dataId)
		}
		for slot := 1; slot <= 21; slot++ {
			var dataId string
			// 电池ID
			dataId = fmt.Sprintf("%d", 1000*slot+1)
			BatteryDataId2SlotPUS3[dataId] = SlotBatteryInfo{
				SlotId: slot,
				Field:  SlotBatteryId,
			}
			BatteryField2DataIdPUS3[SlotBatteryId] = append(BatteryField2DataIdPUS3[SlotBatteryId], dataId)
			// 电池类型
			dataId = fmt.Sprintf("%d", 1000*slot+12)
			BatteryDataId2SlotPUS3[dataId] = SlotBatteryInfo{
				SlotId: slot,
				Field:  SlotBatteryType,
			}
			BatteryField2DataIdPUS3[SlotBatteryType] = append(BatteryField2DataIdPUS3[SlotBatteryType], dataId)
			// 电池用户SOC
			dataId = fmt.Sprintf("%d", 1000*slot+22)
			BatteryDataId2SlotPUS3[dataId] = SlotBatteryInfo{
				SlotId: slot,
				Field:  SlotBatteryUserSoc,
			}
			BatteryField2DataIdPUS3[SlotBatteryUserSoc] = append(BatteryField2DataIdPUS3[SlotBatteryUserSoc], dataId)
		}
		for id := range 23 {
			var dataId string
			// 电池ID
			dataId = fmt.Sprintf("%d", 20000*id+374001)
			BatteryDataId2SlotPUS4[dataId] = SlotBatteryInfo{
				SlotId: id+1,
				Field:  SlotBatteryId,
			}
			BatteryField2DataIdPUS4[SlotBatteryId] = append(BatteryField2DataIdPUS4[SlotBatteryId], dataId)
			// 电池类型
			dataId = fmt.Sprintf("%d", 20000*id+374012)
			BatteryDataId2SlotPUS4[dataId] = SlotBatteryInfo{
				SlotId: id+1,
				Field:  SlotBatteryType,
			}
			BatteryField2DataIdPUS4[SlotBatteryType] = append(BatteryField2DataIdPUS4[SlotBatteryType], dataId)
			// 电池用户SOC
			dataId = fmt.Sprintf("%d", 20000*id+374023)
			BatteryDataId2SlotPUS4[dataId] = SlotBatteryInfo{
				SlotId: id+1,
				Field:  SlotBatteryUserSoc,
			}
			BatteryField2DataIdPUS4[SlotBatteryUserSoc] = append(BatteryField2DataIdPUS4[SlotBatteryUserSoc], dataId)
		}
	})
}

// ConvertBatteryDataId 各仓位的电池实时数据，data_id映射到对应的仓位和字段
func ConvertBatteryDataId(project string, dataId string) (SlotBatteryInfo, bool) {
	InitOnce()
	if project == umw.PowerSwap2 {
		return BatteryDataId2SlotPS2[dataId], true
	} else if project == umw.PUS3 {
		return BatteryDataId2SlotPUS3[dataId], true
	} else if project == umw.PUS4 {
		return BatteryDataId2SlotPUS4[dataId], true
	}
	return SlotBatteryInfo{}, false
}

// GetBatterySlotFieldDataIds 获取各仓位的某些电池信息字段的全部data_id
func GetBatterySlotFieldDataIds(project string, fields ...SlotBatteryField) []string {
	InitOnce()
	batteryField2DataId := make(map[SlotBatteryField][]string)
	if project == umw.PowerSwap2 {
		batteryField2DataId = BatteryField2DataIdPS2
	} else if project == umw.PUS3 {
		batteryField2DataId = BatteryField2DataIdPUS3
	} else if project == umw.PUS4 {
		batteryField2DataId = BatteryField2DataIdPUS4
	}
	res := make([]string, 0)
	for _, field := range fields {
		res = append(res, batteryField2DataId[field]...)
	}
	return res
}

func convertRbDeviceParamPO2DO(project string, po mmgo.RbDeviceInfo) RbDeviceParam {
	res := RbDeviceParam{
		Project:  project,
		DeviceId: po.DeviceId,
	}
	res.Params = make(map[int64]interface{})
	for _, param := range po.Params {
		res.Params[param.Key] = param.Value
	}
	return res
}
