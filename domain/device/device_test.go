package device

import (
	"fmt"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	gocache "github.com/patrickmn/go-cache"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	ulog "git.nevint.com/golang-libs/common-utils/logger"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	log "git.nevint.com/welkin2/welkin-backend/logger"
)

var w client.Watcher
var ctx *gin.Context

func init() {
	os.Setenv("APOLLO_META", "https://apollo-config-cn-stg.nioint.com")
	os.Setenv("ENV", "stg")
	os.Setenv("APOLLO_ACCESSKEY_SECRET", "NIO-ENCRYPT-START_70078af90a9896516b6364767b1a45c20fb96b870f9c958a17b782935828ec8d452c7435b1801d4a17a31a2f7734c88d_NIO-ENCRYPT-END")
	cfg := &ucfg.Config{}
	ucfg.GetConfigApollo("config.json", true, cfg, ucfg.NeedValidate(true), ucfg.ServiceName("ppd-welkin-backend"), ucfg.Cluster(ucfg.GetWelkinApolloCluster()))
	defer ulog.Final()
	cfg.Log.DirPath = "/tmp/logs"
	config.Cfg = cfg
	log.Init(cfg.Log, true)
	logger := log.Logger.Named("activity")
	w = client.NewWatcherByParam(config.Cfg, logger)
	var err error
	if err != nil {
		panic(err)
	}
	cache.CacheRealtimeDataID(w.Mongodb().Client)
	cache.PowerSwapCache = &cache.DeviceInfoCache{
		Cache:         gocache.New(gocache.NoExpiration, gocache.NoExpiration),
		ResourceCache: gocache.New(gocache.NoExpiration, gocache.NoExpiration),
	}
	cache.PowerSwapCache.RefreshDeviceInfoCache(w.Mongodb().Client)
	ctx = &gin.Context{Request: &http.Request{Header: make(http.Header)}}
	ctx.Request.Header.Set("X-Request-ID", "test")
}

func TestDevice_GetSlotBatteryInfo(t *testing.T) {
	// d := Device{
	// 	Project:  umw.PUS3,
	// 	DeviceId: "PS-NIO-efd6e196-0fb4a06c",
	// }
	// res, err := d.GetSlotBatteryInfo(1741190400000)
	// if err != nil {
	// 	t.Fatal(err)
	// }
	// fmt.Println(ucmd.ToJsonStrIgnoreErr(d), ucmd.ToJsonStrIgnoreErr(res))

	d := Device{
		Project:  umw.PUS4,
		DeviceId: "PUS-NIO-095fdc2a-5840a9b6",
	}
	res, err := d.GetSlotBatteryInfo(1742482800000)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(ucmd.ToJsonStrIgnoreErr(d), ucmd.ToJsonStrIgnoreErr(res))
}

func TestDevice_GetReserveBattery(t *testing.T) {
	d := Device{
		Project:  umw.PowerSwap2,
		DeviceId: "PS-NIO-6ab07094-94563340",
	}
	res, err := d.GetReserveBattery()
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(ucmd.ToJsonStrIgnoreErr(res))
}

func TestDevice_CheckSlotBatterySoc(t *testing.T) {
	d := Device{
		Project:  umw.PowerSwap2,
		DeviceId: "PS-NIO-6ab07094-94563340",
	}
	for range 1 {
		err := d.CheckSlotBatterySoc()
		if err != nil {
			t.Fatal(err)
		}
	}
}

func TestListBidirectionalDevices(t *testing.T) {
	d := Device{}

	tests := []struct {
		project string
	}{
		{project: umw.PUS3},
		{project: umw.PUS4},
	}

	for _, tt := range tests {
		t.Run(tt.project, func(t *testing.T) {
			res, err := d.ListBidirectionalDevices(ctx, tt.project)
			if err != nil {
				t.Fatalf("ListBidirectionalDevices() error = %v", err)
			}
			fmt.Println(ucmd.ToJsonStrIgnoreErr(res))
		})
	}
}

func TestDevice_ListRbDeviceParams(t *testing.T) {
	d := Device{}

	cond := RbDeviceParamsCond{
		Project:   umw.PUS3,
		Devices:   []string{"PS-NIO-3285ff15-7f564f27", "PS-NIO-efd6e196-0fb4a06c"},
		ParamKeys: []int64{902561, 902661, 902761, 902861},
	}

	res, err := d.ListRbDeviceParams(ctx, cond)
	if err != nil {
		t.Fatal(err)
	}

	fmt.Println(ucmd.ToJsonStrIgnoreErr(res))
}
func TestDevice_GetDeviceElectricityPrice(t *testing.T) {
	d := Device{}

	tests := []struct {
		name    string
		cond    DeviceElectricityPriceCond
		wantErr bool
	}{
		{
			name: "valid device and date",
			cond: DeviceElectricityPriceCond{
				DeviceId: "PS-NIO-efd6e196-0fb4a06c",
				Day:      time.Now().Format("2006-01-02"),
			},
			wantErr: false,
		},
		{
			name: "non-existent device",
			cond: DeviceElectricityPriceCond{
				DeviceId: "non-existent-device-id",
				Day:      time.Now().Format("2006-01-02"),
			},
			wantErr: false, // Function returns empty result but no error for non-existent devices
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			res, err := d.GetDeviceElectricityPrice(ctx, tt.cond)
			if (err != nil) != tt.wantErr {
				t.Errorf("Device.GetDeviceElectricityPrice() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			fmt.Println(ucmd.ToJsonStrIgnoreErr(res))
		})
	}
}

func TestDevice_GetDeviceElectricityPriceHalfHour(t *testing.T) {
	d := Device{}

	tests := []struct {
		name    string
		cond    DeviceElectricityPriceCond
		wantErr bool
	}{
		{
			name: "valid device and date",
			cond: DeviceElectricityPriceCond{
				DeviceId: "PS-NIO-09396963-9c33bc34",
				Day:      time.Now().Format("2006-01-02"),
			},
			wantErr: false,
		},
		{
			name: "non-existent device",
			cond: DeviceElectricityPriceCond{
				DeviceId: "non-existent-device-id",
				Day:      time.Now().Format("2006-01-02"),
			},
			wantErr: false, // Function returns empty result but no error for non-existent devices
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			res, err := d.GetDeviceElectricityPriceHalfHour(ctx, tt.cond)
			if (err != nil) != tt.wantErr {
				t.Errorf("Device.GetDeviceElectricityPriceHalfHour() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			fmt.Println(ucmd.ToJsonStrIgnoreErr(res))
		})
	}
}