package device

const (
	CollectionDeviceBlacklist        = "device_blacklist"
	CollectionDeviceBlacklistHistory = "device_blacklist_history"
	CollectionDeviceElectricityPrice = "device_electricity_price"

	BlacklistTypeVersionWorksheet = "version_worksheet_blacklist"
)

type BatteryInfo struct {
	SlotId         int     `json:"slot_id"`
	BatteryId      string  `json:"battery_id"`
	BatteryType    int     `json:"battery_type"`
	BatteryUserSoc float64 `json:"battery_user_soc"`
}

type SlotBatteryField string

const (
	SlotBatteryId      SlotBatteryField = "BatteryId"
	SlotBatteryType    SlotBatteryField = "BatteryType"
	SlotBatteryUserSoc SlotBatteryField = "BatteryUserSoc"
)

// FCRDReserveDevices 电力交易设备
var FCRDReserveDevices = map[string]struct{}{
	"PS-NIO-6ab07094-94563340": {},
}

type RbDeviceParam struct {
	Project  string
	DeviceId string
	Params   map[int64]any
}

type DeviceElectricityPrice struct {
	DeviceId    string
	Project     string
	ResourceId  string
	PriceDetail map[int]PriceDetail // 小时-价格
}

type DeviceElectricityPriceHalfHour struct {
	DeviceId    string
	Project     string
	ResourceId  string
	PriceDetail map[string]PriceDetail // 半小时-价格，key示例："00:30"
}

type PriceDetail struct {
	PrictTag string
	Price    float64
}
