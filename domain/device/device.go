package device

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/gomodule/redigo/redis"
	larkcard "github.com/larksuite/oapi-sdk-go/v3/card"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	udao "git.nevint.com/golang-libs/common-utils/dao"
	"git.nevint.com/golang-libs/common-utils/larkservice"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	"git.nevint.com/welkin2/welkin-backend/domain/common"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"
	"git.nevint.com/welkin2/welkin-backend/util"
)

type Device struct {
	Project  string
	DeviceId string
}

// GetReserveBattery 欧洲电力交易，获取预留电池数
func (d *Device) GetReserveBattery() (res map[int32]int, err error) {
	res = make(map[int32]int)
	colName := fmt.Sprintf("%s-%s", umw.PowerParamsRecord, strings.ToLower(d.Project))
	filter := bson.D{
		{"type", "reserve"},
		{"details", bson.M{"$elemMatch": bson.M{"device_id": d.DeviceId, "err_code": 4}}},
	}
	opts := options.Find().SetSort(bson.D{{"create_time", -1}}).SetLimit(1)
	var records []umw.MongoPowerParamsRecord
	_, err = client.GetWatcher().Mongodb().NewMongoEntry(filter).FindMany(umw.FCRDManagement, colName, opts, &records)
	if err != nil {
		log.Logger.Errorf("GetReserveBattery, fail to find params record, err: %v, filter: %s", err, ucmd.ToJsonStrIgnoreErr(filter))
		return
	}
	if len(records) == 0 {
		log.Logger.Errorf("GetReserveBattery, params record is empty, filter: %s", ucmd.ToJsonStrIgnoreErr(filter))
		return res, fmt.Errorf("params record is empty")
	}
	//fmt.Println(ucmd.ToJsonStrIgnoreErr(records[0]))
	for batteryType, count := range records[0].ReservedBatteries {
		batteryUserType, _ := strconv.Atoi(batteryType)
		res[int32(batteryUserType)] = int(count)
	}
	return
}

// GetSlotBatteryInfo 获取ts时刻，所有电池仓中的电池信息
func (d *Device) GetSlotBatteryInfo(ts int64) (res map[int]BatteryInfo, err error) {
	startTime := ts - time.Minute.Milliseconds()*10
	res = make(map[int]BatteryInfo)
	dataIdList := GetBatterySlotFieldDataIds(d.Project, SlotBatteryId, SlotBatteryType, SlotBatteryUserSoc)
	dataIds := strings.Join(dataIdList, ",")
	tdw := &client.TDWatcher{
		TDClient:     client.GetWatcher().TDEngine(),
		RedisClient:  client.GetWatcher().Redis(),
		DeviceId:     d.DeviceId,
		StartTs:      &startTime,
		EndTs:        &ts,
		Limit:        1,
		FilterFields: make([]string, 0),
		Descending:   true,
		Logger:       log.Logger.Named("TDEngine"),
	}
	stbName := fmt.Sprintf("realtime_%s", strings.ToLower(d.Project))
	scanStruct, dataIdMap, err := tdw.GetRealtimeFields("device2oss_realtime", stbName, dataIds, true)
	if err != nil {
		log.Logger.Errorf("GetSlotBatteryInfo, get realtime fields fail, err: %v", err)
		return
	}
	_, rows, err := tdw.FilterDataByFields("device2oss_realtime")
	if err != nil {
		log.Logger.Errorf("GetSlotBatteryInfo, get realtime data by fields fail: %v, err: %v", tdw.FilterFields, err)
		return
	}
	if rows != nil {
		for rows.Next() {
			// 获取动态的查询结构体
			columns := client.ReflectFields(scanStruct)
			if err = rows.Scan(columns...); err != nil {
				log.Logger.Errorf("GetSlotBatteryInfo, scan realtime data from tdengine fail, err: %v", err)
				continue
			}
			for dataId := range dataIdMap {
				val := scanStruct.FieldByName("R" + dataId).Interface().(model.SafeType).GetRealValue()
				slotInfo, ok := ConvertBatteryDataId(d.Project, dataId)
				if !ok {
					log.Logger.Errorf("GetSlotBatteryInfo, fail to convert data id: %s, project: %s", dataId, d.Project)
					continue
				}
				// 电池类型，二三代站上传的int类型不一致，将类型统一转为int
				if slotInfo.Field == SlotBatteryType {
					val = util.ParseInt(val)
				}
				// 取出对应仓位的电池信息，用于将当前data_id的值写入对应字段
				// 若第一次取，会生成一个空的新结构体
				batteryInfo := res[slotInfo.SlotId]
				if err = util.SetField(&batteryInfo, string(slotInfo.Field), val); err != nil {
					log.Logger.Errorf("GetSlotBatteryInfo, fail to set battery info field %s, err: %v, value: %v, type: %T", slotInfo.Field, err, val, val)
					continue
				}
				res[slotInfo.SlotId] = batteryInfo
			}
		}
	}
	for slot, item := range res {
		item.SlotId = slot
		res[slot] = item
	}
	return
}

// CheckSlotBatterySoc 判断满电电池数是否满足预留电池数要求
func (d *Device) CheckSlotBatterySoc() error {
	now := time.Now()
	batteryInfo, err := d.GetSlotBatteryInfo(now.UnixMilli())
	if err != nil {
		log.Logger.Errorf("CheckSlotBatterySoc, fail to get slot battery info, err: %v", err)
		return err
	}
	// 当前站内各种电池的满电数量
	batteryCountMap := make(map[int32]int)
	for _, record := range batteryInfo {
		if record.BatteryUserSoc >= 90 {
			batteryType := int32(record.BatteryType)
			batteryUserType := common.ConvertBatteryUserType(&batteryType)
			if batteryUserType != nil {
				batteryCountMap[*batteryUserType]++
			}
		}
	}
	// 期望预留电池的数量
	reserveBattery, err := d.GetReserveBattery()
	if err != nil {
		log.Logger.Errorf("CheckSlotBatterySoc, fail to get reserve battery, err: %v", err)
		return err
	}
	log.Logger.Infof("CheckSlotBatterySoc, current battery info: %s, battery count: %s, reserve battery: %s", ucmd.ToJsonStrIgnoreErr(batteryInfo), ucmd.ToJsonStrIgnoreErr(batteryCountMap), ucmd.ToJsonStrIgnoreErr(reserveBattery))

	batteryNotMatch := make(map[int32]int)
	for batteryUserType, count := range reserveBattery {
		batteryCount := batteryCountMap[batteryUserType]
		// reserveBattery中的70度电池对应站内的70度和75度电池
		if batteryUserType == 70 {
			batteryCount += batteryCountMap[75]
		}
		if count > batteryCount {
			batteryNotMatch[batteryUserType] = batteryCount
		}
	}
	// 通过redis判断是否满90分钟
	conn := udao.NewRedisConn(client.GetWatcher().Redis())
	defer conn.Close()
	key := fmt.Sprintf("fcrd/reserve-battery/%s", d.DeviceId)
	if len(batteryNotMatch) == 0 {
		// 仓内电池满足预留数量，将时间刷新
		if _, err = conn.Do("SETEX", key, 3600*24*7, now.UnixMilli()); err != nil {
			log.Logger.Errorf("CheckSlotBatterySoc, fail to setex redis key: %s, err: %v", key, err)
			return err
		}
		return nil
	}
	// 满电电池不满足预留电池数
	var ts int64
	ts, err = redis.Int64(conn.Do("GET", key))
	if errors.Is(err, redis.ErrNil) {
		// 若redis中不存在该key，则新建一个
		_, err = conn.Do("SETEX", key, 3600*24*7, now.UnixMilli())
		if err != nil {
			log.Logger.Errorf("CheckSlotBatterySoc, fail to setex redis key: %s, err: %v", key, err)
			return err
		}
		ts = now.UnixMilli()
	} else if err != nil {
		log.Logger.Errorf("CheckSlotBatterySoc, fail to get redis key: %s, err: %v", key, err)
		return err
	}
	// 若计数超过90分钟，每隔30分钟发送一次告警
	delta := now.Sub(time.UnixMilli(ts))
	if delta >= 90*time.Minute && int(delta.Minutes())%30 == 0 {
		ic := larkservice.NewInfoCard()
		ic.HeaderName = "电力交易异常告警"
		ic.HeaderColor = larkcard.TemplateRed
		ic.AppendElement(larkservice.ElementKeyVal, []larkservice.KeyValMessage{
			{Key: "设备ID：", Val: d.DeviceId},
			{Key: "告警时间：", Val: now.Format("2006-01-02 15:04:05")},
		})
		var details []string
		for b, cnt := range batteryNotMatch {
			details = append(details, fmt.Sprintf("电池类型：%dkWh，预留数量：%d，实际满电电池数量：%d", b, reserveBattery[b], cnt))
		}
		ic.AppendElement(larkservice.ElementDetail, []larkservice.DetailMessage{
			{Msg: fmt.Sprintf("**满电电池数量不足**：\n%s", strings.Join(details, "\n"))},
		})
		cardContent, cardErr := ic.Build()
		if cardErr != nil {
			log.Logger.Errorf("CheckSlotBatterySoc, make fcrd card err: %v", cardErr)
			return cardErr
		}
		receiver := larkservice.Receiver{
			Type:       larkim.ReceiveIdTypeEmail,
			ReceiveIds: config.Cfg.CardBot.Receivers["fcrdAlert"],
		}
		if err = larkservice.SendCard(cardContent, receiver); err != nil {
			log.Logger.Errorf("CheckSlotBatterySoc, send fcrd card err: %v", err)
			return err
		}
	}
	return nil
}

// GetCurrentLoginUser 获取设备当前登录MPC的用户
func (d *Device) GetCurrentLoginUser(c context.Context) (user umw.MongoUserInfo, err error) {
	err = client.GetWatcher().Mongodb().NewMongoEntry(bson.D{{"device_id", d.DeviceId}, {"logout_time", 0}}).FindOne(umw.OAuthDB, umw.LoginHistory, options.FindOne().SetSort(bson.M{"login_time": -1}), &user)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			log.CtxLog(c).Warnf("no user log in device: %s", ucmd.ToJsonStrIgnoreErr(c))
			err = nil
		} else {
			log.CtxLog(c).Errorf("fail to find user: %v, device: %s, project: %s", err, d.DeviceId, d.Project)
			return
		}
	}
	return
}

// CountDevices 获取运营设备数量
func (d *Device) CountDevices(c context.Context) (map[string]int64, error) {
	deviceCount := make(map[string]int64)
	pipeline := mongo.Pipeline{
		bson.D{{"$match", bson.M{"is_active": true}}},
		bson.D{{"$group", bson.M{"_id": "$project", "total": bson.M{"$sum": 1}}}},
	}
	var res []bson.M
	err := client.GetWatcher().Mongodb().NewMongoEntry().Aggregate(umw.OAuthDB, umw.DeviceBaseInfo, pipeline, &res)
	if err != nil {
		log.CtxLog(c).Errorf("fail to count devices, err: %v, pipeline: %s", err, ucmd.ToJsonStrIgnoreErr(pipeline))
		return nil, err
	}
	if len(res) == 0 {
		return nil, errors.New("no device found")
	}
	for _, item := range res {
		if item["_id"] == nil || item["_id"] == "" {
			continue
		}
		project := item["_id"].(string)
		total := util.ParseInt(item["total"])
		deviceCount[project] = int64(total)
	}
	return deviceCount, nil
}

// GetDeviceVersionHeader 获取赤兔版本号筛选条件
func (d *Device) GetDeviceVersionHeader(ctx context.Context) (res []model.FilterOption, err error) {
	projection := bson.M{
		"name":                 0,
		"_id":                  0,
		"blacklist":            0,
		"create_ts":            0,
		"ip":                   0,
		"update_ts":            0,
		"PLM":                  0,
		"release_version":      0,
		"deployment":           0,
		"update_status":        0,
		"address":              0,
		"area":                 0,
		"city":                 0,
		"city_company":         0,
		"device_supervisor":    0,
		"official_online_time": 0,
		"region":               0,
		"service_state":        0,
		"params":               0,
		"params_deployment":    0,
		"params_queue":         0,
		"params_update_status": 0,
		"station_params":       0,
		"load_total_state":     0,
		"load_total_state_ts":  0,
		"resource_id":          0,
		"city_company_group":   0,
		"construction":         0,
		"blacklist_history":    0,
		"ecc_exist":            0,
		"charge_station_id":    0,
		"env":                  0,
		"hardware_version":     0,
		"deploy_list":          0,
		"need_cancel":          0,
	}
	opts := options.FindOne().SetProjection(projection)
	result := make(map[string]interface{})
	err = client.GetWatcher().RbMongodb().NewMongoEntry(bson.D{{"is_active", true}, {"online", true}}).FindOne(d.Project, "devices", opts, &result)
	if err != nil {
		log.CtxLog(ctx).Errorf("GetDeviceVersionFilter, fail to get device version filter, err: %v, project: %s", err, d.Project)
		return
	}
	res = []model.FilterOption{
		{
			Key: "in_blacklist", Type: model.FrontendElementRadio,
		},
		{
			Key: "is_active", Type: model.FrontendElementRadio,
		},
		{
			Key: "online", Type: model.FrontendElementRadio,
		},
		// 二代站没有PLM字段
		{
			Key: "PLM", Type: model.FrontendElementInput,
		},
	}
	mongoFields := make([]model.FilterOption, 0)
	for k := range result {
		if k == "is_active" || k == "online" {
			continue
		}
		mongoFields = append(mongoFields, model.FilterOption{
			Key: k, Type: model.FrontendElementInput,
		})
	}
	sort.Slice(mongoFields, func(i, j int) bool {
		return mongoFields[i].Key < mongoFields[j].Key
	})
	res = append(res, mongoFields...)
	return
}

// ListDeviceVersion 获取赤兔版本号
func (d *Device) ListDeviceVersion(ctx context.Context, filter map[string]interface{}) (res []map[string]interface{}, err error) {
	delete(filter, "download")
	// 获取黑名单设备
	blacklistBool := true
	blacklist, err := d.ListDeviceBlacklist(ctx, DeviceBlacklistCond{Project: d.Project, BlacklistType: BlacklistTypeVersionWorksheet, Blacklist: &blacklistBool})
	if err != nil {
		log.CtxLog(ctx).Errorf("ListDeviceVersion, fail to get device blacklist, err: %v, project: %s", err, d.Project)
		return
	}
	blacklistDevices := make([]string, 0)
	blacklistDeviceMap := make(map[string]bool)
	for _, item := range blacklist {
		blacklistDevices = append(blacklistDevices, item.DeviceId)
		blacklistDeviceMap[item.DeviceId] = true
	}
	if val, found := filter["in_blacklist"]; found {
		delete(filter, "in_blacklist")
		inBlacklist, _ := val.(bool)
		if inBlacklist {
			filter["_id"] = bson.M{"$in": blacklistDevices}
		} else {
			filter["_id"] = bson.M{"$nin": blacklistDevices}
		}
	}
	if val, found := filter["PLM"]; found {
		filter["release_version"] = val
		delete(filter, "PLM")
	}
	// 所有版本号模糊查找
	for field, val := range filter {
		if field == "_id" || field == "online" || field == "is_active" || field == "PLM_list" {
			continue
		}
		filter[field] = bson.M{"$regex": fmt.Sprintf("%v", val)}
	}
	// PLM版本号精确查找
	if val, found := filter["PLM_list"]; found {
		delete(filter, "PLM_list")
		filter["release_version"] = bson.M{"$in": val}
	}
	if ucmd.GetEnv() != "prod" {
		if _, found := filter["release_version"]; !found {
			filter["release_version"] = bson.M{"$exists": true}
		}
		filter["name"] = bson.M{"$exists": true}
	}
	pipeline := mongo.Pipeline{
		{{"$match", filter}},
		{{"$addFields", bson.M{"device_id": "$_id", "description": "$name", "PLM": "$release_version"}}},
		{{"$project", bson.M{
			"name":                 0,
			"_id":                  0,
			"blacklist":            0,
			"create_ts":            0,
			"ip":                   0,
			"update_ts":            0,
			"release_version":      0,
			"deployment":           0,
			"update_status":        0,
			"address":              0,
			"area":                 0,
			"city":                 0,
			"city_company":         0,
			"device_supervisor":    0,
			"official_online_time": 0,
			"region":               0,
			"service_state":        0,
			"params":               0,
			"params_deployment":    0,
			"params_queue":         0,
			"params_update_status": 0,
			"station_params":       0,
			"load_total_state":     0,
			"load_total_state_ts":  0,
			"resource_id":          0,
			"city_company_group":   0,
			"construction":         0,
			"blacklist_history":    0,
			"ecc_exist":            0,
			"charge_station_id":    0,
			"env":                  0,
			"hardware_version":     0,
			"deploy_list":          0,
			"need_cancel":          0,
		}}},
	}
	err = client.GetWatcher().RbMongodb().NewMongoEntry().Aggregate(d.Project, "devices", pipeline, &res)
	for i := range res {
		res[i]["in_blacklist"] = false
		deviceId := res[i]["device_id"].(string)
		if blacklistDeviceMap[deviceId] {
			res[i]["in_blacklist"] = true
		}
	}
	return
}

type DeviceBlacklistCond struct {
	Devices       []string
	Project       string
	BlacklistType string
	Operator      string
	Blacklist     *bool
}

// UpdateDeviceBlacklist 变更设备黑名单
func (d *Device) UpdateDeviceBlacklist(ctx context.Context, cond DeviceBlacklistCond) error {
	blacklist := false
	if cond.Blacklist != nil {
		blacklist = *cond.Blacklist
	}
	var models []mongo.WriteModel
	for _, deviceId := range cond.Devices {
		filter := bson.D{{"device_id", deviceId}, {"project", cond.Project}}
		update := bson.M{"$set": bson.M{cond.BlacklistType: blacklist}}
		models = append(models, mongo.NewUpdateOneModel().
			SetFilter(filter).
			SetUpdate(update).
			SetUpsert(true))
	}
	err := client.GetWatcher().Mongodb().NewMongoEntry().UpdateManyBulk(umw.OAuthDB, CollectionDeviceBlacklist, models, client.IndexOption{
		Name:   "project_deviceId_unique",
		Fields: bson.D{{"project", 1}, {"device_id", 1}},
		Unique: true,
	})
	if err != nil {
		log.CtxLog(ctx).Errorf("UpdateDeviceBlacklist, fail to change device blacklist, err: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return err
	}
	insert := bson.M{
		"project":        cond.Project,
		"devices":        cond.Devices,
		"operator":       cond.Operator,
		"blacklist_type": cond.BlacklistType,
		"blacklist":      blacklist,
		"operate_ts":     time.Now().UnixMilli(),
	}
	err = client.GetWatcher().Mongodb().NewMongoEntry().AppendOne(umw.OAuthDB, CollectionDeviceBlacklistHistory, insert)
	if err != nil {
		log.CtxLog(ctx).Errorf("UpdateDeviceBlacklist, fail to insert device blacklist history, err: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return err
	}
	return nil
}

func (d *Device) ListDeviceBlacklist(ctx context.Context, cond DeviceBlacklistCond) (res []mmgo.DeviceBlacklist, err error) {
	filter := bson.D{{"project", cond.Project}, {cond.BlacklistType, bson.M{"$exists": true}}}
	if len(cond.Devices) != 0 {
		filter = append(filter, bson.E{Key: "device_id", Value: bson.M{"$in": cond.Devices}})
	}
	if cond.Blacklist != nil {
		filter = append(filter, bson.E{Key: cond.BlacklistType, Value: *cond.Blacklist})
	}
	_, err = client.GetWatcher().Mongodb().NewMongoEntry(filter).FindMany(umw.OAuthDB, CollectionDeviceBlacklist, nil, &res)
	if err != nil {
		log.CtxLog(ctx).Errorf("ListDeviceBlacklist, fail to list device blacklist, err: %v, project: %s", err, d.Project)
	}
	return
}

// ListBidirectionalDevices 查询双向设备列表
func (d *Device) ListBidirectionalDevices(ctx context.Context, project string) (res []model.DeviceInfoVO, err error) {
	// 三代站：馈网功能开关为1的站是双向站
	// 四代站：充电柜的类型为20的站是双向站
	var filter bson.D
	if project == umw.PUS3 {
		filter = bson.D{
			{"is_active", true},
			{"params", bson.M{"$elemMatch": bson.M{"key": 902200, "value": 1}}},
		}
	} else if project == umw.PUS4 {
		filter = bson.D{
			{"is_active", true},
			{"params", bson.M{"$elemMatch": bson.M{"key": 972044, "value": 20}}},
		}
	} else {
		return nil, fmt.Errorf("project %s not supported", project)
	}

	var results []struct {
		DeviceId string `json:"_id" bson:"_id"`
	}
	_, err = client.GetWatcher().RbMongodb().NewMongoEntry(filter).FindMany(project, "devices", nil, &results)
	if err != nil {
		log.CtxLog(ctx).Errorf("ListBidirectionalDevices, fail to list bidirectional devices, err: %v, project: %s", err, project)
		return
	}
	for _, record := range results {
		deviceInfo, _ := cache.PowerSwapCache.GetSingleDevice(record.DeviceId)
		if deviceInfo != nil {
			res = append(res, model.DeviceInfoVO{
				DeviceId:    record.DeviceId,
				Description: deviceInfo.Description,
				ResourceId:  deviceInfo.ResourceId,
				Project:     project,
				CityCompany: deviceInfo.CityCompany,
				Area:        deviceInfo.Area,
			})
		}
	}
	return
}

type RbDeviceParamsCond struct {
	Project   string
	Devices   []string
	ParamKeys []int64
}

// ListRbDeviceParams 获取设备的赤兔参数信息
func (d *Device) ListRbDeviceParams(ctx context.Context, cond RbDeviceParamsCond) (res []RbDeviceParam, err error) {
	var results []mmgo.RbDeviceInfo
	pipeline := mongo.Pipeline{
		bson.D{{
			"$match", bson.M{
				"_id": bson.M{"$in": cond.Devices},
			},
		}},
		bson.D{{
			"$unwind", "$params",
		}},
		bson.D{{
			"$match", bson.M{
				"params.key": bson.M{
					"$in": cond.ParamKeys,
				},
			},
		}},
		bson.D{{
			"$group", bson.M{
				"_id":    "$_id",
				"params": bson.M{"$push": "$params"},
			},
		}},
	}
	err = client.GetWatcher().RbMongodb().NewMongoEntry().Aggregate(cond.Project, "devices", pipeline, &results)
	if err != nil {
		log.CtxLog(ctx).Errorf("ListRbDeviceParams, fail to list rb device params, err: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	for _, record := range results {
		res = append(res, convertRbDeviceParamPO2DO(cond.Project, record))
	}
	return
}

// GetDeviceTemperature 查询设备所在地区的天气温度
func (d *Device) GetDeviceTemperature(ctx context.Context, deviceId string, days []string) (res map[string]common.WeatherTemperatureInfo, err error) {
	res = make(map[string]common.WeatherTemperatureInfo)
	// 获取设备所在地区
	deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(deviceId)
	if !found {
		log.CtxLog(ctx).Errorf("device not found: %s", deviceId)
		return nil, fmt.Errorf("device not found: %s", deviceId)
	}
	regionId := deviceInfo.RegionId
	if regionId == "" {
		log.CtxLog(ctx).Errorf("device regionId is empty: %s", deviceId)
		return nil, fmt.Errorf("device regionId is empty: %s", deviceId)
	}
	// 查询天气温度
	cond := common.FindWeatherTemperatureByRegionCond{
		Days:     days,
		RegionId: regionId,
	}
	result, err := common.FindWeatherTemperatureByRegion(ctx, cond)
	if err != nil {
		log.CtxLog(ctx).Errorf("fail to find weather temperature by region: %v", err)
		return nil, err
	}
	for _, record := range result {
		res[record.Day] = record
	}
	return
}

type DeviceElectricityPriceCond struct {
	DeviceId string
	Day      string // 2025-03-24
}

func (d *Device) GetDeviceElectricityPriceTag(ctx context.Context, cond DeviceElectricityPriceCond) (res []mmgo.DeviceElectricityPrice, err error) {
	deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(cond.DeviceId)
	if !found {
		log.CtxLog(ctx).Errorf("device not found: %s", cond.DeviceId)
		return
	}
	month := cond.Day[:7]
	filter := bson.D{
		{"resource_id", deviceInfo.ResourceId},
		{"month", month},
		{"start_day", bson.M{"$lte": cond.Day}},
		{"end_day", bson.M{"$gte": cond.Day}},
	}
	_, err = client.GetWatcher().Mongodb().NewMongoEntry(filter).FindMany(umw.OAuthDB, CollectionDeviceElectricityPrice, nil, &res)
	if err != nil {
		log.CtxLog(ctx).Errorf("GetDeviceElectricityPrice, fail to get device electricity price, err: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	return
}

// GetDeviceElectricityPrice 获取设备电价信息
func (d *Device) GetDeviceElectricityPrice(ctx context.Context, cond DeviceElectricityPriceCond) (res DeviceElectricityPrice, err error) {
	deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(cond.DeviceId)
	if !found {
		log.CtxLog(ctx).Errorf("device not found: %s", cond.DeviceId)
		return
	}
	res = DeviceElectricityPrice{
		DeviceId:    cond.DeviceId,
		ResourceId:  deviceInfo.ResourceId,
		Project:     deviceInfo.Project,
		PriceDetail: make(map[int]PriceDetail),
	}
	result, err := d.GetDeviceElectricityPriceTag(ctx, cond)
	if err != nil {
		log.CtxLog(ctx).Errorf("GetDeviceElectricityPrice, fail to get device electricity price, err: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	if len(result) == 0 {
		log.CtxLog(ctx).Warnf("GetDeviceElectricityPrice, no electricity price found, cond: %s", ucmd.ToJsonStrIgnoreErr(cond))
		// todo: 若没获取到电价，则取最近的有数据的日期的电价
		return
	}
	for _, record := range result {
		// 数仓中，即使电价模型为one_price，也会保存之前的different_price电价，因此需要先判断电价模型
		if record.ElectricityPriceModel == "one_price" {
			onePrice := record.OnePricePrice
			if onePrice == 0 {
				onePrice = 0.8
			}
			for hour := range 24 {
				res.PriceDetail[hour] = PriceDetail{
					Price:    onePrice,
					PrictTag: "valley",
				}
			}
			break
		}
		for hour := record.StartHour; hour < record.EndHour; hour++ {
			res.PriceDetail[hour] = PriceDetail{
				Price:    record.Price,
				PrictTag: record.PriceTag,
			}
		}
	}
	return
}

// GetDeviceElectricityPriceHalfHour 获取设备半小时粒度电价信息
func (d *Device) GetDeviceElectricityPriceHalfHour(ctx context.Context, cond DeviceElectricityPriceCond) (res DeviceElectricityPriceHalfHour, err error) {
	deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(cond.DeviceId)
	if !found {
		log.CtxLog(ctx).Errorf("device not found: %s", cond.DeviceId)
		return
	}
	res = DeviceElectricityPriceHalfHour{
		DeviceId:    cond.DeviceId,
		ResourceId:  deviceInfo.ResourceId,
		Project:     deviceInfo.Project,
		PriceDetail: make(map[string]PriceDetail),
	}
	result, err := d.GetDeviceElectricityPriceTag(ctx, cond)
	if err != nil {
		log.CtxLog(ctx).Errorf("GetDeviceElectricityPrice, fail to get device electricity price, err: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	if len(result) == 0 {
		log.CtxLog(ctx).Warnf("GetDeviceElectricityPrice, no electricity price found, cond: %s", ucmd.ToJsonStrIgnoreErr(cond))
		// todo: 若没获取到电价，则取最近的有数据的日期的电价
		return
	}
	for _, record := range result {
		// 数仓中，即使电价模型为one_price，也会保存之前的different_price电价，因此需要先判断电价模型
		if record.ElectricityPriceModel == "one_price" {
			onePrice := record.OnePricePrice
			if onePrice == 0 {
				onePrice = 0.8
			}
			for _, interval := range util.GetHalfHourIntervals(ctx, "00:00", "24:00") {
				res.PriceDetail[interval] = PriceDetail{
					Price:    onePrice,
					PrictTag: "valley",
				}
			}
			break
		}
		for _, hour := range util.GetHalfHourIntervals(ctx, record.Start, record.End) {
			res.PriceDetail[hour] = PriceDetail{
				Price:    record.Price,
				PrictTag: record.PriceTag,
			}
		}
	}
	return
}
