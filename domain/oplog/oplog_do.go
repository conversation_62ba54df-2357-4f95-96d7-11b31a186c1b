package oplog

import (
	"context"
	"fmt"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	"git.nevint.com/welkin2/welkin-backend/client"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	"git.nevint.com/welkin2/welkin-backend/util"
)

const (
	CollectionOperationTable = "operation_table"

	TypeMPCOperation   = 1 // MPC操作日志
	TypeCloudOperation = 2 // 云端操作日志
)

type MPCOpLogDO struct {
	Timestamp int64
	Action    string
	Page      string
	Button    string
	Args      string
	Remark    string
	UserId    string
}

type ListMPCOpLogCond struct {
	Project              string
	DeviceId             string
	StartTime            int64
	EndTime              int64
	OperationInterface   *string
	OperationDescription *string
	Operator             *string
	model.CommonCond
}

func (o *MPCOpLogDO) ListMPCOpLog(ctx context.Context, cond ListMPCOpLogCond) (opLogs []MPCOpLogDO, total int64, err error) {
	if cond.Project == "" || cond.DeviceId == "" || cond.StartTime == 0 || cond.EndTime == 0 {
		err = fmt.Errorf("invalid params, cond: %s", ucmd.ToJsonStrIgnoreErr(cond))
		log.CtxLog(ctx).Errorf("ListMPCOpLog, %v", err)
		return
	}
	dbName := fmt.Sprintf("op-log-%s", ucmd.RenameProjectDB(cond.Project))
	opts := options.Find().SetSort(bson.M{"timestamp": -1})
	if cond.Page != 0 && cond.Size != 0 {
		opts = opts.SetSkip((cond.Page - 1) * cond.Size).SetLimit(cond.Size)
	}
	// MPC操作日志，二代站和三代站协议不通
	if cond.Project == umw.PowerSwap2 {
		// 二代站MPC操作日志
		filter := bson.D{
			util.SelectedTimeDuration("timestamp", cond.StartTime, cond.EndTime),
			{"type", bson.M{"$exists": false}},
		}
		if cond.Operator != nil {
			filter = append(filter, bson.E{Key: "user", Value: *cond.Operator})
		}
		if cond.OperationDescription != nil {
			filter = append(filter, bson.E{Key: "button", Value: bson.M{"$regex": *cond.OperationDescription}})
		}
		if cond.OperationInterface != nil {
			filter = append(filter, bson.E{Key: "page", Value: bson.M{"$regex": *cond.OperationInterface}})
		}
		var res []umw.MongoOperationLog
		total, err = client.GetWatcher().Mongodb().NewMongoEntry(filter).FindMany(dbName, cond.DeviceId, opts, &res)
		if err != nil {
			log.CtxLog(ctx).Errorf("ListMPCOpLog, fail to find op logs: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
			return
		}
		for _, record := range res {
			opLogs = append(opLogs, convertPS2MpcOperation2DO(record))
		}
	} else {
		// 三代站MPC操作日志
		filter := bson.D{
			{"timestamp", bson.M{"$gte": cond.StartTime, "$lte": cond.EndTime}},
			{"type", TypeMPCOperation},
		}
		filter = append(filter, getCustomOperationFilter(ctx, cond)...)
		var res []umw.MongoOperationLogV2
		total, err = client.GetWatcher().Mongodb().NewMongoEntry(filter).FindMany(dbName, cond.DeviceId, opts, &res)
		if err != nil {
			log.CtxLog(ctx).Errorf("ListMPCOpLog, fail to find op logs: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
			return
		}
		var operationTable []model.MPCOperationTable
		_, err = client.GetWatcher().Mongodb().NewMongoEntry(bson.D{{"type", TypeMPCOperation}}).FindMany(dbName, CollectionOperationTable, options.Find(), &operationTable)
		if err != nil {
			log.CtxLog(ctx).Errorf("ListMPCOpLog, fail to list operation table, err: %v, db: %s, cond: %s", err, dbName, ucmd.ToJsonStrIgnoreErr(cond))
			return
		}
		operationMap := make(map[int64]model.MPCOperationTable)
		for _, op := range operationTable {
			operationMap[op.Operation] = op
		}
		for _, record := range res {
			opLogs = append(opLogs, convertPUSMpcOperation2DO(operationMap, record))
		}
	}
	return
}

// 获取MPC操作日志查询的过滤条件（三代站+）
func getCustomOperationFilter(c context.Context, cond ListMPCOpLogCond) (res bson.D) {
	dbName := fmt.Sprintf("%s-%s", umw.OperationLog, ucmd.RenameProjectDB(cond.Project))
	filter := bson.D{
		{"type", TypeMPCOperation},
	}
	needSearch := false
	// todo:国际化
	if cond.OperationDescription != nil {
		filter = append(filter, bson.E{Key: "button_cn_name", Value: bson.M{"$regex": *cond.OperationDescription}})
		needSearch = true
	}
	if cond.OperationInterface != nil {
		filter = append(filter, bson.E{Key: "page_cn_name", Value: bson.M{"$regex": *cond.OperationInterface}})
		needSearch = true
	}
	opList := []int64{-1}
	if needSearch {
		var opTable []model.MPCOperationTable
		_, err := client.GetWatcher().Mongodb().NewMongoEntry(filter).FindMany(dbName, CollectionOperationTable, options.Find(), &opTable)
		if err != nil {
			log.CtxLog(c).Errorf("get operation log, fail to list operation table, err: %v, db: %s, request: %s", err, dbName, ucmd.ToJsonStrIgnoreErr(cond))
			return
		}
		for _, op := range opTable {
			opList = append(opList, op.Operation)
		}
		res = append(res, bson.E{Key: "operation", Value: bson.M{"$in": opList}})
	}

	if cond.Operator != nil {
		res = append(res, bson.E{Key: "user_id", Value: *cond.Operator})
	}
	return
}

type CloudOpLogDO struct {
	Timestamp     int64
	AbilityCode   string
	AbilityParams []ParamData
	IsFail        bool
	FailureReason string
}

type ParamData struct {
	ParamCode  string
	ParamValue interface{}
}

type ListCloudOpLogCond struct {
	Project   string
	DeviceId  string
	StartTime int64
	EndTime   int64
	model.CommonCond
}

func (o *CloudOpLogDO) ListCloudOpLog(ctx context.Context, cond ListCloudOpLogCond) (opLogs []CloudOpLogDO, total int64, err error) {
	if cond.Project == "" || cond.DeviceId == "" || cond.StartTime == 0 || cond.EndTime == 0 {
		err = fmt.Errorf("invalid params, cond: %s", ucmd.ToJsonStrIgnoreErr(cond))
		log.CtxLog(ctx).Errorf("ListCloudOpLog, %v", err)
		return
	}
	dbName := fmt.Sprintf("op-log-%s", ucmd.RenameProjectDB(cond.Project))
	opts := options.Find().SetSort(bson.M{"timestamp": -1})
	if cond.Page != 0 && cond.Size != 0 {
		opts = opts.SetSkip((cond.Page - 1) * cond.Size).SetLimit(cond.Size)
	}
	// 云端操作日志（二三四代站通用）
	filter := bson.D{
		{"timestamp", bson.M{"$gte": cond.StartTime, "$lte": cond.EndTime}},
		{"type", TypeCloudOperation},
	}
	var res []umw.MongoOSSOperationLog
	total, err = client.GetWatcher().Mongodb().NewMongoEntry(filter).FindMany(dbName, cond.DeviceId, opts, &res)
	if err != nil {
		log.CtxLog(ctx).Errorf("ListCloudOpLog, fail to find op logs: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	var operationTable []model.OSSOperationTable
	_, err = client.GetWatcher().Mongodb().NewMongoEntry(bson.D{{"type", TypeCloudOperation}}).FindMany(dbName, CollectionOperationTable, options.Find(), &operationTable)
	if err != nil {
		log.CtxLog(ctx).Errorf("ListCloudOpLog, fail to list operation table, err: %v, db: %s, cond: %s", err, dbName, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	operationMap := make(map[string]model.OSSOperationTable)
	for _, op := range operationTable {
		operationMap[op.Command] = op
	}
	for _, record := range res {
		opLogs = append(opLogs, convertCloudOperation2DO(operationMap, record))
	}
	return
}
