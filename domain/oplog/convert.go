package oplog

import (
	"fmt"
	"strings"

	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	"git.nevint.com/welkin2/welkin-backend/model"
	"git.nevint.com/welkin2/welkin-backend/util"
)

func convertPS2MpcOperation2DO(po umw.MongoOperationLog) MPCOpLogDO {
	return MPCOpLogDO{
		Timestamp: po.Timestamp,
		Action:    fmt.Sprintf("%d", po.Action), // todo: 该如何解析？
		Page:      po.Page,
		Button:    po.Button,
		Args:      po.Args,
		UserId:    po.User,
	}
}

func convertPUSMpcOperation2DO(operationMap map[int64]model.MPCOperationTable, po umw.MongoOperationLogV2) MPCOpLogDO {
	mpcOperation := MPCOpLogDO{
		Timestamp: po.Timestamp,
		Action:    operationMap[po.Operation].ActionCnName,
		Page:      operationMap[po.Operation].PageCnName,
		Button:    operationMap[po.Operation].ButtonCnName,
		UserId:    po.UserId,
		Remark:    operationMap[po.Operation].RemarksCnName,
	}
	if operationMap[po.Operation].ArgsCnName != "" {
		mpcOperation.Args = fmt.Sprintf(operationMap[po.Operation].ArgsCnName, util.ConvertStringArray(strings.Split(po.Args, ";"))...)
	}
	return mpcOperation
}

func convertCloudOperation2DO(operationMap map[string]model.OSSOperationTable, po umw.MongoOSSOperationLog) CloudOpLogDO {
	cloudOperation := CloudOpLogDO{
		Timestamp:     po.Timestamp,
		AbilityCode:   po.AbilityCode,
		IsFail:        po.IsFail,
		FailureReason: po.FailureReason,
	}
	for _, param := range po.AbilityParams {
		cloudOperation.AbilityParams = append(cloudOperation.AbilityParams, ParamData{
			ParamCode:  param.ParamCode,
			ParamValue: param.ParamValue,
		})
	}
	if operationMap[po.AbilityCode].DescriptionCn != "" {
		cloudOperation.AbilityCode = operationMap[po.AbilityCode].DescriptionCn
	}
	return cloudOperation
}
