package oplog

import (
	"fmt"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	gocache "github.com/patrickmn/go-cache"
	"github.com/stretchr/testify/assert"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	ulog "git.nevint.com/golang-libs/common-utils/logger"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
)

var w client.Watcher
var ctx *gin.Context

func init() {
	os.Setenv("APOLLO_META", "https://apollo-config-cn-stg.nioint.com")
	os.Setenv("ENV", "stg")
	cfg := &ucfg.Config{}
	ucfg.GetConfigApollo("config.json", true, cfg, ucfg.NeedValidate(true), ucfg.ServiceName("ppd-welkin-backend"), ucfg.Cluster(ucfg.GetWelkinApolloCluster()))
	defer ulog.Final()
	cfg.Log.DirPath = "/tmp/logs"
	config.Cfg = cfg
	log.Init(cfg.Log, true)
	logger := log.Logger.Named("activity")
	w = client.NewWatcherByParam(config.Cfg, logger)
	var err error
	if err != nil {
		panic(err)
	}
	cache.CacheRealtimeDataID(w.Mongodb().Client)
	cache.PowerSwapCache = &cache.DeviceInfoCache{
		Cache:         gocache.New(gocache.NoExpiration, gocache.NoExpiration),
		ResourceCache: gocache.New(gocache.NoExpiration, gocache.NoExpiration),
	}
	cache.PowerSwapCache.RefreshDeviceInfoCache(w.Mongodb().Client)
	model.InitConstant(cfg.ExtraConfig["evBrand"])
	ctx = &gin.Context{Request: &http.Request{Header: make(http.Header)}}
	ctx.Request.Header.Set("X-Request-ID", "test")
}

func TestListMPCOpLog(t *testing.T) {
	operationInterface := "主页面"
	operationDescription := "账户状态"
	cond := ListMPCOpLogCond{
		Project:   umw.PUS3,
		DeviceId:  "PS-NIO-3285ff15-7f564f27",
		StartTime: time.Now().Add(-24 * time.Hour).UnixMilli(),
		EndTime:   time.Now().UnixMilli(),
		CommonCond: model.CommonCond{
			Page: 1,
			Size: 10,
		},
		OperationInterface:   &operationInterface,
		OperationDescription: &operationDescription,
	}
	opLogDO := &MPCOpLogDO{}
	opLogs, total, err := opLogDO.ListMPCOpLog(ctx, cond)
	assert.NoError(t, err)
	fmt.Println(total, ucmd.ToJsonStrIgnoreErr(opLogs))

	cond = ListMPCOpLogCond{
		Project:   umw.PowerSwap2,
		DeviceId:  "PS-NIO-31da205c-64ba4b36",
		StartTime: time.Now().Add(-7 * 24 * time.Hour).UnixMilli(),
		EndTime:   time.Now().UnixMilli(),
		CommonCond: model.CommonCond{
			Page: 1,
			Size: 10,
		},
	}
	opLogDO = &MPCOpLogDO{}
	opLogs, total, err = opLogDO.ListMPCOpLog(ctx, cond)
	assert.NoError(t, err)
	fmt.Println(total, ucmd.ToJsonStrIgnoreErr(opLogs))
}

func TestListCloudOpLog(t *testing.T) {
	cond := ListCloudOpLogCond{
		Project:   umw.PUS3,
		DeviceId:  "PS-NIO-3285ff15-7f564f27",
		StartTime: time.Now().Add(-24 * time.Hour).UnixMilli(),
		EndTime:   time.Now().UnixMilli(),
		CommonCond: model.CommonCond{
			Page: 1,
			Size: 10,
		},
	}
	opLogDO := &CloudOpLogDO{}
	opLogs, total, err := opLogDO.ListCloudOpLog(ctx, cond)
	assert.NoError(t, err)
	fmt.Println(total, ucmd.ToJsonStrIgnoreErr(opLogs))

	cond = ListCloudOpLogCond{
		Project:   umw.PowerSwap2,
		DeviceId:  "PS-NIO-31da205c-64ba4b36",
		StartTime: time.Now().Add(-7 * 24 * time.Hour).UnixMilli(),
		EndTime:   time.Now().UnixMilli(),
		CommonCond: model.CommonCond{
			Page: 1,
			Size: 10,
		},
	}
	opLogDO = &CloudOpLogDO{}
	opLogs, total, err = opLogDO.ListCloudOpLog(ctx, cond)
	assert.NoError(t, err)
	fmt.Println(total, ucmd.ToJsonStrIgnoreErr(opLogs))
}
