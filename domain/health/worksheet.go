package health

import (
	"sort"
	"strings"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"
)

type Worksheet struct {
	WorksheetId     string
	Project         string
	DeviceId        string
	Description     string
	WorksheetStatus string
	WorksheetName   string
	CityCompany     string
	CreateTime      int64
	Assignee        *string
	UpdateTime      int64
}

type WorksheetCond struct {
	StartTs         int64
	EndTs           int64
	Project         string
	DeviceId        *string
	WorksheetStatus *string
	model.CommonCond
}

func makeWorksheetCond(cond WorksheetCond) bson.D {
	filter := bson.D{{"create_time", bson.M{"$gte": cond.StartTs, "$lt": cond.EndTs}}, {"project", cond.Project}}
	if cond.WorksheetStatus != nil {
		worksheetStatus := strings.Split(*cond.WorksheetStatus, ",")
		filter = append(filter, bson.E{Key: "worksheet_status", Value: bson.M{"$in": worksheetStatus}})
	}
	if cond.DeviceId != nil {
		filter = append(filter, bson.E{Key: "device_id", Value: *cond.DeviceId})
	}
	return filter
}

// GetDistinctStatus 获取所有工单状态
func (w *Worksheet) GetDistinctStatus(ctx *gin.Context) (res []interface{}, err error) {
	res, err = client.GetWatcher().PLCMongodb().NewMongoEntry().Distinct(umw.Algorithm, CollectionWorksheetData, "worksheet_status")
	if err != nil {
		return
	}
	sort.Slice(res, func(i, j int) bool {
		resI, _ := res[i].(string)
		resJ, _ := res[j].(string)
		return resI < resJ
	})
	return
}

// ListHealthWorksheet 获取健康度工单列表
func (w *Worksheet) ListHealthWorksheet(ctx *gin.Context, cond WorksheetCond) (res []Worksheet, total int64, err error) {
	opts := options.Find().SetSort(bson.D{{"create_time", -1}, {"device_id", 1}})
	if cond.Page != 0 && cond.Size != 0 {
		opts = opts.SetSkip((cond.Page - 1) * cond.Size).SetLimit(cond.Size)
	}
	var worksheetData []mmgo.WorksheetData
	total, err = client.GetWatcher().PLCMongodb().NewMongoEntry(makeWorksheetCond(cond)).FindMany(umw.Algorithm, CollectionWorksheetData, opts, &worksheetData)
	if err != nil {
		log.CtxLog(ctx).Errorf("ListHealthWorksheet, fail to find worksheet: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	for _, record := range worksheetData {
		deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(record.DeviceId)
		description := ""
		if found {
			description = deviceInfo.Description
		}
		res = append(res, Worksheet{
			WorksheetId:     record.Id,
			Project:         record.Project,
			DeviceId:        record.DeviceId,
			Description:     description,
			WorksheetStatus: record.WorksheetStatus,
			WorksheetName:   record.WorksheetName,
			CityCompany:     record.CityCompany,
			CreateTime:      record.CreateTime,
			Assignee:        record.Assignee,
			UpdateTime:      record.UpdateTime,
		})
	}
	return
}

// GetHealthWorksheetStatistics 获取健康度统计信息
func (w *Worksheet) GetHealthWorksheetStatistics(ctx *gin.Context, cond WorksheetCond) (res WorksheetStatistics, err error) {
	res.WorksheetAssign = make(map[string][]DailyCount)
	res.WorksheetClose = make(map[string]float64)
	filter := makeWorksheetCond(cond)
	pipeline := mongo.Pipeline{
		bson.D{{"$match", filter}},
		bson.D{{"$group", bson.M{
			"_id":   "$worksheet_status",
			"count": bson.M{"$sum": 1},
		}}},
		bson.D{{"$project", bson.M{
			"worksheet_status": "$_id",
			"count":            1,
		}}},
	}
	worksheetStatusCount := make([]WorksheetStatusCount, 0)
	err = client.GetWatcher().PLCMongodb().NewMongoEntry().Aggregate(umw.Algorithm, CollectionWorksheetData, pipeline, &worksheetStatusCount)
	if err != nil {
		log.CtxLog(ctx).Errorf("GetHealthWorksheetStatistics, fail to get worksheet status count: %v, pipeline: %s", err, ucmd.ToJsonStrIgnoreErr(pipeline))
		return
	}
	sort.Slice(worksheetStatusCount, func(i, j int) bool {
		return worksheetStatusCount[i].WorksheetStatus < worksheetStatusCount[j].WorksheetStatus
	})
	res.WorksheetCompleteStatus = worksheetStatusCount

	pipeline = mongo.Pipeline{
		bson.D{{"$match", filter}},
		bson.D{{"$group", bson.M{
			"_id": bson.M{
				"day":            "$day",
				"worksheet_type": "$worksheet_type",
			},
			"count": bson.M{"$sum": 1},
		}}},
		bson.D{{"$project", bson.M{
			"worksheet_type": "$_id.worksheet_type",
			"day":            "$_id.day",
			"count":          1,
		}}},
	}
	var worksheetDailyCount []mmgo.WorksheetDailyCount
	err = client.GetWatcher().PLCMongodb().NewMongoEntry().Aggregate(umw.Algorithm, CollectionWorksheetData, pipeline, &worksheetDailyCount)
	if err != nil {
		log.CtxLog(ctx).Errorf("GetHealthWorksheetStatistics, fail to get worksheet daily count: %v, pipeline: %s", err, ucmd.ToJsonStrIgnoreErr(pipeline))
		return
	}
	for _, record := range worksheetDailyCount {
		res.WorksheetAssign[record.WorksheetType] = append(res.WorksheetAssign[record.WorksheetType], DailyCount{
			Day:   record.Day,
			Count: record.Count,
		})
	}
	for k := range res.WorksheetAssign {
		sort.Slice(res.WorksheetAssign[k], func(i, j int) bool {
			return res.WorksheetAssign[k][i].Day < res.WorksheetAssign[k][j].Day
		})
	}

	pipeline = mongo.Pipeline{
		bson.D{{"$match", filter}},
		bson.D{{"$group", bson.M{
			"_id": "$worksheet_type",
			"total": bson.M{
				"$sum": bson.M{
					"$cond": bson.A{
						bson.M{"$ne": bson.A{"$worksheet_status", WorksheetStatusReject}},
						1,
						0,
					}},
			},
			"close_total": bson.M{
				"$sum": bson.M{
					"$cond": bson.A{
						bson.M{"$eq": bson.A{"$worksheet_status", WorksheetStatusClose}},
						1,
						0,
					}},
			},
		}}},
		bson.D{{"$project", bson.M{
			"worksheet_type": "$_id",
			"close_rate": bson.M{
				"$cond": bson.A{
					bson.M{"$eq": bson.A{"$total", 0}},
					0,
					bson.M{"$divide": bson.A{"$close_total", "$total"}},
				},
			},
		}}},
	}
	var worksheetCloseRate []mmgo.WorksheetCloseRate
	err = client.GetWatcher().PLCMongodb().NewMongoEntry().Aggregate(umw.Algorithm, CollectionWorksheetData, pipeline, &worksheetCloseRate)
	if err != nil {
		log.CtxLog(ctx).Errorf("GetHealthWorksheetStatistics, fail to get worksheet close rate: %v, pipeline: %s", err, ucmd.ToJsonStrIgnoreErr(pipeline))
		return
	}
	for _, record := range worksheetCloseRate {
		res.WorksheetClose[record.WorksheetType] = record.CloseRate
	}
	return
}
