package health

import (
	um "git.nevint.com/golang-libs/common-utils/model"
	"git.nevint.com/welkin2/welkin-backend/model"
)

const (
	CollectionHealthData    = "health_data"
	CollectionWorksheetData = "worksheet_data"

	CategoryOverall = "overall"
	CategoryServo   = "servo"
	CategoryCharge  = "charge"
	CategorySensor  = "sensor"

	WorksheetStatusClose  = "已完成"
	WorksheetStatusReject = "已驳回"
)

var OnDutyMap = map[int]string{
	0: "无人值守",
	1: "有人值守",
}

var OnDutyMapReverse = map[string]int{
	"无人值守": 0,
	"有人值守": 1,
}

type DeviceHealthRequest struct {
	model.CommonUriParam
	StartTime    int64   `form:"start_time"`
	EndTime      int64   `form:"end_time"`
	Day          int64   `form:"day"`
	DeviceId     *string `form:"device_id"`
	IsPatchOrder *bool   `form:"is_patch_order"`
}

type HealthInfo struct {
	HealthScore              float64 `json:"health_score"`
	HealthIncrease           float64 `json:"health_increase"`
	HealthIncreaseRate       float64 `json:"health_increase_rate"`
	ServoHealthScore         float64 `json:"servo_health_score"`
	ServoHealthIncrease      float64 `json:"servo_health_increase"`
	ServoHealthIncreaseRate  float64 `json:"servo_health_increase_rate"`
	ChargeHealthScore        float64 `json:"charge_health_score"`
	ChargeHealthIncrease     float64 `json:"charge_health_increase"`
	ChargeHealthIncreaseRate float64 `json:"charge_health_increase_rate"`
	SensorHealthScore        float64 `json:"sensor_health_score"`
	SensorHealthIncrease     float64 `json:"sensor_health_increase"`
	SensorHealthIncreaseRate float64 `json:"sensor_health_increase_rate"`
}

type UpdateInfo struct {
	UpdateTs    int64 `json:"update_ts"`
	DeviceCount int   `json:"device_count"`
}

type DailyScore struct {
	Day   int64   `json:"day"`
	Score float64 `json:"score"`
}

type GetHealthWeeklyDataResponse struct {
	um.Base
	HealthInfo  HealthInfo   `json:"health_info"`
	UpdateInfo  UpdateInfo   `json:"update_info"`
	HealthChart []DailyScore `json:"health_chart"`
	ServoChart  []DailyScore `json:"servo_chart"`
	ChargeChart []DailyScore `json:"charge_chart"`
	SensorChart []DailyScore `json:"sensor_chart"`
}

type DeviceHealthInfo struct {
	DeviceId          string  `json:"device_id" bson:"device_id"`
	Description       string  `json:"description" bson:"description"`
	HealthScore       float64 `json:"health_score" bson:"health_score"`
	SensorHealthScore float64 `json:"sensor_health_score" bson:"sensor_health_score"`
	ChargeHealthScore float64 `json:"charge_health_score" bson:"charge_health_score"`
	ServoHealthScore  float64 `json:"servo_health_score" bson:"servo_health_score"`
	Owner             string  `json:"owner"`
	OwnerAvatarUrl    string  `json:"owner_avatar_url"`
	IsPatchOrder      bool    `json:"is_patch_order" bson:"is_patch_order"`
}

type GetHealthTailDevicesResponse struct {
	um.Base
	Total int                `json:"total"`
	Data  []DeviceHealthInfo `json:"data"`
}

type HealthScores struct {
	Day               int64   `json:"day" bson:"day"`
	DeviceCount       int     `json:"device_count" bson:"device_count"`
	HealthScore       float64 `json:"health_score" bson:"health_score"`
	ServoHealthScore  float64 `json:"servo_health_score" bson:"servo_health_score"`
	ChargeHealthScore float64 `json:"charge_health_score" bson:"charge_health_score"`
	SensorHealthScore float64 `json:"sensor_health_score" bson:"sensor_health_score"`
}

type GetHealthDetailRequest struct {
	model.CommonUriParam
	StartTime    int64   `form:"start_time"`
	EndTime      int64   `form:"end_time"`
	Area         *string `form:"area"`
	DeviceId     *string `form:"device_id"`
	OnDuty       *int    `form:"on_duty"`
	Download     bool    `form:"download"`
	IsPatchOrder *bool   `form:"is_patch_order"`
}

type DeviceHealthDetail struct {
	DeviceId          string  `json:"device_id"`
	Description       string  `json:"description"`
	Project           string  `json:"project"`
	OnDuty            string  `json:"on_duty"`
	HealthScore       float64 `json:"health_score"`
	SensorHealthScore float64 `json:"sensor_health_score"`
	ChargeHealthScore float64 `json:"charge_health_score"`
	ServoHealthScore  float64 `json:"servo_health_score"`
	Day               int64   `json:"day"`
	Owner             string  `json:"owner"`
	OwnerAvatarUrl    string  `json:"owner_avatar_url"`
	IsPatchOrder      bool    `json:"is_patch_order"`
}

type GetHealthDetailResponse struct {
	um.Base
	Total int                  `json:"total"`
	Data  []DeviceHealthDetail `json:"data"`
}

type GetSingleDeviceHealthTrendResponse struct {
	um.Base
	HealthChart []DailyScore `json:"health_chart"`
	ServoChart  []DailyScore `json:"servo_chart"`
	ChargeChart []DailyScore `json:"charge_chart"`
	SensorChart []DailyScore `json:"sensor_chart"`
}

type DailyTorque struct {
	Day    int64   `json:"day"`
	Torque float64 `json:"torque"`
}

type TorqueTrend struct {
	Loaded []DailyTorque `json:"loaded,omitempty"`
	Free   []DailyTorque `json:"free,omitempty"`
}

type ServoDetail struct {
	StackerLift       TorqueTrend `json:"stacker_lift"`
	StackerPan        TorqueTrend `json:"stacker_pan"`
	Fork              TorqueTrend `json:"fork"`
	Suggestion        string      `json:"suggestion"`
	IsPatchOrder      bool        `json:"is_patch_order"`
	OnlineDay         int         `json:"online_day"`
	DailyServiceCount int         `json:"daily_service_count"`
}

type GetSingleDeviceHealthRequest struct {
	model.CommonUriInTimeRangeParam
	Day     int64 `json:"day" form:"day"`
	GroupId int   `json:"group_id" form:"group_id"`
}

type GetSingleDeviceServoHealthResponse struct {
	um.Base
	Data ServoDetail `json:"data"`
}

type SensorCamera struct {
	CameraType string `json:"camera_type"`
	CameraName string `json:"camera_name"`
	ImageUrl   string `json:"image_url"`
	MaskUrl    string `json:"mask_url"`
}

type SensorDetail struct {
	Details      []SensorCamera `json:"details"`
	Suggestion   string         `json:"suggestion"`
	IsPatchOrder bool           `json:"is_patch_order"`
}

type GetSingleDeviceSensorHealthResponse struct {
	um.Base
	Data SensorDetail `json:"data"`
}

type ChargePoint struct {
	Timestamp int64       `json:"timestamp"`
	Module1   interface{} `json:"module1"`
	Module2   interface{} `json:"module2"`
	Module3   interface{} `json:"module3"`
}

type ChargeDetail struct {
	OutputCurrent   []ChargePoint `json:"output_current"`
	Sic2Temperature []ChargePoint `json:"sic2_temperature"`
	Suggestion      string        `json:"suggestion"`
	IsPatchOrder    bool          `json:"is_patch_order"`
}

type GetSingleDeviceChargeHealthResponse struct {
	um.Base
	Data ChargeDetail `json:"data"`
}

type WorksheetRequest struct {
	model.CommonUriInTimeRangeParam
	WorksheetStatus *string `json:"worksheet_status" form:"worksheet_status"`
	DeviceId        *string `json:"device_id" form:"device_id"`
}

type WorksheetData struct {
	UpdateTime      int64   `json:"update_time"`
	DeviceId        string  `json:"device_id"`
	Description     string  `json:"description"`
	WorksheetStatus string  `json:"worksheet_status"`
	WorksheetId     string  `json:"worksheet_id"`
	WorksheetName   string  `json:"worksheet_name"`
	CityCompany     string  `json:"city_company"`
	CreateTime      int64   `json:"create_time"`
	Assignee        *string `json:"assignee,omitempty"`
}

type ListHealthWorksheetResponse struct {
	um.Base
	Total int64           `json:"total"`
	Data  []WorksheetData `json:"data"`
}

type WorksheetStatusCount struct {
	WorksheetStatus string `json:"worksheet_status" bson:"worksheet_status"`
	Count           int    `json:"count" bson:"count"`
}

type DailyCount struct {
	Day   int64 `json:"day"`
	Count int   `json:"count"`
}

type WorksheetStatistics struct {
	WorksheetCompleteStatus []WorksheetStatusCount  `json:"worksheet_complete_status"`
	WorksheetAssign         map[string][]DailyCount `json:"worksheet_assign"`
	WorksheetClose          map[string]float64      `json:"worksheet_close"`
}
