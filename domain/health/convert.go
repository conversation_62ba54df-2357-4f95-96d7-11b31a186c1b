package health

import (
	"context"
	"time"
)

func ConvertHealthWeeklyDO2VO(ctx context.Context, scores, lastWeekScores HealthScores, scoresDaily []HealthScores) GetHealthWeeklyDataResponse {
	res := GetHealthWeeklyDataResponse{}
	healthInfo := HealthInfo{
		HealthScore:          scores.HealthScore,
		HealthIncrease:       scores.HealthScore - lastWeekScores.HealthScore,
		ServoHealthScore:     scores.ServoHealthScore,
		ServoHealthIncrease:  scores.ServoHealthScore - lastWeekScores.ServoHealthScore,
		ChargeHealthScore:    scores.ChargeHealthScore,
		ChargeHealthIncrease: scores.ChargeHealthScore - lastWeekScores.ChargeHealthScore,
		SensorHealthScore:    scores.SensorHealthScore,
		SensorHealthIncrease: scores.SensorHealthScore - lastWeekScores.SensorHealthScore,
	}
	if lastWeekScores.HealthScore != 0 {
		healthInfo.HealthIncreaseRate = healthInfo.HealthIncrease / lastWeekScores.HealthScore
	}
	if lastWeekScores.ServoHealthScore != 0 {
		healthInfo.ServoHealthIncreaseRate = healthInfo.ServoHealthIncrease / lastWeekScores.ServoHealthScore
	}
	if lastWeekScores.ChargeHealthScore != 0 {
		healthInfo.ChargeHealthIncreaseRate = healthInfo.ChargeHealthIncrease / lastWeekScores.ChargeHealthScore
	}
	if lastWeekScores.SensorHealthScore != 0 {
		healthInfo.SensorHealthIncreaseRate = healthInfo.SensorHealthIncrease / lastWeekScores.SensorHealthScore
	}
	res.HealthInfo = healthInfo
	res.UpdateInfo.DeviceCount = scores.DeviceCount
	res.UpdateInfo.UpdateTs = time.Now().UnixMilli()

	for _, item := range scoresDaily {
		res.HealthChart = append(res.HealthChart, DailyScore{
			Day:   item.Day,
			Score: item.HealthScore,
		})
		res.ServoChart = append(res.ServoChart, DailyScore{
			Day:   item.Day,
			Score: item.ServoHealthScore,
		})
		res.ChargeChart = append(res.ChargeChart, DailyScore{
			Day:   item.Day,
			Score: item.ChargeHealthScore,
		})
		res.SensorChart = append(res.SensorChart, DailyScore{
			Day:   item.Day,
			Score: item.SensorHealthScore,
		})
	}
	return res
}

func ConvertWorksheetDO2VO(worksheets []Worksheet) (res []WorksheetData) {
	for _, record := range worksheets {
		res = append(res, WorksheetData{
			UpdateTime:      record.UpdateTime,
			DeviceId:        record.DeviceId,
			Description:     record.Description,
			WorksheetStatus: record.WorksheetStatus,
			WorksheetId:     record.WorksheetId,
			WorksheetName:   record.WorksheetName,
			CityCompany:     record.CityCompany,
			CreateTime:      record.CreateTime,
			Assignee:        record.Assignee,
		})
	}
	return res
}
