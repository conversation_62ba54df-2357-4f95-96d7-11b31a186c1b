package health

import (
	"fmt"
	"os"
	"testing"

	"github.com/gin-gonic/gin"
	gocache "github.com/patrickmn/go-cache"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	ulog "git.nevint.com/golang-libs/common-utils/logger"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
)

var cfg *ucfg.Config
var w client.Watcher
var d *DeviceHealth

func init() {
	os.Setenv("APOLLO_META", "https://apollo-config-cn-stg.nioint.com")
	os.Setenv("ENV", "stg")
	cfg = &ucfg.Config{}
	ucfg.GetConfigApollo("config.json", true, cfg, ucfg.NeedValidate(true), ucfg.ServiceName("ppd-welkin-backend"), ucfg.Cluster(ucfg.GetWelkinApolloCluster()))
	defer ulog.Final()
	cfg.Log.DirPath = "/tmp/logs"
	log.Init(cfg.Log, true)
	logger := log.Logger.Named("health")
	w = client.NewWatcherByParam(cfg, logger)
	var err error
	d, err = NewDeviceHealth(umw.PUS3)
	if err != nil {
		panic(err)
	}
	cache.CacheRealtimeDataID(w.Mongodb().Client)
	cache.PowerSwapCache = &cache.DeviceInfoCache{
		Cache: gocache.New(gocache.NoExpiration, gocache.NoExpiration),
	}
	cache.PowerSwapCache.RefreshDeviceInfoCache(w.Mongodb().Client)
}

func TestDeviceHealth_CalculateHealthScoreDaily(t *testing.T) {
	res, err := d.CalculateHealthScoreDaily(&gin.Context{}, 1717948800000, 1718553600000)
	if err != nil {
		t.Fatal(err)
	}
	for _, item := range res {
		fmt.Println(ucmd.ToJsonStrIgnoreErr(item))
	}
}

func TestDeviceHealth_PrepareHealthWeeklyData(t *testing.T) {
	res, err := d.PrepareHealthWeeklyData(&gin.Context{}, 1717948800000, 1718553600000)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(ucmd.ToJsonStrIgnoreErr(res))
}

// func TestDeviceHealth_CalculateHealthTailDevices(t *testing.T) {
// 	res, err := d.CalculateHealthTailDevices(&gin.Context{}, 1717948800000, 1718553600000, CategoryOverall)
// 	if err != nil {
// 		t.Fatal(err)
// 	}
// 	for _, item := range res {
// 		fmt.Println(ucmd.ToJsonStrIgnoreErr(item))
// 	}
// }

func TestDeviceHealth_PrepareHealthDetail(t *testing.T) {
	request := GetHealthDetailRequest{
		CommonUriParam: model.CommonUriParam{
			Page: 1,
			Size: 10,
		},
		StartTime: 1717948800000,
		EndTime:   1718553600000,
		Area:      nil,
		DeviceId:  nil,
		OnDuty:    nil,
		Download:  false,
	}
	res, err := d.PrepareHealthDetail(&gin.Context{}, request)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(ucmd.ToJsonStrIgnoreErr(res))
}
