package health

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"
	"git.nevint.com/welkin2/welkin-backend/util"
)

type DeviceHealth struct {
	Project     string
	AlphaServo  float64
	AlphaSensor float64
	AlphaCharge float64
	StartTime   int64
	EndTime     int64
}

func NewDeviceHealth(project string) (*DeviceHealth, error) {
	if project != umw.PUS3 {
		return nil, fmt.Errorf("project %s is not supported", project)
	}
	rawData, err := client.GetWatcher().PLCMongodb().NewMongoEntry(bson.D{{"config", true}}).GetOne(umw.Algorithm, CollectionHealthData)
	if err != nil {
		return nil, err
	}
	var healthConfig mmgo.DeviceHealthConfig
	if err = bson.Unmarshal(rawData, &healthConfig); err != nil {
		return nil, err
	}
	return &DeviceHealth{
		Project:     project,
		AlphaServo:  healthConfig.AlphaServo,
		AlphaSensor: healthConfig.AlphaSensor,
		AlphaCharge: healthConfig.AlphaCharge,
	}, nil
}

func (d *DeviceHealth) SetTimeRange(startTime, endTime int64) *DeviceHealth {
	d.StartTime = startTime
	d.EndTime = endTime
	return d
}

func (d *DeviceHealth) matchStage(c context.Context, startTs, endTs int64, project string) bson.D {
	return bson.D{{"$match", bson.M{
		"day":                 bson.M{"$gte": startTs, "$lt": endTs},
		"project":             project,
		"servo_health_score":  bson.M{"$exists": true},
		"charge_health_score": bson.M{"$exists": true},
		"sensor_data":         bson.M{"$exists": true},
	}}}
}

func (d *DeviceHealth) groupStage(c context.Context, groupId interface{}) bson.D {
	return bson.D{{"$group", bson.M{
		"_id":                 groupId,
		"count":               bson.M{"$sum": 1},
		"servo_health_score":  bson.M{"$avg": "$servo_health_score.stacker_score"},
		"charge_health_score": bson.M{"$avg": "$charge_health_score"},
		"camera_sum":          bson.M{"$sum": "$sensor_data.camera_sum"},
		"total_score":         bson.M{"$sum": "$sensor_data.total_score"},
		"device_set":          bson.M{"$addToSet": "$device_id"},
	}}}
}

func (d *DeviceHealth) calculateProjectStage(c context.Context, key ...string) bson.D {
	projectStage := bson.M{
		"servo_health_score":  1,
		"charge_health_score": 1,
		"sensor_health_score": bson.M{
			"$cond": bson.A{
				bson.M{"$eq": bson.A{"$camera_sum", 0}},
				0,
				bson.M{"$divide": bson.A{"$total_score", "$camera_sum"}},
			},
		},
		"device_count": bson.M{"$size": "$device_set"},
	}
	if len(key) > 0 {
		projectStage[key[0]] = "$_id"
	}

	return bson.D{{"$project", projectStage}}
}

func (d *DeviceHealth) convertProjectStage(c context.Context, key ...string) bson.D {
	projectStage := bson.M{
		"servo_health_score":  1,
		"charge_health_score": bson.M{"$multiply": bson.A{"$charge_health_score", 100}},
		"sensor_health_score": 1,
		"device_count":        1,
	}
	if len(key) > 0 {
		projectStage[key[0]] = 1
	}
	return bson.D{{"$project", projectStage}}
}

func (d *DeviceHealth) outputProjectStage(c context.Context, key ...string) bson.D {
	projectStage := bson.M{
		"servo_health_score":  1,
		"charge_health_score": 1,
		"sensor_health_score": 1,
		"health_score": bson.M{
			"$add": bson.A{
				bson.M{"$multiply": bson.A{"$servo_health_score", d.AlphaServo}},
				bson.M{"$multiply": bson.A{"$charge_health_score", d.AlphaCharge}},
				bson.M{"$multiply": bson.A{"$sensor_health_score", d.AlphaSensor}},
			},
		},
		"device_count": 1,
	}
	if len(key) > 0 {
		projectStage[key[0]] = 1
	}
	return bson.D{{"$project", projectStage}}
}

// CalculateHealthScore 计算一周的全量设备健康度
func (d *DeviceHealth) CalculateHealthScore(c context.Context, startTs, endTs int64) (healthScores HealthScores, err error) {
	pipeline := mongo.Pipeline{
		d.matchStage(c, startTs, endTs, d.Project),
		d.groupStage(c, primitive.Null{}),
		d.calculateProjectStage(c),
		d.convertProjectStage(c),
		d.outputProjectStage(c),
	}
	var scores []HealthScores
	if err = client.GetWatcher().PLCMongodb().NewMongoEntry().Aggregate(umw.Algorithm, CollectionHealthData, pipeline, &scores); err != nil {
		log.CtxLog(c).Errorf("fail to calculate health scores, err: %v, pipeline: %s", err, ucmd.ToJsonStrIgnoreErr(pipeline))
		return
	}
	if len(scores) == 0 {
		log.CtxLog(c).Errorf("aggregate result len 0, pipeline: %s", ucmd.ToJsonStrIgnoreErr(pipeline))
		return
	}

	return scores[0], nil
}

// CalculateHealthScoreDaily 计算一周内每天的全量设备健康度
func (d *DeviceHealth) CalculateHealthScoreDaily(c context.Context, startTs, endTs int64) (healthScores []HealthScores, err error) {
	pipeline := mongo.Pipeline{
		d.matchStage(c, startTs, endTs, d.Project),
		d.groupStage(c, "$day"),
		d.calculateProjectStage(c, "day"),
		d.convertProjectStage(c, "day"),
		d.outputProjectStage(c, "day"),
		bson.D{{"$sort", bson.M{"day": 1}}},
	}
	if err = client.GetWatcher().PLCMongodb().NewMongoEntry().Aggregate(umw.Algorithm, CollectionHealthData, pipeline, &healthScores); err != nil {
		log.CtxLog(c).Errorf("fail to calculate health scores daily, err: %v, pipeline: %s", err, ucmd.ToJsonStrIgnoreErr(pipeline))
		return
	}
	return
}

// PrepareHealthWeeklyData 获取一周的全量设备健康度数据
func (d *DeviceHealth) PrepareHealthWeeklyData(c context.Context, startTs, endTs int64) (res GetHealthWeeklyDataResponse, err error) {
	g := ucmd.NewErrGroup(c)
	var (
		scores         HealthScores
		lastWeekScores HealthScores
		scoresDaily    []HealthScores
	)
	g.GoRecover(func() error {
		var gErr error
		scores, gErr = d.CalculateHealthScore(c, startTs, endTs)
		if gErr != nil {
			log.CtxLog(c).Errorf("fail to CalculateHealthScoreWeekly current week, err: %v", gErr)
			return gErr
		}
		return nil
	})
	g.GoRecover(func() error {
		var gErr error
		startTime, endTime := time.UnixMilli(startTs), time.UnixMilli(endTs)
		lastWeekScores, gErr = d.CalculateHealthScore(c, startTime.AddDate(0, 0, -7).UnixMilli(), endTime.AddDate(0, 0, -7).UnixMilli())
		if gErr != nil {
			log.CtxLog(c).Errorf("fail to CalculateHealthScoreWeekly last week, err: %v", gErr)
			return gErr
		}
		return nil
	})
	g.GoRecover(func() error {
		var gErr error
		scoresDaily, gErr = d.CalculateHealthScoreDaily(c, startTs, endTs)
		if gErr != nil {
			log.CtxLog(c).Errorf("fail to CalculateHealthScoreDaily, err: %v", gErr)
			return gErr
		}
		return nil
	})
	if err = g.Wait(); err != nil {
		return
	}
	res = ConvertHealthWeeklyDO2VO(c, scores, lastWeekScores, scoresDaily)
	return
}

type DeviceHealthCond struct {
	model.CommonUriParam
	StartTime    int64   `form:"start_time"`
	EndTime      int64   `form:"end_time"`
	DeviceId     *string `form:"device_id"`
	IsPatchOrder *bool   `form:"is_patch_order"`
}

// PrepareHealthTailDevices 获取一周的尾部设备健康数据
func (d *DeviceHealth) PrepareHealthTailDevices(c context.Context, cond DeviceHealthCond) (tailDevices []DeviceHealthInfo, total int, err error) {
	filter := bson.D{
		{"day", bson.M{"$gte": cond.StartTime, "$lte": cond.EndTime}},
		{"project", d.Project},
		{"servo_health_score", bson.M{"$exists": true}},
		{"charge_health_score", bson.M{"$exists": true}},
		{"sensor_data", bson.M{"$exists": true}},
	}
	if cond.DeviceId != nil {
		filter = append(filter, bson.E{Key: "device_id", Value: *cond.DeviceId})
	}
	if cond.Page == 0 || cond.Size == 0 {
		util.SetURIParamDefault(&cond.CommonUriParam)
	}
	pipeline := mongo.Pipeline{
		bson.D{{"$match", filter}},
		bson.D{{"$addFields", bson.M{
			"sensor_health_score_sort": bson.M{
				"$cond": bson.A{
					bson.M{"$eq": bson.A{"$sensor_data.camera_sum", 0}},
					0,
					bson.M{"$divide": bson.A{"$sensor_data.total_score", "$sensor_data.camera_sum"}},
				},
			},
			"charge_health_score_sort": bson.M{"$multiply": bson.A{"$charge_health_score", 100}},
			"servo_health_score_sort":  "$servo_health_score.stacker_score",
		}}},
		bson.D{{"$addFields", bson.M{
			"health_score_sort": bson.M{
				"$add": bson.A{
					bson.M{"$multiply": bson.A{d.AlphaCharge, "$charge_health_score_sort"}},
					bson.M{"$multiply": bson.A{d.AlphaServo, "$servo_health_score_sort"}},
					bson.M{"$multiply": bson.A{d.AlphaSensor, "$sensor_health_score_sort"}},
				},
			},
		}}},
		bson.D{{"$group", bson.M{
			"_id":                      "$device_id",
			"servo_health_score_sort":  bson.M{"$avg": "$servo_health_score_sort"},
			"charge_health_score_sort": bson.M{"$avg": "$charge_health_score_sort"},
			"sensor_health_score_sort": bson.M{"$avg": "$sensor_health_score_sort"},
			"health_score_sort":        bson.M{"$avg": "$health_score_sort"},
			"worksheet_servo":          bson.M{"$max": "$worksheet_servo"},
			"worksheet_sensor":         bson.M{"$max": "$worksheet_sensor"},
			"worksheet_charger":        bson.M{"$max": "$worksheet_charger"},
		}}},
		bson.D{{"$project", bson.M{
			"device_id":           "$_id",
			"servo_health_score":  "$servo_health_score_sort",
			"charge_health_score": "$charge_health_score_sort",
			"sensor_health_score": "$sensor_health_score_sort",
			"health_score":        "$health_score_sort",
			"is_patch_order":      bson.M{"$or": bson.A{"$worksheet_sensor", "$worksheet_servo", "$worksheet_charger"}},
		}}},
	}
	if cond.IsPatchOrder != nil {
		pipeline = append(pipeline, bson.D{{"$match", bson.M{"is_patch_order": *cond.IsPatchOrder}}})
	}
	sortOpt := bson.D{{"day", -1}, {"device_id", 1}}
	if cond.Sort != "" {
		sortVal := 1
		if cond.Descending {
			sortVal = -1
		}
		sortOpt = bson.D{{cond.Sort, sortVal}, {"device_id", 1}}
	}
	pipeline = append(pipeline,
		bson.D{{"$facet", bson.M{
			"total_count": bson.A{bson.M{"$group": bson.M{"_id": primitive.Null{}, "count": bson.M{"$sum": 1}}}},
			"data": bson.A{
				bson.D{{"$sort", sortOpt}},
				bson.D{{"$skip", (cond.Page - 1) * cond.Size}},
				bson.D{{"$limit", cond.Size}},
			},
		}}},
	)
	var res []mmgo.TailDeviceData
	if err = client.GetWatcher().PLCMongodb().NewMongoEntry().Aggregate(umw.Algorithm, CollectionHealthData, pipeline, &res); err != nil {
		log.CtxLog(c).Errorf("fail to calculate health tail devices, err: %v, pipeline: %s", err, ucmd.ToJsonStrIgnoreErr(pipeline))
		return
	}
	if len(res) == 0 {
		log.CtxLog(c).Errorf("fail to calculate health tail devices, len is 0, pipeline: %s", ucmd.ToJsonStrIgnoreErr(pipeline))
		err = fmt.Errorf("len is 0")
		return
	}
	ownerIds := map[string]struct{}{}
	for _, record := range res[0].Data {
		tailDevice := DeviceHealthInfo{
			DeviceId:          record.DeviceId,
			HealthScore:       record.HealthScore,
			SensorHealthScore: record.SensorHealthScore,
			ChargeHealthScore: record.ChargeHealthScore,
			ServoHealthScore:  record.ServoHealthScore,
			IsPatchOrder:      record.IsPatchOrder,
		}
		deviceInfo, _ := cache.PowerSwapCache.GetSingleDevice(record.DeviceId)
		if deviceInfo != nil {
			tailDevice.Description = deviceInfo.Description
			tailDevice.Owner = deviceInfo.DeviceSupervisor
			if deviceInfo.DeviceSupervisor != "" {
				ownerIds[deviceInfo.DeviceSupervisor] = struct{}{}
			}
		}
		tailDevices = append(tailDevices, tailDevice)
	}
	userAvatars, err := util.GetUserAvatar(config.Cfg, ownerIds)
	if err != nil {
		log.CtxLog(c).Errorf("fail to GetUserAvatar: %v, ownerIds: %s", err, ucmd.ToJsonStrIgnoreErr(ownerIds))
		return
	}
	for i := range tailDevices {
		tailDevices[i].OwnerAvatarUrl = userAvatars[tailDevices[i].Owner]
	}
	if len(res[0].TotalCount) > 0 {
		total = res[0].TotalCount[0].Count
	}
	return
}

// PrepareHealthDetail 获取健康值明细数据
func (d *DeviceHealth) PrepareHealthDetail(c context.Context, request GetHealthDetailRequest) (response GetHealthDetailResponse, err error) {
	filter := bson.D{
		{"day", bson.M{"$gte": request.StartTime, "$lte": request.EndTime}},
		{"project", d.Project},
		{"servo_health_score", bson.M{"$exists": true}},
		{"charge_health_score", bson.M{"$exists": true}},
		{"sensor_data", bson.M{"$exists": true}},
	}
	if request.DeviceId != nil {
		filter = append(filter, bson.E{Key: "device_id", Value: *request.DeviceId})
	}
	if request.OnDuty != nil {
		filter = append(filter, bson.E{Key: "tag_is_unattended", Value: OnDutyMap[*request.OnDuty]})
	}
	if request.IsPatchOrder != nil {
		if *request.IsPatchOrder {
			filter = append(filter, bson.E{Key: "$or", Value: bson.A{
				bson.M{"worksheet_sensor": true},
				bson.M{"worksheet_servo": true},
				bson.M{"worksheet_charge": true},
			}})
		} else {
			filter = append(filter,
				bson.E{Key: "worksheet_sensor", Value: bson.M{"$ne": true}},
				bson.E{Key: "worksheet_servo", Value: bson.M{"$ne": true}},
				bson.E{Key: "worksheet_charge", Value: bson.M{"$ne": true}},
			)
		}
	}
	if request.Area != nil {
		area := *request.Area
		deviceList := make([]string, 0)
		devices := cache.PowerSwapCache.GetAllDevices()
		for _, device := range devices {
			if device.CityCompany == area && device.Project == d.Project {
				deviceList = append(deviceList, device.DeviceId)
			}
		}
		filter = append(filter, bson.E{Key: "device_id", Value: bson.M{"$in": deviceList}})
	}
	if request.Page == 0 || request.Size == 0 {
		util.SetURIParamDefault(&request.CommonUriParam)
	}
	if request.Download {
		request.Size = 99999
	}

	pipeline := mongo.Pipeline{
		bson.D{{"$match", filter}},
		bson.D{{"$addFields", bson.M{
			"sensor_health_score_sort": bson.M{
				"$cond": bson.A{
					bson.M{"$eq": bson.A{"$sensor_data.camera_sum", 0}},
					0,
					bson.M{"$divide": bson.A{"$sensor_data.total_score", "$sensor_data.camera_sum"}},
				},
			},
			"charge_health_score_sort": bson.M{"$multiply": bson.A{"$charge_health_score", 100}},
			"servo_health_score_sort":  "$servo_health_score.stacker_score",
		}}},
		bson.D{{"$addFields", bson.M{
			"health_score_sort": bson.M{
				"$add": bson.A{
					bson.M{"$multiply": bson.A{d.AlphaCharge, "$charge_health_score_sort"}},
					bson.M{"$multiply": bson.A{d.AlphaServo, "$servo_health_score_sort"}},
					bson.M{"$multiply": bson.A{d.AlphaSensor, "$sensor_health_score_sort"}},
				},
			},
		}}},
	}
	sortOpt := bson.D{{"day", -1}, {"device_id", 1}}
	if request.Sort != "" {
		sortVal := 1
		if request.Descending {
			sortVal = -1
		}
		sortOpt = bson.D{{fmt.Sprintf("%s_sort", request.Sort), sortVal}, {"device_id", 1}}
	}
	pipeline = append(pipeline,
		bson.D{{"$sort", sortOpt}},
		bson.D{{"$skip", (request.Page - 1) * request.Size}},
		bson.D{{"$limit", request.Size}},
	)

	var healthData []mmgo.DeviceHealthData
	err = client.GetWatcher().PLCMongodb().NewMongoEntry().Aggregate(umw.Algorithm, CollectionHealthData, pipeline, &healthData)
	if err != nil {
		log.CtxLog(c).Errorf("fail to find DeviceHealthData, err: %v, pipeline: %s", err, ucmd.ToJsonStrIgnoreErr(pipeline))
		return
	}
	total, err := client.GetWatcher().PLCMongodb().NewMongoEntry(filter).Count(umw.Algorithm, CollectionHealthData)
	if err != nil {
		log.CtxLog(c).Errorf("fail to count DeviceHealthData, err: %v, filter: %s", err, ucmd.ToJsonStrIgnoreErr(filter))
		return
	}
	response.Total = int(total)
	data := make([]DeviceHealthDetail, 0)
	ownerIds := map[string]struct{}{}
	for _, item := range healthData {
		detail := DeviceHealthDetail{
			DeviceId:          item.DeviceId,
			Project:           item.Project,
			OnDuty:            item.TagIsUnattended,
			ServoHealthScore:  item.ServoHealthScoreSort,
			ChargeHealthScore: item.ChargeHealthScoreSort,
			SensorHealthScore: item.SensorHealthScoreSort,
			HealthScore:       item.HealthScoreSort,
			Day:               item.Day,
			IsPatchOrder:      item.WorksheetCharge || item.WorksheetSensor || item.WorksheetServo,
		}
		deviceInfo, _ := cache.PowerSwapCache.GetSingleDevice(item.DeviceId)
		if deviceInfo != nil {
			detail.Description = deviceInfo.Description
			detail.Owner = deviceInfo.DeviceSupervisor
			if detail.Owner != "" {
				ownerIds[detail.Owner] = struct{}{}
			}
		}
		data = append(data, detail)
	}
	userAvatars, err := util.GetUserAvatar(config.Cfg, ownerIds)
	if err != nil {
		log.CtxLog(c).Errorf("fail to GetUserAvatar: %v, ownerIds: %s", err, ucmd.ToJsonStrIgnoreErr(ownerIds))
		return
	}
	for i := range data {
		data[i].OwnerAvatarUrl = userAvatars[data[i].Owner]
	}
	response.Data = data
	return
}

func (d *DeviceHealth) GetSingleDeviceHealth(c context.Context, deviceId string, day int64) (healthData mmgo.DeviceHealthData, err error) {
	filter := bson.D{
		{"device_id", deviceId},
		{"day", day},
	}
	err = client.GetWatcher().PLCMongodb().NewMongoEntry(filter).FindOne(umw.Algorithm, CollectionHealthData, options.FindOne(), &healthData)
	if err != nil {
		log.CtxLog(c).Errorf("fail to GetSingleDeviceHealth, err: %v, filter: %s", err, ucmd.ToJsonStrIgnoreErr(filter))
		if errors.Is(err, mongo.ErrNoDocuments) {
			return healthData, nil
		}
		return
	}
	return healthData, nil
}

func (d *DeviceHealth) ListSingleDeviceHealth(c context.Context, deviceId string) (healthData []mmgo.DeviceHealthData, err error) {
	filter := bson.D{
		{"device_id", deviceId},
		{"day", bson.M{"$gte": d.StartTime, "$lt": d.EndTime}},
		{"servo_health_score", bson.M{"$exists": true}},
		{"charge_health_score", bson.M{"$exists": true}},
		{"sensor_data", bson.M{"$exists": true}},
	}
	_, err = client.GetWatcher().PLCMongodb().NewMongoEntry(filter).FindMany(umw.Algorithm, CollectionHealthData, options.Find().SetSort(bson.M{"day": 1}), &healthData)
	if err != nil {
		log.CtxLog(c).Errorf("fail to ListSingleDeviceHealth, err: %v, filter: %s", err, ucmd.ToJsonStrIgnoreErr(filter))
		return
	}
	return
}

func (d *DeviceHealth) GetSingleDeviceHealthTrend(c context.Context, deviceId string) (response GetSingleDeviceHealthTrendResponse, err error) {
	healthData, err := d.ListSingleDeviceHealth(c, deviceId)
	for _, item := range healthData {
		chargeScore := item.ChargeHealthScore * 100
		sensorScore := 0.0
		if item.SensorData.CameraSum > 0 {
			sensorScore = item.SensorData.TotalScore / item.SensorData.CameraSum
		}
		servoScore := item.ServoHealthScore.StackerScore
		response.HealthChart = append(response.HealthChart, DailyScore{
			Day:   item.Day,
			Score: d.AlphaCharge*chargeScore + d.AlphaSensor*sensorScore + d.AlphaServo*servoScore,
		})
		response.ChargeChart = append(response.ChargeChart, DailyScore{
			Day:   item.Day,
			Score: chargeScore,
		})
		response.SensorChart = append(response.SensorChart, DailyScore{
			Day:   item.Day,
			Score: sensorScore,
		})
		response.ServoChart = append(response.ServoChart, DailyScore{
			Day:   item.Day,
			Score: servoScore,
		})
	}
	return
}

func stackerSuggestion(c context.Context, field string, value float64) (needSuggestion bool, err error) {
	scores, err := client.GetWatcher().PLCMongodb().NewMongoEntry().GetPercentage(umw.Algorithm, CollectionHealthData, field, false, 5)
	if err != nil {
		log.CtxLog(c).Errorf("fail to GetPercentage %s, %v", scores, err)
		return
	}
	if len(scores) == 0 {
		return
	}
	if value <= scores[0].(float64) {
		needSuggestion = true
	}
	return
}

func (d *DeviceHealth) GetSingleDeviceServo(c context.Context, deviceId string, day int64) (servoDetail ServoDetail, err error) {
	healthDataDay, err := d.GetSingleDeviceHealth(c, deviceId, day)
	if err != nil {
		log.CtxLog(c).Errorf("fail to GetSingleDeviceHealth, err: %v", err)
		return
	}
	servoDetail = ServoDetail{
		StackerLift:       TorqueTrend{},
		StackerPan:        TorqueTrend{},
		Fork:              TorqueTrend{},
		IsPatchOrder:      healthDataDay.WorksheetServo,
		OnlineDay:         healthDataDay.OnlineDay,
		DailyServiceCount: healthDataDay.DailyServiceCnt,
	}
	panOk, err := stackerSuggestion(c, "servo_health_score.pan_score", healthDataDay.ServoHealthScore.PanScore)
	if err != nil {
		return
	}
	liftOk, err := stackerSuggestion(c, "servo_health_score.lift_score", healthDataDay.ServoHealthScore.LiftScore)
	if err != nil {
		return
	}
	forkOk, err := stackerSuggestion(c, "servo_health_score.fork_score", healthDataDay.ServoHealthScore.ForkScore)
	if err != nil {
		return
	}
	suggestion := make([]string, 0)
	if panOk {
		suggestion = append(suggestion, "请注意堆垛机平移")
	}
	if liftOk {
		suggestion = append(suggestion, "请注意堆垛机升降")
	}
	if forkOk {
		suggestion = append(suggestion, "请注意堆垛机货叉")
	}
	servoDetail.Suggestion = strings.Join(suggestion, "，")

	healthData, err := d.ListSingleDeviceHealth(c, deviceId)
	if err != nil {
		log.CtxLog(c).Errorf("fail to ListSingleDeviceHealth, err: %v", err)
		return
	}
	for _, item := range healthData {
		servoDetail.StackerPan.Loaded = append(servoDetail.StackerPan.Loaded, DailyTorque{
			Day:    item.Day,
			Torque: item.StackerPanLoadedTorque,
		})
		servoDetail.StackerPan.Free = append(servoDetail.StackerPan.Free, DailyTorque{
			Day:    item.Day,
			Torque: item.StackerPanFreeTorque,
		})
		servoDetail.StackerLift.Loaded = append(servoDetail.StackerLift.Loaded, DailyTorque{
			Day:    item.Day,
			Torque: item.StackerLiftLoadedTorque,
		})
		servoDetail.StackerLift.Free = append(servoDetail.StackerLift.Free, DailyTorque{
			Day:    item.Day,
			Torque: item.StackerLiftFreeTorque,
		})
		servoDetail.Fork.Loaded = append(servoDetail.Fork.Loaded, DailyTorque{
			Day:    item.Day,
			Torque: item.ForkTorque,
		})
	}
	return
}

func (d *DeviceHealth) GetSingleDeviceSensor(c context.Context, deviceId string, day int64) (sensorDetail SensorDetail, err error) {
	healthDataDay, err := d.GetSingleDeviceHealth(c, deviceId, day)
	if err != nil {
		log.CtxLog(c).Errorf("fail to GetSingleDeviceHealth, err: %v", err)
		return
	}
	cameraScores := make(map[string]int)
	cameraCounts := make(map[string]int)
	for _, record := range healthDataDay.SensorData.CameraData {
		if record.PredictResult == 1 {
			cameraScores[record.CameraType] += 100
		} else if record.PredictResult == 2 {
			cameraScores[record.CameraType] += 70
		} else if record.PredictResult == 3 {
			cameraScores[record.CameraType] += 50
		}
		cameraCounts[record.CameraType] += 1
	}
	suggestion := make([]string, 0)
	for cameraType, totalScore := range cameraScores {
		if float64(totalScore)/float64(cameraCounts[cameraType]) < 60 {
			suggestion = append(suggestion, cameraType)
		}
	}
	sensorDetail = SensorDetail{
		IsPatchOrder: healthDataDay.WorksheetSensor,
	}
	if len(suggestion) > 0 {
		sensorDetail.Suggestion = fmt.Sprintf("请注意%s摄像头，查看是否歪斜", strings.Join(suggestion, "、"))
	}
	cameraInfo := make([]model.CameraInfo, 0)
	colName := ucmd.RenameProjectDB(d.Project) + "_camera_info"
	_, err = client.GetWatcher().Mongodb().NewMongoEntry(bson.D{{"algorithm", bson.M{"$exists": true}}}).FindMany("camera_management", colName, options.Find().SetSort(bson.M{"camera_type": 1}), &cameraInfo)
	if err != nil {
		log.CtxLog(c).Errorf("fail to get camera info, err: %v", err)
		return
	}
	cameraList := make([]string, 0)
	for _, item := range cameraInfo {
		cameraList = append(cameraList, item.CameraType)
	}
	imageInfo, err := client.GetWatcher().Mongodb().FindOneCameraNewestCCImage(d.Project, deviceId, strings.Join(cameraList, ","), ucmd.GetArea(), day, day+24*time.Hour.Milliseconds())
	if err != nil {
		log.CtxLog(c).Errorf("fail to FindOneCameraNewestCCImage, %v", err)
		return
	}
	for _, record := range cameraInfo {
		sensorDetail.Details = append(sensorDetail.Details, SensorCamera{
			CameraType: record.CameraType,
			CameraName: record.CameraName,
			ImageUrl:   imageInfo[record.CameraType].ImageURL,
			MaskUrl:    record.MaskUrl,
		})
	}
	return
}

func (d *DeviceHealth) GetSingleDeviceCharge(c context.Context, deviceId string, day int64, groupId int) (chargeDetail ChargeDetail, err error) {
	healthDataDay, err := d.GetSingleDeviceHealth(c, deviceId, day)
	if err != nil {
		log.CtxLog(c).Errorf("fail to GetSingleDeviceHealth, err: %v", err)
		return
	}
	var suggestion []string
	sort.Slice(healthDataDay.ChargeModuleHealthScore, func(i, j int) bool {
		return healthDataDay.ChargeModuleHealthScore[i].ModuleId < healthDataDay.ChargeModuleHealthScore[j].ModuleId
	})
	for _, item := range healthDataDay.ChargeModuleHealthScore {
		if item.HealthScore < 0.6 {
			suggestion = append(suggestion, fmt.Sprintf("%d", item.ModuleId))
		}
	}
	chargeDetail = ChargeDetail{
		OutputCurrent:   nil,
		Sic2Temperature: nil,
		IsPatchOrder:    healthDataDay.WorksheetCharge,
	}
	if len(suggestion) > 0 {
		chargeDetail.Suggestion = fmt.Sprintf("请注意%s号充电模块", strings.Join(suggestion, "、"))
	}
	moduleMap := make(map[string]string)
	dataIdList := make([]string, 0)
	for i := 1; i <= 3; i++ {
		moduleId := (groupId-1)*3 + i
		outputCurrent := fmt.Sprintf("%d", 100004+(moduleId-1)*30)
		sic2Temp := fmt.Sprintf("%d", 100018+(moduleId-1)*30)
		dataIdList = append(dataIdList, outputCurrent) // 输出电流
		moduleMap[outputCurrent] = fmt.Sprintf("module%d", i)
		dataIdList = append(dataIdList, sic2Temp) // 主热点温度
		moduleMap[sic2Temp] = fmt.Sprintf("module%d", i)
	}
	dataIds := strings.Join(dataIdList, ",")
	endTime := day + 24*time.Hour.Milliseconds()
	tdw := &client.TDWatcher{
		TDClient:     client.GetWatcher().TDEngine(),
		RedisClient:  client.GetWatcher().Redis(),
		DeviceId:     deviceId,
		StartTs:      &day,
		EndTs:        &endTime,
		Limit:        99999,
		FilterFields: make([]string, 0),
		Descending:   false,
		Logger:       log.Logger.Named("TDEngine"),
	}
	stbName := fmt.Sprintf("realtime_%s", strings.ToLower(d.Project))
	scanStruct, dataIdMap, err := tdw.GetRealtimeFields("device2welkin_realtime", stbName, dataIds, true)
	if err != nil {
		log.Logger.Errorf("GetSingleDeviceCharge, get realtime fields fail, err: %v", err)
		return
	}
	_, rows, err := tdw.FilterDataByFields("device2welkin_realtime")
	if err != nil {
		log.Logger.Errorf("GetSingleDeviceCharge, get realtime data by fields fail: %v, err: %v", tdw.FilterFields, err)
		return
	}
	if rows != nil {
		for rows.Next() {
			// 获取动态的查询结构体
			columns := client.ReflectFields(scanStruct)
			if err = rows.Scan(columns...); err != nil {
				log.Logger.Errorf("GetSlotBatteryInfo, scan realtime data from tdengine fail, err: %v", err)
				continue
			}
			timestamp := scanStruct.FieldByName("Ts").Interface().(time.Time).UnixMilli()
			outputCurrentMap := make(map[string]interface{})
			sic2TempMap := make(map[string]interface{})
			for dataId := range dataIdMap {
				val := scanStruct.FieldByName("R" + dataId).Interface().(model.SafeType).GetRealValue()
				realVal := util.ParseFloat(val)
				// 空值给前端返null
				if realVal <= -9999 {
					val = nil
				}
				module := moduleMap[dataId]
				if strings.HasSuffix(dataId, "4") { // 输出电流
					outputCurrentMap[module] = val
				} else if strings.HasSuffix(dataId, "8") { // 主热点温度
					sic2TempMap[module] = val
				}
			}
			chargeDetail.OutputCurrent = append(chargeDetail.OutputCurrent, ChargePoint{
				Timestamp: timestamp,
				Module1:   outputCurrentMap["module1"],
				Module2:   outputCurrentMap["module2"],
				Module3:   outputCurrentMap["module3"],
			})
			chargeDetail.Sic2Temperature = append(chargeDetail.Sic2Temperature, ChargePoint{
				Timestamp: timestamp,
				Module1:   sic2TempMap["module1"],
				Module2:   sic2TempMap["module2"],
				Module3:   sic2TempMap["module3"],
			})
		}
	}
	return
}
