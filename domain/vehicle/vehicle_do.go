package vehicle

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	"git.nevint.com/welkin2/welkin-backend/client"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"
)

const (
	CollectionDsLicense = "ds-license"
)

type VehicleDO struct {
	VehicleId     string
	UserOwnership int
}

type ListVehicleCond struct {
	VehicleIds []string
}

func (v *VehicleDO) ListVehicle(ctx context.Context, cond ListVehicleCond) (res []VehicleDO, err error) {
	filter := bson.D{
		{"vehicle_id", bson.D{{"$in", cond.VehicleIds}}},
	}
	var vehicleOwnership []mmgo.VehicleOwnership
	_, err = client.GetWatcher().Mongodb().NewMongoEntry(filter).FindMany(umw.VehicleManagement, CollectionDsLicense, options.Find(), &vehicleOwnership)
	if err != nil {
		log.CtxLog(ctx).Errorf("ListVehicle find ownership failed, err: %v, cond: %s", err, cond)
		return
	}
	for _, record := range vehicleOwnership {
		res = append(res, convertVehiclePO2DO(record))
	}
	return
}
