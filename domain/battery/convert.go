package battery

import mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"

func convertBatteryPO2DO(batteryIds []string, batteryOwnership []mmgo.BatteryOwnership, restInfo []mmgo.BatteryRestInfo) []BatteryDO {
	restMap := make(map[string]mmgo.BatteryRestInfo)
	for _, rest := range restInfo {
		restMap[rest.BatteryId] = rest
	}
	ownershipMap := make(map[string]mmgo.BatteryOwnership)
	for _, ownership := range batteryOwnership {
		ownershipMap[ownership.BatteryId] = ownership
	}
	res := make([]BatteryDO, 0)
	for _, batteryId := range batteryIds {
		do := BatteryDO{
			BatteryId: batteryId,
		}
		if _, found := restMap[batteryId]; found {
			do.NeedRest = true
		} else {
			do.NeedRest = false
		}
		if _, found := ownershipMap[batteryId]; found {
			do.Ownership = ownershipMap[batteryId].Ownership
		}
		res = append(res, do)
	}
	return res
}
