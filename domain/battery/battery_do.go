package battery

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	"git.nevint.com/welkin2/welkin-backend/client"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"
)

const (
	CollectionRest      = "rest_status"
	CollectionOwnership = "ownership"
)

type BatteryDO struct {
	BatteryId string
	NeedRest  bool
	Ownership string
}

type ListBatteryCond struct {
	BatteryIds []string
}

func (b *BatteryDO) ListBattery(ctx context.Context, cond ListBatteryCond) (res []BatteryDO, err error) {
	filter := bson.D{
		{"battery_id", bson.D{{"$in", cond.BatteryIds}}},
	}
	var batteryRestInfo []mmgo.BatteryRestInfo
	_, err = client.GetWatcher().Mongodb().NewMongoEntry(filter).FindMany(umw.BatteryManagement, CollectionRest, options.Find(), &batteryRestInfo)
	if err != nil {
		log.CtxLog(ctx).Errorf("ListBattery find rest status failed, err: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	var batteryOwnership []mmgo.BatteryOwnership
	_, err = client.GetWatcher().Mongodb().NewMongoEntry(filter).FindMany(umw.BatteryManagement, CollectionOwnership, options.Find(), &batteryOwnership)
	if err != nil {
		log.CtxLog(ctx).Errorf("ListBattery find ownership failed, err: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	res = convertBatteryPO2DO(cond.BatteryIds, batteryOwnership, batteryRestInfo)
	return
}
