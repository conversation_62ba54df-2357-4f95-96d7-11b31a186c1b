package activity

import (
	"context"

	"git.nevint.com/welkin2/welkin-backend/cache"
	mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"
)

func ConvertActivityDevicesDO2VO(ctx context.Context, devices []mmgo.ActivityDevices, index int) []OutOfDataDevice {
	res := make([]OutOfDataDevice, 0)
	for _, item := range devices {
		deviceInfo, exist := cache.PowerSwapCache.GetSingleDevice(item.DeviceId)
		description := ""
		if exist {
			description = deviceInfo.Description
		}
		res = append(res, OutOfDataDevice{
			Id:          index,
			DeviceId:    item.DeviceId,
			Description: description,
			DurationDay: item.DurationDay,
		})
		index += 1
	}
	return res
}
