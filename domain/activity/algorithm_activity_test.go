package activity

import (
	"fmt"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	gocache "github.com/patrickmn/go-cache"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	ulog "git.nevint.com/golang-libs/common-utils/logger"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	log "git.nevint.com/welkin2/welkin-backend/logger"
)

var w client.Watcher
var ctx *gin.Context

func init() {
	os.Setenv("APOLLO_META", "https://apollo-config-cn-stg.nioint.com")
	os.Setenv("ENV", "stg")
	cfg := &ucfg.Config{}
	ucfg.GetConfigApollo("config.json", true, cfg, ucfg.NeedValidate(true), ucfg.ServiceName("ppd-welkin-backend"), ucfg.Cluster(ucfg.GetWelkinApolloCluster()))
	defer ulog.Final()
	cfg.Log.DirPath = "/tmp/logs"
	config.Cfg = cfg
	log.Init(cfg.Log, true)
	logger := log.Logger.Named("activity")
	w = client.NewWatcherByParam(config.Cfg, logger)
	var err error
	if err != nil {
		panic(err)
	}
	cache.CacheRealtimeDataID(w.Mongodb().Client)
	cache.PowerSwapCache = &cache.DeviceInfoCache{
		Cache:         gocache.New(gocache.NoExpiration, gocache.NoExpiration),
		ResourceCache: gocache.New(gocache.NoExpiration, gocache.NoExpiration),
	}
	cache.PowerSwapCache.RefreshDeviceInfoCache(w.Mongodb().Client)
	ctx = &gin.Context{Request: &http.Request{Header: make(http.Header)}}
	ctx.Request.Header.Set("X-Request-ID", "test")
}

func TestActivityDO_CalculateActivity(t *testing.T) {
	startTime, _ := time.ParseInLocation(time.DateTime, "2024-08-01 00:00:00", time.Local)
	endTime, _ := time.ParseInLocation(time.DateTime, "2024-08-06 00:00:00", time.Local)
	a := &ActivityDO{
		StartTime: startTime.UnixMilli(),
		EndTime:   endTime.UnixMilli(),
		Projects:  []string{umw.PowerSwap2, umw.PUS3, umw.PUS4},
	}
	a.CalculateActivity(ctx)
}

func TestActivityDO_SendLarkCard(t *testing.T) {
	startTime, _ := time.ParseInLocation(time.DateTime, "2024-08-01 00:00:00", time.Local)
	endTime, _ := time.ParseInLocation(time.DateTime, "2024-08-06 00:00:00", time.Local)
	a := &ActivityDO{
		StartTime: startTime.UnixMilli(),
		EndTime:   endTime.UnixMilli(),
		Projects:  []string{umw.PowerSwap2, umw.PUS3, umw.PUS4},
	}

	a.SendLarkCard(ctx)
}

func TestActivityDO_ListActivityStats(t *testing.T) {
	startTime, _ := time.ParseInLocation(time.DateTime, "2024-08-01 00:00:00", time.Local)
	endTime, _ := time.ParseInLocation(time.DateTime, "2024-08-06 00:00:00", time.Local)
	failureValue := 0
	a := &ActivityDO{
		StartTime: startTime.UnixMilli(),
		EndTime:   endTime.UnixMilli(),
		Projects:  []string{umw.PUS3},
	}
	res, err := a.ListActivityStats(ctx, failureValue)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(ucmd.ToJsonStrIgnoreErr(res))
}
