package activity

import (
	um "git.nevint.com/golang-libs/common-utils/model"
	"git.nevint.com/welkin2/welkin-backend/model"
)

const (
	CollectionStats   = "activity_stats"
	CollectionDevices = "activity_devices"
)

var ImageTypeNameMap map[int32]string

type CalculateActivityRequest struct {
	Projects  []string `json:"projects"`
	StartTime int64    `json:"start_time"`
	EndTime   int64    `json:"end_time"`
}

type ListActivityStatsRequest struct {
	model.CommonUriInTimeRangeParam
	FailureValue int `json:"failure_value" form:"failure_value"`
}

type AlgorithmStats struct {
	Algorithm   string  `json:"algorithm"`
	UploadTotal int64   `json:"upload_total"`
	UsageRate   float64 `json:"usage_rate"`
}

type ListActivityStatsResponse struct {
	um.Base
	Data []AlgorithmStats `json:"data"`
}

type OutOfDataDevice struct {
	Id          int    `json:"id"`
	DeviceId    string `json:"device_id"`
	Description string `json:"description"`
	DurationDay int    `json:"duration_day"`
}

type ListActivityDevicesResponse struct {
	um.Base
	Total int64             `json:"total"`
	Data  []OutOfDataDevice `json:"data"`
}

type ImageDownloadInfo struct {
	ImageGenTime int64 `json:"image_gen_time"`
	ImageSize    int64 `json:"image_size"`
	ImageType    int   `json:"image_type"`
	Abnormal     bool  `json:"abnormal"`
}
