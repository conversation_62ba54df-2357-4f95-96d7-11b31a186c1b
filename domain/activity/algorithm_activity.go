package activity

import (
	"fmt"
	"os"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	"git.nevint.com/golang-libs/common-utils/larkservice"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	"git.nevint.com/welkin2/welkin-backend/logger"
	mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"
	"git.nevint.com/welkin2/welkin-backend/util"
)

type ActivityDO struct {
	StartTime int64    `json:"start_time"`
	EndTime   int64    `json:"end_time"`
	Projects  []string `json:"projects"`
}

var ImageTypeNameOnce sync.Once

// GetImageTypeNameMap 获取照片类型到算法名称的映射
func GetImageTypeNameMap() map[int32]string {
	ImageTypeNameOnce.Do(func() {
		ImageTypeNameMap = make(map[int32]string)
		for imageName, v := range umw.AlgorithmImageTypeListMap {
			imageTypes := strings.Split(v, ",")
			for _, item := range imageTypes {
				imageType, _ := strconv.Atoi(item)
				ImageTypeNameMap[int32(imageType)] = imageName
			}
		}
	})
	return ImageTypeNameMap
}

// SendLarkCard 活跃度计算失败时，发送飞书通知
func (a *ActivityDO) SendLarkCard(c *gin.Context) {
	_ = larkservice.SendMessage(larkservice.MsgText, fmt.Sprintf("{\"text\": \"[%s] owl活跃度数据更新失败, start: %v, end: %v, projects: %v, request_id: %s\"}", os.Getenv("ENV"), time.UnixMilli(a.StartTime), time.UnixMilli(a.EndTime), a.Projects, c.Request.Header.Get("X-Request-ID")), larkservice.Receiver{
		Type:       larkim.ReceiveIdTypeEmail,
		ReceiveIds: config.Cfg.CardBot.Receivers["activityStats"],
	})
}

// CalculateActivity 计算活跃度统计值
func (a *ActivityDO) CalculateActivity(c *gin.Context) {
	for _, project := range a.Projects {
		for start := a.StartTime; start < a.EndTime; start += 24 * time.Hour.Milliseconds() {
			dbName := fmt.Sprintf("imageinfo-%s", ucmd.RenameProjectDB(project))
			devicesList, err := client.GetWatcher().Mongodb().Client.Database(dbName).ListCollectionNames(c, bson.M{"name": bson.M{"$regex": "(PS|PUS)-NIO-"}})
			if err != nil {
				logger.CtxLog(c).Errorf("failed to get collections, err: %v", err)
				a.SendLarkCard(c)
				return
			}
			// 计算一天的算法数据量统计
			err = calculateAlgorithmStats(c, project, start, devicesList)
			if err != nil {
				logger.CtxLog(c).Errorf("fail to calculate algorithm stats, err: %v, activityDO: %s", err, ucmd.ToJsonStrIgnoreErr(a))
				a.SendLarkCard(c)
				return
			}
			// 计算一天的流量用完站点名单
			err = calculateOutOfDataDevices(c, project, start, devicesList)
			if err != nil {
				logger.CtxLog(c).Errorf("fail to calculate out of data devices, err: %v, activityDO: %s", err, ucmd.ToJsonStrIgnoreErr(a))
				a.SendLarkCard(c)
				return
			}
		}
	}
	logger.CtxLog(c).Infof("finish calculate activity, %s", ucmd.ToJsonStrIgnoreErr(a))
}

// 计算算法数据量统计
func calculateAlgorithmStats(c *gin.Context, project string, startDay int64, devicesList []string) (err error) {
	if project != umw.PowerSwap2 && project != umw.PUS3 && project != umw.PUS4 {
		logger.CtxLog(c).Errorf("invalid project: %s", project)
		return fmt.Errorf("invalid project: %s", project)
	}
	endDay := startDay + 24*time.Hour.Milliseconds()
	algMap := make(map[string]mmgo.ActivityStats)
	g := ucmd.NewErrGroup(c, 100)
	mu := sync.Mutex{}
	for _, collName := range devicesList {
		g.GoRecover(func() error {
			pipeline := mongo.Pipeline{
				bson.D{{
					"$match", bson.M{
						"image_gen_time": bson.M{"$gte": startDay, "$lt": endDay},
						"image_type":     bson.M{"$nin": util.GetOperationImageType()},
					},
				}},
				bson.D{{
					"$group", bson.M{
						"_id": "$image_type",
						"upload_total_success": bson.M{
							"$sum": bson.M{
								"$cond": bson.A{
									bson.M{"$eq": bson.A{"$abnormal", false}},
									"$image_size",
									0,
								},
							},
						},
						"upload_total_failure": bson.M{
							"$sum": bson.M{
								"$cond": bson.A{
									bson.M{"$eq": bson.A{"$abnormal", true}},
									"$image_size",
									0,
								},
							},
						},
					},
				}},
				bson.D{{
					"$project", bson.M{
						"image_type":           "$_id",
						"upload_total_success": 1,
						"upload_total_failure": 1,
					},
				}},
			}
			var res []struct {
				ImageType          int32 `json:"image_type" bson:"image_type"`
				UploadTotalSuccess int64 `json:"upload_total_success" bson:"upload_total_success"`
				UploadTotalFailure int64 `json:"upload_total_failure" bson:"upload_total_failure"`
			}
			if gErr := client.GetWatcher().Mongodb().NewMongoEntry().Aggregate(fmt.Sprintf("imageinfo-%s", ucmd.RenameProjectDB(project)), collName, pipeline, &res); gErr != nil {
				logger.CtxLog(c).Errorf("fail to get algorithm stats, err: %v, collection: %s", err, collName)
				return gErr
			}
			mu.Lock()
			defer mu.Unlock()
			for _, item := range res {
				algorithm := GetImageTypeNameMap()[item.ImageType]
				stats := algMap[algorithm]
				stats.UploadTotalSuccess += item.UploadTotalSuccess
				stats.UploadTotalFailure += item.UploadTotalFailure
				algMap[algorithm] = stats
			}
			return nil
		})
	}
	if err = g.Wait(); err != nil {
		logger.CtxLog(c).Errorf("goroutine err: %v", err)
		return
	}
	//fmt.Printf("project: %s, start: %v, end: %v, algMap: %s\n", project, time.UnixMilli(startDay), time.UnixMilli(endDay), ucmd.ToJsonStrIgnoreErr(algMap))

	g.SetLimit(20)
	for algorithm, item := range algMap {
		g.Go(func() error {
			filter := bson.D{
				{"day", startDay},
				{"project", project},
				{"algorithm", algorithm},
			}
			cnt, gErr := client.GetWatcher().Mongodb().NewMongoEntry(filter).Count(umw.Algorithm, CollectionStats)
			if gErr != nil {
				logger.CtxLog(c).Errorf("fail to count algorithm stats, err: %v, filter: %s", gErr, ucmd.ToJsonStrIgnoreErr(filter))
				return gErr
			}
			now := time.Now().UnixMilli()
			update := bson.M{
				"algorithm":            algorithm,
				"project":              project,
				"day":                  startDay,
				"update_ts":            now,
				"date":                 time.UnixMilli(startDay),
				"upload_total_success": item.UploadTotalSuccess,
				"upload_total_failure": item.UploadTotalFailure,
			}
			if cnt == 0 {
				update["insert_ts"] = now
			}
			gErr = client.GetWatcher().Mongodb().NewMongoEntry(filter).UpdateOne(umw.Algorithm, CollectionStats, bson.M{"$set": update}, true, []client.IndexOption{
				{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 180 * 24 * 3600},
				{Name: "project_day_algorithm_unique", Fields: bson.D{{"project", 1}, {"day", -1}, {"algorithm", 1}}, Unique: true},
			}...)
			if gErr != nil {
				logger.CtxLog(c).Errorf("fail to update algorithm stats, err: %v, update: %s", gErr, ucmd.ToJsonStrIgnoreErr(update))
				return gErr
			}
			return nil
		})
	}
	err = g.Wait()
	return
}

// 计算流量用完站点名单
func calculateOutOfDataDevices(c *gin.Context, project string, startDay int64, devicesList []string) (err error) {
	var uploadDataLimit int64
	if project == umw.PowerSwap2 {
		uploadDataLimit = config.Cfg.Welkin.Algorithm.PowerSwap2.UploadDataLimit
	} else if project == umw.PUS3 {
		uploadDataLimit = config.Cfg.Welkin.Algorithm.PUS3.UploadDataLimit
	} else if project == umw.PUS4 {
		uploadDataLimit = config.Cfg.Welkin.Algorithm.PUS4.UploadDataLimit
	} else {
		logger.CtxLog(c).Errorf("invalid project: %s", project)
		return fmt.Errorf("invalid project: %s", project)
	}
	// 转换单位MB->B
	uploadDataLimit *= 1024 * 1024

	yesterday := startDay - 24*time.Hour.Milliseconds()
	endDay := startDay + 24*time.Hour.Milliseconds()
	g := ucmd.NewErrGroup(c, 50)
	for _, deviceId := range devicesList {
		g.GoRecover(func() error {
			pipeline := mongo.Pipeline{
				bson.D{{
					"$match", bson.M{
						"image_gen_time": bson.M{"$gte": startDay, "$lt": endDay},
						"image_type":     bson.M{"$nin": util.GetOperationImageType()},
					},
				}},
				bson.D{{
					"$group", bson.M{
						"_id": primitive.Null{},
						"upload_total": bson.M{
							"$sum": "$image_size",
						},
					},
				}},
			}
			var res []struct {
				UploadTotal int64 `json:"upload_total" bson:"upload_total"`
			}
			if gErr := client.GetWatcher().Mongodb().NewMongoEntry().Aggregate(fmt.Sprintf("imageinfo-%s", ucmd.RenameProjectDB(project)), deviceId, pipeline, &res); gErr != nil {
				logger.CtxLog(c).Errorf("fail to get algorithm stats, err: %v, collection: %s", err, deviceId)
				return gErr
			}
			if len(res) == 0 {
				return nil
			}
			record := mmgo.ActivityDevices{
				Day:         startDay,
				Project:     project,
				DeviceId:    deviceId,
				UploadTotal: res[0].UploadTotal,
				InsertTs:    time.Now().UnixMilli(),
				Date:        time.UnixMilli(startDay),
			}
			if res[0].UploadTotal < uploadDataLimit {
				// 流量未超过限制，将该站的连续天数清零
				record.DurationDay = 0
			} else {
				// 流量超过限制，将该站的连续天数设置为昨天的连续天数加1
				rawData, gErr := client.GetWatcher().Mongodb().NewMongoEntry(bson.D{{"day", yesterday}, {"project", project}, {"device_id", deviceId}}).GetOne(umw.Algorithm, CollectionDevices)
				if gErr != nil {
					logger.CtxLog(c).Errorf("fail to get yesterday algorithm stats, err: %v", gErr)
					return gErr
				}
				var yesterdayRecord mmgo.ActivityDevices
				if rawData == nil {
					record.DurationDay = 1
				} else {
					if gErr = bson.Unmarshal(rawData, &yesterdayRecord); gErr != nil {
						logger.CtxLog(c).Errorf("fail to unmarshal activity device, err: %v", gErr)
						return gErr
					}
					record.DurationDay = yesterdayRecord.DurationDay + 1
				}
			}
			filter := bson.D{
				{"day", startDay},
				{"project", project},
				{"device_id", deviceId},
			}
			gErr := client.GetWatcher().Mongodb().NewMongoEntry(filter).UpdateOne(umw.Algorithm, CollectionDevices, bson.M{"$set": record}, true, []client.IndexOption{
				{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 180 * 24 * 3600},
				{Name: "project_day_duration", Fields: bson.D{{"project", 1}, {"day", -1}, {"duration_day", -1}}},
			}...)
			if gErr != nil {
				logger.CtxLog(c).Errorf("fail to update algorithm stats, err: %v, update: %s", gErr, ucmd.ToJsonStrIgnoreErr(record))
				return gErr
			}
			return nil
		})
	}
	err = g.Wait()
	return
}

// ListActivityStats 查询数据量统计数据
func (a *ActivityDO) ListActivityStats(c *gin.Context, failureValue int) (res []AlgorithmStats, err error) {
	if len(a.Projects) != 1 {
		err = fmt.Errorf("invalid project: %s", ucmd.ToJsonStrIgnoreErr(a.Projects))
		logger.CtxLog(c).Error(err)
		return
	}
	pipeline := mongo.Pipeline{
		{{
			"$match", bson.M{
				"day":     bson.M{"$gte": a.StartTime, "$lte": a.EndTime},
				"project": bson.M{"$in": a.Projects},
			},
		}},
		{{
			"$group", bson.M{
				"_id":                    "$algorithm",
				"upload_total_failure":   bson.M{"$sum": "$upload_total_failure"},
				"upload_total_success":   bson.M{"$sum": "$upload_total_success"},
				"download_total_success": bson.M{"$sum": "$download_total_success"},
				"download_total_failure": bson.M{"$sum": "$download_total_failure"},
			},
		}},
		{{
			"$project", bson.M{
				"algorithm":              "$_id",
				"upload_total_failure":   1,
				"upload_total_success":   1,
				"download_total_success": 1,
				"download_total_failure": 1,
				"upload_total":           bson.M{"$add": bson.A{"$upload_total_failure", "$upload_total_success"}},
				"download_total":         bson.M{"$add": bson.A{"$download_total_success", "$download_total_failure"}},
			},
		}},
	}
	var activityStats []struct {
		Algorithm            string `json:"algorithm" bson:"algorithm"`
		DownloadTotalSuccess int64  `json:"download_total_success" bson:"download_total_success"`
		UploadTotalSuccess   int64  `json:"upload_total_success" bson:"upload_total_success"`
		DownloadTotalFailure int64  `json:"download_total_failure" bson:"download_total_failure"`
		UploadTotalFailure   int64  `json:"upload_total_failure" bson:"upload_total_failure"`
		UploadTotal          int64  `json:"upload_total" bson:"upload_total"`
		DownloadTotal        int64  `json:"download_total" bson:"download_total"`
	}
	err = client.GetWatcher().Mongodb().NewMongoEntry().Aggregate(umw.Algorithm, CollectionStats, pipeline, &activityStats)
	if err != nil {
		logger.CtxLog(c).Errorf("fail to get activities stats, err: %v", err)
		return
	}
	calculateParams := func(upload, download int64) (uploadTotal int64, usageRate float64) {
		if upload == 0 {
			return
		}
		uploadTotal = upload
		usageRate = float64(download) / float64(upload)
		return
	}
	for _, item := range activityStats {
		var upload, download int64
		if failureValue == 0 {
			// 全量
			upload, download = item.UploadTotal, item.DownloadTotal
		} else if failureValue == 1 {
			// 失败
			upload, download = item.UploadTotalFailure, item.DownloadTotalFailure
		} else if failureValue == 2 {
			// 成功
			upload, download = item.UploadTotalSuccess, item.DownloadTotalSuccess
		}
		uploadTotal, usageRate := calculateParams(upload, download)
		if uploadTotal == 0 {
			continue
		}
		res = append(res, AlgorithmStats{
			Algorithm:   item.Algorithm,
			UploadTotal: uploadTotal,
			UsageRate:   usageRate,
		})
	}
	sort.Slice(res, func(i, j int) bool {
		return res[i].Algorithm < res[j].Algorithm
	})
	return
}

// UpdateImageDownloadInfo 更新图片下载信息
func UpdateImageDownloadInfo(c *gin.Context, project string, imageDownloadData []ImageDownloadInfo) {
	g := ucmd.NewErrGroup(c, 50)
	operationImage := make(map[int]bool)
	for _, imageType := range util.GetOperationImageType() {
		operationImage[int(imageType)] = true
	}
	for _, item := range imageDownloadData {
		g.GoRecover(func() error {
			imageType := item.ImageType
			// 不计入运营照片
			if operationImage[imageType] {
				return nil
			}
			algorithm := GetImageTypeNameMap()[int32(imageType)]
			imageGenTime := time.UnixMilli(item.ImageGenTime)
			startDay := time.Date(imageGenTime.Year(), imageGenTime.Month(), imageGenTime.Day(), 0, 0, 0, 0, time.Local)
			filter := bson.D{
				{"day", startDay.UnixMilli()},
				{"project", project},
				{"algorithm", algorithm},
			}
			cnt, gErr := client.GetWatcher().Mongodb().NewMongoEntry(filter).Count(umw.Algorithm, CollectionStats)
			if gErr != nil {
				logger.CtxLog(c).Errorf("fail to count algorithm stats, err: %v, filter: %s", gErr, ucmd.ToJsonStrIgnoreErr(filter))
				return gErr
			}
			now := time.Now().UnixMilli()
			update := bson.M{
				"algorithm": algorithm,
				"project":   project,
				"day":       startDay.UnixMilli(),
				"update_ts": now,
				"date":      startDay,
			}
			if cnt == 0 {
				update["insert_ts"] = now
			}
			inc := make(bson.M)
			if item.Abnormal {
				inc["download_total_failure"] = item.ImageSize
			} else {
				inc["download_total_success"] = item.ImageSize
			}
			gErr = client.GetWatcher().Mongodb().NewMongoEntry(filter).UpdateOne(umw.Algorithm, CollectionStats, bson.M{"$set": update, "$inc": inc}, true, []client.IndexOption{
				{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 180 * 24 * 3600},
				{Name: "project_day_algorithm_unique", Fields: bson.D{{"project", 1}, {"day", -1}, {"algorithm", 1}}, Unique: true},
			}...)
			if gErr != nil {
				logger.CtxLog(c).Errorf("fail to update algorithm stats, err: %v, update: %s", gErr, ucmd.ToJsonStrIgnoreErr(update))
				return gErr
			}
			return nil
		})
	}
	if err := g.Wait(); err != nil {
		logger.CtxLog(c).Errorf("goroutine err: %v", err)
		activityDO := &ActivityDO{
			Projects: []string{project},
		}
		activityDO.SendLarkCard(c)
	}
}
