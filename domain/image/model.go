package image

import umw "git.nevint.com/golang-libs/common-utils/model/welkin"

const (
	// 数据库名
	DbCamera = "camera_management"

	// 数据库表名
	CollectionCameraFault        = "camera_fault"
	CollectionCameraAlarmHistory = "camera_alarm_history"

	// 算法
	AlgorithmBSA  = "BSA"
	AlgorithmBBSA = "BBSA"

	KafkaOperationImageAlarmKey = "operation_img_alarm"

	CCTypeMPC = "CC_MPC"
)

var normalImageCountPS1 = map[string]int{
	AlgorithmBSA:  2,
	AlgorithmBBSA: 2,
}
var normalImageCountPS2 = map[string]int{
	AlgorithmBSA:  2,
	AlgorithmBBSA: 2,
}
var normalImageCountPUS3 = map[string]int{
	AlgorithmBSA:  2,
	AlgorithmBBSA: 4,
}
var normalImageCountPUS4 = map[string]int{
	AlgorithmBSA:  6,
	AlgorithmBBSA: 4,
}

// NormalImageCount 正常服务各种照片的数量
var NormalImageCount = map[string]map[string]int{
	umw.PowerSwap:  normalImageCountPS1,
	umw.PowerSwap2: normalImageCountPS2,
	umw.PUS3:       normalImageCountPUS3,
	umw.PUS4:       normalImageCountPUS4,
}

type CCExtraData struct {
	CCType string `json:"cc_type" bson:"cc_type"`
}

type CameraStatus struct {
	CameraType  string `json:"camera_type"`
	CameraName  string `json:"camera_name"`
	HasImage    bool   `json:"has_image"`
	JudgeResult int64  `json:"judge_result"`
}

type CameraAcceptanceResult struct {
	AcceptancePass bool           `json:"acceptance_pass"`
	UserId         string         `json:"user_id"`
	UserName       string         `json:"user_name"`
	IsOutside      bool           `json:"is_outside"`
	CameraStatus   []CameraStatus `json:"camera_status"`
}

type SendCameraAcceptanceResultRequest struct {
	DeviceId         string   `json:"device_id"`
	Description      string   `json:"description"`
	UserId           string   `json:"user_id"`
	IsOutside        bool     `json:"is_outside"`
	AcceptanceResult []string `json:"acceptance_result"`
	Remark           string   `json:"remark"`
}
