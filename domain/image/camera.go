package image

import (
	"errors"
	"fmt"
	"sort"
	"strings"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	domain_device "git.nevint.com/welkin2/welkin-backend/domain/device"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
)

var CameraAcceptanceReceiverKey = "cameraAcceptance"

type CameraInfo struct {
	DeviceId string
	Project  string
}

var ErrCameraNotVerified = errors.New("camera not verified")

// GetCameraAcceptanceResult 获取摄像头验收结果
func (c *CameraInfo) GetCameraAcceptanceResult(ctx *gin.Context) (acceptanceResult CameraAcceptanceResult, err error) {
	needAcceptance := true
	cameraDeviceInfo, _, err := client.GetWatcher().Mongodb().GetCameraDeviceInfo(c.Project, model.GetCameraDeviceRequest{DeviceId: c.DeviceId, NeedAcceptance: &needAcceptance, PageNo: 1, PageSize: 1}, ucmd.GetArea(), ctx.Query("lang"))
	if err != nil {
		log.CtxLog(ctx).Errorf("get camera device, mongo get camera info incorrect: %v", err)
		return
	}
	if len(cameraDeviceInfo) == 0 {
		log.CtxLog(ctx).Errorf("empty camera device: %s", ucmd.ToJsonStrIgnoreErr(c))
		return
	}
	// 验收发起人
	d := domain_device.Device{
		Project:  c.Project,
		DeviceId: c.DeviceId,
	}
	user, gErr := d.GetCurrentLoginUser(ctx)
	if gErr != nil {
		log.CtxLog(ctx).Errorf("fail to get current login user: %v", gErr)
		return
	}

	acceptanceResult.UserId = user.UserId
	acceptanceResult.UserName = user.Username
	// 四代站存在设备已登录但是天宫没有登录历史的情况，此时发送给设备负责人兜底
	if acceptanceResult.UserId == "" {
		log.CtxLog(ctx).Warnf("GetCameraAcceptanceResult, no user log in device: %s, project: %s", c.DeviceId, c.Project)
		deviceInfo, _ := cache.PowerSwapCache.GetSingleDevice(c.DeviceId)
		if deviceInfo != nil && deviceInfo.DeviceSupervisor != "" {
			acceptanceResult.UserId = deviceInfo.DeviceSupervisor
			acceptanceResult.UserName = ""
		}
	}
	if strings.HasSuffix(user.UserId, ".o") && strings.Count(user.UserId, ".") == 2 {
		acceptanceResult.IsOutside = true
	}
	// 验收通过
	if cameraDeviceInfo[0].CameraTotal == cameraDeviceInfo[0].NormalCameraTotal {
		acceptanceResult.AcceptancePass = true
		return
	}
	for _, record := range cameraDeviceInfo[0].CameraResult {
		if record.JudgeResult == 0 { // 未验证
			log.CtxLog(ctx).Warnf("image not verify: %s", ucmd.ToJsonStrIgnoreErr(cameraDeviceInfo[0]))
			err = ErrCameraNotVerified
			return
		}
		acceptanceResult.CameraStatus = append(acceptanceResult.CameraStatus, CameraStatus{
			CameraType:  record.CameraType,
			CameraName:  record.CameraName,
			HasImage:    record.HasImage,
			JudgeResult: record.JudgeResult,
		})
	}
	cameraInfoMap := make(map[string]bool)
	for _, cs := range acceptanceResult.CameraStatus {
		cameraInfoMap[cs.CameraType] = true
	}
	cameraInfo, err := client.GetWatcher().Mongodb().FindCameraInfo(c.Project, bson.M{})
	if err != nil {
		log.CtxLog(ctx).Errorf("fail to find camera info: %v", err)
		return
	}
	for _, ci := range cameraInfo {
		if cameraInfoMap[ci.CameraType] {
			continue
		}
		cameraName := ci.CameraName
		if ctx.Query("lang") == "en" {
			cameraName = ci.CameraNameEn
		}
		acceptanceResult.CameraStatus = append(acceptanceResult.CameraStatus, CameraStatus{
			CameraType: ci.CameraType,
			CameraName: cameraName,
			HasImage:   false,
		})
	}

	sort.Slice(acceptanceResult.CameraStatus, func(i, j int) bool {
		return acceptanceResult.CameraStatus[i].CameraType < acceptanceResult.CameraStatus[j].CameraType
	})
	return
}

// ResetAcceptance 重置摄像头验收状态
func (c *CameraInfo) ResetAcceptance(ctx *gin.Context) (err error) {
	filter := bson.D{
		{"device_id", c.DeviceId},
	}
	update := bson.M{"$set": bson.M{"need_acceptance": false}}
	return client.GetWatcher().Mongodb().NewMongoEntry(filter).UpdateMany(DbCamera, fmt.Sprintf("%s_device_camera_data", ucmd.RenameProjectDB(c.Project)), update)
}
