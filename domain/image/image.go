package image

import (
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	larkcard "github.com/larksuite/oapi-sdk-go/v3/card"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	"git.nevint.com/golang-libs/common-utils/larkservice"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	"git.nevint.com/welkin2/welkin-backend/domain/service"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"
	"git.nevint.com/welkin2/welkin-backend/util"
)

type Image struct {
	Project    string
	Algorithm  string
	ImageTypes []int
	StartTime  int64
	EndTime    int64
}

func NewImage(project, algorithm string, startTime, endTime int64) *Image {
	imageTypes := util.TurnStrArrToIntArr(strings.Split(umw.AlgorithmImageTypeListMap[algorithm], ","))
	return &Image{
		Project:    project,
		Algorithm:  algorithm,
		ImageTypes: imageTypes,
		StartTime:  startTime,
		EndTime:    endTime,
	}
}

func (i *Image) GetFaultCamera(ctx *gin.Context, alarmThreshold int) {
	srv := service.Service{
		ServiceStartTime: i.StartTime,
		ServiceEndTime:   i.EndTime,
		Project:          i.Project,
	}
	// 获取每个设备的所有服务
	deviceServices, err := srv.GetDeviceServices(ctx)
	if err != nil {
		log.CtxLog(ctx).Errorf("GetFaultCamera, fail to get device services, err: %v, srv: %s", err, ucmd.ToJsonStrIgnoreErr(srv))
		return
	}
	//fmt.Println("deviceServices:", ucmd.ToJsonStrIgnoreErr(deviceServices))
	var newCameraAlarmInfo []mmgo.CameraAlarmInfo
	// 遍历设备
	for _, record := range deviceServices {
		serviceIdList := make([]string, 0)
		for _, item := range record.ServiceList {
			serviceIdList = append(serviceIdList, item.ServiceId)
		}
		// 获取每单服务的照片数量
		serviceImages, cErr := i.CountServiceImage(ctx, record.DeviceId, serviceIdList)
		if cErr != nil {
			log.CtxLog(ctx).Errorf("GetFaultCamera, fail to count service image: %v, record: %s", err, ucmd.ToJsonStrIgnoreErr(record))
			continue
		}
		// 获取当前设备的初始连续异常服务数
		var cameraFault mmgo.CameraFaultData
		filter := bson.D{{"device_id", record.DeviceId}, {"algorithm", i.Algorithm}}
		err = client.GetWatcher().Mongodb().NewMongoEntry(filter).FindOne(DbCamera, CollectionCameraFault, options.FindOne(), &cameraFault)
		if err != nil {
			if errors.Is(err, mongo.ErrNoDocuments) {
				cameraFault = mmgo.CameraFaultData{
					Project:   i.Project,
					DeviceId:  record.DeviceId,
					Algorithm: i.Algorithm,
				}
			} else {
				log.CtxLog(ctx).Errorf("GetFaultCamera, fail to find camera fault data: %v, filter: %s", err, ucmd.ToJsonStrIgnoreErr(filter))
				continue
			}
		}
		//fmt.Println("cameraFault: ", ucmd.ToJsonStrIgnoreErr(cameraFault))
		// 依次判断每单服务上传的照片是否完整
		newFaultServices := make([]mmgo.ServiceBasic, 0)
		for _, serviceInfo := range record.ServiceList {
			//fmt.Println(serviceInfo.ServiceId, serviceImages[serviceInfo.ServiceId])
			if serviceImages[serviceInfo.ServiceId] < NormalImageCount[i.Project][i.Algorithm] {
				// 异常服务
				cameraFault.FaultServiceCount += 1
				cameraFault.FaultServiceList = append(cameraFault.FaultServiceList, serviceInfo)
				newFaultServices = append(newFaultServices, serviceInfo)
			} else {
				// 正常服务
				cameraFault.FaultServiceCount = 0
				cameraFault.FaultServiceList = make([]mmgo.ServiceBasic, 0)
				newFaultServices = make([]mmgo.ServiceBasic, 0)
			}
		}
		cameraFault.UpdateTs = time.Now().UnixMilli()
		err = client.GetWatcher().Mongodb().NewMongoEntry(filter).UpdateOne(DbCamera, CollectionCameraFault, bson.M{"$set": cameraFault}, true, client.IndexOption{
			Name:   "device_algorithm",
			Fields: bson.D{{"device_id", 1}, {"algorithm", 1}},
			Unique: true,
		})
		if err != nil {
			log.CtxLog(ctx).Errorf("GetFaultCamera, fail to update camera fault: %v, record: %s", err, ucmd.ToJsonStrIgnoreErr(cameraFault))
			continue
		}
		if cameraFault.FaultServiceCount > alarmThreshold && len(newFaultServices) > 0 {
			// 需要告警
			newCameraAlarmInfo = append(newCameraAlarmInfo, mmgo.CameraAlarmInfo{
				DeviceId:         record.DeviceId,
				Algorithm:        i.Algorithm,
				FaultServiceList: newFaultServices,
			})
		}
	}
	// 无告警
	if len(newCameraAlarmInfo) == 0 {
		return
	}

	// 发送告警
	alarmTime := time.Now()
	i.SendOperationImageAlarm(ctx, newCameraAlarmInfo)
	i.SendOperationImageKafka(ctx, newCameraAlarmInfo, alarmTime)
	// 记录告警历史
	alarmRecord := mmgo.CameraAlarmHistory{
		Project:   i.Project,
		AlarmInfo: newCameraAlarmInfo,
		AlarmTs:   alarmTime.UnixMilli(),
		Date:      alarmTime,
	}
	err = client.GetWatcher().Mongodb().NewMongoEntry(bson.D{{"_id", alarmRecord.Id}}).InsertOne(DbCamera, CollectionCameraAlarmHistory, alarmRecord, client.IndexOption{
		Name:        "expire_date",
		Fields:      bson.D{{"date", -1}},
		ExpiredTime: 540 * 24 * 3600,
	})
	if err != nil {
		log.CtxLog(ctx).Errorf("GetFaultCamera, fail to insert camera alarm history: %v, record: %s", err, ucmd.ToJsonStrIgnoreErr(alarmRecord))
		return
	}
}

// CountServiceImage 计算单设备某些服务的照片数量
func (i *Image) CountServiceImage(ctx *gin.Context, deviceId string, services []string) (res map[string]int, err error) {
	res = make(map[string]int)
	dbName := fmt.Sprintf("%s-%s", umw.ImageInfo, ucmd.RenameProjectDB(i.Project))
	collName := deviceId
	pipeline := mongo.Pipeline{
		bson.D{{
			"$match", bson.M{
				"image_type": bson.M{"$in": i.ImageTypes},
				"service_id": bson.M{"$in": services},
			},
		}},
		bson.D{{
			"$group", bson.M{
				"_id":   "$service_id",
				"count": bson.M{"$sum": 1},
			},
		}},
		bson.D{{
			"$project", bson.M{
				"service_id": "$_id",
				"count":      1,
			},
		}},
	}
	var serviceCounts []struct {
		ServiceId string `json:"service_id" bson:"service_id"`
		Count     int    `json:"count" bson:"count"`
	}
	err = client.GetWatcher().Mongodb().NewMongoEntry().Aggregate(dbName, collName, pipeline, &serviceCounts)
	if err != nil {
		log.CtxLog(ctx).Errorf("fail to aggregate: %v, pipeline: %s", err, ucmd.ToJsonStrIgnoreErr(pipeline))
		return
	}
	for _, record := range serviceCounts {
		res[record.ServiceId] = record.Count
	}
	return
}

// SendOperationImageAlarm 发送运营照片丢失飞书告警
func (i *Image) SendOperationImageAlarm(ctx *gin.Context, cameraAlarmInfo []mmgo.CameraAlarmInfo) {
	// 发送飞书通知
	ic := larkservice.NewInfoCard()
	ic.HeaderName = fmt.Sprintf("%s换电站%s照片上传失效", i.Project, i.Algorithm)
	ic.HeaderColor = larkcard.TemplateRed
	ic.AppendElement(larkservice.ElementKeyVal, []larkservice.KeyValMessage{
		{Key: "开始时间：", Val: time.UnixMilli(i.StartTime).Format("2006-01-02 15:04:05")},
		{Key: "结束时间：", Val: time.UnixMilli(i.EndTime).Format("2006-01-02 15:04:05")},
	})
	var details []string
	for _, item := range cameraAlarmInfo {
		var serviceList []string
		for _, srv := range item.FaultServiceList {
			serviceList = append(serviceList, srv.ServiceId)
		}
		details = append(details, fmt.Sprintf("**设备id**：%s，缺失照片的服务：%s", item.DeviceId, ucmd.ToJsonStrIgnoreErr(serviceList)))
	}
	ic.AppendElement(larkservice.ElementDetail, []larkservice.DetailMessage{
		{Msg: fmt.Sprintf("**详情**：\n%s", strings.Join(details, "\n\n"))},
	})
	cardContent, cardErr := ic.Build()
	if cardErr != nil {
		log.Logger.Errorf("SendOperationImageAlarm, make image alarm card err: %v", cardErr)
		return
	}
	receivers := config.Cfg.AlarmReceiver[model.OperationImageAlarmKey]
	if err := larkservice.SendAlarmCard(cardContent, receivers); err != nil {
		log.Logger.Errorf("SendOperationImageAlarm, send image alarm card err: %v", err)
		return
	}
}

// SendOperationImageKafka 运营照片丢失告警推送到kafka
func (i *Image) SendOperationImageKafka(ctx *gin.Context, cameraAlarmInfo []mmgo.CameraAlarmInfo, alarmTime time.Time) {
	if i.Algorithm != AlgorithmBBSA {
		return
	}
	resourceIdList := make([]string, 0)
	for _, record := range cameraAlarmInfo {
		deviceInfo, ok := cache.PowerSwapCache.GetSingleDevice(record.DeviceId)
		if !ok {
			continue
		}
		resourceIdList = append(resourceIdList, deviceInfo.ResourceId)
	}
	// BBSA下表面照片告警推送kafka
	url := fmt.Sprintf("%s/plc/broker/v2/welkin-gen/%s", config.Cfg.Welkin.BackendUrl, KafkaOperationImageAlarmKey)
	requestBody := map[string]interface{}{
		"msg_timestamp": alarmTime.UnixMilli(),
		"msg_type":      KafkaOperationImageAlarmKey,
		"data": map[string]interface{}{
			"resource_id_list": resourceIdList,
		},
	}
	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:    url,
		Method: "POST",
		Header: map[string]string{
			"Content-Type": "application/json",
		},
		RequestBody: requestBody,
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		log.Logger.Errorf("SendOperationImageAlarm, send bbsa image alarm to kafka, err: %v, request body: %s", err, ucmd.ToJsonStrIgnoreErr(requestBody))
		return
	}
	defer body.Close()
	data, dErr := io.ReadAll(body)
	if dErr != nil {
		log.Logger.Errorf("SendOperationImageAlarm, send bbsa image alarm to kafka, err: %s, request body: %s", dErr, ucmd.ToJsonStrIgnoreErr(requestBody))
		return
	}
	if statusCode != http.StatusOK {
		log.Logger.Errorf("SendOperationImageAlarm, failed to send bbsa image alarm to kafka, status code: %d, body: %s", statusCode, string(data))
		return
	}
	log.Logger.Infof("SendOperationImageAlarm, send bbsa image alarm to kafka, request body: %v", ucmd.ToJsonStrIgnoreErr(requestBody))
}
