package image

import (
	"fmt"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/mitchellh/mapstructure"
	gocache "github.com/patrickmn/go-cache"

	ucfg "git.nevint.com/golang-libs/common-utils/config"
	ulog "git.nevint.com/golang-libs/common-utils/logger"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"
)

var w client.Watcher
var ctx *gin.Context

func init() {
	os.Setenv("APOLLO_META", "https://apollo-config-cn-stg.nioint.com")
	os.Setenv("ENV", "stg")
	cfg := &ucfg.Config{}
	ucfg.GetConfigApollo("config.json", true, cfg, ucfg.NeedValidate(true), ucfg.ServiceName("ppd-welkin-backend"), ucfg.Cluster(ucfg.GetWelkinApolloCluster()))
	defer ulog.Final()
	cfg.Log.DirPath = "/tmp/logs"
	config.Cfg = cfg
	log.Init(cfg.Log, true)
	logger := log.Logger.Named("activity")
	w = client.NewWatcherByParam(config.Cfg, logger)
	var err error
	if err != nil {
		panic(err)
	}
	cache.CacheRealtimeDataID(w.Mongodb().Client)
	cache.PowerSwapCache = &cache.DeviceInfoCache{
		Cache:         gocache.New(gocache.NoExpiration, gocache.NoExpiration),
		ResourceCache: gocache.New(gocache.NoExpiration, gocache.NoExpiration),
	}
	cache.PowerSwapCache.RefreshDeviceInfoCache(w.Mongodb().Client)
	ctx = &gin.Context{Request: &http.Request{Header: make(http.Header)}}
	ctx.Request.Header.Set("X-Request-ID", "test")
}

type ImageCheck struct {
	AlarmThreshold int `mapstructure:"alarmThreshold"`
	CheckHour      int `mapstructure:"checkHour"`
}

func TestImage_GetFaultCamera(t *testing.T) {
	imageCheckCfg, ok := config.Cfg.ExtraConfig["imageCheck"]
	if !ok {
		t.Errorf("fail to parse imageCheckCfg, expect map[string]interface, get %T", config.Cfg.ExtraConfig["imageCheck"])
		return
	}
	var imageCheck ImageCheck
	err := mapstructure.Decode(imageCheckCfg, &imageCheck)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(imageCheck.CheckHour, imageCheck.AlarmThreshold)
	//i := NewImage(umw.PUS3, AlgorithmBBSA, 1725120000000, 1726296947000)
	//i.GetFaultCamera(ctx, imageCheckCfg["alarmThreshold"])
}

func TestImage_SendOperationImageKafka(t *testing.T) {
	i := Image{
		Algorithm: "BBSA",
	}

	i.SendOperationImageKafka(ctx, []mmgo.CameraAlarmInfo{
		{
			DeviceId:         "PUS-NIO-76590203-99a846b9",
			Algorithm:        "",
			FaultServiceList: nil,
		},
		{
			DeviceId:         "PUS-NIO-ab5c7945-4b51c747",
			Algorithm:        "",
			FaultServiceList: nil,
		},
	}, time.Now())
}
