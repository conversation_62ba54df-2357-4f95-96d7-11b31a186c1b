package service

import (
	"git.nevint.com/welkin2/welkin-backend/domain/common"
	"git.nevint.com/welkin2/welkin-backend/model"
	mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"
	"git.nevint.com/welkin2/welkin-backend/util"
)

func Convert2SatisfyDetailServiceInfo(service Service, commentInfo *Satisfy) SatisfyDetailServiceInfo {
	commentId := ""
	if commentInfo != nil {
		commentId = commentInfo.CommentId
	}
	return SatisfyDetailServiceInfo{
		DeviceId:         service.DeviceId,
		Description:      service.Description,
		OrderStartTime:   service.OrderStartTime,
		OrderEndTime:     service.OrderEndTime,
		ServiceStartTime: service.ServiceStartTime,
		ServiceEndTime:   service.ServiceEndTime,
		OrderId:          service.OrderId,
		ServiceId:        service.ServiceId,
		CommentId:        commentId,
		VehicleId:        service.VehicleId,
		ServiceBatteryId: service.ServiceBatteryId,
		VehicleBatteryId: service.VehicleBatteryId,
		IsReverseSwap:    &service.IsReverseSwap,
		IsAutomatedSwap:  &service.IsAutomatedSwap,
		EvBrand:          service.EvBrand,
		EvType:           service.EvType,
	}
}

func Convert2UserExperienceInfo(service Service) UserExperienceInfo {
	return UserExperienceInfo{
		ElectricityKwh:         util.RoundFloat(service.ElectricityKwh, 2),
		VehicleBatterySoc:      service.EvBatterySoc,
		VehicleBatterySocOss:   service.EvBatteryRealSoc,
		ServiceBatterySoc:      service.ServiceBatterySoc,
		ServiceBatterySocOss:   service.ServiceBatteryRealSoc,
		VehicleBatteryCapacity: common.ConvertBatteryCapacity(service.EvBatteryCapacity),
		ServiceBatteryCapacity: common.ConvertBatteryCapacity(service.ServiceBatteryCapacity),
	}
}

func Convert2CommentInfo(s *Satisfy) CommentInfo {
	return CommentInfo{
		Score:    s.Score,
		UserTag:  s.UserTag,
		Comment:  s.Comment,
		Reason:   s.Reason,
		Solution: s.Solution,
	}
}

func convertServicePO2DO(project string, po mmgo.MongoServiceInfoV2) ServiceDO {
	do := ServiceDO{
		ServiceStartTime:      po.StartTime,
		ServiceEndTime:        po.EndTime,
		Project:               project,
		DeviceId:              po.DeviceId,
		ServiceId:             po.ServiceId,
		Rid:                   po.Rid,
		FinishResult:          po.FinishResult,
		SwapFailCode:          po.SwapFailCode,
		ServiceBatteryId:      po.ServiceBatteryId,
		VehicleBatteryId:      po.EVBatteryId,
		VehicleId:             po.EvId,
		VehicleType:           po.EvType,
		VehicleBrand:          model.VehicleTypeBrandMap[po.EvType],
		VehicleBatterySoc:     po.EvBatterySoc,
		VehicleBatteryRealSoc: po.EvBatteryRealSoc,
		ServiceBatterySoc:     po.ServiceBatterySoc,
		ServiceBatteryRealSoc: po.ServiceBatteryRealSoc,
		VehicleBatteryType:    po.EvBatteryOriginalType,
		ServiceBatteryType:    po.ServiceBatteryOriginalType,
		IsStuck:               po.IsStuck,
	}
	return do
}
