package service

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sort"
	"strings"
	"sync"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	um "git.nevint.com/golang-libs/common-utils/model"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	domain_alarm "git.nevint.com/welkin2/welkin-backend/domain/alarm"
	"git.nevint.com/welkin2/welkin-backend/domain/common"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"
	"git.nevint.com/welkin2/welkin-backend/util"
)

type Service struct {
	ServiceStartTime               int64
	ServiceEndTime                 int64
	Project                        string
	DeviceId                       string
	Description                    string
	OrderStartTime                 int64
	OrderEndTime                   int64
	ServiceId                      string
	OrderId                        string
	Rid                            string
	FinishResult                   *int32
	ServiceBatteryId               string
	VehicleBatteryId               string
	VehicleId                      string
	EvType                         string
	EvBrand                        string
	IsReverseSwap                  bool
	IsAutomatedSwap                bool
	ElectricityKwh                 float64
	EvBatterySoc                   *float32
	EvBatteryRealSoc               *float32
	ServiceBatterySoc              *float32
	ServiceBatteryRealSoc          *float32
	EvOriginalBatteryCapacity      *int32
	EvBatteryCapacity              *int32
	ServiceOriginalBatteryCapacity *int32
	ServiceBatteryCapacity         *int32
	IsStuck                        *bool
}

// GetServicesForTorque 获取计算扭矩所需的服务信息
func (s *Service) GetServicesForTorque(c context.Context, category string, page, size int, needPlc bool) (res []ServiceForTorque, total int, err error) {
	var host string
	if category == "1" {
		host = config.Cfg.Welkin.BackendStgUrl
	} else {
		return nil, 0, fmt.Errorf("invalid category: %s", category)
	}
	if s.DeviceId == "" {
		return nil, 0, fmt.Errorf("must provide device id")
	}
	deviceId := s.DeviceId

	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:    fmt.Sprintf("%s/device/v1/service-info/%s/list?descending=true&start_time=%d&end_time=%d&device_id=%s&page=%d&size=%d", host, s.Project, s.ServiceStartTime, s.ServiceEndTime, deviceId, page, size),
		Method: "GET",
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		log.CtxLog(c).Errorf("fail to request serviceinfo, err: %v, url: %s", err, ct.URL)
		return
	}
	defer body.Close()
	data, err := io.ReadAll(body)
	if err != nil {
		log.CtxLog(c).Errorf("fail to read body serviceinfo, err: %v, url: %s", err, ct.URL)
		return
	}
	if statusCode != http.StatusOK {
		err = fmt.Errorf("fail to request serviceinfo, status code: %d, url: %s", statusCode, ct.URL)
		log.CtxLog(c).Error(err)
		return
	}
	var serviceResp model.ServiceInfoResponse
	if err = json.Unmarshal(data, &serviceResp); err != nil {
		log.CtxLog(c).Errorf("fail to unmarshal serviceinfo, err: %v, url: %s", err, ct.URL)
		return
	}
	if serviceResp.ErrCode != 0 {
		err = fmt.Errorf("fail to get serviceinfo, %s", ucmd.ToJsonStrIgnoreErr(serviceResp))
		log.CtxLog(c).Error(err)
		return
	}
	total = serviceResp.Total
	for _, item := range serviceResp.ServiceInfoData {
		serviceForTorque := ServiceForTorque{
			ServiceId: item.ServiceId,
			StartTime: item.StartTime,
			EndTime:   item.EndTime,
		}
		if item.FinishResult == 0 {
			serviceForTorque.Success = false
		} else {
			serviceForTorque.Success = true
		}

		if needPlc {
			// 调用stg接口获取plc
			ct = ucmd.NewHttpClient(ucmd.HttpClient{
				URL:    fmt.Sprintf("%s/core/plc/plc-record/%s/exists?start_time=%d&end_time=%d&device_id=%s&service_id=%s", host, s.Project, item.StartTime, item.EndTime, deviceId, item.ServiceId),
				Method: "GET",
			})
			body, statusCode, err = ct.Do()
			if err != nil {
				log.CtxLog(c).Errorf("fail to request plc, err: %v, url: %s", err, ct.URL)
				return
			}
			defer body.Close()
			data, err = io.ReadAll(body)
			if err != nil {
				log.CtxLog(c).Errorf("fail to read body plc, err: %v, url: %s", err, ct.URL)
				return
			}
			if statusCode != http.StatusOK {
				err = fmt.Errorf("fail to request plc, status code: %d, url: %s", statusCode, ct.URL)
				log.CtxLog(c).Error(err)
				return
			}
			var resp struct {
				um.Base
				HasPlcRecord bool `json:"has_plc_record"`
			}
			if err = json.Unmarshal(data, &resp); err != nil {
				log.CtxLog(c).Errorf("fail to unmarshal plc, err: %v, url: %s", err, ct.URL)
				return
			}
			if resp.ErrCode != 0 {
				err = fmt.Errorf("fail to get plc, %s", ucmd.ToJsonStrIgnoreErr(resp))
				log.CtxLog(c).Error(err)
				return
			}
			serviceForTorque.HasPlcRecord = resp.HasPlcRecord
		}
		res = append(res, serviceForTorque)
	}
	return
}

// GetDeviceServices 获取所有设备一段时间内的服务
func (s *Service) GetDeviceServices(ctx context.Context) (res []DeviceServices, err error) {
	pipeline := mongo.Pipeline{
		bson.D{{
			"$match", bson.M{
				"date": bson.M{
					"$gte": time.UnixMilli(s.ServiceStartTime),
					"$lt":  time.UnixMilli(s.ServiceEndTime),
				},
				"finish_result": 1,
				"service_id":    bson.M{"$not": bson.M{"$regex": "OFFLINE"}},
			},
		}},
		bson.D{{
			"$sort", bson.M{
				"date": 1,
			},
		}},
		bson.D{{
			"$group", bson.M{
				"_id": "$device_id",
				"service_list": bson.M{
					"$push": bson.M{
						"service_id": "$service_id",
						"start_time": "$service_start_time",
						"end_time":   "$service_end_time",
					},
				},
			},
		}},
		bson.D{{
			"$project", bson.M{
				"device_id":    "$_id",
				"service_list": 1,
			},
		}},
	}
	dbName := fmt.Sprintf("%s-%s", umw.ServiceInfo, ucmd.RenameProjectDB(s.Project))
	collName := util.EncodeDate(time.UnixMilli(s.ServiceStartTime).Format("2006-01-02"))
	err = client.GetWatcher().Mongodb().NewMongoEntry().Aggregate(dbName, collName, pipeline, &res)
	return
}

type ListServiceForSatisfyCond struct {
	Project               string
	ServiceId             []string
	Rid                   []string
	OrderId               []string
	StartTime             int64
	EndTime               int64
	DeviceId              string
	VehicleId             string
	BatteryId             string
	TimeBefore            int64 // 服务开始事件小于TimeBefore
	OrderStartTimeBetween int64 // 框取订单开始和结束时间包含OrderStartTimeBetween的订单
	model.CommonCond
}

// ListServiceForSatisfy 根据id获取服务的详细信息（t+1的订单信息）
func (s *Service) ListServiceForSatisfy(ctx context.Context, cond ListServiceForSatisfyCond) (services []Service, total int64, err error) {
	if cond.Project == "" {
		err = fmt.Errorf("project is required")
		log.CtxLog(ctx).Errorf("ListServiceForSatisfy, %s, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	// 查询服务小结信息
	filter := bson.D{}
	if cond.StartTime != 0 && cond.EndTime != 0 {
		filter = append(filter, bson.E{Key: "service_start_time", Value: bson.M{"$gte": cond.StartTime, "$lt": cond.EndTime}})
	}
	if cond.TimeBefore != 0 {
		filter = append(filter, bson.E{Key: "service_start_time", Value: bson.M{"$lte": cond.TimeBefore}})
	}
	if cond.OrderStartTimeBetween != 0 {
		serviceIds := make([]string, 0)
		collName := fmt.Sprintf("%s_%s", CollectionOrderInfo, strings.ToLower(cond.Project))
		var orderInfo []mmgo.OrderInfo
		_, err = client.GetWatcher().PLCMongodb().NewMongoEntry(bson.D{
			{"order_start_time", bson.M{"$lt": cond.OrderStartTimeBetween}},
			{"order_end_time", bson.M{"$gt": cond.OrderStartTimeBetween}},
			{"device_id", cond.DeviceId},
		}).FindMany(umw.ServiceInfo, collName, options.Find(), &orderInfo)
		if err != nil {
			log.CtxLog(ctx).Errorf("ListServiceForSatisfy, fail to get %s: %v, cond: %s", collName, err, ucmd.ToJsonStrIgnoreErr(cond))
			return
		}
		for _, item := range orderInfo {
			serviceIds = append(serviceIds, item.ServiceId)
		}
		filter = append(filter, bson.E{Key: "service_id", Value: bson.M{"$in": serviceIds}})
	}
	if cond.DeviceId != "" {
		filter = append(filter, bson.E{Key: "device_id", Value: cond.DeviceId})
	}
	if cond.VehicleId != "" {
		filter = append(filter, bson.E{Key: "ev_id", Value: cond.VehicleId})
	}
	if cond.BatteryId != "" {
		filter = append(filter, bson.E{Key: "$or", Value: bson.A{
			bson.D{{"ev_battery_id", cond.BatteryId}},
			bson.D{{"service_battery_id", cond.BatteryId}},
		}})
	}
	if len(cond.ServiceId) != 0 {
		filter = append(filter, bson.E{Key: "service_id", Value: bson.M{"$in": cond.ServiceId}})
	}
	if len(cond.Rid) != 0 {
		filter = append(filter, bson.E{Key: "rid", Value: bson.M{"$in": cond.Rid}})
	}
	// 服务小结中没有上传order_id，需要通过数仓同步表映射到service_id
	if len(cond.OrderId) != 0 {
		serviceIds := make([]string, 0)
		collName := fmt.Sprintf("%s_%s", CollectionOrderInfo, strings.ToLower(cond.Project))
		var orderInfo []mmgo.OrderInfo
		_, err = client.GetWatcher().PLCMongodb().NewMongoEntry(bson.D{{"_id", bson.M{"$in": cond.OrderId}}}).FindMany(umw.ServiceInfo, collName, options.Find(), &orderInfo)
		if err != nil {
			log.CtxLog(ctx).Errorf("ListServiceForSatisfy, fail to get %s: %v, cond: %s", collName, err, ucmd.ToJsonStrIgnoreErr(cond))
			return
		}
		for _, item := range orderInfo {
			serviceIds = append(serviceIds, item.ServiceId)
		}
		filter = append(filter, bson.E{Key: "service_id", Value: bson.M{"$in": serviceIds}})
	}
	if len(filter) == 0 {
		err = fmt.Errorf("must provide at least one filter field")
		log.CtxLog(ctx).Errorf("ListServiceForSatisfy, %s, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	opts := options.Find().SetSort(bson.M{"service_start_time": -1})
	if cond.Page != 0 && cond.Size != 0 {
		opts = opts.SetSkip((cond.Page - 1) * cond.Size).SetLimit(cond.Size)
	}
	var serviceInfo []mmgo.MongoServiceInfoV2
	total, err = client.GetWatcher().Mongodb().NewMongoEntry(filter).FindMany(umw.ServiceInfo, ucmd.RenameProjectDB(cond.Project), opts, &serviceInfo)
	if err != nil {
		log.CtxLog(ctx).Errorf("ListServiceForSatisfy, fail to get service info: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	serviceInfoMap := make(map[string]mmgo.MongoServiceInfoV2)
	serviceIds := make([]string, 0)
	for _, record := range serviceInfo {
		serviceIds = append(serviceIds, record.ServiceId)
		serviceInfoMap[record.ServiceId] = record
	}

	// 查询订单信息
	collName := fmt.Sprintf("%s_%s", CollectionOrderInfo, strings.ToLower(cond.Project))
	var orderInfo []mmgo.OrderInfo
	_, err = client.GetWatcher().PLCMongodb().NewMongoEntry(bson.D{{"service_id", bson.M{"$in": serviceIds}}}).FindMany(umw.ServiceInfo, collName, options.Find(), &orderInfo)
	if err != nil {
		log.CtxLog(ctx).Errorf("ListServiceForSatisfy, fail to get %s: %v, cond: %s", collName, err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}

	for _, record := range orderInfo {
		srvInfo := serviceInfoMap[record.ServiceId]
		srv := Service{
			ServiceStartTime:      srvInfo.StartTime,
			ServiceEndTime:        srvInfo.EndTime,
			Project:               cond.Project,
			DeviceId:              srvInfo.DeviceId,
			OrderStartTime:        record.OrderStartTime,
			OrderEndTime:          record.OrderEndTime,
			ServiceId:             srvInfo.ServiceId,
			OrderId:               record.Id,
			Rid:                   record.Rid,
			FinishResult:          srvInfo.FinishResult,
			ServiceBatteryId:      srvInfo.ServiceBatteryId,
			VehicleBatteryId:      srvInfo.EVBatteryId,
			VehicleId:             srvInfo.EvId,
			EvType:                srvInfo.EvType,
			EvBrand:               model.VehicleTypeBrandMap[srvInfo.EvType],
			IsReverseSwap:         record.IsReverseSwap,
			IsAutomatedSwap:       record.IsAutomatedSwap,
			ElectricityKwh:        record.ElectricityKwh,
			EvBatterySoc:          srvInfo.EvBatterySoc,
			EvBatteryRealSoc:      srvInfo.EvBatteryRealSoc,
			ServiceBatterySoc:     srvInfo.ServiceBatterySoc,
			ServiceBatteryRealSoc: srvInfo.ServiceBatteryRealSoc,
			IsStuck:               srvInfo.IsStuck,
		}
		deviceInfo, ok := cache.PowerSwapCache.GetSingleDevice(record.DeviceId)
		if ok {
			srv.Description = deviceInfo.Description
		}
		// 数仓中的order_end_time可能为0
		if srv.OrderEndTime == 0 {
			srv.OrderEndTime = srv.ServiceEndTime
		}
		_, evBatteryCapacity := common.ConvertBatteryType(srvInfo.EvBatteryOriginalType)
		_, serviceBatteryCapacity := common.ConvertBatteryType(srvInfo.ServiceBatteryOriginalType)
		srv.EvBatteryCapacity = evBatteryCapacity
		srv.EvOriginalBatteryCapacity = srvInfo.EvBatteryOriginalType
		srv.ServiceBatteryCapacity = serviceBatteryCapacity
		srv.ServiceOriginalBatteryCapacity = srvInfo.ServiceBatteryOriginalType
		services = append(services, srv)
	}
	sort.Slice(services, func(i, j int) bool {
		return services[i].ServiceStartTime > services[j].ServiceStartTime
	})
	return
}

// ListServiceAlarms 列出服务内告警
func (s *Service) ListServiceAlarms(ctx context.Context) (res []domain_alarm.AlarmDO, err error) {
	alarm := &domain_alarm.AlarmDO{}
	res, _, err = alarm.ListAlarms(ctx, domain_alarm.ListAlarmCond{
		StartTs:  s.ServiceStartTime,
		EndTs:    s.ServiceEndTime,
		Project:  s.Project,
		DeviceId: &s.DeviceId,
	})
	if err != nil {
		log.CtxLog(ctx).Errorf("ListServiceAlarms, %v", err)
		return
	}
	return
}

func (s *Service) GetSatisfyDiagnoseConfig(project string) (res mmgo.SatisfyDiagnosisConfig, err error) {
	err = client.GetWatcher().Mongodb().NewMongoEntry(bson.D{{"config", project}}).FindOne(umw.ServiceInfo, "satisfy_diagnosis_config", options.FindOne(), &res)
	return
}

// GetSatisfyDiagnoseResult 获取诊断数据
func (s *Service) GetSatisfyDiagnoseResult(ctx context.Context) (res *SatisfyDiagnoseResult, resService *Service, err error) {
	if s.OrderId == "" || s.Project == "" {
		err = fmt.Errorf("order_id and project is needed")
		log.CtxLog(ctx).Errorf("GetSatisfyDiagnoseResult, %v", err)
		return
	}
	// 获取服务信息
	services, _, err := s.ListServiceForSatisfy(ctx, ListServiceForSatisfyCond{
		Project: s.Project,
		OrderId: []string{s.OrderId},
	})
	if err != nil {
		log.CtxLog(ctx).Errorf("GetSatisfyDiagnoseResult, %v", err)
		return
	}
	if len(services) == 0 {
		log.CtxLog(ctx).Warnf("GetSatisfyDiagnoseResult, cannot find service, order_id: %s, project: %s", s.OrderId, s.Project)
		return nil, nil, nil
	}
	resService = &services[0]
	satisfyDiagnosisConfig, _ := s.GetSatisfyDiagnoseConfig(s.Project)
	res = &SatisfyDiagnoseResult{}

	g := ucmd.NewErrGroup(ctx)
	mu := sync.Mutex{}
	for tag := range SatisfyDiagnosisTags {
		serviceInfo := services[0]
		g.GoRecover(func() error {
			switch tag {
			case DiagnosisTagSwapTimeLong:
				if serviceInfo.ServiceEndTime-serviceInfo.ServiceStartTime <= satisfyDiagnosisConfig.ServiceTimeThreshold {
					return nil
				}
				mu.Lock()
				defer mu.Unlock()
				res.SwapTime = &SatisfySwapTime{
					ServiceStartTime: serviceInfo.ServiceStartTime,
					ServiceEndTime:   serviceInfo.ServiceEndTime,
				}
			case DiagnosisTagSwapFail:
				if !((serviceInfo.FinishResult != nil && *serviceInfo.FinishResult == 0) || (serviceInfo.IsStuck != nil && *serviceInfo.IsStuck)) {
					// 正常换电，直接返回
					return nil
				}
				alarmList, gErr := serviceInfo.ListServiceAlarms(ctx)
				if gErr != nil {
					log.CtxLog(ctx).Errorf("GetSatisfyDiagnoseResult, %v, tag: %s", gErr, tag)
					return gErr
				}
				alarms := make([]SatisfyAlarmInfo, 0)
				for _, alarmInfo := range alarmList {
					satisfyAlarm := SatisfyAlarmInfo{
						AlarmType:         alarmInfo.AlarmType,
						DataIdDescription: alarmInfo.DataIdDescription,
						DataId:            alarmInfo.DataId,
						IsStuck:           alarmInfo.IsStuck,
						AlarmLevel:        alarmInfo.AlarmLevel,
						CreateTs:          alarmInfo.CreateTs,
						ClearTs:           alarmInfo.ClearTs,
						DeviceId:          alarmInfo.DeviceId,
						State:             alarmInfo.State,
					}
					deviceInfo, ok := cache.PowerSwapCache.GetSingleDevice(satisfyAlarm.DeviceId)
					if ok {
						satisfyAlarm.DeviceName = deviceInfo.Description
					}
					alarms = append(alarms, satisfyAlarm)
				}
				mu.Lock()
				defer mu.Unlock()
				res.SwapFail = &SatisfySwapFail{
					AlarmList:    alarms,
					FinishResult: serviceInfo.FinishResult,
					IsStuck:      serviceInfo.IsStuck,
				}
			case DiagnosisTagSwapQueueTimeLong:
				if serviceInfo.ServiceStartTime-serviceInfo.OrderStartTime <= satisfyDiagnosisConfig.QueueTimeThreshold {
					return nil
				}
				mu.Lock()
				defer mu.Unlock()
				res.SwapQueueTime = &SatisfySwapQueueTime{
					OrderTime: serviceInfo.OrderStartTime,
					CallTime:  serviceInfo.ServiceStartTime,
				}
			case DiagnosisTagPreorderSwapTimeLong:
				// 前序服务
				// prev.order_end_time > curr.order_start_time && prev.order_start_time < curr.order_start_time
				preorderServices, _, gErr := s.ListServiceForSatisfy(ctx, ListServiceForSatisfyCond{
					Project:               s.Project,
					DeviceId:              serviceInfo.DeviceId,
					OrderStartTimeBetween: serviceInfo.OrderStartTime,
				})
				if gErr != nil {
					log.CtxLog(ctx).Errorf("GetSatisfyDiagnoseResult, %v, tag: %s", gErr, tag)
					return gErr
				}
				timeLong := false
				var satisfyOrderInfo []SatisfyOrderInfo
				for i, record := range preorderServices {
					satisfyOrderInfo = append(satisfyOrderInfo, SatisfyOrderInfo{
						Id:               i + 1,
						ServiceStartTime: record.ServiceStartTime,
						ServiceEndTime:   record.ServiceEndTime,
						OrderStartTime:   record.OrderStartTime,
						OrderEndTime:     record.OrderEndTime,
						ServiceId:        record.ServiceId,
						OrderId:          record.OrderId,
						Project:          record.Project,
						VehicleId:        record.VehicleId,
					})
					if record.ServiceEndTime-record.ServiceStartTime > satisfyDiagnosisConfig.ServiceTimeThreshold {
						timeLong = true
					}
				}
				if !timeLong {
					return nil
				}
				sort.Slice(satisfyOrderInfo, func(i, j int) bool {
					return satisfyOrderInfo[i].ServiceStartTime < satisfyOrderInfo[j].ServiceStartTime
				})
				// 当前服务本身
				satisfyOrderInfo = append(satisfyOrderInfo, SatisfyOrderInfo{
					Id:               0,
					ServiceStartTime: serviceInfo.ServiceStartTime,
					ServiceEndTime:   serviceInfo.ServiceEndTime,
					OrderStartTime:   serviceInfo.OrderStartTime,
					OrderEndTime:     serviceInfo.OrderEndTime,
					ServiceId:        serviceInfo.ServiceId,
					OrderId:          serviceInfo.OrderId,
					Project:          serviceInfo.Project,
					VehicleId:        serviceInfo.VehicleId,
				})
				mu.Lock()
				defer mu.Unlock()
				res.PreorderSwapTime = satisfyOrderInfo
			case DiagnosisTagPreorderSwapFail:
				// 前序服务
				preorderServices, _, gErr := s.ListServiceForSatisfy(ctx, ListServiceForSatisfyCond{
					Project:               s.Project,
					DeviceId:              serviceInfo.DeviceId,
					OrderStartTimeBetween: serviceInfo.OrderStartTime,
				})
				if gErr != nil {
					log.CtxLog(ctx).Errorf("GetSatisfyDiagnoseResult, %v, tag: %s", gErr, tag)
					return gErr
				}
				for _, record := range preorderServices {
					if !((record.FinishResult != nil && *record.FinishResult == 0) || (record.IsStuck != nil && *record.IsStuck)) {
						// 正常换电，直接返回
						continue
					}
					var alarmList []domain_alarm.AlarmDO
					alarmList, gErr = record.ListServiceAlarms(ctx)
					if gErr != nil {
						log.CtxLog(ctx).Errorf("GetSatisfyDiagnoseResult, %v, tag: %s", gErr, tag)
						return gErr
					}
					var alarmListVO []SatisfyAlarmInfo
					for _, alarm := range alarmList {
						satisfyAlarm := SatisfyAlarmInfo{
							AlarmType:         alarm.AlarmType,
							DataIdDescription: alarm.DataIdDescription,
							DataId:            alarm.DataId,
							IsStuck:           alarm.IsStuck,
							AlarmLevel:        alarm.AlarmLevel,
							CreateTs:          alarm.CreateTs,
							ClearTs:           alarm.ClearTs,
							DeviceId:          alarm.DeviceId,
							State:             alarm.State,
						}
						deviceInfo, ok := cache.PowerSwapCache.GetSingleDevice(satisfyAlarm.DeviceId)
						if ok {
							satisfyAlarm.DeviceName = deviceInfo.Description
						}
						alarmListVO = append(alarmListVO, satisfyAlarm)
					}
					mu.Lock()
					res.PreorderSwapFail = append(res.PreorderSwapFail, SatisfyServiceAlarmInfo{
						ServiceId:    record.ServiceId,
						FinishResult: record.FinishResult,
						IsStuck:      record.IsStuck,
						VehicleId:    record.VehicleId,
						AlarmList:    alarmListVO,
					})
					mu.Unlock()
				}
			case DiagnosisTagMultipleSwap:
				multiServices, _, gErr := s.ListServiceForSatisfy(ctx, ListServiceForSatisfyCond{
					Project:   s.Project,
					DeviceId:  serviceInfo.DeviceId,
					StartTime: serviceInfo.ServiceStartTime - 6*time.Hour.Milliseconds(),
					EndTime:   serviceInfo.ServiceEndTime,
				})
				if gErr != nil {
					log.CtxLog(ctx).Errorf("GetSatisfyDiagnoseResult, %v", gErr)
					return gErr
				}
				var satisfyOrderInfo []SatisfyOrderInfo
				intervalMap := make(map[string]int)
				currServiceId := ""
				for _, record := range multiServices {
					// 找到同一辆车的换电订单
					if record.VehicleId == serviceInfo.VehicleId {
						currServiceId = record.ServiceId
						satisfyOrderInfo = append(satisfyOrderInfo, SatisfyOrderInfo{
							ServiceStartTime: record.ServiceStartTime,
							ServiceEndTime:   record.ServiceEndTime,
							OrderStartTime:   record.OrderStartTime,
							OrderEndTime:     record.OrderEndTime,
							ServiceId:        record.ServiceId,
							OrderId:          record.OrderId,
							Project:          record.Project,
							FinishResult:     record.FinishResult,
						})
					} else {
						intervalMap[currServiceId]++
					}
				}
				if len(satisfyOrderInfo) <= 1 {
					return nil
				}
				for i := 0; i < len(satisfyOrderInfo)-1; i++ {
					satisfyOrderInfo[i].Interval = intervalMap[satisfyOrderInfo[i].ServiceId]
				}
				mu.Lock()
				defer mu.Unlock()
				res.MultipleSwap = satisfyOrderInfo
			case DiagnosisTagBattery70:
				if serviceInfo.ServiceBatteryCapacity != nil && *serviceInfo.ServiceBatteryCapacity == common.Capacity70kwh {
					mu.Lock()
					defer mu.Unlock()
					res.Battery70 = "70"
				}
			default:
				log.CtxLog(ctx).Warnf("GetSatisfyDiagnoseResult, invalid tag: %s", tag)
			}
			return nil
		})
	}

	if err = g.Wait(); err != nil {
		log.CtxLog(ctx).Errorf("GetSatisfyDiagnoseResult, gouroutine err: %v", err)
		return
	}
	satisfy := &Satisfy{OrderId: s.OrderId}
	satisfy, err = satisfy.GetSatisfyDataById(ctx)
	if satisfy != nil && satisfy.FinalL3Label != "" {
		res.DiagnosisResult = map[string]string{
			"l1_label": satisfy.FinalL1Label,
			"l2_label": satisfy.FinalL2Label,
			"l3_label": satisfy.FinalL3Label,
		}

		data, getSatisfyDiyLabelErr := client.GetSatisfyDiyLabel(ctx)
		if getSatisfyDiyLabelErr != nil {
			log.CtxLog(ctx).Errorf("d.getDiyLabel err. err:%v", getSatisfyDiyLabelErr)
			return
		}
		l3L2Map := map[string]string{}
		for _, l2l3 := range data {
			for l2, l3 := range l2l3 {
				for _, v := range l3 {
					l3L2Map[v] = l2
				}
			}
		}
		l2l3 := map[string][]string{}
		for l3, value := range satisfy.L3Labels {
			if value == 0 {
				continue
			}
			l2l3[l3L2Map[l3]] = append(l2l3[l3L2Map[l3]], l3)
		}
		if len(l2l3) == 0 {
			l2l3["未知"] = []string{"未知"}
		}
		res.DiagnosisLabel = l2l3
	}

	return
}
