package service

import (
	"fmt"
	"net/http"
	"os"
	"testing"

	"github.com/gin-gonic/gin"
	gocache "github.com/patrickmn/go-cache"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	ulog "git.nevint.com/golang-libs/common-utils/logger"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	log "git.nevint.com/welkin2/welkin-backend/logger"
)

var w client.Watcher
var ctx *gin.Context

func init() {
	os.Setenv("APOLLO_META", "https://apollo-config-cn-stg.nioint.com")
	os.Setenv("ENV", "stg")
	cfg := &ucfg.Config{}
	ucfg.GetConfigApollo("config.json", true, cfg, ucfg.NeedValidate(true), ucfg.ServiceName("ppd-welkin-backend"), ucfg.Cluster(ucfg.GetWelkinApolloCluster()))
	defer ulog.Final()
	cfg.Log.DirPath = "/tmp/logs"
	config.Cfg = cfg
	log.Init(cfg.Log, true)
	logger := log.Logger.Named("activity")
	w = client.NewWatcherByParam(config.Cfg, logger)
	var err error
	if err != nil {
		panic(err)
	}
	cache.CacheRealtimeDataID(w.Mongodb().Client)
	cache.PowerSwapCache = &cache.DeviceInfoCache{
		Cache:         gocache.New(gocache.NoExpiration, gocache.NoExpiration),
		ResourceCache: gocache.New(gocache.NoExpiration, gocache.NoExpiration),
	}
	cache.PowerSwapCache.RefreshDeviceInfoCache(w.Mongodb().Client)
	ctx = &gin.Context{Request: &http.Request{Header: make(http.Header)}}
	ctx.Request.Header.Set("X-Request-ID", "test")
}

func TestService_GetDeviceServices(t *testing.T) {
	srv := Service{
		ServiceStartTime: 1725120000000,
		ServiceEndTime:   1726296947000,
		Project:          umw.PUS3,
	}
	res, err := srv.GetDeviceServices(ctx)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(ucmd.ToJsonStrIgnoreErr(res))
}

func TestService_ListServiceDetailById(t *testing.T) {
	srv := &Service{}
	//serviceId := []string{"PS-NIO-caca52d7-12e2ee5c3f39257f997443e826164816500010201726761577036", "PS-NIO-1c7e3ab8-6c8fe24771896a927c8540fe9b7c54026f18cc641726761586418"}
	//rid := []string{"ph_243561461607042", "ph_243561435829030"}
	//orderId := []string{"825281243561461370", "825548243561435566"}
	orderId := []string{"825174243335800983"}
	cond := ListServiceForSatisfyCond{
		Project:   umw.PowerSwap2,
		ServiceId: nil,
		Rid:       nil,
		OrderId:   orderId,
		//TimeBefore: 1726761577036,
		//CommonCond: model.CommonCond{Size: 5, Page: 1},
	}
	res, total, err := srv.ListServiceForSatisfy(ctx, cond)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(total, ucmd.ToJsonStrIgnoreErr(res))
}
