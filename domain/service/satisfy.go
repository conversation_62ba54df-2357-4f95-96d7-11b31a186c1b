package service

import (
	"context"
	"errors"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	"git.nevint.com/golang-libs/common-utils/larkservice"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"
	"git.nevint.com/welkin2/welkin-backend/util"
)

type Satisfy struct {
	CommentId        string
	ServiceId        string
	OrderId          string
	CommentTime      int64
	UserTag          []string
	DiagnosisTag     []string
	Score            int
	DeviceId         string
	Description      string
	Project          string
	IsValid          bool
	Comment          string
	Reason           string
	Solution         string
	L3Labels         map[string]int64
	FinalL1Label     string
	FinalL2Label     string
	FinalL3Label     string
	CityCompany      string
	CityCompanyGroup string
	CarPlatform      string
	ReportStatus     string
}

// GetSatisfyDataById 根据id获取满意度评价数据详情
func (s *Satisfy) GetSatisfyDataById(ctx context.Context) (res *Satisfy, err error) {
	filter := bson.D{}
	if s.CommentId != "" {
		filter = append(filter, bson.E{Key: "_id", Value: s.CommentId})
	}
	if s.OrderId != "" {
		filter = append(filter, bson.E{Key: "order_id", Value: s.OrderId})
	}
	if len(filter) == 0 {
		err = fmt.Errorf("must provide at least one filter field")
		log.CtxLog(ctx).Errorf("GetSatisfyDataById, %s, cond: %s", err, ucmd.ToJsonStrIgnoreErr(s))
		return
	}
	var satisfyData mmgo.SwapSatisfyData
	err = client.GetWatcher().PLCMongodb().NewMongoEntry(filter).FindOne(umw.ServiceInfo, CollectionSatisfyData, options.FindOne(), &satisfyData)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			log.CtxLog(ctx).Infof("GetSatisfyDataById, no satisfy data, %s", ucmd.ToJsonStrIgnoreErr(filter))
			return res, nil
		}
		log.CtxLog(ctx).Errorf("GetSatisfyDataById, fail to get satisfy data: %v, filter: %s", err, ucmd.ToJsonStrIgnoreErr(filter))
		return
	}
	res = &Satisfy{
		CommentId:        satisfyData.Id,
		ServiceId:        satisfyData.ServiceId,
		OrderId:          satisfyData.OrderId,
		CommentTime:      satisfyData.CommentTime,
		UserTag:          satisfyData.UserTag,
		DiagnosisTag:     satisfyData.DiagnosisTag,
		Score:            satisfyData.Score,
		DeviceId:         satisfyData.DeviceId,
		Project:          satisfyData.Project,
		IsValid:          satisfyData.IsValid,
		Comment:          satisfyData.Comment,
		Reason:           satisfyData.Reason,
		Solution:         satisfyData.Solution,
		L3Labels:         satisfyData.L3Labels,
		FinalL1Label:     satisfyData.FinalL1Label,
		FinalL2Label:     satisfyData.FinalL2Label,
		FinalL3Label:     satisfyData.FinalL3Label,
		CityCompany:      satisfyData.CityCompany,
		CityCompanyGroup: satisfyData.CityCompanyGroup,
		CarPlatform:      satisfyData.VehiclePlatform,
		ReportStatus:     satisfyData.ReportStatus,
	}
	deviceInfo, ok := cache.PowerSwapCache.GetSingleDevice(satisfyData.DeviceId)
	if ok {
		res.Description = deviceInfo.Description
	}
	return
}

// GetServiceInfoByCommentId 根据评价id获取服务相关信息
func (s *Satisfy) GetServiceInfoByCommentId(ctx context.Context) (res Service, err error) {
	if s.CommentId == "" {
		err = fmt.Errorf("empty comment id")
		log.CtxLog(ctx).Errorf("GetServiceInfoByCommentId, %v", err)
		return
	}
	satisfyData, err := s.GetSatisfyDataById(ctx)
	if err != nil {
		log.CtxLog(ctx).Errorf("GetIdsByCommentId, %v, commentId: %s", err, s.CommentId)
		return
	}
	srv := &Service{}
	services, _, err := srv.ListServiceForSatisfy(ctx, ListServiceForSatisfyCond{
		Project:   res.Project,
		ServiceId: []string{satisfyData.ServiceId},
	})
	if err != nil {
		log.CtxLog(ctx).Errorf("GetIdsByCommentId, %v, commentId: %s", err, s.CommentId)
		return
	}
	if len(services) > 0 {
		res = services[0]
	}
	if res.OrderId == "" || res.Rid == "" || res.ServiceId == "" {
		err = fmt.Errorf("get empty id")
		log.CtxLog(ctx).Errorf("GetIdsByCommentId, %v, orderId: %s, rid: %s, serviceId: %s", err, res.OrderId, res.Rid, res.ServiceId)
		return
	}
	return
}

// ListSatisfyData 获取满意度评价列表
func (s *Satisfy) ListSatisfyData(ctx context.Context, cond ListSatisfyRequest) (res []Satisfy, total int64, err error) {
	filter := bson.D{}
	if cond.StartTime != 0 && cond.EndTime != 0 {
		filter = append(filter, bson.E{Key: "comment_time", Value: bson.M{"$gte": cond.StartTime, "$lt": cond.EndTime}})
	}
	if cond.DiagnosisTag != "" {
		filter = append(filter, bson.E{Key: "diagnosis_tag", Value: bson.M{"$in": strings.Split(cond.DiagnosisTag, ",")}})
	}
	if cond.UserTag != "" {
		filter = append(filter, bson.E{Key: "user_tag", Value: bson.M{"$in": strings.Split(cond.UserTag, ",")}})
	}
	if cond.DeviceId != "" {
		filter = append(filter, bson.E{Key: "device_id", Value: cond.DeviceId})
	}
	if cond.DeviceIds != "" {
		filter = append(filter, bson.E{Key: "device_id", Value: bson.M{"$in": strings.Split(cond.DeviceIds, ",")}})
	}
	if cond.Project != "" {
		filter = append(filter, bson.E{Key: "project", Value: cond.Project})
	}
	if cond.Score != "" {
		scores := util.TurnStrArrToIntArr(strings.Split(cond.Score, ","))
		filter = append(filter, bson.E{Key: "score", Value: bson.M{"$in": scores}})
	}
	if cond.OrderId != "" {
		filter = append(filter, bson.E{Key: "order_id", Value: cond.OrderId})
	}
	if cond.IsValid != nil {
		filter = append(filter, bson.E{Key: "is_valid", Value: *cond.IsValid})
	}
	if cond.ServiceId != "" {
		filter = append(filter, bson.E{Key: "service_id", Value: cond.ServiceId})
	}
	if cond.BatteryId != "" {
		filter = append(filter, bson.E{Key: "$or", Value: bson.A{
			bson.D{{"ev_battery_id", cond.BatteryId}},
			bson.D{{"service_battery_id", cond.BatteryId}},
		}})
	}
	if cond.VehicleId != "" {
		filter = append(filter, bson.E{Key: "ev_id", Value: cond.VehicleId})
	}
	if cond.EvBrand != "" {
		evBrands := strings.Split(cond.EvBrand, ",")
		evTypes := make([]string, 0)
		for _, brand := range evBrands {
			evTypes = append(evTypes, model.VehicleBrandMap[brand]...)
		}
		filter = append(filter, bson.E{Key: "ev_type", Value: bson.M{"$in": evTypes}})
	}
	if cond.EvType != "" {
		filter = append(filter, bson.E{Key: "ev_type", Value: bson.M{"$in": strings.Split(cond.EvType, ",")}})
	}
	if cond.Labels != "" {
		labels := strings.Split(cond.Labels, ",")
		l3Labels := []string{}
		for _, label := range labels {
			l1l2l3 := strings.Split(label, "-")
			if len(l1l2l3) != 3 {
				continue
			}
			l3Labels = append(l3Labels, l1l2l3[2])
		}
		filter = append(filter, bson.E{Key: "final_l3_label", Value: bson.M{"$in": l3Labels}})
	}
	if cond.CityCompanies != "" {
		filter = append(filter, bson.E{Key: "city_company", Value: bson.M{"$in": strings.Split(cond.CityCompanies, ",")}})
	}
	if cond.CityCompanyGroups != "" {
		filter = append(filter, bson.E{Key: "city_company_group", Value: bson.M{"$in": strings.Split(cond.CityCompanyGroups, ",")}})
	}
	if cond.CarPlatform != "" {
		filter = append(filter, bson.E{Key: "vehicle_platform", Value: bson.M{"$in": strings.Split(cond.CarPlatform, ",")}})
	}
	if len(filter) == 0 {
		err = fmt.Errorf("must provide at least one filter field")
		log.CtxLog(ctx).Errorf("ListSatisfyData, %s, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	opts := options.Find().SetSort(bson.M{"comment_time": -1})
	if cond.Page != 0 && cond.Size != 0 {
		opts = opts.SetSkip(int64((cond.Page - 1) * cond.Size)).SetLimit(int64(cond.Size))
	}
	var satisfyData []mmgo.SwapSatisfyData
	total, err = client.GetWatcher().PLCMongodb().NewMongoEntry(filter).FindMany(umw.ServiceInfo, CollectionSatisfyData, opts, &satisfyData)
	if err != nil {
		log.CtxLog(ctx).Errorf("ListSatisfyData, fail to find satisfy data: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	for _, record := range satisfyData {
		satisfy := Satisfy{
			CommentId:        record.Id,
			ServiceId:        record.ServiceId,
			OrderId:          record.OrderId,
			CommentTime:      record.CommentTime,
			UserTag:          record.UserTag,
			DiagnosisTag:     record.DiagnosisTag,
			Score:            record.Score,
			DeviceId:         record.DeviceId,
			Project:          record.Project,
			IsValid:          record.IsValid,
			L3Labels:         record.L3Labels,
			FinalL1Label:     record.FinalL1Label,
			FinalL2Label:     record.FinalL2Label,
			FinalL3Label:     record.FinalL3Label,
			CityCompany:      record.CityCompany,
			CityCompanyGroup: record.CityCompanyGroup,
			CarPlatform:      record.VehiclePlatform,
			ReportStatus:     record.ReportStatus,
		}
		deviceInfo, ok := cache.PowerSwapCache.GetSingleDevice(record.DeviceId)
		if ok {
			satisfy.Description = deviceInfo.Description
		}
		res = append(res, satisfy)
	}
	return
}

func (s *Satisfy) GetSatisfyDiyLabelStat(ctx context.Context, cond ListSatisfyRequest, labelLevel string) (map[string]LabelStst, error) {
	filter := bson.D{
		{"report_status", bson.M{"$ne": "reject"}},
		{"final_l3_label", bson.M{"$ne": nil}},
	}
	if cond.StartTime != 0 && cond.EndTime != 0 {
		filter = append(filter, bson.E{Key: "comment_time", Value: bson.M{"$gte": cond.StartTime, "$lt": cond.EndTime}})
	}
	if cond.DiagnosisTag != "" {
		filter = append(filter, bson.E{Key: "diagnosis_tag", Value: bson.M{"$in": strings.Split(cond.DiagnosisTag, ",")}})
	}
	if cond.UserTag != "" {
		filter = append(filter, bson.E{Key: "user_tag", Value: bson.M{"$in": strings.Split(cond.UserTag, ",")}})
	}
	if cond.DeviceId != "" {
		filter = append(filter, bson.E{Key: "device_id", Value: cond.DeviceId})
	}
	if cond.DeviceIds != "" {
		filter = append(filter, bson.E{Key: "device_id", Value: bson.M{"$in": strings.Split(cond.DeviceIds, ",")}})
	}
	if cond.Project != "" {
		filter = append(filter, bson.E{Key: "project", Value: cond.Project})
	}
	if cond.Score != "" {
		scores := util.TurnStrArrToIntArr(strings.Split(cond.Score, ","))
		filter = append(filter, bson.E{Key: "score", Value: bson.M{"$in": scores}})
	}
	if cond.OrderId != "" {
		filter = append(filter, bson.E{Key: "order_id", Value: cond.OrderId})
	}
	if cond.IsValid != nil {
		filter = append(filter, bson.E{Key: "is_valid", Value: *cond.IsValid})
	}
	if cond.ServiceId != "" {
		filter = append(filter, bson.E{Key: "service_id", Value: cond.ServiceId})
	}
	if cond.BatteryId != "" {
		filter = append(filter, bson.E{Key: "$or", Value: bson.A{
			bson.D{{"ev_battery_id", cond.BatteryId}},
			bson.D{{"service_battery_id", cond.BatteryId}},
		}})
	}
	if cond.VehicleId != "" {
		filter = append(filter, bson.E{Key: "ev_id", Value: cond.VehicleId})
	}
	if cond.EvBrand != "" {
		evBrands := strings.Split(cond.EvBrand, ",")
		evTypes := make([]string, 0)
		for _, brand := range evBrands {
			evTypes = append(evTypes, model.VehicleBrandMap[brand]...)
		}
		filter = append(filter, bson.E{Key: "ev_type", Value: bson.M{"$in": evTypes}})
	}
	if cond.EvType != "" {
		filter = append(filter, bson.E{Key: "ev_type", Value: bson.M{"$in": strings.Split(cond.EvType, ",")}})
	}
	if cond.Labels != "" {
		labels := strings.Split(cond.Labels, ",")
		l3Labels := []string{}
		for _, label := range labels {
			l1l2l3 := strings.Split(label, "-")
			if len(l1l2l3) != 3 {
				continue
			}
			l3Labels = append(l3Labels, l1l2l3[2])
		}
		filter = append(filter, bson.E{Key: "final_l3_label", Value: bson.M{"$in": l3Labels}})
	}
	if cond.CityCompanies != "" {
		filter = append(filter, bson.E{Key: "city_company", Value: bson.M{"$in": strings.Split(cond.CityCompanies, ",")}})
	}
	if cond.CityCompanyGroups != "" {
		filter = append(filter, bson.E{Key: "city_company_group", Value: bson.M{"$in": strings.Split(cond.CityCompanyGroups, ",")}})
	}
	if cond.CarPlatform != "" {
		filter = append(filter, bson.E{Key: "vehicle_platform", Value: bson.M{"$in": strings.Split(cond.CarPlatform, ",")}})
	}
	if len(filter) == 0 {
		err := fmt.Errorf("must provide at least one filter field")
		log.CtxLog(ctx).Errorf("ListSatisfyData, %s, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return nil, err
	}
	dbFieldName := "final_l3_label"
	switch labelLevel {
	case "L1":
		dbFieldName = "final_l1_label"
	case "L2":
		dbFieldName = "final_l2_label"
	case "L3":
		dbFieldName = "final_l3_label"
	}
	cursor, err := client.GetWatcher().PLCMongodb().Client.Database(umw.ServiceInfo).Collection(CollectionSatisfyData).Aggregate(ctx, mongo.Pipeline{
		bson.D{{"$match", filter}},
		bson.D{{"$group", bson.M{
			"_id":         fmt.Sprintf("$%v", dbFieldName),
			"count":       bson.M{"$sum": 1},
			"total_score": bson.M{"$sum": "$score"},
		}}},
		bson.D{{"$project", bson.M{
			"label":       "$_id",
			"count":       1,
			"total_score": 1,
		}}},
		bson.D{{"$sort", bson.D{{"label", 1}}}},
	})
	if err != nil {
		log.CtxLog(ctx).Errorf("fail to aggregate. err:%v", err)
		return nil, err
	}
	var res []struct {
		Label      string `json:"label" bson:"label"`
		Count      int64  `json:"count" bson:"count"`
		TotalScore int64  `json:"total_score" bson:"total_score"`
	}
	if err = cursor.All(ctx, &res); err != nil {
		log.CtxLog(ctx).Errorf("cursor.All err. err:%v", err)
		return nil, err
	}
	result := map[string]LabelStst{}
	for _, re := range res {
		result[re.Label] = LabelStst{
			Count:      re.Count,
			TotalScore: re.TotalScore,
		}
	}
	return result, nil
}

func (s *Satisfy) GetSatisfyDiyLabelStatV2(ctx context.Context, cond ListSatisfyRequest) (map[string]LabelStst, error) {
	filter := bson.D{
		{"report_status", bson.M{"$ne": "reject"}},
		{"final_l3_label", bson.M{"$ne": nil}},
	}
	if cond.StartTime != 0 && cond.EndTime != 0 {
		filter = append(filter, bson.E{Key: "comment_time", Value: bson.M{"$gte": cond.StartTime, "$lt": cond.EndTime}})
	}
	if cond.DiagnosisTag != "" {
		filter = append(filter, bson.E{Key: "diagnosis_tag", Value: bson.M{"$in": strings.Split(cond.DiagnosisTag, ",")}})
	}
	if cond.UserTag != "" {
		filter = append(filter, bson.E{Key: "user_tag", Value: bson.M{"$in": strings.Split(cond.UserTag, ",")}})
	}
	if cond.DeviceId != "" {
		filter = append(filter, bson.E{Key: "device_id", Value: cond.DeviceId})
	}
	if cond.DeviceIds != "" {
		filter = append(filter, bson.E{Key: "device_id", Value: bson.M{"$in": strings.Split(cond.DeviceIds, ",")}})
	}
	if cond.Project != "" {
		filter = append(filter, bson.E{Key: "project", Value: cond.Project})
	}
	if cond.Score != "" {
		scores := util.TurnStrArrToIntArr(strings.Split(cond.Score, ","))
		filter = append(filter, bson.E{Key: "score", Value: bson.M{"$in": scores}})
	}
	if cond.OrderId != "" {
		filter = append(filter, bson.E{Key: "order_id", Value: cond.OrderId})
	}
	if cond.IsValid != nil {
		filter = append(filter, bson.E{Key: "is_valid", Value: *cond.IsValid})
	}
	if cond.ServiceId != "" {
		filter = append(filter, bson.E{Key: "service_id", Value: cond.ServiceId})
	}
	if cond.BatteryId != "" {
		filter = append(filter, bson.E{Key: "$or", Value: bson.A{
			bson.D{{"ev_battery_id", cond.BatteryId}},
			bson.D{{"service_battery_id", cond.BatteryId}},
		}})
	}
	if cond.VehicleId != "" {
		filter = append(filter, bson.E{Key: "ev_id", Value: cond.VehicleId})
	}
	if cond.EvBrand != "" {
		evBrands := strings.Split(cond.EvBrand, ",")
		evTypes := make([]string, 0)
		for _, brand := range evBrands {
			evTypes = append(evTypes, model.VehicleBrandMap[brand]...)
		}
		filter = append(filter, bson.E{Key: "ev_type", Value: bson.M{"$in": evTypes}})
	}
	if cond.EvType != "" {
		filter = append(filter, bson.E{Key: "ev_type", Value: bson.M{"$in": strings.Split(cond.EvType, ",")}})
	}
	if cond.L1Labels != "" {
		filter = append(filter, bson.E{Key: "final_l1_label", Value: bson.M{"$in": strings.Split(cond.L1Labels, ",")}})
	}
	if cond.Labels != "" {
		labels := strings.Split(cond.Labels, ",")
		l3Labels := []string{}
		for _, label := range labels {
			l1l2l3 := strings.Split(label, "-")
			if len(l1l2l3) != 3 {
				continue
			}
			l3Labels = append(l3Labels, l1l2l3[2])
		}
		filter = append(filter, bson.E{Key: "final_l3_label", Value: bson.M{"$in": l3Labels}})
	}
	if cond.CityCompanies != "" {
		filter = append(filter, bson.E{Key: "city_company", Value: bson.M{"$in": strings.Split(cond.CityCompanies, ",")}})
	}
	if cond.CityCompanyGroups != "" {
		filter = append(filter, bson.E{Key: "city_company_group", Value: bson.M{"$in": strings.Split(cond.CityCompanyGroups, ",")}})
	}
	if cond.CarPlatform != "" {
		filter = append(filter, bson.E{Key: "vehicle_platform", Value: bson.M{"$in": strings.Split(cond.CarPlatform, ",")}})
	}
	if len(filter) == 0 {
		err := fmt.Errorf("must provide at least one filter field")
		log.CtxLog(ctx).Errorf("ListSatisfyData, %s, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return nil, err
	}
	dbFieldName := "final_l3_label"
	cursor, err := client.GetWatcher().PLCMongodb().Client.Database(umw.ServiceInfo).Collection(CollectionSatisfyData).Aggregate(ctx, mongo.Pipeline{
		bson.D{{"$match", filter}},
		bson.D{{"$group", bson.M{
			"_id":            fmt.Sprintf("$%v", dbFieldName),
			"final_l1_label": bson.M{"$first": "$final_l1_label"},
			"final_l2_label": bson.M{"$first": "$final_l2_label"},
			"score_1_count": bson.M{
				"$sum": bson.M{
					"$cond": bson.A{
						bson.M{"$eq": bson.A{"$score", 1}},
						1,
						0,
					},
				},
			},
			"score_2_count": bson.M{
				"$sum": bson.M{
					"$cond": bson.A{
						bson.M{"$eq": bson.A{"$score", 2}},
						1,
						0,
					},
				},
			},
			"score_3_count": bson.M{
				"$sum": bson.M{
					"$cond": bson.A{
						bson.M{"$eq": bson.A{"$score", 3}},
						1,
						0,
					},
				},
			},
			"score_4_count": bson.M{
				"$sum": bson.M{
					"$cond": bson.A{
						bson.M{"$eq": bson.A{"$score", 4}},
						1,
						0,
					},
				},
			},
			"score_5_count": bson.M{
				"$sum": bson.M{
					"$cond": bson.A{
						bson.M{"$eq": bson.A{"$score", 5}},
						1,
						0,
					},
				},
			},
			"count":       bson.M{"$sum": 1},
			"total_score": bson.M{"$sum": "$score"},
		}}},
		bson.D{{"$project", bson.M{
			"label":          "$_id",
			"final_l1_label": 1,
			"final_l2_label": 1,
			"score_1_count":  1,
			"score_2_count":  1,
			"score_3_count":  1,
			"score_4_count":  1,
			"score_5_count":  1,
			"count":          1,
			"total_score":    1,
		}}},
		bson.D{{"$sort", bson.D{{"label", 1}}}},
	})
	if err != nil {
		log.CtxLog(ctx).Errorf("fail to aggregate. err:%v", err)
		return nil, err
	}
	var res []struct {
		Label        string `json:"label" bson:"label"`
		FinalL1Label string `json:"final_l1_label" bson:"final_l1_label"`
		FinalL2Label string `json:"final_l2_label" bson:"final_l2_label"`
		Score1Count  int64  `json:"score_1_count" bson:"score_1_count"`
		Score2Count  int64  `json:"score_2_count" bson:"score_2_count"`
		Score3Count  int64  `json:"score_3_count" bson:"score_3_count"`
		Score4Count  int64  `json:"score_4_count" bson:"score_4_count"`
		Score5Count  int64  `json:"score_5_count" bson:"score_5_count"`
		Count        int64  `json:"count" bson:"count"`
		TotalScore   int64  `json:"total_score" bson:"total_score"`
	}
	if err = cursor.All(ctx, &res); err != nil {
		log.CtxLog(ctx).Errorf("cursor.All err. err:%v", err)
		return nil, err
	}
	result := map[string]LabelStst{}
	for _, re := range res {
		result[re.Label] = LabelStst{
			FinalL1Label: re.FinalL1Label,
			FinalL2Label: re.FinalL2Label,
			Score1Count:  re.Score1Count,
			Score2Count:  re.Score2Count,
			Score3Count:  re.Score3Count,
			Score4Count:  re.Score4Count,
			Score5Count:  re.Score5Count,
			Count:        re.Count,
			TotalScore:   re.TotalScore,
		}
	}
	return result, nil
}

type LabelStst struct {
	FinalL1Label string
	FinalL2Label string
	Score1Count  int64
	Score2Count  int64
	Score3Count  int64
	Score4Count  int64
	Score5Count  int64
	Count        int64
	TotalScore   int64
}

func (s *Satisfy) SyncSatisfyData(ctx context.Context, dayTs int64) {
	var err error
	defer func() {
		if err != nil {
			// 发送飞书通知
			requestId := ""
			ginCtx, ok := ctx.(*gin.Context)
			if ok {
				requestId = ginCtx.Request.Header.Get("X-Request-ID")
			}
			_ = larkservice.SendMessage(larkservice.MsgText, fmt.Sprintf("{\"text\": \"[%s] 换电满意度数据更新失败, day: %v, request_id: %s\"}", os.Getenv("ENV"), time.UnixMilli(dayTs), requestId), larkservice.Receiver{
				Type:       larkim.ReceiveIdTypeEmail,
				ReceiveIds: config.Cfg.CardBot.Receivers["develop"],
			})
		} else {
			_ = larkservice.SendMessage(larkservice.MsgText, fmt.Sprintf("{\"text\": \"[%s] 换电满意度数据更新成功, day: %v\"}", os.Getenv("ENV"), time.UnixMilli(dayTs)), larkservice.Receiver{
				Type:       larkim.ReceiveIdTypeEmail,
				ReceiveIds: config.Cfg.CardBot.Receivers["develop"],
			})
		}
	}()
	batchSize := 1000
	page := 1
	g := ucmd.NewErrGroup(ctx, 5)
	for {
		var satisfyData []Satisfy
		var total int64
		cond := ListSatisfyRequest{
			CommonUriInTimeRangeParam: model.CommonUriInTimeRangeParam{
				CommonUriParam: model.CommonUriParam{
					Page: page,
					Size: batchSize,
				},
				StartTime: dayTs,
				EndTime:   dayTs + 24*time.Hour.Milliseconds(),
			},
		}
		if s.OrderId != "" {
			cond.OrderId = s.OrderId
		}
		satisfyData, total, err = s.ListSatisfyData(ctx, cond)
		if err != nil {
			log.CtxLog(ctx).Errorf("SyncSatisfyData, fail to list satisfy data: %v", err)
			return
		}
		if total == 0 {
			err = fmt.Errorf("empty satisfy data, day: %d", dayTs)
			log.CtxLog(ctx).Errorf("SyncSatisfyData, %v", err)
			return
		}

		for _, item := range satisfyData {
			g.GoRecover(func() error {
				srv := &Service{
					Project: item.Project,
					OrderId: item.OrderId,
				}
				res, serviceInfo, gErr := srv.GetSatisfyDiagnoseResult(ctx)
				if gErr != nil {
					log.CtxLog(ctx).Errorf("SyncSatisfyData, %v", gErr)
					return gErr
				}
				if res == nil || serviceInfo == nil {
					return nil
				}
				diagnoseTag := make([]string, 0)
				if res.SwapTime != nil {
					diagnoseTag = append(diagnoseTag, DiagnosisTagSwapTimeLong)
				}
				if res.SwapFail != nil {
					diagnoseTag = append(diagnoseTag, DiagnosisTagSwapFail)
				}
				if res.SwapQueueTime != nil {
					diagnoseTag = append(diagnoseTag, DiagnosisTagSwapQueueTimeLong)
				}
				if len(res.PreorderSwapTime) != 0 {
					diagnoseTag = append(diagnoseTag, DiagnosisTagPreorderSwapTimeLong)
				}
				if len(res.PreorderSwapFail) != 0 {
					diagnoseTag = append(diagnoseTag, DiagnosisTagPreorderSwapFail)
				}
				if len(res.MultipleSwap) != 0 {
					diagnoseTag = append(diagnoseTag, DiagnosisTagMultipleSwap)
				}
				if res.Battery70 != "" {
					diagnoseTag = append(diagnoseTag, DiagnosisTagBattery70)
				}
				update := bson.M{
					"$set": bson.M{
						"diagnosis_tag":      diagnoseTag,
						"service_id":         serviceInfo.ServiceId,
						"service_battery_id": serviceInfo.ServiceBatteryId,
						"ev_battery_id":      serviceInfo.VehicleBatteryId,
						"ev_id":              serviceInfo.VehicleId,
						"ev_type":            serviceInfo.EvType,
					},
				}
				return client.GetWatcher().PLCMongodb().NewMongoEntry(bson.D{{"_id", item.CommentId}}).UpdateOne(umw.ServiceInfo, CollectionSatisfyData, update, false)
			})
		}
		if page*batchSize >= int(total) {
			break
		}
		page += 1
	}
	err = g.Wait()
	if err != nil {
		log.CtxLog(ctx).Errorf("SyncSatisfyData, goroutine err: %v", err)
		return
	}
}
