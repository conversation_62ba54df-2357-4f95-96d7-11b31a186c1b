package service

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	"git.nevint.com/welkin2/welkin-backend/client"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"
)

type ServiceDO struct {
	ServiceStartTime      int64
	ServiceEndTime        int64
	Project               string
	DeviceId              string
	ServiceId             string
	Rid                   string
	FinishResult          *int32
	SwapFailCode          *int32
	ServiceBatteryId      string
	VehicleBatteryId      string
	VehicleId             string
	VehicleType           string
	VehicleBrand          string
	VehicleBatterySoc     *float32
	VehicleBatteryRealSoc *float32
	ServiceBatterySoc     *float32
	ServiceBatteryRealSoc *float32
	VehicleBatteryType    *int32
	ServiceBatteryType    *int32
	IsStuck               *bool
}

type GetServiceCond struct {
	Project   string
	ServiceId string
}

func (s *ServiceDO) GetService(ctx *gin.Context, cond GetServiceCond) (res ServiceDO, err error) {
	if cond.Project == "" {
		err = fmt.Errorf("project is required")
		log.CtxLog(ctx).Errorf("GetService, %s, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	filter := bson.D{{"service_id", cond.ServiceId}}
	if len(filter) == 0 {
		err = fmt.Errorf("must provide at least one filter field")
		log.CtxLog(ctx).Errorf("GetService, %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	var serviceInfo mmgo.MongoServiceInfoV2
	err = client.GetWatcher().Mongodb().NewMongoEntry(filter).FindOne(umw.ServiceInfo, ucmd.RenameProjectDB(cond.Project), options.FindOne(), &serviceInfo)
	if err != nil {
		log.CtxLog(ctx).Errorf("GetService, fail to find one: %v, filter: %s", err, ucmd.ToJsonStrIgnoreErr(filter))
		if errors.Is(err, mongo.ErrNoDocuments) {
			return res, nil
		}
		return
	}
	res = convertServicePO2DO(cond.Project, serviceInfo)
	return
}

type ListServiceCond struct {
	Project      string
	ServiceIds   []string
	Rids         []string
	StartTime    int64
	EndTime      int64
	DeviceId     string
	VehicleId    string
	BatteryId    string
	VehicleType  string
	VehicleBrand string
	FinishResult *int
	IsStuck      *bool
	model.CommonCond
}

func (s *ServiceDO) ListServices(ctx context.Context, cond ListServiceCond) (res []ServiceDO, total int64, err error) {
	if cond.Project == "" {
		err = fmt.Errorf("project is required")
		log.CtxLog(ctx).Errorf("ListServices, %s, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	filter := bson.D{}
	if cond.StartTime != 0 && cond.EndTime != 0 {
		filter = append(filter, bson.E{Key: "service_start_time", Value: bson.M{"$gte": cond.StartTime, "$lt": cond.EndTime}})
	}
	if cond.DeviceId != "" {
		filter = append(filter, bson.E{Key: "device_id", Value: cond.DeviceId})
	}
	if cond.VehicleId != "" {
		filter = append(filter, bson.E{Key: "ev_id", Value: cond.VehicleId})
	}
	if cond.BatteryId != "" {
		filter = append(filter, bson.E{Key: "$or", Value: bson.A{
			bson.D{{"ev_battery_id", cond.BatteryId}},
			bson.D{{"service_battery_id", cond.BatteryId}},
		}})
	}
	if len(cond.ServiceIds) != 0 {
		filter = append(filter, bson.E{Key: "service_id", Value: bson.M{"$in": cond.ServiceIds}})
	}
	if len(cond.Rids) != 0 {
		filter = append(filter, bson.E{Key: "rid", Value: bson.M{"$in": cond.Rids}})
	}
	if cond.FinishResult != nil {
		filter = append(filter, bson.E{Key: "finish_result", Value: *cond.FinishResult})
	}
	if cond.IsStuck != nil {
		filter = append(filter, bson.E{Key: "is_stuck", Value: *cond.IsStuck})
	}
	if cond.VehicleBrand != "" {
		vehicleBrands := strings.Split(cond.VehicleBrand, ",")
		vehicleTypes := make([]string, 0)
		for _, brand := range vehicleBrands {
			vehicleTypes = append(vehicleTypes, model.VehicleBrandMap[brand]...)
		}
		filter = append(filter, bson.E{Key: "ev_type", Value: bson.M{"$in": vehicleTypes}})
	}
	if cond.VehicleType != "" {
		filter = append(filter, bson.E{Key: "ev_type", Value: bson.M{"$in": strings.Split(cond.VehicleType, ",")}})
	}
	if len(filter) == 0 {
		err = fmt.Errorf("must provide at least one filter field")
		log.CtxLog(ctx).Errorf("ListServices, %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	opts := options.Find()
	if cond.Page != 0 && cond.Size != 0 {
		opts = opts.SetSkip((cond.Page - 1) * cond.Size).SetLimit(cond.Size)
	}

	if cond.Sort != "" {
		descending := 1
		if cond.Descending {
			descending = -1
		}
		opts = opts.SetSort(bson.M{cond.Sort: descending})
	} else {
		opts = opts.SetSort(bson.M{"service_start_time": -1})
	}
	var serviceInfos []mmgo.MongoServiceInfoV2
	total, err = client.GetWatcher().Mongodb().NewMongoEntry(filter).FindMany(umw.ServiceInfo, ucmd.RenameProjectDB(cond.Project), opts, &serviceInfos)
	if err != nil {
		log.CtxLog(ctx).Errorf("ListServices, fail to get service info: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	for _, record := range serviceInfos {
		res = append(res, convertServicePO2DO(cond.Project, record))
	}
	return
}

type CountServiceCond struct {
	ServiceStartTime int64
	ServiceEndTime   int64
	Project          string
	DeviceId         string
}

func (s *ServiceDO) CountService(ctx context.Context, cond CountServiceCond) (total int64, err error) {
	if cond.Project == "" {
		err = fmt.Errorf("project is required")
		log.CtxLog(ctx).Errorf("CountService, %s, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	filter := bson.D{}
	if cond.ServiceStartTime != 0 && cond.ServiceEndTime != 0 {
		filter = append(filter, bson.E{Key: "service_start_time", Value: bson.M{"$gte": cond.ServiceStartTime, "$lt": cond.ServiceEndTime}})
	}
	if cond.DeviceId != "" {
		filter = append(filter, bson.E{Key: "device_id", Value: cond.DeviceId})
	}
	total, err = client.GetWatcher().Mongodb().NewMongoEntry(filter).Count(umw.ServiceInfo, ucmd.RenameProjectDB(cond.Project))
	if err != nil {
		log.CtxLog(ctx).Errorf("CountService, fail to count service info: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	return
}
