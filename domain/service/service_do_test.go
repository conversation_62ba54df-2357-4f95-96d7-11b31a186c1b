package service

import (
	"net/http"
	"os"
	"testing"

	"github.com/gin-gonic/gin"
	gocache "github.com/patrickmn/go-cache"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	ulog "git.nevint.com/golang-libs/common-utils/logger"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
)

func init() {
	os.Setenv("APOLLO_META", "https://apollo-config-cn-stg.nioint.com")
	os.Setenv("ENV", "stg")
	cfg := &ucfg.Config{}
	ucfg.GetConfigApollo("config.json", true, cfg, ucfg.NeedValidate(true), ucfg.ServiceName("ppd-welkin-backend"), ucfg.Cluster(ucfg.GetWelkinApolloCluster()))
	defer ulog.Final()
	cfg.Log.DirPath = "/tmp/logs"
	config.Cfg = cfg
	log.Init(cfg.Log, true)
	logger := log.Logger.Named("activity")
	w = client.NewWatcherByParam(config.Cfg, logger)
	var err error
	if err != nil {
		panic(err)
	}
	cache.CacheRealtimeDataID(w.Mongodb().Client)
	cache.PowerSwapCache = &cache.DeviceInfoCache{
		Cache:         gocache.New(gocache.NoExpiration, gocache.NoExpiration),
		ResourceCache: gocache.New(gocache.NoExpiration, gocache.NoExpiration),
	}
	cache.PowerSwapCache.RefreshDeviceInfoCache(w.Mongodb().Client)
	model.InitConstant(cfg.ExtraConfig["evBrand"])
	ctx = &gin.Context{Request: &http.Request{Header: make(http.Header)}}
	ctx.Request.Header.Set("X-Request-ID", "test")
}

func TestServiceDO_GetService(t *testing.T) {
	srv := &ServiceDO{}
	cond := GetServiceCond{
		Project:   umw.PUS4,
		ServiceId: "PUS-NIO-095fdc2a-5840a9b65580b80dec5a4e5177862857202010101733203535813",
	}
	res, err := srv.GetService(ctx, cond)
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("res: %s", ucmd.ToJsonStrIgnoreErr(res))
}

func TestServiceDO_ListServices(t *testing.T) {
	srv := &ServiceDO{}
	cond := ListServiceCond{
		Project: umw.PUS4,
		//ServiceIds: []string{"PUS-NIO-095fdc2a-5840a9b65580b80dec5a4e5177862857202010101733203535813"},
		StartTime: 1731168000000,
		EndTime:   1732896000000,
		DeviceId:  "PUS-NIO-095fdc2a-5840a9b6",
		VehicleId: "1e72bef332fd4a9da0fcee9a89d2fd30",
		BatteryId: "P0205908AG19121V218952L12A00137",
		CommonCond: model.CommonCond{
			Page: 1,
			Size: 10,
		},
	}
	res, total, err := srv.ListServices(ctx, cond)
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("total: %d, res: %s", total, ucmd.ToJsonStrIgnoreErr(res))
}

func TestServiceDO_CountService(t *testing.T) {
	srv := &ServiceDO{}
	cond := CountServiceCond{
		ServiceStartTime: 1731168000000,
		ServiceEndTime:   1732896000000,
		Project:          umw.PUS4,
	}
	total, err := srv.CountService(ctx, cond)
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("total: %d", total)
}
