package service

import (
	"time"

	um "git.nevint.com/golang-libs/common-utils/model"
	"git.nevint.com/welkin2/welkin-backend/model"
	mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"
)

const (
	CollectionOrderInfo         = "order_info_full"
	CollectionSatisfyData       = "satisfy_data"
	CollectionServiceDailyCount = "service_daily_count"

	DiagnosisTagSwapTimeLong         = "swap_time"          // 换电时间长
	DiagnosisTagSwapFail             = "swap_fail"          // 换电挂车/失败
	DiagnosisTagSwapQueueTimeLong    = "swap_queue_time"    // 换电排队时间长
	DiagnosisTagPreorderSwapTimeLong = "preorder_swap_time" // 前序订单时间长
	DiagnosisTagPreorderSwapFail     = "preorder_swap_fail" // 前序订单挂车/失败
	DiagnosisTagMultipleSwap         = "multiple_swap"      // 多次下单
	DiagnosisTagBattery70            = "battery_70"         // 70度电池
)

var (
	SatisfySwapTimeThreshold      = 5 * time.Minute.Milliseconds() // 换电时间长的阈值
	SatisfySwapQueueTimeThreshold = 9 * time.Minute.Milliseconds() // 排队时间长的阈值
)

// SatisfyDiagnosisTags 满意度诊断标签
var SatisfyDiagnosisTags = map[string]string{
	DiagnosisTagSwapTimeLong:         "换电时间长",
	DiagnosisTagSwapFail:             "换电挂车/失败",
	DiagnosisTagSwapQueueTimeLong:    "换电排队时间长",
	DiagnosisTagPreorderSwapTimeLong: "前序订单时间长",
	DiagnosisTagPreorderSwapFail:     "前序订单挂车/失败",
	DiagnosisTagMultipleSwap:         "多次下单",
	DiagnosisTagBattery70:            "70度电池",
}

type ServiceForTorque struct {
	ServiceId    string `json:"service_id"`
	StartTime    int64  `json:"start_time"`
	EndTime      int64  `json:"end_time"`
	Success      bool   `json:"success"`
	HasPlcRecord bool   `json:"has_plc_record"`
}

type ServiceForTorqueRequest struct {
	model.CommonUriInTimeRangeParam
	DeviceId string `json:"device_id" form:"device_id"`
}

type ServiceForTorqueResponse struct {
	um.Base
	Total int                `json:"total"`
	Data  []ServiceForTorque `json:"data"`
}

type DeviceServices struct {
	DeviceId    string              `bson:"device_id" json:"device_id"`
	ServiceList []mmgo.ServiceBasic `bson:"service_list" json:"service_list"`
}

type ListSatisfyRequest struct {
	model.CommonUriInTimeRangeParam
	DiagnosisTag      string `json:"diagnosis_tag" form:"diagnosis_tag"`
	UserTag           string `json:"user_tag" form:"user_tag"`
	DeviceId          string `json:"device_id" form:"device_id"`
	Project           string `json:"project" form:"project"`
	Score             string `json:"score" form:"score"`
	OrderId           string `json:"order_id" form:"order_id"`
	IsValid           *bool  `json:"is_valid" form:"is_valid,omitempty"`
	ServiceId         string `json:"service_id" form:"service_id"`
	BatteryId         string `json:"battery_id" form:"battery_id"`
	VehicleId         string `json:"vehicle_id" form:"vehicle_id"`
	EvType            string `json:"ev_type" form:"ev_type"`
	EvBrand           string `json:"ev_brand" form:"ev_brand"`
	Labels            string `json:"labels" form:"labels"`
	CityCompanies     string `json:"city_companies" form:"city_companies"`
	CityCompanyGroups string `json:"city_company_groups" form:"city_company_groups"`
	CarPlatform       string `json:"car_platform" form:"car_platform"`
	DeviceIds         string `json:"device_ids" form:"device_ids"`
	L1Labels          string `json:"l1_labels" form:"l1_labels"`
}

type ListSatisfyResponse struct {
	um.Base
	Total int64             `json:"total"`
	Data  []ListSatisfyData `json:"data"`
}

type ListSatisfyData struct {
	CommentId        string   `json:"comment_id"`
	CommentTime      int64    `json:"comment_time"`
	DiagnosisTag     []string `json:"diagnosis_tag,omitempty"`
	UserTag          []string `json:"user_tag,omitempty"`
	SwapDuration     int64    `json:"swap_duration"`
	QueueDuration    int64    `json:"queue_duration"`
	OrderStartTime   int64    `json:"order_start_time"`
	ServiceStartTime int64    `json:"service_start_time"`
	ServiceEndTime   int64    `json:"service_end_time"`
	Score            int      `json:"score"`
	DeviceId         string   `json:"device_id"`
	Description      string   `json:"description"`
	IsValid          bool     `json:"is_valid"`
	ServiceId        string   `json:"service_id"`
	OrderId          string   `json:"order_id"`
	VehicleId        string   `json:"vehicle_id"`
	ServiceBatteryId string   `json:"service_battery_id"`
	VehicleBatteryId string   `json:"vehicle_battery_id"`
	EvBrand          string   `json:"ev_brand"`
	EvType           string   `json:"ev_type"`
	Project          string   `json:"project"`
	L1Label          string   `json:"l1_label"`
	L2Label          string   `json:"l2_label"`
	L3Label          string   `json:"l3_label"`
	CityCompany      string   `json:"city_company"`
	CityCompanyGroup string   `json:"city_company_group"`
	CarPlatform      string   `json:"car_platform"`
	ReportStatus     string   `json:"report_status"`
}

type EventInfo struct {
	Event     string `json:"event"`
	Timestamp int64  `json:"timestamp"`
}

type GetSatisfySwapLogResponse struct {
	um.Base
	Data []EventInfo `json:"data"`
}

type BasicServiceInfo struct {
	DeviceId             string `json:"device_id"`
	Description          string `json:"description"`
	ServiceId            string `json:"service_id"`
	Project              string `json:"project"`
	ServiceStartTime     int64  `json:"service_start_time"`
	ServiceEndTime       int64  `json:"service_end_time"`
	OrderTimeThreshold   int64  `json:"order_time_threshold"`
	ServiceTimeThreshold int64  `json:"service_time_threshold"`
	VehicleId            string `json:"vehicle_id"`
}

type GetSatisfyDetailResponse struct {
	um.Base
	Data struct {
		ServiceInfo        SatisfyDetailServiceInfo `json:"service_info"`
		UserExperienceInfo UserExperienceInfo       `json:"user_experience_info"`
		CommentInfo        *CommentInfo             `json:"comment_info,omitempty"`
		DiagnosisResult    map[string]string        `json:"diagnosis_result,omitempty"`
		DiagnosisLabel     map[string][]string      `json:"diagnosis_label,omitempty"`
	} `json:"data"`
}

type SatisfyDetailServiceInfo struct {
	DeviceId         string `json:"device_id,omitempty"`
	Description      string `json:"description,omitempty"`
	OrderStartTime   int64  `json:"order_start_time,omitempty"`
	OrderEndTime     int64  `json:"order_end_time,omitempty"`
	ServiceStartTime int64  `json:"service_start_time,omitempty"`
	ServiceEndTime   int64  `json:"service_end_time,omitempty"`
	OrderId          string `json:"order_id,omitempty"`
	ServiceId        string `json:"service_id,omitempty"`
	CommentId        string `json:"comment_id,omitempty"`
	VehicleId        string `json:"vehicle_id,omitempty"`
	ServiceBatteryId string `json:"service_battery_id,omitempty"`
	VehicleBatteryId string `json:"vehicle_battery_id,omitempty"`
	IsReverseSwap    *bool  `json:"is_reverse_swap,omitempty"`
	IsAutomatedSwap  *bool  `json:"is_automated_swap,omitempty"`
	EvType           string `json:"ev_type,omitempty"`
	EvBrand          string `json:"ev_brand,omitempty"`
}

type UserExperienceInfo struct {
	ElectricityKwh         float64  `json:"electricity_kwh"`
	VehicleBatterySoc      *float32 `json:"vehicle_battery_soc,omitempty"`
	VehicleBatterySocOss   *float32 `json:"vehicle_battery_soc_oss,omitempty"`
	ServiceBatterySoc      *float32 `json:"service_battery_soc,omitempty"`
	ServiceBatterySocOss   *float32 `json:"service_battery_soc_oss,omitempty"`
	VehicleBatteryCapacity *int32   `json:"vehicle_battery_capacity,omitempty"`
	ServiceBatteryCapacity *int32   `json:"service_battery_capacity,omitempty"`
}

type CommentInfo struct {
	Score    int      `json:"score,omitempty"`
	UserTag  []string `json:"user_tag,omitempty"`
	Comment  string   `json:"comment,omitempty"`
	Reason   string   `json:"reason,omitempty"`
	Solution string   `json:"solution,omitempty"`
}

type GetSatisfyDiagnoseResultResponse struct {
	um.Base
	Data SatisfyDiagnoseResult `json:"data"`
}

type SatisfyDiagnoseResult struct {
	SwapTime         *SatisfySwapTime          `json:"swap_time,omitempty"`
	SwapFail         *SatisfySwapFail          `json:"swap_fail,omitempty"`
	SwapQueueTime    *SatisfySwapQueueTime     `json:"swap_queue_time,omitempty"`
	PreorderSwapTime []SatisfyOrderInfo        `json:"preorder_swap_time,omitempty"`
	PreorderSwapFail []SatisfyServiceAlarmInfo `json:"preorder_swap_fail,omitempty"`
	MultipleSwap     []SatisfyOrderInfo        `json:"multiple_swap,omitempty"`
	Battery70        string                    `json:"battery_70,omitempty"`
	DiagnosisResult  map[string]string         `json:"diagnosis_result,omitempty"`
	DiagnosisLabel   map[string][]string       `json:"diagnosis_label,omitempty"`
}

type SatisfySwapTime struct {
	ServiceStartTime int64 `json:"service_start_time"`
	ServiceEndTime   int64 `json:"service_end_time"`
}

type SatisfyAlarmInfo struct {
	AlarmType         int32  `json:"alarm_type"`
	DataIdDescription string `json:"data_id_description,omitempty"`
	DataId            string `json:"data_id,omitempty"`
	IsStuck           bool   `json:"is_stuck"`
	AlarmLevel        int32  `json:"alarm_level,omitempty"`
	CreateTs          int64  `json:"create_ts,omitempty"`
	ClearTs           int64  `json:"clear_ts,omitempty"`
	DeviceId          string `json:"device_id,omitempty"`
	DeviceName        string `json:"device_name,omitempty"`
	State             int32  `json:"state"`
}

type SatisfySwapFail struct {
	AlarmList    []SatisfyAlarmInfo `json:"alarm_list"`
	FinishResult *int32             `json:"finish_result,omitempty"`
	IsStuck      *bool              `json:"is_stuck,omitempty"`
}

type SatisfyServiceAlarmInfo struct {
	ServiceId    string             `json:"service_id,omitempty"`
	FinishResult *int32             `json:"finish_result,omitempty"`
	IsStuck      *bool              `json:"is_stuck,omitempty"`
	VehicleId    string             `json:"vehicle_id,omitempty"`
	AlarmList    []SatisfyAlarmInfo `json:"alarm_list"`
}

type SatisfySwapQueueTime struct {
	OrderTime int64 `json:"order_time"`
	CallTime  int64 `json:"call_time"`
}

type SatisfyOrderInfo struct {
	Id               int    `json:"id"`
	ServiceStartTime int64  `json:"service_start_time,omitempty"`
	ServiceEndTime   int64  `json:"service_end_time,omitempty"`
	OrderStartTime   int64  `json:"order_start_time,omitempty"`
	OrderEndTime     int64  `json:"order_end_time,omitempty"`
	ServiceId        string `json:"service_id,omitempty"`
	OrderId          string `json:"order_id,omitempty"`
	Project          string `json:"project,omitempty"`
	FinishResult     *int32 `json:"finish_result,omitempty"`
	Interval         int    `json:"interval"`
	VehicleId        string `json:"vehicle_id,omitempty"`
}
