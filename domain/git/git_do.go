package git

import (
	"context"
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"os"
	"strings"

	"github.com/go-git/go-git/v5"
	"github.com/go-git/go-git/v5/plumbing"
	gitHttp "github.com/go-git/go-git/v5/plumbing/transport/http"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"

	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/util"
)

const (
	RepoProtoBufPs = "proto_buf_ps"

	ClonePathProtoBufPs = "/tmp/proto_buf_ps"

	AlarmFileName    = "报警信息.csv"
	AlarmFileNamePS2 = "故障点表-PS2.0.csv"

	ProtoBranchPUS3 = "PUS-3.0"
	ProtoBranchPUS4 = "PUS-4.0"
	ProtoBranchPS2  = "PS2.0"
	ProtoBranchFY   = "FY"
)

var SyncBranches = []string{ProtoBranchPUS3, ProtoBranchPUS4, ProtoBranchPS2, ProtoBranchFY}
var Branch2Project = map[string]string{
	ProtoBranchPUS3: umw.PUS3,
	ProtoBranchPUS4: umw.PUS4,
	ProtoBranchPS2:  umw.PowerSwap2,
	ProtoBranchFY:   umw.FYPUS1,
}

type GitDO struct {
	Repository *git.Repository
	WorkTree   *git.Worktree
}

// CloneRepo 克隆远程仓库
func (g *GitDO) CloneRepo(ctx context.Context, repo, localPath string) error {
	r, err := git.PlainClone(localPath, false, &git.CloneOptions{
		URL:      config.Cfg.GitLab.Repo[repo],
		Progress: os.Stdout,
		Auth: &gitHttp.BasicAuth{
			Username: config.Cfg.GitLab.UserName,
			Password: config.Cfg.GitLab.Password,
		},
	})
	if err != nil {
		log.CtxLog(ctx).Errorf("CloneRepo, fail to clone repo: %v", err)
		return err
	}
	w, err := r.Worktree()
	if err != nil {
		log.CtxLog(ctx).Errorf("CloneRepo, fail to get worktree: %v", err)
		return err
	}
	g.Repository = r
	g.WorkTree = w
	return nil
}

// SyncProtobufPS 同步git仓库的点表
func (g *GitDO) SyncProtobufPS(ctx context.Context, localPath string, syncBranches ...string) error {
	defer os.RemoveAll(localPath)
	if err := g.CloneRepo(ctx, RepoProtoBufPs, localPath); err != nil {
		log.CtxLog(ctx).Errorf("SyncProtobufPS, fail to clone repo: %v", err)
		return err
	}
	// 默认同步所有分支
	if len(syncBranches) == 0 {
		syncBranches = SyncBranches
	}
	for _, branch := range syncBranches {
		if err := g.SyncProtoAlarm(ctx, localPath, branch); err != nil {
			log.CtxLog(ctx).Errorf("SyncProtoAlarm, fail to handle branch %s: %v", branch, err)
			return err
		}
	}
	return nil
}

// SyncProtoAlarm 同步报警信息
func (g *GitDO) SyncProtoAlarm(ctx context.Context, localPath, branch string) error {
	remoteRefName := plumbing.NewRemoteReferenceName("origin", branch)
	remoteRef, err := g.Repository.Reference(remoteRefName, true)
	if err != nil {
		log.CtxLog(ctx).Errorf("SyncProtoAlarm, fail to find remote branch: %v", err)
		return err
	}
	branchCoOpts := git.CheckoutOptions{
		Branch: remoteRef.Name(),
		Create: false,
	}
	if err = g.WorkTree.Checkout(&branchCoOpts); err != nil {
		log.CtxLog(ctx).Errorf("SyncProtoAlarm, fail to switch branch: %v", err)
		return err
	}
	alarmFileName := AlarmFileName
	if branch == "PS2.0" {
		alarmFileName = AlarmFileNamePS2
	}
	file, err := os.Open(fmt.Sprintf("%s/%s", localPath, alarmFileName))
	if err != nil {
		log.CtxLog(ctx).Errorf("SyncProtoAlarm, fail to open file: %v", err)
		return err
	}
	defer file.Close()

	reader := csv.NewReader(file)
	header, err := reader.Read()
	if err != nil {
		log.CtxLog(ctx).Errorf("SyncProtoAlarm, fail to read title: %v", err)
		return err
	}
	fieldMap := make(map[string]int)
	for i, field := range header {
		if strings.Contains(field, "故障码") {
			fieldMap["data_id"] = i
		} else if strings.Contains(field, "故障描述") && !strings.Contains(field, "英文") {
			fieldMap["var_cn_name"] = i
		} else if strings.Contains(field, "英文") {
			fieldMap["var_en_name"] = i
		} else if strings.Contains(field, "故障等级") {
			fieldMap["alarm_level"] = i
		}
	}
	var record []string
	var models []mongo.WriteModel
	for {
		record, err = reader.Read()
		if errors.Is(err, io.EOF) {
			break
		}
		if err != nil {
			log.CtxLog(ctx).Errorf("SyncProtoAlarm, fail to read record: %v", err)
			return err
		}
		if len(record) < 4 {
			continue
		}
		dataId := strings.Trim(record[fieldMap["data_id"]], " ")
		if dataId == "" {
			continue
		}
		filter := bson.D{{"data_id", dataId}}
		update := bson.M{"$set": bson.D{
			{"data_id", dataId},
			{"var_cn_name", record[fieldMap["var_cn_name"]]},
			{"var_en_name", record[fieldMap["var_en_name"]]},
			{"alarm_level", util.ParseInt(record[fieldMap["alarm_level"]])},
		}}
		models = append(models, mongo.NewUpdateOneModel().
			SetFilter(filter).
			SetUpdate(update).
			SetUpsert(true))
		//fmt.Println("update record: ", ucmd.ToJsonStrIgnoreErr(update))
	}
	err = client.GetWatcher().Mongodb().NewMongoEntry().UpdateManyBulk(umw.Device2Cloud, fmt.Sprintf("alarm-%s", strings.ToLower(Branch2Project[branch])), models)
	if err != nil {
		log.CtxLog(ctx).Errorf("SyncProtoAlarm, fail to update record: %v, branch: %s", err, branch)
		return err
	}
	log.CtxLog(ctx).Infof("SyncProtoAlarm, success to sync alarm, branch %s, items: %d", branch, len(models))
	return nil
}
