package revenue

import (
	"context"
	"errors"
	"fmt"
	"git.nevint.com/welkin2/welkin-backend/util"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"sync"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	"git.nevint.com/welkin2/welkin-backend/client"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"
)

const (
	AggregateGroupByProject  = "project"
	AggregateGroupByDeviceId = "device_id"
	AggregateGroupByDay      = "day"
)

type RevenueDO struct {
	Day                       int64
	DeviceId                  string
	Project                   string
	CityCompany               string
	MaxRevenueRate            *float64
	TotalRevenue              *float64 // OffPeakRevenue + EnergyRevenue + BatteryMaintenanceRevenue
	OffPeakRevenue            *float64
	EnergyRevenue             *float64
	ChargeRevenue             *float64
	WaterRevenue              *float64
	OperationRevenue          *float64
	BatteryMaintenanceRevenue *float64
	BatteryMaintenanceTimes   *float64
	BatteryTotal              *float64
}

type ListRevenueCond struct {
	model.CommonCond
	StartTime   int64
	EndTime     int64
	Project     string
	CityCompany string
	DeviceId    string
}

func (r *RevenueDO) ListRevenue(ctx context.Context, cond ListRevenueCond) (res []RevenueDO, total int64, err error) {
	if cond.StartTime == 0 || cond.EndTime == 0 {
		err = fmt.Errorf("start time and end time are required")
		log.CtxLog(ctx).Errorf("ListRevenue, %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	filter := bson.D{}
	if cond.StartTime != 0 && cond.EndTime != 0 {
		filter = append(filter, bson.E{Key: "day", Value: bson.M{"$gte": cond.StartTime, "$lte": cond.EndTime}})
	}
	if cond.DeviceId != "" {
		filter = append(filter, bson.E{Key: "device_id", Value: cond.DeviceId})
	}
	if cond.Project != "" {
		filter = append(filter, bson.E{Key: "project", Value: cond.Project})
	}
	if cond.CityCompany != "" {
		filter = append(filter, bson.E{Key: "city_company", Value: cond.CityCompany})
	}
	if len(filter) == 0 {
		err = fmt.Errorf("must provide at least one filter field")
		log.CtxLog(ctx).Errorf("ListRevenue, %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	opts := options.Find().SetSort(bson.D{{"day", -1}, {"device_id", 1}})
	if cond.Page != 0 && cond.Size != 0 {
		opts = opts.SetSkip((cond.Page - 1) * cond.Size).SetLimit(cond.Size)
	}
	var revenueInfos []mmgo.DeviceModelRevenue
	total, err = client.GetWatcher().PLCMongodb().NewMongoEntry(filter).FindMany(mmgo.DBDeviceModel, mmgo.CollectionRevenue, opts, &revenueInfos)
	if err != nil {
		log.CtxLog(ctx).Errorf("ListRevenue, fail to get revenue: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	for _, record := range revenueInfos {
		res = append(res, convertRevenuePO2DO(record))
	}
	return
}

type GetRevenueCond struct {
	Day      int64
	Project  string
	DeviceId string
}

func (r *RevenueDO) GetRevenue(ctx context.Context, cond GetRevenueCond) (res *RevenueDO, err error) {
	filter := bson.D{
		{"day", cond.Day},
		{"project", cond.Project},
		{"device_id", cond.DeviceId},
	}
	var revenueInfo mmgo.DeviceModelRevenue
	err = client.GetWatcher().PLCMongodb().NewMongoEntry(filter).FindOne(mmgo.DBDeviceModel, mmgo.CollectionRevenue, options.FindOne(), &revenueInfo)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			log.CtxLog(ctx).Warnf("GetRevenue, no revenue found, cond: %s", ucmd.ToJsonStrIgnoreErr(cond))
			return nil, nil
		}
		log.CtxLog(ctx).Errorf("GetRevenue, fail to get revenue: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	result := convertRevenuePO2DO(revenueInfo)
	return &result, nil
}

type AggregateRevenueCond struct {
	StartTime   int64
	EndTime     int64
	Project     string
	CityCompany string
	DeviceId    string
}

type RevenueOverview struct {
	TotalRevenue              float64
	OffPeakRevenue            float64
	EnergyRevenue             float64
	BatteryMaintenanceRevenue float64
	Count                     int
	Project                   *string
	DeviceId                  *string
	Day                       *int64
}

// AggregateRevenue 聚合收益数据，单站单日平均收益，groupBy为project或device_id或day
func (r *RevenueDO) AggregateRevenue(ctx context.Context, cond AggregateRevenueCond, groupBy string) (res []RevenueOverview, err error) {
	res = make([]RevenueOverview, 0)
	filter := bson.M{
		"day":                         bson.M{"$gte": cond.StartTime, "$lte": cond.EndTime},
		"off_peak_revenue":            bson.M{"$exists": true},
		"battery_maintenance_revenue": bson.M{"$exists": true},
		"energy_revenue":              bson.M{"$exists": true},
	}
	if cond.Project != "" {
		filter["project"] = cond.Project
	}
	if cond.CityCompany != "" {
		filter["city_company"] = cond.CityCompany
	}
	if cond.DeviceId != "" {
		filter["device_id"] = cond.DeviceId
	}
	pipeline := mongo.Pipeline{
		{{"$match", filter}},
		{{"$group", bson.M{
			"_id":                               fmt.Sprintf("$%s", groupBy),
			"count":                             bson.M{"$sum": 1},
			"total_off_peak_revenue":            bson.M{"$sum": "$off_peak_revenue"},
			"total_battery_maintenance_revenue": bson.M{"$sum": "$battery_maintenance_revenue"},
			"total_energy_revenue":              bson.M{"$sum": "$energy_revenue"},
		}}},
		{{"$addFields", bson.M{
			"total_revenue": bson.M{
				"$add": bson.A{
					"$total_off_peak_revenue",
					"$total_battery_maintenance_revenue",
					"$total_energy_revenue",
				},
			},
		}}},
	}
	var results []bson.M
	err = client.GetWatcher().PLCMongodb().NewMongoEntry().Aggregate(mmgo.DBDeviceModel, mmgo.CollectionRevenue, pipeline, &results)
	if err != nil {
		log.CtxLog(ctx).Errorf("AggregateRevenue, fail to aggregate energy: %v, pipeline: %s", err, ucmd.ToJsonStrIgnoreErr(pipeline))
		return
	}
	for _, record := range results {
		revenueOverview := RevenueOverview{
			TotalRevenue:              util.ParseFloat(record["total_revenue"]),
			OffPeakRevenue:            util.ParseFloat(record["total_off_peak_revenue"]),
			EnergyRevenue:             util.ParseFloat(record["total_energy_revenue"]),
			BatteryMaintenanceRevenue: util.ParseFloat(record["total_battery_maintenance_revenue"]),
			Count:                     util.ParseInt(record["count"]),
		}
		if groupBy == "project" {
			project := record["_id"].(string)
			revenueOverview.Project = &project
		} else if groupBy == "device_id" {
			deviceId := record["_id"].(string)
			revenueOverview.DeviceId = &deviceId
		} else if groupBy == "day" {
			day := record["_id"].(int64)
			revenueOverview.Day = &day
		} else {
			err = fmt.Errorf("invalid groupBy field: %s", groupBy)
			log.CtxLog(ctx).Errorf("AggregateRevenue, %v", err)
			return
		}
		res = append(res, revenueOverview)
	}
	return
}

func (r *RevenueDO) GetRevenueUpdateTime(ctx context.Context, startTime, endTime int64) (updateTime int64, err error) {
	opt := options.FindOne().SetSort(bson.M{"day": -1})
	var res mmgo.DeviceModelRevenue
	filter := bson.D{
		{"day", bson.M{"$gte": startTime, "$lte": endTime}},
		{"off_peak_revenue", bson.M{"$exists": true}},
		{"battery_maintenance_revenue", bson.M{"$exists": true}},
		{"energy_revenue", bson.M{"$exists": true}},
	}
	err = client.GetWatcher().PLCMongodb().NewMongoEntry(filter).FindOne(mmgo.DBDeviceModel, mmgo.CollectionRevenue, opt, &res)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return 0, nil
		}
		log.CtxLog(ctx).Errorf("GetRevenueUpdateTime, fail to get energy: %v", err)
		return
	}
	updateTime = res.Day
	return
}

// CountValidDays 计算收益有效的天数
func (r *RevenueDO) CountValidDays(ctx context.Context, cond AggregateRevenueCond) (days int, err error) {
	filter := bson.D{
		{"day", bson.M{"$gte": cond.StartTime, "$lte": cond.EndTime}},
		{"off_peak_revenue", bson.M{"$exists": true}},
		{"battery_maintenance_revenue", bson.M{"$exists": true}},
		{"energy_revenue", bson.M{"$exists": true}},
	}
	if cond.DeviceId != "" {
		filter = append(filter, bson.E{Key: "device_id", Value: cond.DeviceId})
	}
	if cond.Project != "" {
		filter = append(filter, bson.E{Key: "project", Value: cond.Project})
	}
	if cond.CityCompany != "" {
		filter = append(filter, bson.E{Key: "city_company", Value: cond.CityCompany})
	}
	distinctDays, err := client.GetWatcher().PLCMongodb().NewMongoEntry(filter).Distinct(mmgo.DBDeviceModel, mmgo.CollectionRevenue, "day")
	if err != nil {
		log.CtxLog(ctx).Errorf("CountValidDays, fail to count valid days: %v, filter: %s", err, ucmd.ToJsonStrIgnoreErr(filter))
		return
	}
	return len(distinctDays), nil
}

// CalculateDeviceSuggestion 计算站点建议（单日）
func (r *RevenueDO) CalculateDeviceSuggestion(ctx context.Context, cond AggregateRevenueCond) (suggestions []string, err error) {
	g := ucmd.NewErrGroup(ctx, 10)
	mu := sync.Mutex{}
	var deviceRevenue mmgo.DeviceModelRevenue
	err = client.GetWatcher().PLCMongodb().NewMongoEntry(bson.D{{"day", cond.StartTime}, {"device_id", cond.DeviceId}}).FindOne(mmgo.DBDeviceModel, mmgo.CollectionRevenue, options.FindOne(), &deviceRevenue)
	if err != nil {
		log.CtxLog(ctx).Errorf("CalculateDeviceSuggestion, fail to get revenue: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	log.CtxLog(ctx).Infof("CalculateDeviceSuggestion, device revenue: %s", ucmd.ToJsonStrIgnoreErr(deviceRevenue))
	// 总收益
	g.GoRecover(func() error {
		// 总收益为null的设备，不计入统计
		if deviceRevenue.OffPeakRevenue == nil && deviceRevenue.BatteryMaintenanceRevenue == nil && deviceRevenue.EnergyRevenue == nil {
			log.CtxLog(ctx).Warnf("CalculateDeviceSuggestion, device revenue is null: %s", ucmd.ToJsonStrIgnoreErr(deviceRevenue))
			return nil
		}
		totalRevenue := 0.0
		if deviceRevenue.OffPeakRevenue != nil {
			totalRevenue += *deviceRevenue.OffPeakRevenue
		}
		if deviceRevenue.BatteryMaintenanceRevenue != nil {
			totalRevenue += *deviceRevenue.BatteryMaintenanceRevenue
		}
		if deviceRevenue.EnergyRevenue != nil {
			totalRevenue += *deviceRevenue.EnergyRevenue
		}
		filter := bson.D{
			{"day", bson.M{"$gte": cond.StartTime, "$lte": cond.EndTime}},
			{"$or", bson.A{
				bson.M{"off_peak_revenue": bson.M{"$exists": true, "$ne": primitive.Null{}}},
				bson.M{"battery_maintenance_revenue": bson.M{"$exists": true, "$ne": primitive.Null{}}},
				bson.M{"energy_revenue": bson.M{"$exists": true, "$ne": primitive.Null{}}},
			}},
		}
		// 总数量
		totalCount, gErr := client.GetWatcher().PLCMongodb().NewMongoEntry(filter).Count(mmgo.DBDeviceModel, mmgo.CollectionRevenue)
		if gErr != nil {
			log.CtxLog(ctx).Errorf("CalculateDeviceSuggestion, fail to count total revenue: %v, filter: %s", gErr, ucmd.ToJsonStrIgnoreErr(filter))
			return gErr
		}
		// 30%的阈值
		threshold := float64(totalCount) * 0.3
		// 小于当前设备值的数量
		pipeline := mongo.Pipeline{
			{{"$match", filter}},
			{{"$group", bson.M{
				"_id":                               fmt.Sprintf("$%s", AggregateGroupByDeviceId),
				"total_off_peak_revenue":            bson.M{"$sum": "$off_peak_revenue"},
				"total_battery_maintenance_revenue": bson.M{"$sum": "$battery_maintenance_revenue"},
				"total_energy_revenue":              bson.M{"$sum": "$energy_revenue"},
			}}},
			{{"$addFields", bson.M{
				"total_revenue": bson.M{
					"$add": bson.A{
						"$total_off_peak_revenue",
						"$total_battery_maintenance_revenue",
						"$total_energy_revenue",
					},
				},
			}}},
			{{"$match", bson.M{"total_revenue": bson.M{"$lte": totalRevenue}}}},
		}
		var results []bson.M
		gErr = client.GetWatcher().PLCMongodb().NewMongoEntry().Aggregate(mmgo.DBDeviceModel, mmgo.CollectionRevenue, pipeline, &results)
		if gErr != nil {
			log.CtxLog(ctx).Errorf("CalculateDeviceSuggestion, fail to aggregate revenue: %v, pipeline: %s", gErr, ucmd.ToJsonStrIgnoreErr(pipeline))
			return gErr
		}
		if len(results) <= int(threshold) {
			log.CtxLog(ctx).Infof("CalculateDeviceSuggestion, device: %s, day: %v, total_revenue: %f, len(results): %d, threshold: %f", cond.DeviceId, util.ConvertTime(cond.StartTime), totalRevenue, len(results), threshold)
			mu.Lock()
			defer mu.Unlock()
			suggestions = append(suggestions, "该站点总收益较低，需排查")
		}
		return nil
	})
	// 收益达成率
	g.GoRecover(func() error {
		if deviceRevenue.MaxRevenueRate == nil {
			log.CtxLog(ctx).Warnf("CalculateDeviceSuggestion, max_revenue_rate is null: %s", ucmd.ToJsonStrIgnoreErr(deviceRevenue))
			return nil
		}
		filter := bson.D{
			{"day", bson.M{"$gte": cond.StartTime, "$lte": cond.EndTime}},
			{"max_revenue_rate", bson.M{"$exists": true, "$ne": primitive.Null{}}},
		}
		// 总数量
		totalCount, gErr := client.GetWatcher().PLCMongodb().NewMongoEntry(filter).Count(mmgo.DBDeviceModel, mmgo.CollectionRevenue)
		if gErr != nil {
			log.CtxLog(ctx).Errorf("CalculateDeviceSuggestion, fail to count max revenue rate: %v, filter: %s", gErr, ucmd.ToJsonStrIgnoreErr(filter))
			return gErr
		}
		// 30%的阈值
		threshold := float64(totalCount) * 0.3
		// 小于当前设备值的数量
		filter = append(filter, bson.E{Key: "max_revenue_rate", Value: bson.M{"$lte": *deviceRevenue.MaxRevenueRate}})
		count, gErr := client.GetWatcher().PLCMongodb().NewMongoEntry(filter).Count(mmgo.DBDeviceModel, mmgo.CollectionRevenue)
		if gErr != nil {
			log.CtxLog(ctx).Errorf("CalculateDeviceSuggestion, fail to count max revenue rate: %v, filter: %s", gErr, ucmd.ToJsonStrIgnoreErr(filter))
			return gErr
		}
		if count <= int64(threshold) {
			log.CtxLog(ctx).Infof("CalculateDeviceSuggestion, device: %s, day: %v, max_revenue_rate: %f, len(results): %d, threshold: %f", cond.DeviceId, util.ConvertTime(cond.StartTime), *deviceRevenue.MaxRevenueRate, count, threshold)
			mu.Lock()
			defer mu.Unlock()
			suggestions = append(suggestions, "站点错峰收益达成率偏低")
		}
		return nil
	})
	// 电池保养比例
	g.GoRecover(func() error {
		if deviceRevenue.BatteryMaintenanceTimes == nil || deviceRevenue.BatteryTotal == nil || *deviceRevenue.BatteryTotal == 0 {
			log.CtxLog(ctx).Warnf("CalculateDeviceSuggestion, battery maintenance times or battery total is null: %s", ucmd.ToJsonStrIgnoreErr(deviceRevenue))
			return nil
		}
		batteryMaintenanceRate := *deviceRevenue.BatteryMaintenanceTimes / *deviceRevenue.BatteryTotal
		filter := bson.D{
			{"day", bson.M{"$gte": cond.StartTime, "$lte": cond.EndTime}},
			{"battery_maintenance_times", bson.M{"$exists": true, "$ne": primitive.Null{}}},
			{"battery_total", bson.M{"$exists": true, "$ne": 0}},
		}
		// 总数量
		totalCount, gErr := client.GetWatcher().PLCMongodb().NewMongoEntry(filter).Count(mmgo.DBDeviceModel, mmgo.CollectionRevenue)
		if gErr != nil {
			log.CtxLog(ctx).Errorf("CalculateDeviceSuggestion, fail to count battery maintenance times: %v, filter: %s", gErr, ucmd.ToJsonStrIgnoreErr(filter))
			return gErr
		}
		// 30%的阈值
		threshold := float64(totalCount) * 0.3
		// 小于当前设备值的数量
		pipeline := mongo.Pipeline{
			{{"$match", filter}},
			{{"$group", bson.M{
				"_id":                             fmt.Sprintf("$%s", AggregateGroupByDeviceId),
				"total_battery_maintenance_times": bson.M{"$sum": "$battery_maintenance_times"},
				"total_battery_total":             bson.M{"$sum": "$battery_total"},
			}}},
			{{"$addFields", bson.M{
				"battery_maintenance_rate": bson.M{
					"$divide": bson.A{
						"$total_battery_maintenance_times",
						"$total_battery_total",
					},
				},
			}}},
			{{"$match", bson.M{"battery_maintenance_rate": bson.M{"$lte": batteryMaintenanceRate}}}},
		}
		var results []bson.M
		gErr = client.GetWatcher().PLCMongodb().NewMongoEntry().Aggregate(mmgo.DBDeviceModel, mmgo.CollectionRevenue, pipeline, &results)
		if gErr != nil {
			log.CtxLog(ctx).Errorf("CalculateDeviceSuggestion, fail to aggregate revenue: %v, pipeline: %s", gErr, ucmd.ToJsonStrIgnoreErr(pipeline))
			return gErr
		}
		if len(results) <= int(threshold) {
			log.CtxLog(ctx).Infof("CalculateDeviceSuggestion, device: %s, day: %v, battery_maintenance_rate: %f, len(results): %d, threshold: %f", cond.DeviceId, util.ConvertTime(cond.StartTime), batteryMaintenanceRate, len(results), threshold)
			mu.Lock()
			defer mu.Unlock()
			suggestions = append(suggestions, "站点电池保养比例偏低")
		}
		return nil
	})
	// 能效收益
	g.GoRecover(func() error {
		if deviceRevenue.EnergyRevenue == nil {
			log.CtxLog(ctx).Warnf("CalculateDeviceSuggestion, energy revenue is null: %s", ucmd.ToJsonStrIgnoreErr(deviceRevenue))
			return nil
		}
		filter := bson.D{
			{"day", bson.M{"$gte": cond.StartTime, "$lte": cond.EndTime}},
			{"energy_revenue", bson.M{"$exists": true, "$ne": primitive.Null{}}},
		}
		// 总数量
		totalCount, gErr := client.GetWatcher().PLCMongodb().NewMongoEntry(filter).Count(mmgo.DBDeviceModel, mmgo.CollectionRevenue)
		if gErr != nil {
			log.CtxLog(ctx).Errorf("CalculateDeviceSuggestion, fail to count energy_revenue: %v, filter: %s", gErr, ucmd.ToJsonStrIgnoreErr(filter))
			return gErr
		}
		// 30%的阈值
		threshold := float64(totalCount) * 0.3
		// 小于当前设备值的数量
		filter = append(filter, bson.E{Key: "energy_revenue", Value: bson.M{"$lte": *deviceRevenue.EnergyRevenue}})
		count, gErr := client.GetWatcher().PLCMongodb().NewMongoEntry(filter).Count(mmgo.DBDeviceModel, mmgo.CollectionRevenue)
		if gErr != nil {
			log.CtxLog(ctx).Errorf("CalculateDeviceSuggestion, fail to count energy_revenue: %v, filter: %s", gErr, ucmd.ToJsonStrIgnoreErr(filter))
			return gErr
		}
		if count <= int64(threshold) {
			log.CtxLog(ctx).Infof("CalculateDeviceSuggestion, device: %s, day: %v, energy_revenue: %f, len(results): %d, threshold: %f", cond.DeviceId, util.ConvertTime(cond.StartTime), *deviceRevenue.EnergyRevenue, count, threshold)
			mu.Lock()
			defer mu.Unlock()
			suggestions = append(suggestions, "站点能效收益偏低，注意水冷、运营或充电的节能情况")
		}
		return nil
	})
	if err = g.Wait(); err != nil {
		log.CtxLog(ctx).Errorf("CalculateDeviceSuggestion, fail to calculate device suggestion: %v", err)
		return
	}
	return
}
