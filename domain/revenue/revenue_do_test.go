package revenue

import (
	"fmt"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	gocache "github.com/patrickmn/go-cache"
	"github.com/stretchr/testify/assert"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	ulog "git.nevint.com/golang-libs/common-utils/logger"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
)

var w client.Watcher
var ctx *gin.Context

func init() {
	os.Setenv("APOLLO_META", "https://apollo-config-cn-stg.nioint.com")
	os.Setenv("ENV", "stg")
	cfg := &ucfg.Config{}
	ucfg.GetConfigApollo("config.json", true, cfg, ucfg.NeedValidate(true), ucfg.ServiceName("ppd-welkin-backend"), ucfg.Cluster(ucfg.GetWelkinApolloCluster()))
	defer ulog.Final()
	cfg.Log.DirPath = "/tmp/logs"
	config.Cfg = cfg
	log.Init(cfg.Log, true)
	logger := log.Logger.Named("activity")
	w = client.NewWatcherByParam(config.Cfg, logger)
	var err error
	if err != nil {
		panic(err)
	}
	cache.CacheRealtimeDataID(w.Mongodb().Client)
	cache.PowerSwapCache = &cache.DeviceInfoCache{
		Cache:         gocache.New(gocache.NoExpiration, gocache.NoExpiration),
		ResourceCache: gocache.New(gocache.NoExpiration, gocache.NoExpiration),
	}
	cache.PowerSwapCache.RefreshDeviceInfoCache(w.Mongodb().Client)
	model.InitConstant(cfg.ExtraConfig["evBrand"])
	ctx = &gin.Context{Request: &http.Request{Header: make(http.Header)}}
	ctx.Request.Header.Set("X-Request-ID", "test")
}

func TestAggregateEnergyByProject(t *testing.T) {
	cond := AggregateRevenueCond{
		StartTime: time.Now().AddDate(0, 0, -14).UnixMilli(),
		EndTime:   time.Now().UnixMilli(),
		Project:   umw.PUS3,
		//DeviceId:    "PS-NIO-00a6f660-cfebfb86",
		CityCompany: "上海公司",
	}
	e := &RevenueDO{}
	res, err := e.AggregateRevenue(ctx, cond, AggregateGroupByProject)
	assert.NoError(t, err)
	assert.NotNil(t, res)
	fmt.Println(len(res), ucmd.ToJsonStrIgnoreErr(res))
}

func TestGetRevenueUpdateTime(t *testing.T) {
	r := &RevenueDO{}
	updateTime, err := r.GetRevenueUpdateTime(ctx, 1735660800000, 1736697600000)
	assert.NoError(t, err)
	assert.NotZero(t, updateTime)
	fmt.Println(updateTime)
}

func TestCalculateDeviceSuggestion(t *testing.T) {
	t1, _ := time.ParseInLocation(time.DateOnly, "2025-01-08", time.Local)
	t2, _ := time.ParseInLocation(time.DateOnly, "2025-01-09", time.Local)
	cond := AggregateRevenueCond{
		StartTime: t1.UnixMilli(),
		EndTime:   t2.UnixMilli(),
		DeviceId:  "PS-NIO-027919ba-20345dac",
	}
	r := &RevenueDO{}
	suggestions, err := r.CalculateDeviceSuggestion(ctx, cond)
	assert.NoError(t, err)
	fmt.Println(suggestions)
}
