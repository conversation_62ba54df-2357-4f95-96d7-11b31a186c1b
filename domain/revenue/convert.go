package revenue

import mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"

func convertRevenuePO2DO(po mmgo.DeviceModelRevenue) RevenueDO {
	do := RevenueDO{
		Day:                       po.Day,
		DeviceId:                  po.DeviceId,
		Project:                   po.Project,
		CityCompany:               po.CityCompany,
		MaxRevenueRate:            po.MaxRevenueRate,
		OffPeakRevenue:            po.OffPeakRevenue,
		EnergyRevenue:             po.EnergyRevenue,
		ChargeRevenue:             po.ChargeRevenue,
		WaterRevenue:              po.WaterRevenue,
		OperationRevenue:          po.OperationRevenue,
		BatteryMaintenanceRevenue: po.BatteryMaintenanceRevenue,
		BatteryMaintenanceTimes:   po.BatteryMaintenanceTimes,
		BatteryTotal:              po.BatteryTotal,
	}
	totalRevenue := 0.0
	if do.OffPeakRevenue != nil {
		totalRevenue += *do.OffPeakRevenue
	}
	if do.EnergyRevenue != nil {
		totalRevenue += *do.EnergyRevenue
	}
	if do.BatteryMaintenanceRevenue != nil {
		totalRevenue += *do.BatteryMaintenanceRevenue
	}
	if totalRevenue != 0 {
		do.TotalRevenue = &totalRevenue
	}
	return do
}
