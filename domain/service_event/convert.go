package service_event

import (
	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/model/mongo"
)

func convertPO2DO(eventPO mongo.MongoMechanicalSwapEvent) *ServiceEventDO {
	project := ""
	device, found := cache.PowerSwapCache.GetSingleDevice(eventPO.DeviceId)
	if found {
		project = device.Project
	}

	eventName := ""
	eventTag, found := cache.SwapEventCache.GetSingleSwapEvent(project, eventPO.EventId)
	if found {
		eventName = eventTag.EventDescription
	}
	res := &ServiceEventDO{
		DeviceId:    eventPO.DeviceId,
		Project:     project,
		Rid:         eventPO.Rid,
		ServiceId:   eventPO.ServiceId,
		EventTs:     eventPO.EventTs,
		EventSource: eventPO.EventSource,
		EventId:     eventPO.EventId,
		EventName:   eventName,
		Context:     eventPO.Context,
	}
	return res
}
