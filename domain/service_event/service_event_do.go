package service_event

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	"git.nevint.com/welkin2/welkin-backend/client"
	mongo_model "git.nevint.com/welkin2/welkin-backend/model/mongo"
	"go.mongodb.org/mongo-driver/bson"
)

const (
	EventSourceSwapStation = 1
	EventSourceShaman      = 2
)

type ServiceEventDO struct {
	DeviceId    string
	Project     string
	Rid         string
	ServiceId   string
	EventTs     int64
	EventSource int
	EventId     string
	EventName   string
	Context     map[string]string
}

type ListServiceEventsCond struct {
	Project        string
	Rid            string
	DeviceId       string
	ServiceId      string
	StartTs        int64
	EndTs          int64
	OrderFieldName string
	Descending     bool
	Limit          int64
	Offset         int64
}

func (s *ServiceEventDO) ListServiceEvents(ctx context.Context, cond ListServiceEventsCond) ([]*ServiceEventDO, int64, error) {
	if cond.Project == "" {
		return nil, 0, errors.New("project param must have!")
	}
	collectionName := fmt.Sprintf("service_event_%v", ucmd.RenameProjectDB(cond.Project))
	filter := bson.D{}
	if cond.Rid != "" {
		filter = append(filter, bson.E{Key: "rid", Value: cond.Rid})
	}
	if cond.DeviceId != "" {
		filter = append(filter, bson.E{Key: "device_id", Value: cond.DeviceId})
	}
	if cond.ServiceId != "" {
		filter = append(filter, bson.E{Key: "service_id", Value: cond.ServiceId})
	}
	if cond.StartTs != 0 || cond.EndTs != 0 {
		filter = append(filter, bson.E{Key: "event_ts", Value: bson.M{"$gte": cond.StartTs, "$lt": cond.EndTs}})
	}
	sortFieldName := "event_ts"
	if cond.OrderFieldName != "" {
		sortFieldName = cond.OrderFieldName
	}
	simulationByteData, total, err := client.GetWatcher().Mongodb().NewMongoEntry(filter).ListByPagination(umw.ServiceInfo, collectionName,
		client.Pagination{Limit: cond.Limit, Offset: cond.Offset},
		client.Ordered{Key: sortFieldName, Descending: cond.Descending})
	if err != nil {
		return nil, 0, err
	}
	var swapEventPOs []mongo_model.MongoMechanicalSwapEvent
	if err = json.Unmarshal(simulationByteData, &swapEventPOs); err != nil {
		return nil, 0, err
	}
	serviceEventDOs := []*ServiceEventDO{}
	for _, swapEventPO := range swapEventPOs {
		serviceEventDOs = append(serviceEventDOs, convertPO2DO(swapEventPO))
	}
	return serviceEventDOs, total, nil
}

func (s *ServiceEventDO) OrganizeEvents(ctx context.Context, serviceEventDOs []*ServiceEventDO) []*ServiceEventDO {

	return nil
}

func (s *ServiceEventDO) GetByOrderId(ctx context.Context, orderId string) (*ServiceEventDO, error) {
	return nil, nil
}
