package psos

import (
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/rs/xid"
	"github.com/gin-gonic/gin"
	gocache "github.com/patrickmn/go-cache"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	ulog "git.nevint.com/golang-libs/common-utils/logger"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	log "git.nevint.com/welkin2/welkin-backend/logger"
)

var cfg *ucfg.Config
var w client.Watcher
var p *Handler

func init() {
	os.Setenv("APOLLO_META", "https://apollo-config-cn-stg.nioint.com")
	os.Setenv("ENV", "stg")
	cfg = &ucfg.Config{}
	ucfg.GetConfigApollo("config.json", true, cfg, ucfg.NeedValidate(true), ucfg.ServiceName("ppd-welkin-backend"), ucfg.Cluster(ucfg.GetWelkinApolloCluster()))
	defer ulog.Final()
	cfg.Log.DirPath = "/tmp/logs"
	log.Init(cfg.Log, true)
	logger := log.Logger.Named("psos_config")
	w = client.NewWatcherByParam(cfg, logger)
	p = NewHandler(w)
	cache.CacheRealtimeDataID(w.Mongodb().Client)
	cache.PowerSwapCache = &cache.DeviceInfoCache{
		Cache:         gocache.New(gocache.NoExpiration, gocache.NoExpiration),
		ResourceCache: gocache.New(gocache.NoExpiration, gocache.NoExpiration),
	}
	cache.PowerSwapCache.RefreshDeviceInfoCache(w.Mongodb().Client)
}

func Test_GetBatteryRealtime(t *testing.T) {
	project := umw.PUS4
	deviceId := "PUS-NIO-095fdc2a-5840a9b6"
	startTime := int64(1728698400000)
	endTime := startTime + 5*time.Minute.Milliseconds()
	res, err := p.GetBatteryRealtime(&gin.Context{}, project, deviceId, startTime, endTime)
	if err != nil {
		t.Error(err)
		return
	}
	fmt.Println(ucmd.ToJsonStrIgnoreErr(res))

	//project = umw.PUS3
	//deviceId = "PS-NIO-3285ff15-7f564f27"
	//startTime = int64(1718208000000)
	//endTime = startTime + 5*time.Minute.Milliseconds()
	//res, err = p.GetBatteryRealtime(&gin.Context{}, project, deviceId, startTime, endTime)
	//if err != nil {
	//	t.Error(err)
	//	return
	//}
	//fmt.Println(ucmd.ToJsonStrIgnoreErr(res))
	//
	//project = umw.PowerSwap2
	//deviceId = "PS-NIO-31da205c-64ba4b36"
	//res, err = p.GetBatteryRealtime(&gin.Context{}, project, deviceId, startTime, endTime)
	//if err != nil {
	//	t.Error(err)
	//	return
	//}
	//fmt.Println(ucmd.ToJsonStrIgnoreErr(res))
}

func TestHandler_GetPriceInfo(t *testing.T) {
	project := umw.PUS3
	deviceList := []string{"PS-NIO-c681eb9d-6bb58423", "PS-NIO-2715111b-9421f1ff"}
	res, err := p.GetPriceInfo(&gin.Context{}, project, deviceList)
	if err != nil {
		t.Error(err)
		return
	}
	for key, val := range res {
		fmt.Println(key, ucmd.ToJsonStrIgnoreErr(val))
	}
}

func TestHandler_GetSwapDuration(t *testing.T) {
	project := umw.PUS3
	ts := int64(1718208000000)
	deviceList := []string{"PS-NIO-b47f7a28-8ebef461", "PS-NIO-bbf37a1f-e060644e"}
	success, fail, err := p.GetSwapDuration(&gin.Context{}, project, ts, deviceList)
	if err != nil {
		t.Error(err)
		return
	}
	fmt.Println(ucmd.ToJsonStrIgnoreErr(success))
	fmt.Println(ucmd.ToJsonStrIgnoreErr(fail))
}

func TestHandler_GetSwapUserList(t *testing.T) {
	project := umw.PUS3
	startTs := int64(1739808000000)
	endTs := int64(1739894400000)
	deviceList := []string{"PS-NIO-3285ff15-7f564f27"}
	res, err := p.GetSwapUserList(&gin.Context{}, project, startTs, endTs, deviceList)
	if err != nil {
		t.Error(err)
		return
	}
	for _, val := range res {
		fmt.Println(ucmd.ToJsonStrIgnoreErr(val))
	}
}

func TestHandler_GetRedrabbitDeviceParams(t *testing.T) {
	project := umw.PUS3
	deviceList := []string{"PS-NIO-3285ff15-7f564f27", "PS-NIO-test1"}
	res, err := p.GetRedrabbitDeviceParams(&gin.Context{}, project, deviceList)
	if err != nil {
		t.Error(err)
		return
	}
	for key, val := range res {
		fmt.Println(key, len(val), ucmd.ToJsonStrIgnoreErr(val))
	}
	fmt.Printf("circuit_distribution_capacity type: %T\n", res["PS-NIO-test1"]["circuit_distribution_capacity"])
	fmt.Printf("branch_circuit_current_limit type: %T\n", res["PS-NIO-test1"]["branch_circuit_current_limit"])
	fmt.Println()

	project = umw.PowerSwap2
	deviceList = []string{"PS-NIO-31da205c-64ba4b36"}
	res, err = p.GetRedrabbitDeviceParams(&gin.Context{}, project, deviceList)
	if err != nil {
		t.Error(err)
		return
	}
	for key, val := range res {
		fmt.Println(key, len(val), ucmd.ToJsonStrIgnoreErr(val))
	}
}

func TestHandler_NewPsosTaskByDevice(t *testing.T) {
	optimizeSwitch := 0
	optimizeMode := 1
	request := NewTaskByDeviceRequest{
		TaskName:        "test1",
		Remark:          nil,
		UserId:          "william.shen2",
		Project:         umw.PUS3,
		StartTs:         1718365370835,
		EndTs:           1718426880860,
		StationCapacity: nil,
		CircuitCapacity: nil,
		SimulationStep:  nil,
		CmsSwitch:       nil,
		Devices:         []string{"PS-NIO-3285ff15-7f564f27"},
		OptimizeSwitch:  &optimizeSwitch,
		OptimizeMode:    &optimizeMode,
		BatteryConfig: [][]float64{
			{0, 9, 9, 1},
			{1, 2, 8, 2},
		},
	}
	taskId := "task_" + xid.New().String()
	fmt.Println(taskId)
	err := p.NewPsosTaskByDevice(&gin.Context{}, request, taskId)
	if err != nil {
		t.Error(err)
	}
}
