package psos

import (
	"fmt"
	"strconv"
	"strings"
	"sync"

	um "git.nevint.com/golang-libs/common-utils/model"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/domain/common"
	"git.nevint.com/welkin2/welkin-backend/model"
	mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"
	"git.nevint.com/welkin2/welkin-backend/util"
)

const (
	ElectricityOnePrice       = "one_price"
	ElectricityDifferentPrice = "different_price"
	PlatformPS2               = "2.0"
	PlatformPUS3              = "3.0"
	PlatformPUS4              = "4.0"

	// ParamStationCapacity 设备整站容量
	ParamStationCapacity = "power_distribution_capacity"
	// ParamCircuitCapacity 各进线容量
	ParamCircuitCapacity = "circuit_distribution_capacity"
	// ParamBranchCurrentLimit 支路限流
	ParamBranchCurrentLimit = "branch_circuit_current_limit"
	// ParamBatteryExchangeSwitch 电池倒仓开关
	ParamBatteryExchangeSwitch = "battery_exchange_switch"
	// ParamSilentModeSwitch 充电策略--静音模式开关
	ParamSilentModeSwitch = "silent_mode_switch"
	// ParamCmsStrategySwitch 充电策略--CMS策略开关
	ParamCmsStrategySwitch = "cms_strategy_switch"
	// ParamCmsFrequency CMS调度频率
	ParamCmsFrequency = "cms_frequency"
	// Param50kWhChargingStopSoc 50度电池充电截止SOC
	Param50kWhChargingStopSoc = "50kWh_charging_stop_soc"
	// Param70kWhChargingStopSoc 70度电池充电截止SOC
	Param70kWhChargingStopSoc = "70kWh_charging_stop_soc"
	// Param75kWhChargingStopSoc 75度电池充电截止SOC
	Param75kWhChargingStopSoc = "75kWh_charging_stop_soc"
	// Param100kWhChargingStopSoc 100度电池充电截止SOC
	Param100kWhChargingStopSoc = "100kWh_charging_stop_soc"
	// Param150kWhChargingStopSoc 150度电池充电截止SOC
	Param150kWhChargingStopSoc = "150kWh_charging_stop_soc"
	// Param60kWhChargingStopSoc 60度电池充电截止SOC
	Param60kWhChargingStopSoc = "60kWh_charging_stop_soc"
	// Param85kWhChargingStopSoc 85度电池充电截止SOC
	Param85kWhChargingStopSoc = "85kWh_charging_stop_soc"

	CollectionTasks       = "psos_tasks"
	CollectionSimulations = "psos_simulations"
	CollectionConfigs     = "psos_configs"
	CollectionServices    = "service_info"
	CollectionFeature     = "psos_feature_data"
	CollectionResult      = "psos_result"
)

var (
	// AlgorithmBatteryList psos仿真算法使用的电池类型
	AlgorithmBatteryList = map[string][]int{
		umw.PowerSwap2: {common.CapacityValue50kwh, common.CapacityValue75kwh, common.CapacityValue100kwh, common.CapacityValue150kwh},
		umw.PUS3:       {common.CapacityValue50kwh, common.CapacityValue75kwh, common.CapacityValue100kwh, common.CapacityValue150kwh, common.CapacityValue60kwh, common.CapacityValue85kwh},
		umw.PUS4:       {common.CapacityValue50kwh, common.CapacityValue75kwh, common.CapacityValue100kwh, common.CapacityValue150kwh, common.CapacityValue60kwh, common.CapacityValue85kwh},
	}

	// SimulateBatteryList psos平台侧使用的电池类型
	SimulateBatteryList = map[string][]int{
		umw.PowerSwap2: {common.CapacityValue50kwh, common.CapacityValue75kwh, common.CapacityValue100kwh, common.CapacityValue150kwh},
		umw.PUS3:       {common.CapacityValue50kwh, common.CapacityValue60kwh, common.CapacityValue75kwh, common.CapacityValue85kwh, common.CapacityValue100kwh, common.CapacityValue150kwh},
		umw.PUS4:       {common.CapacityValue50kwh, common.CapacityValue60kwh, common.CapacityValue75kwh, common.CapacityValue85kwh, common.CapacityValue100kwh, common.CapacityValue150kwh},
	}

	// ServiceRealBatteryList 服务自动生成时，生成的仿真使用的真实电池类型
	ServiceRealBatteryList = map[string][]int{
		umw.PowerSwap2: {common.CapacityValue50kwh, common.CapacityValue70kwh, common.CapacityValue75kwh, common.CapacityValue100kwh, common.CapacityValue150kwh},
		umw.PUS3:       {common.CapacityValue50kwh, common.CapacityValue70kwh, common.CapacityValue75kwh, common.CapacityValue100kwh, common.CapacityValue150kwh, common.CapacityValue60kwh, common.CapacityValue85kwh},
		umw.PUS4:       {common.CapacityValue50kwh, common.CapacityValue70kwh, common.CapacityValue75kwh, common.CapacityValue100kwh, common.CapacityValue150kwh, common.CapacityValue60kwh, common.CapacityValue85kwh},
	}

	// SimulationLimit 单个任务最多仿真次数
	SimulationLimit = 240

	// BatterySlotNum 电池仓数量
	BatterySlotNum = map[string]int{
		umw.PowerSwap2: 13,
		umw.PUS3:       21,
		umw.PUS4:       23,
	}

	// BatterySlotBatteryNum 电池仓内最多电池数量
	BatterySlotBatteryNum = map[string]int{
		umw.PowerSwap2: 13,
		umw.PUS3:       20,
		umw.PUS4:       23,
	}

	// SwappingTimeSuccess 成功换电时长
	SwappingTimeSuccess = map[string]int{
		umw.PowerSwap2: 317,
		umw.PUS3:       307,
		umw.PUS4:       240,
	}

	// SwappingTimeFault 失败换电时长
	SwappingTimeFault = map[string]int{
		umw.PowerSwap2: 461,
		umw.PUS3:       523,
		umw.PUS4:       550,
	}

	// PowerDistributionCapacity 设备整站容量
	PowerDistributionCapacity = map[string]int{
		umw.PowerSwap2: 500,
		umw.PUS3:       500,
		umw.PUS4:       500,
	}

	// CircuitDistributionCapacity 各进线容量
	CircuitDistributionCapacity = map[string][]int{
		umw.PowerSwap2: {250, 250},
		umw.PUS3:       {250, 250},
		umw.PUS4:       {250, 250},
	}

	// ChargingStopSoc 电池充电截止soc
	ChargingStopSoc50kWh = map[string]int{
		umw.PowerSwap2: 93,
		umw.PUS3:       93,
		umw.PUS4:       93,
	}
	ChargingStopSoc70kWh = map[string]int{
		umw.PowerSwap2: 93,
		umw.PUS3:       93,
		umw.PUS4:       93,
	}
	ChargingStopSoc75kWh = map[string]int{
		umw.PowerSwap2: 93,
		umw.PUS3:       93,
		umw.PUS4:       93,
	}
	ChargingStopSoc100kWh = map[string]int{
		umw.PowerSwap2: 93,
		umw.PUS3:       93,
		umw.PUS4:       93,
	}
	ChargingStopSoc150kWh = map[string]int{
		umw.PowerSwap2: 93,
		umw.PUS3:       93,
		umw.PUS4:       93,
	}
	ChargingStopSoc60kWh = map[string]int{
		umw.PowerSwap2: 93,
		umw.PUS3:       93,
		umw.PUS4:       93,
	}
	ChargingStopSoc85kWh = map[string]int{
		umw.PowerSwap2: 93,
		umw.PUS3:       93,
		umw.PUS4:       93,
	}

	// ElectricityPriceModel 电价模式
	ElectricityPriceModel = map[string]string{
		umw.PowerSwap2: "one_price",
		umw.PUS3:       "one_price",
		umw.PUS4:       "one_price",
	}

	// BatteryTypeConfig 初始电池配比
	BatteryTypeConfig = map[string][]float64{
		umw.PowerSwap2: {0, 9, 4, 0},
		umw.PUS3:       {0, 11, 9, 0, 0, 0},
		umw.PUS4:       {0, 13, 10, 0, 0, 0},
	}

	// BranchCircuitCurrentLimit 支路限流
	BranchCircuitCurrentLimit = map[string][]int{
		umw.PowerSwap2: {-250, -250, -250, -250, -250, -250, -250, -250, -250, -250, -250, -250, -250},
		umw.PUS3:       {-0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -250, -250, -250, -250, -250, -250, -250, -250, -250, -250, -0},
		umw.PUS4:       {-0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -250, -250, -250, -250, -250, -250, -250, -250, -250, -250, -0},
	}

	// OperationTime 运营时间
	OperationTime = map[string][]int{
		umw.PowerSwap2: {0, 24},
		umw.PUS3:       {0, 24},
		umw.PUS4:       {0, 24},
	}

	// OperationTimeList 运营时间可选项
	OperationTimeList = map[string]bool{
		"0,24": true,
		"7,22": true,
		"8,20": true,
		"8,22": true,
		"9,21": true,
	}

	// ElectricityDetailsOnePrice 电价信息，one_price
	ElectricityDetailsOnePrice = map[string][]float64{
		umw.PowerSwap2: {0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8},
		umw.PUS3:       {0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8},
		umw.PUS4:       {0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8},
	}

	// ElectricityDetailsDifferentPrice 电价信息，different_price
	ElectricityDetailsDifferentPrice = map[string][]float64{
		umw.PowerSwap2: {0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 0.6835, 0.6835, 0.6835, 0.6835},
		umw.PUS3:       {0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 0.6835, 0.6835, 0.6835, 0.6835},
		umw.PUS4:       {0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 0.6835, 0.6835, 0.6835, 0.6835},
	}

	// BatterySocLowerLimit 非满电换电电池SOC下限
	BatterySocLowerLimit = map[string]int{
		umw.PowerSwap2: 78,
		umw.PUS3:       78,
		umw.PUS4:       78,
	}

	// BatterySocUpperLimit 非满电换电电池SOC上限
	BatterySocUpperLimit = map[string]int{
		umw.PowerSwap2: 87,
		umw.PUS3:       87,
		umw.PUS4:       87,
	}

	// FullyBatteryRatio 满电电池比例
	FullyBatteryRatio = map[string]float64{
		umw.PowerSwap2: 0.5,
		umw.PUS3:       0.5,
		umw.PUS4:       0.5,
	}

	// CircuitNum 设备进线数量
	CircuitNum = map[string]int{
		umw.PowerSwap2: 2,
		umw.PUS3:       2,
		umw.PUS4:       2,
	}

	// ModuleSupplier 模块供应商
	ModuleSupplier = map[string]string{
		umw.PowerSwap2: "UU",
		umw.PUS3:       "UU",
		umw.PUS4:       "UU",
	}

	// ModuleNum 模块数量
	ModuleNum = map[string]int{
		umw.PowerSwap2: 13,
		umw.PUS3:       10,
		umw.PUS4:       16,
	}

	// PerSubmoduleNum 每个模块中包含的子模块数量
	PerSubmoduleNum = map[string]int{
		umw.PowerSwap2: 2,
		umw.PUS3:       3,
		umw.PUS4:       1,
	}

	// SubmodulePowerLimit 子模块最大功率
	SubmodulePowerLimit = map[string]int{
		umw.PowerSwap2: 20,
		umw.PUS3:       20,
		umw.PUS4:       40,
	}

	// SwapBatterySlot 换电仓位序列
	SwapBatterySlot = map[string][]int{
		umw.PowerSwap2: {1, 3, 5, 7, 9, 11, 13, 2, 4, 6, 8, 10, 12},
		umw.PUS3:       {1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20},
		umw.PUS4:       {1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22},
	}

	// StorableBatterySlot 存放电池仓位
	StorableBatterySlot = map[string][]int{
		umw.PowerSwap2: {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13},
		umw.PUS3:       {11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 21},
		umw.PUS4:       {13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12},
	}

	// GenerateBatterySlot 随机生成电池的仓位顺序
	GenerateBatterySlot = map[string][]int{
		umw.PowerSwap2: {1, 3, 5, 7, 9, 11, 13, 2, 4, 6, 8, 10, 12},
		umw.PUS3:       {11, 13, 15, 17, 19, 12, 14, 16, 18, 20, 1, 3, 5, 7, 9, 2, 4, 6, 8, 10, 21},
		umw.PUS4:       {13, 15, 17, 19, 21, 14, 16, 18, 20, 22, 1, 3, 5, 7, 9, 11, 2, 4, 6, 8, 10, 12, 23},
	}

	// CircuitLineAllocation 各进线仓位配置
	CircuitLineAllocation = map[string]map[string][]int{
		umw.PowerSwap2: {
			"1": {1, 2, 3, 4, 5, 13},
			"2": {6, 7, 8, 9, 10, 11, 12},
		},
		umw.PUS3: {
			"1": {11, 12, 13, 14, 15},
			"2": {16, 17, 18, 19, 20},
		},
		umw.PUS4: {
			"1": {13, 14, 15, 16, 17},
			"2": {18, 19, 20, 21, 22},
		},
	}

	// CoupleStructure 各仓位模块配置
	CoupleStructure = map[string]map[string][]int{
		umw.PowerSwap2: {
			"1":  {1, 2},
			"2":  {2, 1},
			"3":  {3, 4},
			"4":  {4, 3},
			"5":  {5, 6},
			"6":  {6, 5},
			"7":  {7, 8},
			"8":  {8, 7},
			"9":  {9, 10},
			"10": {10, 9},
			"11": {11, 12},
			"12": {12, 11},
			"13": {13},
		},
		umw.PUS3: {
			"11": {1, 2},
			"12": {2, 1},
			"13": {3, 4},
			"14": {4, 3},
			"15": {5, 6},
			"16": {6, 5},
			"17": {7, 8},
			"18": {8, 7},
			"19": {9, 10},
			"20": {10, 9},
		},
		umw.PUS4: {
			"13": {2, 1, 3, 4, 5, 6, 7, 8},
			"14": {3, 1, 2, 4, 5, 6, 7, 8},
			"15": {6, 5, 7, 8, 1, 2, 3, 4},
			"16": {7, 5, 6, 8, 1, 2, 3, 4},
			"17": {8, 5, 6, 7, 1, 2, 3, 4},
			"18": {10, 9, 11, 12, 13, 14, 15, 16},
			"19": {11, 9, 10, 12, 13, 14, 15, 16},
			"20": {14, 13, 15, 16, 9, 10, 11, 12},
			"21": {15, 13, 14, 16, 9, 10, 11, 12},
			"22": {16, 13, 14, 15, 9, 10, 11, 12},
		},
	}

	// BatteryPackMaxTemperature 站内电池温度
	BatteryPackMaxTemperature = 26.0

	// BatterySoc 站内电池soc
	BatterySoc = 30.0

	// BatteryChargingStopSoc 站内电池停止充电soc
	BatteryChargingStopSoc = 93.0

	// ServiceBatteryTemperature 换电服务进站电池温度
	ServiceBatteryTemperature = 26

	// ServiceBatterySoc 换电服务进站电池soc
	ServiceBatterySoc = 10

	// ServiceCrossSwapBatteryType 跨级换电默认电池类型
	ServiceCrossSwapBatteryType = common.CapacityValue75kwh

	DefaultBatteryOwnership = "NIO"
	DefaultUserOwnership    = "BaaS"
	DefaultBatteryRestLabel = 1
)

// RedRabbitDataId 赤兔数据点，格式：{"PUS3": {902417: "power_distribution_capacity"}}
var RedRabbitDataId map[string]map[int64]string

// RedRabbitDataIdList 需要的赤兔数据点列表，格式：{"PUS3": [902417, 902418, ....]}
var RedRabbitDataIdList map[string][]int64

type PriceInfo struct {
	mmgo.PsosScenarioInfo
	OperationTime []int `json:"operation_time" bson:"operation_time"`
}

type NewTaskByDeviceRequest struct {
	TaskName        string      `json:"task_name" binding:"required"`
	Remark          *string     `json:"remark"`
	UserId          string      `json:"user_id"`
	Project         string      `json:"project" binding:"required"`
	StartTs         int64       `json:"start_ts" binding:"required"`
	EndTs           int64       `json:"end_ts" binding:"required"`
	StationCapacity *int        `json:"station_capacity"`
	CircuitCapacity *int        `json:"circuit_capacity"`
	SimulationStep  *int        `json:"simulation_step"`
	CmsSwitch       *int        `json:"cms_switch"`
	Devices         []string    `json:"devices" binding:"required"`
	OptimizeSwitch  *int        `json:"optimize_switch" binding:"required"`
	OptimizeMode    *int        `json:"optimize_mode"`
	BatteryConfig   [][]float64 `json:"battery_config"`
}

type NewTaskByConfigRequest struct {
	TaskName       string      `json:"task_name" binding:"required"`
	Remark         *string     `json:"remark"`
	UserId         string      `json:"user_id"`
	Project        string      `json:"project" binding:"required"`
	SimulationStep *int        `json:"simulation_step"`
	OptimizeSwitch *int        `json:"optimize_switch" binding:"required"`
	OptimizeMode   *int        `json:"optimize_mode"`
	BatteryConfig  [][]float64 `json:"battery_config"`
	Configs        []string    `json:"configs" binding:"required"`
}

type NewTaskResponse struct {
	um.Base
	TaskId string `json:"task_id"`
}

type BatteryRestSwitch struct {
	SwitchValue              int `json:"switch_value"`
	DefaultRestCurrent       int `json:"default_rest_current"`
	DefaultHangingDuration   int `json:"default_hanging_duration"`
	DefaultHangingStep       int `json:"default_hanging_step"`
	DefaultHangingCurrentMax int `json:"default_hanging_current_max"`
}

type CommonPsosConfig struct {
	ConfigName            string                             `json:"config_name"`
	Remark                string                             `json:"remark"`
	Project               string                             `json:"project"`
	StartTs               int64                              `json:"start_ts"`
	EndTs                 int64                              `json:"end_ts"`
	StationCapacity       *int                               `json:"station_capacity"`
	Circuit1Capacity      *int                               `json:"circuit_1_capacity"`
	Circuit2Capacity      *int                               `json:"circuit_2_capacity"`
	CmsSwitch             *int                               `json:"cms_switch"`
	ElectricityPriceModel *string                            `json:"electricity_price_model"`
	ElectricityDetailList []mmgo.ElectricityDetailUserConfig `json:"electricity_detail_list"`
	OperationStartHour    *int                               `json:"operation_start_hour"`
	OperationEndHour      *int                               `json:"operation_end_hour"`
	NotfullySwapSwitch    *NotfullySwapSwitch                `json:"notfully_swap_switch"`
	SilentModeSwitch      *int                               `json:"silent_mode_switch"`
	BatteryExchangeSwitch *int                               `json:"battery_exchange_switch"`
	IsRealDevice          *bool                              `json:"is_real_device"`
	BatteryRestSwitch     *BatteryRestSwitch                 `json:"battery_rest_switch"`
}

type PsosBatchConfigRequest struct {
	CommonPsosConfig
	Devices []string `json:"devices"`
}

type PsosSingleConfigRequest struct {
	CommonPsosConfig
	DeviceId              string                 `json:"device_id"`
	BatteryInfo           []mmgo.PsosBatteryInfo `json:"battery_info"`
	ServiceList           []mmgo.SwappingUser    `json:"service_list"`
	ConfigId              *string                `json:"config_id"`
	SkipLevelSwapSwitch   int                    `json:"skip_level_swap_switch"`
	SwappingFailureSwitch int                    `json:"swapping_failure_switch"`
}

type GenerateRandomRequest struct {
	Project               string `json:"project" form:"project"`
	DeviceId              string `json:"device_id" form:"device_id"`
	StartTs               int64  `json:"start_ts" form:"start_ts"`
	EndTs                 int64  `json:"end_ts" form:"end_ts"`
	ServiceCount          string `json:"service_count" form:"service_count"`
	BatteryConfig         string `json:"battery_config" form:"battery_config"`
	SkipLevelSwapSwitch   int    `json:"skip_level_swap_switch" form:"skip_level_swap_switch"`
	SwappingFailureSwitch int    `json:"swapping_failure_switch" form:"swapping_failure_switch"`
	OperationStartHour    int    `json:"operation_start_hour" form:"operation_start_hour"`
	OperationEndHour      int    `json:"operation_end_hour" form:"operation_end_hour"`
}

type ListPsosConfigsRequest struct {
	model.CommonUriParam
	ConfigId   *string `json:"config_id" form:"config_id"`
	ConfigName *string `json:"config_name" form:"config_name"`
	Creator    *string `json:"creator" form:"creator"`
	Project    *string `json:"project" form:"project"`
}

type ConfigInfo struct {
	ConfigName   string `json:"config_name" bson:"config_name"`
	ConfigId     string `json:"config_id" bson:"_id"`
	IsRealDevice bool   `json:"is_real_device" bson:"is_real_device"`
	DeviceInfo   struct {
		DeviceId string `json:"device_id" bson:"device_id"`
	} `json:"device_info" bson:"device_info"`
	DeviceId      string `json:"device_id" bson:"device_id"`
	Description   string `json:"description" bson:"description"`
	Remark        string `json:"remark" bson:"remark"`
	Project       string `json:"project" bson:"project"`
	Creator       string `json:"creator" bson:"creator"`
	CreatorAvatar string `json:"creator_avatar" bson:"creator_avatar"`
	CreateTs      int64  `json:"create_ts" bson:"create_ts"`
	UpdateTs      int64  `json:"update_ts" bson:"update_ts"`
}

type ListPsosConfigsResponse struct {
	um.Base
	Total int64        `json:"total"`
	Data  []ConfigInfo `json:"data"`
}

type ConfigDetail struct {
	ConfigName            string                             `json:"config_name"`
	ConfigNameOrigin      string                             `json:"config_name_origin"`
	Remark                string                             `json:"remark"`
	StartTs               int64                              `json:"start_ts"`
	EndTs                 int64                              `json:"end_ts"`
	StationCapacity       int                                `json:"station_capacity"`
	Circuit1Capacity      int                                `json:"circuit_1_capacity"`
	Circuit2Capacity      int                                `json:"circuit_2_capacity"`
	CmsSwitch             int                                `json:"cms_switch"`
	ElectricityPriceModel string                             `json:"electricity_price_model"`
	ElectricityDetailList []mmgo.ElectricityDetailUserConfig `json:"electricity_detail_list"`
	DeviceId              string                             `json:"device_id,omitempty"`
	Description           string                             `json:"description"`
	BatteryInfo           []mmgo.PsosBatteryInfo             `json:"battery_info"`
	ServiceList           []mmgo.SwappingUser                `json:"service_list"`
	IsRealDevice          bool                               `json:"is_real_device"`
	SkipLevelSwapSwitch   int                                `json:"skip_level_swap_switch"`
	SwappingFailureSwitch int                                `json:"swapping_failure_switch"`
	OperationStartHour    int                                `json:"operation_start_hour"`
	OperationEndHour      int                                `json:"operation_end_hour"`
	NotfullySwapSwitch    NotfullySwapSwitch                 `json:"notfully_swap_switch"`
	SilentModeSwitch      int                                `json:"silent_mode_switch"`
	BatteryExchangeSwitch int                                `json:"battery_exchange_switch"`
	BatteryRestSwitch     BatteryRestSwitch                  `json:"battery_rest_switch"`
	Project               string                             `json:"project"`
}

type NotfullySwapSwitch struct {
	SwitchValue   int `json:"switch_value"`
	SocLowerLimit int `json:"soc_lower_limit"`
	SocUpperLimit int `json:"soc_upper_limit"`
}

type GetConfigResponse struct {
	um.Base
	Data ConfigDetail `json:"data"`
}

type TDengineBatteryInfo struct {
	SlotId              int     `json:"slot_id" bson:"slot_id"`
	BatteryId           string  `json:"battery_id" bson:"battery_id,omitempty"`
	RealBatteryRatedKwh int     `json:"real_battery_rated_kwh" bson:"real_battery_rated_kwh,omitempty"`
	BatterySoc          float64 `json:"battery_soc" bson:"battery_soc,omitempty"`
	ChargingStopSoc     float64 `json:"charging_stop_soc" bson:"charging_stop_soc,omitempty"`
	PackMaxTemperature  float64 `json:"pack_max_temperature" bson:"pack_max_temperature,omitempty"`
}

type SimulationAllDevicesRequest struct {
	Project        string   `json:"project"`
	ChargeStrategy string   `json:"charge_strategy"`
	StartTs        int64    `json:"start_ts"`
	EndTs          int64    `json:"end_ts"`
	Limit          *int     `json:"limit"`           // 测试用，限制仿真数量
	FromCheckpoint bool     `json:"from_checkpoint"` // 补数据用
	DeviceIds      []string `json:"device_ids"`      // 补数据用
}

var once sync.Once

// 电池实时数据，data_id映射到对应的slot_id
var BatteryRealtimeSlotPS2 = make(map[int]int)
var BatteryRealtimeSlotPUS3 = make(map[int]int)
var BatteryRealtimeSlotPUS4 = make(map[int]int)

func InitOnce() {
	once.Do(func() {
		for index := 1; index <= 13; index++ {
			BatteryRealtimeSlotPS2[500100+index*1000] = index // 电池ID
			BatteryRealtimeSlotPS2[500156+index*1000] = index // 电池类型
			BatteryRealtimeSlotPS2[500108+index*1000] = index // 电池用户SOC
			BatteryRealtimeSlotPS2[500122+index*1000] = index // 电池最高电芯温度
		}
		for id := 1; id <= 21; id++ {
			BatteryRealtimeSlotPUS3[1000*id+1] = id  // 电池ID
			BatteryRealtimeSlotPUS3[1000*id+12] = id // 电池类型
			BatteryRealtimeSlotPUS3[1000*id+22] = id // 电池用户SOC
			BatteryRealtimeSlotPUS3[1000*id+36] = id // 电池最高电芯温度
		}
		for id := 0; id < 23; id++ {
			BatteryRealtimeSlotPUS4[20000*id+374001] = id + 1 // BMS电池蔚来ID
			BatteryRealtimeSlotPUS4[20000*id+374012] = id + 1 // BMS电池类型
			BatteryRealtimeSlotPUS4[20000*id+374023] = id + 1 // BMS用户SOC
			BatteryRealtimeSlotPUS4[20000*id+374037] = id + 1 // BMS最高电芯温度
		}
		// 赤兔数据点
		RedRabbitDataId = make(map[string]map[int64]string)
		RedRabbitDataId[umw.PowerSwap2] = make(map[int64]string)
		RedRabbitDataId[umw.PowerSwap2][870007] = ParamStationCapacity        // 设备整站容量
		RedRabbitDataId[umw.PowerSwap2][870060] = ParamCircuitCapacity + "-0" // 各进线容量
		RedRabbitDataId[umw.PowerSwap2][870061] = ParamCircuitCapacity + "-1" // 各进线容量
		for id := 0; id <= 12; id++ {                                         // 支路限流
			RedRabbitDataId[umw.PowerSwap2][int64(850000+id)] = ParamBranchCurrentLimit + fmt.Sprintf("-%d", id)
		}
		RedRabbitDataId[umw.PowerSwap2][870013] = ParamSilentModeSwitch      // 静音模式开关
		RedRabbitDataId[umw.PowerSwap2][870128] = ParamCmsStrategySwitch     // CMS策略开关
		RedRabbitDataId[umw.PowerSwap2][870140] = ParamCmsFrequency          // CMS调度频率
		RedRabbitDataId[umw.PowerSwap2][850014] = Param70kWhChargingStopSoc  // 70度电池充电截止SOC
		RedRabbitDataId[umw.PowerSwap2][850046] = Param75kWhChargingStopSoc  // 75度电池充电截止SOC
		RedRabbitDataId[umw.PowerSwap2][850038] = Param100kWhChargingStopSoc // 100度电池充电截止SOC
		RedRabbitDataId[umw.PowerSwap2][850064] = Param150kWhChargingStopSoc // 150度电池充电截止SOC
		RedRabbitDataId[umw.PUS3] = make(map[int64]string)
		RedRabbitDataId[umw.PUS3][902416] = ParamStationCapacity        // 设备整站容量
		RedRabbitDataId[umw.PUS3][902417] = ParamCircuitCapacity + "-0" // 各进线容量
		RedRabbitDataId[umw.PUS3][902418] = ParamCircuitCapacity + "-1" // 各进线容量
		for id := 0; id <= 9; id++ {                                    // 支路限流
			RedRabbitDataId[umw.PUS3][int64(900000+id)] = ParamBranchCurrentLimit + fmt.Sprintf("-%d", id)    // C仓
			RedRabbitDataId[umw.PUS3][int64(900100+id)] = ParamBranchCurrentLimit + fmt.Sprintf("-%d", id+10) // A仓
		}
		RedRabbitDataId[umw.PUS3][902439] = ParamBatteryExchangeSwitch + "-0" // 倒仓开关
		RedRabbitDataId[umw.PUS3][902409] = ParamBatteryExchangeSwitch + "-1" // 倒仓开关
		RedRabbitDataId[umw.PUS3][902205] = ParamSilentModeSwitch             // 静音模式开关
		RedRabbitDataId[umw.PUS3][902428] = ParamCmsStrategySwitch            // CMS策略开关
		RedRabbitDataId[umw.PUS3][902442] = ParamCmsFrequency                 // CMS调度频率
		RedRabbitDataId[umw.PUS3][901002] = Param70kWhChargingStopSoc         // 70度电池充电截止SOC
		RedRabbitDataId[umw.PUS3][901302] = Param75kWhChargingStopSoc         // 75度电池充电截止SOC
		RedRabbitDataId[umw.PUS3][901202] = Param100kWhChargingStopSoc        // 100度电池充电截止SOC
		RedRabbitDataId[umw.PUS3][901402] = Param150kWhChargingStopSoc        // 150度电池充电截止SOC
		RedRabbitDataId[umw.PUS3][901502] = Param60kWhChargingStopSoc         // 60度电池充电截止SOC
		RedRabbitDataId[umw.PUS4] = make(map[int64]string)
		RedRabbitDataId[umw.PUS4][972031] = ParamStationCapacity        // 设备整站容量
		RedRabbitDataId[umw.PUS4][972032] = ParamCircuitCapacity + "-0" // 各进线容量
		RedRabbitDataId[umw.PUS4][972033] = ParamCircuitCapacity + "-1" // 各进线容量
		for id := 0; id <= 9; id++ {                                    // 支路限流
			RedRabbitDataId[umw.PUS4][int64(970000+id)] = ParamBranchCurrentLimit + fmt.Sprintf("-%d", id+12) // A仓
		}
		RedRabbitDataId[umw.PUS4][972025] = ParamBatteryExchangeSwitch + "-0" // 倒仓开关
		RedRabbitDataId[umw.PUS4][972050] = ParamBatteryExchangeSwitch + "-1" // 倒仓开关
		RedRabbitDataId[umw.PUS4][972006] = ParamSilentModeSwitch             // 静音模式开关
		RedRabbitDataId[umw.PUS4][972064] = ParamCmsStrategySwitch            // CMS策略开关
		RedRabbitDataId[umw.PUS4][972066] = ParamCmsFrequency                 // CMS调度频率
		RedRabbitDataId[umw.PUS4][970102] = Param70kWhChargingStopSoc         // 70度电池充电截止SOC
		RedRabbitDataId[umw.PUS4][970402] = Param75kWhChargingStopSoc         // 75度电池充电截止SOC
		RedRabbitDataId[umw.PUS4][970302] = Param100kWhChargingStopSoc        // 100度电池充电截止SOC
		RedRabbitDataId[umw.PUS4][970502] = Param150kWhChargingStopSoc        // 150度电池充电截止SOC
		RedRabbitDataId[umw.PUS4][970602] = Param60kWhChargingStopSoc         // 60度电池充电截止SOC

		// 赤兔数据点列表
		RedRabbitDataIdList = make(map[string][]int64)
		for project, idMap := range RedRabbitDataId {
			for id := range idMap {
				RedRabbitDataIdList[project] = append(RedRabbitDataIdList[project], id)
			}
			//fmt.Println(ucmd.ToJsonStrIgnoreErr(idMap))
			//fmt.Println(project, ucmd.ToJsonStrIgnoreErr(RedRabbitDataIdList[project]))
		}
	})
}

// ConvertDataId 转换电池data_id到对应的仓位和字段
func ConvertDataId(project string, dataId string) (handleVal func(interface{}) interface{}, slot int, field string, err error) {
	dataIdInt, err := strconv.Atoi(dataId)
	if err != nil {
		return
	}
	handleVal = func(i interface{}) interface{} {
		return i
	}
	// 二三代站上传的电池类型的点，数据类型解析出来不同，需要统一成int
	handleBatteryType := func(i interface{}) interface{} {
		return ConvertPsosBatteryType(util.ParseInt(i))
	}
	if project == umw.PowerSwap2 {
		slot = BatteryRealtimeSlotPS2[dataIdInt]
		if strings.HasSuffix(dataId, "100") {
			field = "BatteryId"
		}
		if strings.HasSuffix(dataId, "156") {
			field = "RealBatteryRatedKwh"
			handleVal = handleBatteryType
		}
		if strings.HasSuffix(dataId, "108") {
			field = "BatterySoc"
		}
		if strings.HasSuffix(dataId, "122") {
			field = "PackMaxTemperature"
		}
		return
	} else if project == umw.PUS3 {
		slot = BatteryRealtimeSlotPUS3[dataIdInt]
		if strings.HasSuffix(dataId, "01") {
			field = "BatteryId"
		}
		if strings.HasSuffix(dataId, "12") {
			field = "RealBatteryRatedKwh"
			handleVal = handleBatteryType
		}
		if strings.HasSuffix(dataId, "22") {
			field = "BatterySoc"
		}
		if strings.HasSuffix(dataId, "36") {
			field = "PackMaxTemperature"
		}
		return
	} else if project == umw.PUS4 {
		slot = BatteryRealtimeSlotPUS4[dataIdInt]
		if strings.HasSuffix(dataId, "4001") {
			field = "BatteryId"
		}
		if strings.HasSuffix(dataId, "4012") {
			field = "RealBatteryRatedKwh"
			handleVal = handleBatteryType
		}
		if strings.HasSuffix(dataId, "4023") {
			field = "BatterySoc"
		}
		if strings.HasSuffix(dataId, "4037") {
			field = "PackMaxTemperature"
		}
		return
	}
	err = fmt.Errorf("fail to convert data id, project: %s, dataId: %s", project, dataId)
	return
}

// ConvertPsosBatteryType 将站上传的原始电池类型转为真实容量数值
func ConvertPsosBatteryType(batteryType int) int {
	originType := int32(batteryType)
	userCapacity := common.ConvertBatteryUserType(&originType)
	if userCapacity == nil {
		return -1
	}
	return int(*userCapacity)
}

// ConvertPsosBatteryCapacity 将电池容量枚举转为真实容量数值
func ConvertPsosBatteryCapacity(capacityType int32) int {
	userCapacity := common.ConvertBatteryCapacity(&capacityType)
	if userCapacity == nil {
		return -1
	}
	return int(*userCapacity)
}

// ConvertPsosRealBatteryType 转换真实电池容量（与真实电池情况可能不符，仅供psos使用）
func ConvertPsosRealBatteryType(realType int) int {
	switch realType {
	case common.CapacityValue70kwh:
		return common.CapacityValue75kwh
	}
	return realType
}

type PsosSimulationTaskStatusStat struct {
	CreateCount  int64 `json:"create_count"`
	RunCount     int64 `json:"run_count"`
	SuccessCount int64 `json:"success_count"`
	FailCount    int64 `json:"fail_count"`
}

type PsosFmsCompressInfo struct {
	DeviceCompressTaskStatus  string `json:"device_compress_task_status"`
	BatteryCompressTaskStatus string `json:"battery_compress_task_status"`
	ServiceCompressTaskStatus string `json:"service_compress_task_status"`
	DeviceResultUrl           string `json:"device_result_url"`
	BatteryResultUrl          string `json:"battery_result_url"`
	ServiceResultUrl          string `json:"service_result_url"`
}

type PsosTaskVO struct {
	TaskId        string                       `json:"task_id"`
	TaskName      string                       `json:"task_name"`
	Remark        string                       `json:"remark"`
	Project       string                       `json:"project"`
	Status        string                       `json:"status"`
	Creator       string                       `json:"creator"`
	CreatorAvatar string                       `json:"creator_avatar"`
	StatusDetail  PsosSimulationTaskStatusStat `json:"status_detail"`
	CreateTs      int64                        `json:"create_ts"`
	UpdateTs      int64                        `json:"update_ts"`
}

type ListPsosTasksResponse struct {
	um.Base
	Total int64        `json:"total"`
	Data  []PsosTaskVO `json:"data"`
}

type PsosSimulationVO struct {
	SimulationId           string               `json:"simulation_id"`
	TaskId                 string               `json:"task_id"`
	ConfigId               string               `json:"config_id"`
	Status                 string               `json:"status"`
	IsRealDevice           bool                 `json:"is_real_device"`
	Description            string               `json:"description"`
	DeviceResultUrl        string               `json:"device_result_url"`
	BatteryResultUrl       string               `json:"battery_result_url"`
	ServiceResultUrl       string               `json:"service_result_url"`
	AvgSwappingQueueTime   float64              `json:"avg_swapping_queue_time"`
	AvgBatteryChargingTime float64              `json:"avg_battery_charging_time"`
	AvgCapacityUtilization float64              `json:"avg_capacity_utilization"`
	BatteryElectricityCost float64              `json:"battery_electricity_cost"`
	ServiceCount           int64                `json:"service_count"`
	Battery50KwCount       int64                `json:"battery_50_kw_count"`
	Battery75KwCount       int64                `json:"battery_75_kw_count"`
	Battery100KwCount      int64                `json:"battery_100_kw_count"`
	Battery150KwCount      int64                `json:"battery_150_kw_count"`
	Battery60KwCount       int64                `json:"battery_60_kw_count"`
	Battery85KwCount       int64                `json:"battery_85_kw_count"`
	ProgressRate           float64              `json:"progress_rate"`
	SimulationConfig       ConfigDetail         `json:"simulation_config"`
	SimulationReport       PsosSimulationReport `json:"simulation_report"`
	StartRunTimestamp      int64                `json:"start_run_timestamp"`
	CreateTimestamp        int64                `json:"create_timestamp"`
	UpdateTimestamp        int64                `json:"update_timestamp"`
}

type PsosSimulationReport struct {
	CmsSwitch                int                      `json:"cms_switch"`
	ElectricityPriceModel    string                   `json:"electricity_price_model"`
	OperationStartHour       int                      `json:"operation_start_hour"`
	OperationEndHour         int                      `json:"operation_end_hour"`
	Project                  string                   `json:"project"`
	SimulationDuration       int64                    `json:"simulation_duration"`
	ContributionMargin       float64                  `json:"contribution_margin"`
	AvgSwappingQueueTime     float64                  `json:"avg_swapping_queue_time"`
	BatteryElectricityCost   float64                  `json:"battery_electricity_cost"`
	AvgCapacityUtilization   float64                  `json:"avg_capacity_utilization"`
	UserReport               PsosUserReport           `json:"user_report"`
	SwapStationBatteryReport SwapStationBatteryReport `json:"swap_station_battery_report"`
	DeviceReport             DeviceReport             `json:"device_report"`
}

type EnergyEfficiency struct {
	Efficiency                      float64 `json:"efficiency"`
	TotalDeviceEfficiency           float64 `json:"total_device_efficiency"`
	PowerDistributionLoopEfficiency float64 `json:"power_distribution_loop_efficiency"`
	StationBatteryChargeAmount      float64 `json:"station_battery_charge_amount"`
	SCTChargingPileChargeAmount     float64 `json:"sct_charging_pile_charge_amount"`
}

type Capacity struct {
	CapacityFactor float64 `json:"capacity_factor"`
	RatedCapacity  float64 `json:"rated_capacity"`
	LoadFactor     float64 `json:"load_factor"`
	MaxCapacity    float64 `json:"max_capacity"`
	AvgCapacity    float64 `json:"avg_capacity"`
}

type Module struct {
	ModuleRatio              float64 `json:"module_ratio"`
	ModuleAmount             int     `json:"module_amount"`
	ModuleRealRunTime        float64 `json:"module_real_run_time"`
	ModuleTheoreticalRunTime float64 `json:"module_theoretical_run_time"`
	ModulePower              float64 `json:"module_power"`
}

type Cost struct {
	TotalCost                        float64 `json:"total_cost"`
	StationInnerBatteryChargeCost    float64 `json:"station_inner_battery_charge_cost"`
	StationOutChargingPileChargeCost float64 `json:"station_out_charging_pile_charge_cost"`
	PowerConsumptionLoopCost         float64 `json:"power_consumption_loop_cost"`
}

type Income struct {
	TotalIncome            float64 `json:"total_income"`
	PowerSwapServiceIncome float64 `json:"power_swap_service_income"`
	ChargeServiceIncome    float64 `json:"charge_service_income"`
	GridIncome             float64 `json:"grid_income"`
}

type PowerSwap struct {
	ActualServiceCount   int                 `json:"actual_service_count"`
	EstimateServiceCount int                 `json:"estimate_service_count"`
	WaitTime             map[string]WaitTime `json:"wait_time"`
}

type Charge struct {
	ActualServiceCount   int                 `json:"actual_service_count"`
	EstimateServiceCount int                 `json:"estimate_service_count"`
	WaitTime             map[string]WaitTime `json:"wait_time"`
}

type WaitTime struct {
	TotalWaitTime   float64 `json:"total_wait_time"`
	WaitBatteryTime float64 `json:"wait_battery_time"`
	WaitUserTime    float64 `json:"wait_user_time"`
}

type PsosUserReport struct {
	DownloadUrl string                `json:"download_url"`
	PowerSwap   PowerSwap             `json:"power_swap"`
	Charge      Charge                `json:"charge"`
	GraphData   []UserExperienceGraph `json:"graph_data"`
}

type UserExperienceGraph struct {
	Hour         int     `json:"hour"`
	ServiceNum   int     `json:"service_num"`
	AvgQueueTime float64 `json:"avg_queue_time"`
}

type SwapStationBatteryReport struct {
	DownloadUrl string         `json:"download_url"`
	Cost        Cost           `json:"cost"`
	Income      Income         `json:"income"`
	GraphData   []BatteryGraph `json:"graph_data"`
}

type BatteryGraph struct {
	Hour       int     `json:"hour"`
	ChargeNum  float64 `json:"charge_num"`
	ChargeCost float64 `json:"charge_cost"`
}

type DeviceReport struct {
	DownloadUrl      string           `json:"download_url"`
	EnergyEfficiency EnergyEfficiency `json:"energy_efficiency"`
	Capacity         Capacity         `json:"capacity"`
	Module           Module           `json:"module"`
	GraphData        []DeviceGraph    `json:"graph_data"`
}

type DeviceGraph struct {
	Hour           int     `json:"hour"`
	CapacityFactor float64 `json:"capacity_factor"`
	LoadFactor     float64 `json:"load_factor"`
	ModuleRate     float64 `json:"module_rate"`
}

type PsosSimulationConfig struct {
	SimulationStartTimestamp     int64                 `json:"simulation_start_timestamp"`
	SimulationEndTimestamp       int64                 `json:"simulation_end_timestamp"`
	PowerDistributionCapacity    int                   `json:"power_distribution_capacity"`
	CircuitDistributionCapacity1 int                   `json:"circuit_distribution_capacity_1"`
	CircuitDistributionCapacity2 int                   `json:"circuit_distribution_capacity_2"`
	CmsSwitchStatus              int32                 `json:"cms_switch_status"`
	BatteryConfig                PsosBatteryConfig     `json:"battery_config"`
	ServiceConfig                PsosServiceConfig     `json:"service_config"`
	ElectricityConfig            PsosElectricityConfig `json:"electricity_config"`
}

type PsosElectricityConfig struct {
	ElectricityPriceModel        string                        `json:"electricity_price_model"`
	OnePrice                     float64                       `json:"one_price"`
	ElectricityDetailUserConfigs []ElectricityDetailUserConfig `json:"electricity_detail_user_configs"`
}

type ElectricityDetailUserConfig struct {
	Start string  `json:"start"`
	End   string  `json:"end"`
	Price float64 `json:"price"`
}

type PsosBatteryConfig struct {
	BatteryInfos []PsosBatteryInfo `json:"battery_infos"`
}

type PsosBatteryInfo struct {
	SlotId         int      `json:"slot_id"`
	BatterySoc     *float64 `json:"battery_soc"`
	BatteryRateKwh *int     `json:"battery_rate_kwh"`
	ChargeStopSoc  *float64 `json:"charge_stop_soc"`
	BatteryId      *string  `json:"battery_id"`
}

type PsosServiceConfig struct {
	Kwh50            int               `json:"kwh_50"`
	Kwh75            int               `json:"kwh_75"`
	Kwh100           int               `json:"kwh_100"`
	Kwh150           int               `json:"kwh_150"`
	PsosServiceInfos []PsosServiceInfo `json:"psos_service_infos"`
}

type PsosServiceInfo struct {
	ServiceId            *string `json:"service_id"`
	VehicleId            *string `json:"vehicle_id"`
	BatteryId            *string `json:"battery_id"`
	UserArriveTimestamp  int64   `json:"user_arrive_timestamp"`
	BatteryRateKwh       int     `json:"battery_rate_kwh"`
	TargetBatteryRateKwh *int    `json:"target_battery_rate_kwh"`
	BatterySoc           int     `json:"battery_soc"`
}

type ListPsosSimulationResponse struct {
	um.Base
	Total int64              `json:"total"`
	Data  []PsosSimulationVO `json:"data"`
}

type GetPsosSimulationByIdResponse struct {
	um.Base
	Data PsosSimulationVO `json:"data"`
}

type GetPsosTaskGraphDataResponse struct {
	um.Base
	Data PsosTaskGraphData `json:"data"`
}

type PsosTaskGraphData struct {
	FmsCompressInfo             PsosFmsCompressInfo      `json:"fms_compress_info"`
	BaseQueueTime               float64                  `json:"base_queue_time"`
	BaseChargeCost              float64                  `json:"base_charge_cost"`
	BaseCapacityUtilization     float64                  `json:"base_capacity_utilization"`
	AvgQueueTimeGraph           []PsosTaskGraphFloatData `json:"avg_queue_time_graph"`
	ChargeCostGraph             []PsosTaskGraphFloatData `json:"charge_cost_graph"`
	AvgCapacityUtilizationGraph []PsosTaskGraphFloatData `json:"avg_capacity_utilization_graph"`
}

type PsosTaskGraphFloatData struct {
	XValue string  `json:"x_value"`
	YValue float64 `json:"y_value"`
}
