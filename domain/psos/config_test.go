package psos

import (
	"context"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"math/rand"
	"net/http"
	"os"
	"strconv"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	gocache "github.com/patrickmn/go-cache"

	um "git.nevint.com/golang-libs/common-utils/model"
	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	ulog "git.nevint.com/golang-libs/common-utils/logger"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"
	"git.nevint.com/welkin2/welkin-backend/util"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	log "git.nevint.com/welkin2/welkin-backend/logger"
)

func init() {
	os.Setenv("APOLLO_META", "https://apollo-config-cn-stg.nioint.com")
	os.Setenv("ENV", "stg")
	cfg = &ucfg.Config{}
	ucfg.GetConfigApollo("config.json", true, cfg, ucfg.NeedValidate(true), ucfg.ServiceName("ppd-welkin-backend"), ucfg.Cluster(ucfg.GetWelkinApolloCluster()))
	defer ulog.Final()
	cfg.Log.DirPath = "/tmp/logs"
	log.Init(cfg.Log, true)
	config.Cfg = cfg
	logger := log.Logger.Named("psos_config")
	w = client.NewWatcherByParam(cfg, logger)
	p = NewHandler(w)
	cache.CacheRealtimeDataID(w.Mongodb().Client)
	cache.PowerSwapCache = &cache.DeviceInfoCache{
		Cache:         gocache.New(gocache.NoExpiration, gocache.NoExpiration),
		ResourceCache: gocache.New(gocache.NoExpiration, gocache.NoExpiration),
	}
	cache.PowerSwapCache.RefreshDeviceInfoCache(w.Mongodb().Client)
}

func TestGeneratePsosConfig(t *testing.T) {
	file, err := os.Open("/Users/<USER>/Downloads/psos.csv")
	if err != nil {
		fmt.Println("Error opening file:", err)
		return
	}
	defer file.Close()
	reader := csv.NewReader(file)

	// 读取标题行
	_, err = reader.Read()
	if err != nil {
		fmt.Println("Error reading headers:", err)
		return
	}
	var record []string
	//do := false
	// 逐行读取CSV文件
	for {
		record, err = reader.Read()
		if err != nil {
			break // 如果读取错误，跳出循环
		}

		generateOneConfig := func(record []string, fullSlot bool) {
			resourceId := record[1]
			serviceCount, _ := strconv.ParseFloat(record[2], 64)
			deviceInfo, ok := cache.PowerSwapCache.GetDeviceByResourceId(resourceId)
			//if deviceInfo.DeviceId == "PS-NIO-a423f3aa-d955e51f" {
			//	do = true
			//}
			//if !do {
			//	return
			//}

			serviceTotal := int(math.Round(serviceCount))
			if !ok {
				fmt.Println("cannot find device:", resourceId)
				return
			}
			service75 := serviceTotal * 7 / 10
			service100 := serviceTotal - service75
			fmt.Println(deviceInfo.Project, deviceInfo.DeviceId, serviceTotal, service75, service100)
			startTs, endTs := 1725897600000, 1725984000000

			// 获取单站真实配置
			url := fmt.Sprintf("https://api-welkin-backend.nioint.com/algorithm/v1/psos/config/real-device/%s/%s?start_ts=%d&end_ts=%d", deviceInfo.Project, deviceInfo.DeviceId, startTs, endTs)
			fmt.Println(url)
			ct := ucmd.NewHttpClient(ucmd.HttpClient{
				URL:    url,
				Method: "GET",
			})
			body, statusCode, err := ct.Do()
			if err != nil {
				t.Fatal(err)
			}
			defer body.Close()
			data, err := io.ReadAll(body)
			if err != nil {
				t.Fatalf("fail to read body GetConfigResponse, err: %v, url: %s\n", err, ct.URL)
				return
			}
			if statusCode != http.StatusOK {
				err = fmt.Errorf("fail to request GetConfigResponse, status code: %d, url: %s", statusCode, ct.URL)
				t.Fatal(err)
				return
			}
			var resp GetConfigResponse
			if err = json.Unmarshal(data, &resp); err != nil {
				t.Fatalf("fail to unmarshal GetConfigResponse, err: %v, url: %s\n", err, ct.URL)
				return
			}
			if resp.ErrCode != 0 {
				err = fmt.Errorf("fail to get GetConfigResponse, %s", ucmd.ToJsonStrIgnoreErr(resp))
				t.Fatal(err)
				return
			}
			fmt.Println(ucmd.ToJsonStrIgnoreErr(resp.Data))

			// 电池快速配置
			batteryInfo := resp.Data.BatteryInfo
			if fullSlot {
				slotNum := BatterySlotBatteryNum[deviceInfo.Project]
				slot75 := slotNum * 7 / 10
				slot100 := slotNum - slot75
				url = fmt.Sprintf("https://api-welkin-backend.nioint.com/algorithm/v1/psos/battery-config/generate?battery_config=%s&project=%s&device_id=%s&start_ts=%d", fmt.Sprintf("0,%d,%d,0", slot75, slot100), deviceInfo.Project, deviceInfo.DeviceId, startTs)
				fmt.Println(url)
				ct = ucmd.NewHttpClient(ucmd.HttpClient{
					URL:    url,
					Method: "GET",
				})
				body, statusCode, err = ct.Do()
				if err != nil {
					t.Fatal(err)
				}
				defer body.Close()
				data, err = io.ReadAll(body)
				if err != nil {
					t.Fatalf("fail to read body PsosBatteryInfo, err: %v, url: %s\n", err, ct.URL)
					return
				}
				if statusCode != http.StatusOK {
					err = fmt.Errorf("fail to request PsosBatteryInfo, status code: %d, url: %s", statusCode, ct.URL)
					t.Fatal(err)
					return
				}
				var respBattery struct {
					um.Base
					Data []mmgo.PsosBatteryInfo `json:"data"`
				}
				if err = json.Unmarshal(data, &respBattery); err != nil {
					t.Fatalf("fail to unmarshal PsosBatteryInfo, err: %v, url: %s\n", err, ct.URL)
					return
				}
				if respBattery.ErrCode != 0 {
					err = fmt.Errorf("fail to get PsosBatteryInfo, %s", ucmd.ToJsonStrIgnoreErr(resp))
					t.Fatal(err)
					return
				}
				fmt.Println(ucmd.ToJsonStrIgnoreErr(respBattery.Data))
				batteryInfo = respBattery.Data
			}

			// 订单快速配置
			batteryConfig := make([]int, 4)
			for _, slot := range batteryInfo {
				if slot.BatteryRatedKwh == nil {
					continue
				}
				switch *slot.BatteryRatedKwh {
				case 50:
					batteryConfig[0]++
				case 75:
					batteryConfig[1]++
				case 100:
					batteryConfig[2]++
				case 150:
					batteryConfig[3]++
				}
			}

			url = fmt.Sprintf("https://api-welkin-backend.nioint.com/algorithm/v1/psos/service-list/generate?battery_config=%s&project=%s&device_id=%s&start_ts=%d&end_ts=%d&service_count=%s&skip_level_swap_switch=%d&swapping_failure_switch=%d", fmt.Sprintf("%d,%d,%d,%d", batteryConfig[0], batteryConfig[1], batteryConfig[2], batteryConfig[3]), deviceInfo.Project, deviceInfo.DeviceId, startTs, endTs, fmt.Sprintf("0,%d,%d,0", service75, service100), resp.Data.SkipLevelSwapSwitch, resp.Data.SwappingFailureSwitch)
			fmt.Println(url)
			ct = ucmd.NewHttpClient(ucmd.HttpClient{
				URL:    url,
				Method: "GET",
			})
			body, statusCode, err = ct.Do()
			if err != nil {
				t.Fatal(err)
			}
			defer body.Close()
			data, err = io.ReadAll(body)
			if err != nil {
				t.Fatalf("fail to read body SwappingUser, err: %v, url: %s\n", err, ct.URL)
				return
			}
			if statusCode != http.StatusOK {
				err = fmt.Errorf("fail to request SwappingUser, status code: %d, url: %s", statusCode, ct.URL)
				t.Fatal(err)
				return
			}
			var respService struct {
				um.Base
				Total int64               `json:"total"`
				Data  []mmgo.SwappingUser `json:"data"`
			}
			if err = json.Unmarshal(data, &respService); err != nil {
				t.Fatalf("fail to unmarshal SwappingUser, err: %v, url: %s\n", err, ct.URL)
				return
			}
			if respService.ErrCode != 0 {
				err = fmt.Errorf("fail to get SwappingUser, %s", ucmd.ToJsonStrIgnoreErr(resp))
				t.Fatal(err)
				return
			}
			fmt.Println(ucmd.ToJsonStrIgnoreErr(respService.Data))

			// 新建单站配置
			requestBody := map[string]interface{}{
				"config_name":             "24国庆_满电池",
				"remark":                  "",
				"project":                 deviceInfo.Project,
				"start_ts":                startTs,
				"end_ts":                  endTs,
				"station_capacity":        resp.Data.StationCapacity,
				"circuit_1_capacity":      resp.Data.Circuit1Capacity,
				"circuit_2_capacity":      resp.Data.Circuit2Capacity,
				"electricity_price_model": resp.Data.ElectricityPriceModel,
				"electricity_detail_list": resp.Data.ElectricityDetailList,
				"cms_switch":              resp.Data.CmsSwitch,
				"operation_start_hour":    resp.Data.OperationStartHour,
				"operation_end_hour":      resp.Data.OperationEndHour,
				"notfully_swap_switch":    resp.Data.NotfullySwapSwitch,
				"silent_mode_switch":      resp.Data.SilentModeSwitch,
				"battery_exchange_switch": resp.Data.BatteryExchangeSwitch,
				"battery_rest_switch":     resp.Data.BatteryRestSwitch,
				"is_real_device":          true,
				"description":             deviceInfo.Description,
				"device_id":               deviceInfo.DeviceId,
				"battery_info":            batteryInfo,
				"service_list":            respService.Data,
				"skip_level_swap_switch":  resp.Data.SkipLevelSwapSwitch,
				"swapping_failure_switch": resp.Data.SwappingFailureSwitch,
			}
			ct = ucmd.NewHttpClient(ucmd.HttpClient{
				URL:    "https://api-welkin-backend.nioint.com/algorithm/v1/psos/config/single",
				Method: "POST",
				Header: map[string]string{
					"Content-Type": "application/json",
					"X-User-ID":    "caca.wang",
				},
				RequestBody: requestBody,
			})
			body, statusCode, err = ct.Do()
			if err != nil {
				t.Fatal(err)
				return
			}
			defer body.Close()
			data, _ = io.ReadAll(body)
			if statusCode != http.StatusOK {
				err = fmt.Errorf("fail to request GetConfigResponse, status code: %d, url: %s", statusCode, ct.URL)
				t.Fatal(err)
				return
			}
			var cResp um.Base
			if err = json.Unmarshal(data, &cResp); err != nil {
				t.Fatal(err)
				return
			}
			if cResp.ErrCode != 0 {
				err = fmt.Errorf("fail to get GetConfigResponse, %s", ucmd.ToJsonStrIgnoreErr(resp))
				t.Fatal(err)
				return
			}
		}

		generateOneConfig(record, true)
	}
}

func TestConfigDO_GetDeviceConfig(t *testing.T) {
	request := PsosBatchConfigRequest{
		CommonPsosConfig: CommonPsosConfig{
			Project:               umw.PUS3,
			StartTs:               1718365370835,
			EndTs:                 1718426880860,
			StationCapacity:       nil,
			Circuit1Capacity:      nil,
			Circuit2Capacity:      nil,
			CmsSwitch:             nil,
			ElectricityPriceModel: nil,
			ElectricityDetailList: nil,
		},
		Devices: []string{"full"},
	}
	configDO := ConvertBatchConfigVO2DO(context.Background(), request, "william.shen2")
	res, err := configDO.GetDeviceConfig(&gin.Context{})
	if err != nil {
		t.Fatal(err)
	}
	for _, item := range res {
		fmt.Println("origin:", ucmd.ToJsonStrIgnoreErr(item))
		fmt.Println("after prepare:", ucmd.ToJsonStrIgnoreErr(configDO.PrepareAutoParams(&gin.Context{}, item)))
	}
}

func TestConfigDO_GenerateBatchConfig(t *testing.T) {
	stationCapacity := 400
	circuit1Capacity := 100
	circuit2Capacity := 230
	cmsSwitch := 0
	priceModel := "different_price"
	request := PsosBatchConfigRequest{
		CommonPsosConfig: CommonPsosConfig{
			ConfigName:            "test_sck",
			Remark:                "xxx",
			Project:               umw.PUS3,
			StartTs:               1718365370835,
			EndTs:                 1718426880860,
			StationCapacity:       &stationCapacity,
			Circuit1Capacity:      &circuit1Capacity,
			Circuit2Capacity:      &circuit2Capacity,
			CmsSwitch:             &cmsSwitch,
			ElectricityPriceModel: &priceModel,
			ElectricityDetailList: nil,
			//ElectricityDetails:    []float64{0.999, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 0.6835, 0.6835, 0.6835, 0.6835},
		},
		Devices: []string{"PS-NIO-3285ff15-7f564f27"},
	}
	configDO := ConvertBatchConfigVO2DO(context.Background(), request, "william.shen2")
	err := configDO.GenerateBatchConfig(&gin.Context{})
	if err != nil {
		t.Fatal(err)
	}
}

func TestConfigDO_GenerateSingleConfig(t *testing.T) {
	stationCapacity := 400
	circuit1Capacity := 100
	circuit2Capacity := 230
	cmsSwitch := 0
	priceModel := "different_price"
	configId := "cfg_cqb18nbkb78vr4udkrlg"
	batteryInfo := []mmgo.PsosBatteryInfo{
		//{
		//	SlotId:             11,
		//	BatteryId:          "xxx",
		//	BatteryRatedKwh:    75,
		//	BatterySoc:         32,
		//	PackMaxTemperature: 10,
		//},
	}
	serviceList := []mmgo.SwappingUser{
		//{
		//	ServiceId:             "service111",
		//	VehicleId:             "xx",
		//	UserArrivalTime:       1718365371,
		//	SwappingTime:          240,
		//	TargetBatteryRatedKwh: 100,
		//	BatteryId:             "battery100",
		//	BatterySoc:            12,
		//	BatteryRatedKwh:       50,
		//},
	}
	request := PsosSingleConfigRequest{
		CommonPsosConfig: CommonPsosConfig{
			ConfigName:            "test_sck2",
			Remark:                "xxx",
			Project:               umw.PUS3,
			StartTs:               1718365370835,
			EndTs:                 1718426880860,
			StationCapacity:       &stationCapacity,
			Circuit1Capacity:      &circuit1Capacity,
			Circuit2Capacity:      &circuit2Capacity,
			CmsSwitch:             &cmsSwitch,
			ElectricityPriceModel: &priceModel,
			ElectricityDetailList: nil,
			//ElectricityDetails:    []float64{0.999, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 0.286, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 0.6835, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 1.1753, 0.6835, 0.6835, 0.6835, 0.6835},
		},
		DeviceId:    "PS-NIO-3285ff15-7f564f27",
		BatteryInfo: batteryInfo,
		ServiceList: serviceList,
		ConfigId:    &configId,
	}
	configDO := ConvertSingleConfigVO2DO(context.Background(), request, "william.shen2")
	err := configDO.GenerateSingleConfig(&gin.Context{})
	if err != nil {
		t.Fatal(err)
	}
}

func TestConfigDO_RandomBatteryInfo(t *testing.T) {
	configDO := ConfigDO{
		StartTs: 1720897200000,
		Project: umw.PUS3,
		Rng:     &util.Rand{Rand: rand.New(rand.NewSource(time.Now().UnixNano()))},
	}
	batteryConfig := []float64{2, 3, 1, 4, 2, 1}
	deviceId := "PS-NIO-e04d6331-61f5ed64"
	res, err := configDO.RandomBatteryInfo(&gin.Context{}, deviceId, batteryConfig)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println("res:", ucmd.ToJsonStrIgnoreErr(res))
}

func TestConfigDO_GenerateRandomService(t *testing.T) {
	configDO := ConfigDO{
		StartTs:               1721809070000,
		EndTs:                 1721895470000,
		Project:               umw.PUS3,
		Devices:               []string{"PS-NIO-3285ff15-7f564f27"},
		SkipLevelSwapSwitch:   1,
		SwappingFailureSwitch: 1,
		Rng:                   &util.Rand{Rand: rand.New(rand.NewSource(time.Now().UnixNano()))},
	}
	deviceId := "PS-NIO-e04d6331-61f5ed64"
	batteryConfig := []float64{0, 3, 1, 4}
	serviceCount := []int{0, 10, 8, 200}
	res, err := configDO.RandomService(&gin.Context{}, deviceId, batteryConfig, serviceCount)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println("res:", ucmd.ToJsonStrIgnoreErr(res))
}

func TestCalculateElectricityDetails(t *testing.T) {
	input := []mmgo.ElectricityDetailUserConfig{
		{
			Start: "00:00",
			End:   "02:30",
			Price: 0.5,
		},
		{
			Start: "02:30",
			End:   "04:30",
			Price: 0.3,
		},
		{
			Start: "04:30",
			End:   "09:00",
			Price: 0.9,
		},
		{
			Start: "09:00",
			End:   "22:30",
			Price: 0.1,
		},
		{
			Start: "22:30",
			End:   "24:00",
			Price: 0.999,
		},
	}
	res, err := CalculateElectricityDetails(input)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(len(res), "res:", ucmd.ToJsonStrIgnoreErr(res))
}

func TestCalculateElectricityList(t *testing.T) {
	input := []float64{0.5, 0.5, 0.5, 0.5, 0.5, 0.3, 0.3, 0.3, 0.3, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.999, 0.999, 0.888}
	res, err := CalculateElectricityList(input)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(len(res), "res:", ucmd.ToJsonStrIgnoreErr(res))
}
