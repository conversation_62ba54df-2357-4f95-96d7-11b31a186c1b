package psos

import (
	"context"
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/rs/xid"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/domain/common"
	domain_device "git.nevint.com/welkin2/welkin-backend/domain/device"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"
	"git.nevint.com/welkin2/welkin-backend/util"
)

type ConfigDO struct {
	ConfigId              *string                            `json:"config_id"`
	ConfigName            string                             `json:"config_name"`
	IsRealDevice          bool                               `json:"is_real_device"`
	Remark                string                             `json:"remark"`
	UserId                string                             `json:"user_id"`
	Project               string                             `json:"project"`
	StartTs               int64                              `json:"start_ts"`
	EndTs                 int64                              `json:"end_ts"`
	StationCapacity       *int                               `json:"station_capacity"`
	Circuit1Capacity      *int                               `json:"circuit_1_capacity"`
	Circuit2Capacity      *int                               `json:"circuit_2_capacity"`
	CmsSwitch             *int                               `json:"cms_switch"`
	OperationStartHour    *int                               `json:"operation_start_hour"`
	OperationEndHour      *int                               `json:"operation_end_hour"`
	NotfullySwapSwitch    *NotfullySwapSwitch                `json:"notfully_swap_switch"`
	SilentModeSwitch      *int                               `json:"silent_mode_switch"`
	BatteryExchangeSwitch *int                               `json:"battery_exchange_switch"`
	BatteryRestSwitch     *BatteryRestSwitch                 `json:"battery_rest_switch"`
	ElectricityPriceModel *string                            `json:"electricity_price_model"`
	ElectricityDetailList []mmgo.ElectricityDetailUserConfig `json:"electricity_details"`
	Devices               []string                           `json:"devices"`
	BatteryInfo           []mmgo.PsosBatteryInfo             `json:"battery_info,omitempty"` // 单配方
	ServiceList           []mmgo.SwappingUser                `json:"service_list,omitempty"` // 单配方
	SkipLevelSwapSwitch   int                                `json:"skip_level_swap_switch"`
	SwappingFailureSwitch int                                `json:"swapping_failure_switch"`
	Rng                   *util.Rand
}

// GetDeviceConfig 获取设备真实配置
func (c *ConfigDO) GetDeviceConfig(ctx context.Context) (res map[string]mmgo.MongoPsosConfig, err error) {
	res = make(map[string]mmgo.MongoPsosConfig)
	if len(c.Devices) == 0 {
		err = fmt.Errorf("get psos config, empty devices")
		log.CtxLog(ctx).Error(err)
		return
	}
	var (
		priceInfo           map[string]PriceInfo
		devicePrice         = make(map[string]domain_device.DeviceElectricityPriceHalfHour)
		successMap, failMap map[string]int
		swapUserList        map[string][]mmgo.SwappingUser
		deviceParams        map[string]map[string]interface{}
		psosFeature         map[string]mmgo.MongoPsosFeature
		electricityConsumption map[string]float64 // TODO: 暂时注释，未使用
		// epsInput               map[string]mmgo.EPSInput // TODO: 暂时注释，未使用
	)
	psosHandler := NewHandler(client.GetWatcher())
	g := ucmd.NewErrGroup(ctx)
	g.GoRecover(func() error {
		var gErr error
		// 只用priceInfo中的OperationTime
		priceInfo, gErr = psosHandler.GetPriceInfo(ctx, c.Project, c.Devices)
		if gErr != nil {
			log.CtxLog(ctx).Errorf("get psos config, fail to GetPriceInfo, err: %v", gErr)
			return gErr
		}
		return nil
	})
	g.GoRecover(func() error {
		for _, deviceId := range c.Devices {
			deviceDO := domain_device.Device{}
			electricityPrice, gErr := deviceDO.GetDeviceElectricityPriceHalfHour(ctx, domain_device.DeviceElectricityPriceCond{DeviceId: deviceId, Day: time.UnixMilli(c.StartTs).Format("2006-01-02")})
			if gErr != nil {
				log.CtxLog(ctx).Warnf("get psos config, fail to GetDeviceElectricityPrice: %v, conf: %s", err, ucmd.ToJsonStrIgnoreErr(c))
				return gErr
			}
			devicePrice[deviceId] = electricityPrice
		}
		return nil
	})
	g.GoRecover(func() error {
		var gErr error
		successMap, failMap, gErr = psosHandler.GetSwapDuration(ctx, c.Project, c.StartTs, c.Devices)
		if gErr != nil {
			log.CtxLog(ctx).Errorf("get psos config, fail to GetSwapDuration, err: %v", gErr)
			return gErr
		}
		return nil
	})
	g.GoRecover(func() error {
		var gErr error
		swapUserList, gErr = psosHandler.GetSwapUserList(ctx, c.Project, c.StartTs, c.EndTs, c.Devices)
		if gErr != nil {
			log.CtxLog(ctx).Errorf("get psos config, fail to GetSwapUserList, err: %v", gErr)
			return gErr
		}
		return nil
	})
	g.GoRecover(func() error {
		var gErr error
		deviceParams, gErr = psosHandler.GetRedrabbitDeviceParams(ctx, c.Project, c.Devices)
		if gErr != nil {
			log.CtxLog(ctx).Errorf("get psos config, fail to GetRedrabbitDeviceParams, err: %v", gErr)
			return gErr
		}
		return nil
	})
	g.GoRecover(func() error {
		var gErr error
		psosFeature, gErr = c.GetPsosFeature(ctx)
		if gErr != nil {
			log.CtxLog(ctx).Errorf("get psos config, fail to GetPsosFeature, err: %v", gErr)
			return gErr
		}
		return nil
	})
	// 每小时换电量
	g.GoRecover(func() error {
		hours := getHoursInRange(ctx, c.StartTs, c.EndTs)
		if len(hours) == 0 {
			return nil
		}
		sameDay := true
		day := hours[0]["day"]
		hourList := make([]string, 0)
		for _, item := range hours {
			hourList = append(hourList, item["hour"])
			if item["day"] != day {
				sameDay = false
				break
			}
		}
		resourceIdList := make([]string, 0)
		resourceIdMap := make(map[string]string)
		for _, deviceId := range c.Devices {
			deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(deviceId)
			if found {
				resourceId := deviceInfo.ResourceId
				resourceIdList = append(resourceIdList, resourceId)
				resourceIdMap[resourceId] = deviceId
			}
		}
		matchStage := bson.D{}
		if sameDay {
			// 优化查询
			matchStage = bson.D{
				{"resource_id", bson.M{"$in": resourceIdList}},
				{"day", day},
				{"hour", bson.M{"$in": hourList}},
			}
		} else {
			matchStage = bson.D{
				{"resource_id", bson.M{"$in": resourceIdList}},
				{"$or", hours},
			}
		}
		pipeline := mongo.Pipeline{
			bson.D{{"$match", matchStage}},
			bson.D{{"$group", bson.M{
				"_id": "$resource_id",
			}}},
		}
		client.GetWatcher().Mongodb().NewMongoEntry().Aggregate(umw.Algorithm, "device_hourly_meter", pipeline, &)
		// TODO: 实现聚合查询
		return nil
	})
	// eps输入参数
	g.GoRecover(func() error {
		// TODO: 实现eps输入参数获取
		return nil
	})
	if err = g.Wait(); err != nil {
		return
	}

	platform := PlatformPS2
	if c.Project == umw.PUS3 {
		platform = PlatformPUS3
	} else if c.Project == umw.PUS4 {
		platform = PlatformPUS4
	}
	psosSimulationInfo := mmgo.PsosSimulationInfo{
		SimulationStartTime: c.StartTs,
		SimulationPeriod:    c.EndTs - c.StartTs,
	}

	for _, deviceId := range c.Devices {
		batteryRealtime, gErr := psosHandler.GetBatteryRealtime(ctx, c.Project, deviceId, c.StartTs, c.StartTs+5*time.Minute.Milliseconds())
		if gErr != nil {
			log.CtxLog(ctx).Errorf("get psos config, fail to GetBatteryRealtime, err: %v", gErr)
		}
		batteryInfo := make([]mmgo.PsosBatteryInfo, 0)
		hasBatterySlot := make(map[int]bool)
		for _, bi := range batteryRealtime {
			// 去除无电池的电池仓
			if bi.BatteryRatedKwh == nil || bi.RealBatteryRatedKwh == nil {
				continue
			}
			hasBatterySlot[bi.SlotId] = true
			// 补全缺失参数
			stopSoc := float64(util.ParseInt(deviceParams[deviceId][fmt.Sprintf("%dkWh_charging_stop_soc", *bi.BatteryRatedKwh)]))
			if stopSoc != -1 {
				bi.ChargingStopSoc = &stopSoc
			} else {
				bi.ChargingStopSoc = &BatteryChargingStopSoc
			}
			if bi.PackMaxTemperature == nil {
				bi.PackMaxTemperature = &BatteryPackMaxTemperature
			}
			if bi.BatterySoc == nil {
				bi.BatterySoc = &BatterySoc
			}
			if bi.BatteryId == nil {
				batteryId := xid.New().String()
				bi.BatteryId = &batteryId
			}
			batteryInfo = append(batteryInfo, bi)
		}
		for i := range BatterySlotNum[c.Project] {
			slotId := i + 1
			if !hasBatterySlot[slotId] {
				batteryInfo = append(batteryInfo, mmgo.PsosBatteryInfo{
					SlotId: slotId,
				})
			}
		}
		sort.Slice(batteryInfo, func(i, j int) bool {
			return batteryInfo[i].SlotId < batteryInfo[j].SlotId
		})
		var ok bool
		operationStrategyInfo := mmgo.OperationStrategyInfo{}
		operationStrategyInfo.BatteryExchangeSwitch.SwitchValue = int32(util.ParseInt(deviceParams[deviceId][ParamBatteryExchangeSwitch]))
		operationStrategyInfo.CmsStrategySwitch.SwitchValue = int32(util.ParseInt(deviceParams[deviceId][ParamCmsStrategySwitch]))
		operationStrategyInfo.CmsStrategySwitch.SchedulingInterval = util.ParseInt(deviceParams[deviceId][ParamCmsFrequency])
		operationStrategyInfo.CmsStrategySwitch.StrategyValue = make([]int, 0)   // 默认值
		operationStrategyInfo.SmartChargingSwitch.StrategyValue = make([]int, 0) // 默认值
		operationStrategyInfo.ExchangeDuration = 120                             // 默认值
		operationStrategyInfo.BatteryInfo = batteryInfo
		operationStrategyInfo.PowerDistributionCapacity = util.ParseInt(deviceParams[deviceId][ParamStationCapacity])
		operationStrategyInfo.CircuitDistributionCapacity, ok = deviceParams[deviceId][ParamCircuitCapacity].([]int)
		if !ok {
			operationStrategyInfo.CircuitDistributionCapacity = CircuitDistributionCapacity[c.Project]
		}
		operationStrategyInfo.BranchCircuitCurrentLimit, ok = deviceParams[deviceId][ParamBranchCurrentLimit].([]int)
		if !ok {
			operationStrategyInfo.BranchCircuitCurrentLimit = BranchCircuitCurrentLimit[c.Project]
		}
		operationStrategyInfo.OperationTime = priceInfo[deviceId].OperationTime
		operationStrategyInfo.NotfullySwapSwitch.SwitchValue = 1 // 默认值
		operationStrategyInfo.SilentModeSwitch.SwitchValue = int32(util.ParseInt(deviceParams[deviceId][ParamSilentModeSwitch]))
		operationStrategyInfo.ChargingStopSoc50kWh = util.ParseInt(deviceParams[deviceId][Param50kWhChargingStopSoc])
		operationStrategyInfo.ChargingStopSoc70kWh = util.ParseInt(deviceParams[deviceId][Param70kWhChargingStopSoc])
		operationStrategyInfo.ChargingStopSoc75kWh = util.ParseInt(deviceParams[deviceId][Param75kWhChargingStopSoc])
		operationStrategyInfo.ChargingStopSoc100kWh = util.ParseInt(deviceParams[deviceId][Param100kWhChargingStopSoc])
		operationStrategyInfo.ChargingStopSoc150kWh = util.ParseInt(deviceParams[deviceId][Param150kWhChargingStopSoc])
		operationStrategyInfo.ChargingStopSoc60kWh = util.ParseInt(deviceParams[deviceId][Param60kWhChargingStopSoc])
		operationStrategyInfo.ChargingStopSoc85kWh = util.ParseInt(deviceParams[deviceId][Param85kWhChargingStopSoc])
		operationStrategyInfo.BatterySocLowerLimit = BatterySocLowerLimit[c.Project]
		operationStrategyInfo.BatterySocUpperLimit = BatterySocUpperLimit[c.Project]
		//operationStrategyInfo.BatteryTypeConf = BatteryTypeConfig[c.Project]
		operationStrategyInfo.BatteryTypeConf = GetBatteryConfig(batteryRealtime, c.Project)
		operationStrategyInfo.BatteryKWhGroup = AlgorithmBatteryList[c.Project]
		operationStrategyInfo.FullyBatteryRatio = FullyBatteryRatio[c.Project]
		operationStrategyInfo.BatteryRestSwitch = mmgo.BatteryRestSwitch{ // 默认值
			SwitchValue:              0,
			DefaultRestLabel:         1,
			DefaultRestDuration:      600,
			DefaultRestCurrent:       0,
			DefaultHangingDuration:   0,
			DefaultHangingStep:       10,
			DefaultHangingCurrentMax: 50,
		}
		chargeSwapSystem := mmgo.ChargeSwapSystem{
			CircuitNum:                        CircuitNum[c.Project],
			BatterySlotNum:                    BatterySlotNum[c.Project],
			ModuleSupplier:                    ModuleSupplier[c.Project],
			ModuleNum:                         ModuleNum[c.Project],
			PerSubmoduleNum:                   PerSubmoduleNum[c.Project],
			SubmodulePowerLimit:               SubmodulePowerLimit[c.Project],
			SwapBatterySlot:                   SwapBatterySlot[c.Project],
			StorableBatterySlot:               StorableBatterySlot[c.Project],
			CircuitLineAllocation:             CircuitLineAllocation[c.Project],
			CoupleStructure:                   CoupleStructure[c.Project],
			SwappingTimeSuccess:               successMap[deviceId],
			SwappingTimeFault:                 failMap[deviceId],
			NonChargingElectricityConsumption: psosFeature[deviceId].AvgNonChargingLoss,
		}
		psosDeviceInfo := mmgo.PsosDeviceInfo{
			Platform:              platform,
			DeviceId:              deviceId,
			OperationStrategyInfo: operationStrategyInfo,
			ChargeSwapSystem:      chargeSwapSystem,
		}

		// 删除运营时间外的订单
		validSwappingUserList := make([]mmgo.SwappingUser, 0)
		for _, item := range swapUserList[deviceId] {
			arrivalHour := time.UnixMilli(item.UserArrivalTime).Hour()
			if arrivalHour >= operationStrategyInfo.OperationTime[0] && arrivalHour < operationStrategyInfo.OperationTime[1] {
				validSwappingUserList = append(validSwappingUserList, item)
			}
		}
		psosServiceInfo := mmgo.PsosServiceInfo{
			SwappingUserNum:  len(validSwappingUserList),
			SwappingUserList: validSwappingUserList,
		}
		sort.Slice(psosServiceInfo.SwappingUserList, func(i, j int) bool {
			return psosServiceInfo.SwappingUserList[i].UserArrivalTime > psosServiceInfo.SwappingUserList[j].UserArrivalTime
		})
		psosScenarioInfo := mmgo.PsosScenarioInfo{}
		psosScenarioInfo.ElectricityPriceModel = ElectricityOnePrice
		lastPrice := devicePrice[deviceId].PriceDetail["00:00"].Price
		for _, hour := range util.GetHalfHourIntervals(ctx, "00:00", "24:00") {
			// psos需要的电价是半小时颗粒度的
			psosScenarioInfo.ElectricityDetails = append(
				psosScenarioInfo.ElectricityDetails,
				devicePrice[deviceId].PriceDetail[hour].Price,
			)
			if psosScenarioInfo.ElectricityPriceModel == ElectricityOnePrice && devicePrice[deviceId].PriceDetail[hour].Price != lastPrice {
				psosScenarioInfo.ElectricityPriceModel = ElectricityDifferentPrice
			}
		}

		res[deviceId] = mmgo.MongoPsosConfig{
			Project:           c.Project,
			SimulationInfo:    psosSimulationInfo,
			DeviceInfo:        psosDeviceInfo,
			ServiceInfo:       psosServiceInfo,
			ScenarioInfo:      psosScenarioInfo,
			BaseBatteryConfig: GetBatteryConfig(batteryRealtime, c.Project),
		}
	}
	return
}

func (c *ConfigDO) GetPsosFeature(ctx context.Context) (res map[string]mmgo.MongoPsosFeature, err error) {
	res = make(map[string]mmgo.MongoPsosFeature)
	resourceIdMap := make(map[string]string)
	resourceIdList := make([]string, 0)
	for _, deviceId := range c.Devices {
		deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(deviceId)
		if found {
			resourceId := deviceInfo.ResourceId
			resourceIdList = append(resourceIdList, resourceId)
			resourceIdMap[resourceId] = deviceId
		}
	}
	filter := bson.D{
		{"device_id", bson.M{"$in": resourceIdList}},
		{"day", time.UnixMilli(c.StartTs).Format("20060102")},
	}
	projection := bson.M{"device_id": 1, "avg_non_charging_loss": 1}
	opts := options.Find().SetProjection(projection)
	var features []mmgo.MongoPsosFeature
	_, err = client.GetWatcher().PLCMongodb().NewMongoEntry(filter).FindMany(umw.Algorithm, CollectionFeature, opts, &features)
	if err != nil {
		err = fmt.Errorf("fail to get psos feature, err: %v, filter: %s", err, ucmd.ToJsonStrIgnoreErr(filter))
		log.CtxLog(ctx).Error(err)
		return
	}
	// 获取最新的full数据
	filter = bson.D{
		{"device_id", "full"},
	}
	opts = options.Find().SetSort(bson.M{"day": -1}).SetLimit(1).SetProjection(projection)
	var latestFeatures []mmgo.MongoPsosFeature
	_, err = client.GetWatcher().PLCMongodb().NewMongoEntry(filter).FindMany(umw.Algorithm, CollectionFeature, opts, &latestFeatures)
	if err != nil || len(latestFeatures) == 0 {
		err = fmt.Errorf("fail to get latest psos feature, err: %v, filter: %s", err, ucmd.ToJsonStrIgnoreErr(filter))
		log.CtxLog(ctx).Error(err)
		return
	}
	for _, feature := range features {
		if feature.AvgNonChargingLoss > 0 {
			res[resourceIdMap[feature.DeviceId]] = feature
		}
	}
	// 补全剩余device的信息
	for _, deviceId := range c.Devices {
		if _, ok := res[deviceId]; !ok {
			res[deviceId] = latestFeatures[0]
		}
	}
	return
}

// GenerateBatchConfig 批量生成配置
func (c *ConfigDO) GenerateBatchConfig(ctx context.Context) (err error) {
	configMap, err := c.GetDeviceConfig(ctx)
	if err != nil {
		log.CtxLog(ctx).Errorf("generate batch config, fail to get device config, err: %v, config: %s", err, ucmd.ToJsonStrIgnoreErr(configMap))
		return
	}
	configList := make([]interface{}, 0)
	now := time.Now()
	for deviceId, configPO := range configMap {
		configPO = c.PrepareUserParams(ctx, configPO, "batch")
		configPO = c.PrepareAutoParams(ctx, configPO)
		configPO.Id = "cfg_" + xid.New().String()
		configPO.ConfigNameOrigin = c.ConfigName
		configPO.ConfigName = fmt.Sprintf("%s-%s-%s", now.Format("060102"), c.ConfigName, deviceId)
		configPO.Remark = c.Remark
		configPO.Creator = c.UserId
		configPO.CreateTs = now.UnixMilli()
		configPO.UpdateTs = now.UnixMilli()
		configPO.Date = now
		configPO.IsRealDevice = c.IsRealDevice
		configList = append(configList, configPO)
	}
	err = client.GetWatcher().Mongodb().NewMongoEntry().InsertMany(umw.Algorithm, CollectionConfigs, configList, []client.IndexOption{
		{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 180 * 24 * 3600},
		{Name: "creator_name_unique", Fields: bson.D{{"creator", 1}, {"config_name", 1}}, Unique: true},
	}...)
	if err != nil {
		log.CtxLog(ctx).Errorf("generate batch config, fail to insert config, err: %v, config: %s", err, ucmd.ToJsonStrIgnoreErr(configMap))
		return
	}
	return
}

// GenerateSingleConfig 生成单站配置
func (c *ConfigDO) GenerateSingleConfig(ctx context.Context) (err error) {
	refresh := true
	configMap := make(map[string]mmgo.MongoPsosConfig)
	// 在编辑配置时，判断设备和仿真时间是否有修改
	if c.ConfigId != nil {
		var originConfig mmgo.MongoPsosConfig
		err = client.GetWatcher().Mongodb().NewMongoEntry(bson.D{{"_id", *c.ConfigId}}).FindOne(umw.Algorithm, CollectionConfigs, options.FindOne(), &originConfig)
		if err != nil {
			log.CtxLog(ctx).Errorf("generate single config, fail to get origin config, err: %v, configDO: %s", err, ucmd.ToJsonStrIgnoreErr(c))
			return
		}
		configMap[originConfig.DeviceInfo.DeviceId] = originConfig
		// 设备和仿真时间都没有修改
		if originConfig.SimulationInfo.SimulationStartTime == c.StartTs &&
			originConfig.SimulationInfo.SimulationStartTime+originConfig.SimulationInfo.SimulationPeriod == c.EndTs &&
			originConfig.DeviceInfo.DeviceId == c.Devices[0] {
			refresh = false
		}
	}
	if refresh {
		configMap, err = c.GetDeviceConfig(ctx)
		if err != nil {
			log.CtxLog(ctx).Errorf("generate single config, fail to get device config, err: %v, config: %s", err, ucmd.ToJsonStrIgnoreErr(configMap))
			return
		}
	}
	deviceId := c.Devices[0]
	configPO := configMap[c.Devices[0]]
	now := time.Now()
	configPO = c.PrepareUserParams(ctx, configPO, "single")
	configPO = c.PrepareAutoParams(ctx, configPO)
	if c.ConfigId != nil {
		configPO.Id = *c.ConfigId
	} else {
		configPO.Id = "cfg_" + xid.New().String()
	}
	configPO.ConfigNameOrigin = c.ConfigName
	configPO.ConfigName = fmt.Sprintf("%s-%s-%s", now.Format("060102"), c.ConfigName, deviceId)
	configPO.Remark = c.Remark
	configPO.Creator = c.UserId
	configPO.CreateTs = now.UnixMilli()
	configPO.UpdateTs = now.UnixMilli()
	configPO.Date = now
	configPO.IsRealDevice = c.IsRealDevice

	filter := bson.D{{"_id", configPO.Id}}
	if c.ConfigId != nil {
		configPO.CreateTs = 0
	}
	update := bson.M{"$set": configPO}
	err = client.GetWatcher().Mongodb().NewMongoEntry(filter).UpdateOne(umw.Algorithm, CollectionConfigs, update, true, []client.IndexOption{
		{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 180 * 24 * 3600},
		{Name: "creator_name_unique", Fields: bson.D{{"creator", 1}, {"config_name", 1}}, Unique: true},
	}...)
	if err != nil {
		log.CtxLog(ctx).Errorf("generate single config, fail to update config, err: %v, config: %s", err, ucmd.ToJsonStrIgnoreErr(configMap))
		return
	}
	return
}

// PrepareUserParams 将用户输入的参数覆盖站的真实参数
func (c *ConfigDO) PrepareUserParams(ctx context.Context, configPO mmgo.MongoPsosConfig, mode string) mmgo.MongoPsosConfig {
	if c.CmsSwitch != nil {
		configPO.DeviceInfo.OperationStrategyInfo.CmsStrategySwitch.SwitchValue = int32(*c.CmsSwitch)
	}
	if c.StationCapacity != nil {
		configPO.DeviceInfo.OperationStrategyInfo.PowerDistributionCapacity = *c.StationCapacity
	}
	if c.Circuit1Capacity != nil && c.Circuit2Capacity != nil {
		configPO.DeviceInfo.OperationStrategyInfo.CircuitDistributionCapacity = []int{*c.Circuit1Capacity, *c.Circuit2Capacity}
	}
	if c.ElectricityPriceModel != nil && len(c.ElectricityDetailList) > 0 {
		// 生成48个元素的半小时粒度的电价数组
		if *c.ElectricityPriceModel == ElectricityOnePrice {
			c.ElectricityDetailList[0].Start = "00:00"
			c.ElectricityDetailList[0].End = "24:00"
		}
		electricityDetails, err := CalculateElectricityDetails(c.ElectricityDetailList)
		if err != nil {
			log.CtxLog(ctx).Errorf("fail to CalculateElectricityDetails, err: %v, electricity detail list: %s", err, ucmd.ToJsonStrIgnoreErr(c.ElectricityDetailList))
		} else {
			configPO.ScenarioInfo.ElectricityPriceModel = *c.ElectricityPriceModel
			configPO.ScenarioInfo.ElectricityDetails = electricityDetails
			configPO.ScenarioInfo.ElectricityDetailUser = c.ElectricityDetailList
		}
	}
	if c.OperationStartHour != nil && c.OperationEndHour != nil && *c.OperationStartHour < *c.OperationEndHour {
		configPO.DeviceInfo.OperationStrategyInfo.OperationTime = []int{*c.OperationStartHour, *c.OperationEndHour}
	}
	if c.NotfullySwapSwitch != nil {
		configPO.DeviceInfo.OperationStrategyInfo.NotfullySwapSwitch.SwitchValue = int32(c.NotfullySwapSwitch.SwitchValue)
		if c.NotfullySwapSwitch.SwitchValue == 1 {
			configPO.DeviceInfo.OperationStrategyInfo.BatterySocLowerLimit = c.NotfullySwapSwitch.SocLowerLimit
			configPO.DeviceInfo.OperationStrategyInfo.BatterySocUpperLimit = c.NotfullySwapSwitch.SocUpperLimit
		}
	}
	if c.SilentModeSwitch != nil {
		configPO.DeviceInfo.OperationStrategyInfo.SilentModeSwitch.SwitchValue = int32(*c.SilentModeSwitch)
	}
	if c.BatteryExchangeSwitch != nil {
		configPO.DeviceInfo.OperationStrategyInfo.BatteryExchangeSwitch.SwitchValue = int32(*c.BatteryExchangeSwitch)
	}
	if c.BatteryRestSwitch != nil {
		configPO.DeviceInfo.OperationStrategyInfo.BatteryRestSwitch = mmgo.BatteryRestSwitch{
			SwitchValue:              int32(c.BatteryRestSwitch.SwitchValue),
			DefaultRestCurrent:       c.BatteryRestSwitch.DefaultRestCurrent,
			DefaultHangingDuration:   c.BatteryRestSwitch.DefaultHangingDuration,
			DefaultHangingStep:       c.BatteryRestSwitch.DefaultHangingStep,
			DefaultHangingCurrentMax: c.BatteryRestSwitch.DefaultHangingCurrentMax,
			DefaultRestLabel:         1,
			DefaultRestDuration:      600,
		}
	}
	if mode == "single" {
		configPO.DeviceInfo.OperationStrategyInfo.BatteryInfo = c.BatteryInfo
		batteryMap := make(map[int]mmgo.PsosBatteryInfo)
		for _, item := range c.BatteryInfo {
			batteryMap[item.SlotId] = item
		}
		batteryTypeConfig := GetBatteryConfig(batteryMap, c.Project)
		configPO.DeviceInfo.OperationStrategyInfo.BatteryTypeConf = batteryTypeConfig
		configPO.BaseBatteryConfig = batteryTypeConfig
		configPO.ServiceInfo.SwappingUserList = c.ServiceList
		configPO.ServiceInfo.SwappingUserNum = len(c.ServiceList)
		configPO.ServiceInfo.SwappingFailureSwitch.SwitchValue = int32(c.SwappingFailureSwitch)
		configPO.ServiceInfo.SkipLevelSwapSwitch.SwitchValue = int32(c.SkipLevelSwapSwitch)
	}
	return configPO
}

func CalculateElectricityDetails(electricityDetailList []mmgo.ElectricityDetailUserConfig) ([]float64, error) {
	electricityDetails := make([]float64, 0)
	sort.Slice(electricityDetailList, func(i, j int) bool {
		return electricityDetailList[i].Start < electricityDetailList[j].Start
	})
	for _, item := range electricityDetailList {
		start, err1 := time.Parse("15:04", item.Start)
		if err1 != nil {
			return nil, err1
		}
		if item.End == "24:00" {
			item.End = "23:59"
		}
		end, err2 := time.Parse("15:04", item.End)
		if err2 != nil {
			return nil, err2
		}
		duration := end.Sub(start)
		halfHoursDiff := int(duration.Minutes()+1) / 30
		for range halfHoursDiff {
			electricityDetails = append(electricityDetails, item.Price)
		}
	}
	return electricityDetails, nil
}

func CalculateElectricityList(electricityDetails []float64) ([]mmgo.ElectricityDetailUserConfig, error) {
	electricityDetailList := make([]mmgo.ElectricityDetailUserConfig, 0)
	start := 0
	for end := range electricityDetails {
		if electricityDetails[end] != electricityDetails[start] {
			electricityDetailList = append(electricityDetailList, mmgo.ElectricityDetailUserConfig{
				Start: time.Date(0, 0, 0, start/2, start%2*30, 0, 0, time.Local).Format("15:04"),
				End:   time.Date(0, 0, 0, end/2, end%2*30, 0, 0, time.Local).Format("15:04"),
				Price: electricityDetails[start],
			})
			start = end
		}
	}
	electricityDetailList = append(electricityDetailList, mmgo.ElectricityDetailUserConfig{
		Start: time.Date(0, 0, 0, start/2, start%2*30, 0, 0, time.Local).Format("15:04"),
		End:   "24:00",
		Price: electricityDetails[start],
	})
	return electricityDetailList, nil
}

// PrepareAutoParams 检查站真实参数，使用默认值覆盖值为空的字段
func (c *ConfigDO) PrepareAutoParams(ctx context.Context, configPO mmgo.MongoPsosConfig) mmgo.MongoPsosConfig {
	//// 电池配比
	//isEmpty := true
	//for _, cnt := range configPO.BaseBatteryConfig {
	//	if int(cnt) > 0 {
	//		isEmpty = false
	//		break
	//	}
	//}
	//if isEmpty {
	//	configPO.BaseBatteryConfig = BatteryTypeConfig[c.Project]
	//	configPO.DeviceInfo.OperationStrategyInfo.BatteryTypeConf = BatteryTypeConfig[c.Project]
	//}
	//// 电池信息随机生成
	//hasBattery := false
	//for _, batteryInfo := range configPO.DeviceInfo.OperationStrategyInfo.BatteryInfo {
	//	if batteryInfo.BatteryRatedKwh != nil {
	//		hasBattery = true
	//		break
	//	}
	//}
	//if !hasBattery {
	//	randomBatteryInfo, err := c.RandomBatteryInfo(ctx, configPO.DeviceInfo.DeviceId, BatteryTypeConfig[c.Project])
	//	if err == nil {
	//		configPO.DeviceInfo.OperationStrategyInfo.BatteryTypeConf = BatteryTypeConfig[c.Project]
	//		configPO.DeviceInfo.OperationStrategyInfo.BatteryInfo = randomBatteryInfo
	//	} else {
	//		log.CtxLog(ctx).Errorf("fail to get random battery info, err: %v", err)
	//	}
	//}
	// 电池信息补全
	for i, item := range configPO.DeviceInfo.OperationStrategyInfo.BatteryInfo {
		if item.BatteryRatedKwh == nil {
			continue
		}
		if item.ChargingStopSoc == nil {
			var stopSoc float64
			switch *item.BatteryRatedKwh {
			case common.CapacityValue50kwh:
				stopSoc = float64(configPO.DeviceInfo.OperationStrategyInfo.ChargingStopSoc50kWh)
			case common.CapacityValue75kwh:
				stopSoc = float64(configPO.DeviceInfo.OperationStrategyInfo.ChargingStopSoc75kWh)
			case common.CapacityValue100kwh:
				stopSoc = float64(configPO.DeviceInfo.OperationStrategyInfo.ChargingStopSoc100kWh)
			case common.CapacityValue150kwh:
				stopSoc = float64(configPO.DeviceInfo.OperationStrategyInfo.ChargingStopSoc150kWh)
			case common.CapacityValue60kwh:
				stopSoc = float64(configPO.DeviceInfo.OperationStrategyInfo.ChargingStopSoc60kWh)
			case common.CapacityValue85kwh:
				stopSoc = float64(configPO.DeviceInfo.OperationStrategyInfo.ChargingStopSoc85kWh)
			default:
				stopSoc = BatteryChargingStopSoc
			}
			configPO.DeviceInfo.OperationStrategyInfo.BatteryInfo[i].ChargingStopSoc = &stopSoc
		}
		if item.BatteryId == nil {
			batteryId := xid.New().String()
			configPO.DeviceInfo.OperationStrategyInfo.BatteryInfo[i].BatteryId = &batteryId
		}
		if item.BatterySoc == nil {
			configPO.DeviceInfo.OperationStrategyInfo.BatteryInfo[i].BatterySoc = &BatterySoc
		}
		if item.PackMaxTemperature == nil {
			configPO.DeviceInfo.OperationStrategyInfo.BatteryInfo[i].PackMaxTemperature = &BatteryPackMaxTemperature
		}
		if item.RealBatteryRatedKwh == nil {
			configPO.DeviceInfo.OperationStrategyInfo.BatteryInfo[i].RealBatteryRatedKwh = item.BatteryRatedKwh
		}
		if item.BatteryOwnership == nil {
			configPO.DeviceInfo.OperationStrategyInfo.BatteryInfo[i].BatteryOwnership = &DefaultBatteryOwnership
		}
	}
	// 服务订单信息补全
	for i, item := range configPO.ServiceInfo.SwappingUserList {
		if item.RealBatteryRatedKwh == 0 {
			configPO.ServiceInfo.SwappingUserList[i].RealBatteryRatedKwh = item.BatteryRatedKwh
		}
		if item.BatteryId == nil {
			batteryId := xid.New().String()
			configPO.ServiceInfo.SwappingUserList[i].BatteryId = &batteryId
		}
		if item.VehicleId == nil {
			vehicleId := xid.New().String()
			configPO.ServiceInfo.SwappingUserList[i].VehicleId = &vehicleId
		}
		if item.ServiceId == nil {
			serviceId := xid.New().String()
			configPO.ServiceInfo.SwappingUserList[i].ServiceId = &serviceId
		}
		if item.SwappingTime == nil {
			swappingTime := SwappingTimeSuccess[c.Project]
			configPO.ServiceInfo.SwappingUserList[i].SwappingTime = &swappingTime
		}
		if item.TargetBatteryRatedKwh == nil {
			configPO.ServiceInfo.SwappingUserList[i].TargetBatteryRatedKwh = &item.BatteryRatedKwh
		}
		if item.RealTargetBatteryRatedKwh == nil {
			configPO.ServiceInfo.SwappingUserList[i].RealTargetBatteryRatedKwh = configPO.ServiceInfo.SwappingUserList[i].TargetBatteryRatedKwh
		}
		if item.PackMaxTemperature == nil {
			configPO.ServiceInfo.SwappingUserList[i].PackMaxTemperature = &ServiceBatteryTemperature
		}
		if item.BatteryOwnership == nil {
			configPO.ServiceInfo.SwappingUserList[i].BatteryOwnership = &DefaultBatteryOwnership
		}
		if item.UserOwnership == nil {
			configPO.ServiceInfo.SwappingUserList[i].UserOwnership = &DefaultUserOwnership
		}
		if item.BatteryRestLabel == nil {
			configPO.ServiceInfo.SwappingUserList[i].BatteryRestLabel = &DefaultBatteryRestLabel
		}
	}
	// 电价信息
	if len(configPO.ScenarioInfo.ElectricityDetailUser) == 0 {
		res, err := CalculateElectricityList(configPO.ScenarioInfo.ElectricityDetails)
		if err == nil {
			configPO.ScenarioInfo.ElectricityDetailUser = res
		} else {
			log.CtxLog(ctx).Errorf("fail to calculate electricity list, err: %v, details: %s", err, ucmd.ToJsonStrIgnoreErr(configPO.ScenarioInfo.ElectricityDetails))
		}
	}
	// 仿真步长
	if configPO.SimulationInfo.TimeStep == 0 {
		configPO.SimulationInfo.TimeStep = 10
	}
	return configPO
}

// RandomBatteryInfo 根据电池配比生成所有仓位的随机电池信息
func (c *ConfigDO) RandomBatteryInfo(ctx context.Context, deviceId string, batteryConfig []float64) (res []mmgo.PsosBatteryInfo, err error) {
	BatteryKWhGroup := AlgorithmBatteryList[c.Project]
	if len(batteryConfig) != len(BatteryKWhGroup) {
		err = fmt.Errorf("batteryConfig (%s), batteryKWhGroup (%s) not match", ucmd.ToJsonStrIgnoreErr(batteryConfig), ucmd.ToJsonStrIgnoreErr(BatteryKWhGroup))
		return
	}

	deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(deviceId)
	resourceId := "full"
	if found {
		resourceId = deviceInfo.ResourceId
	}

	featureData, err := c.GetRandomFeature(ctx, resourceId, bson.M{"battery_temp_result_map": 1, "soc_result_map": 1})
	if err != nil {
		log.CtxLog(ctx).Errorf("fail to get random feature: %v, config: %s", err, ucmd.ToJsonStrIgnoreErr(c))
		return
	}
	fullFeatureData, err := c.GetRandomFeature(ctx, "full", bson.M{})
	if err != nil {
		log.CtxLog(ctx).Errorf("fail to get random full feature: %v, config: %s", err, ucmd.ToJsonStrIgnoreErr(c))
		return
	}
	hour := time.UnixMilli(c.StartTs).Hour()
	var temperatureMap, socMap HourlyBatteryTypeRatioList = featureData.BatteryTempResultMap, featureData.SocResultMap
	if temperatureMap == nil || len(temperatureMap) == 0 {
		temperatureMap = fullFeatureData.BatteryTempResultMap
	}
	if socMap == nil || len(socMap) == 0 {
		socMap = fullFeatureData.SocResultMap
	}
	temperatureRandom, socRandom := make(map[int]mmgo.RatioMap), make(map[int]mmgo.RatioMap)
	// 乐道60度和85度电池使用100度电池的概率
	for _, batteryType := range BatteryKWhGroup {
		if mp := temperatureMap.GetPsosRatioMap(ctx, hour, batteryType); len(mp) > 0 {
			temperatureRandom[batteryType] = mp
		}
		if mp := socMap.GetPsosRatioMap(ctx, hour, batteryType); len(mp) > 0 {
			socRandom[batteryType] = mp
		}
	}
	if temperatureRandom[common.CapacityValue60kwh] == nil {
		temperatureRandom[common.CapacityValue60kwh] = temperatureRandom[common.CapacityValue100kwh]
	}
	if temperatureRandom[common.CapacityValue85kwh] == nil {
		temperatureRandom[common.CapacityValue85kwh] = temperatureRandom[common.CapacityValue100kwh]
	}
	if socRandom[common.CapacityValue60kwh] == nil {
		socRandom[common.CapacityValue60kwh] = socRandom[common.CapacityValue100kwh]
	}
	if socRandom[common.CapacityValue85kwh] == nil {
		socRandom[common.CapacityValue85kwh] = socRandom[common.CapacityValue100kwh]
	}
	log.CtxLog(ctx).Infof("generate random battery, temp: %s, resource id: %s", ucmd.ToJsonStrIgnoreErr(temperatureRandom), resourceId)
	log.CtxLog(ctx).Infof("generate random battery, soc: %s, resource id: %s", ucmd.ToJsonStrIgnoreErr(socRandom), resourceId)

	randomBattery := func(batteryType int) (res mmgo.PsosBatteryInfo) {
		batteryId := xid.New().String()
		res.BatteryId = &batteryId
		res.BatteryRatedKwh = &batteryType
		res.RealBatteryRatedKwh = &batteryType
		res.ChargingStopSoc = &BatteryChargingStopSoc
		res.BatteryOwnership = &DefaultBatteryOwnership
		temperatureRange := c.Rng.GenerateRandomKey(temperatureRandom[batteryType])
		if temperatureRange == "" {
			log.CtxLog(ctx).Warnf("generate random battery, cannot get temperatureRange, %s, battery_type: %d", ucmd.ToJsonStrIgnoreErr(temperatureRandom[batteryType]), batteryType)
			res.PackMaxTemperature = &BatteryPackMaxTemperature
		} else {
			temp := c.Rng.GenerateIntFromRange(temperatureRange)
			if temp == math.MinInt {
				log.CtxLog(ctx).Errorf("generate random battery, invalid temperatureRange: %s, battery_type: %d", temperatureRange, batteryType)
				res.PackMaxTemperature = &BatteryPackMaxTemperature
			} else {
				tempFloat := float64(temp)
				res.PackMaxTemperature = &tempFloat
			}
		}
		socRange := c.Rng.GenerateRandomKey(socRandom[batteryType])
		if socRange == "" {
			log.CtxLog(ctx).Warnf("generate random battery, cannot get socRange, %s, battery_type: %d", ucmd.ToJsonStrIgnoreErr(socRandom[batteryType]), batteryType)
			res.BatterySoc = &BatterySoc
		} else {
			soc := c.Rng.GenerateIntFromRange(socRange)
			if soc == math.MinInt {
				log.CtxLog(ctx).Errorf("generate random battery, invalid socRange: %s, battery_type: %d", socRange, batteryType)
				res.BatterySoc = &BatterySoc
			} else {
				socFloat := float64(soc)
				res.BatterySoc = &socFloat
			}
		}
		return
	}

	for i, batteryType := range BatteryKWhGroup {
		count := batteryConfig[i]
		for range int(count) {
			res = append(res, randomBattery(batteryType))
		}
	}

	idx := 0
	hasBatterySlot := make(map[int]bool)
	// 先确定非满电电池的仓位
	for i := range res {
		if *res[i].BatterySoc < *res[i].ChargingStopSoc {
			slotId := GenerateBatterySlot[c.Project][idx]
			res[i].SlotId = slotId
			hasBatterySlot[slotId] = true
			idx += 1
		}
	}
	// 再确认满电电池的仓位
	for i := range res {
		if *res[i].BatterySoc >= *res[i].ChargingStopSoc {
			slotId := GenerateBatterySlot[c.Project][idx]
			res[i].SlotId = slotId
			hasBatterySlot[slotId] = true
			idx += 1
		}
	}
	for i := range BatterySlotNum[c.Project] {
		slotId := i + 1
		if !hasBatterySlot[slotId] {
			res = append(res, mmgo.PsosBatteryInfo{
				SlotId: slotId,
			})
		}
	}
	sort.Slice(res, func(i, j int) bool {
		return res[i].SlotId < res[j].SlotId
	})
	return
}

// RandomService 根据服务电池数量生成的随机服务订单信息
func (c *ConfigDO) RandomService(ctx context.Context, deviceId string, batteryConfig []float64, serviceCount []int) (res []mmgo.SwappingUser, err error) {
	BatteryKWhGroup := AlgorithmBatteryList[c.Project]
	if len(batteryConfig) != len(serviceCount) || len(batteryConfig) != len(BatteryKWhGroup) {
		err = fmt.Errorf("batteryConfig (%s), serviceCount (%s), batteryKWhGroup (%s) not match", ucmd.ToJsonStrIgnoreErr(batteryConfig), ucmd.ToJsonStrIgnoreErr(serviceCount), ucmd.ToJsonStrIgnoreErr(BatteryKWhGroup))
		return
	}
	// 站内无初始电池，直接返回
	sum := 0.0
	batteryExist := make(map[string]bool)
	for i, item := range batteryConfig {
		sum += item
		if item > 0 {
			batteryExist[fmt.Sprintf("%d", BatteryKWhGroup[i])] = true
		}
	}
	if sum == 0 {
		return
	}

	deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(deviceId)
	resourceId := "full"
	if found {
		resourceId = deviceInfo.ResourceId
	}

	startTime := time.UnixMilli(c.StartTs)
	endTime := time.UnixMilli(c.EndTs)
	featureData, err := c.GetRandomFeature(ctx, resourceId, bson.M{
		"hour_service_cnt_ratio_map": 1,
		"in_battery_temp_ratio_map":  1,
		"success_fail_map":           1,
	})
	if err != nil {
		log.CtxLog(ctx).Errorf("fail to get random feature: %v, device id: %s", err, resourceId)
		return
	}
	fullFeatureData, err := c.GetRandomFeature(ctx, "full", bson.M{})
	if err != nil {
		log.CtxLog(ctx).Errorf("fail to get random feature: %v, device id: full", err)
		return
	}

	// 每小时的订单产生概率
	var operationTime []int
	if c.OperationStartHour != nil && c.OperationEndHour != nil && *c.OperationStartHour < *c.OperationEndHour {
		operationTime = []int{*c.OperationStartHour, *c.OperationEndHour}
	} else {
		psosHandler := NewHandler(client.GetWatcher())
		var operateInfo map[string]PriceInfo
		operateInfo, err = psosHandler.GetPriceInfo(ctx, c.Project, c.Devices)
		if err != nil {
			log.CtxLog(ctx).Errorf("fail to get price info, err: %v", err)
			return
		}
		for _, val := range operateInfo {
			operationTime = val.OperationTime
		}
		if len(operationTime) < 2 || operationTime[0] > operationTime[1] {
			operationTime = OperationTime[c.Project]
		}
	}
	operationTimeMap := make(map[int]bool)
	for i := operationTime[0]; i < operationTime[1]; i++ {
		operationTimeMap[i] = true
	}
	log.CtxLog(ctx).Infof("generate random service, operation time: %s", ucmd.ToJsonStrIgnoreErr(operationTime))

	hourProbMap := make(map[int]float64)
	hourServiceCntRatioMap := featureData.HourServiceCntRatioMap
	if hourServiceCntRatioMap == nil || len(hourServiceCntRatioMap) == 0 {
		hourServiceCntRatioMap = fullFeatureData.HourServiceCntRatioMap
	}
	for _, item := range hourServiceCntRatioMap {
		hourProbMap[item.Hour] = item.HourServiceRatio
	}
	hourServiceCntProb := make(map[string]float64)
	current := time.Date(startTime.Year(), startTime.Month(), startTime.Day(), startTime.Hour(), 0, 0, 0, time.Local)
	for endTime.Sub(current) > time.Second {
		if operationTimeMap[current.Hour()] {
			hourServiceCntProb[fmt.Sprintf("%d", current.UnixMilli())] = hourProbMap[current.Hour()]
		}
		current = current.Add(time.Hour)
	}
	hourServiceCntProb = util.StandardizeProbMap(hourServiceCntProb)
	//fmt.Println("len", len(hourServiceCntProb))
	//for k, v := range hourServiceCntProb {
	//	kInt, _ := strconv.ParseInt(k, 10, 64)
	//	fmt.Println(time.UnixMilli(kInt), ":", v)
	//}
	// 每小时进站电池温度概率
	var inBatteryTemperatureList HourlyRatioList = featureData.InBatteryTempRatioMap
	if inBatteryTemperatureList == nil || len(inBatteryTemperatureList) == 0 {
		inBatteryTemperatureList = fullFeatureData.InBatteryTempRatioMap
	}
	inBatteryTemperatureRandom := make(map[int]mmgo.RatioMap)
	for h := range 24 {
		if mp := inBatteryTemperatureList.GetPsosRatioMap(ctx, h); len(mp) > 0 {
			inBatteryTemperatureRandom[h] = mp
		}
	}
	// 故障换电概率
	successFailProb := make(map[string]float64)
	successFailMap := make(map[string]mmgo.SuccessFail)
	successFailMapData := featureData.SuccessFailMap
	if successFailMapData == nil || len(successFailMapData) == 0 {
		successFailMapData = fullFeatureData.SuccessFailMap
	}
	for _, item := range successFailMapData {
		successFailProb[fmt.Sprintf("%d", item.SuccessFail)] = item.Ratio
		successFailMap[fmt.Sprintf("%d", item.SuccessFail)] = item
	}
	// 跨级换电概率
	var crossLevelBatterySwappingList BatteryTypeRatioList = fullFeatureData.CrossLevelBatterySwappingMap
	crossLevelBatterySwappingRandom := make(map[int]mmgo.RatioMap)
	for _, batteryType := range BatteryKWhGroup {
		tempProb := crossLevelBatterySwappingList.GetPsosRatioMap(ctx, batteryType)
		totalProb := 0.0
		for _, prob := range tempProb {
			totalProb += prob
		}
		// 换成同一种电池
		tempProb[fmt.Sprintf("%d-%d", batteryType, batteryType)] = 1 - totalProb
		// 去除不合法的电池类型
		crossLevelBatterySwappingProb := make(mmgo.RatioMap)
		for key, prob := range tempProb {
			// key形式：75-100
			targetBattery := strings.Split(key, "-")[1]
			if !batteryExist[targetBattery] {
				continue
			}
			crossLevelBatterySwappingProb[key] = prob
		}
		if len(crossLevelBatterySwappingProb) == 0 {
			// 默认值，NIO全都换成75度电池，乐道全都换成60度电池
			if common.GetBatteryBrand(batteryType) == model.EvBrandONVO {
				crossLevelBatterySwappingProb[fmt.Sprintf("%d-%d", batteryType, common.CapacityValue60kwh)] = 1
			} else {
				crossLevelBatterySwappingProb[fmt.Sprintf("%d-%d", batteryType, common.CapacityValue75kwh)] = 1
			}
		}
		// 标准化
		crossLevelBatterySwappingRandom[batteryType] = util.StandardizeProbMap(crossLevelBatterySwappingProb)
	}
	// 进站电池soc概率
	var startSocList RatioMapList = fullFeatureData.StartSocGroupRatioMap
	startSocProb := startSocList.GetPsosRatioMap(ctx)

	log.CtxLog(ctx).Infof("generate random service, hourServiceCntProb: %s, resource id: %s", ucmd.ToJsonStrIgnoreErr(hourServiceCntProb), resourceId)
	log.CtxLog(ctx).Infof("generate random service, inBatteryTemperatureRandom: %s, resource id: %s", ucmd.ToJsonStrIgnoreErr(inBatteryTemperatureRandom), resourceId)
	log.CtxLog(ctx).Infof("generate random service, successFailProb: %s, resource id: %s", ucmd.ToJsonStrIgnoreErr(successFailProb), resourceId)
	log.CtxLog(ctx).Infof("generate random service, crossLevelBatterySwappingRandom: %s, resource id: %s", ucmd.ToJsonStrIgnoreErr(crossLevelBatterySwappingRandom), resourceId)
	log.CtxLog(ctx).Infof("generate random service, startSocProb: %s, resource id: %s", ucmd.ToJsonStrIgnoreErr(startSocProb), resourceId)

	randomService := func(userArrivalTime time.Time, batteryType int) (res mmgo.SwappingUser) {
		res.UserArrivalTime = userArrivalTime.UnixMilli()
		res.BatteryRatedKwh = batteryType
		res.RealBatteryRatedKwh = batteryType
		serviceId := xid.New().String()
		batteryId := xid.New().String()
		vehicleId := xid.New().String()
		res.ServiceId = &serviceId
		res.BatteryId = &batteryId
		res.VehicleId = &vehicleId
		res.UserOwnership = &DefaultUserOwnership
		res.BatteryRestLabel = &DefaultBatteryRestLabel
		res.BatteryOwnership = &DefaultBatteryOwnership
		// 进站电池soc
		startSocRange := c.Rng.GenerateRandomKey(startSocProb)
		if startSocRange == "" {
			log.CtxLog(ctx).Warnf("generate random service, cannot get startSocRange, %s", ucmd.ToJsonStrIgnoreErr(startSocProb))
			res.BatterySoc = ServiceBatterySoc
		} else {
			soc := c.Rng.GenerateIntFromRange(startSocRange)
			if soc == math.MinInt {
				log.CtxLog(ctx).Errorf("generate random service, invalid startSocRange: %s", startSocRange)
				res.BatterySoc = ServiceBatterySoc
			} else {
				res.BatterySoc = soc
			}
		}
		// 换电时长
		var swappingTime int
		if c.SwappingFailureSwitch == 1 {
			// 开启故障换电开关
			successFail := c.Rng.GenerateRandomKey(successFailProb)
			if successFail == "" {
				log.CtxLog(ctx).Warnf("generate random service, cannot get successFail, %s", ucmd.ToJsonStrIgnoreErr(successFailProb))
				swappingTime = SwappingTimeSuccess[c.Project]
			} else {
				swappingTime = int(successFailMap[successFail].Duration)
			}
		} else {
			swappingTime = int(successFailMap["0"].Duration)
		}
		res.SwappingTime = &swappingTime
		// 目标电池类型
		if c.SkipLevelSwapSwitch == 1 {
			// 开启跨级换电开关
			crossLevelBattery := c.Rng.GenerateRandomKey(crossLevelBatterySwappingRandom[batteryType])
			if len(strings.Split(crossLevelBattery, "-")) != 2 {
				log.CtxLog(ctx).Warnf("generate random service, cannot get crossLevelRange, %s", ucmd.ToJsonStrIgnoreErr(crossLevelBattery))
				res.TargetBatteryRatedKwh = &ServiceCrossSwapBatteryType
			} else {
				var targetBattery int
				targetBattery, err = strconv.Atoi(strings.Split(crossLevelBattery, "-")[1])
				if err != nil {
					log.CtxLog(ctx).Errorf("generate random service, cannot get targetBattery, %v, crossLevelBattery: %s", err, ucmd.ToJsonStrIgnoreErr(crossLevelBattery))
					targetBattery = batteryType
				}
				res.TargetBatteryRatedKwh = &targetBattery
			}
		} else {
			res.TargetBatteryRatedKwh = &batteryType
		}
		res.RealTargetBatteryRatedKwh = res.TargetBatteryRatedKwh
		// 进站电池温度
		batteryTemperatureRange := c.Rng.GenerateRandomKey(inBatteryTemperatureRandom[userArrivalTime.Hour()])
		if batteryTemperatureRange == "" {
			log.CtxLog(ctx).Warnf("generate random service, cannot get batteryTemperatureRange, %s", ucmd.ToJsonStrIgnoreErr(batteryTemperatureRange))
			res.PackMaxTemperature = &ServiceBatteryTemperature
		} else {
			temp := c.Rng.GenerateIntFromRange(batteryTemperatureRange)
			if temp == math.MinInt {
				log.CtxLog(ctx).Errorf("generate random service, invalid batteryTemperatureRange: %s", batteryTemperatureRange)
				res.PackMaxTemperature = &ServiceBatteryTemperature
			} else {
				res.PackMaxTemperature = &temp
			}
		}
		return
	}

	startHour := time.Date(startTime.Year(), startTime.Month(), startTime.Day(), startTime.Hour(), 0, 0, 0, time.Local)
	endHour := time.Date(endTime.Year(), endTime.Month(), endTime.Day(), endTime.Hour(), 0, 0, 0, time.Local)
	for i, batteryType := range BatteryKWhGroup {
		count := serviceCount[i]
		for range count {
			// 生成随机用户到达时间
			var randomHour int64
			randomHour, err = strconv.ParseInt(c.Rng.GenerateRandomKey(hourServiceCntProb), 10, 64)
			if err != nil {
				log.CtxLog(ctx).Errorf("fail to get random hour, err: %v, hourServiceCntProb: %s", err, ucmd.ToJsonStrIgnoreErr(hourServiceCntProb))
				randomHour = int64(startHour.Hour())
			}

			baseTimeHour := time.UnixMilli(randomHour)
			// 生成可能的秒数，对于开始小时和结束小时，需要进一步缩小范围
			seconds := []int{0, 3600}
			//fmt.Printf("startHour: %v, endHour: %v, baseTimeHour: %v\n", startHour, endHour, baseTimeHour)
			if baseTimeHour == startHour {
				seconds[0] = startTime.Second() + startTime.Minute()*60
			} else if baseTimeHour == endHour {
				seconds[1] = endTime.Second() + endTime.Minute()*60
			}
			baseTimeHour = baseTimeHour.Add(time.Duration(c.Rng.Intn(seconds[1]-seconds[0])+seconds[0]) * time.Second)
			//fmt.Printf("seconds: %v, user arrival time: %v, start time: %v, end time: %v\n", seconds, baseTimeHour, startTime, endTime)
			res = append(res, randomService(baseTimeHour, batteryType))
		}
	}
	sort.Slice(res, func(i, j int) bool {
		return res[i].UserArrivalTime > res[j].UserArrivalTime
	})
	return
}

// GetRandomFeature 获取start_ts对应当天的psos随机特征数据
func (c *ConfigDO) GetRandomFeature(ctx context.Context, resourceId string, projection bson.M) (featureData mmgo.MongoPsosFeature, err error) {
	originResourceId := resourceId
	if resourceId == "" {
		resourceId = "full"
	}
	cnt, err := client.GetWatcher().PLCMongodb().NewMongoEntry(bson.D{{"device_id", resourceId}}).Count(umw.Algorithm, CollectionFeature)
	if err != nil {
		log.CtxLog(ctx).Errorf("fail to count psos feature, err: %v", err)
		return
	}
	if cnt == 0 {
		// 若不使用真实设备，则resource_id为随机生成，使用全量站数据
		resourceId = "full"
	}
	// 获取当天数据
	filter := bson.D{
		{"device_id", resourceId},
		{"day", time.UnixMilli(c.StartTs).Format("20060102")},
	}
	rawData, err := client.GetWatcher().PLCMongodb().NewMongoEntry(filter).GetOne(umw.Algorithm, CollectionFeature, client.MongoOptions{Projection: &projection})
	if err != nil {
		log.CtxLog(ctx).Errorf("fail to get psos feature, err: %v, filter: %s", err, ucmd.ToJsonStrIgnoreErr(filter))
		return
	}
	if rawData != nil {
		if err = bson.Unmarshal(rawData, &featureData); err != nil {
			log.CtxLog(ctx).Errorf("fail to unmarshal psos feature, err: %v, filter: %s", err, ucmd.ToJsonStrIgnoreErr(filter))
			return
		}
	} else {
		// 若仿真开始当天数据为空，则获取最新数据
		opts := &options.FindOptions{}
		filter = bson.D{
			{"device_id", resourceId},
		}
		opts = opts.SetSort(bson.M{"day": -1}).SetLimit(1).SetProjection(projection)
		var features []mmgo.MongoPsosFeature
		_, err = client.GetWatcher().PLCMongodb().NewMongoEntry(filter).FindMany(umw.Algorithm, CollectionFeature, opts, &features)
		if err != nil || len(features) == 0 {
			err = fmt.Errorf("fail to get latest psos feature, err: %v, filter: %s", err, ucmd.ToJsonStrIgnoreErr(filter))
			log.CtxLog(ctx).Error(err)
			return
		}
		featureData = features[0]
	}
	log.CtxLog(ctx).Infof("get random feature, origin resource id: %s, filter: %s", originResourceId, ucmd.ToJsonStrIgnoreErr(filter))
	return
}

// CheckConfigName 检查配置名是否重复
func (c *ConfigDO) CheckConfigName(ctx context.Context) (ok bool, err error) {
	today := time.Now().Format("060102")
	names := make([]string, 0)
	for _, deviceId := range c.Devices {
		names = append(names, fmt.Sprintf("%s-%s-%s", today, c.ConfigName, deviceId))
	}
	filter := bson.M{"creator": c.UserId, "config_name": bson.M{"$in": names}}
	if c.ConfigId != nil {
		filter["_id"] = bson.M{"$ne": *c.ConfigId}
	}
	cnt, err := client.GetWatcher().Mongodb().CountDocuments(umw.Algorithm, CollectionConfigs, filter)
	if err != nil {
		return
	}
	if cnt == 0 {
		ok = true
	}
	return
}

type HourlyBatteryTypeRatioList []mmgo.HourlyBatteryTypeRatio
type BatteryTypeRatioList []mmgo.BatteryTypeRatio
type HourlyRatioList []mmgo.HourlyRatio
type RatioMapList []mmgo.RatioMap

func (l HourlyBatteryTypeRatioList) GetPsosRatioMap(ctx context.Context, hourInt, batteryInt int) (res mmgo.RatioMap) {
	res = make(mmgo.RatioMap)
	hour := fmt.Sprintf("%02d", hourInt)
	batteryType := fmt.Sprintf("%d", batteryInt)
	var batteryTypeRatioList []mmgo.BatteryTypeRatio
	for _, hourlyBatteryTypeRatio := range l {
		if _, found := hourlyBatteryTypeRatio[hour]; found {
			batteryTypeRatioList = hourlyBatteryTypeRatio[hour]
			break
		}
	}
	var ratioMapList []mmgo.RatioMap
	for _, batteryTypeRatio := range batteryTypeRatioList {
		if _, found := batteryTypeRatio[batteryType]; found {
			ratioMapList = batteryTypeRatio[batteryType]
			break
		}
	}

	for _, item := range ratioMapList {
		for k, v := range item {
			res[k] = v
		}
	}
	return
}

func (l BatteryTypeRatioList) GetPsosRatioMap(ctx context.Context, batteryInt int) (res mmgo.RatioMap) {
	res = make(mmgo.RatioMap)
	batteryType := fmt.Sprintf("%d", batteryInt)
	var ratioMapList []mmgo.RatioMap
	for _, batteryTypeRatio := range l {
		if _, found := batteryTypeRatio[batteryType]; found {
			ratioMapList = batteryTypeRatio[batteryType]
			break
		}
	}
	for _, item := range ratioMapList {
		for k, v := range item {
			res[k] = v
		}
	}
	return
}

func (l HourlyRatioList) GetPsosRatioMap(ctx context.Context, hourInt int) (res mmgo.RatioMap) {
	res = make(mmgo.RatioMap)
	hour := fmt.Sprintf("%02d", hourInt)
	var ratioMapList []mmgo.RatioMap
	for _, hourlyRatio := range l {
		if _, found := hourlyRatio[hour]; found {
			ratioMapList = hourlyRatio[hour]
			break
		}
	}
	for _, item := range ratioMapList {
		for k, v := range item {
			res[k] = v
		}
	}
	return
}

func (l RatioMapList) GetPsosRatioMap(ctx context.Context) (res mmgo.RatioMap) {
	res = make(mmgo.RatioMap)
	for _, item := range l {
		for k, v := range item {
			res[k] = v
		}
	}
	return
}

// getHoursInRange 获取StartTs到EndTs之间的小时列表，超过半小时才算一小时
// 返回格式：[{"day": "2025-08-01", "hour": "10"}, {"day": "2025-08-01", "hour": "11"}]
func getHoursInRange(ctx context.Context, startTs, endTs int64) []map[string]string {
	var result []map[string]string

	startTime := time.UnixMilli(startTs)
	endTime := time.UnixMilli(endTs)

	// 从开始时间的整点开始
	current := time.Date(startTime.Year(), startTime.Month(), startTime.Day(), startTime.Hour(), 0, 0, 0, startTime.Location())

	for current.Before(endTime) {
		nextHour := current.Add(time.Hour)

		// 计算当前小时内的有效时间长度
		var validStart, validEnd time.Time

		// 确定当前小时的有效开始时间
		if current.Before(startTime) {
			validStart = startTime
		} else {
			validStart = current
		}

		// 确定当前小时的有效结束时间
		if nextHour.After(endTime) {
			validEnd = endTime
		} else {
			validEnd = nextHour
		}

		// 计算有效时间长度
		validDuration := validEnd.Sub(validStart)

		// 如果有效时间超过30分钟，则包含这个小时
		if validDuration > 30*time.Minute {
			result = append(result, map[string]string{
				"day":  current.Format("2006-01-02"),
				"hour": strconv.Itoa(current.Hour()),
			})
		}

		current = nextHour
	}

	return result
}
