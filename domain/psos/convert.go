package psos

import (
	"context"
	"fmt"
	"math/rand"
	"strings"
	"time"

	"github.com/rs/xid"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/model/mongo"
	"git.nevint.com/welkin2/welkin-backend/util"
)

func ConvertTaskPO2DO(ctx context.Context, PO *mongo.MongoPsosTask) *TaskDO {
	taskDO := &TaskDO{
		Id:                  PO.Id,
		Name:                PO.Name,
		Status:              PO.Status,
		Remark:              PO.Remark,
		Project:             PO.Project,
		Creator:             PO.Creator,
		FmsCompressTaskInfo: PO.FmsCompressTaskInfo,
		CreateTimestamp:     PO.CreateTs,
		UpdateTimestamp:     PO.UpdateTs,
	}
	return taskDO

}

func ConvertSimulationPO2DO(ctx context.Context, PO *mongo.MongoPsosSimulation) *SimulationDO {
	simulationDO := &SimulationDO{
		Id:                  PO.Id,
		TaskId:              PO.TaskId,
		ConfigId:            PO.ConfigId,
		Status:              PO.Status,
		IsBaseConfig:        PO.IsBaseConfig,
		IsRealDevice:        PO.IsRealDevice,
		DeviceResultUrl:     PO.DeviceResultUrl,
		BatteryResultUrl:    PO.BatteryResultUrl,
		ServiceResultUrl:    PO.ServiceResultUrl,
		BusinessCalculation: PO.BusinessCalculation,
		DevicePerformance:   PO.DevicePerformance,
		UserExperience:      PO.UserExperience,
		SimulationInfo:      PO.SimulationInfo,
		DeviceInfo:          PO.DeviceInfo,
		ServiceInfo:         PO.ServiceInfo,
		ScenarioInfo:        PO.ScenarioInfo,
		ProgressRate:        PO.Progress,
		StartRunTimestamp:   PO.StartRunningTs,
		CreateTimestamp:     PO.CreateTs,
		UpdateTimestamp:     PO.UpdateTs,
	}
	return simulationDO
}

func ConvertBatchConfigVO2DO(ctx context.Context, VO PsosBatchConfigRequest, userId string) *ConfigDO {
	configDO := &ConfigDO{
		ConfigName:            VO.ConfigName,
		Remark:                VO.Remark,
		UserId:                userId,
		Project:               VO.Project,
		StartTs:               VO.StartTs,
		EndTs:                 VO.EndTs,
		StationCapacity:       VO.StationCapacity,
		Circuit1Capacity:      VO.Circuit1Capacity,
		Circuit2Capacity:      VO.Circuit2Capacity,
		CmsSwitch:             VO.CmsSwitch,
		ElectricityPriceModel: VO.ElectricityPriceModel,
		ElectricityDetailList: VO.ElectricityDetailList,
		Devices:               VO.Devices,
		IsRealDevice:          true,
		OperationStartHour:    VO.OperationStartHour,
		OperationEndHour:      VO.OperationEndHour,
		NotfullySwapSwitch:    VO.NotfullySwapSwitch,
		SilentModeSwitch:      VO.SilentModeSwitch,
		BatteryExchangeSwitch: VO.BatteryExchangeSwitch,
		BatteryRestSwitch:     VO.BatteryRestSwitch,
	}
	configDO.Rng = &util.Rand{Rand: rand.New(rand.NewSource(time.Now().UnixNano()))}
	return configDO
}

func ConvertSingleConfigVO2DO(ctx context.Context, VO PsosSingleConfigRequest, userId string) *ConfigDO {
	configDO := &ConfigDO{
		ConfigName:            VO.ConfigName,
		Remark:                VO.Remark,
		UserId:                userId,
		Project:               VO.Project,
		StartTs:               VO.StartTs,
		EndTs:                 VO.EndTs,
		StationCapacity:       VO.StationCapacity,
		Circuit1Capacity:      VO.Circuit1Capacity,
		Circuit2Capacity:      VO.Circuit2Capacity,
		CmsSwitch:             VO.CmsSwitch,
		ElectricityPriceModel: VO.ElectricityPriceModel,
		ElectricityDetailList: VO.ElectricityDetailList,
		ConfigId:              VO.ConfigId,
		SkipLevelSwapSwitch:   VO.SkipLevelSwapSwitch,
		SwappingFailureSwitch: VO.SwappingFailureSwitch,
		BatteryInfo:           VO.BatteryInfo,
		ServiceList:           VO.ServiceList,
		OperationStartHour:    VO.OperationStartHour,
		OperationEndHour:      VO.OperationEndHour,
		NotfullySwapSwitch:    VO.NotfullySwapSwitch,
		SilentModeSwitch:      VO.SilentModeSwitch,
		BatteryExchangeSwitch: VO.BatteryExchangeSwitch,
		BatteryRestSwitch:     VO.BatteryRestSwitch,
	}
	if VO.DeviceId == "full" || VO.DeviceId == "" {
		VO.DeviceId = xid.New().String()
	}
	if VO.DeviceId != "" {
		configDO.Devices = []string{VO.DeviceId}
	}
	if VO.IsRealDevice != nil {
		configDO.IsRealDevice = *VO.IsRealDevice
	}
	configDO.Rng = &util.Rand{Rand: rand.New(rand.NewSource(time.Now().UnixNano()))}
	return configDO
}

func ConvertGenerateRandomVO2DO(ctx context.Context, VO GenerateRandomRequest) *ConfigDO {
	configDO := &ConfigDO{
		StartTs:               VO.StartTs,
		EndTs:                 VO.EndTs,
		Project:               VO.Project,
		Devices:               []string{VO.DeviceId},
		SkipLevelSwapSwitch:   VO.SkipLevelSwapSwitch,
		SwappingFailureSwitch: VO.SwappingFailureSwitch,
		OperationStartHour:    &VO.OperationStartHour,
		OperationEndHour:      &VO.OperationEndHour,
		Rng:                   &util.Rand{Rand: rand.New(rand.NewSource(time.Now().UnixNano()))},
	}
	return configDO
}

func ConvertConfigDetailPO2VO(ctx context.Context, PO *mongo.MongoPsosConfig) *ConfigDetail {
	configDetail := &ConfigDetail{
		StartTs:               PO.SimulationInfo.SimulationStartTime,
		EndTs:                 PO.SimulationInfo.SimulationStartTime + PO.SimulationInfo.SimulationPeriod,
		ConfigName:            PO.ConfigName,
		ConfigNameOrigin:      PO.ConfigNameOrigin,
		Remark:                PO.Remark,
		StationCapacity:       PO.DeviceInfo.OperationStrategyInfo.PowerDistributionCapacity,
		Circuit1Capacity:      PO.DeviceInfo.OperationStrategyInfo.CircuitDistributionCapacity[0],
		Circuit2Capacity:      PO.DeviceInfo.OperationStrategyInfo.CircuitDistributionCapacity[1],
		CmsSwitch:             int(PO.DeviceInfo.OperationStrategyInfo.CmsStrategySwitch.SwitchValue),
		ElectricityPriceModel: PO.ScenarioInfo.ElectricityPriceModel,
		ElectricityDetailList: PO.ScenarioInfo.ElectricityDetailUser,
		DeviceId:              PO.DeviceInfo.DeviceId,
		BatteryInfo:           PO.DeviceInfo.OperationStrategyInfo.BatteryInfo,
		ServiceList:           PO.ServiceInfo.SwappingUserList,
		IsRealDevice:          PO.IsRealDevice,
		SkipLevelSwapSwitch:   int(PO.ServiceInfo.SkipLevelSwapSwitch.SwitchValue),
		SwappingFailureSwitch: int(PO.ServiceInfo.SwappingFailureSwitch.SwitchValue),
		NotfullySwapSwitch: NotfullySwapSwitch{
			SwitchValue:   int(PO.DeviceInfo.OperationStrategyInfo.NotfullySwapSwitch.SwitchValue),
			SocLowerLimit: PO.DeviceInfo.OperationStrategyInfo.BatterySocLowerLimit,
			SocUpperLimit: PO.DeviceInfo.OperationStrategyInfo.BatterySocUpperLimit,
		},
		SilentModeSwitch:      int(PO.DeviceInfo.OperationStrategyInfo.SilentModeSwitch.SwitchValue),
		BatteryExchangeSwitch: int(PO.DeviceInfo.OperationStrategyInfo.BatteryExchangeSwitch.SwitchValue),
		BatteryRestSwitch: BatteryRestSwitch{
			SwitchValue:              int(PO.DeviceInfo.OperationStrategyInfo.BatteryRestSwitch.SwitchValue),
			DefaultRestCurrent:       PO.DeviceInfo.OperationStrategyInfo.BatteryRestSwitch.DefaultRestCurrent,
			DefaultHangingDuration:   PO.DeviceInfo.OperationStrategyInfo.BatteryRestSwitch.DefaultHangingDuration,
			DefaultHangingStep:       PO.DeviceInfo.OperationStrategyInfo.BatteryRestSwitch.DefaultHangingStep,
			DefaultHangingCurrentMax: PO.DeviceInfo.OperationStrategyInfo.BatteryRestSwitch.DefaultHangingCurrentMax,
		},
		Project: PO.Project,
	}
	cutoff := len(PO.ConfigName) - len(PO.DeviceInfo.DeviceId) - 1
	if cutoff > 0 {
		configDetail.ConfigName = PO.ConfigName[:cutoff]
	}
	hours := make([]string, 0)
	for _, hour := range PO.DeviceInfo.OperationStrategyInfo.OperationTime {
		hours = append(hours, fmt.Sprintf("%d", hour))
	}
	// 若运营时间段不在可选值中，赋上默认值
	if OperationTimeList[strings.Join(hours, ",")] {
		configDetail.OperationStartHour = PO.DeviceInfo.OperationStrategyInfo.OperationTime[0]
		configDetail.OperationEndHour = PO.DeviceInfo.OperationStrategyInfo.OperationTime[1]
	} else {
		configDetail.OperationStartHour = OperationTime[PO.Project][0]
		configDetail.OperationEndHour = OperationTime[PO.Project][1]
	}
	deviceInfo, exist := cache.PowerSwapCache.GetSingleDevice(PO.DeviceInfo.DeviceId)
	if exist {
		configDetail.Description = deviceInfo.Description
	}
	return configDetail
}

// 天宫前后端交互，电池顺序为50、60、75、85、100、150；天宫和psos算法交互，电池顺序为50、75、100、150、60、85
func ConvertSimulateBattery2AlgorithmBatteryList[T any](project string, batteryList []T) []T {
	if len(batteryList) != len(SimulateBatteryList[project]) {
		return batteryList
	}
	orderMap := make(map[int]T)
	for i, item := range batteryList {
		orderMap[SimulateBatteryList[project][i]] = item
	}
	for i, batteryType := range AlgorithmBatteryList[project] {
		batteryList[i] = orderMap[batteryType]
	}
	return batteryList
}

func ConvertAlgorithmBattery2SimulateBatteryList[T any](project string, batteryList []T) []T {
	if len(batteryList) != len(SimulateBatteryList[project]) {
		return batteryList
	}
	orderMap := make(map[int]T)
	for i, item := range batteryList {
		orderMap[AlgorithmBatteryList[project][i]] = item
	}
	for i, batteryType := range SimulateBatteryList[project] {
		batteryList[i] = orderMap[batteryType]
	}
	return batteryList
}

func convertBatteryOwnership(origin string) string {
	switch origin {
	case "car_owner":
		return "Car_owner"
	case "nio":
		return "NIO"
	default:
		return "Bac"
	}
}

func convertVehicleOwnership(origin int) string {
	switch origin {
	case 1:
		return "BaaS"
	default:
		return "BuyOut"
	}
}
