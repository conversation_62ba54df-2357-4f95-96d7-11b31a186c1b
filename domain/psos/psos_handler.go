package psos

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"math/rand"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rs/xid"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	udao "git.nevint.com/golang-libs/common-utils/dao"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/domain/battery"
	"git.nevint.com/welkin2/welkin-backend/domain/common"
	domain_service "git.nevint.com/welkin2/welkin-backend/domain/service"
	"git.nevint.com/welkin2/welkin-backend/domain/vehicle"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"
	"git.nevint.com/welkin2/welkin-backend/util"
)

type Handler struct {
	Watcher client.Watcher
}

func NewHandler(w client.Watcher) *Handler {
	InitOnce()
	return &Handler{
		Watcher: w,
	}
}

// GetBatteryRealtime 获取电池相关实时数据
func (h *Handler) GetBatteryRealtime(c context.Context, project, deviceId string, startTime, endTime int64) (batteryMap map[int]mmgo.PsosBatteryInfo, err error) {
	batteryMap = make(map[int]mmgo.PsosBatteryInfo)
	var batteryRealtime = make(map[int]int)
	batterySlotNum := BatterySlotNum[project]
	if project == umw.PowerSwap2 {
		batteryRealtime = BatteryRealtimeSlotPS2
	} else if project == umw.PUS3 {
		batteryRealtime = BatteryRealtimeSlotPUS3
	} else if project == umw.PUS4 {
		batteryRealtime = BatteryRealtimeSlotPUS4
	}
	res := make(map[int]TDengineBatteryInfo)
	for i := 1; i <= batterySlotNum; i++ {
		res[i] = TDengineBatteryInfo{
			SlotId:              i,
			RealBatteryRatedKwh: -1,
		}
		batteryMap[i] = mmgo.PsosBatteryInfo{SlotId: i}
	}
	var dataIdList []string
	for dataId := range batteryRealtime {
		dataIdList = append(dataIdList, strconv.Itoa(dataId))
	}
	dataIds := strings.Join(dataIdList, ",")
	tdw := &client.TDWatcher{
		TDClient:     h.Watcher.TDEngine(),
		RedisClient:  h.Watcher.Redis(),
		DeviceId:     deviceId,
		StartTs:      &startTime,
		EndTs:        &endTime,
		Limit:        1,
		FilterFields: make([]string, 0),
		Descending:   true,
		Logger:       log.CtxLog(c).Named("TDEngine"),
	}
	stbName := fmt.Sprintf("realtime_%s", strings.ToLower(project))
	scanStruct, dataIdMap, err := tdw.GetRealtimeFields("device2oss_realtime", stbName, dataIds, true)
	if err != nil {
		log.CtxLog(c).Errorf("get realtime fields fail, err: %v", err)
		return
	}
	_, rows, err := tdw.FilterDataByFields("device2oss_realtime")
	if err != nil {
		log.CtxLog(c).Errorf("get realtime data by fields fail: %v, err: %v", tdw.FilterFields, err)
		return
	}
	if rows != nil {
		for rows.Next() {
			// 获取动态的查询结构体
			columns := client.ReflectFields(scanStruct)
			if err = rows.Scan(columns...); err != nil {
				log.CtxLog(c).Errorf("scan realtime data from tdengine fail, err: %v", err)
				continue
			}
			for dataId := range dataIdMap {
				val := scanStruct.FieldByName("R" + dataId).Interface().(model.SafeType).GetRealValue()
				handleVal, slot, field, cErr := ConvertDataId(project, dataId)
				if cErr != nil {
					log.CtxLog(c).Errorf("fail to convert data id, err: %v", cErr)
					continue
				}
				val = handleVal(val)
				batteryInfo := res[slot]
				if err = util.SetField(&batteryInfo, field, val); err != nil {
					log.CtxLog(c).Errorf("fail to set battery info field %s, err: %v, value: %v, type: %T", field, err, val, val)
					continue
				}
				res[slot] = batteryInfo
			}
		}
	}
	batteryIds := make([]string, 0)
	for slot, batteryInfo := range res {
		if batteryInfo.RealBatteryRatedKwh == -1 || batteryInfo.BatteryId == "" {
			continue
		}
		batteryIds = append(batteryIds, batteryInfo.BatteryId)
		batteryType := ConvertPsosRealBatteryType(batteryInfo.RealBatteryRatedKwh)
		psosBatteryInfo := mmgo.PsosBatteryInfo{
			SlotId:              slot,
			BatteryRatedKwh:     &batteryType,
			RealBatteryRatedKwh: &batteryInfo.RealBatteryRatedKwh,
		}
		// 判断各个字段是否为无效值
		if batteryInfo.BatterySoc > -9990 {
			psosBatteryInfo.BatterySoc = &batteryInfo.BatterySoc
		}
		if batteryInfo.PackMaxTemperature > -9990 {
			psosBatteryInfo.PackMaxTemperature = &batteryInfo.PackMaxTemperature
		}
		psosBatteryInfo.BatteryId = &batteryInfo.BatteryId
		batteryMap[slot] = psosBatteryInfo
	}
	// 电池产权
	batteryDO := &battery.BatteryDO{}
	batteryList, err := batteryDO.ListBattery(c, battery.ListBatteryCond{BatteryIds: batteryIds})
	if err != nil {
		log.CtxLog(c).Errorf("fail to ListBattery, err: %v, batteryIds: %s", err, ucmd.ToJsonStrIgnoreErr(batteryList))
	}
	batteryDOMap := make(map[string]battery.BatteryDO)
	for _, b := range batteryList {
		batteryDOMap[b.BatteryId] = b
	}
	for i, record := range batteryMap {
		if record.BatteryId == nil {
			continue
		}
		if b, ok := batteryDOMap[*record.BatteryId]; ok {
			ownership := convertBatteryOwnership(b.Ownership)
			record.BatteryOwnership = &ownership
			batteryMap[i] = record
		}
	}
	return
}

// HandleDeviceBatteryConfig 获取平均电池配比，通过真实设备模拟
func (h *Handler) HandleDeviceBatteryConfig(c context.Context, project string) ([]float64, error) {
	ctx, ok := c.(*gin.Context)
	if !ok {
		return nil, fmt.Errorf("invalid context")
	}
	devices := ctx.Query("devices")
	if len(devices) == 0 {
		return nil, fmt.Errorf("empty devices")
	}
	deviceList := strings.Split(devices, ",")
	startTimeStr := ctx.Query("start_time")
	startTime, err := strconv.ParseInt(startTimeStr, 10, 64)
	if err != nil {
		log.CtxLog(c).Errorf("fail to parse start_time %s, err: %v", startTimeStr, err)
		return nil, err
	}

	// 查找时间范围为 startTime~startTime+5min
	endTime := startTime + 5*time.Minute.Milliseconds()
	g := ucmd.NewErrGroup(c, 10)
	batteryConfigList := make([][]float64, len(deviceList))
	mu := sync.Mutex{}
	for i := range deviceList {
		iCopy := i
		g.GoRecover(func() error {
			deviceId := deviceList[iCopy]
			res, gErr := h.GetBatteryRealtime(c, project, deviceId, startTime, endTime)
			if gErr != nil {
				log.CtxLog(c).Errorf("fail to getBatteryRealtime, err: %v, project: %s, deviceId: %s, startTime: %d, endTime: %d", gErr, project, deviceId, startTime, endTime)
				return gErr
			}

			mu.Lock()
			batteryConfigList[iCopy] = GetBatteryConfig(res, project)
			defer mu.Unlock()
			return nil
		})
	}
	if err = g.Wait(); err != nil {
		return nil, err
	}
	// 计算平均值
	res := make([]float64, 0)
	for i := range batteryConfigList[0] {
		var sum float64
		for _, bc := range batteryConfigList {
			sum += bc[i]
		}
		res = append(res, sum/float64(len(batteryConfigList)))
	}
	return res, nil
}

// HandleConfigBatteryConfig 获取平均电池配比，通过已有配方
func (h *Handler) HandleConfigBatteryConfig(c context.Context, configList []string, project string) ([]float64, error) {
	res := make([]float64, len(AlgorithmBatteryList[project]))
	opts := &options.FindOptions{}
	opts.SetProjection(bson.M{"base_battery_config": 1})
	filter := bson.D{{"_id", bson.M{"$in": configList}}}
	var configData []mmgo.MongoPsosConfig
	total, err := h.Watcher.Mongodb().NewMongoEntry(filter).FindMany(umw.Algorithm, CollectionConfigs, opts, &configData)
	if err != nil || total == 0 {
		log.CtxLog(c).Errorf("fail to find psos configs, err: %v, filter: %s", err, ucmd.ToJsonStrIgnoreErr(filter))
		return res, err
	}
	for _, config := range configData {
		for i := range config.BaseBatteryConfig {
			res[i] += config.BaseBatteryConfig[i]
		}
	}
	for i := range res {
		res[i] = math.Floor(res[i] / float64(total))
	}
	return ConvertAlgorithmBattery2SimulateBatteryList(project, res), nil
}

// GetPriceInfo 获取设备电价信息以及运营情况
func (h *Handler) GetPriceInfo(c context.Context, project string, deviceList []string) (res map[string]PriceInfo, err error) {
	res = make(map[string]PriceInfo)
	if project != umw.PUS3 && project != umw.PowerSwap2 && project != umw.PUS4 {
		err = fmt.Errorf("invalid project: %s", project)
		log.CtxLog(c).Error(err)
		return
	}
	// 写入默认值
	for _, deviceId := range deviceList {
		priceInfo := PriceInfo{
			OperationTime: OperationTime[project],
		}
		priceInfo.ElectricityPriceModel = ElectricityOnePrice
		priceInfo.ElectricityDetails = ElectricityDetailsOnePrice[project]
		res[deviceId] = priceInfo
	}
	// 若为三代站，需要将原本的device_id转为虚拟id进行查找
	var resourceList []string
	resourceDeviceMap := make(map[string]string)
	for _, deviceId := range deviceList {
		deviceInfo, ok := cache.PowerSwapCache.GetSingleDevice(deviceId)
		if ok {
			resourceList = append(resourceList, deviceInfo.ResourceId)
			resourceDeviceMap[deviceInfo.ResourceId] = deviceId
		} else {
			resourceList = append(resourceList, deviceId)
			resourceDeviceMap[deviceId] = deviceId
		}
	}

	filter := bson.D{
		{"device_id", bson.M{"$in": resourceList}},
	}
	byteData, err := udao.NewMongoEntry(h.Watcher.PLCMongodb().Client, filter).ListAll(umw.Algorithm, umw.CmsHivHistoryData, udao.MongoOptions{
		Projection: &bson.M{"_id": 0, "device_id": 1, "map_hourly_electricity_price": 1, "electricity_price_model": 1, "operation_time": 1},
	})
	if err != nil {
		log.CtxLog(c).Errorf("fail to find hive history data, err: %v, device list: %s", err, ucmd.ToJsonStrIgnoreErr(deviceList))
		return
	}
	var deviceElectricity []mmgo.DeviceElectricityPriceData
	if err = json.Unmarshal(byteData, &deviceElectricity); err != nil {
		log.CtxLog(c).Errorf("fail to unmarshal DeviceElectricityPriceData, err: %v, data: %s", err, string(byteData))
		return
	}
	// 格式转换
	for _, item := range deviceElectricity {
		operationTime := make([]int, 0)
		if len(item.OperationTime) == 0 {
			operationTime = OperationTime[project]
		} else {
			operationTime = item.OperationTime
		}
		priceInfo := PriceInfo{
			OperationTime: operationTime,
		}
		// 只有different_price的站才做转换，其他站均看作one_price
		if item.ElectricityPriceModel != ElectricityDifferentPrice {
			priceInfo.ElectricityPriceModel = ElectricityOnePrice
			priceInfo.ElectricityDetails = ElectricityDetailsOnePrice[project]
			res[item.DeviceId] = priceInfo
			continue
		}
		// 将different_price的站做转换
		if len(item.MapHourlyElectricityPrice) == 0 {
			priceInfo.ElectricityPriceModel = ElectricityDifferentPrice
			priceInfo.ElectricityDetails = ElectricityDetailsDifferentPrice[project]
			res[item.DeviceId] = priceInfo
			continue
		}
		priceList := item.MapHourlyElectricityPrice
		sort.Slice(priceList, func(i, j int) bool {
			startI, _ := strconv.ParseFloat(priceList[i].StartHour, 32)
			startJ, _ := strconv.ParseFloat(priceList[j].StartHour, 32)
			return startI < startJ
		})
		electricityDetailsList := make([]float64, 0)
		for _, price := range priceList {
			start, _ := strconv.ParseFloat(price.StartHour, 64)
			end, _ := strconv.ParseFloat(price.EndHour, 64)
			priceFloat, _ := strconv.ParseFloat(price.Price, 64)
			currCnt := int(2 * (end - start))
			if currCnt == 0 || priceFloat == 0 {
				log.CtxLog(c).Warnf("invalid price, data: %s", ucmd.ToJsonStrIgnoreErr(priceList))
				electricityDetailsList = ElectricityDetailsDifferentPrice[project]
				break
			}
			for i := 0; i <= currCnt; i++ {
				electricityDetailsList = append(electricityDetailsList, priceFloat)
			}
		}
		if len(electricityDetailsList) != 48 {
			electricityDetailsList = ElectricityDetailsDifferentPrice[project]
		}
		priceInfo.ElectricityPriceModel = ElectricityDifferentPrice
		priceInfo.ElectricityDetails = electricityDetailsList
		res[resourceDeviceMap[item.DeviceId]] = priceInfo
	}
	return
}

// GetSwapDuration 获取过去一个月的成功/失败换电的平均时长
func (h *Handler) GetSwapDuration(c context.Context, project string, ts int64, deviceList []string) (successDuration map[string]int, failDuration map[string]int, err error) {
	successDuration, failDuration = make(map[string]int), make(map[string]int)
	if project != umw.PowerSwap2 && project != umw.PUS3 && project != umw.PUS4 {
		err = fmt.Errorf("invalid project: %s", project)
		log.CtxLog(c).Error(err)
		return
	}
	// 写入默认值
	for _, deviceId := range deviceList {
		successDuration[deviceId] = SwappingTimeSuccess[project]
		failDuration[deviceId] = SwappingTimeFault[project]
	}
	// 若为三代站，需要将原本的device_id转为虚拟id进行查找
	var resourceList []string
	resourceDeviceMap := make(map[string]string)
	for _, deviceId := range deviceList {
		deviceInfo, ok := cache.PowerSwapCache.GetSingleDevice(deviceId)
		if ok {
			resourceList = append(resourceList, deviceInfo.ResourceId)
			resourceDeviceMap[deviceInfo.ResourceId] = deviceId
		} else {
			resourceList = append(resourceList, deviceId)
			resourceDeviceMap[deviceId] = deviceId
		}
	}

	startTs := ts - 30*24*time.Hour.Milliseconds()
	pipeline := mongo.Pipeline{
		bson.D{{"$match", bson.M{
			"resource_id":       bson.M{"$in": resourceList},
			"user_arrival_time": bson.M{"$gte": startTs / 1000, "$lte": ts / 1000},
		}}},
		bson.D{{"$group", bson.M{
			"_id":          bson.M{"resource_id": "$resource_id", "status": "$status"},
			"duration_avg": bson.M{"$avg": "$duration"},
		}}},
		bson.D{{"$project", bson.M{
			"_id":         0,
			"resource_id": "$_id.resource_id",
			"status":      "$_id.status",
			"duration":    bson.M{"$toInt": "$duration_avg"},
		}}},
	}
	var serviceData []mmgo.MongoPsosServiceInfo
	if err = h.Watcher.Mongodb().NewMongoEntry().Aggregate(umw.Algorithm, CollectionServices, pipeline, &serviceData); err != nil {
		log.CtxLog(c).Errorf("fail to get swap duration, err: %v, pipeline: %s", err, ucmd.ToJsonStrIgnoreErr(pipeline))
		return
	}

	for _, sd := range serviceData {
		if sd.Duration <= 0 {
			continue
		}
		if sd.Status == 1 {
			// 成功订单
			successDuration[resourceDeviceMap[sd.ResourceId]] = sd.Duration
		} else if sd.Status == 2 {
			// 故障订单
			failDuration[resourceDeviceMap[sd.ResourceId]] = sd.Duration
		}
	}
	return
}

// GetRedrabbitDeviceParams 获取psos所需赤兔参数点
func (h *Handler) GetRedrabbitDeviceParams(c context.Context, project string, deviceList []string) (deviceParams map[string]map[string]interface{}, err error) {
	deviceParams = make(map[string]map[string]interface{})
	if project != umw.PowerSwap2 && project != umw.PUS3 && project != umw.PUS4 {
		err = fmt.Errorf("invalid project: %s", project)
		log.CtxLog(c).Error(err)
		return
	}
	// 写入默认值
	for _, deviceId := range deviceList {
		deviceParams[deviceId] = make(map[string]interface{})
		deviceParams[deviceId][ParamStationCapacity] = PowerDistributionCapacity[project]
		deviceParams[deviceId][ParamCircuitCapacity] = CircuitDistributionCapacity[project]
		deviceParams[deviceId][ParamBranchCurrentLimit] = BranchCircuitCurrentLimit[project]
		deviceParams[deviceId][ParamBatteryExchangeSwitch] = 1
		deviceParams[deviceId][ParamSilentModeSwitch] = 0
		deviceParams[deviceId][ParamCmsStrategySwitch] = 0
		deviceParams[deviceId][ParamCmsFrequency] = 10
		deviceParams[deviceId][Param50kWhChargingStopSoc] = ChargingStopSoc50kWh[project]
		deviceParams[deviceId][Param70kWhChargingStopSoc] = ChargingStopSoc70kWh[project]
		deviceParams[deviceId][Param75kWhChargingStopSoc] = ChargingStopSoc75kWh[project]
		deviceParams[deviceId][Param100kWhChargingStopSoc] = ChargingStopSoc100kWh[project]
		deviceParams[deviceId][Param150kWhChargingStopSoc] = ChargingStopSoc150kWh[project]
		deviceParams[deviceId][Param60kWhChargingStopSoc] = ChargingStopSoc60kWh[project]
		deviceParams[deviceId][Param85kWhChargingStopSoc] = ChargingStopSoc85kWh[project]
	}
	filter := bson.D{
		{"_id", bson.M{"$in": deviceList}},
		{"params", bson.M{"$elemMatch": bson.M{
			"key": bson.M{"$in": RedRabbitDataIdList[project]},
		}}},
	}
	byteData, err := udao.NewMongoEntry(h.Watcher.RbMongodb().Client, filter).ListAll(project, "devices", udao.MongoOptions{
		Projection: &bson.M{"_id": 1, "params": 1},
	})
	if err != nil {
		log.CtxLog(c).Errorf("fail to get redrabbit data, err: %v, filter: %s", err, ucmd.ToJsonStrIgnoreErr(filter))
		return
	}
	var redrabbitDevice []struct {
		Id     string                   `json:"_id" bson:"_id"`
		Params []map[string]interface{} `json:"params" bson:"params"`
	}
	if err = json.Unmarshal(byteData, &redrabbitDevice); err != nil {
		log.CtxLog(c).Errorf("fail to unmarshal redrabbitDevice, err: %v, data: %s", err, string(byteData))
		return
	}
	for _, item := range redrabbitDevice {
		circuitCapacity := make([]int, len(CircuitDistributionCapacity[project]))
		copy(circuitCapacity, CircuitDistributionCapacity[project])
		branchCurrentLimit := make([]int, len(BranchCircuitCurrentLimit[project]))
		copy(branchCurrentLimit, BranchCircuitCurrentLimit[project])
		batteryExchangeSwitch := []int{0, 0}
		for _, param := range item.Params {
			dataId := int64(util.ParseInt(param["key"]))
			if name, ok := RedRabbitDataId[project][dataId]; ok {
				// 四个点需要做额外处理
				idx := 0
				if len(strings.Split(name, "-")) == 2 {
					idx, _ = strconv.Atoi(strings.Split(name, "-")[1])
				}
				if strings.HasPrefix(name, ParamCircuitCapacity) {
					circuitCapacity[idx] = util.ParseInt(param["value"])
				} else if strings.HasPrefix(name, ParamBranchCurrentLimit) {
					branchCurrentLimit[idx] = util.ParseInt(param["value"])
				} else if strings.HasPrefix(name, ParamBatteryExchangeSwitch) {
					batteryExchangeSwitch[idx] = util.ParseInt(param["value"])
				} else if strings.HasPrefix(name, ParamCmsStrategySwitch) {
					if util.ParseInt(param["value"]) > 0 {
						deviceParams[item.Id][name] = 1
					}
				} else {
					deviceParams[item.Id][name] = param["value"]
				}
			}
		}
		deviceParams[item.Id][ParamCircuitCapacity] = circuitCapacity
		deviceParams[item.Id][ParamBranchCurrentLimit] = branchCurrentLimit
		// 两个倒仓开关，只要有一个开了就算打开
		if batteryExchangeSwitch[0] == 1 || batteryExchangeSwitch[1] == 1 {
			deviceParams[item.Id][ParamBatteryExchangeSwitch] = 1
		}
	}
	return
}

// GetSwapUserList 获取设备一段时间内的订单用户列表
func (h *Handler) GetSwapUserList(c context.Context, project string, startTs, endTs int64, deviceList []string) (swapUserMap map[string][]mmgo.SwappingUser, err error) {
	swapUserMap = make(map[string][]mmgo.SwappingUser)
	deviceServiceMap := make(map[string]map[string]mmgo.SwappingUser)
	for _, deviceId := range deviceList {
		swapUserMap[deviceId] = make([]mmgo.SwappingUser, 0)
		deviceServiceMap[deviceId] = make(map[string]mmgo.SwappingUser)
	}
	if project != umw.PowerSwap2 && project != umw.PUS3 && project != umw.PUS4 {
		err = fmt.Errorf("invalid project: %s", project)
		log.CtxLog(c).Error(err)
		return
	}

	filter := bson.D{
		{"device_id", bson.M{"$in": deviceList}},
		{"creation_time", bson.M{"$gte": startTs, "$lte": endTs}},
	}
	var orders []mmgo.MongoOrder
	_, err = client.GetWatcher().Mongodb().NewMongoEntry(filter).FindMany(umw.ServiceInfo, fmt.Sprintf("order_%s", ucmd.RenameProjectDB(project)), nil, &orders)
	if err != nil {
		log.CtxLog(c).Errorf("GetSwapUserList, fail to get order info, err: %v, filter: %s", err, ucmd.ToJsonStrIgnoreErr(filter))
		return
	}
	var ridList []string
	ridOrderMap := make(map[string]mmgo.MongoOrder)
	for _, record := range orders {
		ridList = append(ridList, record.Rid)
		ridOrderMap[record.Rid] = record
	}
	if len(ridList) == 0 {
		return
	}
	serviceDO := domain_service.ServiceDO{}
	services, _, err := serviceDO.ListServices(c, domain_service.ListServiceCond{
		Project: project,
		Rids:    ridList,
	})
	if err != nil {
		log.CtxLog(c).Errorf("GetSwapUserList, fail to get service info, err: %v, rids: %s", err, ucmd.ToJsonStrIgnoreErr(ridList))
		return
	}
	batteryIds := make([]string, 0)
	vehicleIds := make([]string, 0)
	for _, si := range services {
		orderInfo, found := ridOrderMap[si.Rid]
		if !found {
			log.CtxLog(c).Warnf("GetSwapUserList, fail to find order info, si: %s", ucmd.ToJsonStrIgnoreErr(si))
			continue
		}
		swappingUser := mmgo.SwappingUser{
			ServiceId:           &si.ServiceId,
			UserArrivalTime:     orderInfo.CreationTime,
			BatterySoc:          -1,
			RealBatteryRatedKwh: -1,
		}
		if si.VehicleBatteryType != nil {
			swappingUser.RealBatteryRatedKwh = ConvertPsosBatteryType(int(*si.VehicleBatteryType))
		}
		if swappingUser.RealBatteryRatedKwh == -1 {
			batteryKWhTypes := ServiceRealBatteryList[project]
			swappingUser.RealBatteryRatedKwh = batteryKWhTypes[rand.Intn(len(batteryKWhTypes))]
		}

		swappingUser.BatteryRatedKwh = ConvertPsosRealBatteryType(swappingUser.RealBatteryRatedKwh)
		if si.VehicleBatteryId != "" {
			swappingUser.BatteryId = &si.VehicleBatteryId
			batteryIds = append(batteryIds, si.VehicleBatteryId)
		}

		if si.VehicleBatterySoc != nil {
			swappingUser.BatterySoc = int(*si.VehicleBatterySoc)
		}
		if swappingUser.BatterySoc == -1 {
			swappingUser.BatterySoc = ServiceBatterySoc
		}

		swappingTime := int(orderInfo.FinishTime - orderInfo.ParkingStartTime)
		if orderInfo.ParkingStartTime == 0 || swappingTime < 0 {
			// 没有泊车时间时，泊车默认时间(2分钟)+机械换电时长
			swappingTime = int(si.ServiceEndTime - si.ServiceStartTime + 120*time.Second.Milliseconds())
		}
		swappingTime /= 1000
		swappingUser.SwappingTime = &swappingTime

		if si.VehicleId != "" {
			swappingUser.VehicleId = &si.VehicleId
			vehicleIds = append(vehicleIds, si.VehicleId)
		}

		if si.ServiceBatteryType != nil {
			realTargetBatteryRatedKwh := ConvertPsosBatteryType(int(*si.ServiceBatteryType))
			if realTargetBatteryRatedKwh != -1 {
				swappingUser.RealTargetBatteryRatedKwh = &realTargetBatteryRatedKwh
			}
		}
		if swappingUser.RealTargetBatteryRatedKwh == nil {
			swappingUser.RealTargetBatteryRatedKwh = &swappingUser.BatteryRatedKwh
		}
		targetBatteryRatedKwh := ConvertPsosRealBatteryType(*swappingUser.RealTargetBatteryRatedKwh)
		swappingUser.TargetBatteryRatedKwh = &targetBatteryRatedKwh
		swappingUser.PackMaxTemperature = &ServiceBatteryTemperature

		deviceServiceMap[si.DeviceId][si.ServiceId] = swappingUser
	}

	// 电池产权
	batteryDO := &battery.BatteryDO{}
	batteryList, err := batteryDO.ListBattery(c, battery.ListBatteryCond{BatteryIds: batteryIds})
	if err != nil {
		log.CtxLog(c).Errorf("fail to ListBattery, err: %v, batteryIds: %s", err, ucmd.ToJsonStrIgnoreErr(batteryList))
	}
	batteryDOMap := make(map[string]battery.BatteryDO)
	for _, b := range batteryList {
		batteryDOMap[b.BatteryId] = b
	}
	// 车辆产权
	vehicleDO := &vehicle.VehicleDO{}
	vehicleList, err := vehicleDO.ListVehicle(c, vehicle.ListVehicleCond{VehicleIds: vehicleIds})
	if err != nil {
		log.CtxLog(c).Errorf("fail to ListVehicle, err: %v, vehicleIds: %s", err, ucmd.ToJsonStrIgnoreErr(vehicleList))
	}
	vehicleDOMap := make(map[string]vehicle.VehicleDO)
	for _, v := range vehicleList {
		vehicleDOMap[v.VehicleId] = v
	}

	for deviceId, srvMap := range deviceServiceMap {
		for _, srv := range srvMap {
			if srv.BatteryId != nil {
				if b, ok := batteryDOMap[*srv.BatteryId]; ok {
					ownership := convertBatteryOwnership(b.Ownership)
					restLabel := 1
					if b.NeedRest {
						restLabel = 2
					}
					srv.BatteryOwnership = &ownership
					srv.BatteryRestLabel = &restLabel
				}
			}
			if srv.VehicleId != nil {
				if v, ok := vehicleDOMap[*srv.VehicleId]; ok {
					ownership := convertVehicleOwnership(v.UserOwnership)
					srv.UserOwnership = &ownership
				}
			}
			swapUserMap[deviceId] = append(swapUserMap[deviceId], srv)
		}
	}
	return
}

// NewPsosTaskByDevice 新增psos任务【deprecated】
func (h *Handler) NewPsosTaskByDevice(c context.Context, request NewTaskByDeviceRequest, taskId string) (err error) {
	var (
		priceInfo           map[string]PriceInfo
		successMap, failMap map[string]int
		swapUserList        map[string][]mmgo.SwappingUser
		deviceParams        map[string]map[string]interface{}
	)
	g := ucmd.NewErrGroup(c)
	g.GoRecover(func() error {
		var gErr error
		priceInfo, gErr = h.GetPriceInfo(c, request.Project, request.Devices)
		if gErr != nil {
			log.CtxLog(c).Errorf("fail to GetPriceInfo, err: %v", gErr)
			return gErr
		}
		return nil
	})
	g.GoRecover(func() error {
		var gErr error
		successMap, failMap, gErr = h.GetSwapDuration(c, request.Project, request.StartTs, request.Devices)
		if gErr != nil {
			log.CtxLog(c).Errorf("fail to GetSwapDuration, err: %v", gErr)
			return gErr
		}
		return nil
	})
	g.GoRecover(func() error {
		var gErr error
		swapUserList, gErr = h.GetSwapUserList(c, request.Project, request.StartTs, request.EndTs, request.Devices)
		if gErr != nil {
			log.CtxLog(c).Errorf("fail to GetSwapUserList, err: %v", gErr)
			return gErr
		}
		return nil
	})
	g.GoRecover(func() error {
		var gErr error
		deviceParams, gErr = h.GetRedrabbitDeviceParams(c, request.Project, request.Devices)
		if gErr != nil {
			log.CtxLog(c).Errorf("fail to GetRedrabbitDeviceParams, err: %v", gErr)
			return gErr
		}
		return nil
	})
	if err = g.Wait(); err != nil {
		return
	}

	platform := "2.0"
	if request.Project == umw.PUS3 {
		platform = "3.0"
	}
	psosSimulationInfo := mmgo.PsosSimulationInfo{
		SimulationStartTime: request.StartTs,
		SimulationPeriod:    request.EndTs - request.StartTs,
		TimeStep:            10,
	}
	if request.SimulationStep != nil {
		psosSimulationInfo.TimeStep = int32(*request.SimulationStep)
	}

	psosSimulations := make([]interface{}, 0)
	simulationList := make([]string, 0)
	for _, deviceId := range request.Devices {
		batteryRealtime, gErr := h.GetBatteryRealtime(c, request.Project, deviceId, request.StartTs, request.StartTs+5*time.Minute.Milliseconds())
		if gErr != nil {
			log.CtxLog(c).Errorf("fail to GetBatteryRealtime, err: %v", gErr)
		}
		batteryInfo := make([]mmgo.PsosBatteryInfo, 0)
		for _, bi := range batteryRealtime {
			// 去除无电池的电池仓
			if bi.BatteryRatedKwh == nil {
				continue
			}
			batteryInfo = append(batteryInfo, bi)
		}
		now := time.Now()
		var ok bool
		operationStrategyInfo := mmgo.OperationStrategyInfo{}
		operationStrategyInfo.BatteryExchangeSwitch.SwitchValue = int32(util.ParseInt(deviceParams[deviceId][ParamBatteryExchangeSwitch]))
		if request.CmsSwitch != nil {
			operationStrategyInfo.CmsStrategySwitch.SwitchValue = int32(*request.CmsSwitch)
		} else {
			operationStrategyInfo.CmsStrategySwitch.SwitchValue = int32(util.ParseInt(deviceParams[deviceId][ParamCmsStrategySwitch]))
		}
		operationStrategyInfo.CmsStrategySwitch.SchedulingInterval = util.ParseInt(deviceParams[deviceId][ParamCmsFrequency])
		operationStrategyInfo.CmsStrategySwitch.StrategyValue = make([]int, 0)   // 默认值
		operationStrategyInfo.SmartChargingSwitch.StrategyValue = make([]int, 0) // 默认值
		operationStrategyInfo.ExchangeDuration = 120                             // 默认值
		operationStrategyInfo.BatteryInfo = batteryInfo
		if request.StationCapacity != nil {
			operationStrategyInfo.PowerDistributionCapacity = *request.StationCapacity
		} else {
			operationStrategyInfo.PowerDistributionCapacity = util.ParseInt(deviceParams[deviceId][ParamStationCapacity])
		}
		if request.CircuitCapacity != nil {
			operationStrategyInfo.CircuitDistributionCapacity = []int{*request.CircuitCapacity, operationStrategyInfo.PowerDistributionCapacity - *request.CircuitCapacity}
		} else {
			operationStrategyInfo.CircuitDistributionCapacity, ok = deviceParams[deviceId][ParamCircuitCapacity].([]int)
			if !ok {
				operationStrategyInfo.CircuitDistributionCapacity = CircuitDistributionCapacity[request.Project]
			}
			if operationStrategyInfo.CircuitDistributionCapacity[0]+operationStrategyInfo.CircuitDistributionCapacity[1] > operationStrategyInfo.PowerDistributionCapacity {
				operationStrategyInfo.PowerDistributionCapacity = operationStrategyInfo.CircuitDistributionCapacity[0] + operationStrategyInfo.CircuitDistributionCapacity[1]
			}
		}
		operationStrategyInfo.BranchCircuitCurrentLimit, ok = deviceParams[deviceId][ParamBranchCurrentLimit].([]int)
		if !ok {
			operationStrategyInfo.BranchCircuitCurrentLimit = BranchCircuitCurrentLimit[request.Project]
		}
		operationStrategyInfo.OperationTime = priceInfo[deviceId].OperationTime
		operationStrategyInfo.NotfullySwapSwitch.SwitchValue = 1 // 默认值
		operationStrategyInfo.SilentModeSwitch.SwitchValue = int32(util.ParseInt(deviceParams[deviceId][ParamSilentModeSwitch]))
		operationStrategyInfo.ChargingStopSoc50kWh = util.ParseInt(deviceParams[deviceId][Param50kWhChargingStopSoc])
		operationStrategyInfo.ChargingStopSoc70kWh = util.ParseInt(deviceParams[deviceId][Param70kWhChargingStopSoc])
		operationStrategyInfo.ChargingStopSoc75kWh = util.ParseInt(deviceParams[deviceId][Param75kWhChargingStopSoc])
		operationStrategyInfo.ChargingStopSoc100kWh = util.ParseInt(deviceParams[deviceId][Param100kWhChargingStopSoc])
		operationStrategyInfo.ChargingStopSoc150kWh = util.ParseInt(deviceParams[deviceId][Param150kWhChargingStopSoc])
		operationStrategyInfo.BatterySocLowerLimit = BatterySocLowerLimit[request.Project]
		operationStrategyInfo.BatterySocUpperLimit = BatterySocUpperLimit[request.Project]
		operationStrategyInfo.BatteryTypeConf = BatteryTypeConfig[request.Project]
		operationStrategyInfo.BatteryKWhGroup = SimulateBatteryList[request.Project]
		operationStrategyInfo.FullyBatteryRatio = FullyBatteryRatio[request.Project]
		chargeSwapSystem := mmgo.ChargeSwapSystem{
			CircuitNum:            CircuitNum[request.Project],
			BatterySlotNum:        BatterySlotNum[request.Project],
			ModuleSupplier:        ModuleSupplier[request.Project],
			ModuleNum:             ModuleNum[request.Project],
			PerSubmoduleNum:       PerSubmoduleNum[request.Project],
			SubmodulePowerLimit:   SubmodulePowerLimit[request.Project],
			SwapBatterySlot:       SwapBatterySlot[request.Project],
			StorableBatterySlot:   StorableBatterySlot[request.Project],
			CircuitLineAllocation: CircuitLineAllocation[request.Project],
			CoupleStructure:       CoupleStructure[request.Project],
			SwappingTimeSuccess:   successMap[deviceId],
			SwappingTimeFault:     failMap[deviceId],
		}
		psosDeviceInfo := mmgo.PsosDeviceInfo{
			Platform:              platform,
			DeviceId:              deviceId,
			OperationStrategyInfo: operationStrategyInfo,
			ChargeSwapSystem:      chargeSwapSystem,
		}
		psosServiceInfo := mmgo.PsosServiceInfo{
			SwappingUserNum:  len(swapUserList[deviceId]),
			SwappingUserList: swapUserList[deviceId],
		}
		psosScenarioInfo := mmgo.PsosScenarioInfo{
			ElectricityPriceModel: priceInfo[deviceId].ElectricityPriceModel,
			ElectricityDetails:    priceInfo[deviceId].ElectricityDetails,
		}
		configId := fmt.Sprintf("%s_%s", deviceId, xid.New().String())

		newSimulation := func(batteryConfig []float64) {
			simulationId := "sim_" + xid.New().String()
			simulationList = append(simulationList, simulationId)
			psosDeviceInfo.OperationStrategyInfo.BatteryTypeConf = batteryConfig
			simulation := mmgo.MongoPsosSimulation{
				Id:             simulationId,
				ConfigId:       configId,
				TaskId:         taskId,
				SimulationInfo: psosSimulationInfo,
				DeviceInfo:     psosDeviceInfo,
				ServiceInfo:    psosServiceInfo,
				ScenarioInfo:   psosScenarioInfo,
				Status:         SimulationStatusCreate,
				CreateTs:       now.UnixMilli(),
				UpdateTs:       now.UnixMilli(),
				Date:           now,
			}
			psosSimulations = append(psosSimulations, simulation)
		}
		// 不开启寻优
		if *request.OptimizeSwitch == 0 {
			newSimulation(GetBatteryConfig(batteryRealtime, request.Project))
		} else {
			psosDeviceInfo.OperationStrategyInfo.BatteryInfo = make([]mmgo.PsosBatteryInfo, 0)
			switch *request.OptimizeMode {
			case 1:
				for _, batteryConfig := range request.BatteryConfig {
					newSimulation(batteryConfig)
				}
			default:
				log.CtxLog(c).Errorf("invalid optimize mode: %d,", *request.OptimizeMode)
			}
		}
	}
	// todo:加索引
	err = h.Watcher.Mongodb().NewMongoEntry().InsertMany(umw.Algorithm, CollectionSimulations, psosSimulations, []client.IndexOption{
		{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 30 * 24 * 3600},
		{Name: "create_ts_status", Fields: bson.D{{"create_ts", 1}, {"status", 1}}},
		{Name: "task_id", Fields: bson.D{{"task_id", 1}}},
	}...)
	if err != nil {
		log.CtxLog(c).Errorf("fail to insert psos simulations, err: %v, simulations: %s", err, ucmd.ToJsonStrIgnoreErr(simulationList))
		return
	}

	now := time.Now()
	task := mmgo.MongoPsosTask{
		Id:             taskId,
		Name:           request.TaskName,
		Creator:        request.UserId,
		Project:        request.Project,
		SimulationList: simulationList,
		Status:         TaskStatusCreate,
		CreateTs:       now.UnixMilli(),
		UpdateTs:       now.UnixMilli(),
		Date:           now,
	}
	if request.Remark != nil {
		task.Remark = *request.Remark
	}
	err = h.Watcher.Mongodb().NewMongoEntry(bson.D{{"_id", taskId}}).InsertOne(umw.Algorithm, CollectionTasks, task, []client.IndexOption{
		{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 30 * 24 * 3600},
		{Name: "create_ts_status", Fields: bson.D{{"create_ts", 1}, {"status", 1}}},
	}...)
	if err != nil {
		log.CtxLog(c).Errorf("fail to insert psos task, err: %v, task: %s", err, ucmd.ToJsonStrIgnoreErr(task))
		return
	}
	return
}

func (h *Handler) CheckTaskName(ctx context.Context, userId, name string) (ok bool, err error) {
	filter := bson.M{"creator": userId, "name": name}
	cnt, err := client.GetWatcher().Mongodb().CountDocuments(umw.Algorithm, CollectionTasks, filter)
	if err != nil {
		return
	}
	if cnt == 0 {
		ok = true
	}
	return
}

// NewPsosTaskByConfig 新增psos任务，根据已有配方
func (h *Handler) NewPsosTaskByConfig(c context.Context, request NewTaskByConfigRequest, taskId string) (err error) {
	filter := bson.D{
		{"_id", bson.M{"$in": request.Configs}},
	}
	var configList []mmgo.MongoPsosConfig
	_, err = h.Watcher.Mongodb().NewMongoEntry(filter).FindMany(umw.Algorithm, CollectionConfigs, &options.FindOptions{}, &configList)
	if err != nil {
		log.CtxLog(c).Errorf("fail to find psos configs, err: %v, filter: %s", err, ucmd.ToJsonStrIgnoreErr(filter))
		return
	}

	psosSimulations := make([]interface{}, 0)
	simulationList := make([]string, 0)
	g := ucmd.NewErrGroup(c, 100)
	mu := sync.Mutex{}
	for _, configDetail := range configList {
		if request.SimulationStep != nil {
			configDetail.SimulationInfo.TimeStep = int32(*request.SimulationStep)
		}
		now := time.Now()
		configDO := &ConfigDO{
			StartTs: configDetail.SimulationInfo.SimulationStartTime,
			Project: request.Project,
			Rng:     &util.Rand{Rand: rand.New(rand.NewSource(time.Now().UnixNano()))},
		}
		newSimulation := func(batteryConfig []float64, isBaseConfig bool, simulationDetail mmgo.MongoPsosConfig) {
			simulationId := "sim_" + xid.New().String()
			// 随机生成battery_info
			if !isBaseConfig {
				simulationDetail.DeviceInfo.OperationStrategyInfo.BatteryTypeConf = batteryConfig
				batteryInfo, rErr := configDO.RandomBatteryInfo(c, simulationDetail.DeviceInfo.DeviceId, batteryConfig)
				if rErr != nil {
					log.CtxLog(c).Errorf("fail to get random battery info, err: %v", rErr)
				}
				simulationDetail.DeviceInfo.OperationStrategyInfo.BatteryInfo = batteryInfo
			}
			simulation := mmgo.MongoPsosSimulation{
				Id:             simulationId,
				ConfigId:       simulationDetail.Id,
				TaskId:         taskId,
				SimulationInfo: simulationDetail.SimulationInfo,
				DeviceInfo:     simulationDetail.DeviceInfo,
				ServiceInfo:    simulationDetail.ServiceInfo,
				ScenarioInfo:   simulationDetail.ScenarioInfo,
				IsBaseConfig:   isBaseConfig,
				IsRealDevice:   simulationDetail.IsRealDevice,
				Status:         SimulationStatusCreate,
				CreateTs:       now.UnixMilli(),
				UpdateTs:       now.UnixMilli(),
				Date:           now,
			}
			mu.Lock()
			defer mu.Unlock()
			simulationList = append(simulationList, simulationId)
			psosSimulations = append(psosSimulations, simulation)
		}
		newSimulation(configDetail.BaseBatteryConfig, true, configDetail)
		// 开启寻优
		if *request.OptimizeSwitch == 1 {
			switch *request.OptimizeMode {
			case 1:
				for _, batteryConfig := range request.BatteryConfig {
					g.GoRecover(func() error {
						batteryConfig = ConvertSimulateBattery2AlgorithmBatteryList(request.Project, batteryConfig)
						// 若生成的配比与基准配比相同，需要去重
						if util.CompareFloatSlice(batteryConfig, configDetail.BaseBatteryConfig) {
							return nil
						}
						newSimulation(batteryConfig, false, configDetail)
						return nil
					})
				}
			default:
				log.CtxLog(c).Errorf("invalid optimize mode: %d,", *request.OptimizeMode)
			}
		}
	}
	g.Wait()

	err = h.Watcher.Mongodb().NewMongoEntry().InsertMany(umw.Algorithm, CollectionSimulations, psosSimulations, []client.IndexOption{
		{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 30 * 24 * 3600},
		{Name: "create_ts_status", Fields: bson.D{{"create_ts", 1}, {"status", 1}}},
		{Name: "task_id", Fields: bson.D{{"task_id", 1}}},
	}...)
	if err != nil {
		log.CtxLog(c).Errorf("fail to insert psos simulations, err: %v, simulations: %s", err, ucmd.ToJsonStrIgnoreErr(simulationList))
		return
	}

	now := time.Now()
	task := mmgo.MongoPsosTask{
		Id:             taskId,
		Name:           request.TaskName,
		Creator:        request.UserId,
		Project:        request.Project,
		SimulationList: simulationList,
		Status:         TaskStatusCreate,
		CreateTs:       now.UnixMilli(),
		UpdateTs:       now.UnixMilli(),
		Date:           now,
	}
	if request.Remark != nil {
		task.Remark = *request.Remark
	}
	err = h.Watcher.Mongodb().NewMongoEntry(bson.D{{"_id", taskId}}).InsertOne(umw.Algorithm, CollectionTasks, task, []client.IndexOption{
		{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 30 * 24 * 3600},
		{Name: "create_ts_status", Fields: bson.D{{"create_ts", 1}, {"status", 1}}},
	}...)
	if err != nil {
		log.CtxLog(c).Errorf("fail to insert psos task, err: %v, task: %s", err, ucmd.ToJsonStrIgnoreErr(task))
		return
	}
	return
}

// GetBatteryConfig 获取站的不同种类的电池数量分布，算法用的顺序
func GetBatteryConfig(batteryInfo map[int]mmgo.PsosBatteryInfo, project string) []float64 {
	// [50, 75, 100, 150, 60, 85]
	res := make([]float64, 6)
	for _, bf := range batteryInfo {
		if bf.BatteryRatedKwh == nil {
			continue
		}
		switch *bf.BatteryRatedKwh {
		case common.CapacityValue50kwh:
			res[0] += 1
		case common.CapacityValue75kwh:
			res[1] += 1
		case common.CapacityValue100kwh:
			res[2] += 1
		case common.CapacityValue150kwh:
			res[3] += 1
		case common.CapacityValue60kwh:
			res[4] += 1
		case common.CapacityValue85kwh:
			res[5] += 1
		default:
		}
	}
	if project == umw.PowerSwap2 {
		return res[:4]
	}
	return res
}
