package psos

import (
	"context"
	"encoding/json"
	"fmt"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	"git.nevint.com/welkin2/welkin-backend/client"
	mongo_model "git.nevint.com/welkin2/welkin-backend/model/mongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	// 任务状态
	TaskStatusCreate = "create"
	TaskStatusRun    = "run"
	TaskStatusFinish = "finish"
	TaskStatusStop   = "stop"
)

type TaskDO struct {
	Id                     string
	Name                   string
	Status                 string
	Creator                string
	Remark                 string
	Project                string
	Simulations            map[string][]*SimulationDO // key: status value:simulations
	CreateSimulationCount  int64
	RunSimulationCount     int64
	SuccessSimulationCount int64
	FailSimulationCount    int64
	FmsCompressTaskInfo    mongo_model.FmsCompressTaskInfo
	CreateTimestamp        int64
	UpdateTimestamp        int64
}

// list筛选条件
type ListTaskQueryCond struct {
	TaskId         string
	Name           string
	Status         []string
	Creator        string
	OrderFieldName string
	Descending     bool
	Limit          int64
	Offset         int64
}

func (t *TaskDO) ListTasks(ctx context.Context, cond ListTaskQueryCond) ([]*TaskDO, int64, error) {
	filter := bson.D{}
	if cond.TaskId != "" {
		filter = append(filter, bson.E{Key: "_id", Value: cond.TaskId})
	}
	if cond.Name != "" {
		filter = append(filter, bson.E{Key: "name", Value: bson.M{"$regex": fmt.Sprintf(".*%v.*", cond.Name)}})
	}
	if len(cond.Status) != 0 {
		filter = append(filter, bson.E{Key: "status", Value: bson.M{"$in": cond.Status}})
	}
	if cond.Creator != "" {
		filter = append(filter, bson.E{Key: "creator", Value: cond.Creator})
	}
	if cond.OrderFieldName == "" {
		cond.OrderFieldName = "create_ts"
	}
	taskByteData, total, err := client.GetWatcher().Mongodb().NewMongoEntry(filter).ListByPagination(umw.Algorithm, CollectionTasks,
		client.Pagination{Limit: cond.Limit, Offset: cond.Offset},
		client.Ordered{Key: cond.OrderFieldName, Descending: cond.Descending})
	if err != nil {
		return nil, 0, err
	}
	var taskPOs []mongo_model.MongoPsosTask
	if err = json.Unmarshal(taskByteData, &taskPOs); err != nil {
		return nil, 0, err
	}
	tasks := []*TaskDO{}
	for _, taskPO := range taskPOs {
		tasks = append(tasks, ConvertTaskPO2DO(ctx, &taskPO))
	}
	err = t.loadSimulationCount(ctx, tasks)
	if err != nil {
		return nil, 0, err
	}
	return tasks, total, nil
}

func (t *TaskDO) GetTaskById(ctx context.Context, id string) (*TaskDO, error) {
	filter := bson.D{bson.E{Key: "_id", Value: id}}
	taskByteData, err := client.GetWatcher().Mongodb().NewMongoEntry(filter).GetOne(umw.Algorithm, CollectionTasks)
	if err != nil {
		return nil, err
	}
	var psosTask mongo_model.MongoPsosTask
	if err = bson.Unmarshal(taskByteData, &psosTask); err != nil {
		return nil, err
	}
	return ConvertTaskPO2DO(ctx, &psosTask), nil
}

func (t *TaskDO) StopTask(ctx context.Context) error {
	err := client.GetWatcher().Mongodb().NewMongoEntry(bson.D{bson.E{Key: "_id", Value: t.Id}, bson.E{Key: "status", Value: TaskStatusRun}}).
		UpdateOne(umw.Algorithm, CollectionTasks, bson.M{"$set": bson.M{"status": TaskStatusStop}},
			false)
	return err
}

func (t *TaskDO) loadFmsCompressStatus(ctx context.Context, tasks []*TaskDO) error {
	fmsTaskIds := []string{}
	for _, task := range tasks {
		if task.FmsCompressTaskInfo.ServiceCompressTaskId != "" {
			fmsTaskIds = append(fmsTaskIds, task.FmsCompressTaskInfo.ServiceCompressTaskId)
		}
		if task.FmsCompressTaskInfo.BatteryCompressTaskId != "" {
			fmsTaskIds = append(fmsTaskIds, task.FmsCompressTaskInfo.BatteryCompressTaskId)
		}
		if task.FmsCompressTaskInfo.DeviceCompressTaskId != "" {
			fmsTaskIds = append(fmsTaskIds, task.FmsCompressTaskInfo.DeviceCompressTaskId)
		}
	}
	fliter := bson.D{bson.E{Key: "task_id", Value: bson.M{"$in": fmsTaskIds}}}
	allBytedata, err := client.GetWatcher().Mongodb().NewMongoEntry(fliter).ListAll(mongo_model.FmsDataBaseName, mongo_model.FmsTaskStatusCollectionName, client.Ordered{})
	if err != nil {
		return err
	}
	var fmsCompressTaskInfo []mongo_model.FmsTaskStatus
	err = json.Unmarshal(allBytedata, &fmsCompressTaskInfo)
	if err != nil {
		return err
	}
	fmsTaskIdStatusMap := map[string]string{}
	for _, compressTaskInfo := range fmsCompressTaskInfo {
		fmsTaskIdStatusMap[compressTaskInfo.TaskId] = compressTaskInfo.Status
	}
	//for _, task := range tasks {
	//	//DeviceCompressStatus := ""
	//	//BatteryCompressStatus := ""
	//	//ServiceCompressStatus := ""
	//	status, found := fmsTaskIdStatusMap[task.FmsCompressTaskInfo.DeviceCompressTaskId]
	//	if found {
	//		DeviceCompressStatus = status
	//	}
	//	status, found = fmsTaskIdStatusMap[task.FmsCompressTaskInfo.BatteryCompressTaskId]
	//	if found {
	//		BatteryCompressStatus = status
	//	}
	//	status, found = fmsTaskIdStatusMap[task.FmsCompressTaskInfo.ServiceCompressTaskId]
	//	if found {
	//		ServiceCompressStatus = status
	//	}
	//	//task.FmsCompressTaskInfo.DeviceCompressStatus = DeviceCompressStatus
	//	//task.FmsCompressTaskInfo.BatteryCompressStatus = BatteryCompressStatus
	//	//task.FmsCompressTaskInfo.ServiceCompressStatus = ServiceCompressStatus
	//}
	return nil
}

func (t *TaskDO) loadSimulationCount(ctx context.Context, tasks []*TaskDO) error {
	taskIds := []string{}
	for _, task := range tasks {
		taskIds = append(taskIds, task.Id)
	}
	cursor, err := client.GetWatcher().Mongodb().Client.Database(umw.Algorithm).Collection(CollectionSimulations).Aggregate(ctx, mongo.Pipeline{
		bson.D{{"$match", bson.D{bson.E{Key: "task_id", Value: bson.M{"$in": taskIds}}}}},
		bson.D{{"$group", bson.M{
			"_id":               "$task_id",
			"simulation_status": bson.M{"$push": "$status"},
		}}},
		bson.D{{"$project", bson.M{
			"task_id":           "$_id",
			"simulation_status": 1,
		}}},
	})
	if err != nil {
		return err
	}
	var aggreValues []struct {
		TaskId           string   `json:"task_id" bson:"task_id"`
		SimulationStatus []string `json:"simulation_status" bson:"simulation_status"`
	}
	if err = cursor.All(ctx, &aggreValues); err != nil {
		return err
	}
	type StatusStat struct {
		CreateCount  int64
		RunCount     int64
		SuccessCount int64
		FailCount    int64
	}
	taskIdStatusMap := map[string]*StatusStat{}
	for _, value := range aggreValues {
		if taskIdStatusMap[value.TaskId] == nil {
			taskIdStatusMap[value.TaskId] = &StatusStat{}
		}
		for _, status := range value.SimulationStatus {
			if status == SimulationStatusCreate {
				taskIdStatusMap[value.TaskId].CreateCount++
			} else if status == SimulationStatusRun {
				taskIdStatusMap[value.TaskId].RunCount++
			} else if status == SimulationStatusSuccess {
				taskIdStatusMap[value.TaskId].SuccessCount++
			} else if status == SimulationStatusFail {
				taskIdStatusMap[value.TaskId].FailCount++
			}
		}
	}

	for _, task := range tasks {
		_, found := taskIdStatusMap[task.Id]
		if !found {
			continue
		}
		task.CreateSimulationCount = taskIdStatusMap[task.Id].CreateCount
		task.RunSimulationCount = taskIdStatusMap[task.Id].RunCount
		task.SuccessSimulationCount = taskIdStatusMap[task.Id].SuccessCount
		task.FailSimulationCount = taskIdStatusMap[task.Id].FailCount
	}
	return nil
}

func (t *TaskDO) LoadAllSimulation(ctx context.Context) error {
	filter := bson.D{bson.E{Key: "task_id", Value: t.Id}}
	byteData, err := client.GetWatcher().Mongodb().NewMongoEntry(filter).ListAll(umw.Algorithm, CollectionSimulations, client.Ordered{})
	if err != nil {
		return err
	}
	var simulationPOs []mongo_model.MongoPsosSimulation
	if err = json.Unmarshal(byteData, &simulationPOs); err != nil {
		return err
	}
	simulations := []*SimulationDO{}
	for _, simulationPO := range simulationPOs {
		simulations = append(simulations, ConvertSimulationPO2DO(ctx, &simulationPO))
	}

	t.Simulations = map[string][]*SimulationDO{
		SimulationStatusCreate:  {},
		SimulationStatusSuccess: {},
		SimulationStatusRun:     {},
		SimulationStatusFail:    {},
	}
	for _, simulation := range simulations {
		t.Simulations[simulation.Status] = append(t.Simulations[simulation.Status], simulation)
	}
	return nil
}
