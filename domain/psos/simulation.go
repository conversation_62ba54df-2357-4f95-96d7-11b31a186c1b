package psos

import (
	"archive/zip"
	"bytes"
	"context"
	"encoding/csv"
	"encoding/json"
	"errors"
	"fmt"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/logger"
	mongo_model "git.nevint.com/welkin2/welkin-backend/model/mongo"
	"git.nevint.com/welkin2/welkin-backend/util"
	"go.mongodb.org/mongo-driver/bson"
	"io"
	"net/http"
	"strconv"
	"time"
)

const (

	// 仿真状态
	SimulationStatusCreate  = "create"
	SimulationStatusRun     = "run"
	SimulationStatusSuccess = "success"
	SimulationStatusFail    = "fail"
)

type SimulationDO struct {
	Id                  string
	TaskId              string
	ConfigId            string
	Status              string
	IsBaseConfig        bool
	IsRealDevice        bool
	DeviceResultUrl     string
	BatteryResultUrl    string
	ServiceResultUrl    string
	BusinessCalculation mongo_model.BusinessCalculation
	DevicePerformance   mongo_model.DevicePerformance
	UserExperience      mongo_model.UserExperience
	SimulationInfo      mongo_model.PsosSimulationInfo
	DeviceInfo          mongo_model.PsosDeviceInfo
	ServiceInfo         mongo_model.PsosServiceInfo
	ScenarioInfo        mongo_model.PsosScenarioInfo
	ProgressRate        float64 // 运行进度
	StartRunTimestamp   int64   // 仿真开始时间
	CreateTimestamp     int64
	UpdateTimestamp     int64
}

const (
	SimulationSortFieldBatteryChargingTime    = "avg_battery_charging_time"
	SimulationSortFieldCapacityUtilization    = "avg_capacity_utilization"
	SimulationSortFieldSwappingQueueTime      = "avg_swapping_queue_time"
	SimulationSortFieldBatteryElectricityCost = "battery_electricity_cost"
)

var SimulationSortField = map[string]string{
	SimulationSortFieldBatteryChargingTime:    "business_calculation.cost.avg_battery_chargingtime",
	SimulationSortFieldCapacityUtilization:    "device_performance.power.capacity_factor",
	SimulationSortFieldSwappingQueueTime:      "user_experience.swap_service.avg_swapping_queuetime",
	SimulationSortFieldBatteryElectricityCost: "business_calculation.cost.battery_electricity_cost",
}

// list筛选条件
type ListSimulationQueryCond struct {
	SimulationId   string
	TaskId         string
	ConfigId       string
	OrderFieldName string
	Descending     bool
	Limit          int64
	Offset         int64
}

func (s *SimulationDO) ListSimulation(ctx context.Context, cond ListSimulationQueryCond) ([]*SimulationDO, int64, error) {
	filter := bson.D{}
	if cond.SimulationId != "" {
		filter = append(filter, bson.E{Key: "_id", Value: cond.SimulationId})
	}
	if cond.TaskId != "" {
		filter = append(filter, bson.E{Key: "task_id", Value: cond.TaskId})
	}
	if cond.ConfigId != "" {
		filter = append(filter, bson.E{Key: "config_id", Value: cond.ConfigId})
	}
	sortFieldName := "create_ts"
	if cond.OrderFieldName != "" {
		sortFieldName = cond.OrderFieldName
		v, found := SimulationSortField[cond.OrderFieldName]
		if found {
			sortFieldName = v
		}
	}
	simulationByteData, total, err := client.GetWatcher().Mongodb().NewMongoEntry(filter).ListByPagination(umw.Algorithm, CollectionSimulations,
		client.Pagination{Limit: cond.Limit, Offset: cond.Offset},
		client.Ordered{Key: sortFieldName, Descending: cond.Descending})
	if err != nil {
		return nil, 0, err
	}
	var simulationPOs []mongo_model.MongoPsosSimulation
	if err = json.Unmarshal(simulationByteData, &simulationPOs); err != nil {
		return nil, 0, err
	}
	simulations := []*SimulationDO{}
	for _, simulationPO := range simulationPOs {
		simulations = append(simulations, ConvertSimulationPO2DO(ctx, &simulationPO))
	}
	return simulations, total, nil
}

func (s *SimulationDO) GetSimulationById(ctx context.Context, simulationId string) (*SimulationDO, error) {
	bsonByteData, err := client.GetWatcher().Mongodb().NewMongoEntry(bson.D{bson.E{Key: "_id", Value: simulationId}}).GetOne(umw.Algorithm, CollectionSimulations)
	if err != nil {
		return nil, err
	}
	var simulationPO mongo_model.MongoPsosSimulation
	err = bson.Unmarshal(bsonByteData, &simulationPO)
	if err != nil {
		return nil, err
	}
	return ConvertSimulationPO2DO(ctx, &simulationPO), nil
}

// 暂时不需要解析，后期可能会用上
func (s *SimulationDO) GenReport(ctx context.Context) error {
	if s.Status != SimulationStatusSuccess {
		return errors.New("simulation not finish can not generate report")
	}
	resp, err := http.Get(s.ServiceResultUrl)
	if err != nil {
		return err
	}
	type Stat struct {
		ServiceCount int64
		WaitingTime  float64

		PowerTotal          float64
		PowerOccupied       float64
		DeviceRealtimeCount int64

		ChargeCunt int64
	}
	hourlyStat := []*Stat{}
	for i := 0; i < 24; i++ {
		hourlyStat = append(hourlyStat, &Stat{})
	}
	csvReader := csv.NewReader(resp.Body)
	idx := 0
	for {
		idx++
		// 读取一行
		record, err := csvReader.Read()
		if err == io.EOF {
			// 到达文件末尾，退出循环
			break
		}
		if err != nil {
			logger.Logger.Errorf("")
		}

		// 跳过表头
		if idx == 1 {
			continue
		}
		// 打印行数据
		fmt.Println(record)
		serviceStatrtTimeStr := record[8]
		queueTimeStr := record[10]
		serviceStatrtTime, err := time.ParseInLocation("2006-01-02 15:04:05", serviceStatrtTimeStr, util.GetTimeLoc())
		if err != nil {
			logger.Logger.Errorf("time.ParseInLocation err. serviceStatrtTimeStr:%v err:%v", serviceStatrtTimeStr, err)
			continue
		}
		queueTime, err := strconv.ParseFloat(queueTimeStr, 64)
		if err != nil {
			logger.Logger.Errorf("strconv.ParseFloat err. queueTimeStr:%v err:%v", queueTime, err)
			continue
		}

		hourlyStat[serviceStatrtTime.Hour()].ServiceCount++
		hourlyStat[serviceStatrtTime.Hour()].WaitingTime = hourlyStat[serviceStatrtTime.Hour()].WaitingTime + queueTime
	}

	resp, err = http.Get(s.DeviceResultUrl)
	if err != nil {
		return err
	}
	bodyBates, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	zipReader, err := zip.NewReader(bytes.NewReader(bodyBates), int64(len(bodyBates)))
	if err != nil {
		return err
	}
	for _, f := range zipReader.File {
		fmt.Printf("Found file in zip: %s\n", f.Name)
		if f.Name == "device_realtime_data.csv" {
			reader, err := f.Open()
			if err != nil {
				return err
			}
			csvReader = csv.NewReader(reader)
			idx := 0
			for {
				idx++
				// 读取一行
				record, err := csvReader.Read()
				if err == io.EOF {
					// 到达文件末尾，退出循环
					break
				}
				if err != nil {
					logger.Logger.Errorf("")
				}

				// 跳过表头
				if idx == 1 {
					continue
				}
				// 打印行数据
				fmt.Println(record)
				hourStr := record[4]
				occupPowerStr := record[7]
				idlePowerStr := record[8]
				hour, err := strconv.ParseInt(hourStr, 10, 64)
				if err != nil {
					continue
				}
				occupPower, err := strconv.ParseFloat(occupPowerStr, 64)
				if err != nil {
					continue
				}
				idlePower, err := strconv.ParseFloat(idlePowerStr, 64)
				if err != nil {
					continue
				}
				hourlyStat[int(hour)].PowerTotal = occupPower + idlePower
				hourlyStat[int(hour)].PowerOccupied = hourlyStat[int(hour)].PowerOccupied + occupPower
				hourlyStat[int(hour)].DeviceRealtimeCount++
			}
		}
	}

	resp, err = http.Get(s.BatteryResultUrl)
	if err != nil {
		return err
	}
	bodyBates, err = io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	zipReader, err = zip.NewReader(bytes.NewReader(bodyBates), int64(len(bodyBates)))
	if err != nil {
		return err
	}
	for _, f := range zipReader.File {
		fmt.Printf("Found file in zip: %s\n", f.Name)
		if f.Name == "battery_behavior_data.csv" {
			reader, err := f.Open()
			if err != nil {
				return err
			}
			csvReader = csv.NewReader(reader)
			idx := 0
			for {
				idx++
				// 读取一行
				record, err := csvReader.Read()
				if err == io.EOF {
					// 到达文件末尾，退出循环
					break
				}
				if err != nil {
					logger.Logger.Errorf("")
				}

				// 跳过表头
				if idx == 1 {
					continue
				}
				// 打印行数据
				fmt.Println(record)
				chargeStartTimeStr := record[8]
				if chargeStartTimeStr == "" {
					continue
				}
				chargeStartTime, err := time.ParseInLocation("2006-01-02 15:04:05", chargeStartTimeStr, util.GetTimeLoc())
				if err != nil {
					logger.Logger.Errorf("time.ParseInLocation err. chargeStartTimeStr:%v err:%v", chargeStartTimeStr, err)
					continue
				}
				hourlyStat[chargeStartTime.Hour()].ChargeCunt++
			}
		}
	}

	return nil
}
