package realtime

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
)

const (
	// 值守状态
	OnDutyStatusUnknown    = 0 // 未知
	OnDutyStatusAttended   = 1 // 有人值守
	OnDutyStatusUnAttended = 2 // 无人值守
)

// OnDutyStatusDataId 值守状态
var OnDutyStatusDataId = map[string]string{
	umw.PowerSwap2: "282",
	umw.PUS3:       "10",
	umw.PUS4:       "4004",
}

type RealtimeDO struct {
	Data map[string]any
}

type ListRealtimeCond struct {
	model.CommonCond
	Project   string
	DeviceId  string
	DataIds   string
	StartTime int64
	EndTime   int64
}

func (r *RealtimeDO) ListRealtime(ctx context.Context, cond ListRealtimeCond) (res []RealtimeDO, total int64, err error) {
	if cond.Project == "" || cond.DeviceId == "" || cond.DataIds == "" {
		err = fmt.Errorf("ListRealtime, empty cond: %s", ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	getRealtimeData := func(dbName, dataIds string, getAll bool) ([]map[string]interface{}, int64, error) {
		tdw := &client.TDWatcher{
			TDClient:     client.GetWatcher().TDEngine(),
			RedisClient:  client.GetWatcher().Redis(),
			DeviceId:     cond.DeviceId,
			StartTs:      &cond.StartTime,
			EndTs:        &cond.EndTime,
			Descending:   cond.Descending,
			FilterFields: make([]string, 0),
			Logger:       log.CtxLog(ctx).Named("TDEngine"),
		}
		if !getAll && tdw.Offset != 0 && tdw.Limit != 0 {
			tdw.Offset = int((cond.Page - 1) * cond.Size)
			tdw.Limit = int(cond.Size)
		}
		stableName := fmt.Sprintf("realtime_%s", strings.ToLower(cond.Project))
		scanStruct, dataIdMap, gErr := tdw.GetRealtimeFields(dbName, stableName, dataIds, true)
		if gErr != nil {
			log.CtxLog(ctx).Errorf("get realtime fields fail, err: %v", gErr)
			return nil, 0, gErr
		}
		// fmt.Printf("data id map: %s\n", ucmd.ToJsonStrIgnoreErr(dataIdMap))
		partTotal, rows, gErr := tdw.FilterDataByFields(dbName)
		if gErr != nil {
			log.CtxLog(ctx).Errorf("get realtime data by fields fail: %v, err: %v", tdw.FilterFields, gErr)
			return nil, 0, gErr
		}

		results := make([]map[string]interface{}, 0)
		if rows != nil {
			for rows.Next() {
				// 获取动态的查询结构体
				columns := client.ReflectFields(scanStruct)
				if gErr = rows.Scan(columns...); gErr != nil {
					log.CtxLog(ctx).Errorf("scan realtime data from tdengine fail, err: %v", gErr)
					continue
				}
				result := map[string]interface{}{
					"timestamp": scanStruct.FieldByName("Ts").Interface().(time.Time).UnixMilli(),
				}
				for dataId := range dataIdMap {
					result[dataId] = scanStruct.FieldByName("R" + dataId).Interface().(model.SafeType).GetRealValue()
				}
				results = append(results, result)
			}
		}
		return results, partTotal, nil
	}
	var ossDataId, welkinDataId []string
	for _, id := range strings.Split(cond.DataIds, ",") {
		if _, ok := cache.OSSRealtimeData[cond.Project][id]; ok {
			ossDataId = append(ossDataId, id)
			continue
		}
		if _, ok := cache.WelkinRealtimeData[cond.Project][id]; ok {
			welkinDataId = append(welkinDataId, id)
			continue
		}
	}
	// 表示是否需要同时查oss realtime和welkin realtime，若要同时查询，则必须全部查出来之后在内存中排序
	getAll := true
	if len(ossDataId) == 0 || len(welkinDataId) == 0 {
		getAll = false
	}
	// fmt.Println("getAll: ", getAll)
	if len(ossDataId) != 0 {
		dbName := "device2oss_realtime"
		var records []map[string]interface{}
		var partTotal int64
		records, partTotal, err = getRealtimeData(dbName, strings.Join(ossDataId, ","), getAll)
		if err != nil {
			log.CtxLog(ctx).Errorf("fail to get oss realtime data, err: %v", err)
			return
		}
		for _, record := range records {
			res = append(res, RealtimeDO{Data: record})
		}
		total += partTotal
	}
	if len(welkinDataId) != 0 {
		dbName := "device2welkin_realtime"
		var records []map[string]interface{}
		var partTotal int64
		records, partTotal, err = getRealtimeData(dbName, strings.Join(welkinDataId, ","), getAll)
		if err != nil {
			log.CtxLog(ctx).Errorf("fail to get welkin realtime data, err: %v", err)
			return
		}
		for _, record := range records {
			res = append(res, RealtimeDO{Data: record})
		}
		total += partTotal
	}
	if getAll {
		sort.Slice(res, func(i, j int) bool {
			ts1, ok := res[i].Data["timestamp"].(int64)
			if !ok {
				return false
			}
			ts2, ok := res[j].Data["timestamp"].(int64)
			if !ok {
				return false
			}
			return ts1 > ts2
		})
		startIndex, endIndex := int((cond.Page-1)*cond.Size), int(cond.Page*cond.Size)
		if startIndex >= len(res) {
			errMsg := fmt.Sprintf("startIndex %d too large, page: %d, size: %d", startIndex, cond.Page, cond.Size)
			log.CtxLog(ctx).Error(errMsg)
			return
		}
		if endIndex > len(res) {
			endIndex = len(res)
		}
		res = res[startIndex:endIndex]
	}
	return
}

// GenerateRealtimeCsv 生成实时数据下载的csv
func (r *RealtimeDO) GenerateRealtimeCsv(ctx context.Context, project string, dataIds []string, realtimeData []RealtimeDO) (records [][]string, err error) {
	headersRaw := []string{"timestamp"}
	headersRaw = append(headersRaw, dataIds...)
	headers := make([]string, len(headersRaw))
	headers[0] = "时间"
	for i := 1; i < len(headers); i++ {
		if tag, ok := cache.OSSRealtimeData[project][headersRaw[i]]; ok {
			headers[i] = tag.VarCnName
			continue
		}
		if tag, ok := cache.WelkinRealtimeData[project][headersRaw[i]]; ok {
			headers[i] = tag.VarCnName
			continue
		}
	}
	records = make([][]string, len(realtimeData)+1)
	for i := range records {
		records[i] = make([]string, len(headersRaw))
	}
	records[0] = headers
	for i, result := range realtimeData {
		for j, header := range headersRaw {
			var record string
			resultMap := result.Data
			if header == "timestamp" {
				ts, ok := resultMap[header].(int64)
				if !ok {
					log.CtxLog(ctx).Errorf("expect ts to be int64, get value: %v, type: %T", resultMap[header], resultMap[header])
					break
				}
				record = time.UnixMilli(ts).Format("2006-01-02 15:04:05.000")
			} else {
				record = fmt.Sprintf("%v", resultMap[header])
			}
			records[i+1][j] = record
		}
	}
	return
}
