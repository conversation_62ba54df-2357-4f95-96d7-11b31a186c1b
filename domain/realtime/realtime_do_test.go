package realtime

import (
	"fmt"
	"net/http"
	"os"
	"testing"

	"github.com/gin-gonic/gin"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	ulog "git.nevint.com/golang-libs/common-utils/logger"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
)

var w client.Watcher
var ctx *gin.Context

func init() {
	os.Setenv("APOLLO_META", "https://apollo-config-cn-stg.nioint.com")
	os.Setenv("ENV", "stg")
	cfg := &ucfg.Config{}
	ucfg.GetConfigApollo("config.json", true, cfg, ucfg.NeedValidate(true), ucfg.ServiceName("ppd-welkin-backend"), ucfg.Cluster(ucfg.GetWelkinApolloCluster()))
	defer ulog.Final()
	cfg.Log.DirPath = "/tmp/logs"
	config.Cfg = cfg
	log.Init(cfg.Log, true)
	logger := log.Logger.Named("activity")
	w = client.NewWatcherByParam(config.Cfg, logger)
	var err error
	if err != nil {
		panic(err)
	}
	cache.CacheRealtimeDataID(w.Mongodb().Client)
	model.InitConstant(cfg.ExtraConfig["evBrand"])
	ctx = &gin.Context{Request: &http.Request{Header: make(http.Header)}}
	ctx.Request.Header.Set("X-Request-ID", "test")
}

func TestRealtimeDO_ListRealtime(t *testing.T) {
	r := &RealtimeDO{}
	res, total, err := r.ListRealtime(ctx, ListRealtimeCond{
		CommonCond: model.CommonCond{
			Page:       1,
			Size:       10,
			Descending: true,
		},
		Project:   umw.PUS4,
		DeviceId:  "PUS-NIO-095fdc2a-5840a9b6",
		DataIds:   "54002,54004,54005,54006,54007,54008,54009",
		StartTime: 1733446508839,
		EndTime:   1733450108839,
	})
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(umw.PUS4, total, ucmd.ToJsonStrIgnoreErr(res))

	res, total, err = r.ListRealtime(ctx, ListRealtimeCond{
		CommonCond: model.CommonCond{
			Page:       1,
			Size:       10,
			Descending: true,
		},
		Project:   umw.PowerSwap2,
		DeviceId:  "PS-NIO-31da205c-64ba4b36",
		DataIds:   "605199,513108,605152,513105,513134,605200,502108,605141,502105,502134",
		StartTime: 1733446508839,
		EndTime:   1733450108839,
	})
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(umw.PowerSwap2, total, ucmd.ToJsonStrIgnoreErr(res))

	res, total, err = r.ListRealtime(ctx, ListRealtimeCond{
		CommonCond: model.CommonCond{
			Page:       1,
			Size:       10,
			Descending: true,
		},
		Project:   umw.PUS3,
		DeviceId:  "PS-NIO-3285ff15-7f564f27",
		DataIds:   "104104,104105,104123,104127,104128,104125,104013,104011,104122,104126,104124,104014,104121,104012,104023,104024,104144,104147,104148",
		StartTime: 1733446508839,
		EndTime:   1733450108839,
	})
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(umw.PUS3, total, ucmd.ToJsonStrIgnoreErr(res))
}
