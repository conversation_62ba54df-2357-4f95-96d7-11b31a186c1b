package energy

import (
	"fmt"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	gocache "github.com/patrickmn/go-cache"
	"github.com/stretchr/testify/assert"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	ulog "git.nevint.com/golang-libs/common-utils/logger"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
)

var w client.Watcher
var ctx *gin.Context

func init() {
	os.Setenv("APOLLO_META", "https://apollo-config-cn-stg.nioint.com")
	os.Setenv("ENV", "stg")
	cfg := &ucfg.Config{}
	ucfg.GetConfigApollo("config.json", true, cfg, ucfg.NeedValidate(true), ucfg.ServiceName("ppd-welkin-backend"), ucfg.Cluster(ucfg.GetWelkinApolloCluster()))
	defer ulog.Final()
	cfg.Log.DirPath = "/tmp/logs"
	config.Cfg = cfg
	log.Init(cfg.Log, true)
	logger := log.Logger.Named("activity")
	w = client.NewWatcherByParam(config.Cfg, logger)
	var err error
	if err != nil {
		panic(err)
	}
	cache.CacheRealtimeDataID(w.Mongodb().Client)
	cache.PowerSwapCache = &cache.DeviceInfoCache{
		Cache:         gocache.New(gocache.NoExpiration, gocache.NoExpiration),
		ResourceCache: gocache.New(gocache.NoExpiration, gocache.NoExpiration),
	}
	cache.PowerSwapCache.RefreshDeviceInfoCache(w.Mongodb().Client)
	model.InitConstant(cfg.ExtraConfig["evBrand"])
	ctx = &gin.Context{Request: &http.Request{Header: make(http.Header)}}
	ctx.Request.Header.Set("X-Request-ID", "test")
}

func TestAggregateEnergyByProject(t *testing.T) {
	cond := AggregateEnergyCond{
		StartTime: time.Now().AddDate(0, 0, -14).UnixMilli(),
		EndTime:   time.Now().UnixMilli(),
		Project:   umw.PUS3,
		//DeviceId:    "PS-NIO-00a6f660-cfebfb86",
		CityCompany: "上海公司",
	}
	e := &EnergyDO{}
	res, err := e.AggregateEnergy(ctx, cond, "project")
	assert.NoError(t, err)
	assert.NotNil(t, res)
	fmt.Println(len(res), ucmd.ToJsonStrIgnoreErr(res))

	res, err = e.AggregateEnergy(ctx, cond, "device_id")
	assert.NoError(t, err)
	assert.NotNil(t, res)
	fmt.Println(len(res), ucmd.ToJsonStrIgnoreErr(res))

	res, err = e.AggregateEnergy(ctx, cond, "day")
	assert.NoError(t, err)
	assert.NotNil(t, res)
	fmt.Println(len(res), ucmd.ToJsonStrIgnoreErr(res))
}

func TestEnergyDO_ListEnergy(t *testing.T) {
	t1, _ := time.ParseInLocation(time.DateOnly, "2025-01-08", time.Local)
	t2, _ := time.ParseInLocation(time.DateOnly, "2025-01-09", time.Local)
	cond := ListEnergyCond{
		CommonCond: model.CommonCond{
			Page: 1,
			Size: 10,
		},
		StartTime:   t1.UnixMilli(),
		EndTime:     t2.UnixMilli(),
		Project:     umw.PUS3,
		CityCompany: "上海公司",
		DeviceId:    "PS-NIO-027919ba-20345dac",
	}
	fmt.Println("cond:", ucmd.ToJsonStrIgnoreErr(cond))
	e := &EnergyDO{}
	res, total, err := e.ListEnergy(ctx, cond)
	assert.NoError(t, err)
	assert.NotNil(t, res)
	fmt.Println(total, ucmd.ToJsonStrIgnoreErr(res))
}

func TestEnergyRankDO_GetEnergyRank(t *testing.T) {
	t1, _ := time.ParseInLocation(time.DateOnly, "2025-01-08", time.Local)
	temperature := 5.279056
	serviceCnt := 14
	cond := GetEnergyRankCond{
		Ts:           t1.UnixMilli(),
		Project:      umw.PUS3,
		ServiceCount: &serviceCnt,
		Temperature:  &temperature,
	}
	e := &EnergyRankDO{}
	res, err := e.GetEnergyRank(ctx, cond)
	assert.NoError(t, err)
	fmt.Println(ucmd.ToJsonStrIgnoreErr(res))
}
