package energy

import (
	"context"
	"errors"
	"fmt"
	"git.nevint.com/welkin2/welkin-backend/util"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	"git.nevint.com/welkin2/welkin-backend/client"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"
)

type EnergyDO struct {
	Day                     int64
	DeviceId                string
	Project                 string
	CityCompany             string
	Efficiency              *float64
	ChargingEnergyAvailable *float64
	ModuleOutputEnergyTotal *float64
	ServiceCount            *int
	TotalConsumption        *float64
	ChargeConsumption       *float64
	WaterConsumption        *float64
	OperationConsumption    *float64
	MechanicalConsumption   *float64
	LightConsumption        *float64
	UpsConsumption          *float64
	CoolingConsumption      *float64
	NonChargeConsumption    *float64
	ChargingEnergy          *float64
	ChargingPileEnergy      *float64
	RankLevelFinal          *string
	HighTemperature         *float64
}

type ListEnergyCond struct {
	model.CommonCond
	StartTime   int64
	EndTime     int64
	Project     string
	CityCompany string
	DeviceId    string
}

func (e *EnergyDO) ListEnergy(ctx context.Context, cond ListEnergyCond) (res []EnergyDO, total int64, err error) {
	if cond.StartTime == 0 || cond.EndTime == 0 {
		err = fmt.Errorf("start time and end time are required")
		log.CtxLog(ctx).Errorf("ListEnergy, %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	filter := bson.D{}
	if cond.StartTime != 0 && cond.EndTime != 0 {
		filter = append(filter, bson.E{Key: "day", Value: bson.M{"$gte": cond.StartTime, "$lte": cond.EndTime}})
	}
	if cond.DeviceId != "" {
		filter = append(filter, bson.E{Key: "device_id", Value: cond.DeviceId})
	}
	if cond.Project != "" {
		filter = append(filter, bson.E{Key: "project", Value: cond.Project})
	}
	if cond.CityCompany != "" {
		filter = append(filter, bson.E{Key: "city_company", Value: cond.CityCompany})
	}
	if len(filter) == 0 {
		err = fmt.Errorf("must provide at least one filter field")
		log.CtxLog(ctx).Errorf("ListEnergy, %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	opts := options.Find().SetSort(bson.D{{"day", -1}, {"device_id", 1}})
	if cond.Page != 0 && cond.Size != 0 {
		opts = opts.SetSkip((cond.Page - 1) * cond.Size).SetLimit(cond.Size)
	}
	var energyInfos []mmgo.DeviceModelEnergy
	total, err = client.GetWatcher().PLCMongodb().NewMongoEntry(filter).FindMany(mmgo.DBDeviceModel, mmgo.CollectionEnergy, opts, &energyInfos)
	if err != nil {
		log.CtxLog(ctx).Errorf("ListEnergy, fail to get energy: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	for _, record := range energyInfos {
		res = append(res, convertEnergyPO2DO(record))
	}
	return
}

type GetEnergyCond struct {
	Day      int64
	Project  string
	DeviceId string
}

func (e *EnergyDO) GetEnergy(ctx context.Context, cond GetEnergyCond) (res *EnergyDO, err error) {
	filter := bson.D{
		{"day", cond.Day},
		{"project", cond.Project},
		{"device_id", cond.DeviceId},
	}
	var energyInfo mmgo.DeviceModelEnergy
	err = client.GetWatcher().PLCMongodb().NewMongoEntry(filter).FindOne(mmgo.DBDeviceModel, mmgo.CollectionEnergy, options.FindOne(), &energyInfo)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			log.CtxLog(ctx).Warnf("GetEnergy, no energy found, cond: %s", ucmd.ToJsonStrIgnoreErr(cond))
			return nil, nil
		}
		log.CtxLog(ctx).Errorf("GetEnergy, fail to get energy: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	result := convertEnergyPO2DO(energyInfo)
	return &result, nil
}

type AggregateEnergyCond struct {
	StartTime   int64
	EndTime     int64
	Project     string
	CityCompany string
	DeviceId    string
}

type EnergyOverview struct {
	TotalConsumption float64
	TotalCharge      float64
	Efficiency       float64
	ServiceCount     int
	Day              *int64
	Project          *string
	DeviceId         *string
}

// AggregateEnergy 聚合能效数据，groupBy为project或device_id或day
func (e *EnergyDO) AggregateEnergy(ctx context.Context, cond AggregateEnergyCond, groupBy string) (res []EnergyOverview, err error) {
	res = make([]EnergyOverview, 0)
	filter := bson.M{
		"day":        bson.M{"$gte": cond.StartTime, "$lte": cond.EndTime},
		"efficiency": bson.M{"$ne": nil},
	}
	if cond.Project != "" {
		filter["project"] = cond.Project
	}
	if cond.CityCompany != "" {
		filter["city_company"] = cond.CityCompany
	}
	if cond.DeviceId != "" {
		filter["device_id"] = cond.DeviceId
	}
	pipeline := mongo.Pipeline{
		{{"$match", filter}},
		{{"$group", bson.M{
			"_id":               fmt.Sprintf("$%s", groupBy),
			"total_consumption": bson.M{"$sum": "$total_consumption"},
			"total_charge":      bson.M{"$sum": "$charging_energy_available"},
			"service_count":     bson.M{"$sum": "$service_count"},
		}}},
		{{"$addFields", bson.M{
			"efficiency": bson.M{
				"$cond": bson.A{
					bson.M{"$or": bson.A{
						bson.M{"$eq": bson.A{"$total_consumption", 0}},
						bson.M{"$eq": bson.A{"$total_consumption", nil}},
					}},
					0.0,
					bson.M{"$divide": bson.A{"$total_charge", "$total_consumption"}},
				},
			},
		}}},
	}
	var results []bson.M
	err = client.GetWatcher().PLCMongodb().NewMongoEntry().Aggregate(mmgo.DBDeviceModel, mmgo.CollectionEnergy, pipeline, &results)
	if err != nil {
		log.CtxLog(ctx).Errorf("AggregateEnergy, fail to aggregate energy: %v, pipeline: %s", err, ucmd.ToJsonStrIgnoreErr(pipeline))
		return
	}
	for _, record := range results {
		item := EnergyOverview{
			TotalConsumption: util.ParseFloat(record["total_consumption"]),
			TotalCharge:      util.ParseFloat(record["total_charge"]),
			Efficiency:       util.ParseFloat(record["efficiency"]),
			ServiceCount:     util.ParseInt(record["service_count"]),
		}
		if groupBy == "project" {
			project := record["_id"].(string)
			item.Project = &project
		} else if groupBy == "device_id" {
			deviceId := record["_id"].(string)
			item.DeviceId = &deviceId
		} else if groupBy == "day" {
			day := record["_id"].(int64)
			item.Day = &day
		} else {
			err = fmt.Errorf("invalid groupBy field: %s", groupBy)
			log.CtxLog(ctx).Errorf("AggregateEnergy, %v", err)
			return
		}
		res = append(res, item)
	}
	return
}

func (e *EnergyDO) GetEnergyUpdateTime(ctx context.Context, startTime, endTime int64) (updateTime int64, err error) {
	opt := options.FindOne().SetSort(bson.M{"day": -1})
	var res mmgo.DeviceModelEnergy
	err = client.GetWatcher().PLCMongodb().NewMongoEntry(bson.D{{"day", bson.M{"$gte": startTime, "$lte": endTime}}}).FindOne(mmgo.DBDeviceModel, mmgo.CollectionEnergy, opt, &res)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return 0, nil
		}
		log.CtxLog(ctx).Errorf("GetEnergyUpdateTime, fail to get energy: %v", err)
		return
	}
	updateTime = res.Day
	return
}

type EnergyRankDO struct {
	Month               string
	Project             string
	ServiceCntInterval  string
	TemperatureInterval string
	ServiceCntLower     float64
	ServiceCntUpper     float64
	TemperatureLower    float64
	TemperatureUpper    float64
	Ees01               *float64
	Ees02               *float64
	Ees03               *float64
	Ees04               *float64
	Oes01               *float64
	Oes02               *float64
	Oes03               *float64
	Oes04               *float64
	Wes01               *float64
	Wes02               *float64
	Wes03               *float64
	Wes04               *float64
	HighTempBias        *float64
	LowTempBias         *float64
}

type GetEnergyRankCond struct {
	Ts           int64
	Project      string
	ServiceCount *int
	Temperature  *float64
}

// GetEnergyRank 获取能效评级
func (er *EnergyRankDO) GetEnergyRank(ctx context.Context, cond GetEnergyRankCond) (res EnergyRankDO, err error) {
	month := time.UnixMilli(cond.Ts).Format("200601")
	if cond.ServiceCount == nil || cond.Temperature == nil {
		log.CtxLog(ctx).Warnf("GetEnergyRank, nil service_count or temperature, cond: %s", ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	filter := bson.D{
		{"month", month},
		{"project", cond.Project},
		{"service_cnt_lower", bson.M{"$lt": *cond.ServiceCount}},
		{"service_cnt_upper", bson.M{"$gte": *cond.ServiceCount}},
		{"temperature_lower", bson.M{"$lt": *cond.Temperature}},
		{"temperature_upper", bson.M{"$gte": *cond.Temperature}},
	}
	var energyRanks mmgo.DeviceModelEnergyRank
	err = client.GetWatcher().PLCMongodb().NewMongoEntry(filter).FindOne(mmgo.DBDeviceModel, mmgo.CollectionEnergyRank, options.FindOne(), &energyRanks)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			log.CtxLog(ctx).Warnf("GetEnergyRank, no energy rank found, cond: %s", ucmd.ToJsonStrIgnoreErr(cond))
			return res, nil
		}
		log.CtxLog(ctx).Errorf("GetEnergyRank, fail to get energy rank: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	res = convertEnergyRankPO2DO(energyRanks)
	return res, nil
}
