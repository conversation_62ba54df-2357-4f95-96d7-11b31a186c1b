package energy

import (
	mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"
)

func convertEnergyPO2DO(po mmgo.DeviceModelEnergy) EnergyDO {
	do := EnergyDO{
		Day:                     po.Day,
		DeviceId:                po.DeviceId,
		Project:                 po.Project,
		CityCompany:             po.CityCompany,
		Efficiency:              po.Efficiency,
		ChargingEnergyAvailable: po.ChargingEnergyAvailable,
		ModuleOutputEnergyTotal: po.ModuleOutputEnergyTotal,
		ServiceCount:            po.ServiceCount,
		TotalConsumption:        po.TotalConsumption,
		ChargeConsumption:       po.ChargeConsumption,
		WaterConsumption:        po.WaterConsumption,
		OperationConsumption:    po.OperationConsumption,
		MechanicalConsumption:   po.MechanicalConsumption,
		LightConsumption:        po.LightConsumption,
		UpsConsumption:          po.UpsConsumption,
		CoolingConsumption:      po.CoolingConsumption,
		ChargingEnergy:          po.ChargingEnergy,
		ChargingPileEnergy:      po.ChargingPileEnergy,
		RankLevelFinal:          po.RankLevelFinal,
		HighTemperature:         po.HighTemperature,
	}
	nonChargeConsumption := 0.0
	if po.TotalConsumption != nil {
		nonChargeConsumption = *po.TotalConsumption
	}
	if po.ChargeConsumption != nil {
		nonChargeConsumption -= *po.ChargeConsumption
	}
	do.NonChargeConsumption = &nonChargeConsumption
	return do
}

func convertEnergyRankPO2DO(po mmgo.DeviceModelEnergyRank) EnergyRankDO {
	do := EnergyRankDO{
		Month:               po.Month,
		Project:             po.Project,
		ServiceCntInterval:  po.ServiceCntInterval,
		TemperatureInterval: po.TemperatureInterval,
		ServiceCntLower:     po.ServiceCntLower,
		ServiceCntUpper:     po.ServiceCntUpper,
		TemperatureLower:    po.TemperatureLower,
		TemperatureUpper:    po.TemperatureUpper,
		Ees01:               po.Ees01,
		Ees02:               po.Ees02,
		Ees03:               po.Ees03,
		Ees04:               po.Ees04,
		Oes01:               po.Oes01,
		Oes02:               po.Oes02,
		Oes03:               po.Oes03,
		Oes04:               po.Oes04,
		Wes01:               po.Wes01,
		Wes02:               po.Wes02,
		Wes03:               po.Wes03,
		Wes04:               po.Wes04,
		HighTempBias:        po.HighTempBias,
		LowTempBias:         po.LowTempBias,
	}
	return do
}
