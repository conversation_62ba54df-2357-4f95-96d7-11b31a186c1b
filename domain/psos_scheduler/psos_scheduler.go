package psos_scheduler

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"net/http"
	"strings"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"go.mongodb.org/mongo-driver/bson"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	"git.nevint.com/welkin2/welkin-backend/domain/psos"
	"git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	"git.nevint.com/welkin2/welkin-backend/model/mongo"
	"git.nevint.com/welkin2/welkin-backend/service"
)

const (
	MaxParallelNum    = 10
	SimulationTimeOut = time.Second * 180
)

type PsosSchedulerDO struct {
	Watcher client.Watcher
	Fms     service.FMS
}

type SimulationConfig struct {
	TaskId         string                   `json:"task_id"`
	SimulationId   string                   `json:"simulation_id"`
	Ignore         bool                     `json:"ignore"`
	DeviceInfo     mongo.PsosDeviceInfo     `json:"device_info"`
	ServiceInfo    mongo.PsosServiceInfo    `json:"service_info"`
	ScenarioInfo   mongo.PsosScenarioInfo   `json:"scenario_info"`
	SimulationInfo mongo.PsosSimulationInfo `json:"simulation_info"`
}

func (p *PsosSchedulerDO) StartTask(ctx context.Context, task *psos.TaskDO) error {
	err := p.Watcher.Mongodb().NewMongoEntry(bson.D{bson.E{Key: "_id", Value: task.Id}, bson.E{Key: "status", Value: psos.TaskStatusCreate}}).
		UpdateOne(umw.Algorithm, psos.CollectionTasks, bson.M{"$set": bson.M{"status": psos.TaskStatusRun}},
			false)
	if err != nil {
		return err
	}

	// 调度
	load, err := p.checkLoad(ctx)
	if err != nil {
		return err
	}
	needExcuteCount := MaxParallelNum - math.Ceil(MaxParallelNum*load)
	if needExcuteCount <= 0 || needExcuteCount > MaxParallelNum {
		return nil
	}
	needRunSimulationByteData, _, err := p.Watcher.Mongodb().NewMongoEntry(bson.D{bson.E{Key: "task_id", Value: bson.M{"$in": []string{task.Id}}}, bson.E{Key: "status", Value: psos.SimulationStatusCreate}}).
		ListByPagination(umw.Algorithm, psos.CollectionSimulations,
			client.Pagination{Limit: int64(needExcuteCount), Offset: int64(0)},
			client.Ordered{Key: "start_running_ts", Descending: false})
	if err != nil {
		return err
	}
	var needRunPsosSimulations []mongo.MongoPsosSimulation
	if err = json.Unmarshal(needRunSimulationByteData, &needRunPsosSimulations); err != nil {
		return err
	}
	for _, simulationPO := range needRunPsosSimulations {
		simulation := psos.ConvertSimulationPO2DO(ctx, &simulationPO)
		go func(v *psos.SimulationDO) {
			defer ucmd.RecoverPanic()
			err := p.ExecuteTask(ctx, v)
			if err != nil {
				p.simulationStatusTransfer(ctx, v.TaskId, v.Id, psos.SimulationStatusRun, psos.SimulationStatusFail)
				logger.Logger.Errorf("psos SimulationDO excute fail. task id:%v simulation id:%v err:%v", v.TaskId, v.Id, err)
			}
		}(simulation)
	}
	return nil
}

func (p *PsosSchedulerDO) ExecuteTask(ctx context.Context, simulation *psos.SimulationDO) error {
	start := time.Now()
	// 检查task是否在运行状态
	taskByteData, err := p.Watcher.Mongodb().NewMongoEntry(bson.D{bson.E{Key: "_id", Value: simulation.TaskId}}).GetOne(umw.Algorithm, psos.CollectionTasks)
	if err != nil {
		return err
	}
	if taskByteData == nil {
		return errors.New("no running task")
	}
	var psosTask mongo.MongoPsosTask
	if err = bson.Unmarshal(taskByteData, &psosTask); err != nil {
		return err
	}
	if psosTask.Status != psos.TaskStatusRun {
		return errors.New(fmt.Sprintf("task not in running status, not allow to run simulation. task id:%v simulation id:%v", simulation.TaskId, simulation.Id))
	}

	// 状态流转
	nowTs := time.Now().UnixMilli()
	err = p.Watcher.Mongodb().NewMongoEntry(bson.D{bson.E{Key: "_id", Value: simulation.Id}, bson.E{Key: "status", Value: psos.SimulationStatusCreate}}).UpdateOne("algorithm", psos.CollectionSimulations, bson.M{"$set": bson.M{
		"status":           psos.SimulationStatusRun,
		"start_running_ts": nowTs,
		"update_ts":        nowTs,
	}}, false)
	p.asyncTaskUpdateTime(ctx, simulation.TaskId, nowTs)
	if err != nil {
		return err
	}
	// 传给算法的时间戳用秒 🤷🏻‍
	simulation.SimulationInfo.SimulationStartTime = simulation.SimulationInfo.SimulationStartTime / 1000
	simulation.SimulationInfo.SimulationPeriod = simulation.SimulationInfo.SimulationPeriod / 1000
	for i, _ := range simulation.ServiceInfo.SwappingUserList {
		simulation.ServiceInfo.SwappingUserList[i].UserArrivalTime = simulation.ServiceInfo.SwappingUserList[i].UserArrivalTime / 1000
	}
	simulationConfig := SimulationConfig{
		TaskId:         simulation.TaskId,
		SimulationId:   simulation.Id,
		DeviceInfo:     simulation.DeviceInfo,
		ServiceInfo:    simulation.ServiceInfo,
		ScenarioInfo:   simulation.ScenarioInfo,
		SimulationInfo: simulation.SimulationInfo,
	}

	jsonData, err := json.Marshal(simulationConfig)
	if err != nil {
		return err
	}
	resp, err := http.Post(fmt.Sprintf("%v/psos", config.Cfg.Welkin.PsosUrl), "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("http request psos fail, err: %v", err)
	}
	if resp.StatusCode != http.StatusOK {
		logger.Logger.Errorf("http request psos fail status code:%v resp:%v", resp.StatusCode, ucmd.ToJsonStrIgnoreErr(resp))
		return fmt.Errorf("http request psos fail status code:%v", resp.StatusCode)
	}
	defer resp.Body.Close()
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("io.ReadAll, err: %v", err)
	}
	var psosResp PsosResponse
	if err = json.Unmarshal(data, &psosResp); err != nil {
		logger.Logger.Error("fail to unmarshal psos response, err: %v, body: %s", err, string(data))
		return fmt.Errorf("fail to unmarshal psos response, err: %v, body: %s", err, string(data))
	}
	if psosResp.ErrCode != 0 {
		return fmt.Errorf("resp err code not equal 0 message:%v request data:%v", psosResp.Message, ucmd.ToJsonStrIgnoreErr(simulationConfig))
	}

	batteryDataByte, err := base64.StdEncoding.DecodeString(psosResp.SimulationData.BatteryData)
	if err != nil {
		return err
	}
	nowTimeStamp := time.Now().UnixMilli()
	batteryDataURL, err := p.store2FMS(ctx, batteryDataByte, fmt.Sprintf("%v_battery_%v.zip", simulation.Id, nowTimeStamp), simulation)
	if err != nil {
		return err
	}
	deviceDataByte, err := base64.StdEncoding.DecodeString(psosResp.SimulationData.DeviceData)
	if err != nil {
		return err
	}
	deviceDataURL, err := p.store2FMS(ctx, deviceDataByte, fmt.Sprintf("%v_device_%v.zip", simulation.Id, nowTimeStamp), simulation)
	if err != nil {
		return err
	}
	serviceDataByte, err := base64.StdEncoding.DecodeString(psosResp.SimulationData.ServiceData)
	if err != nil {
		return err
	}
	serviceDataURL, err := p.store2FMS(ctx, serviceDataByte, fmt.Sprintf("%v_service_%v.csv", simulation.Id, nowTimeStamp), simulation)
	if err != nil {
		return err
	}

	businessCalculation := mongo.BusinessCalculation{
		MarginalContributionMargin: psosResp.AggregateMetrics.BusinessCalculation.MarginalContributionMargin,
		Cost: mongo.PsosCost{
			BatteryElectricityCost:       psosResp.AggregateMetrics.BusinessCalculation.Cost.BatteryElectricityCost,
			AvgBatteryChargingtime:       psosResp.AggregateMetrics.BusinessCalculation.Cost.AvgBatteryChargingtime,
			HourlyAvgBatteryChargingtime: psosResp.AggregateMetrics.BusinessCalculation.Cost.HourlyAvgBatteryChargingtime,
			HourlyBatteryChargingNums:    psosResp.AggregateMetrics.BusinessCalculation.Cost.HourlyBatteryChargingNums,
			HourlyBatteryElectricityCost: psosResp.AggregateMetrics.BusinessCalculation.Cost.HourlyBatteryElectricityCost,
			TotalCost:                    psosResp.AggregateMetrics.BusinessCalculation.Cost.TotalCost,
			NonChargingElectricityCost:   psosResp.AggregateMetrics.BusinessCalculation.Cost.NonChargingElectricityCost,
		},
		Income: mongo.PsosIncome{
			TotalIncome:       psosResp.AggregateMetrics.BusinessCalculation.Income.TotalIncome,
			SwapServiceIncome: psosResp.AggregateMetrics.BusinessCalculation.Income.SwapServiceIncome,
			OffPeakIncome:     psosResp.AggregateMetrics.BusinessCalculation.Income.OffPeakIncome,
		},
	}
	devicePerformance := mongo.DevicePerformance{
		Module: mongo.PsosModule{
			ModuleNums:                   psosResp.AggregateMetrics.DevicePerformance.Module.ModuleNums,
			ModuleUtilizationRatio:       psosResp.AggregateMetrics.DevicePerformance.Module.ModuleUtilizationRatio,
			SingleModuleActualWorkTime:   psosResp.AggregateMetrics.DevicePerformance.Module.SingleModuleActualWorkTime,
			SingleModulePowerLimit:       psosResp.AggregateMetrics.DevicePerformance.Module.SingleModulePowerLimit,
			SingleModuleRatedWorkTime:    psosResp.AggregateMetrics.DevicePerformance.Module.SingleModuleRatedWorkTime,
			HourlyModuleUtilizationRatio: psosResp.AggregateMetrics.DevicePerformance.Module.HourlyModuleUtilizationRatio,
		},
		Power: mongo.PsosPower{
			AvgPower:             psosResp.AggregateMetrics.DevicePerformance.Power.AvgPower,
			CapacityFactor:       psosResp.AggregateMetrics.DevicePerformance.Power.CapacityFactor,
			HourlyCapacityFactor: psosResp.AggregateMetrics.DevicePerformance.Power.HourlyCapacityFactor,
			HourlyLoadFactor:     psosResp.AggregateMetrics.DevicePerformance.Power.HourlyLoadFactor,
			LoadFactor:           psosResp.AggregateMetrics.DevicePerformance.Power.LoadFactor,
			MaxPower:             psosResp.AggregateMetrics.DevicePerformance.Power.MaxPower,
			RatedPower:           psosResp.AggregateMetrics.DevicePerformance.Power.RatedPower,
		},
		EnergyEfficiency: mongo.EnergyEfficiency{
			EnergyEfficiency:                  psosResp.AggregateMetrics.DevicePerformance.EnergyEfficiency.EnergyEfficiency,
			DeviceElectricityConsumption:      psosResp.AggregateMetrics.DevicePerformance.EnergyEfficiency.DeviceElectricityConsumption,
			BatteryElectricityConsumption:     psosResp.AggregateMetrics.DevicePerformance.EnergyEfficiency.BatteryElectricityConsumption,
			ModuleElectricityConsumption:      psosResp.AggregateMetrics.DevicePerformance.EnergyEfficiency.ModuleElectricityConsumption,
			NonChargingElectricityConsumption: psosResp.AggregateMetrics.DevicePerformance.EnergyEfficiency.NonChargingElectricityConsumption,
			SctElectricityConsumption:         psosResp.AggregateMetrics.DevicePerformance.EnergyEfficiency.SctElectricityConsumption,
		},
	}
	userExperience := mongo.UserExperience{
		SwapService: mongo.PsosSwapService{
			SwappingQueuetime:           psosResp.AggregateMetrics.UserExperience.SwapService.SwappingQueuetime,
			SwappingQueuetimeBattery:    psosResp.AggregateMetrics.UserExperience.SwapService.SwappingQueuetimeBattery,
			SwappingQueuetimeUser:       psosResp.AggregateMetrics.UserExperience.SwapService.SwappingQueuetimeUser,
			AvgSwappingQueuetime:        psosResp.AggregateMetrics.UserExperience.SwapService.AvgSwappingQueuetime,
			AvgSwappingQueuetimeBattery: psosResp.AggregateMetrics.UserExperience.SwapService.AvgSwappingQueuetimeBattery,
			AvgSwappingQueuetimeUser:    psosResp.AggregateMetrics.UserExperience.SwapService.AvgSwappingQueuetimeUser,
			HourlyAvgSwappingQueuetime:  psosResp.AggregateMetrics.UserExperience.SwapService.HourlyAvgSwappingQueuetime,
			HourlySwappingServiceNums:   psosResp.AggregateMetrics.UserExperience.SwapService.HourlySwappingServiceNums,
			SwappingServiceNums:         psosResp.AggregateMetrics.UserExperience.SwapService.SwappingServiceNums,
			SwappedServiceNums:          psosResp.AggregateMetrics.UserExperience.SwapService.SwappedServiceNums,
		},
	}

	//update result to db 更新simulation表 以及判断task表是否需要更新为完成
	nowTs = time.Now().UnixMilli()
	err = p.Watcher.Mongodb().NewMongoEntry(bson.D{bson.E{Key: "_id", Value: simulation.Id}, bson.E{Key: "status", Value: psos.SimulationStatusRun}}).UpdateOne(umw.Algorithm, psos.CollectionSimulations, bson.M{"$set": bson.M{
		"status":               psos.SimulationStatusSuccess,
		"finish_ts":            nowTs,
		"business_calculation": businessCalculation,
		"device_performance":   devicePerformance,
		"user_experience":      userExperience,
		"device_result_url":    deviceDataURL,
		"battery_result_url":   batteryDataURL,
		"service_result_url":   serviceDataURL,
		"update_ts":            nowTs,
	}}, false)
	p.asyncTaskUpdateTime(ctx, simulation.TaskId, nowTs)
	if err != nil {
		return err
	}
	// 检查Simulation是否都成功跑完
	totalCount, err := p.Watcher.Mongodb().NewMongoEntry(bson.D{bson.E{Key: "task_id", Value: simulation.TaskId}}).Count(umw.Algorithm, psos.CollectionSimulations)
	if err != nil {
		return err
	}
	successCount, err := p.Watcher.Mongodb().NewMongoEntry(bson.D{bson.E{Key: "task_id", Value: simulation.TaskId},
		bson.E{Key: "status", Value: psos.SimulationStatusSuccess}}).Count(umw.Algorithm, psos.CollectionSimulations)
	if err != nil {
		return err
	}
	if totalCount == successCount {
		err = p.Watcher.Mongodb().NewMongoEntry(bson.D{bson.E{Key: "_id", Value: simulation.TaskId}, bson.E{Key: "status", Value: psos.TaskStatusRun}}).UpdateOne(umw.Algorithm, psos.CollectionTasks, bson.M{"$set": bson.M{
			"status":    psos.TaskStatusFinish,
			"update_ts": time.Now().UnixMilli(),
		}}, false)
		if err != nil {
			return err
		}
		err = p.sendFilesCompress(ctx, simulation.TaskId)
		if err != nil {
			return err
		}
	}
	_, _ = http.Post(fmt.Sprintf("%v/algorithm/v1/psos/watch_dog", config.Cfg.Welkin.BackendUrl), "application/json", bytes.NewBuffer([]byte{}))
	client.GetPrometheus().MetricCollectors["psos_simulation_time_cost"].(*prometheus.SummaryVec).WithLabelValues(simulation.TaskId).Observe(float64(time.Since(start).Milliseconds()))
	return nil
}

func (p *PsosSchedulerDO) simulationStatusTransfer(ctx context.Context, taskId, simulationId, fromStatus, toStautus string) error {
	nowTs := time.Now().UnixMilli()
	p.asyncTaskUpdateTime(ctx, taskId, nowTs)
	return p.Watcher.Mongodb().NewMongoEntry(bson.D{bson.E{Key: "_id", Value: simulationId}, bson.E{Key: "status", Value: fromStatus}}).UpdateOne(umw.Algorithm, psos.CollectionSimulations, bson.M{"$set": bson.M{
		"status":    toStautus,
		"update_ts": nowTs,
	}}, false)
}

// asyncTaskUpdateTime 异步更新任务更新时间，用于仿真状态变更的同时也变更任务变更时间
func (p *PsosSchedulerDO) asyncTaskUpdateTime(ctx context.Context, taskId string, ts int64) {
	go func() {
		filter := bson.D{bson.E{Key: "_id", Value: taskId}}
		err := client.GetWatcher().Mongodb().NewMongoEntry(filter).UpdateOne(umw.Algorithm, psos.CollectionTasks, bson.M{"$set": bson.M{
			"update_ts": ts,
		}}, false)
		if err != nil {
			logger.Logger.Errorln("asyncTaskUpdateTime err. err:%v", err)
		}
	}()
}

func (p *PsosSchedulerDO) store2FMS(ctx context.Context, data []byte, fileName string, simulation *psos.SimulationDO) (string, error) {
	buffer := bytes.NewBuffer(data)
	fmsFileDir := fmt.Sprintf(fmt.Sprintf("/algorithm/psos/%v/", simulation.TaskId))
	tokenRes, tokenErr := p.Fms.GetFileUploadToken(fmsFileDir, fileName, model.NeedPublic, buffer.String(), ucmd.GetArea())
	if tokenErr != nil {
		return "", tokenErr
	}
	rd := tokenRes.ResultData
	rd.SupplierHttp.Header["Content-Type"] = "application/octet-stream"
	if err := p.Fms.UploadFile(rd.SupplierHttp.URL, rd.SupplierHttp.Header, buffer); err != nil {
		return "", err
	}
	fileURL := ""
	for _, item := range rd.DomainInfoList {
		if item.DomainAttr.CDN {
			fileURL = fmt.Sprintf("%s://%s/%s", item.Protocol, item.Domain, rd.FileKey)
			break
		}
	}
	if fileURL == "" {
		return "", errors.New("gen empty file url")
	}
	return fileURL, nil
}

type PsosResp struct {
	MODELVERSION     string `json:"MODEL_VERSION"`
	ErrCode          int    `json:"err_code"`
	Message          string `json:"message"`
	ModelTriggerTime string `json:"model_trigger_time"`
	RequestId        string `json:"request_id"`
	AggregateMetrics struct {
		AvgBatteryChargingtime       float64 `json:"avg_battery_chargingtime"`
		AvgSwappingQueuetime         float64 `json:"avg_swapping_queuetime"`
		HourlyAvgCapacityUtilization float64 `json:"hourly_avg_capacity_utilization"`
	} `json:"aggregate_metrics"`
	SimulationData struct {
		BatteryData string `json:"battery_data"`
		DeviceData  string `json:"device_data"`
		ServiceData string `json:"service_data"`
	} `json:"simulation_data"`
}

type PsosResponse struct {
	MODELVERSION   string `json:"MODEL_VERSION"`
	SimulationData struct {
		BatteryData string `json:"battery_data"`
		DeviceData  string `json:"device_data"`
		ServiceData string `json:"service_data"`
	} `json:"simulation_data"`
	AggregateMetrics struct {
		BusinessCalculation struct {
			MarginalContributionMargin float64 `json:"marginal_contribution_margin"`
			Cost                       struct {
				BatteryElectricityCost       float64   `json:"battery_electricity_cost"`
				AvgBatteryChargingtime       float64   `json:"avg_battery_chargingtime"`
				HourlyAvgBatteryChargingtime []float64 `json:"hourly_avg_battery_chargingtime"`
				HourlyBatteryChargingNums    []float64 `json:"hourly_battery_charging_nums"`
				HourlyBatteryElectricityCost []float64 `json:"hourly_battery_electricity_cost"`
				TotalCost                    float64   `json:"total_cost"`
				NonChargingElectricityCost   float64   `json:"non_charging_electricity_cost"`
			} `json:"cost"`
			Income struct {
				TotalIncome       float64 `json:"total_income"`
				SwapServiceIncome float64 `json:"swap_service_income"`
				OffPeakIncome     float64 `json:"Off_peak_income"`
			} `json:"income"`
		} `json:"business_calculation"`
		DevicePerformance struct {
			EnergyEfficiency struct {
				EnergyEfficiency                  float64 `json:"energy_efficiency"`
				DeviceElectricityConsumption      float64 `json:"device_electricity_consumption"`
				BatteryElectricityConsumption     float64 `json:"battery_electricity_consumption"`
				ModuleElectricityConsumption      float64 `json:"module_electricity_consumption"`
				NonChargingElectricityConsumption float64 `json:"non_charging_electricity_consumption"`
				SctElectricityConsumption         float64 `json:"sct_electricity_consumption"`
			} `json:"energy_efficiency"`
			Module struct {
				ModuleNums                   float64   `json:"module_nums"`
				ModuleUtilizationRatio       float64   `json:"module_utilization_ratio"`
				SingleModuleActualWorkTime   float64   `json:"single_module_actual_work_time"`
				SingleModulePowerLimit       float64   `json:"single_module_power_limit"`
				SingleModuleRatedWorkTime    float64   `json:"single_module_rated_work_time"`
				HourlyModuleUtilizationRatio []float64 `json:"hourly_module_utilization_ratio"`
			} `json:"module"`
			Power struct {
				AvgPower             float64   `json:"avg_power"`
				CapacityFactor       float64   `json:"capacity_factor"`
				HourlyCapacityFactor []float64 `json:"hourly_capacity_factor"`
				HourlyLoadFactor     []float64 `json:"hourly_load_factor"`
				LoadFactor           float64   `json:"load_factor"`
				MaxPower             float64   `json:"max_power"`
				RatedPower           float64   `json:"rated_power"`
			} `json:"power"`
		} `json:"device_performance"`
		UserExperience struct {
			SctService  interface{} `json:"sct_service"`
			SwapService struct {
				SwappingQueuetime           float64   `json:"90_swapping_queuetime"`
				SwappingQueuetimeBattery    float64   `json:"90_swapping_queuetime_battery"`
				SwappingQueuetimeUser       float64   `json:"90_swapping_queuetime_user"`
				AvgSwappingQueuetime        float64   `json:"avg_swapping_queuetime"`
				AvgSwappingQueuetimeBattery float64   `json:"avg_swapping_queuetime_battery"`
				AvgSwappingQueuetimeUser    float64   `json:"avg_swapping_queuetime_user"`
				HourlyAvgSwappingQueuetime  []float64 `json:"hourly_avg_swapping_queuetime"`
				HourlySwappingServiceNums   []float64 `json:"hourly_swapping_service_nums"`
				SwappingServiceNums         float64   `json:"swapping_service_nums"`
				SwappedServiceNums          float64   `json:"swapped_service_nums"`
			} `json:"swap_service"`
		} `json:"user_experience"`
	} `json:"aggregate_metrics"`
	ErrCode          int    `json:"err_code"`
	Message          string `json:"message"`
	ModelTriggerTime string `json:"model_trigger_time"`
	SimulationId     string `json:"simulation_id"`
	TaskId           string `json:"task_id"`
}

func (p *PsosSchedulerDO) ProcessHeartbeat(ctx context.Context, simulationId string, progress float64) error {
	err := p.Watcher.Mongodb().NewMongoEntry(bson.D{bson.E{Key: "_id", Value: simulationId}}).UpdateOne(umw.Algorithm, psos.CollectionSimulations, bson.M{"$set": bson.M{
		"progress":  progress,
		"update_ts": time.Now().UnixMilli(),
	}}, false)
	if err != nil {
		return err
	}
	return nil
}

func (p *PsosSchedulerDO) checkLoad(ctx context.Context) (float64, error) {
	filter := bson.D{bson.E{Key: "status", Value: psos.SimulationStatusRun}}
	value, err := p.Watcher.Mongodb().NewMongoEntry(filter).Count(umw.Algorithm, psos.CollectionSimulations)
	if err != nil {
		return 0, err
	}
	if MaxParallelNum <= 0 {
		return 100, err
	}
	return float64(value) / float64(MaxParallelNum), nil
}

// 处理超时Simulation
// 处理fail的simulation 并重新调度simulation成运行
// 更新监控数据
func (p *PsosSchedulerDO) WatchDog(ctx context.Context) error {
	runSimulationBytedate, err := p.Watcher.Mongodb().NewMongoEntry(bson.D{bson.E{Key: "status", Value: psos.SimulationStatusRun}}).ListAll(umw.Algorithm, psos.CollectionSimulations, client.Ordered{Key: "start_running_ts", Descending: false})
	if err != nil {
		return err
	}
	var mongoRunPsosSimulations []mongo.MongoPsosSimulation
	if err = json.Unmarshal(runSimulationBytedate, &mongoRunPsosSimulations); err != nil {
		return err
	}
	runningSimulationCount := len(mongoRunPsosSimulations)

	failSimulationBytedate, err := p.Watcher.Mongodb().NewMongoEntry(bson.D{bson.E{Key: "status", Value: psos.SimulationStatusFail}}).ListAll(umw.Algorithm, psos.CollectionSimulations, client.Ordered{Key: "create_ts", Descending: false})
	if err != nil {
		return err
	}
	var mongoFailPsosSimulations []mongo.MongoPsosSimulation
	if err = json.Unmarshal(failSimulationBytedate, &mongoFailPsosSimulations); err != nil {
		return err
	}
	failSimulationCount := len(mongoFailPsosSimulations)

	runTaskBytedate, err := p.Watcher.Mongodb().NewMongoEntry(bson.D{bson.E{Key: "status", Value: psos.SimulationStatusRun}}).ListAll(umw.Algorithm, psos.CollectionTasks, client.Ordered{Key: "create_ts", Descending: false})
	if err != nil {
		return err
	}
	var runTasks []mongo.MongoPsosTask
	runingTaskIds := []string{}
	if err = json.Unmarshal(runTaskBytedate, &runTasks); err != nil {
		return err
	}
	for _, runTask := range runTasks {
		runingTaskIds = append(runingTaskIds, runTask.Id)
	}
	runningTaskCount := len(runTasks)

	// 打点监控统计
	client.GetPrometheus().MetricCollectors["psos_statistic_count"].(*prometheus.GaugeVec).WithLabelValues("running_simulation_count").Set(float64(runningSimulationCount))
	client.GetPrometheus().MetricCollectors["psos_statistic_count"].(*prometheus.GaugeVec).WithLabelValues("fail_simulation_count").Set(float64(failSimulationCount))
	client.GetPrometheus().MetricCollectors["psos_statistic_count"].(*prometheus.GaugeVec).WithLabelValues("running_task_count").Set(float64(runningTaskCount))

	// 检查并处理超时任务 状态 运行->失败
	timeOutSimulations := []*mongo.MongoPsosSimulation{}
	nowTs := time.Now().UnixMilli()
	for i, simulation := range mongoRunPsosSimulations {
		if nowTs-simulation.StartRunningTs > SimulationTimeOut.Milliseconds() {
			timeOutSimulations = append(timeOutSimulations, &mongoRunPsosSimulations[i])
		}
	}
	for _, simulation := range timeOutSimulations {
		logger.Logger.Warnf("simulation time out. simulation info:%v", ucmd.ToJsonStrIgnoreErr(simulation))
		err = p.simulationStatusTransfer(ctx, simulation.TaskId, simulation.Id, psos.SimulationStatusRun, psos.SimulationStatusFail)
		if err != nil {
			logger.Logger.Errorf("simulation time out and change status in db err. err:%v", err)
			return err
		}
	}

	// 将失败的任务重新搬运到create状态等待后面调度机会
	err = p.Watcher.Mongodb().NewMongoEntry(bson.D{bson.E{Key: "status", Value: psos.SimulationStatusFail}}).UpdateMany(umw.Algorithm, psos.CollectionSimulations, bson.M{"$set": bson.M{"status": psos.SimulationStatusCreate}})
	if err != nil {
		return err
	}

	// 调度
	if len(runTasks) == 0 {
		return nil
	}

	load, err := p.checkLoad(ctx)
	if err != nil {
		return err
	}
	needExcuteCount := MaxParallelNum - math.Ceil(MaxParallelNum*load)
	if needExcuteCount <= 0 || needExcuteCount > MaxParallelNum {
		return nil
	}

	taskDOs, _, err := (&psos.TaskDO{}).ListTasks(ctx, psos.ListTaskQueryCond{
		Status:         []string{psos.TaskStatusRun},
		Limit:          int64(runningTaskCount),
		OrderFieldName: "create_ts",
	})
	creatorRunCount := map[string]int64{}
	creatorCreateCount := map[string]int64{}
	creatorSimulationDO := map[string][]*psos.SimulationDO{}
	for _, taskDO := range taskDOs {
		err = taskDO.LoadAllSimulation(ctx)
		if err != nil {
			return err
		}
		creatorRunCount[taskDO.Creator] += int64(len(taskDO.Simulations[psos.SimulationStatusRun]))
		creatorCreateCount[taskDO.Creator] += int64(len(taskDO.Simulations[psos.SimulationStatusCreate]))

		creatorSimulationDO[taskDO.Creator] = append(creatorSimulationDO[taskDO.Creator], taskDO.Simulations[psos.SimulationStatusCreate]...)
	}

	emptyUser := []string{}
	toRunLow := []*psos.SimulationDO{}
	for creator, count := range creatorRunCount {
		if count == 0 {
			emptyUser = append(emptyUser, creator)
		} else {
			toRunLow = append(toRunLow, creatorSimulationDO[creator]...)
		}
	}

	tmp := [][]*psos.SimulationDO{}
	for _, user := range emptyUser {
		tmp = append(tmp, creatorSimulationDO[user])
	}
	toRunHight := scatter(tmp)
	toRun := append(toRunHight, toRunLow...)

	for i, simulationDO := range toRun {
		if i+1 > int(needExcuteCount) {
			break
		}
		go func(v *psos.SimulationDO) {
			defer ucmd.RecoverPanic()
			err := p.ExecuteTask(ctx, v)
			if err != nil {
				p.simulationStatusTransfer(ctx, v.TaskId, v.Id, psos.SimulationStatusRun, psos.SimulationStatusFail)
				logger.Logger.Errorf("psos SimulationDO excute fail. task id:%v simulation id:%v err:%v", v.TaskId, v.Id, err)
			}
		}(simulationDO)
	}

	return nil
}

func (p *PsosSchedulerDO) sendFilesCompress(ctx context.Context, taskId string) error {
	simulations, _, err := (&psos.SimulationDO{}).ListSimulation(ctx, psos.ListSimulationQueryCond{
		TaskId: taskId,
	})
	if err != nil {
		return err
	}
	configSet := map[string]bool{}
	deviceFileUrls := []string{}
	configDeviceFileUrls := map[string][]string{}
	batteryFileUrls := []string{}
	configBatteryFileUrls := map[string][]string{}
	serviceFileUrls := []string{}
	configServiceFileUrls := map[string][]string{}
	for _, simulation := range simulations {
		if simulation.Status != psos.SimulationStatusSuccess {
			continue
		}
		configSet[simulation.ConfigId] = true
		deviceFileUrls = append(deviceFileUrls, simulation.DeviceResultUrl)
		batteryFileUrls = append(batteryFileUrls, simulation.BatteryResultUrl)
		serviceFileUrls = append(serviceFileUrls, simulation.ServiceResultUrl)
		configDeviceFileUrls[simulation.ConfigId] = append(configDeviceFileUrls[simulation.ConfigId], simulation.DeviceResultUrl)
		configBatteryFileUrls[simulation.ConfigId] = append(configBatteryFileUrls[simulation.ConfigId], simulation.BatteryResultUrl)
		configServiceFileUrls[simulation.ConfigId] = append(configServiceFileUrls[simulation.ConfigId], simulation.ServiceResultUrl)
	}
	fmsCompressTaskInfo := mongo.FmsCompressTaskInfo{}
	fmsCompressTaskInfo.ConfigFmsCompressTaskInfo = map[string]mongo.FmsCompressTaskInfoByConfig{}
	for configId, _ := range configSet {
		fmsCompressTaskInfoByConfig := mongo.FmsCompressTaskInfoByConfig{}
		strs := strings.SplitAfter(configDeviceFileUrls[configId][0], "/")
		destUrlPath := strings.Join(strs[:len(strs)-1], "")
		destUrl := fmt.Sprintf("%v%v_device_data.zip", destUrlPath, configId)
		fmsTaskId, err := p.Fms.SubmitCompressTask(destUrl, configDeviceFileUrls[configId])
		if err != nil {
			return err
		}
		fmsTaskStatus := mongo.FmsTaskStatus{
			TaskId:   fmsTaskId,
			Status:   mongo.FmsTaskStatusInit,
			CreateTs: time.Now().UnixMilli(),
			Date:     time.Now(),
		}
		err = client.GetWatcher().Mongodb().NewMongoEntry(bson.D{}).InsertMany(mongo.FmsDataBaseName, mongo.FmsTaskStatusCollectionName, []interface{}{fmsTaskStatus}, client.IndexOption{Name: "task_id", Fields: bson.D{{"task_id", 1}}, Unique: true})
		if err != nil {
			return err
		}
		fmsCompressTaskInfoByConfig.DeviceCompressTaskId = fmsTaskId
		fmsCompressTaskInfoByConfig.DeviceResultUrl = destUrl

		strs = strings.SplitAfter(configBatteryFileUrls[configId][0], "/")
		destUrlPath = strings.Join(strs[:len(strs)-1], "")
		destUrl = fmt.Sprintf("%v%v_battery_data.zip", destUrlPath, configId)
		fmsTaskId, err = p.Fms.SubmitCompressTask(destUrl, configBatteryFileUrls[configId])
		if err != nil {
			return err
		}
		fmsTaskStatus = mongo.FmsTaskStatus{
			TaskId:   fmsTaskId,
			Status:   mongo.FmsTaskStatusInit,
			CreateTs: time.Now().UnixMilli(),
			Date:     time.Now(),
		}
		err = client.GetWatcher().Mongodb().NewMongoEntry(bson.D{}).InsertMany(mongo.FmsDataBaseName, mongo.FmsTaskStatusCollectionName, []interface{}{fmsTaskStatus}, client.IndexOption{Name: "task_id", Fields: bson.D{{"task_id", 1}}, Unique: true})
		if err != nil {
			return err
		}
		fmsCompressTaskInfoByConfig.BatteryCompressTaskId = fmsTaskId
		fmsCompressTaskInfoByConfig.BatteryResultUrl = destUrl

		strs = strings.SplitAfter(configServiceFileUrls[configId][0], "/")
		destUrlPath = strings.Join(strs[:len(strs)-1], "")
		destUrl = fmt.Sprintf("%v%v_service_data.zip", destUrlPath, configId)
		fmsTaskId, err = p.Fms.SubmitCompressTask(destUrl, configServiceFileUrls[configId])
		if err != nil {
			return err
		}
		fmsTaskStatus = mongo.FmsTaskStatus{
			TaskId:   fmsTaskId,
			Status:   mongo.FmsTaskStatusInit,
			CreateTs: time.Now().UnixMilli(),
			Date:     time.Now(),
		}
		err = client.GetWatcher().Mongodb().NewMongoEntry(bson.D{}).InsertMany(mongo.FmsDataBaseName, mongo.FmsTaskStatusCollectionName, []interface{}{fmsTaskStatus}, client.IndexOption{Name: "task_id", Fields: bson.D{{"task_id", 1}}, Unique: true})
		if err != nil {
			return err
		}
		fmsCompressTaskInfoByConfig.ServiceCompressTaskId = fmsTaskId
		fmsCompressTaskInfoByConfig.ServiceResultUrl = destUrl

		fmsCompressTaskInfo.ConfigFmsCompressTaskInfo[configId] = fmsCompressTaskInfoByConfig
	}

	//if len(deviceFileUrls) != 0 {
	//	strs := strings.SplitAfter(deviceFileUrls[0], "/")
	//	destUrlPath := strings.Join(strs[:len(strs)-1], "")
	//	destUrl := fmt.Sprintf("%vall_device_data.zip", destUrlPath)
	//	fmsTaskId, err := p.Fms.SubmitCompressTask(destUrl, deviceFileUrls)
	//	if err != nil {
	//		return err
	//	}
	//	fmsTaskStatus := mongo.FmsTaskStatus{
	//		TaskId:   fmsTaskId,
	//		Status:   mongo.FmsTaskStatusInit,
	//		CreateTs: time.Now().UnixMilli(),
	//		Date:     time.Now(),
	//	}
	//	err = client.GetWatcher().Mongodb().NewMongoEntry(bson.D{}).InsertMany(mongo.FmsDataBaseName, mongo.FmsTaskStatusCollectionName, []interface{}{fmsTaskStatus}, client.IndexOption{Name: "task_id", Fields: bson.D{{"task_id", 1}}, Unique: true})
	//	if err != nil {
	//		return err
	//	}
	//	fmsCompressTaskInfo.DeviceCompressTaskId = fmsTaskId
	//	fmsCompressTaskInfo.DeviceResultUrl = destUrl
	//}
	//if len(batteryFileUrls) != 0 {
	//	strs := strings.SplitAfter(batteryFileUrls[0], "/")
	//	destUrlPath := strings.Join(strs[:len(strs)-1], "")
	//	destUrl := fmt.Sprintf("%vall_battery_data.zip", destUrlPath)
	//	fmsTaskId, err := p.Fms.SubmitCompressTask(destUrl, batteryFileUrls)
	//	if err != nil {
	//		return err
	//	}
	//	fmsTaskStatus := mongo.FmsTaskStatus{
	//		TaskId:   fmsTaskId,
	//		Status:   mongo.FmsTaskStatusInit,
	//		CreateTs: time.Now().UnixMilli(),
	//		Date:     time.Now(),
	//	}
	//	err = client.GetWatcher().Mongodb().NewMongoEntry(bson.D{}).InsertMany(mongo.FmsDataBaseName, mongo.FmsTaskStatusCollectionName, []interface{}{fmsTaskStatus}, client.IndexOption{Name: "task_id", Fields: bson.D{{"task_id", 1}}, Unique: true})
	//	if err != nil {
	//		return err
	//	}
	//	fmsCompressTaskInfo.BatteryCompressTaskId = fmsTaskId
	//	fmsCompressTaskInfo.BatteryResultUrl = destUrl
	//}
	//if len(serviceFileUrls) != 0 {
	//	strs := strings.SplitAfter(serviceFileUrls[0], "/")
	//	destUrlPath := strings.Join(strs[:len(strs)-1], "")
	//	destUrl := fmt.Sprintf("%vall_service_data.zip", destUrlPath)
	//	fmsTaskId, err := p.Fms.SubmitCompressTask(destUrl, serviceFileUrls)
	//	if err != nil {
	//		return err
	//	}
	//	fmsTaskStatus := mongo.FmsTaskStatus{
	//		TaskId:   fmsTaskId,
	//		Status:   mongo.FmsTaskStatusInit,
	//		CreateTs: time.Now().UnixMilli(),
	//		Date:     time.Now(),
	//	}
	//	err = client.GetWatcher().Mongodb().NewMongoEntry(bson.D{}).InsertMany(mongo.FmsDataBaseName, mongo.FmsTaskStatusCollectionName, []interface{}{fmsTaskStatus}, client.IndexOption{Name: "task_id", Fields: bson.D{{"task_id", 1}}, Unique: true})
	//	if err != nil {
	//		return err
	//	}
	//	fmsCompressTaskInfo.ServiceCompressTaskId = fmsTaskId
	//	fmsCompressTaskInfo.ServiceResultUrl = destUrl
	//}
	filter := bson.D{bson.E{Key: "_id", Value: taskId}}
	err = client.GetWatcher().Mongodb().NewMongoEntry(filter).UpdateOne(umw.Algorithm, psos.CollectionTasks, bson.M{"$set": bson.M{
		"fms_compress_task_info": fmsCompressTaskInfo,
		"update_ts":              time.Now().UnixMilli(),
	}}, false)
	if err != nil {
		return err
	}
	return nil
}

func scatter(arrays [][]*psos.SimulationDO) []*psos.SimulationDO {
	maxLen := 0
	for _, array := range arrays {
		if len(array) > maxLen {
			maxLen = len(array)
		}
	}
	res := []*psos.SimulationDO{}
	for i := 0; i < maxLen; i++ {
		for _, array := range arrays {
			if len(array) > i {
				res = append(res, array[i])
			}
		}
	}
	return res
}
