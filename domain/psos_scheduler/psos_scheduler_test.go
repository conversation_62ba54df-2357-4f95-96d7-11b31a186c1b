package psos_scheduler

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	ulog "git.nevint.com/golang-libs/common-utils/logger"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	"git.nevint.com/welkin2/welkin-backend/domain/psos"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	"git.nevint.com/welkin2/welkin-backend/service"
	"io"
	"net/http"
	"os"
	"testing"
	"time"
)

func (p *PsosSchedulerDO) TESTExecuteTask(ctx context.Context, simulation *psos.SimulationDO) error {
	// simulation status transfer form create to run
	//err := p.Watcher.Mongodb().NewMongoEntry(bson.D{bson.E{Key: "_id", Value: simulation.Id}, bson.E{Key: "status", Value: SimulationStatusCreate}}).UpdateOne("algorithm", CollectionNameSimulation, bson.M{"$set": bson.M{
	//	"status":           SimulationStatusRun,
	//	"start_running_ts": time.Now().UnixMilli(),
	//}}, false)
	//if err != nil {
	//	return err
	//}

	testParam := map[string]interface{}{
		"task_id":       "42345345",
		"simulation_id": "232423423",
		"device_info": map[string]interface{}{
			"platform": "3.0",
			"operation_strategy_info": map[string]interface{}{
				"battery_rest_switch": map[string]interface{}{
					"switch_value": 0,
				},
				"battery_exchange_switch": map[string]interface{}{
					"switch_value": 1,
				},
				"notfully_swap_switch": map[string]interface{}{
					"switch_value": 1,
				},
				"exchange_duration": 120,
			},
		},
		"scenario_info": map[string]interface{}{},
		"service_info": map[string]interface{}{
			"swapping_user_num": 50,
		},
		"simulation_info": map[string]interface{}{
			"simulation_start_time": 1705075200,
		},
	}
	jsonData, err := json.Marshal(testParam)
	if err != nil {
		return err
	}
	//resp, err := http.Post(fmt.Sprintf("%v/psos", config.Cfg.Welkin.PsosUrl), "application/json", bytes.NewBuffer(jsonData))

	resp, err := http.Post("https://api-welkin-algorithm-psos-stg.nioint.com/psos", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("http request psos fail, err: %v", err)
	}
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("http request psos fail status code:%v", resp.StatusCode)
	}
	defer resp.Body.Close()
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("io.ReadAll, err: %v", err)
	}
	var psosResp PsosResp
	if err = json.Unmarshal(data, &psosResp); err != nil {
		return fmt.Errorf("fail to unmarshal psos response, err: %v, body: %s", err, string(data))
	}

	batteryDataByte, err := base64.StdEncoding.DecodeString(psosResp.SimulationData.BatteryData)
	if err != nil {
		return err
	}
	nowTimeStamp := time.Now().UnixMilli()
	batteryDataURL, err := p.store2FMS(ctx, batteryDataByte, fmt.Sprintf("%v_battery_%v.zip", simulation.Id, nowTimeStamp), simulation)
	if err != nil {
		return err
	}
	deviceDataByte, err := base64.StdEncoding.DecodeString(psosResp.SimulationData.DeviceData)
	if err != nil {
		return err
	}
	deviceDataURL, err := p.store2FMS(ctx, deviceDataByte, fmt.Sprintf("%v_device_%v.zip", simulation.Id, nowTimeStamp), simulation)
	if err != nil {
		return err
	}
	serviceDataByte, err := base64.StdEncoding.DecodeString(psosResp.SimulationData.ServiceData)
	if err != nil {
		return err
	}
	serviceDataURL, err := p.store2FMS(ctx, serviceDataByte, fmt.Sprintf("%v_service_%v.csv", simulation.Id, nowTimeStamp), simulation)
	if err != nil {
		return err
	}
	fmt.Println(batteryDataURL, deviceDataURL, serviceDataURL)
	fmt.Println("请求成功")
	return nil
}

func intiTest() {
	os.Setenv("APOLLO_META", "https://apollo-config-cn-stg.nioint.com")
	os.Setenv("K8S_ENV", "stg")
	cfg := &ucfg.Config{}
	ucfg.GetConfigApollo("config.json", true, cfg, ucfg.NeedValidate(true), ucfg.ServiceName("ppd-welkin-backend"), ucfg.Cluster(ucfg.GetWelkinApolloCluster()))
	defer ulog.Final()
	cfg.Log.DirPath = "/Users/<USER>/Documents/GoProject/welkin-backend/config"
	config.Cfg = cfg
	log.Init(cfg.Log, true)
}

func TestExcuteSimulation(t *testing.T) {
	intiTest()
	ctx := context.Background()
	sDO, _ := (&psos.SimulationDO{}).GetSimulationById(ctx, "sim_cqk5r5qs6rakihf4gq5g")
	fms := service.FMS{
		URL:          config.Cfg.FMS.Url,
		AppId:        config.Cfg.Sentry.AppId,
		AppSecret:    config.Cfg.Sentry.AppSecret,
		ClientId:     config.Cfg.FMS.ClientId,
		ClientSecret: config.Cfg.FMS.ClientSecret,
		PriBucketKey: config.Cfg.FMS.PriBucketKey,
		PubBucketKey: config.Cfg.FMS.PubBucketKey,
		Header: map[string]string{
			"Content-Type": "application/json;charset=UTF-8",
		},
		Logger: log.Logger.Named("FMS"),
	}
	p := &PsosSchedulerDO{
		Fms:     fms,
		Watcher: client.NewWatcherByParam(config.Cfg, log.Logger.Named("Watcher")),
	}
	err := p.ExecuteTask(ctx, sDO)
	println(err)
}

func TestFmsTask(t *testing.T) {
	intiTest()
	fms := service.FMS{
		URL:          config.Cfg.FMS.Url,
		AppId:        config.Cfg.Sentry.AppId,
		AppSecret:    config.Cfg.Sentry.AppSecret,
		ClientId:     config.Cfg.FMS.ClientId,
		ClientSecret: config.Cfg.FMS.ClientSecret,
		PriBucketKey: config.Cfg.FMS.PriBucketKey,
		PubBucketKey: config.Cfg.FMS.PubBucketKey,
		Header: map[string]string{
			"Content-Type": "application/json;charset=UTF-8",
		},
		Logger: log.Logger.Named("FMS"),
	}
	fms.QueryTask([]string{"welkin_cn_319699-task-a204e2ad76474803a8123eb8de67578a"})
}

func TestCompress(t *testing.T) {
	intiTest()
	fms := service.FMS{
		URL:          config.Cfg.FMS.Url,
		AppId:        config.Cfg.Sentry.AppId,
		AppSecret:    config.Cfg.Sentry.AppSecret,
		ClientId:     config.Cfg.FMS.ClientId,
		ClientSecret: config.Cfg.FMS.ClientSecret,
		PriBucketKey: config.Cfg.FMS.PriBucketKey,
		PubBucketKey: config.Cfg.FMS.PubBucketKey,
		Header: map[string]string{
			"Content-Type": "application/json;charset=UTF-8",
		},
		Logger: log.Logger.Named("FMS"),
	}
	p := PsosSchedulerDO{
		Fms:     fms,
		Watcher: client.NewWatcherByParam(config.Cfg, log.Logger.Named("Watcher")),
	}
	ctx := context.Background()
	err := p.sendFilesCompress(ctx, "task_cpqdcuqs6rai326312pg")
	println(err)
}

func TestFMSfileList(t *testing.T) {
	intiTest()
	fms := service.FMS{
		URL:          config.Cfg.FMS.Url,
		AppId:        config.Cfg.Sentry.AppId,
		AppSecret:    config.Cfg.Sentry.AppSecret,
		ClientId:     config.Cfg.FMS.ClientId,
		ClientSecret: config.Cfg.FMS.ClientSecret,
		PriBucketKey: config.Cfg.FMS.PriBucketKey,
		PubBucketKey: config.Cfg.FMS.PubBucketKey,
		Header: map[string]string{
			"Content-Type": "application/json;charset=UTF-8",
		},
		Logger: log.Logger.Named("FMS"),
	}
	fms.ListFile(model.NeedPublic)
}

func TestAA(t *testing.T) {
	intiTest()
	fms := service.FMS{
		URL:          config.Cfg.FMS.Url,
		AppId:        config.Cfg.Sentry.AppId,
		AppSecret:    config.Cfg.Sentry.AppSecret,
		ClientId:     config.Cfg.FMS.ClientId,
		ClientSecret: config.Cfg.FMS.ClientSecret,
		PriBucketKey: config.Cfg.FMS.PriBucketKey,
		PubBucketKey: config.Cfg.FMS.PubBucketKey,
		Header: map[string]string{
			"Content-Type": "application/json;charset=UTF-8",
		},
		Logger: log.Logger.Named("FMS"),
	}

	p := PsosSchedulerDO{
		Fms:     fms,
		Watcher: client.NewWatcherByParam(config.Cfg, log.Logger.Named("Watcher")),
	}
	ctx := context.Background()
	err := p.WatchDog(ctx)
	if err != nil {
		println("有错")
		println(err.Error())
	}
	//p.ProcessHeartbeat(ctx, "222", 65.8)
	//p.ExecuteTask(ctx, &Simulation{
	//	Id: "66666666666666666",
	//})

	//var wg sync.WaitGroup
	//for i := 0; i < 20; i++ {
	//	wg.Add(1)
	//	fmt.Println(fmt.Sprintf("开始%v", i))
	//	go func(i int) {
	//		defer wg.Done()
	//		err := p.TESTExecuteTask(ctx, &psos.SimulationDO{
	//			Id: fmt.Sprintf("%v_wuxintest", i),
	//		})
	//		if err != nil {
	//			fmt.Println(fmt.Sprintf("错误:%v", err))
	//		}
	//	}(i)
	//}
	//wg.Wait()

	//for i := 0; i < 1000; i++ {
	//	err := p.TESTExecuteTask(ctx, &psos.SimulationDO{
	//		Id: fmt.Sprintf("%v_wuxintest", i),
	//	})
	//	if err != nil {
	//		fmt.Println(fmt.Sprintf("错误:%v", err))
	//	}
	//}

	// 创建一个带缓冲的channel，缓冲大小为3，表示同时最多运行3个goroutine
	//ch := make(chan struct{}, 3)
	//
	//for i := 0; i < 1000000; i++ {
	//	// 在启动goroutine前，尝试向channel发送一个空结构体
	//	ch <- struct{}{}
	//
	//	go func(id int) {
	//		defer func() { <-ch }() // 模拟goroutine结束时，从channel接收一个值
	//
	//		fmt.Printf("Starting goroutine %d", id)
	//		err := p.TESTExecuteTask(ctx, &psos.SimulationDO{
	//			Id: fmt.Sprintf("%v_wuxintest", i),
	//		})
	//		if err != nil {
	//			fmt.Println(fmt.Sprintf("错误:%v", err))
	//		}
	//		fmt.Printf("Finished goroutine %d\n", id)
	//	}(i)
	//}
}
