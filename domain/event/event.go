package event

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	"git.nevint.com/welkin2/welkin-backend/client"
	mongo_model "git.nevint.com/welkin2/welkin-backend/model/mongo"
	"go.mongodb.org/mongo-driver/bson"
)

const (
	EventSourceSwapStation = 1
	EventSourceShaman      = 2
	EventSourceQueue       = 3
)

type EventDO struct {
	DeviceId    string
	Project     string
	EventTs     int64
	EventSource int
	EventId     string
	EventName   string
	Context     map[string]string
}

type ListEventsCond struct {
	Project        string
	Rid            string
	DeviceId       string
	ServiceId      string
	EventIds       []string
	StartTs        int64
	EndTs          int64
	OrderFieldName string
	Descending     bool
	Limit          int64
	Offset         int64
}

func (s *EventDO) ListEvents(ctx context.Context, cond ListEventsCond) ([]*EventDO, int64, error) {
	if cond.Project == "" {
		return nil, 0, errors.New("project param must have!")
	}
	collectionName := fmt.Sprintf("event_%v", ucmd.RenameProjectDB(cond.Project))
	filter := bson.D{}
	if cond.DeviceId != "" {
		filter = append(filter, bson.E{Key: "device_id", Value: cond.DeviceId})
	}
	if cond.StartTs != 0 || cond.EndTs != 0 {
		filter = append(filter, bson.E{Key: "event_ts", Value: bson.M{"$gte": cond.StartTs, "$lt": cond.EndTs}})
	}
	if len(cond.EventIds) != 0 {
		filter = append(filter, bson.E{Key: "event_id", Value: bson.M{"$in": cond.EventIds}})
	}
	sortFieldName := "event_ts"
	if cond.OrderFieldName != "" {
		sortFieldName = cond.OrderFieldName
	}
	simulationByteData, total, err := client.GetWatcher().Mongodb().NewMongoEntry(filter).ListByPagination("events", collectionName,
		client.Pagination{Limit: cond.Limit, Offset: cond.Offset},
		client.Ordered{Key: sortFieldName, Descending: cond.Descending})
	if err != nil {
		return nil, 0, err
	}
	var swapEventPOs []mongo_model.MongoPowerSwapEvent
	if err = json.Unmarshal(simulationByteData, &swapEventPOs); err != nil {
		return nil, 0, err
	}
	serviceEventDOs := []*EventDO{}
	for _, swapEventPO := range swapEventPOs {
		serviceEventDOs = append(serviceEventDOs, convertPO2DO(swapEventPO))
	}
	return serviceEventDOs, total, nil
}

func (s *EventDO) GetEventSourceName() string {
	switch s.EventSource {
	case EventSourceSwapStation:
		return "换电站"
	case EventSourceShaman:
		return "shaman系统"
	case EventSourceQueue:
		return "排队系统"
	}
	return "未知来源"
}
