package event

import (
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/model/mongo"
)

func convertPO2DO(eventPO mongo.MongoPowerSwapEvent) *EventDO {
	project := ""
	device, found := cache.PowerSwapCache.GetSingleDevice(eventPO.DeviceId)
	if found {
		project = device.Project
	}

	res := &EventDO{
		DeviceId:    eventPO.DeviceId,
		Project:     project,
		EventTs:     eventPO.EventTs,
		EventSource: eventPO.EventSource,
		EventId:     eventPO.EventId,
		EventName:   getEventName(project, eventPO.EventId, eventPO.EventSource),
		Context:     convertContextId2Name(project, eventPO.Context),
	}
	return res
}

func getEventName(project, eventId string, eventSource int) string {
	switch eventSource {
	case EventSourceSwapStation:
		_, found := ProjectEventIdNameMap[project]
		if !found {
			return "未知事件"
		}
		eventName, found := ProjectEventIdNameMap[project][eventId]
		if !found {
			return "未知事件"
		}
		return eventName
	case EventSourceShaman:
		eventName, found := ShamanEventIdNameMap[eventId]
		if !found {
			return "未知事件"
		}
		return eventName
	case EventSourceQueue:
		eventName, found := QueueEventIdNameMap[eventId]
		if !found {
			return "未知事件"
		}
		return eventName
	}
	return "未知事件"
}

func convertContextId2Name(project string, context map[string]string) map[string]string {
	res := map[string]string{}
	_, found := ProjectEventContextIdNameMap[project]
	if !found {
		return context
	}
	for key, value := range context {
		contextName, found := ProjectEventContextIdNameMap[project][key]
		if found {
			res[contextName] = value
		} else {
			res[key] = value
		}
	}
	return res
}

var ProjectEventIdNameMap = map[string]map[string]string{
	umw.PowerSwap2: {
		"1200": "检查泊车到位",
		"1201": "车机鉴权",
		"1202": "启动换电按键使能",
		"1203": "BMS刷写中",
		"1204": "服务电池出仓",
		"1205": "服务电池到提升机接驳位",
		"1206": "四轮推杆到工作位",
		"1207": "开合门打开",
		"1208": "车辆举升到工作位",
		"1209": "电池拆卸，RGV举升",
		"1210": "电池拆卸，解锁",
		"1211": "电池拆卸，RGV下降",
		"1212": "车辆下降",
		"1213": "亏电电池到缓存位",
		"1214": "服务电池转运到停车平台",
		"1215": "车辆水平调整 ",
		"1216": "电池安装，RGV举升",
		"1217": "电池安装，加锁",
		"1218": "电池安装，RGV下降",
		"1219": "车辆下降",
		"1220": "亏电电池从缓存位到提升机",
		"1221": "亏电电池进电池仓",
		"1222": "亏电电池启动充电",
		"1223": "推杆回中",
		"1224": "开合门关闭",
		"1225": "车辆自检",
		"1226": "车辆上高压",
		"1227": "换电完成",
		"1228": "换电完成车辆驶离",
		"2000": "电池刷写事件",
		"2001": "PLC retry记录",
		"2002": "CMS结果上传",
		"2003": "电池静置事件",
		"2004": "潜在订单执行事件",
		"2005": "MCS资源监控告警事件",
	},
	umw.PUS3: {
		"800000": "电池仓充电服务小结开始",
		"800001": "电池仓充电服务小结结束",
		"800002": "电池仓充电服务小结结束电池自放电检查",
		"802000": "泊车到位",
		"802001": "车机鉴权",
		"802002": "启动换电按键使能",
		"802003": "BMS刷写中",
		"802004": "推杆一次定位",
		"802005": "推杆二次定位&开合门打开",
		"802006": "RGV举升至工作位",
		"802007": "电磁铁吸合&解锁",
		"802008": "电磁铁释放&RGV下降",
		"802009": "开合门关闭&RGV平移至电池对接位",
		"802010": "前后导向条伸出",
		"802011": "电池从停车平台流转至左缓存位",
		"802012": "接驳机下降零点位&RGV挡块伸出&右缓存位挡块缩回",
		"802013": "电池从接驳机流转至停车平台",
		"802014": "前后导向条缩回&开合门打开",
		"802015": "RGV平移至加解锁位",
		"802016": "RGV举升至卡销位",
		"802017": "RGV举升至销子位&车身定位销伸出&平台阻挡块缩回",
		"802018": "RGV举升至工作位&车身定位销缩回",
		"802019": "电磁铁吸合&加锁",
		"802020": "电磁铁释放&RGV下降至原点位&车身定位销缩回",
		"802021": "推杆至零点位&开合门关闭&RGV平移至电池流转位",
		"802022": "水电插头缩回&堆垛机至目标仓对接位&接驳机举升",
		"802023": "货叉目标仓伸出&堆垛机升至高位&货叉缩回",
		"802024": "堆垛机至接驳机对接位",
		"802025": "货叉接驳机伸出&接驳机升至工作位&货叉缩回",
		"802026": "接驳机升至原点&平台挡块缩回",
		"802027": "前后导向条伸出&电池从左缓存位至接驳机",
		"802028": "接驳机举升至工作位",
		"802029": "货叉接驳机伸出&接驳机至原点位&货叉缩回",
		"802030": "堆垛机至目标仓&货叉高位伸出&堆垛机至低位&货叉缩回",
		"802031": "水电插头伸出",
		"802032": "堆垛机从目标仓至初始位",
		"802033": "车辆自检",
		"802034": "车辆上高压",
		"802035": "换电完成",
		"802036": "换电完成车辆驶离",
		"803001": "系统自动启动转运",
		"803002": "云端远程启动转运",
		"803003": "插头拔出完成",
		"803004": "电池出仓到堆垛机完成",
		"803005": "电池转运到消防仓对接高位完成",
		"803006": "启动落水",
		"803007": "落水完成",
		"803008": "系统自动启动转运失败",
		"803009": "云端远程启动转运失败",
		"803010": "插头拔出失败",
		"803011": "电池出仓到堆垛机失败",
		"803012": "电池转运到消防仓对接高位失败",
		"803013": "启动落水失败",
		"803014": "落水失败",
		"804000": "换电服务小结开始",
		"804001": "换电服务小结结束",
		"804002": "换电服务小结车辆驶离",
		"804003": "换电服务小结机械结束",
		"805000": "站外充电插枪",
		"805001": "站外充电拔枪",
		"805002": "站外充电车桩鉴权",
		"805003": "站外充电服务开始",
		"805004": "站外充电服务结束",
		"805005": "站外充电充电复位",
		"805006": "站外充电服务中",
		"805007": "刷NFC卡事件",
		"805008": "车辆驶入事件",
		"805009": "车辆驶离事件",
		"806000": "云端电池刷写",
		"806001": "电池静置开始事件",
		"806002": "泊车事件",
		"806003": "潜在订单执行上报",
		"806004": "电池刷写事件",
		"808000": "CMS结果上传",
	},
	umw.PUS4: {
		"980001": "电池仓充电服务小结开始",
		"980002": "电池仓充电服务小结结束",
		"980003": "电池仓充电服务小结结束电池自放电检查",
		"981001": "换电服务小结开始",
		"981002": "换电服务小结结束",
		"981003": "换电服务小结车辆驶离",
		"981004": "换电服务小结机械结束",
		"982001": "泊车到位",
		"982002": "车机鉴权",
		"982003": "启动换电按键使能",
		"982004": "BMS刷写中",
		"982005": "推杆一次定位",
		"982006": "推杆二次定位&开合门打开",
		"982007": "RGV举升至工作位",
		"982008": "电磁铁吸合&解锁",
		"982009": "电磁铁释放&RGV下降",
		"982010": "开合门关闭&RGV平移至电池对接位",
		"982011": "前后导向条伸出",
		"982012": "电池从停车平台流转至左缓存位",
		"982013": "接驳机下降零点位&RGV挡块伸出&右缓存位挡块缩回",
		"982014": "电池从接驳机流转至停车平台",
		"982015": "前后导向条缩回&开合门打开",
		"982016": "RGV平移至加解锁位",
		"982017": "RGV举升至卡销位",
		"982018": "RGV举升至销子位&车身定位销伸出&平台阻挡块缩回",
		"982019": "RGV举升至工作位&车身定位销缩回",
		"982020": "电磁铁吸合&加锁",
		"982021": "电磁铁释放&RGV下降至原点位&车身定位销缩回",
		"982022": "推杆至零点位&开合门关闭&RGV平移至电池流转位",
		"982023": "水电插头缩回&堆垛机至目标仓对接位&接驳机举升",
		"982024": "货叉目标仓伸出&堆垛机升至高位&货叉缩回",
		"982025": "堆垛机至接驳机对接位",
		"982026": "货叉接驳机伸出&接驳机升至工作位&货叉缩回",
		"982027": "接驳机升至原点&平台挡块缩回",
		"982028": "前后导向条伸出&电池从左缓存位至接驳机",
		"982029": "接驳机举升至工作位",
		"982030": "货叉接驳机伸出&接驳机至原点位&货叉缩回",
		"982031": "堆垛机至目标仓&货叉高位伸出&堆垛机至低位&货叉缩回",
		"982032": "水电插头伸出",
		"982033": "堆垛机从目标仓至初始位",
		"982034": "车辆自检",
		"982035": "车辆上高压",
		"982036": "换电完成",
		"982037": "换电完成车辆驶离",
		"983001": "系统自动启动转运",
		"983002": "云端远程启动转运",
		"983003": "插头拔出完成",
		"983004": "电池出仓到堆垛机完成",
		"983005": "电池转运到消防仓对接高位完成",
		"983006": "启动落水",
		"983007": "落水完成",
		"983008": "系统自动启动转运失败",
		"983009": "云端远程启动转运失败",
		"983010": "插头拔出失败",
		"983011": "电池出仓到堆垛机失败",
		"983012": "电池转运到消防仓对接高位失败",
		"983013": "启动落水失败",
		"983014": "落水失败",
		"984001": "站外充电插枪",
		"984002": "站外充电拔枪",
		"984003": "站外充电车桩鉴权",
		"984004": "站外充电服务开始",
		"984005": "站外充电服务结束",
		"984006": "站外充电充电复位",
		"984007": "站外充电服务中",
		"984008": "刷NFC卡事件",
		"984009": "车辆驶入事件",
		"984010": "车辆驶离事件",
		"985001": "云端电池刷写",
		"985101": "电池静置",
		"985201": "开关卷帘门",
		"985202": "泊车事件",
		"985203": "潜在订单事件上报",
		"985204": "电池刷写事件上报",
		"999001": "开始升级",
		"999002": "升级成功",
		"999003": "升级失败"},
}

var ShamanEventIdNameMap = map[string]string{
	"order_create":  "订单创建",
	"parking_start": "泊车开始",
	"order_cancel":  "取消订单",
}

var QueueEventIdNameMap = map[string]string{
	"queue_call":          "排队叫号",
	"queue_call_time_out": "排队过号",
}

var ProjectEventContextIdNameMap = map[string]map[string]string{
	umw.PowerSwap2: {
		"960000": "泊车告警图片",
		"960001": "潜在订单执行结果",
		"960002": "潜在订单执行结果",
		"960003": "潜在订单执行结果",
		"960004": "MCS资源的告警信息",
		"960005": "齿圈有问题电池",
		"3003":   "服务id",
		"3004":   "服务电池id",
		"3005":   "车辆电池id",
		"3018":   "rid",
		"3200":   "云端电池刷写结果",
		"3201":   "请求ID",
		"3202":   "电池ID",
		"3203":   "目标版本",
		"3204":   "刷写电池容量",
		"3205":   "云端指定电池id",
		"3206":   "云端指定电池容量",
		"3207":   "触发源",
		"3208":   "刷写支路",
		"3209":   "刷写开始时间",
		"3210":   "刷写结束时间",
		"3211":   "鉴权开始时间",
		"3212":   "订单rid",
		"3400":   "PLC retrey模块",
		"3401":   "PLC retry时的service id",
		"3500":   "CMS结果",
		"3600":   "请求ID",
		"3601":   "策略执行类型",
		"3602":   "触发事件时间戳",
		"3603":   "策略生效的电池ID",
		"3604":   "事件触发时电池所在仓",
		"3605":   "策略执行结果",
		"3606":   "策略执行失败原因",
		"503003": "#3 充电系统预计充满时间",
		"503004": "#3 充电系统最近开始充电时间",
		"503005": "#3 充电系统CAN通讯状态",
		"503018": "#3 CDC投切直流正极接触器状态",
		"503200": "#3 电池包单体电压N1",
		"503201": "#3 电池包单体电压N2",
		"503202": "#3 电池包单体电压N3",
		"503203": "#3 电池包单体电压N4",
		"503204": "#3 电池包单体电压N5",
		"503205": "#3 电池包单体电压N6",
		"503206": "#3 电池包单体电压N7",
		"503207": "#3 电池包单体电压N8",
		"503208": "#3 电池包单体电压N9",
		"503209": "#3 电池包单体电压N10",
		"503210": "#3 电池包单体电压N11",
		"503211": "#3 电池包单体电压N12",
		"503212": "#3 电池包单体电压N13",
		"503500": "#3 电池包电芯温度Cell_T1",
		"503600": "#3 电池包0X274帧第1字节",
		"503601": "#3 电池包0X274帧第2字节",
		"503602": "#3 电池包0X274帧第3字节",
		"503603": "#3 电池包0X274帧第4字节",
		"503604": "#3 电池包0X274帧第5字节",
		"503605": "#3 电池包0X274帧第6字节",
		"503606": "#3 电池包0X274帧第7字节",
		"513003": "#13 充电系统预计充满时间",
		"513004": "#13 充电系统最近开始充电时间",
		"513005": "#13 充电系统CAN通讯状态",
		"513018": "#13 CDC投切直流正极接触器状态",
		"513200": "#13 电池包单体电压N1",
		"513201": "#13 电池包单体电压N2",
		"513202": "#13 电池包单体电压N3",
		"513203": "#13 电池包单体电压N4",
		"513204": "#13 电池包单体电压N5",
		"513205": "#13 电池包单体电压N6",
		"513206": "#13 电池包单体电压N7",
		"513207": "#13 电池包单体电压N8",
		"513208": "#13 电池包单体电压N9",
		"513209": "#13 电池包单体电压N10",
		"513210": "#13 电池包单体电压N11",
		"513211": "#13 电池包单体电压N12",
		"513212": "#13 电池包单体电压N13",
		"513500": "#13 电池包电芯温度Cell_T1",
		"513600": "#13 电池包0X274帧第1字节",
		"513601": "#13 电池包0X274帧第2字节",
		"513602": "#13 电池包0X274帧第3字节",
		"513603": "#13 电池包0X274帧第4字节",
		"513604": "#13 电池包0X274帧第5字节",
		"513605": "#13 电池包0X274帧第6字节",
		"513606": "#13 电池包0X274帧第7字节",
		"603003": "左后推杆工作位",
		"603004": "右前推杆原点位",
		"603005": "右前推杆工作位",
		"603018": "加解锁平台抬升至至加锁位",
	},
	umw.PUS3: {
		"500150": "云端电池刷写结果",
		"500151": "请求ID",
		"500152": "电池ID",
		"500153": "目标版本",
		"600000": "CMS结果",
		"600100": "request_id",
		"600101": "type",
		"600102": "timestamp",
		"600103": "battery_id",
		"600104": "branch_id",
		"600105": "result",
		"600106": "message",
		"600110": "事件类型",
		"600111": "时间戳",
		"600112": "车辆ID",
		"600113": "泊车模式",
		"600120": "请求ID",
		"600121": "需求详情",
		"600122": "满足需求详情",
		"600130": "告警图片名",
		"600131": "刷写结果",
		"600132": "请求ID",
		"600133": "刷写电池ID",
		"600134": "刷写目标版本",
		"600135": "刷写电池容量",
		"600136": "云端指定电池id",
		"600137": "云端指定电池容量",
		"600138": "触发源",
		"600139": "刷写支路",
		"600140": "刷写开始时间",
		"600141": "刷写结束时间",
		"600142": "鉴权开始时间",
		"600143": "订单rid",
		"203500": "终端二维码",
		"203501": "SCT-cc1状态",
		"203502": "鉴权结果result",
		"203503": "vin",
		"203504": "鉴权失败原因",
		"203505": "HMAC",
		"203506": "SUB_ID",
		"203507": "Random",
		"203508": "BRAND_ID",
		"203509": "bms当前soc",
		"203510": "服务id",
		"203511": "开始时间戳  ",
		"203512": "时间发生时间戳  ",
		"203513": "当前已经充电电量",
		"203514": "电表当前的读数",
		"203515": "开始充电时电表读数 ",
		"203516": "终端积分电量",
		"203517": "服务开始时SOC ",
		"203518": "被充车辆vin码",
		"203519": "本次订单id ",
		"203520": "本次启动命令码 ",
		"203521": "服务结束时SOC ",
		"203522": "结束原因",
		"203523": "复位按钮状态",
		"203524": "output_voltage",
		"203525": "output_current",
		"203526": "output_power",
		"203527": "charged_energy",
		"203528": "bms_request_charge_voltage ",
		"203529": "bms_request_charge_current ",
		"203530": "remain_charge_time",
		"203531": "charging_connector_work_state",
		"203532": "charge_state_now",
		"203533": "session_id",
		"203534": "充电卡卡号",
		"203535": "stop_err_code",
		"203536": "车辆驶入驶出标志",
		"203537": "车牌信息",
		"203538": "卡类型",
		"203539": "交易流水号",
		"203540": "预扣费结果",
		"203541": "evse_id",
		"203542": "pos_sn",
		"203543": "刷卡类型",
		"203544": "商户交易流水号",
	},
	umw.PUS4: {
		"992001":  "开始时间戳",
		"992002":  "结束时间戳",
		"992003":  "支路号",
		"992004":  "电池ID",
		"992005":  "电池国标ID",
		"992006":  "电池类型",
		"992007":  "完成原因",
		"992008":  "充电开始电量",
		"992009":  "结束电量",
		"992010":  "开始最高电芯温度",
		"992011":  "开始最高电芯温度编号",
		"992012":  "结束最高电芯温度",
		"992013":  "结束最高电芯温度编号",
		"992014":  "充电起始电压",
		"992015":  "充电结束电压",
		"992016":  "SOE容量1",
		"992017":  "SOE电量1",
		"992018":  "SOE容量2",
		"992019":  "SOE电量2",
		"992020":  "SOE容量3",
		"992021":  "SOE电量3",
		"992022":  "SOE容量4",
		"992023":  "SOE电量4",
		"992024":  "SOE容量5",
		"992025":  "SOE电量5",
		"992026":  "SOE容量6",
		"992027":  "SOE电量6",
		"992028":  "SOE容量7",
		"992029":  "SOE电量7",
		"992030":  "SOE容量8",
		"992031":  "SOE电量8",
		"992032":  "SOE容量9",
		"992033":  "SOE电量9",
		"992034":  "SOE容量10",
		"992035":  "SOE电量10",
		"992036":  "SOE容量11",
		"992037":  "SOE电量11",
		"992038":  "SOE容量12",
		"992039":  "SOE电量12",
		"992040":  "SOC30最高温度",
		"992041":  "SOC30最低温度",
		"992042":  "SOC50最高温度",
		"992043":  "SOC50最低温度",
		"992044":  "SOC85最高温度",
		"992045":  "SOC85最低温度",
		"992046":  "SOH起始SOC_1",
		"992047":  "SOH起始电流_1",
		"992048":  "SOHMaxR_a_1",
		"992049":  "SOHMaxRNum_a_1",
		"992050":  "SOHMaxR_b_1",
		"992051":  "SOHMaxRNum_b_1",
		"992052":  "SOHMaxR_c_1",
		"992053":  "SOHMaxRNum_c_1",
		"992054":  "SOHMinR_a_1",
		"992055":  "SOHMinRNum_a_1",
		"992056":  "SOHMinR_b_1",
		"992057":  "SOHMinRNum_b_1",
		"992058":  "SOHMinR_c_1",
		"992059":  "SOHMinRNum_c_1",
		"992060":  "SOHAvgR_1",
		"992061":  "SOHViff_1",
		"992062":  "SOH起始SOC_2",
		"992063":  "SOH起始电流_2",
		"992064":  "SOHMaxR_a_2",
		"992065":  "SOHMaxRNum_a_2",
		"992066":  "SOHMaxR_b_2",
		"992067":  "SOHMaxRNum_b_2",
		"992068":  "SOHMaxR_c_2",
		"992069":  "SOHMaxRNum_c_2",
		"992070":  "SOHMinR_a_2",
		"992071":  "SOHMinRNum_a_2",
		"992072":  "SOHMinR_b_2",
		"992073":  "SOHMinRNum_b_2",
		"992074":  "SOHMinR_c_2",
		"992075":  "SOHMinRNum_c_2",
		"992076":  "SOHAvgR_2",
		"992077":  "SOHViff_2",
		"992078":  "SOH起始SOC_3",
		"992079":  "SOH起始电流_3",
		"992080":  "SOHMaxR_a_3",
		"992081":  "SOHMaxRNum_a_3",
		"992082":  "SOHMaxR_b_3",
		"992083":  "SOHMaxRNum_b_3",
		"992084":  "SOHMaxR_c_3",
		"992085":  "SOHMaxRNum_c_3",
		"992086":  "SOHMinR_a_3",
		"992087":  "SOHMinRNum_a_3",
		"992088":  "SOHMinR_b_3",
		"992089":  "SOHMinRNum_b_3",
		"992090":  "SOHMinR_c_3",
		"992091":  "SOHMinRNum_c_3",
		"992092":  "SOHAvgR_3",
		"992093":  "SOHViff_3",
		"992094":  "SOC70最高温度",
		"992095":  "SOC70最低温度",
		"992096":  "SOC85起始电流",
		"992097":  "充电服务事件",
		"992098":  "自放电检查结果",
		"992099":  "自放电检查时间点",
		"992100":  "电池压差_0",
		"992101":  "电池压差_1",
		"992102":  "压降速率",
		"992103":  "离群程度",
		"992104":  "SOE容量13",
		"992105":  "SOE电量13",
		"992106":  "SOE容量14",
		"992107":  "SOE电量14",
		"992108":  "SOE容量15",
		"992109":  "SOE电量15",
		"992110":  "SOE容量16",
		"992111":  "SOE电量16",
		"992112":  "SOHMaxR_B_a_1",
		"992113":  "SOHMaxRNum_B_a_1",
		"992114":  "SOHMaxR_B_b_1",
		"992115":  "SOHMaxRNum_B_b_1",
		"992116":  "SOHMaxR_B_c_1",
		"992117":  "SOHMaxRNum_B_c_1",
		"992118":  "SOHMinR_B_a_1",
		"992119":  "SOHMinRNum_B_a_1",
		"992120":  "SOHMinR_B_b_1",
		"992121":  "SOHMinRNum_B_b_1",
		"992122":  "SOHMinR_B_c_1",
		"992123":  "SOHMinRNum_B_c_1",
		"992124":  "SOHMaxR_B_a_2",
		"992125":  "SOHMaxRNum_B_a_2",
		"992126":  "SOHMaxR_B_b_2",
		"992127":  "SOHMaxRNum_B_b_2",
		"992128":  "SOHMaxR_B_c_2",
		"992129":  "SOHMaxRNum_B_c_2",
		"992130":  "SOHMinR_B_a_2",
		"992131":  "SOHMinRNum_B_a_2",
		"992132":  "SOHMinR_B_b_2",
		"992133":  "SOHMinRNum_B_b_2",
		"992134":  "SOHMinR_B_c_2",
		"992135":  "SOHMinRNum_B_c_2",
		"992136":  "SOHMaxR_B_a_3",
		"992137":  "SOHMaxRNum_B_a_3",
		"992138":  "SOHMaxR_B_b_3",
		"992139":  "SOHMaxRNum_B_b_3",
		"992140":  "SOHMaxR_B_c_3",
		"992141":  "SOHMaxRNum_B_c_3",
		"992142":  "SOHMinR_B_a_3",
		"992143":  "SOHMinRNum_B_a_3",
		"992144":  "SOHMinR_B_b_3",
		"992145":  "SOHMinRNum_B_b_3",
		"992146":  "SOHMinR_B_c_3",
		"992147":  "SOHMinRNum_B_c_3",
		"992148":  "电池健康度",
		"992149":  "总充电安时数",
		"992150":  "总放电安时数",
		"992151":  "总快充安时数",
		"990001":  "设备ID",
		"990002":  "服务事件",
		"990003":  "开始换电时间",
		"990004":  "服务id",
		"990005":  "服务电池id",
		"990006":  "车辆电池id",
		"990007":  "订单id",
		"990008":  "服务电池用户soc",
		"990009":  "服务电池实际soc",
		"990010":  "车辆电池用户soc",
		"990011":  "车辆电池实际soc",
		"990012":  "服务电池类型",
		"990013":  "车辆电池类型",
		"990014":  "服务电池软件版本",
		"990015":  "车辆电池软件版本",
		"990016":  "车机软件版本",
		"990017":  "车辆类型",
		"990018":  "车辆vin",
		"990019":  "rid",
		"990020":  "定位偏差x",
		"990021":  "定位偏差y",
		"990022":  "定位偏差z",
		"990023":  "车辆胎压左前",
		"990024":  "车辆胎压右前",
		"990025":  "车辆胎压左后",
		"990026":  "车辆胎压右后",
		"990027":  "服务电池异常标签",
		"990028":  "结束换电时间",
		"990029":  "车辆ID",
		"990030":  "车辆驶离状态",
		"990031":  "换电失败原因",
		"990032":  "离线模式",
		"990033":  "车辆驶离时间",
		"990034":  "机械换电完成时间",
		"990035":  "鉴权失败原因",
		"990036":  "预选电池ID",
		"990037":  "车站通信协议",
		"990038":  "泊车方式",
		"994001":  "终端二维码",
		"994002":  "SCT-cc1状态",
		"994003":  "鉴权结果result",
		"994004":  "vin",
		"994005":  "鉴权失败原因",
		"994006":  "HMAC",
		"994007":  "SUB_ID",
		"994008":  "Random",
		"994009":  "BRAND_ID",
		"994010":  "bms当前soc",
		"994011":  "服务id:service_id",
		"994012":  "开始时间戳  ",
		"994013":  "时间发生时间戳  ",
		"994014":  "当前已经充电电量",
		"994015":  "电表当前的读数",
		"994016":  "开始充电时电表读数 ",
		"994017":  "终端积分电量",
		"994018":  "服务开始时SOC ",
		"994019":  "被充车辆vin码",
		"994020":  "本次订单id ",
		"994021":  "本次启动命令码 ",
		"994022":  "服务结束时SOC ",
		"994023":  "结束原因",
		"994024":  "复位按钮状态",
		"994025":  "output_voltage",
		"994026":  "output_current",
		"994027":  "output_power",
		"994028":  "charged_energy",
		"994029":  "bms_request_charge_voltage ",
		"994030":  "bms_request_charge_current ",
		"994031":  "remain_charge_time",
		"994032":  "charging_connector_work_state",
		"994033":  "charge_state_now",
		"994034":  "session_id",
		"994035":  "充电卡卡号",
		"994036":  "stop_err_code",
		"994037":  "车辆驶入驶出标志",
		"994038":  "车牌信息",
		"994039":  "卡类型",
		"994040":  "交易流水号",
		"994041":  "预扣费结果",
		"994042":  "evse_id",
		"994043":  "pos_sn",
		"994044":  "刷卡类型",
		"994045":  "商户交易流水号",
		"994046":  "是否蓝牙离线充电订单",
		"994047":  "预充值金额",
		"994048":  "电费金额",
		"994049":  "服务费金额",
		"994050":  "订单渠道",
		"996001":  "云端电池刷写结果",
		"996002":  "请求ID",
		"996003":  "电池ID",
		"996004	": "目标版本",
		"996101":  "request_id",
		"996102":  "类型",
		"996103":  "触发事件时间戳",
		"996104":  "策略生效的电池ID",
		"996105":  "事件触发时电池所在仓",
		"996106":  "策略执行结果",
		"996107":  "策略执行失败原因",
		"996201":  "前门或者后门",
		"996202":  "开或者关",
		"996203":  "AI上到状态",
		"996204":  "AI下到状态",
		"996205":  "PLC上到位状态",
		"996206":  "PLC下到位状态",
		"996207":  "编码器数值",
		"999010":  "升级时间",
		"999011":  "升级失败的原因",
		"996301":  "事件类型",
		"996302":  "时间戳",
		"996303":  "车辆ID",
		"996304":  "泊车模式",
		"996401":  "请求ID",
		"996402":  "需求详情",
		"996403	": "满足需求详情",
		"996410	": "SAPA告警上报ID",
		"996501":  "刷写结果",
		"996502":  "请求ID",
		"996503":  "刷写电池ID",
		"996504":  "刷写目标版本",
		"996505":  "刷写电池容量",
		"996506":  "云端指定电池id",
		"996507":  "云端指定电池容量",
		"996508":  "触发源",
		"996509":  "刷写支路",
		"996510":  "刷写开始时间",
		"996511":  "刷写结束时间",
		"996512":  "鉴权开始时间",
		"996513":  "订单rid",
	},
}
