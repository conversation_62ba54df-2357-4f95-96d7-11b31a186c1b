package order

import (
	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/model/mongo"
)

func convertOrderPO2DO(orderPO mongo.MongoOrder) *OrderDO {
	carPlatform := ""
	carModelType := ""
	carPackagePartNumber := ""
	carPackageGlobalVersion := ""
	carBrand := ""
	if orderPO.CarPlatform != nil {
		carPlatform = *orderPO.CarPlatform
	}
	if orderPO.CarModelType != nil {
		carModelType = *orderPO.CarModelType
	}
	if orderPO.CarPackagePartNumber != nil {
		carPackagePartNumber = *orderPO.CarPackagePartNumber
	}
	if orderPO.CarPackageGlobalVersion != nil {
		carPackageGlobalVersion = *orderPO.CarPackageGlobalVersion
	}
	if orderPO.CarBrand != nil {
		carBrand = *orderPO.CarBrand
	}

	description := ""
	device, found := cache.PowerSwapCache.GetSingleDevice(orderPO.DeviceId)
	if found {
		description = device.Description
	}
	orderDO := &OrderDO{
		OrderId:                         orderPO.OrderId,
		DeviceId:                        orderPO.DeviceId,
		Description:                     description,
		Project:                         orderPO.Project,
		Rid:                             orderPO.Rid,
		Status:                          orderPO.Status,
		ServiceType:                     orderPO.ServiceType,
		UserId:                          orderPO.UserId,
		OwnerId:                         orderPO.OwnerId,
		StaffId:                         orderPO.StaffId,
		VehicleId:                       orderPO.VehicleId,
		Vin:                             orderPO.Vin,
		PlateNumber:                     orderPO.PlateNumber,
		BatteryId:                       orderPO.BatteryId,
		BatteryCapSeries:                orderPO.BatteryCapSeries,
		BatterySoc:                      orderPO.BatterySoc,
		BatteryCapLevel:                 orderPO.BatteryCapLevel,
		TargetBatteryId:                 orderPO.TargetBatteryId,
		TargetBatteryCapSeries:          orderPO.TargetBatteryCapSeries,
		TargetBatterySoc:                orderPO.TargetBatterySoc,
		TargetBatteryCapLevel:           orderPO.TargetBatteryCapLevel,
		Energy:                          orderPO.Energy,
		PkgVer:                          orderPO.PkgVer,
		BmsSoftwarePn:                   orderPO.BmsSoftwarePn,
		BmsHardwarePn:                   orderPO.BmsHardwarePn,
		CdcSoftwarePn:                   orderPO.CdcSoftwarePn,
		CdcHardwarePn:                   orderPO.CdcHardwarePn,
		CgwSoftwarePn:                   orderPO.CgwSoftwarePn,
		CgwHardwarePn:                   orderPO.CgwHardwarePn,
		VcuSoftwarePn:                   orderPO.VcuSoftwarePn,
		VcuHardwarePn:                   orderPO.VcuHardwarePn,
		StartTime:                       orderPO.StartTime,
		FinishTime:                      orderPO.FinishTime,
		FinishType:                      orderPO.FinishType,
		Price:                           orderPO.Price,
		ServicePriceType:                orderPO.ServicePriceType,
		ServicePrice:                    orderPO.ServicePrice,
		ElectricityPriceType:            orderPO.ElectricityPriceType,
		ElectricityPrice:                orderPO.ElectricityPrice,
		ShouldPrice:                     orderPO.ShouldPrice,
		ShouldServicePrice:              orderPO.ShouldServicePrice,
		ShouldElectricityPrice:          orderPO.ShouldElectricityPrice,
		PaymentType:                     orderPO.PaymentType,
		AutoPayChannel:                  orderPO.AutoPayChannel,
		IsPaid:                          orderPO.IsPaid,
		PayTime:                         orderPO.PayTime,
		SwapId:                          orderPO.SwapId,
		OwnerRole:                       orderPO.OwnerRole,
		OrderSource:                     orderPO.OrderSource,
		CancelSource:                    orderPO.CancelSource,
		CancelReason:                    orderPO.CancelReason,
		EconomyPrice:                    orderPO.EconomyPrice,
		AutoSwap:                        orderPO.AutoSwap,
		CreationTime:                    orderPO.CreationTime,
		ParkingStartTime:                orderPO.ParkingStartTime,
		Channel:                         orderPO.Channel,
		Client:                          orderPO.Client,
		AutomatedSwapPeopleLeave:        orderPO.AutomatedSwapPeopleLeave,
		AutomatedSwapPeopleLeavedBefore: orderPO.AutomatedSwapPeopleLeavedBefore,
		CarBrand:                        carBrand,
		CarPlatform:                     carPlatform,
		CarModelType:                    carModelType,
		CarPackagePartNumber:            carPackagePartNumber,
		CarPackageGlobalVersion:         carPackageGlobalVersion,
		VdpDiagnosisResult:              orderPO.VdpDiagnosisResult,
		Status0Ts:                       orderPO.Status0Ts,
		Status10Ts:                      orderPO.Status10Ts,
		Status20Ts:                      orderPO.Status20Ts,
		Status30Ts:                      orderPO.Status30Ts,
		Status100Ts:                     orderPO.Status100Ts,
		Status255Ts:                     orderPO.Status255Ts,
	}
	return orderDO
}
