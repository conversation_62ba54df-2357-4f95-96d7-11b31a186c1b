package order

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	"git.nevint.com/welkin2/welkin-backend/client"
	mongo_model "git.nevint.com/welkin2/welkin-backend/model/mongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	// Shaman订单状态
	CreateShamanStatus                = 0
	PowerRunSwapShamanStatus          = 10
	PowerSwapSuccessUnPayShamanStatus = 20
	PowerSwapFailShamanStatus         = 30
	FinishShamanStatus                = 100
	CancelShamanStatus                = 255
	UnknownStatus                     = -999
)

type OrderDO struct {
	OrderId                         string
	DeviceId                        string
	Description                     string
	Project                         string
	Rid                             string
	Status                          int64
	ServiceType                     int64
	UserId                          string
	OwnerId                         string
	StaffId                         string
	VehicleId                       string
	Vin                             string
	PlateNumber                     string
	BatteryId                       string
	BatteryCapSeries                int64
	BatterySoc                      float64
	BatteryCapLevel                 int64
	TargetBatteryId                 string
	TargetBatteryCapSeries          int64
	TargetBatterySoc                float64
	TargetBatteryCapLevel           int64
	Energy                          float64
	PkgVer                          string
	BmsSoftwarePn                   string
	BmsHardwarePn                   string
	CdcSoftwarePn                   string
	CdcHardwarePn                   string
	CgwSoftwarePn                   string
	CgwHardwarePn                   string
	VcuSoftwarePn                   string
	VcuHardwarePn                   string
	StartTime                       int64
	FinishTime                      int64 // 换电完成时间
	FinishType                      int64
	Price                           int64
	ServicePriceType                int64
	ServicePrice                    int64
	ElectricityPriceType            int64
	ElectricityPrice                int64
	ShouldPrice                     int64
	ShouldServicePrice              int64
	ShouldElectricityPrice          int64
	PaymentType                     int64
	AutoPayChannel                  int64
	IsPaid                          bool
	PayTime                         int64
	SwapId                          int64
	OwnerRole                       int64
	OrderSource                     int64
	CancelSource                    int64
	CancelReason                    int64
	EconomyPrice                    int64
	AutoSwap                        int64
	CreationTime                    int64
	ParkingStartTime                int64
	Channel                         string
	Client                          string
	AutomatedSwapPeopleLeave        bool
	AutomatedSwapPeopleLeavedBefore bool
	CarBrand                        string
	CarPlatform                     string
	CarModelType                    string
	CarPackagePartNumber            string
	CarPackageGlobalVersion         string
	VdpDiagnosisResult              *string
	// shaman系统订单状态变化时间戳
	Status0Ts   *int64
	Status10Ts  *int64
	Status20Ts  *int64
	Status30Ts  *int64
	Status100Ts *int64
	Status255Ts *int64
}

type ListOrderCond struct {
	Project        string
	OrderId        string
	DeviceId       string
	CarPlatforms   []string
	VehicleId      string
	CarModelTypes  []string
	CarBrands      []string
	Status         []int64
	StartTs        int64
	EndTs          int64
	OrderFieldName string
	Descending     bool
	Limit          int64
	Offset         int64
}

func (d *OrderDO) ListOrder(ctx context.Context, cond ListOrderCond) ([]*OrderDO, int64, error) {
	if cond.Project == "" {
		return nil, 0, errors.New("project param must have!")
	}
	collectionName := fmt.Sprintf("order_%v", ucmd.RenameProjectDB(cond.Project))
	filter := bson.D{}
	if cond.OrderId != "" {
		filter = append(filter, bson.E{Key: "order_id", Value: cond.OrderId})
	}
	if cond.DeviceId != "" {
		filter = append(filter, bson.E{Key: "device_id", Value: cond.DeviceId})
	}
	if len(cond.CarPlatforms) > 0 {
		filter = append(filter, bson.E{Key: "car_platform", Value: bson.M{"$in": cond.CarPlatforms}})
	}
	if cond.VehicleId != "" {
		filter = append(filter, bson.E{Key: "vehicle_id", Value: cond.VehicleId})
	}
	if len(cond.CarModelTypes) > 0 {
		filter = append(filter, bson.E{Key: "car_model_type", Value: bson.M{"$in": cond.CarModelTypes}})
	}
	if len(cond.CarBrands) > 0 {
		filter = append(filter, bson.E{Key: "car_brand", Value: bson.M{"$in": cond.CarBrands}})
	}
	if cond.StartTs != 0 || cond.EndTs != 0 {
		filter = append(filter, bson.E{Key: "creation_time", Value: bson.M{"$gte": cond.StartTs, "$lt": cond.EndTs}})
	}
	if len(cond.Status) > 0 {
		filter = append(filter, bson.E{Key: "status", Value: bson.M{"$in": cond.Status}})
	}
	sortFieldName := "creation_time"
	if cond.OrderFieldName != "" {
		sortFieldName = cond.OrderFieldName
	}
	simulationByteData, total, err := client.GetWatcher().Mongodb().NewMongoEntry(filter).ListByPagination(umw.ServiceInfo, collectionName,
		client.Pagination{Limit: cond.Limit, Offset: cond.Offset},
		client.Ordered{Key: sortFieldName, Descending: cond.Descending})
	if err != nil {
		return nil, 0, err
	}
	var orderPOs []mongo_model.MongoOrder
	if err = json.Unmarshal(simulationByteData, &orderPOs); err != nil {
		return nil, 0, err
	}
	orderDOs := []*OrderDO{}
	for _, orderPO := range orderPOs {
		orderDOs = append(orderDOs, convertOrderPO2DO(orderPO))
	}
	return orderDOs, total, nil
}

func (d *OrderDO) GetOrderByOrderID(ctx context.Context, project, orderId string) (*OrderDO, error) {
	collectionName := fmt.Sprintf("order_%v", ucmd.RenameProjectDB(project))
	filter := bson.D{bson.E{Key: "order_id", Value: orderId}}
	var orderPO mongo_model.MongoOrder
	err := client.GetWatcher().Mongodb().NewMongoEntry(filter).FindOne(umw.ServiceInfo, collectionName, options.FindOne(), &orderPO)
	if err != nil {
		return nil, err
	}
	return convertOrderPO2DO(orderPO), nil
}

func (d *OrderDO) GetEndTimestamp() int64 {
	if d.FinishTime != 0 {
		return d.FinishTime
	}
	if d.Status == CancelShamanStatus && d.Status255Ts != nil {
		return *d.Status255Ts
	}
	return 0
}

func (d *OrderDO) GetOrderStatusForServiceVisual() string {
	if d.Status == CreateShamanStatus || d.Status == PowerRunSwapShamanStatus {
		return "run"
	}
	if d.Status == CancelShamanStatus {
		return "cancel"
	}
	if d.Status == PowerSwapSuccessUnPayShamanStatus || d.Status == FinishShamanStatus {
		return "success"
	}
	if d.Status == PowerSwapFailShamanStatus {
		return "fail"
	}
	return "unknown"
}

func (d *OrderDO) GetServiceVisualResult2OrderStatus(serviceStatus string) []int64 {
	if serviceStatus == "run" {
		return []int64{CreateShamanStatus, PowerRunSwapShamanStatus}
	}
	if serviceStatus == "cancel" {
		return []int64{CancelShamanStatus}
	}
	if serviceStatus == "success" {
		return []int64{PowerSwapSuccessUnPayShamanStatus, FinishShamanStatus}
	}
	if serviceStatus == "fail" {
		return []int64{PowerSwapFailShamanStatus}
	}
	return []int64{UnknownStatus}
}
