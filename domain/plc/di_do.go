package plc

import (
	"context"
	"fmt"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
)

type DIDO struct {
	Project   string
	DeviceId  string
	ServiceId string
	Total     int64
	DIData    map[string][]model.DIRecordsData
}

type ListDICond struct {
	StartTime int64
	EndTime   int64
	BCStepNum *int32
	PLStepNum *int32
	VarNames  string
	Project   string
	DeviceId  string
	ServiceId string
}

func (p *DIDO) ListDI(ctx context.Context, cond ListDICond) (res DIDO, err error) {
	res = DIDO{
		Project:   cond.Project,
		DeviceId:  cond.DeviceId,
		ServiceId: cond.ServiceId,
		DIData:    make(map[string][]model.DIRecordsData),
	}
	// 扩大时间范围，service_id保证不会有多余数据
	filter := bson.D{
		{"metadata.device_id", cond.DeviceId},
		{"ts", bson.M{"$gte": time.UnixMilli(cond.StartTime).Add(-time.Minute * 5), "$lte": time.UnixMilli(cond.EndTime).Add(time.Minute * 5)}},
		{"service_id", cond.ServiceId},
	}
	if cond.BCStepNum != nil && *cond.BCStepNum != -1 {
		filter = append(filter, bson.E{Key: "bc_step_num", Value: *cond.BCStepNum})
	}
	if cond.PLStepNum != nil && *cond.PLStepNum != -1 {
		filter = append(filter, bson.E{Key: "pl_step_num", Value: *cond.PLStepNum})
	}
	opts := options.Find().SetSort(bson.M{"ts": 1})
	var diData []umw.TsDIRecord
	total, err := client.GetWatcher().MongodbSupportTimeseries().NewMongoEntry(filter).FindMany(DBDI, fmt.Sprintf("%s_%s", CollectionDI, ucmd.RenameProjectDB(cond.Project)), opts, &diData)
	if err != nil {
		return
	}
	var diVarNameMap map[string]int
	if cond.Project == umw.PUS3 {
		diVarNameMap = model.GetDIVarNameMap()
	} else if cond.Project == umw.PUS4 {
		diVarNameMap = cache.DIPS4NameMap
	} else if cond.Project == umw.FYPUS1 {
		diVarNameMap = cache.DIFYPUS1NameMap
	} else {
		return res, fmt.Errorf("project: %s, query di not supported", cond.Project)
	}

	for _, di := range diData {
		if len(di.DI) < 42 || len(di.DI) > 100 {
			log.CtxLog(ctx).Warnf("`DI` length does not match, should range in [42,100]")
			continue
		}
		if cond.VarNames == "all" {
			for varName := range diVarNameMap {
				if res.DIData[varName] == nil {
					res.DIData[varName] = make([]model.DIRecordsData, 0)
				}
				res.DIData[varName] = append(res.DIData[varName], model.DIRecordsData{
					Timestamp: di.Ts.UnixMilli(),
					Value:     int(di.DI[diVarNameMap[varName]] - '0'),
				})
			}
		} else {
			varNameList := strings.Split(cond.VarNames, ",")
			for _, varName := range varNameList {
				if _, ok := diVarNameMap[varName]; !ok {
					log.CtxLog(ctx).Warnf("varname (%s) does not exist", varName)
					continue
				}
				if res.DIData[varName] == nil {
					res.DIData[varName] = make([]model.DIRecordsData, 0)
				}
				res.DIData[varName] = append(res.DIData[varName], model.DIRecordsData{
					Timestamp: di.Ts.UnixMilli(),
					Value:     int(di.DI[diVarNameMap[varName]] - '0'),
				})
			}
		}
	}

	res.Total = total
	return
}
