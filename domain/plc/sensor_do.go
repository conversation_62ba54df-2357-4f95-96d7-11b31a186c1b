package plc

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
)

type SensorDO struct {
	Project    string
	DeviceId   string
	ServiceId  string
	Total      int64
	SensorData map[string][]model.SensorData
}

type ListSensorCond struct {
	StartTime int64
	EndTime   int64
	BCStepNum *int32
	PLStepNum *int32
	VarNames  string
	Project   string
	DeviceId  string
	ServiceId string
}

func (p *SensorDO) ListSensor(ctx context.Context, cond ListSensorCond) (res SensorDO, err error) {
	res = SensorDO{
		Project:    cond.Project,
		DeviceId:   cond.DeviceId,
		ServiceId:  cond.ServiceId,
		SensorData: make(map[string][]model.SensorData),
	}
	// 扩大时间范围，service_id保证不会有多余数据
	filter := bson.D{
		{"metadata.device_id", cond.DeviceId},
		{"ts", bson.M{"$gte": time.UnixMilli(cond.StartTime).Add(-time.Minute * 5), "$lte": time.UnixMilli(cond.EndTime).Add(time.Minute * 5)}},
		{"service_id", cond.ServiceId},
	}
	if cond.BCStepNum != nil && *cond.BCStepNum != -1 {
		filter = append(filter, bson.E{Key: "bc_step_num", Value: *cond.BCStepNum})
	}
	if cond.PLStepNum != nil && *cond.PLStepNum != -1 {
		filter = append(filter, bson.E{Key: "pl_step_num", Value: *cond.PLStepNum})
	}
	opts := options.Find().SetSort(bson.M{"ts": 1})
	var sensorData []umw.TsSensorRecord
	total, err := client.GetWatcher().MongodbSupportTimeseries().NewMongoEntry(filter).FindMany(DBSensor, fmt.Sprintf("%s_%s", CollectionSensor, ucmd.RenameProjectDB(cond.Project)), opts, &sensorData)
	if err != nil {
		return
	}
	var sensorVarNameMap map[string]int
	switch cond.Project {
	case umw.PUS3:
		sensorVarNameMap = cache.SensorPS3VarNameMap
	case umw.PUS4:
		sensorVarNameMap = cache.SensorPS4VarNameMap
	case umw.FYPUS1:
		sensorVarNameMap = cache.SensorFYPUS1VarNameMap
	case umw.PowerSwap2:
		sensorVarNameMap = cache.SensorPS2VarNameMap
	default:
		return res, fmt.Errorf("project: %s, query sensor not supported", cond.Project)
	}

	for _, sensor := range sensorData {
		if cond.Project == umw.PUS3 {
			if len(sensor.Sensors) < 427 {
				log.CtxLog(ctx).Warnf("`Sensors` length does not match, expected >= 427, get %d value:%v", len(sensor.Sensors), sensor.Sensors)
				continue
			}
		} else if cond.Project == umw.PUS4 {
			if len(sensor.Sensors) < 195 {
				log.CtxLog(ctx).Warnf("`Sensors` length does not match, expected >= 195, get %d value:%v", len(sensor.Sensors), sensor.Sensors)
				continue
			}
		} else if cond.Project == umw.FYPUS1 {
			if len(sensor.Sensors) < 200 {
				log.CtxLog(ctx).Warnf("`Sensors` length does not match, expected >= 200, get %d value:%v", len(sensor.Sensors), sensor.Sensors)
				continue
			}
		} else {
			// default project is PowerSwap2
			if len(sensor.Sensors) < 192 {
				log.CtxLog(ctx).Warnf("`Sensors` length does not match, expected >= 192, get %d value:%v", len(sensor.Sensors), sensor.Sensors)
				continue
			}
		}

		if cond.VarNames == "all" {
			for varName := range sensorVarNameMap {
				if res.SensorData[varName] == nil {
					res.SensorData[varName] = make([]model.SensorData, 0)
				}
				res.SensorData[varName] = append(res.SensorData[varName], model.SensorData{
					Timestamp: sensor.Ts.UnixMilli(),
					Value:     float64(sensor.Sensors[sensorVarNameMap[varName]] - '0'),
				})
			}
		} else {
			varNameList := strings.Split(cond.VarNames, ",")
			for _, varName := range varNameList {
				if strings.Contains(sensor.Sensors, ",") {
					// 存在非bool值的传感器
					strs := strings.Split(sensor.Sensors, ",")
					if len(strs) >= 3 {
						if res.SensorData["front_left_pressure_transducer"] == nil {
							res.SensorData["front_left_pressure_transducer"] = make([]model.SensorData, 0)
						}
						value, err := strconv.ParseFloat(strs[1], 64)
						if err == nil {
							res.SensorData["front_left_pressure_transducer"] = append(res.SensorData["front_left_pressure_transducer"], model.SensorData{
								Timestamp: sensor.Ts.UnixMilli(),
								Value:     value,
							})
						}
						if res.SensorData["right_rear_pressure_transducer"] == nil {
							res.SensorData["right_rear_pressure_transducer"] = make([]model.SensorData, 0)
						}
						value, err = strconv.ParseFloat(strs[2], 64)
						if err == nil {
							res.SensorData["right_rear_pressure_transducer"] = append(res.SensorData["right_rear_pressure_transducer"], model.SensorData{
								Timestamp: sensor.Ts.UnixMilli(),
								Value:     value,
							})
						}
					}
				}
				if _, ok := sensorVarNameMap[varName]; !ok {
					log.CtxLog(ctx).Warnf("varname (%s) does not exist", varName)
					continue
				}
				if res.SensorData[varName] == nil {
					res.SensorData[varName] = make([]model.SensorData, 0)
				}
				res.SensorData[varName] = append(res.SensorData[varName], model.SensorData{
					Timestamp: sensor.Ts.UnixMilli(),
					Value:     float64(sensor.Sensors[sensorVarNameMap[varName]] - '0'),
				})
			}
		}
	}

	res.Total = total
	return
}
