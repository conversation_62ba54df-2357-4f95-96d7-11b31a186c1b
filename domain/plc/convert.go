package plc

import (
	"strings"
	"time"

	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/model"
	"git.nevint.com/welkin2/welkin-backend/util"
)

func ConvertPLCDO2VO(plcDO PLCDO, axes string) map[string][]model.TTPSData {
	res := make(map[string][]model.TTPSData)
	handler := getProjectHandler(plcDO.Project, plcDO)
	if handler == nil {
		return res
	}

	for _, record := range handler.records {
		ts := getRecordTimestamp(record)
		for _, axis := range strings.Split(axes, ",") {
			torque, speed, position := handler.parseFunc(record, util.ParseInt(axis))
			if torque == nil && speed == nil && position == nil {
				continue
			}
			res[axis] = append(res[axis], model.TTPSData{
				Timestamp: ts.UnixMilli(),
				Position:  *position,
				Speed:     *speed,
				Torque:    *torque,
			})
		}
	}
	return res
}

type projectHandler struct {
	records   []interface{}
	parseFunc func(record interface{}, axis int) (*int32, *int32, *int64)
}

func getProjectHandler(project string, plcDO PLCDO) *projectHandler {
	switch project {
	case umw.PowerSwap2:
		return &projectHandler{
			records: util.ToInterfaceSlice(plcDO.PowerSwap2Data),
			parseFunc: func(record interface{}, axis int) (*int32, *int32, *int64) {
				p := &plcMongoData{
					project:        project,
					powerSwap2Data: record.(umw.TsPowerSwap2PLCRecord),
				}
				return p.ParsePLCRecordDetails(axis)
			},
		}
	case umw.PUS3:
		return &projectHandler{
			records: util.ToInterfaceSlice(plcDO.PUS3Data),
			parseFunc: func(record interface{}, axis int) (*int32, *int32, *int64) {
				p := &plcMongoData{
					project:  project,
					pus3Data: record.(umw.TsPUS3PLCRecord),
				}
				return p.ParsePLCRecordDetails(axis)
			},
		}
	case umw.PUS4:
		return &projectHandler{
			records: util.ToInterfaceSlice(plcDO.PUS4Data),
			parseFunc: func(record interface{}, axis int) (*int32, *int32, *int64) {
				p := &plcMongoData{
					project:  project,
					pus4Data: record.(umw.TsPUS4PLCRecord),
				}
				return p.ParsePLCRecordDetails(axis)
			},
		}
	case umw.FYPUS1:
		return &projectHandler{
			records: util.ToInterfaceSlice(plcDO.FYPUS1Data),
			parseFunc: func(record interface{}, axis int) (*int32, *int32, *int64) {
				p := &plcMongoData{
					project:    project,
					fypus1Data: record.(umw.TsFYPUS1PLCRecord),
				}
				return p.ParsePLCRecordDetails(axis)
			},
		}
	default:
		return nil
	}
}

func getRecordTimestamp(record interface{}) time.Time {
	switch r := record.(type) {
	case umw.TsPowerSwap2PLCRecord:
		return r.Ts
	case umw.TsPUS3PLCRecord:
		return r.Ts
	case umw.TsPUS4PLCRecord:
		return r.Ts
	case umw.TsFYPUS1PLCRecord:
		return r.Ts
	default:
		return time.Time{}
	}
}

type plcMongoData struct {
	project        string
	powerSwapData  umw.MongoPowerSwapPLCRecord // deprecated
	powerSwap2Data umw.TsPowerSwap2PLCRecord
	pus3Data       umw.TsPUS3PLCRecord
	pus4Data       umw.TsPUS4PLCRecord
	fypus1Data     umw.TsFYPUS1PLCRecord
}

func NewPlcMongoData(project string, plcData any) *plcMongoData {
	switch project {
	case umw.PowerSwap2:
		return &plcMongoData{
			project:        project,
			powerSwap2Data: plcData.(umw.TsPowerSwap2PLCRecord),
		}
	case umw.PUS3:
		return &plcMongoData{
			project:  project,
			pus3Data: plcData.(umw.TsPUS3PLCRecord),
		}
	case umw.PUS4:
		return &plcMongoData{
			project:  project,
			pus4Data: plcData.(umw.TsPUS4PLCRecord),
		}
	case umw.FYPUS1:
		return &plcMongoData{
			project:    project,
			fypus1Data: plcData.(umw.TsFYPUS1PLCRecord),
		}
	default:
		return nil
	}
}

func (p *plcMongoData) ParsePLCRecordDetails(axis int) (*int32, *int32, *int64) {
	var torque, speed *int32
	var position *int64

	switch axis {
	case 1:
		if p.project == umw.PowerSwap {
			torque, speed, position = &p.powerSwapData.Gun1Torque, &p.powerSwapData.Gun1Speed, &p.powerSwapData.Gun1Position
		} else if p.project == umw.PowerSwap2 {
			torque, speed, position = &p.powerSwap2Data.Gun1Torque, &p.powerSwap2Data.Gun1Speed, &p.powerSwap2Data.Gun1Position
		} else if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.ForkXTorque, &p.pus3Data.ForkXSpeed, &p.pus3Data.ForkXPosition
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.ForkXTorque, &p.pus4Data.ForkXSpeed, &p.pus4Data.ForkXPosition
		} else if p.project == umw.FYPUS1 {
			torque, speed, position = &p.fypus1Data.Gun1Torque, &p.fypus1Data.Gun1Speed, &p.fypus1Data.Gun1Position
		}
	case 2:
		if p.project == umw.PowerSwap {
			torque, speed, position = &p.powerSwapData.Gun2Torque, &p.powerSwapData.Gun2Speed, &p.powerSwapData.Gun2Position
		} else if p.project == umw.PowerSwap2 {
			torque, speed, position = &p.powerSwap2Data.Gun2Torque, &p.powerSwap2Data.Gun2Speed, &p.powerSwap2Data.Gun2Position
		} else if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.StackerMoveTorque, &p.pus3Data.StackerMoveSpeed, &p.pus3Data.StackerMovePosition
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.StackerMoveTorque, &p.pus4Data.StackerMoveSpeed, &p.pus4Data.StackerMovePosition
		} else if p.project == umw.FYPUS1 {
			torque, speed, position = &p.fypus1Data.Gun2Torque, &p.fypus1Data.Gun2Speed, &p.fypus1Data.Gun2Position
		}
	case 3:
		if p.project == umw.PowerSwap {
			torque, speed, position = &p.powerSwapData.Gun3Torque, &p.powerSwapData.Gun3Speed, &p.powerSwapData.Gun3Position
		} else if p.project == umw.PowerSwap2 {
			torque, speed, position = &p.powerSwap2Data.Gun3Torque, &p.powerSwap2Data.Gun3Speed, &p.powerSwap2Data.Gun3Position
		} else if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.StackerLiftTorque, &p.pus3Data.StackerLiftSpeed, &p.pus3Data.StackerLiftPosition
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.StackerLiftTorque, &p.pus4Data.StackerLiftSpeed, &p.pus4Data.StackerLiftPosition
		} else if p.project == umw.FYPUS1 {
			torque, speed, position = &p.fypus1Data.Gun3Torque, &p.fypus1Data.Gun3Speed, &p.fypus1Data.Gun3Position
		}
	case 4:
		if p.project == umw.PowerSwap {
			torque, speed, position = &p.powerSwapData.Gun4Torque, &p.powerSwapData.Gun4Speed, &p.powerSwapData.Gun4Position
		} else if p.project == umw.PowerSwap2 {
			torque, speed, position = &p.powerSwap2Data.Gun4Torque, &p.powerSwap2Data.Gun4Speed, &p.powerSwap2Data.Gun4Position
		} else if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.Gun1Torque, &p.pus3Data.Gun1Speed, &p.pus3Data.Gun1Position
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.Gun1Torque, &p.pus4Data.Gun1Speed, &p.pus4Data.Gun1Position
		} else if p.project == umw.FYPUS1 {
			torque, speed, position = &p.fypus1Data.Gun4Torque, &p.fypus1Data.Gun4Speed, &p.fypus1Data.Gun4Position
		}
	case 5:
		if p.project == umw.PowerSwap {
			torque, speed, position = &p.powerSwapData.Gun5Torque, &p.powerSwapData.Gun5Speed, &p.powerSwapData.Gun5Position
		} else if p.project == umw.PowerSwap2 {
			torque, speed, position = &p.powerSwap2Data.Gun5Torque, &p.powerSwap2Data.Gun5Speed, &p.powerSwap2Data.Gun5Position
		} else if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.Gun2Torque, &p.pus3Data.Gun2Speed, &p.pus3Data.Gun2Position
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.Gun2Torque, &p.pus4Data.Gun2Speed, &p.pus4Data.Gun2Position
		} else if p.project == umw.FYPUS1 {
			torque, speed, position = &p.fypus1Data.Gun5Torque, &p.fypus1Data.Gun5Speed, &p.fypus1Data.Gun5Position
		}
	case 6:
		if p.project == umw.PowerSwap {
			torque, speed, position = &p.powerSwapData.Gun6Torque, &p.powerSwapData.Gun6Speed, &p.powerSwapData.Gun6Position
		} else if p.project == umw.PowerSwap2 {
			torque, speed, position = &p.powerSwap2Data.Gun6Torque, &p.powerSwap2Data.Gun6Speed, &p.powerSwap2Data.Gun6Position
		} else if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.Gun3Torque, &p.pus3Data.Gun3Speed, &p.pus3Data.Gun3Position
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.Gun3Torque, &p.pus4Data.Gun3Speed, &p.pus4Data.Gun3Position
		} else if p.project == umw.FYPUS1 {
			torque, speed, position = &p.fypus1Data.Gun6Torque, &p.fypus1Data.Gun6Speed, &p.fypus1Data.Gun6Position
		}
	case 7:
		if p.project == umw.PowerSwap {
			torque, speed, position = &p.powerSwapData.Gun7Torque, &p.powerSwapData.Gun7Speed, &p.powerSwapData.Gun7Position
		} else if p.project == umw.PowerSwap2 {
			torque, speed, position = &p.powerSwap2Data.Gun7Torque, &p.powerSwap2Data.Gun7Speed, &p.powerSwap2Data.Gun7Position
		} else if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.Gun4Torque, &p.pus3Data.Gun4Speed, &p.pus3Data.Gun4Position
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.Gun4Torque, &p.pus4Data.Gun4Speed, &p.pus4Data.Gun4Position
		} else if p.project == umw.FYPUS1 {
			torque, speed, position = &p.fypus1Data.Gun7Torque, &p.fypus1Data.Gun7Speed, &p.fypus1Data.Gun7Position
		}
	case 8:
		if p.project == umw.PowerSwap {
			torque, speed, position = &p.powerSwapData.Gun8Torque, &p.powerSwapData.Gun8Speed, &p.powerSwapData.Gun8Position
		} else if p.project == umw.PowerSwap2 {
			torque, speed, position = &p.powerSwap2Data.Gun8Torque, &p.powerSwap2Data.Gun8Speed, &p.powerSwap2Data.Gun8Position
		} else if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.Gun5Torque, &p.pus3Data.Gun5Speed, &p.pus3Data.Gun5Position
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.Gun5Torque, &p.pus4Data.Gun5Speed, &p.pus4Data.Gun5Position
		} else if p.project == umw.FYPUS1 {
			torque, speed, position = &p.fypus1Data.Gun8Torque, &p.fypus1Data.Gun8Speed, &p.fypus1Data.Gun8Position
		}
	case 9:
		if p.project == umw.PowerSwap {
			torque, speed, position = &p.powerSwapData.Gun9Torque, &p.powerSwapData.Gun9Speed, &p.powerSwapData.Gun9Position
		} else if p.project == umw.PowerSwap2 {
			torque, speed, position = &p.powerSwap2Data.Gun9Torque, &p.powerSwap2Data.Gun9Speed, &p.powerSwap2Data.Gun9Position
		} else if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.Gun6Torque, &p.pus3Data.Gun6Speed, &p.pus3Data.Gun6Position
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.Gun6Torque, &p.pus4Data.Gun6Speed, &p.pus4Data.Gun6Position
		} else if p.project == umw.FYPUS1 {
			torque, speed, position = &p.fypus1Data.LfPinTorque, &p.fypus1Data.LfPinSpeed, &p.fypus1Data.LfPinPosition
		}
	case 10:
		if p.project == umw.PowerSwap {
			torque, speed, position = &p.powerSwapData.Gun10Torque, &p.powerSwapData.Gun10Speed, &p.powerSwapData.Gun10Position
		} else if p.project == umw.PowerSwap2 {
			torque, speed, position = &p.powerSwap2Data.Gun10Torque, &p.powerSwap2Data.Gun10Speed, &p.powerSwap2Data.Gun10Position
		} else if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.Gun7Torque, &p.pus3Data.Gun7Speed, &p.pus3Data.Gun7Position
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.Gun7Torque, &p.pus4Data.Gun7Speed, &p.pus4Data.Gun7Position
		} else if p.project == umw.FYPUS1 {
			torque, speed, position = &p.fypus1Data.RrPinTorque, &p.fypus1Data.RrPinSpeed, &p.fypus1Data.RrPinPosition
		}
	case 11:
		if p.project == umw.PowerSwap {
			torque, speed, position = &p.powerSwapData.RgvLift11Torque, &p.powerSwapData.RgvLift11Speed, &p.powerSwapData.RgvLift11Position
		} else if p.project == umw.PowerSwap2 {
			torque, speed, position = &p.powerSwap2Data.RgvPinleft11Torque, &p.powerSwap2Data.RgvPinleft11Speed, &p.powerSwap2Data.RgvPinleft11Position
		} else if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.Gun8Torque, &p.pus3Data.Gun8Speed, &p.pus3Data.Gun8Position
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.Gun8Torque, &p.pus4Data.Gun8Speed, &p.pus4Data.Gun8Position
		} else if p.project == umw.FYPUS1 {
			torque, speed, position = &p.fypus1Data.LfCalmpTorque, &p.fypus1Data.LfCalmpSpeed, &p.fypus1Data.LfCalmpPosition
		}
	case 12:
		if p.project == umw.PowerSwap {
			torque, speed, position = &p.powerSwapData.RgvMove12Torque, &p.powerSwapData.RgvMove12Speed, &p.powerSwapData.RgvMove12Position
		} else if p.project == umw.PowerSwap2 {
			torque, speed, position = &p.powerSwap2Data.RgvPinleft12Torque, &p.powerSwap2Data.RgvPinleft12Speed, &p.powerSwap2Data.RgvPinleft12Position
		} else if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.Gun9Torque, &p.pus3Data.Gun9Speed, &p.pus3Data.Gun9Position
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.Gun9Torque, &p.pus4Data.Gun9Speed, &p.pus4Data.Gun9Position
		} else if p.project == umw.FYPUS1 {
			torque, speed, position = &p.fypus1Data.RfCalmpTorque, &p.fypus1Data.RfCalmpSpeed, &p.fypus1Data.RfCalmpPosition
		}
	case 13:
		if p.project == umw.PowerSwap {
			torque, speed, position = &p.powerSwapData.Lift13Torque, &p.powerSwapData.Lift13Speed, &p.powerSwapData.Lift13Position
		} else if p.project == umw.PowerSwap2 {
			torque, speed, position = &p.powerSwap2Data.LfFix13Torque, &p.powerSwap2Data.LfFix13Speed, &p.powerSwap2Data.LfFix13Position
		} else if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.Gun10Torque, &p.pus3Data.Gun10Speed, &p.pus3Data.Gun10Position
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.Gun10Torque, &p.pus4Data.Gun10Speed, &p.pus4Data.Gun10Position
		} else if p.project == umw.FYPUS1 {
			torque, speed, position = &p.fypus1Data.LfCalmpTorque, &p.fypus1Data.LfCalmpSpeed, &p.fypus1Data.LfCalmpPosition
		}
	case 14:
		if p.project == umw.PowerSwap {
			torque, speed, position = &p.powerSwapData.Rotor14Torque, &p.powerSwapData.Rotor14Speed, &p.powerSwapData.Rotor14Position
		} else if p.project == umw.PowerSwap2 {
			torque, speed, position = &p.powerSwap2Data.RfFix14Torque, &p.powerSwap2Data.RfFix14Speed, &p.powerSwap2Data.RfFix14Position
		} else if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.Gun11Torque, &p.pus3Data.Gun11Speed, &p.pus3Data.Gun11Position
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.Gun11Torque, &p.pus4Data.Gun11Speed, &p.pus4Data.Gun11Position
		} else if p.project == umw.FYPUS1 {
			torque, speed, position = &p.fypus1Data.RrCalmpTorque, &p.fypus1Data.RrCalmpSpeed, &p.fypus1Data.RrCalmpPosition
		}
	case 15:
		if p.project == umw.PowerSwap {
			torque, speed, position = &p.powerSwapData.LfFix15Torque, &p.powerSwapData.LfFix15Speed, &p.powerSwapData.LfFix15Position
		} else if p.project == umw.PowerSwap2 {
			torque, speed, position = &p.powerSwap2Data.LrFix15Torque, &p.powerSwap2Data.LrFix15Speed, &p.powerSwap2Data.LrFix15Position
		} else if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.Gun12Torque, &p.pus3Data.Gun12Speed, &p.pus3Data.Gun12Position
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.Gun12Torque, &p.pus4Data.Gun12Speed, &p.pus4Data.Gun12Position
		} else if p.project == umw.FYPUS1 {
			torque, speed, position = &p.fypus1Data.RgvMoveTorque, &p.fypus1Data.RgvMoveSpeed, &p.fypus1Data.RgvMovePosition
		}
	case 16:
		if p.project == umw.PowerSwap {
			torque, speed, position = &p.powerSwapData.RfFix16Torque, &p.powerSwapData.RfFix16Speed, &p.powerSwapData.RfFix16Position
		} else if p.project == umw.PowerSwap2 {
			torque, speed, position = &p.powerSwap2Data.RrFix16Torque, &p.powerSwap2Data.RrFix16Speed, &p.powerSwap2Data.RrFix16Position
		} else if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.Door1Torque, &p.pus3Data.Door1Speed, &p.pus3Data.Door1Position
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.Door1Torque, &p.pus4Data.Door1Speed, &p.pus4Data.Door1Position
		} else if p.project == umw.FYPUS1 {
			torque, speed, position = &p.fypus1Data.RgvLiftTorque, &p.fypus1Data.RgvLiftSpeed, &p.fypus1Data.RgvLiftPosition
		}
	case 17:
		if p.project == umw.PowerSwap {
			torque, speed, position = &p.powerSwapData.LrFix17Torque, &p.powerSwapData.LrFix17Speed, &p.powerSwapData.LrFix17Position
		} else if p.project == umw.PowerSwap2 {
			torque, speed, position = &p.powerSwap2Data.LrVslot17Torque, &p.powerSwap2Data.LrVslot17Speed, &p.powerSwap2Data.LrVslot17Position
		} else if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.Door2Torque, &p.pus3Data.Door2Speed, &p.pus3Data.Door2Position
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.Door2Torque, &p.pus4Data.Door2Speed, &p.pus4Data.Door2Position
		} else if p.project == umw.FYPUS1 {
			torque, speed, position = &p.fypus1Data.ForkTorque, &p.fypus1Data.ForkSpeed, &p.fypus1Data.ForkPosition
		}
	case 18:
		if p.project == umw.PowerSwap {
			torque, speed, position = &p.powerSwapData.RrFix18Torque, &p.powerSwapData.RrFix18Speed, &p.powerSwapData.RrFix18Position
		} else if p.project == umw.PowerSwap2 {
			torque, speed, position = &p.powerSwap2Data.Leftdoor18Torque, &p.powerSwap2Data.Leftdoor18Speed, &p.powerSwap2Data.Leftdoor18Position
		} else if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.Gun1ZTorque, &p.pus3Data.Gun1ZSpeed, &p.pus3Data.Gun1ZPosition
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.Gun1ZTorque, &p.pus4Data.Gun1ZSpeed, &p.pus4Data.Gun1ZPosition
		} else if p.project == umw.FYPUS1 {
			torque, speed, position = &p.fypus1Data.StackerLiftTorque, &p.fypus1Data.StackerLiftSpeed, &p.fypus1Data.StackerLiftPosition
		}
	case 19:
		if p.project == umw.PowerSwap {
			torque, speed, position = &p.powerSwapData.LfLift19Torque, &p.powerSwapData.LfLift19Speed, &p.powerSwapData.LfLift19Position
		} else if p.project == umw.PowerSwap2 {
			torque, speed, position = &p.powerSwap2Data.Rightdoor19Torque, &p.powerSwap2Data.Rightdoor19Speed, &p.powerSwap2Data.Rightdoor19Position
		} else if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.Gun2ZTorque, &p.pus3Data.Gun2ZSpeed, &p.pus3Data.Gun2ZPosition
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.Gun2ZTorque, &p.pus4Data.Gun2ZSpeed, &p.pus4Data.Gun2ZPosition
		} else if p.project == umw.FYPUS1 {
			torque, speed, position = &p.fypus1Data.RotateDoorTorque, &p.fypus1Data.RotateDoorSpeed, &p.fypus1Data.RotateDoorPosition
		}
	case 20:
		if p.project == umw.PowerSwap {
			torque, speed, position = &p.powerSwapData.RfLift20Torque, &p.powerSwapData.RfLift20Speed, &p.powerSwapData.RfLift20Position
		} else if p.project == umw.PowerSwap2 {
			torque, speed, position = &p.powerSwap2Data.CarLiftLeft20Torque, &p.powerSwap2Data.CarLiftLeft20Speed, &p.powerSwap2Data.CarLiftLeft20Position
		} else if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.Gun9XTorque, &p.pus3Data.Gun9XSpeed, &p.pus3Data.Gun9XPosition
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.Gun9ZTorque, &p.pus4Data.Gun9ZSpeed, &p.pus4Data.Gun9ZPosition
		} else if p.project == umw.FYPUS1 {
			torque, speed, position = &p.fypus1Data.RgvSTorque, &p.fypus1Data.RgvSSpeed, &p.fypus1Data.RgvSPosition
		}
	case 21:
		if p.project == umw.PowerSwap {
			torque, speed, position = &p.powerSwapData.LrLift21Torque, &p.powerSwapData.LrLift21Speed, &p.powerSwapData.LrLift21Position
		} else if p.project == umw.PowerSwap2 {
			torque, speed, position = &p.powerSwap2Data.CarLiftRight21Torque, &p.powerSwap2Data.CarLiftRight21Speed, &p.powerSwap2Data.CarLiftRight21Position
		} else if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.Gun9ZTorque, &p.pus3Data.Gun9ZSpeed, &p.pus3Data.Gun9ZPosition
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.Gun10ZTorque, &p.pus4Data.Gun10ZSpeed, &p.pus4Data.Gun10ZPosition
		} else if p.project == umw.FYPUS1 {
			torque, speed, position = &p.fypus1Data.LfVehicleLiftHydraulicTorque, &p.fypus1Data.LfVehicleLiftHydraulicSpeed, &p.fypus1Data.LfVehicleLiftHydraulicPosition
		}
	case 22:
		if p.project == umw.PowerSwap {
			torque, speed, position = &p.powerSwapData.RrLift22Torque, &p.powerSwapData.RrLift22Speed, &p.powerSwapData.RrLift22Position
		} else if p.project == umw.PowerSwap2 {
			torque, speed, position = &p.powerSwap2Data.RgvLift22Torque, &p.powerSwap2Data.RgvLift22Speed, &p.powerSwap2Data.RgvLift22Position
		} else if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.Gun10XTorque, &p.pus3Data.Gun10XSpeed, &p.pus3Data.Gun10XPosition
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.Gun11ZTorque, &p.pus4Data.Gun11ZSpeed, &p.pus4Data.Gun11ZPosition
		} else if p.project == umw.FYPUS1 {
			torque, speed, position = &p.fypus1Data.RfVehicleLiftHydraulicTorque, &p.fypus1Data.RfVehicleLiftHydraulicSpeed, &p.fypus1Data.RfVehicleLiftHydraulicPosition
		}
	case 23:
		if p.project == umw.PowerSwap2 {
			torque, speed, position = &p.powerSwap2Data.CabLift23Torque, &p.powerSwap2Data.CabLift23Speed, &p.powerSwap2Data.CabLift23Position
		} else if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.Gun10ZTorque, &p.pus3Data.Gun10ZSpeed, &p.pus3Data.Gun10ZPosition
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.Gun12ZTorque, &p.pus4Data.Gun12ZSpeed, &p.pus4Data.Gun12ZPosition
		} else if p.project == umw.FYPUS1 {
			torque, speed, position = &p.fypus1Data.LfVehicleLiftHydraulicTorque, &p.fypus1Data.LfVehicleLiftHydraulicSpeed, &p.fypus1Data.LfVehicleLiftHydraulicPosition
		}
	case 24:
		if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.Gun11ZTorque, &p.pus3Data.Gun11ZSpeed, &p.pus3Data.Gun11ZPosition
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.LBatPinTorque, &p.pus4Data.LBatPinSpeed, &p.pus4Data.LBatPinPosition
		} else if p.project == umw.FYPUS1 {
			torque, speed, position = &p.fypus1Data.RrVehicleLiftHydraulicTorque, &p.fypus1Data.RrVehicleLiftHydraulicSpeed, &p.fypus1Data.RrVehicleLiftHydraulicPosition
		}
	case 25:
		if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.Gun21ZTorque, &p.pus3Data.Gun21ZSpeed, &p.pus3Data.Gun21ZPosition
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.RBatPinTorque, &p.pus4Data.RBatPinSpeed, &p.pus4Data.RBatPinPosition
		} else if p.project == umw.FYPUS1 {
			torque, speed, position = &p.fypus1Data.BcLift1HydraulicTorque, &p.fypus1Data.BcLift1HydraulicSpeed, &p.fypus1Data.BcLift1HydraulicPosition
		}
	case 26:
		if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.LrLfPinTorque, &p.pus3Data.LrLfPinSpeed, &p.pus3Data.LrLfPinPosition
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.LrLfPinTorque, &p.pus4Data.LrLfPinSpeed, &p.pus4Data.LrLfPinPosition
		} else if p.project == umw.FYPUS1 {
			torque, speed, position = &p.fypus1Data.BcLift2HydraulicTorque, &p.fypus1Data.BcLift2HydraulicSpeed, &p.fypus1Data.BcLift2HydraulicPosition
		}
	case 27:
		if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.LrLrPinTorque, &p.pus3Data.LrLrPinSpeed, &p.pus3Data.LrLrPinPosition
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.LrLrPinTorque, &p.pus4Data.LrLrPinSpeed, &p.pus4Data.LrLrPinPosition
		}
	case 28:
		if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.LrRrPinTorque, &p.pus3Data.LrRrPinSpeed, &p.pus3Data.LrRrPinPosition
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.LrRrPinTorque, &p.pus4Data.LrRrPinSpeed, &p.pus4Data.LrRrPinPosition
		}
	case 29:
		if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.LfCalmpTorque, &p.pus3Data.LfCalmpSpeed, &p.pus3Data.LfCalmpPosition
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.LfCalmpTorque, &p.pus4Data.LfCalmpSpeed, &p.pus4Data.LfCalmpPosition
		}
	case 30:
		if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.RfCalmpTorque, &p.pus3Data.RfCalmpSpeed, &p.pus3Data.RfCalmpPosition
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.RfCalmpTorque, &p.pus4Data.RfCalmpSpeed, &p.pus4Data.RfCalmpPosition
		}
	case 31:
		if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.FGuideTorque, &p.pus3Data.FGuideSpeed, &p.pus3Data.FGuidePosition
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.LrCalmpTorque, &p.pus4Data.LrCalmpSpeed, &p.pus4Data.LrCalmpPosition
		}
	case 32:
		if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.VTorque, &p.pus3Data.VSpeed, &p.pus3Data.VPosition
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.RrCalmpTorque, &p.pus4Data.RrCalmpSpeed, &p.pus4Data.RrCalmpPosition
		}
	case 33:
		if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.LrCalmpTorque, &p.pus3Data.LrCalmpSpeed, &p.pus3Data.LrCalmpPosition
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.FGuideTorque, &p.pus4Data.FGuideSpeed, &p.pus4Data.FGuidePosition
		}
	case 34:
		if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.RrCalmpTorque, &p.pus3Data.RrCalmpSpeed, &p.pus3Data.RrCalmpPosition
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.RGuideTorque, &p.pus4Data.RGuideSpeed, &p.pus4Data.RGuidePosition
		}
	case 35:
		if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.RGuideTorque, &p.pus3Data.RGuideSpeed, &p.pus3Data.RGuidePosition
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.LrMoveTorque, &p.pus4Data.LrMoveSpeed, &p.pus4Data.LrMovePosition
		}
	case 36:
		if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.LrMoveTorque, &p.pus3Data.LrMoveSpeed, &p.pus3Data.LrMovePosition
		} else if p.project == umw.PUS4 {
			torque, speed, position = &p.pus4Data.LrLiftTorque, &p.pus4Data.LrLiftSpeed, &p.pus4Data.LrLiftPosition
		}
	case 37:
		if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.LrLiftTorque, &p.pus3Data.LrLiftSpeed, &p.pus3Data.LrLiftPosition
		}
	case 40:
		if p.project == umw.PUS3 {
			torque, speed, position = &p.pus3Data.PlLifterTorque, &p.pus3Data.PlLifterSpeed, &p.pus3Data.PlLifterPosition
		}
	}
	return torque, speed, position
}
