package plc

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/client"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
)

type ConverterDO struct {
	Project       string
	DeviceId      string
	ServiceId     string
	Total         int64
	ConverterData map[string][]model.ConverterData
}

type ListConverterCond struct {
	StartTime int64
	EndTime   int64
	BCStepNum *int32
	PLStepNum *int32
	ConvNums  string
	Project   string
	DeviceId  string
	ServiceId string
}

func (p *ConverterDO) ListConverter(ctx context.Context, cond ListConverterCond) (res ConverterDO, err error) {
	res = ConverterDO{
		Project:       cond.Project,
		DeviceId:      cond.DeviceId,
		ServiceId:     cond.ServiceId,
		ConverterData: make(map[string][]model.ConverterData),
	}
	// 扩大时间范围，service_id保证不会有多余数据
	filter := bson.D{
		{"metadata.device_id", cond.DeviceId},
		{"ts", bson.M{"$gte": time.UnixMilli(cond.StartTime).Add(-time.Minute * 5), "$lte": time.UnixMilli(cond.EndTime).Add(time.Minute * 5)}},
		{"service_id", cond.ServiceId},
	}
	if cond.BCStepNum != nil && *cond.BCStepNum != -1 {
		filter = append(filter, bson.E{Key: "bc_step_num", Value: *cond.BCStepNum})
	}
	if cond.PLStepNum != nil && *cond.PLStepNum != -1 {
		filter = append(filter, bson.E{Key: "pl_step_num", Value: *cond.PLStepNum})
	}
	opts := options.Find().SetSort(bson.M{"ts": 1})
	var converterData []umw.TsConverterRecord
	total, err := client.GetWatcher().MongodbSupportTimeseries().NewMongoEntry(filter).FindMany(DBConverter, fmt.Sprintf("%s_%s", CollectionConverter, ucmd.RenameProjectDB(cond.Project)), opts, &converterData)
	if err != nil {
		return
	}
	matchedNums := make(map[string]struct{})
	numbers := strings.Split(cond.ConvNums, ",")
	for _, num := range numbers {
		matchedNums[num] = struct{}{}
	}

	for _, conv := range converterData {
		if conv.Converters == "" {
			log.CtxLog(ctx).Warnf("filter: %s, `converters` is empty", ucmd.ToJsonStrIgnoreErr(filter))
			continue
		}
		converters := strings.Split(strings.TrimRight(conv.Converters, ";"), ";")
		for _, c := range converters {
			cList := strings.Split(c, ",")
			cid := cList[0]
			if _, has := matchedNums[cid]; !has {
				continue
			}
			errCode, err := strconv.Atoi(cList[1])
			if err != nil {
				log.CtxLog(ctx).Errorf("parse converter, filter: %s, err: %v", ucmd.ToJsonStrIgnoreErr(filter), err)
				continue
			}
			power, err := strconv.Atoi(cList[2])
			if err != nil {
				log.CtxLog(ctx).Errorf("parse converter, filter: %s, err: %v", ucmd.ToJsonStrIgnoreErr(filter), err)
				continue
			}
			current, err := strconv.Atoi(cList[3])
			if err != nil {
				log.CtxLog(ctx).Errorf("parse converter, filter: %s, err: %v", ucmd.ToJsonStrIgnoreErr(filter), err)
				continue
			}
			frequency, err := strconv.Atoi(cList[4])
			if err != nil {
				log.CtxLog(ctx).Errorf("parse converter, filter: %s, err: %v", ucmd.ToJsonStrIgnoreErr(filter), err)
				continue
			}

			if res.ConverterData[cid] == nil {
				res.ConverterData[cid] = make([]model.ConverterData, 0)
			}
			res.ConverterData[cid] = append(res.ConverterData[cid], model.ConverterData{
				CommonConverterData: model.CommonConverterData{
					ErrCode:   errCode,
					Current:   current,
					Power:     power,
					Frequency: frequency,
				},
				Timestamp: conv.Ts.UnixMilli(),
			})
		}

	}

	res.Total = total
	return
}
