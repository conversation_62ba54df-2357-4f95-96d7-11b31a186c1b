package plc

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/avast/retry-go"
	"github.com/mitchellh/mapstructure"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	"git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"
	"git.nevint.com/welkin2/welkin-backend/util"
)

type PLCDO struct {
	Project        string
	DeviceId       string
	ServiceId      string
	Total          int64
	PowerSwap2Data []umw.TsPowerSwap2PLCRecord
	PUS3Data       []umw.TsPUS3PLCRecord
	PUS4Data       []umw.TsPUS4PLCRecord
	FYPUS1Data     []umw.TsFYPUS1PLCRecord
}

type ListPLCCond struct {
	StartTime int64
	EndTime   int64
	BCStepNum string
	PLStepNum string
	Project   string
	DeviceId  string
	ServiceId string
}

func (p *PLCDO) ListPLC(ctx context.Context, cond ListPLCCond) (res PLCDO, err error) {
	res = PLCDO{
		Project:   cond.Project,
		DeviceId:  cond.DeviceId,
		ServiceId: cond.ServiceId,
	}
	// 扩大时间范围，service_id保证不会有多余数据
	filter := bson.D{
		{"metadata.device_id", cond.DeviceId},
		{"ts", bson.M{"$gte": time.UnixMilli(cond.StartTime).Add(-time.Minute * 15), "$lte": time.UnixMilli(cond.EndTime).Add(time.Minute * 15)}},
		{"service_id", cond.ServiceId},
	}
	if cond.BCStepNum != "" {
		bcList := make([]int, 0)
		for _, s := range strings.Split(cond.BCStepNum, ",") {
			bcList = append(bcList, util.ParseInt(s))
		}
		filter = append(filter, bson.E{Key: "bc_step_num", Value: bson.M{"$in": bcList}})
	}
	if cond.PLStepNum != "" {
		plList := make([]int, 0)
		for _, s := range strings.Split(cond.PLStepNum, ",") {
			plList = append(plList, util.ParseInt(s))
		}
		filter = append(filter, bson.E{Key: "pl_step_num", Value: bson.M{"$in": plList}})
	}
	opts := options.Find().SetSort(bson.M{"ts": 1})
	var total int64
	switch cond.Project {
	case umw.PowerSwap2:
		total, err = client.GetWatcher().MongodbSupportTimeseries().NewMongoEntry(filter).FindMany(DBPLC, fmt.Sprintf("%s_%s", CollectionPLC, ucmd.RenameProjectDB(cond.Project)), opts, &res.PowerSwap2Data)
	case umw.PUS3:
		total, err = client.GetWatcher().MongodbSupportTimeseries().NewMongoEntry(filter).FindMany(DBPLC, fmt.Sprintf("%s_%s", CollectionPLC, ucmd.RenameProjectDB(cond.Project)), opts, &res.PUS3Data)
	case umw.PUS4:
		total, err = client.GetWatcher().MongodbSupportTimeseries().NewMongoEntry(filter).FindMany(DBPLC, fmt.Sprintf("%s_%s", CollectionPLC, ucmd.RenameProjectDB(cond.Project)), opts, &res.PUS4Data)
	case umw.FYPUS1:
		total, err = client.GetWatcher().MongodbSupportTimeseries().NewMongoEntry(filter).FindMany(DBPLC, fmt.Sprintf("%s_%s", CollectionPLC, ucmd.RenameProjectDB(cond.Project)), opts, &res.FYPUS1Data)
	default:
		err = fmt.Errorf("unknown project: %s", cond.Project)
		return
	}
	if err != nil {
		return
	}
	res.Total = total
	return
}

func (p *PLCDO) ListArchiveRestoredPLC(ctx context.Context, cond ListPLCCond) (res PLCDO, err error) {
	res = PLCDO{
		Project:   cond.Project,
		DeviceId:  cond.DeviceId,
		ServiceId: cond.ServiceId,
	}
	// 扩大时间范围，service_id保证不会有多余数据
	filter := bson.D{
		{"metadata.device_id", cond.DeviceId},
		{"service_id", cond.ServiceId},
	}
	if cond.BCStepNum != "" {
		bcList := make([]int, 0)
		for _, s := range strings.Split(cond.BCStepNum, ",") {
			bcList = append(bcList, util.ParseInt(s))
		}
		filter = append(filter, bson.E{Key: "bc_step_num", Value: bson.M{"$in": bcList}})
	}
	if cond.PLStepNum != "" {
		plList := make([]int, 0)
		for _, s := range strings.Split(cond.PLStepNum, ",") {
			plList = append(plList, util.ParseInt(s))
		}
		filter = append(filter, bson.E{Key: "pl_step_num", Value: bson.M{"$in": plList}})
	}
	fmt.Printf("filter: %s\n", ucmd.ToJsonStrIgnoreErr(filter))
	opts := options.Find().SetSort(bson.M{"ts": 1})
	var total int64
	switch cond.Project {
	case umw.PowerSwap2:
		total, err = client.GetWatcher().Mongodb().NewMongoEntry(filter).FindMany(DBArchiveRestore, CollectionRestoredPLCData, opts, &res.PowerSwap2Data)
	case umw.PUS3:
		total, err = client.GetWatcher().Mongodb().NewMongoEntry(filter).FindMany(DBArchiveRestore, CollectionRestoredPLCData, opts, &res.PUS3Data)
	case umw.PUS4:
		total, err = client.GetWatcher().Mongodb().NewMongoEntry(filter).FindMany(DBArchiveRestore, CollectionRestoredPLCData, opts, &res.PUS4Data)
	case umw.FYPUS1:
		total, err = client.GetWatcher().Mongodb().NewMongoEntry(filter).FindMany(DBArchiveRestore, CollectionRestoredPLCData, opts, &res.FYPUS1Data)
	default:
		err = fmt.Errorf("unknown project: %s", cond.Project)
		return
	}
	if err != nil {
		return
	}
	res.Total = total
	return
}

func (p *PLCDO) CountPLC(ctx context.Context, cond ListPLCCond) (total int64, err error) {
	filter := bson.D{
		{"metadata.device_id", cond.DeviceId},
		{"ts", bson.M{"$gte": time.UnixMilli(cond.StartTime).Add(-time.Minute * 5), "$lte": time.UnixMilli(cond.EndTime).Add(time.Minute * 5)}},
		{"service_id", cond.ServiceId},
	}
	switch cond.Project {
	case umw.PowerSwap2:
		total, err = client.GetWatcher().MongodbSupportTimeseries().NewMongoEntry(filter).Count(DBPLC, fmt.Sprintf("%s_%s", CollectionPLC, ucmd.RenameProjectDB(cond.Project)))
	case umw.PUS3:
		total, err = client.GetWatcher().MongodbSupportTimeseries().NewMongoEntry(filter).Count(DBPLC, fmt.Sprintf("%s_%s", CollectionPLC, ucmd.RenameProjectDB(cond.Project)))
	case umw.PUS4:
		total, err = client.GetWatcher().MongodbSupportTimeseries().NewMongoEntry(filter).Count(DBPLC, fmt.Sprintf("%s_%s", CollectionPLC, ucmd.RenameProjectDB(cond.Project)))
	case umw.FYPUS1:
		total, err = client.GetWatcher().MongodbSupportTimeseries().NewMongoEntry(filter).Count(DBPLC, fmt.Sprintf("%s_%s", CollectionPLC, ucmd.RenameProjectDB(cond.Project)))
	default:
		err = fmt.Errorf("unknown project: %s", cond.Project)
		return
	}
	return
}

type ArchiveCond struct {
	Type             string
	ServiceStartTime int64
	Project          string
	DeviceId         string
	ServiceId        string
}

func (p *PLCDO) GetPLCArchiveStatus(ctx context.Context, cond ArchiveCond) (status string, err error) {
	// ServiceStartTime距离现在在一个月内，则还未归档
	if time.UnixMilli(cond.ServiceStartTime).Add(time.Hour * 24 * 30).After(time.Now()) {
		status = StatusUnarchived
		return
	}

	filter := bson.D{
		{"type", cond.Type},
		{"service_id", cond.ServiceId},
	}
	var archiveStatus mmgo.PLCArchiveStatus
	err = client.GetWatcher().Mongodb().NewMongoEntry(filter).FindOne(DBArchiveRestore, CollectionPLCStatus, nil, &archiveStatus)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			status = StatusArchived
			return status, nil
		}
		logger.CtxLog(ctx).Errorf("GetPLCArchiveStatus, failed to get plc archive status, err: %v", err)
		return
	}
	status = archiveStatus.Status
	return
}

func (p *PLCDO) RestorePLC(ctx context.Context, cond ArchiveCond) (err error) {
	statusFilter := bson.D{
		{"type", cond.Type},
		{"service_id", cond.ServiceId},
	}
	// 如果err不为nil，则删除数据库中的recoverStatus
	defer func() {
		if err != nil {
			client.GetWatcher().Mongodb().NewMongoEntry(statusFilter).DeleteOne(DBArchiveRestore, CollectionPLCStatus)
		}
	}()
	// 若recoverStatus已存在，则返回错误
	var archiveStatus mmgo.PLCArchiveStatus
	err = client.GetWatcher().Mongodb().NewMongoEntry(statusFilter).FindOne(DBArchiveRestore, CollectionPLCStatus, nil, &archiveStatus)
	if err != nil && err != mongo.ErrNoDocuments {
		logger.CtxLog(ctx).Errorf("RestorePLC, failed to get plc archive status, err: %v", err)
		return err
	}
	switch archiveStatus.Status {
	case StatusRestoring:
		logger.CtxLog(ctx).Warnf("RestorePLC, plc archive status is restoring, cond: %s", ucmd.ToJsonStrIgnoreErr(cond))
		return nil
	case StatusRestored:
		logger.CtxLog(ctx).Warnf("RestorePLC, plc archive status is restored, cond: %s", ucmd.ToJsonStrIgnoreErr(cond))
		return nil
	}

	archiveStatus = mmgo.PLCArchiveStatus{
		Type:      cond.Type,
		DeviceId:  cond.DeviceId,
		Project:   cond.Project,
		ServiceId: cond.ServiceId,
		Status:    StatusRestoring,
		Date:      time.Now(),
	}
	err = client.GetWatcher().Mongodb().NewMongoEntry(statusFilter).InsertOne(DBArchiveRestore, CollectionPLCStatus, archiveStatus, []client.IndexOption{
		{Name: "type_service_id", Fields: bson.D{{"type", 1}, {"service_id", 1}}, Unique: true},
		{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 30 * 24 * 3600},
	}...)
	if err != nil {
		logger.CtxLog(ctx).Errorf("RestorePLC, failed to update plc archive status, err: %v", err)
		return err
	}
	url := config.Cfg.DatasightApi[fmt.Sprintf("plc%s", cond.Project)].Api
	token := config.Cfg.DatasightApi[fmt.Sprintf("plc%s", cond.Project)].Token
	if url == "" || token == "" {
		logger.CtxLog(ctx).Errorf("RestorePLC, url or token is empty, url: %s, token: %s, cond: %s", url, token, ucmd.ToJsonStrIgnoreErr(cond))
		return fmt.Errorf("url or token is empty, url: %s, token: %s", url, token)
	}
	var handleFunc func(ctx context.Context, deviceId string, msg KafkaPLC) (err error)
	switch cond.Project {
	case umw.FYPUS1:
		handleFunc = HandleFYPUS1PLCRecordData
	case umw.PUS4:
		handleFunc = HandlePUS4PLCRecordData
	case umw.PUS3:
		handleFunc = HandlePUS3PLCRecordData
	case umw.PowerSwap2:
		handleFunc = HandlePowerSwap2PLCRecordData
	default:
		logger.CtxLog(ctx).Errorf("RestorePLC, unknown project: %s, cond: %s", cond.Project, ucmd.ToJsonStrIgnoreErr(cond))
		return fmt.Errorf("unknown project: %s", cond.Project)
	}
	queryDatasight := func(datetime string) (err error) {
		requestBody := map[string]interface{}{
			"datetime":   datetime,
			"service_id": cond.ServiceId,
		}
		ct := ucmd.NewHttpClient(ucmd.HttpClient{
			URL:    url,
			Method: "POST",
			Header: map[string]string{
				"Content-Type":  "application/json",
				"Authorization": fmt.Sprintf("Bearer %s", token),
			},
			RequestBody: requestBody,
		})
		logger.CtxLog(ctx).Infof("RestorePLC, queryDatasight, url: %s, requestBody: %s", url, ucmd.ToJsonStrIgnoreErr(requestBody))
		body, statusCode, err := ct.Do()
		if err != nil {
			logger.CtxLog(ctx).Errorf("RestorePLC, failed to do request, err: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
			return err
		}
		defer body.Close()
		data, dErr := io.ReadAll(body)
		if dErr != nil {
			logger.CtxLog(ctx).Errorf("RestorePLC, failed to read body, err: %v, cond: %s", dErr, ucmd.ToJsonStrIgnoreErr(cond))
			return dErr
		}
		if statusCode != http.StatusOK {
			logger.CtxLog(ctx).Errorf("RestorePLC, status code: %d, body: %s, cond: %s", statusCode, string(data), ucmd.ToJsonStrIgnoreErr(cond))
			return fmt.Errorf("status code: %d, body: %s", statusCode, string(data))
		}
		var response model.DatasightResponse
		if err = json.Unmarshal(data, &response); err != nil {
			logger.CtxLog(ctx).Errorf("RestorePLC, failed to unmarshal body, err: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
			return err
		}
		if response.ResultCode != "success" {
			logger.CtxLog(ctx).Errorf("RestorePLC, result code: %s, display msg: %s, debug msg: %s, request_id: %s, cond: %s", response.ResultCode, response.DisplayMsg, response.DebugMsg, response.RequestID, ucmd.ToJsonStrIgnoreErr(cond))
			return fmt.Errorf("result code: %s, display msg: %s, debug msg: %s, request_id: %s", response.ResultCode, response.DisplayMsg, response.DebugMsg, response.RequestID)
		}
		for _, item := range response.Data {
			var plcData model.DatasightPLC
			err := mapstructure.Decode(item, &plcData)
			if err != nil {
				logger.CtxLog(ctx).Errorf("RestorePLC, failed to decode data, err: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
				return err
			}
			var arrData []string
			if err = json.Unmarshal([]byte(plcData.Data), &arrData); err != nil {
				logger.CtxLog(ctx).Errorf("RestorePLC, failed to unmarshal data, err: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
				return err
			}
			plcRecordData := KafkaPLC{
				Project:   cond.Project,
				ServiceId: cond.ServiceId,
				Timestamp: plcData.Ts,
				Data:      arrData,
			}
			handleFunc(ctx, cond.DeviceId, plcRecordData)
		}
		return nil
	}
	err = retry.Do(func() error {
		return queryDatasight(time.UnixMilli(cond.ServiceStartTime).Format("2006010215"))
	}, retry.Attempts(3), retry.Delay(time.Second*1))
	if err != nil {
		logger.CtxLog(ctx).Errorf("RestorePLC, failed to query datasight, err: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return err
	}
	err = retry.Do(func() error {
		return queryDatasight(time.UnixMilli(cond.ServiceStartTime).Add(time.Hour).Format("2006010215"))
	}, retry.Attempts(3), retry.Delay(time.Second*1))
	if err != nil {
		logger.CtxLog(ctx).Errorf("RestorePLC, failed to query datasight, err: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return err
	}
	archiveStatus.Status = StatusRestored
	archiveStatus.Date = time.Now()
	err = client.GetWatcher().Mongodb().NewMongoEntry(statusFilter).UpdateOne(DBArchiveRestore, CollectionPLCStatus, bson.M{"$set": archiveStatus}, true)
	if err != nil {
		logger.CtxLog(ctx).Errorf("RestorePLC, failed to update plc archive status, err: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return err
	}
	return nil
}

type KafkaPLC struct {
	Project   string   `json:"project" binding:"required"`
	ServiceId string   `json:"si" binding:"required"`
	Timestamp int64    `json:"ts" binding:"required"` // time to send to kafka
	Data      []string `json:"data" binding:"required"`
}

func HandleFYPUS1PLCRecordData(ctx context.Context, deviceId string, kafkaPLCRecordData KafkaPLC) (err error) {
	plcRecordsData := make([]interface{}, 0)
	for _, item := range kafkaPLCRecordData.Data {
		var mPLCRecordData mmgo.RestoredFYPUS1PLCData
		details := strings.Split(strings.TrimSuffix(item, ";"), ";")
		if len(details[0]) != 13 {
			logger.CtxLog(ctx).Warnf("device_id: %s, timestamp format is incorrect, si: %s, timestamp: %s", deviceId, kafkaPLCRecordData.ServiceId, details[0])
			continue
		}
		first, _ := strconv.ParseInt(details[0], 10, 64)
		mPLCRecordData.Ts = time.UnixMilli(first)
		second := strings.Split(details[1], ",")
		if len(second) != 3 {
			// ignore and skip if the required data is missing
			continue
		}
		mode, _ := strconv.Atoi(second[0])
		mPLCRecordData.Mode = mode
		bcs, _ := strconv.Atoi(second[1])
		mPLCRecordData.BcStepNum = bcs
		pls, _ := strconv.Atoi(second[2])
		mPLCRecordData.PlStepNum = pls
		//p.logger.Infof("consume plc record - project: %s, si: %s, ts: %d, bc_step_num: %d, pl_step_num: %d", kafkaPLCRecordData.Project, kafkaPLCRecordData.ServiceId, first, bcs, pls)

		for _, axis := range details[2:] {
			content := strings.Split(axis, ",")
			if len(content) != 4 {
				// keep the default value: 0 for the current axis if the required data is missing
				continue
			}
			index, err := strconv.Atoi(content[0])
			if err != nil {
				// keep the default value: 0 for the current axis if failed to parse the number of it
				continue
			}
			switch index {
			case 1:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun1Position = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun1Speed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun1Torque = int32(gt)
			case 2:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun2Position = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun2Speed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun2Torque = int32(gt)
			case 3:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun3Position = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun3Speed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun3Torque = int32(gt)
			case 4:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun4Position = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun4Speed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun4Torque = int32(gt)
			case 5:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun5Position = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun5Speed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun5Torque = int32(gt)
			case 6:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun6Position = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun6Speed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun6Torque = int32(gt)
			case 7:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun7Position = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun7Speed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun7Torque = int32(gt)
			case 8:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun8Position = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun8Speed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun8Torque = int32(gt)
			case 9:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.LfPinPosition = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.LfPinSpeed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.LfPinTorque = int32(gt)
			case 10:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.RrPinPosition = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.RrPinSpeed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.RrPinTorque = int32(gt)
			case 11:
				rplp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.LfCalmpPosition = rplp
				rpls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.LfCalmpSpeed = int32(rpls)
				rplt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.LfCalmpTorque = int32(rplt)
			case 12:
				rplp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.RfCalmpPosition = rplp
				rpls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.RfCalmpSpeed = int32(rpls)
				rplt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.RfCalmpTorque = int32(rplt)
			case 13:
				lffp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.LrCalmpPosition = lffp
				lffs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.LrCalmpSpeed = int32(lffs)
				lfft, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.LrCalmpTorque = int32(lfft)
			case 14:
				rffp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.RrCalmpPosition = rffp
				rffs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.RrCalmpSpeed = int32(rffs)
				rfft, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.RrCalmpTorque = int32(rfft)
			case 15:
				lrfp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.RgvMovePosition = lrfp
				lrfs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.RgvMoveSpeed = int32(lrfs)
				lrft, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.RgvMoveTorque = int32(lrft)
			case 16:
				rrfp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.RgvLiftPosition = rrfp
				rrfs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.RgvLiftSpeed = int32(rrfs)
				rrft, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.RgvLiftTorque = int32(rrft)
			case 17:
				lvp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.ForkPosition = lvp
				lvs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.ForkSpeed = int32(lvs)
				lvt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.ForkTorque = int32(lvt)
			case 18:
				ldp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.StackerLiftPosition = ldp
				lds, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.StackerLiftSpeed = int32(lds)
				ldt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.StackerLiftTorque = int32(ldt)
			case 19:
				rdp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.RotateDoorPosition = rdp
				rds, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.RotateDoorSpeed = int32(rds)
				rdt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.RotateDoorTorque = int32(rdt)
			case 20:
				cllp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.RgvSPosition = cllp
				clls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.RgvSSpeed = int32(clls)
				cllt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.RgvSTorque = int32(cllt)
			case 21:
				clrp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.LfVehicleLiftHydraulicPosition = clrp
				clrs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.LfVehicleLiftHydraulicSpeed = int32(clrs)
				clrt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.LfVehicleLiftHydraulicTorque = int32(clrt)
			case 22:
				rlp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.RfVehicleLiftHydraulicPosition = rlp
				rls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.RfVehicleLiftHydraulicSpeed = int32(rls)
				rlt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.RfVehicleLiftHydraulicTorque = int32(rlt)
			case 23:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.LrVehicleLiftHydraulicPosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.LrVehicleLiftHydraulicSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.LrVehicleLiftHydraulicTorque = int32(clt)
			case 24:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.RrVehicleLiftHydraulicPosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.RrVehicleLiftHydraulicSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.RrVehicleLiftHydraulicTorque = int32(clt)
			case 25:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.BcLift1HydraulicPosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.BcLift1HydraulicSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.BcLift1HydraulicTorque = int32(clt)
			case 26:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.BcLift2HydraulicPosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.BcLift2HydraulicSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.BcLift2HydraulicTorque = int32(clt)
			}
		}
		mPLCRecordData.ServiceId = kafkaPLCRecordData.ServiceId
		mPLCRecordData.Metadata.DeviceId = deviceId
		mPLCRecordData.SendKfkTs = kafkaPLCRecordData.Timestamp
		mPLCRecordData.WriteDbTs = time.Now().UnixMilli()
		mPLCRecordData.Date = time.Now()
		plcRecordsData = append(plcRecordsData, mPLCRecordData)
	}

	err = client.GetWatcher().Mongodb().NewMongoEntry().InsertMany(DBArchiveRestore, CollectionRestoredPLCData, plcRecordsData, []client.IndexOption{
		{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 30 * 24 * 3600},
		{Name: "service_id", Fields: bson.D{{"service_id", 1}}},
	}...)
	if err != nil {
		logger.CtxLog(ctx).Errorf("failed to insert plc-record data to mongodb: %v, project: %s, service_id: %s, device_id: %s", err, kafkaPLCRecordData.Project, kafkaPLCRecordData.ServiceId, deviceId)
		return err
	}
	return nil
}

func HandlePUS4PLCRecordData(ctx context.Context, deviceId string, kafkaPLCRecordData KafkaPLC) (err error) {
	plcRecordsData := make([]interface{}, 0)
	for _, item := range kafkaPLCRecordData.Data {
		var mPLCRecordData mmgo.RestoredPUS4PLCData
		details := strings.Split(strings.TrimSuffix(item, ";"), ";")
		//if len(details) < 40 || len(details) > 42 {
		//	// format: ts;mode,bcStep,plStep;axisIndex,position,speed,torque;[1-40]
		//	// ignore and skip if the required data is missing
		//	// 38,39 is reserved, temporarily unused
		//	p.logger.Warnf("device_id: %s, plc record format is incorrect, si: %s, data: %v", deviceId, kafkaPLCRecordData.ServiceId, kafkaPLCRecordData.Data[i])
		//	return
		//}
		if len(details[0]) != 13 {
			logger.CtxLog(ctx).Warnf("device_id: %s, timestamp format is incorrect, si: %s, timestamp: %s", deviceId, kafkaPLCRecordData.ServiceId, details[0])
			continue
		}
		first, _ := strconv.ParseInt(details[0], 10, 64)
		mPLCRecordData.Ts = time.UnixMilli(first)
		second := strings.Split(details[1], ",")
		if len(second) != 3 {
			// ignore and skip if the required data is missing
			continue
		}
		mode, _ := strconv.Atoi(second[0])
		mPLCRecordData.Mode = mode
		bcs, _ := strconv.Atoi(second[1])
		mPLCRecordData.BcStepNum = bcs
		pls, _ := strconv.Atoi(second[2])
		mPLCRecordData.PlStepNum = pls
		//p.logger.Infof("consume plc record - project: %s, si: %s, ts: %d, bc_step_num: %d, pl_step_num: %d", kafkaPLCRecordData.Project, kafkaPLCRecordData.ServiceId, first, bcs, pls)
		for _, axis := range details[2:] {
			content := strings.Split(axis, ",")
			if len(content) != 4 {
				// keep the default value: 0 for the current axis if the required data is missing
				continue
			}
			index, err := strconv.Atoi(content[0])
			if err != nil {
				// keep the default value: 0 for the current axis if failed to parse the number of it
				continue
			}
			switch index {
			case 1:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.ForkXPosition = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.ForkXSpeed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.ForkXTorque = int32(gt)
			case 2:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.StackerMovePosition = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.StackerMoveSpeed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.StackerMoveTorque = int32(gt)
			case 3:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.StackerLiftPosition = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.StackerLiftSpeed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.StackerLiftTorque = int32(gt)
			case 4:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun1Position = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun1Speed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun1Torque = int32(gt)
			case 5:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun2Position = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun2Speed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun2Torque = int32(gt)
			case 6:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun3Position = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun3Speed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun3Torque = int32(gt)
			case 7:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun4Position = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun4Speed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun4Torque = int32(gt)
			case 8:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun5Position = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun5Speed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun5Torque = int32(gt)
			case 9:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun6Position = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun6Speed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun6Torque = int32(gt)
			case 10:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun7Position = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun7Speed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun7Torque = int32(gt)
			case 11:
				rplp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun8Position = rplp
				rpls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun8Speed = int32(rpls)
				rplt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun8Torque = int32(rplt)
			case 12:
				rplp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun9Position = rplp
				rpls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun9Speed = int32(rpls)
				rplt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun9Torque = int32(rplt)
			case 13:
				lffp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun10Position = lffp
				lffs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun10Speed = int32(lffs)
				lfft, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun10Torque = int32(lfft)
			case 14:
				rffp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun11Position = rffp
				rffs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun11Speed = int32(rffs)
				rfft, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun11Torque = int32(rfft)
			case 15:
				lrfp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun12Position = lrfp
				lrfs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun12Speed = int32(lrfs)
				lrft, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun12Torque = int32(lrft)
			case 16:
				rrfp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Door1Position = rrfp
				rrfs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Door1Speed = int32(rrfs)
				rrft, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Door1Torque = int32(rrft)
			case 17:
				lvp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Door2Position = lvp
				lvs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Door2Speed = int32(lvs)
				lvt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Door2Torque = int32(lvt)
			case 18:
				ldp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun1ZPosition = ldp
				lds, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun1ZSpeed = int32(lds)
				ldt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun1ZTorque = int32(ldt)
			case 19:
				rdp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun2ZPosition = rdp
				rds, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun2ZSpeed = int32(rds)
				rdt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun2ZTorque = int32(rdt)
			case 20:
				cllp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun9ZPosition = cllp
				clls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun9ZSpeed = int32(clls)
				cllt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun9ZTorque = int32(cllt)
			case 21:
				clrp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun10ZPosition = clrp
				clrs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun10ZSpeed = int32(clrs)
				clrt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun10ZTorque = int32(clrt)
			case 22:
				rlp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun11ZPosition = rlp
				rls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun11ZSpeed = int32(rls)
				rlt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun11ZTorque = int32(rlt)
			case 23:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun12ZPosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun12ZSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun12ZTorque = int32(clt)
			case 24:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.LBatPinPosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.LBatPinSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.LBatPinTorque = int32(clt)
			case 25:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.RBatPinPosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.RBatPinSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.RBatPinTorque = int32(clt)
			case 26:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.LrLfPinPosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.LrLfPinSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.LrLfPinTorque = int32(clt)
			case 27:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.LrLrPinPosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.LrLrPinSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.LrLrPinTorque = int32(clt)
			case 28:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.LrRrPinPosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.LrRrPinSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.LrRrPinTorque = int32(clt)
			case 29:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.LfCalmpPosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.LfCalmpSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.LfCalmpTorque = int32(clt)
			case 30:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.RfCalmpPosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.RfCalmpSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.RfCalmpTorque = int32(clt)
			case 31:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.LrCalmpPosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.LrCalmpSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.LrCalmpTorque = int32(clt)
			case 32:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.RrCalmpPosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.RrCalmpSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.RrCalmpTorque = int32(clt)
			case 33:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.FGuidePosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.FGuideSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.FGuideTorque = int32(clt)
			case 34:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.RGuidePosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.RGuideSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.RGuideTorque = int32(clt)
			case 35:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.LrMovePosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.LrMoveSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.LrMoveTorque = int32(clt)
			case 36:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.LrLiftPosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.LrLiftSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.LrLiftTorque = int32(clt)
			}
		}
		mPLCRecordData.ServiceId = kafkaPLCRecordData.ServiceId
		mPLCRecordData.Metadata.DeviceId = deviceId
		mPLCRecordData.SendKfkTs = kafkaPLCRecordData.Timestamp
		mPLCRecordData.WriteDbTs = time.Now().UnixMilli()
		mPLCRecordData.Date = time.Now()
		plcRecordsData = append(plcRecordsData, mPLCRecordData)
	}
	err = client.GetWatcher().Mongodb().NewMongoEntry().InsertMany(DBArchiveRestore, CollectionRestoredPLCData, plcRecordsData, []client.IndexOption{
		{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 30 * 24 * 3600},
		{Name: "service_id", Fields: bson.D{{"service_id", 1}}},
	}...)
	if err != nil {
		logger.CtxLog(ctx).Errorf("failed to insert plc-record data to mongodb: %v, project: %s, service_id: %s, device_id: %s", err, kafkaPLCRecordData.Project, kafkaPLCRecordData.ServiceId, deviceId)
		return err
	}
	return nil
}

func HandlePUS3PLCRecordData(ctx context.Context, deviceId string, kafkaPLCRecordData KafkaPLC) (err error) {
	plcRecordsData := make([]interface{}, 0)
	for _, item := range kafkaPLCRecordData.Data {
		var mPLCRecordData mmgo.RestoredPUS3PLCData
		details := strings.Split(strings.TrimSuffix(item, ";"), ";")
		if len(details) < 40 || len(details) > 42 {
			// format: ts;mode,bcStep,plStep;axisIndex,position,speed,torque;[1-40]
			// ignore and skip if the required data is missing
			// 38,39 is reserved, temporarily unused
			logger.CtxLog(ctx).Warnf("device_id: %s, plc record format is incorrect, si: %s, data: %v", deviceId, kafkaPLCRecordData.ServiceId, item)
			continue
		}
		if len(details[0]) != 13 {
			logger.CtxLog(ctx).Warnf("device_id: %s, timestamp format is incorrect, si: %s, timestamp: %s", deviceId, kafkaPLCRecordData.ServiceId, details[0])
			continue
		}
		first, _ := strconv.ParseInt(details[0], 10, 64)
		mPLCRecordData.Ts = time.UnixMilli(first)
		second := strings.Split(details[1], ",")
		if len(second) != 3 {
			// ignore and skip if the required data is missing
			continue
		}
		mode, _ := strconv.Atoi(second[0])
		mPLCRecordData.Mode = mode
		bcs, _ := strconv.Atoi(second[1])
		mPLCRecordData.BcStepNum = bcs
		pls, _ := strconv.Atoi(second[2])
		mPLCRecordData.PlStepNum = pls
		//p.logger.Infof("consume plc record - project: %s, si: %s, ts: %d, bc_step_num: %d, pl_step_num: %d", kafkaPLCRecordData.Project, kafkaPLCRecordData.ServiceId, first, bcs, pls)

		for _, axis := range details[2:] {
			content := strings.Split(axis, ",")
			if len(content) != 4 {
				// keep the default value: 0 for the current axis if the required data is missing
				continue
			}
			index, err := strconv.Atoi(content[0])
			if err != nil {
				// keep the default value: 0 for the current axis if failed to parse the number of it
				continue
			}
			switch index {
			case 1:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.ForkXPosition = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.ForkXSpeed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.ForkXTorque = int32(gt)
			case 2:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.StackerMovePosition = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.StackerMoveSpeed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.StackerMoveTorque = int32(gt)
			case 3:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.StackerLiftPosition = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.StackerLiftSpeed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.StackerLiftTorque = int32(gt)
			case 4:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun1Position = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun1Speed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun1Torque = int32(gt)
			case 5:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun2Position = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun2Speed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun2Torque = int32(gt)
			case 6:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun3Position = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun3Speed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun3Torque = int32(gt)
			case 7:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun4Position = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun4Speed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun4Torque = int32(gt)
			case 8:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun5Position = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun5Speed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun5Torque = int32(gt)
			case 9:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun6Position = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun6Speed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun6Torque = int32(gt)
			case 10:
				gp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun7Position = gp
				gs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun7Speed = int32(gs)
				gt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun7Torque = int32(gt)
			case 11:
				rplp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun8Position = rplp
				rpls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun8Speed = int32(rpls)
				rplt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun8Torque = int32(rplt)
			case 12:
				rplp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun9Position = rplp
				rpls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun9Speed = int32(rpls)
				rplt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun9Torque = int32(rplt)
			case 13:
				lffp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun10Position = lffp
				lffs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun10Speed = int32(lffs)
				lfft, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun10Torque = int32(lfft)
			case 14:
				rffp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun11Position = rffp
				rffs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun11Speed = int32(rffs)
				rfft, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun11Torque = int32(rfft)
			case 15:
				lrfp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun12Position = lrfp
				lrfs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun12Speed = int32(lrfs)
				lrft, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun12Torque = int32(lrft)
			case 16:
				rrfp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Door1Position = rrfp
				rrfs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Door1Speed = int32(rrfs)
				rrft, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Door1Torque = int32(rrft)
			case 17:
				lvp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Door2Position = lvp
				lvs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Door2Speed = int32(lvs)
				lvt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Door2Torque = int32(lvt)
			case 18:
				ldp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun1ZPosition = ldp
				lds, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun1ZSpeed = int32(lds)
				ldt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun1ZTorque = int32(ldt)
			case 19:
				rdp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun2ZPosition = rdp
				rds, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun2ZSpeed = int32(rds)
				rdt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun2ZTorque = int32(rdt)
			case 20:
				cllp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun9XPosition = cllp
				clls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun9XSpeed = int32(clls)
				cllt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun9XTorque = int32(cllt)
			case 21:
				clrp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun9ZPosition = clrp
				clrs, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun9ZSpeed = int32(clrs)
				clrt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun9ZTorque = int32(clrt)
			case 22:
				rlp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun10XPosition = rlp
				rls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun10XSpeed = int32(rls)
				rlt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun10XTorque = int32(rlt)
			case 23:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun10ZPosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun10ZSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun10ZTorque = int32(clt)
			case 24:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun11ZPosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun11ZSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun11ZTorque = int32(clt)
			case 25:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun21ZPosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun21ZSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun21ZTorque = int32(clt)
			case 26:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.LrLfPinPosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.LrLfPinSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.LrLfPinTorque = int32(clt)
			case 27:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.LrLrPinPosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.LrLrPinSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.LrLrPinTorque = int32(clt)
			case 28:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.LrRrPinPosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.LrRrPinSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.LrRrPinTorque = int32(clt)
			case 29:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.LfCalmpPosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.LfCalmpSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.LfCalmpTorque = int32(clt)
			case 30:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.RfCalmpPosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.RfCalmpSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.RfCalmpTorque = int32(clt)
			case 31:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.FGuidePosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.FGuideSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.FGuideTorque = int32(clt)
			case 32:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.VPosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.VSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.VTorque = int32(clt)
			case 33:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.LrCalmpPosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.LrCalmpSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.LrCalmpTorque = int32(clt)
			case 34:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.RrCalmpPosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.RrCalmpSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.RrCalmpTorque = int32(clt)
			case 35:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.RGuidePosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.RGuideSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.RGuideTorque = int32(clt)
			case 36:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.LrMovePosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.LrMoveSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.LrMoveTorque = int32(clt)
			case 37:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.LrLiftPosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.LrLiftSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.LrLiftTorque = int32(clt)
			case 40:
				clp, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.PlLifterPosition = clp
				cls, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.PlLifterSpeed = int32(cls)
				clt, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.PlLifterTorque = int32(clt)
			}
		}
		mPLCRecordData.ServiceId = kafkaPLCRecordData.ServiceId
		mPLCRecordData.Metadata.DeviceId = deviceId
		mPLCRecordData.SendKfkTs = kafkaPLCRecordData.Timestamp
		mPLCRecordData.WriteDbTs = time.Now().UnixMilli()
		mPLCRecordData.Date = time.Now()
		plcRecordsData = append(plcRecordsData, mPLCRecordData)
	}
	err = client.GetWatcher().Mongodb().NewMongoEntry().InsertMany(DBArchiveRestore, CollectionRestoredPLCData, plcRecordsData, []client.IndexOption{
		{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 30 * 24 * 3600},
		{Name: "service_id", Fields: bson.D{{"service_id", 1}}},
	}...)
	if err != nil {
		logger.CtxLog(ctx).Errorf("failed to insert plc-record data to mongodb: %v, project: %s, service_id: %s, device_id: %s", err, kafkaPLCRecordData.Project, kafkaPLCRecordData.ServiceId, deviceId)
		return err
	}
	return nil
}

func HandlePowerSwap2PLCRecordData(ctx context.Context, deviceId string, kafkaPLCRecordData KafkaPLC) (err error) {
	plcRecordsData := make([]interface{}, 0)
	for _, item := range kafkaPLCRecordData.Data {
		var mPLCRecordData mmgo.RestoredPowerSwap2PLCData
		details := strings.Split(strings.TrimSuffix(item, ";"), ";")
		if len(details) != 25 {
			// format: ts;mode,bcStep,plStep;axisIndex,position,speed,torque;[1-23]
			// ignore and skip if the required data is missing
			logger.CtxLog(ctx).Warnf("device_id: %s, plc-record format is incorrect, si: %s, data: %v", deviceId, kafkaPLCRecordData.ServiceId, item)
			continue
		}
		if len(details[0]) != 13 {
			logger.CtxLog(ctx).Warnf("device_id: %s, timestamp format is incorrect, si: %s, timestamp: %s", deviceId, kafkaPLCRecordData.ServiceId, details[0])
			continue
		}
		first, _ := strconv.ParseInt(details[0], 10, 64)
		mPLCRecordData.Ts = time.UnixMilli(first)

		second := strings.Split(details[1], ",")
		if len(second) != 3 {
			// ignore and skip if the required data is missing
			continue
		}
		mode, _ := strconv.Atoi(second[0])
		mPLCRecordData.Mode = mode
		bcs, _ := strconv.Atoi(second[1])
		mPLCRecordData.BcStepNum = bcs
		pls, _ := strconv.Atoi(second[2])
		mPLCRecordData.PlStepNum = pls
		//p.logger.Infof("consume plc record - project: %s, si: %s, ts: %d, bc_step_num: %d, pl_step_num: %d", kafkaPLCRecordData.Project, kafkaPLCRecordData.ServiceId, first, bcs, pls)

		for _, axis := range details[2:] {
			content := strings.Split(axis, ",")
			if len(content) != 4 {
				// keep the default value: 0 for the current axis if the required data is missing
				continue
			}
			index, err := strconv.Atoi(content[0])
			if err != nil {
				// keep the default value: 0 for the current axis if failed to parse the number of it
				continue
			}
			switch index {
			case 1:
				position, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun1Position = position
				speed, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun1Speed = int32(speed)
				torque, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun1Torque = int32(torque)
			case 2:
				position, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun2Position = position
				speed, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun2Speed = int32(speed)
				torque, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun2Torque = int32(torque)
			case 3:
				position, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun3Position = position
				speed, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun3Speed = int32(speed)
				torque, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun3Torque = int32(torque)
			case 4:
				position, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun4Position = position
				speed, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun4Speed = int32(speed)
				torque, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun4Torque = int32(torque)
			case 5:
				position, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun5Position = position
				speed, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun5Speed = int32(speed)
				torque, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun5Torque = int32(torque)
			case 6:
				position, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun6Position = position
				speed, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun6Speed = int32(speed)
				torque, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun6Torque = int32(torque)
			case 7:
				position, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun7Position = position
				speed, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun7Speed = int32(speed)
				torque, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun7Torque = int32(torque)
			case 8:
				position, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun8Position = position
				speed, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun8Speed = int32(speed)
				torque, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun8Torque = int32(torque)
			case 9:
				position, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun9Position = position
				speed, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun9Speed = int32(speed)
				torque, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun9Torque = int32(torque)
			case 10:
				position, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Gun10Position = position
				speed, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Gun10Speed = int32(speed)
				torque, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Gun10Torque = int32(torque)
			case 11:
				position, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.RgvPinleft11Position = position
				speed, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.RgvPinleft11Speed = int32(speed)
				torque, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.RgvPinleft11Torque = int32(torque)
			case 12:
				position, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.RgvPinleft12Position = position
				speed, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.RgvPinleft12Speed = int32(speed)
				torque, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.RgvPinleft12Torque = int32(torque)
			case 13:
				position, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.LfFix13Position = position
				speed, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.LfFix13Speed = int32(speed)
				torque, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.LfFix13Torque = int32(torque)
			case 14:
				position, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.RfFix14Position = position
				speed, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.RfFix14Speed = int32(speed)
				torque, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.RfFix14Torque = int32(torque)
			case 15:
				position, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.LrFix15Position = position
				speed, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.LrFix15Speed = int32(speed)
				torque, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.LrFix15Torque = int32(torque)
			case 16:
				position, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.RrFix16Position = position
				speed, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.RrFix16Speed = int32(speed)
				torque, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.RrFix16Torque = int32(torque)
			case 17:
				position, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.LrVslot17Position = position
				speed, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.LrVslot17Speed = int32(speed)
				torque, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.LrVslot17Torque = int32(torque)
			case 18:
				position, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Leftdoor18Position = position
				speed, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Leftdoor18Speed = int32(speed)
				torque, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Leftdoor18Torque = int32(torque)
			case 19:
				position, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.Rightdoor19Position = position
				speed, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.Rightdoor19Speed = int32(speed)
				torque, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.Rightdoor19Torque = int32(torque)
			case 20:
				position, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.CarLiftLeft20Position = position
				speed, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.CarLiftLeft20Speed = int32(speed)
				torque, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.CarLiftLeft20Torque = int32(torque)
			case 21:
				position, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.CarLiftRight21Position = position
				speed, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.CarLiftRight21Speed = int32(speed)
				torque, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.CarLiftRight21Torque = int32(torque)
			case 22:
				position, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.RgvLift22Position = position
				speed, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.RgvLift22Speed = int32(speed)
				torque, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.RgvLift22Torque = int32(torque)
			case 23:
				position, _ := strconv.ParseInt(content[1], 10, 64)
				mPLCRecordData.CabLift23Position = position
				speed, _ := strconv.ParseInt(content[2], 10, 32)
				mPLCRecordData.CabLift23Speed = int32(speed)
				torque, _ := strconv.ParseInt(content[3], 10, 32)
				mPLCRecordData.CabLift23Torque = int32(torque)
			}
		}
		mPLCRecordData.ServiceId = kafkaPLCRecordData.ServiceId
		mPLCRecordData.Metadata.DeviceId = deviceId
		mPLCRecordData.SendKfkTs = kafkaPLCRecordData.Timestamp
		mPLCRecordData.WriteDbTs = time.Now().UnixMilli()
		mPLCRecordData.Date = time.Now()
		plcRecordsData = append(plcRecordsData, mPLCRecordData)
	}
	err = client.GetWatcher().Mongodb().NewMongoEntry().InsertMany(DBArchiveRestore, CollectionRestoredPLCData, plcRecordsData, []client.IndexOption{
		{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 30 * 24 * 3600},
		{Name: "service_id", Fields: bson.D{{"service_id", 1}}},
	}...)
	if err != nil {
		logger.CtxLog(ctx).Errorf("failed to insert plc-record data to mongodb: %v, project: %s, service_id: %s, device_id: %s", err, kafkaPLCRecordData.Project, kafkaPLCRecordData.ServiceId, deviceId)
		return err
	}
	return nil
}
