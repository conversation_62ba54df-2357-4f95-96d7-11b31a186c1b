package alarm

import (
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
)

func convertAlarmPO2DO(po umw.MongoAlarmRecord, project string) AlarmDO {
	alarm := AlarmDO{
		AlarmType: po.AlarmType,
		DeviceId:  po.DeviceId,
		Project:   project,
		CreateTs:  po.CreateTS,
		ClearTs:   po.ClearTS,
		DataId:    po.DataId,
		State:     po.State,
		IsStuck:   false,
	}
	alarmInfo, ok := cache.DeviceAlarmInfoCache.GetSingleDeviceAlarm(project, po.DataId)
	if ok {
		alarm.AlarmLevel = alarmInfo.AlarmLevel
		alarm.DataIdDescription = alarmInfo.VarCnName
	}
	_, alarm.IsStuck = cache.StuckAlarmInfoCache.GetSingleStuckAlarm(project, po.DataId)
	return alarm
}
