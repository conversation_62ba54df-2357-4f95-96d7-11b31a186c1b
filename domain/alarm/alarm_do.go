package alarm

import (
	"context"
	"strings"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/client"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
)

const (
	CollectionStuckAlarm = "stuck-alarm"
)

type AlarmDO struct {
	AlarmType         int32
	AlarmLevel        int32
	DeviceId          string
	Project           string
	CreateTs          int64
	ClearTs           int64
	DataId            string
	DataIdDescription string
	State             int32
	IsStuck           bool
}

type ListAlarmCond struct {
	StartTs  int64
	EndTs    int64
	Project  string
	DeviceId *string
	AlarmIds []string
	model.CommonCond
}

// ListAlarms 获取一段时间内的设备告警
func (a *AlarmDO) ListAlarms(ctx context.Context, cond ListAlarmCond) (alarms []AlarmDO, total int64, err error) {
	filter := bson.D{{"create_ts", bson.M{"$gte": cond.StartTs, "$lt": cond.EndTs}}}
	if cond.DeviceId != nil {
		filter = append(filter, bson.E{Key: "device_id", Value: *cond.DeviceId})
	}
	if len(cond.AlarmIds) > 0 {
		filter = append(filter, bson.E{Key: "data_id", Value: bson.M{"$in": cond.AlarmIds}})
	}
	opts := options.Find().SetSort(bson.M{"create_ts": -1})
	if cond.Page != 0 && cond.Size != 0 {
		opts = opts.SetSkip((cond.Page - 1) * cond.Size).SetLimit(cond.Size)
	}
	var res []umw.MongoAlarmRecord
	total, err = client.GetWatcher().Mongodb().NewMongoEntry(filter).FindMany(umw.AlarmInfo, strings.ToLower(cond.Project), opts, &res)
	if err != nil {
		log.CtxLog(ctx).Errorf("ListAlarms, fail to find alarms: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return
	}
	for _, record := range res {
		alarms = append(alarms, convertAlarmPO2DO(record, cond.Project))
	}
	return
}
