package alarm

import (
	"fmt"
	"net/http"
	"os"
	"testing"

	"github.com/gin-gonic/gin"
	gocache "github.com/patrickmn/go-cache"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	ulog "git.nevint.com/golang-libs/common-utils/logger"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
)

var w client.Watcher
var ctx *gin.Context

func init() {
	os.Setenv("APOLLO_META", "https://apollo-config-cn-stg.nioint.com")
	os.Setenv("ENV", "stg")
	cfg := &ucfg.Config{}
	ucfg.GetConfigApollo("config.json", true, cfg, ucfg.NeedValidate(true), ucfg.ServiceName("ppd-welkin-backend"), ucfg.Cluster(ucfg.GetWelkinApolloCluster()))
	defer ulog.Final()
	cfg.Log.DirPath = "/tmp/logs"
	config.Cfg = cfg
	log.Init(cfg.Log, true)
	logger := log.Logger.Named("device")
	w = client.NewWatcherByParam(config.Cfg, logger)
	var err error
	if err != nil {
		panic(err)
	}
	cache.CacheRealtimeDataID(w.Mongodb().Client)
	cache.PowerSwapCache = &cache.DeviceInfoCache{
		Cache:         gocache.New(gocache.NoExpiration, gocache.NoExpiration),
		ResourceCache: gocache.New(gocache.NoExpiration, gocache.NoExpiration),
	}
	cache.PowerSwapCache.RefreshDeviceInfoCache(w.Mongodb().Client)

	cache.InitAlarmInfoCache()
	// 挂车告警缓存
	cache.StuckAlarmInfoCache.RefreshStuckAlarmInfoCache(w.Mongodb().Client)
	// 设备告警缓存
	cache.DeviceAlarmInfoCache.RefreshDeviceAlarmInfoCache(w.Mongodb().Client)

	ctx = &gin.Context{Request: &http.Request{Header: make(http.Header)}}
	ctx.Request.Header.Set("X-Request-ID", "test")
}

func TestAlarm_ListAlarms(t *testing.T) {
	alarm := &AlarmDO{}
	deviceId := "PS-NIO-efd6e196-0fb4a06c"
	cond := ListAlarmCond{
		StartTs:  1740758400000,
		EndTs:    1742400000000,
		Project:  umw.PUS3,
		DeviceId: &deviceId,
		AlarmIds: []string{"726141"},
		CommonCond: model.CommonCond{
			Page: 1,
			Size: 10,
		},
	}
	res, total, err := alarm.ListAlarms(ctx, cond)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(total, ucmd.ToJsonStrIgnoreErr(res))
}
