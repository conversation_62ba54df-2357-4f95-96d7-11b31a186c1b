package powercharger_event

import (
	"context"
	"encoding/json"
	"errors"
	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	"git.nevint.com/welkin2/welkin-backend/client"
	"go.mongodb.org/mongo-driver/bson"
)

var EventSourceEventId2CnName map[string]map[string]string = map[string]map[string]string{
	"device": {
		"805000": "站外充电插枪",
		"805001": "站外充电拔枪",
		"805002": "站外充电车桩鉴权",
		"805003": "站外充电服务开始",
		"805004": "站外充电服务结束",
		"805005": "站外充电充电复位",
		"805006": "站外充电服务中",
		"805007": "刷NFC卡事件",
		"805008": "车辆驶入事件",
		"805009": "车辆驶离事件",
	},
	"shaman": {
		"order_create":         "订单创建",
		"order_success_finish": "订单成功完成",
		"order_cancel":         "订单取消",
		"order_fail_finish":    "订单失败完成",
	},
}

type PowerChargerEventDO struct {
	DeviceId       string
	ResourceId     string
	EventSource    string
	EventId        string
	EventName      string
	EventTimestamp int64
	Context        map[string]string
}

type ListPowerChargerEventsCond struct {
	Project        string
	DeviceId       string
	ResourceId     string
	StartTs        int64
	EndTs          int64
	OrderFieldName string
	Descending     bool
	Limit          int64
	Offset         int64
}

func (s *PowerChargerEventDO) ListServiceEvents(ctx context.Context, cond ListPowerChargerEventsCond) ([]*PowerChargerEventDO, int64, error) {
	if cond.Project == "" {
		return nil, 0, errors.New("project param must have!")
	}
	collectionName := ucmd.RenameProjectDB(cond.Project)
	filter := bson.D{}
	if cond.DeviceId != "" {
		filter = append(filter, bson.E{Key: "device_id", Value: cond.DeviceId})
	}
	if cond.ResourceId != "" {
		filter = append(filter, bson.E{Key: "resource_id", Value: cond.ResourceId})
	}
	if cond.StartTs != 0 || cond.EndTs != 0 {
		filter = append(filter, bson.E{Key: "event_timestamp", Value: bson.M{"$gte": cond.StartTs, "$lt": cond.EndTs}})
	}
	sortFieldName := "event_timestamp"
	if cond.OrderFieldName != "" {
		sortFieldName = cond.OrderFieldName
	}
	simulationByteData, total, err := client.GetWatcher().Mongodb().NewMongoEntry(filter).ListByPagination("power_charge_event", collectionName,
		client.Pagination{Limit: cond.Limit, Offset: cond.Offset},
		client.Ordered{Key: sortFieldName, Descending: cond.Descending})
	if err != nil {
		return nil, 0, err
	}
	var swapEventPOs []umw.MongoPowerChargerEvent
	if err = json.Unmarshal(simulationByteData, &swapEventPOs); err != nil {
		return nil, 0, err
	}
	serviceEventDOs := []*PowerChargerEventDO{}
	for _, swapEventPO := range swapEventPOs {
		serviceEventDOs = append(serviceEventDOs, convertPowerChargerEventPO2DO(swapEventPO))
	}
	return serviceEventDOs, total, nil
}
