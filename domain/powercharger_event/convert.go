package powercharger_event

import (
	"fmt"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
)

func convertPowerChargerEventPO2DO(powerChargerEventPO umw.MongoPowerChargerEvent) *PowerChargerEventDO {
	context := map[string]string{}
	for _, eventContext := range powerChargerEventPO.EventContext {
		context[eventContext.Key] = eventContext.Value
	}
	powerChargerEventDO := &PowerChargerEventDO{
		DeviceId:       powerChargerEventPO.DeviceId,
		ResourceId:     powerChargerEventPO.ResourceId,
		EventSource:    powerChargerEventPO.EventSource,
		EventId:        powerChargerEventPO.EventId,
		EventName:      eventId2CnName(powerChargerEventPO.EventSource, powerChargerEventPO.EventId),
		EventTimestamp: powerChargerEventPO.EventTimestamp,
		Context:        context,
	}
	return powerChargerEventDO
}

func eventId2CnName(eventSource, eventId string) string {
	_, found := EventSourceEventId2CnName[eventSource]
	if !found {
		return fmt.Sprintf("未知事件(%v)", eventId)
	}
	cnName, found := EventSourceEventId2CnName[eventSource][eventId]
	if !found {
		return fmt.Sprintf("未知事件(%v)", eventId)
	}
	return cnName
}
