package common

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/rs/xid"
	"io"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
)

func FindElectricityPrice(ctx context.Context, request GetElectricityPriceRequest) (total int64, res []ElectricityPriceData, err error) {
	month := time.UnixMilli(request.Ts).Format("2006-01")
	filter := bson.D{{"month", month}}
	if request.FuzzyProvince != nil {
		filter = append(filter, bson.E{Key: "province_name", Value: bson.M{"$regex": *request.FuzzyProvince}})
	}
	if request.FuzzyDistrict != nil {
		filter = append(filter, bson.E{Key: "district_split", Value: bson.M{"$regex": *request.FuzzyDistrict}})
	}
	if request.BillingMethod != nil {
		filter = append(filter, bson.E{Key: "billing_method", Value: *request.BillingMethod})
	}
	if request.VoltageLevel != nil {
		filter = append(filter, bson.E{Key: "voltage_level", Value: *request.VoltageLevel})
	}
	if request.PriceType != nil {
		filter = append(filter, bson.E{Key: "price_type", Value: *request.PriceType})
	}
	var priceData []model.MongoElectricityPriceData
	opts := options.Find().SetSort(bson.D{{"province_id", 1}, {"district_split", 1}})
	total, err = client.GetWatcher().PLCMongodb().NewMongoEntry(filter).FindMany(umw.Algorithm, CollectionElectricityPrice, opts, &priceData)
	if err != nil {
		return
	}
	res = ConvertElectricityPricePO2VO(priceData)
	return
}

func QueryPeople(c context.Context, fuzzyName string) (peopleList []model.People, total int, err error) {
	ts := time.Now().Unix()
	path := "/people/v1/employee/query"
	sign := ucmd.Sign{
		Timestamp: ts,
		AppId:     config.Cfg.Sentry.AppId,
		AppSecret: config.Cfg.Sentry.AppSecret,
		Method:    "GET",
		Path:      path,
		URLParams: map[string]interface{}{
			"hash_type":  "sha256",
			"fuzzy_name": fuzzyName,
			"limit":      "1000",
		},
	}
	sn := sign.Generate()
	if sn == "" {
		err = fmt.Errorf("gen sign fail")
		log.CtxLog(c).Error(err)
		return
	}
	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL: fmt.Sprintf("%s%s?app_id=%s&sign=%s&timestamp=%d&hash_type=sha256&fuzzy_name=%v&limit=1000",
			config.Cfg.Welkin.PeopleUrl, path, config.Cfg.Sentry.AppId, sn, ts, fuzzyName),
		Method: "GET",
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		log.CtxLog(c).Errorf("QueryPeople: %v", err)
		return
	}
	if statusCode != 200 {
		err = fmt.Errorf("QueryPeople, http status code: %d", statusCode)
		log.CtxLog(c).Error(err)
		return
	}
	defer body.Close()
	data, err := io.ReadAll(body)
	if err != nil {
		log.CtxLog(c).Errorf("QueryPeople: %v", err)
		return
	}
	var queryPeopleResp struct {
		ResultCode string `json:"result_code"`
		Data       struct {
			Amount int `json:"amount"`
			List   []struct {
				EmployeeId         string  `json:"employee_id"`
				WorkerUserId       string  `json:"worker_user_id"`
				Name               string  `json:"name"`
				Domain             string  `json:"domain"`
				EmployeeStatus     string  `json:"employee_status"`
				DataDifference     string  `json:"data_difference"`
				EmployeeDifference string  `json:"employee_difference"`
				Country            *string `json:"country"`
				Area               *string `json:"area"`
			} `json:"list"`
		} `json:"data"`
		Message    string `json:"message"`
		DebugMsg   string `json:"debug_msg"`
		DisplayMsg string `json:"display_msg"`
		RequestId  string `json:"request_id"`
		Path       string `json:"path"`
		TxId       string `json:"tx_id"`
		ServerTime int    `json:"server_time"`
	}
	err = json.Unmarshal(data, &queryPeopleResp)
	if err != nil {
		log.CtxLog(c).Errorf("QueryPeople: fail to unmarshal response: %v, body: %s", err, string(data))
		return
	}
	if queryPeopleResp.ResultCode != "success" {
		err = fmt.Errorf("QueryPeople: result code not success, body: %s", string(data))
		log.CtxLog(c).Error(err)
		return
	}
	total = queryPeopleResp.Data.Amount
	for _, people := range queryPeopleResp.Data.List {
		country := ""
		area := ""
		if people.Country != nil {
			country = *people.Country
		}
		if people.Area != nil {
			area = *people.Area
		}
		peopleList = append(peopleList, model.People{
			EmployeeId:         people.EmployeeId,
			WorkerUserId:       people.WorkerUserId,
			Name:               people.Name,
			Domain:             people.Domain,
			EmployeeStatus:     people.EmployeeStatus,
			DataDifference:     people.DataDifference,
			EmployeeDifference: people.EmployeeDifference,
			Country:            country,
			Area:               area,
		})
	}
	return
}

func issueWorksheetByAdapter(ctx context.Context, ruleCode, userId string, worksheetInfo []map[string]interface{}) (err error) {
	url := fmt.Sprintf("%s/pe/platform/adapter/v1/adapter/batch?app_id=1", config.Cfg.OSS.PowUrl)
	batchData := make([]map[string]interface{}, 0)
	for _, record := range worksheetInfo {
		batchData = append(batchData, map[string]interface{}{
			"nonce": xid.New().String(),
			"biz_info": map[string]interface{}{
				"worksheet_creator": "welkin",
				"content":           record,
			},
		})
	}
	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:    url,
		Method: "POST",
		Header: map[string]string{
			"X-User-Id":    userId,
			"Content-Type": "application/json",
		},
		RequestBody: map[string]interface{}{
			"rule_code":  ruleCode,
			"batch_data": batchData,
		},
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		return
	}
	defer body.Close()
	data, _ := io.ReadAll(body)
	if statusCode/100 != 2 {
		return fmt.Errorf("status code: %d, err: %s", statusCode, string(data))
	}
	var resp model.CreateWorksheetResponse
	if err = json.Unmarshal(data, &resp); err != nil {
		return fmt.Errorf("fail to unmarshal response, err: %v, body: %s", err, string(data))
	}
	if resp.ResultCode != "success" {
		return fmt.Errorf("fail to issue worksheet, server err: %s", string(data))
	}
	log.CtxLog(ctx).Infof("IssueWorksheetByAdapter success, resp data: %s", ucmd.ToJsonStrIgnoreErr(resp.Data))
	return
}

// IssueWorksheetByAdapter 通过装配器下发工单
func IssueWorksheetByAdapter(ctx context.Context, ruleCode, userId string, worksheetInfo []map[string]interface{}) (err error) {
	batchSize := 10
	// 按照batchSize分批下发工单
	for i := 0; i < len(worksheetInfo); i += batchSize {
		end := i + batchSize
		if end > len(worksheetInfo) {
			end = len(worksheetInfo)
		}
		if err = issueWorksheetByAdapter(ctx, ruleCode, userId, worksheetInfo[i:end]); err != nil {
			return
		}
	}
	return
}

type FindWeatherTemperatureByRegionCond struct {
	Days     []string
	RegionId string
}

// FindWeatherTemperatureByRegion 查询指定日期、地区的天气温度
func FindWeatherTemperatureByRegion(ctx context.Context, cond FindWeatherTemperatureByRegionCond) (res []WeatherTemperatureInfo, err error) {
	filter := bson.D{
		{"day", bson.M{"$in": cond.Days}},
		{"region_id", cond.RegionId},
	}
	_, err = client.GetWatcher().Mongodb().NewMongoEntry(filter).FindMany(umw.Algorithm, CollectionWeatherTemperature, options.Find().SetSort(bson.M{"day": 1}), &res)
	return
}
