package common

import (
	um "git.nevint.com/golang-libs/common-utils/model"

	"git.nevint.com/welkin2/welkin-backend/model"
)

const (
	CollectionElectricityPrice   = "electricity_price"
	CollectionWeatherTemperature = "weather_temperature"

	PeopleAreaEU = "EU"
	PeopleAreaCN = "CN"

	WorksheetAdapterDeviceVersion = "device_version_update"
)

type GetElectricityPriceRequest struct {
	Ts            int64   `json:"ts" form:"ts"`
	FuzzyProvince *string `json:"fuzzy_province" form:"fuzzy_province"`
	FuzzyDistrict *string `json:"fuzzy_district" form:"fuzzy_district"`
	BillingMethod *string `json:"billing_method" form:"billing_method"`
	VoltageLevel  *string `json:"voltage_level" form:"voltage_level"`
	PriceType     *string `json:"price_type" form:"price_type"`
}

type GetElectricityPriceResponse struct {
	um.Base
	Total int64                  `json:"total"`
	Data  []ElectricityPriceData `json:"data"`
}

type ElectricityPriceData struct {
	BillingMethod string                 `json:"billing_method"`
	DistrictSplit string                 `json:"district_split"`
	Month         string                 `json:"month"`
	PriceType     string                 `json:"price_type"`
	ProvinceName  string                 `json:"province_name"`
	VoltageLevel  string                 `json:"voltage_level"`
	PriceInfo     []ElectricityPriceInfo `json:"price_info"`
}

type ElectricityPriceInfo struct {
	PriceStart string  `json:"price_start"`
	AgentPrice float64 `json:"agent_price"`
	PriceTag   string  `json:"price_tag"`
	PriceEnd   string  `json:"price_end"`
	Price      float64 `json:"price"`
}

type GetElectricityByOrganizedResponse struct {
	um.Base
	Month string `json:"month"`
	// 省/直辖市 区 电价类型 电压等级 一部制/两部制
	Data map[string]map[string]map[string]map[string]map[string][]ElectricityPriceInfo `json:"data"`
}

type DeviceVersionWorksheetInfo struct {
	DeviceId             string `json:"device_id"`
	WorksheetName        string `json:"worksheet_name"`
	WorksheetDescription string `json:"worksheet_description"`
	Sop                  string `json:"sop"`
}

type WeatherTemperatureInfo struct {
	Day              string  `json:"day" bson:"day"`
	RegionId         string  `json:"region_id" bson:"region_id"`
	RegionName       string  `json:"region_name" bson:"region_name"`
	CityId           string  `json:"city_id" bson:"city_id"`
	CityName         string  `json:"city_name" bson:"city_name"`
	ProvinceId       string  `json:"province_id" bson:"province_id"`
	ProvinceName     string  `json:"province_name" bson:"province_name"`
	RegionType       string  `json:"region_type" bson:"region_type"`
	WeatherDayCode   string  `json:"weather_day_code" bson:"weather_day_code"`
	WeatherDayName   string  `json:"weather_day_name" bson:"weather_day_name"`
	WeatherNightCode string  `json:"weather_night_code" bson:"weather_night_code"`
	WeatherNightName string  `json:"weather_night_name" bson:"weather_night_name"`
	LowTemperature   float64 `json:"low_temperature" bson:"low_temperature"`
	HighTemperature  float64 `json:"high_temperature" bson:"high_temperature"`
}

// 电池类型
const (
	Capacity70kwh = iota
	Capacity84kwh
	Capacity100kwh
	Capacity75kwh
	Capacity150kwh
	Capacity50kwh
	Capacity60kwh
	Capacity42kwh
	Capacity102kwh
	Capacity85kwh
)

// 电池容量数值
const (
	CapacityValue70kwh  = 70
	CapacityValue84kwh  = 84
	CapacityValue100kwh = 100
	CapacityValue75kwh  = 75
	CapacityValue150kwh = 150
	CapacityValue50kwh  = 50
	CapacityValue60kwh  = 60
	CapacityValue85kwh  = 85
	CapacityValue102kwh = 102
	CapacityValue42kwh  = 42
)

// ConvertBatteryType 将站上传的原始电池类型转为物理容量类型和用户容量类型
// 转换规则：https://nio.feishu.cn/docx/Xowadedmro92GmxtK9FcBqtrnbd
func ConvertBatteryType(originType *int32) (*int32, *int32) {
	var physicalType, userType int32 = -1, -1
	if originType == nil {
		return nil, nil
	}
	switch *originType {
	case 0, 1, 3:
		physicalType, userType = Capacity70kwh, Capacity70kwh
	case 2:
		physicalType, userType = Capacity84kwh, Capacity84kwh
	case 4:
		physicalType, userType = Capacity84kwh, Capacity70kwh
	case 6, 13:
		physicalType, userType = Capacity100kwh, Capacity100kwh
	case 7:
		physicalType, userType = Capacity100kwh, Capacity70kwh
	case 8, 12:
		physicalType, userType = Capacity75kwh, Capacity75kwh
	case 9:
		physicalType, userType = Capacity100kwh, Capacity84kwh
	case 10:
		physicalType, userType = Capacity150kwh, Capacity150kwh
	case 11, 14:
		physicalType, userType = Capacity100kwh, Capacity75kwh
	case 16:
		physicalType, userType = Capacity50kwh, Capacity50kwh
	case 17:
		physicalType, userType = Capacity70kwh, Capacity50kwh
	case 66:
		physicalType, userType = Capacity102kwh, Capacity102kwh
	case 68:
		physicalType, userType = Capacity75kwh, Capacity75kwh
	case 71:
		physicalType, userType = Capacity85kwh, Capacity85kwh
	case 73:
		physicalType, userType = Capacity60kwh, Capacity60kwh
	case 74:
		physicalType, userType = Capacity60kwh, Capacity60kwh
	case 75:
		physicalType, userType = Capacity85kwh, Capacity60kwh
	case 79:
		physicalType, userType = Capacity60kwh, Capacity60kwh
	case 200:
		physicalType, userType = Capacity42kwh, Capacity42kwh
	}
	if physicalType == -1 || userType == -1 {
		return nil, nil
	}
	return &physicalType, &userType
}

func ConvertBattery2PhysicalAndUserCapacity(originType *int32) (*int32, *int32) {
	var physicalType, userType int32 = -1, -1
	if originType == nil {
		return nil, nil
	}
	switch *originType {
	case 0, 1, 3:
		physicalType, userType = CapacityValue70kwh, CapacityValue70kwh
	case 2:
		physicalType, userType = CapacityValue84kwh, CapacityValue84kwh
	case 4:
		physicalType, userType = CapacityValue84kwh, CapacityValue70kwh
	case 6, 13:
		physicalType, userType = CapacityValue100kwh, CapacityValue100kwh
	case 7:
		physicalType, userType = CapacityValue100kwh, CapacityValue70kwh
	case 8, 12:
		physicalType, userType = CapacityValue75kwh, CapacityValue75kwh
	case 9:
		physicalType, userType = CapacityValue100kwh, CapacityValue84kwh
	case 10:
		physicalType, userType = CapacityValue150kwh, CapacityValue150kwh
	case 11, 14:
		physicalType, userType = CapacityValue100kwh, CapacityValue75kwh
	case 16:
		physicalType, userType = CapacityValue50kwh, CapacityValue50kwh
	case 17:
		physicalType, userType = CapacityValue70kwh, CapacityValue50kwh
	case 66:
		physicalType, userType = CapacityValue102kwh, CapacityValue102kwh
	case 68:
		physicalType, userType = CapacityValue75kwh, CapacityValue75kwh
	case 71:
		physicalType, userType = CapacityValue85kwh, CapacityValue85kwh
	case 73:
		physicalType, userType = CapacityValue60kwh, CapacityValue60kwh
	case 74:
		physicalType, userType = CapacityValue60kwh, CapacityValue60kwh
	case 79:
		physicalType, userType = CapacityValue60kwh, CapacityValue60kwh
	case 200:
		physicalType, userType = CapacityValue42kwh, CapacityValue42kwh
	}
	if physicalType == -1 || userType == -1 {
		return nil, nil
	}
	return &physicalType, &userType
}

// ConvertBatteryCapacity 将电池容量类型转为对应的容量数值
func ConvertBatteryCapacity(capacityType *int32) *int32 {
	if capacityType == nil {
		return nil
	}
	var capacity int32 = -1
	switch *capacityType {
	case Capacity70kwh:
		capacity = CapacityValue70kwh
	case Capacity84kwh:
		capacity = CapacityValue84kwh
	case Capacity50kwh:
		capacity = CapacityValue50kwh
	case Capacity60kwh:
		capacity = CapacityValue60kwh
	case Capacity75kwh:
		capacity = CapacityValue75kwh
	case Capacity100kwh:
		capacity = CapacityValue100kwh
	case Capacity150kwh:
		capacity = CapacityValue150kwh
	case Capacity85kwh:
		capacity = CapacityValue85kwh
	case Capacity102kwh:
		capacity = CapacityValue102kwh
	case Capacity42kwh:
		capacity = CapacityValue42kwh
	}
	if capacity == -1 {
		return nil
	}
	return &capacity
}

// ConvertBatteryUserType 将站上传的原始电池类型转为用户可用容量数值
func ConvertBatteryUserType(originType *int32) *int32 {
	_, userType := ConvertBatteryType(originType)
	return ConvertBatteryCapacity(userType)
}

// GetBatteryBrand 根据电池容量数据判断电池对应的车品牌（仅用于psos）
func GetBatteryBrand(capacity int) string {
	if capacity == CapacityValue60kwh || capacity == CapacityValue85kwh {
		return model.EvBrandONVO
	}
	return model.EvBrandNIO
}

// GetBatteryOriginType 根据电池容量获取电池可能的原始类型（电池刷写使用）
func GetBatteryOriginType(capacity int) []string {
	switch capacity {
	case CapacityValue70kwh:
		return []string{"0", "1", "3", "4", "7"}
	case CapacityValue84kwh:
		return []string{"2", "9"}
	case CapacityValue100kwh:
		return []string{"6", "13"}
	case CapacityValue75kwh:
		return []string{"8", "11", "12", "14", "68"}
	case CapacityValue150kwh:
		return []string{"10"}
	case CapacityValue50kwh:
		return []string{"16", "17"}
	case CapacityValue85kwh:
		return []string{"71"}
	case CapacityValue60kwh:
		return []string{"73", "74", "79"}
	}
	return nil
}
