package common

import (
	"context"
	"fmt"
	"os"
	"testing"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	ulog "git.nevint.com/golang-libs/common-utils/logger"

	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	log "git.nevint.com/welkin2/welkin-backend/logger"
)

var w client.Watcher

func init() {
	os.Setenv("APOLLO_META", "https://apollo-config-cn-stg.nioint.com")
	os.Setenv("ENV", "stg")
	cfg := &ucfg.Config{}
	ucfg.GetConfigApollo("config.json", true, cfg, ucfg.NeedValidate(true), ucfg.ServiceName("ppd-welkin-backend"), ucfg.Cluster(ucfg.GetWelkinApolloCluster()))
	defer ulog.Final()
	cfg.Log.DirPath = "/tmp/logs"
	config.Cfg = cfg
	log.Init(cfg.Log, true)
	logger := log.Logger.Named("common")
	w = client.NewWatcherByParam(config.Cfg, logger)
	var err error
	if err != nil {
		panic(err)
	}
}

func TestIssueWorksheetByAdapter(t *testing.T) {
	ctx := context.Background()
	ruleCode := "testRuleCode"
	userId := "testUserId"
	worksheetInfo := []map[string]interface{}{
		{"key1": "value1"},
		{"key2": "value2"},
		{"key3": "value3"},
		{"key4": "value4"},
		{"key5": "value5"},
		{"key6": "value6"},
		{"key7": "value7"},
		{"key8": "value8"},
		{"key9": "value9"},
		{"key10": "value10"},
		{"key11": "value11"},
	}

	err := IssueWorksheetByAdapter(ctx, ruleCode, userId, worksheetInfo)
	if err != nil {
		t.Errorf("IssueWorksheetByAdapter failed: %v", err)
	}
}

func TestFindWeatherTemperature(t *testing.T) {
	ctx := context.Background()
	testF := func(regionId string) {
		cond := FindWeatherTemperatureByRegionCond{
			Days:     []string{"20250225"},
			RegionId: regionId,
		}
		res, err := FindWeatherTemperatureByRegion(ctx, cond)
		if err != nil {
			panic(err)
		}
		fmt.Println(len(res), ucmd.ToJsonStrIgnoreErr(res))
	}
	testF("120113")
	testF("371311")
}
