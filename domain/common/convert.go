package common

import (
	"strconv"

	"git.nevint.com/welkin2/welkin-backend/model"
)

func ConvertElectricityPricePO2VO(po []model.MongoElectricityPriceData) []ElectricityPriceData {
	res := make([]ElectricityPriceData, 0)
	for _, item := range po {
		priceData := ElectricityPriceData{
			BillingMethod: item.BillingMethod,
			DistrictSplit: item.DistrictSplit,
			Month:         item.Month,
			PriceType:     item.PriceType,
			ProvinceName:  item.ProvinceName,
			VoltageLevel:  item.VoltageLevel,
			PriceInfo:     nil,
		}
		for _, p := range item.PriceInfo {
			agentPrice, _ := strconv.ParseFloat(p.AgentPrice, 64)
			price, _ := strconv.ParseFloat(p.Price, 64)
			priceData.PriceInfo = append(priceData.PriceInfo, ElectricityPriceInfo{
				PriceStart: p.PriceStart,
				AgentPrice: agentPrice,
				PriceTag:   p.PriceTag,
				PriceEnd:   p.PriceEnd,
				Price:      price,
			})
		}
		res = append(res, priceData)
	}
	return res
}
