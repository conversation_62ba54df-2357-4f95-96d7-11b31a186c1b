package model

import (
	"database/sql"
	"fmt"
	"reflect"
	"strings"
	"sync"
)

var (
	diOnce       sync.Once
	diVarNameMap = make(map[string]int)
)

func GetDIVarNameMap() map[string]int {
	diOnce.Do(func() {
		diVarNameMap["emergency_stop_switch_01"] = 0
		diVarNameMap["liq_level_warning"] = 1
		idx := 2
		for i := 1; i <= 24; i, idx = i+1, idx+1 {
			diVarNameMap[fmt.Sprintf("I_power_st_%d", i)] = idx
		}
		for i := 1; i <= 10; i, idx = i+1, idx+1 {
			if i >= 3 && i <= 5 {
				diVarNameMap[fmt.Sprintf("A01_A0%d_check", i)] = idx
			} else {
				diVarNameMap[fmt.Sprintf("A01_A%d_check", i)] = idx
			}
		}
		for i := 1; i <= 5; i, idx = i+1, idx+1 {
			diVarNameMap[fmt.Sprintf("A02_A%d_module_status", i)] = idx
		}
		diVarNameMap["A02_A06_module_check"] = idx
	})
	return diVarNameMap
}

func GetFieldName(obj interface{}) (res []string, err error) {
	refType := reflect.TypeOf(obj)
	refValue := reflect.ValueOf(obj)
	if refValue.Kind() == reflect.Struct {
		for i := 0; i < refType.NumField(); i++ {
			f := refType.Field(i)
			tags := strings.Split(string(f.Tag), " ")
			var name string
			for _, tag := range tags {
				if strings.HasPrefix(tag, "json") {
					name = strings.Split(tag, "\"")[1]
					break
				}
			}
			if len(name) > 0 {
				res = append(res, name)
			} else {
				err = fmt.Errorf("GetFieldName error: missing json tag")
				return
			}
		}
	}
	return
}

type Device2CloudVariableMap struct {
	DataId    string `json:"data_id" bson:"data_id"`
	VarType   string `json:"var_type" bson:"var_type"`
	Variable  string `json:"var_name" bson:"variable"`
	VarCNName string `json:"var_cn_name" bson:"var_cn_name"`
	VarENName string `json:"var_en_name" bson:"var_en_name"`
	Subsystem string `json:"subsystem" bson:"subsystem"`
	Category  string `json:"category" bson:"category"`
	Unit      string `json:"unit" bson:"unit"`
}

type SafeType interface {
	GetRealValue() interface{}
	IsValid() bool
}

type NullInt32 sql.NullInt32

func (ni *NullInt32) Scan(value interface{}) error {
	var i sql.NullInt32
	if err := i.Scan(value); err != nil {
		return err
	}
	if reflect.TypeOf(value) == nil {
		*ni = NullInt32{Int32: i.Int32}
	} else {
		*ni = NullInt32{Int32: i.Int32, Valid: true}
	}
	return nil
}
func (ni NullInt32) GetRealValue() interface{} {
	if ni.Valid {
		return ni.Int32
	}
	return -9999
}

func (ni NullInt32) IsValid() bool {
	return ni.Valid
}

type NullInt64 sql.NullInt64

func (ni *NullInt64) Scan(value interface{}) error {
	var i sql.NullInt64
	if err := i.Scan(value); err != nil {
		return err
	}
	if reflect.TypeOf(value) == nil {
		*ni = NullInt64{Int64: i.Int64}
	} else {
		*ni = NullInt64{Int64: i.Int64, Valid: true}
	}
	return nil
}
func (ni NullInt64) GetRealValue() interface{} {
	if ni.Valid {
		return ni.Int64
	}
	return -9999
}

func (ni NullInt64) IsValid() bool {
	return ni.Valid
}

type NullFloat64 sql.NullFloat64

func (nf *NullFloat64) Scan(value interface{}) error {
	var f sql.NullFloat64
	if err := f.Scan(value); err != nil {
		return err
	}
	if reflect.TypeOf(value) == nil {
		*nf = NullFloat64{Float64: f.Float64}
	} else {
		*nf = NullFloat64{Float64: f.Float64, Valid: true}
	}

	return nil
}
func (nf NullFloat64) GetRealValue() interface{} {
	if nf.Valid {
		return nf.Float64
	}
	return -9999.9
}

func (ni NullFloat64) IsValid() bool {
	return ni.Valid
}

type NullString sql.NullString

func (ns *NullString) Scan(value interface{}) error {
	var s sql.NullString
	if err := s.Scan(value); err != nil {
		return err
	}
	if reflect.TypeOf(value) == nil {
		*ns = NullString{String: s.String}
	} else {
		*ns = NullString{String: s.String, Valid: true}
	}

	return nil
}
func (ns NullString) GetRealValue() interface{} {
	if ns.Valid {
		return ns.String
	}
	return ""
}

func (ni NullString) IsValid() bool {
	return ni.Valid
}
