package model

import (
	"encoding/xml"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PointRecord struct {
	Id                   primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	PublishTs            int64              `json:"publish_ts" bson:"publish_ts"`
	Area                 string             `json:"area" bson:"area"`
	CurrencyUnitName     string             `json:"currency_unit_name" bson:"currency_unit_name"`
	PriceMeasureUnitName string             `json:"price_measure_unit_name" bson:"price_measure_unit_name"`
	Points               []Point            `bson:"points" json:"points"`
}

type Point struct {
	Time        int64   `xml:"position" json:"time" bson:"time"`
	PriceAmount float64 `xml:"price.amount" json:"priceAmount" bson:"priceAmount"`
}
type WindSolarForecastRecord struct {
	Id                      primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	PublishTs               int64              `json:"publish_ts" bson:"publish_ts"`
	Area                    string             `json:"area" bson:"area"`
	QuantityMeasureUnitName string             `json:"quantity_measure_unit_name" bson:"quantity_measure_unit_name"`
	PsrType                 string             `json:"psr_type" bson:"psr_type"`
	Points                  []Point            `json:"points" bson:"points"`
}

type AfrrPrice struct {
	Id        primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	PublishTs int64              `json:"publish_ts" bson:"publish_ts"`
	UnitPrice string             `json:"unit_price" bson:"unit_price"`
	Records   []AfrrRecord       `json:"records" bson:"records"`
}
type AfrrRecord struct {
	DownwardDispatch float32 `bson:"downward_dispatch_price" json:"downward_dispatch" xml:"DOWNWARD_DISPATCH"`
}
type Afrr struct {
	XMLName xml.Name     `xml:"ImbalancePrice"`
	Records []AfrrRecord `xml:"Record"`
}
type ImbalancePrice2 struct {
	Id        primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	PublishTs int64              `json:"publish_ts" bson:"publish_ts"`
	UnitPrice string             `json:"unit_price" bson:"unit_price"`
	MinPrice  *float32           `xml:"MIN_PRICE" json:"min_price" bson:"min_price"`
	MaxPrice  *float32           `xml:"MAX_PRICE" json:"max_price" bson:"max_price"`
	Number    int                `xml:"NUMBER" json:"number" bson:"number"`
}

type ImbalancePriceRecord struct {
	Id        primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	PublishTs int64              `json:"publish_ts" bson:"publish_ts"`
	UnitPrice string             `json:"unit_price" bson:"unit_price"`
	Records   []Record           `json:"records" bson:"records"`
}
type ImbalancePrice struct {
	Id        primitive.ObjectID     `json:"_id,omitempty" bson:"_id,omitempty"`
	PublishTs int64                  `json:"publish_ts" bson:"publish_ts"`
	UnitPrice string                 `json:"unit_price" bson:"unit_price"`
	Records   []RecordImbalancePrice `json:"records" bson:"records"`
}

type BALANCE_DELTA struct {
	XMLName xml.Name `xml:"BALANCE_DELTA"`
	Records []Record `xml:"RECORD"`
}

type Record struct {
	SQUENCE_NUMBER int      `xml:"SEQUENCE_NUMBER" bson:"squence_number" json:"squence_number"`
	Time           string   `xml:"TIME" bson:"time" json:"time"`
	MinPrice       *float32 `xml:"MIN_PRICE" json:"min_price" bson:"min_price"`
	MaxPrice       *float32 `xml:"MAX_PRICE" json:"max_price" bson:"max_price"`
}

type RecordImbalancePrice struct {
	Time     int64    `xml:"TIME" bson:"ts" json:"ts"`
	MinPrice *float32 `xml:"MIN_PRICE" json:"min_price" bson:"min_price"`
	MaxPrice *float32 `xml:"MAX_PRICE" json:"max_price" bson:"max_price"`
}

type TimeSeries struct {
	MRID                    int      `xml:"mRID"`
	BusinessType            string   `xml:"businessType"`
	InDomainMRID            string   `xml:"in_Domain.mRID"`
	OutDomainMRID           string   `xml:"out_Domain.mRID"`
	CurrencyUnitName        string   `xml:"currency_Unit.name"`
	PriceMeasureUnitName    string   `xml:"price_Measure_Unit.name"`
	CurveType               string   `xml:"curveType"`
	QuantityMeasureUnitName string   `xml:"quantity_Measure_Unit.name"`
	MktPSRType              PsrType  `xml:"MktPSRType" bson:"mktPSRType" json:"mktPSRType"`
	Periods                 []Period `xml:"Period" bson:"periods" json:"periods"`
}
type PsrType struct {
	PsrType string `xml:"psrType"`
}

type PublicationMarketDocument struct {
	MRID                 string       `xml:"mRID"`
	RevisionNumber       int          `xml:"revisionNumber"`
	Type                 string       `xml:"type"`
	SenderMRID           string       `xml:"sender_MarketParticipant.mRID"`
	SenderMarketRoleType string       `xml:"sender_MarketParticipant.marketRole.type"`
	ReceiverMRID         string       `xml:"receiver_MarketParticipant.mRID"`
	ReceiverMarketRole   string       `xml:"receiver_MarketParticipant.marketRole.type"`
	CreatedDateTime      string       `xml:"createdDateTime"`
	TimeSeries           []TimeSeries `xml:"TimeSeries"`
}
type Period struct {
	TimeInterval struct {
		Start string `xml:"start"`
		End   string `xml:"end"`
	} `xml:"timeInterval"`
	Resolution string  `xml:"resolution"`
	Points     []Point `xml:"Point"`
}

type FcrdPrice struct {
	Id        primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	PublishTs int64              `json:"publish_ts" bson:"publish_ts"`
	Unit      string             `json:"unit" bson:"unit"`
	Records   []RecordFcrd       `json:"records" bson:"records"`
}
type RecordFcrd struct {
	Time             int64   `bson:"ts" json:"ts"`
	FcrdDownD_1Price float64 `bson:"fcrd_down_d-1_price" json:"fcrd_down_d-1_price" xml:"FCRD_DOWN_D-1_PRICE"`
	FcrdDownD_2Price float64 `bson:"fcrd_down_d-2_price" json:"fcrd_down_d-2_price" xml:"FCRD_DOWN_D-2_PRICE"`
}
