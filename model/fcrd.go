package model

type DataResult struct {
	ProductName string  `json:"productName"`
	Capacity    float64 `json:"capacity"`
	Revenue     float64 `json:"revenue"`
	Sc          float64 `json:"sc"`
	Partner     float64 `json:"partner"`
}

type DataPrice struct {
	Timestamp string  `json:"timestamp"` // RFC3339 format
	D1        float64 `json:"D-1"`
	D2        float64 `json:"D-2"`
	Area      string  `json:"area"`
}

type FcrdRevenueAPIData struct {
	ProductionDate string       `json:"productionDate"`
	InstanceID     string       `json:"instanceId"`
	Regulation     string       `json:"regulation"`
	Results        []DataResult `json:"results"`
	MarketPrice    []DataPrice  `json:"marketPrice"`
}

// FcrdRevenueAPIResponse is the response of the API: truegreen.scnordic.com
type FcrdRevenueAPIResponse struct {
	Message string             `json:"message" bson:"message"`
	Success bool               `json:"success" bson:"success"`
	Data    FcrdRevenueAPIData `json:"data" bson:"data"`
}

type FcrdRevenueHourly struct {
	Project           string  `json:"project" bson:"project"`
	DeviceID          string  `json:"device_id" bson:"device_id"`
	Timestamp         int64   `json:"timestamp" bson:"timestamp"`                     // Unix timestamp
	Date              string  `json:"date" bson:"date"`                               // Date in UTC
	IdealBidCapacity  float64 `json:"ideal_bid_capacity" bson:"ideal_bid_capacity"`   // Ideal bid capacity, unit: MW
	ActualBidCapacity float64 `json:"actual_bid_capacity" bson:"actual_bid_capacity"` // ActBid capacity
	IdealRevenue      int32   `json:"ideal_revenue" bson:"ideal_revenue"`             // Ideal revenue: Ideal bid capacity * price * rate, unit: cent
	Revenue           int32   `json:"actual_revenue" bson:"actual_revenue"`           // Actual revenue: Bid capacity * price * rate, unit: cent
	Price             int32   `json:"price" bson:"price"`                             // Bid Price, D1 or D2, unit: cent
}

func (FcrdRevenueHourly) CollectionName() string {
	return "fcrd_revenue_hourly"
}

type FcrdRevenue struct {
	Project           string  `json:"project" bson:"project"`
	DeviceID          string  `json:"device_id" bson:"device_id"`
	Timestamp         int64   `json:"timestamp" bson:"timestamp"`
	IdealBidCapacity  float64 `json:"ideal_bid_capacity" bson:"ideal_bid_capacity"`   // unit: MW
	ActualBidCapacity float64 `json:"actual_bid_capacity" bson:"actual_bid_capacity"` // unit: MW
	IdealRevenue      int32   `json:"ideal_revenue" bson:"ideal_revenue"`             // currency: EUR, unit: cent
	Revenue           int32   `json:"revenue" bson:"revenue"`                         // currency: EUR, unit: cent
}

func (FcrdRevenue) CollectionName() string {
	return "fcrd_revenue"
}
