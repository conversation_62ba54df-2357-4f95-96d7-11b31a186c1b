package mongo

import (
	"time"
)

type PsosBatteryInfo struct {
	SlotId              int      `json:"slot_id" bson:"slot_id"`
	BatteryId           *string  `json:"battery_id" bson:"battery_id,omitempty"`
	BatteryRatedKwh     *int     `json:"battery_rated_kwh" bson:"battery_rated_kwh,omitempty"`
	RealBatteryRatedKwh *int     `json:"real_battery_rated_kwh" bson:"real_battery_rated_kwh,omitempty"`
	BatterySoc          *float64 `json:"battery_soc" bson:"battery_soc,omitempty"`
	ChargingStopSoc     *float64 `json:"charging_stop_soc" bson:"charging_stop_soc,omitempty"`
	PackMaxTemperature  *float64 `json:"pack_max_temperature" bson:"pack_max_temperature,omitempty"`
	BatteryOwnership    *string  `json:"battery_ownership" bson:"battery_ownership,omitempty"`
}

type ElectricityPriceInfo struct {
	EndHour   string `json:"end_hour" bson:"end_hour"`
	PriceTag  string `json:"price_tag" bson:"price_tag"`
	Price     string `json:"price" bson:"price"`
	StartHour string `json:"start_hour" bson:"start_hour"`
}

type DeviceElectricityPriceData struct {
	DeviceId                  string                 `json:"device_id" bson:"device_id"`
	ElectricityPriceModel     string                 `json:"electricity_price_model" bson:"electricity_price_model"`
	MapHourlyElectricityPrice []ElectricityPriceInfo `json:"map_hourly_electricity_price" bson:"map_hourly_electricity_price"`
	OperationTime             []int                  `json:"operation_time" bson:"operation_time"`
}

type ElectricityDetailUserConfig struct {
	Start string  `json:"start" bson:"start"`
	End   string  `json:"end" bson:"end"`
	Price float64 `json:"price" bson:"price"`
}

type PsosScenarioInfo struct {
	ElectricityPriceModel string                        `json:"electricity_price_model" bson:"electricity_price_model"`
	ElectricityDetails    []float64                     `json:"electricity_details" bson:"electricity_details"`
	ElectricityDetailUser []ElectricityDetailUserConfig `json:"electricity_detail_user" bson:"electricity_detail_user"`
}

type PsosSimulationInfo struct {
	SimulationStartTime int64 `json:"simulation_start_time" bson:"simulation_start_time"`
	SimulationPeriod    int64 `json:"simulation_period" bson:"simulation_period"`
	TimeStep            int32 `json:"time_step" bson:"time_step"`
}

type PsosDeviceInfo struct {
	Platform              string                `json:"platform" bson:"platform"`
	DeviceId              string                `json:"device_id" bson:"device_id"`
	OperationStrategyInfo OperationStrategyInfo `json:"operation_strategy_info" bson:"operation_strategy_info"`
	ChargeSwapSystem      ChargeSwapSystem      `json:"charge_swap_system" bson:"charge_swap_system"`
}

type BatteryRestSwitch struct {
	SwitchValue              int32 `json:"switch_value" bson:"switch_value"`
	DefaultRestLabel         int   `json:"default_rest_label" bson:"default_rest_label"`
	DefaultRestDuration      int   `json:"default_rest_duration" bson:"default_rest_duration"`
	DefaultRestCurrent       int   `json:"default_rest_current" bson:"default_rest_current"`
	DefaultHangingDuration   int   `json:"default_hanging_duration" bson:"default_hanging_duration"`
	DefaultHangingStep       int   `json:"default_hanging_step" bson:"default_hanging_step"`
	DefaultHangingCurrentMax int   `json:"default_hanging_current_max" bson:"default_hanging_current_max"`
}

type OperationStrategyInfo struct {
	BatteryExchangeSwitch BatteryRestSwitch `json:"battery_exchange_switch" bson:"battery_exchange_switch"`
	CmsStrategySwitch     struct {
		SwitchValue        int32 `json:"switch_value" bson:"switch_value"`
		SchedulingInterval int   `json:"scheduling_interval" bson:"scheduling_interval"`
		StrategyValue      []int `json:"strategy_value" bson:"strategy_value"`
	} `json:"cms_strategy_switch" bson:"cms_strategy_switch"`
	SmartChargingSwitch struct {
		SwitchValue   int32 `json:"switch_value" bson:"switch_value"`
		StrategyValue []int `json:"strategy_value" bson:"strategy_value"`
	} `json:"smart_charging_switch" bson:"smart_charging_switch"`
	BatteryRestSwitch           BatteryRestSwitch `json:"battery_rest_switch" bson:"battery_rest_switch"`
	ExchangeDuration            int               `json:"exchange_duration" bson:"exchange_duration"`
	BatteryInfo                 []PsosBatteryInfo `json:"battery_info" bson:"battery_info"`
	PowerDistributionCapacity   int               `json:"power_distribution_capacity" bson:"power_distribution_capacity"`
	CircuitDistributionCapacity []int             `json:"circuit_distribution_capacity" bson:"circuit_distribution_capacity"`
	BranchCircuitCurrentLimit   []int             `json:"branch_circuit_current_limit" bson:"branch_circuit_current_limit"`
	OperationTime               []int             `json:"operation_time" bson:"operation_time"`
	NotfullySwapSwitch          struct {
		SwitchValue int32 `json:"switch_value" bson:"switch_value"`
	} `json:"notfully_swap_switch" bson:"notfully_swap_switch"`
	SilentModeSwitch struct {
		SwitchValue int32 `json:"switch_value" bson:"switch_value"`
	} `json:"silent_mode_switch" bson:"silent_mode_switch"`
	BatteryThermalManagementSwitch struct {
		SwitchValue int32 `json:"switch_value" bson:"switch_value"`
	} `json:"battery_thermal_management_switch" bson:"battery_thermal_management_switch"`
	CellInternalResistanceCalSwitch struct {
		SwitchValue int32 `json:"switch_value" bson:"switch_value"`
	} `json:"cell_internal_resistance_cal_switch" bson:"cell_internal_resistance_cal_switch"`
	ChargingStopSoc50kWh  int       `json:"50kWh_charging_stop_soc" bson:"50kWh_charging_stop_soc"`
	ChargingStopSoc70kWh  int       `json:"70kWh_charging_stop_soc" bson:"70kWh_charging_stop_soc"`
	ChargingStopSoc75kWh  int       `json:"75kWh_charging_stop_soc" bson:"75kWh_charging_stop_soc"`
	ChargingStopSoc100kWh int       `json:"100kWh_charging_stop_soc" bson:"100kWh_charging_stop_soc"`
	ChargingStopSoc150kWh int       `json:"150kWh_charging_stop_soc" bson:"150kWh_charging_stop_soc"`
	ChargingStopSoc60kWh  int       `json:"60kWh_charging_stop_soc" bson:"60kWh_charging_stop_soc"`
	ChargingStopSoc85kWh  int       `json:"85kWh_charging_stop_soc" bson:"85kWh_charging_stop_soc"`
	BatterySocLowerLimit  int       `json:"battery_soc_lower_limit" bson:"battery_soc_lower_limit"`
	BatterySocUpperLimit  int       `json:"battery_soc_upper_limit" bson:"battery_soc_upper_limit"`
	BatteryTypeConf       []float64 `json:"battery_type_conf" bson:"battery_type_conf"`
	BatteryKWhGroup       []int     `json:"battery_kWh_group" bson:"battery_kWh_group"`
	FullyBatteryRatio     float64   `json:"fully_battery_ratio" bson:"fully_battery_ratio"`
}

type ChargeSwapSystem struct {
	CircuitNum                        int              `json:"circuit_num" bson:"circuit_num"`
	BatterySlotNum                    int              `json:"battery_slot_num" bson:"battery_slot_num"`
	ModuleSupplier                    string           `json:"module_supplier" bson:"module_supplier"`
	ModuleNum                         int              `json:"module_num" bson:"module_num"`
	PerSubmoduleNum                   int              `json:"per_submodule_num" bson:"per_submodule_num"`
	SubmodulePowerLimit               int              `json:"submodule_power_limit" bson:"submodule_power_limit"`
	SwapBatterySlot                   []int            `json:"swap_battery_slot" bson:"swap_battery_slot"`
	StorableBatterySlot               []int            `json:"storable_battery_slot" bson:"storable_battery_slot"`
	CircuitLineAllocation             map[string][]int `json:"circuit_line_allocation" bson:"circuit_line_allocation"`
	CoupleStructure                   map[string][]int `json:"couple_structure" bson:"couple_structure"`
	SwappingTimeSuccess               int              `json:"swapping_time_success" bson:"swapping_time_success"`
	SwappingTimeFault                 int              `json:"swapping_time_fault" bson:"swapping_time_fault"`
	NonChargingElectricityConsumption float64          `json:"non_charging_electricity_consumption" bson:"non_charging_electricity_consumption"`
}

type PsosServiceInfo struct {
	SwappingFailureSwitch struct {
		SwitchValue int32 `json:"switch_value" bson:"switch_value"`
	} `json:"swapping_failure_switch" bson:"swapping_failure_switch"`
	SkipLevelSwapSwitch struct {
		SwitchValue int32 `json:"switch_value" bson:"switch_value"`
	} `json:"skip_level_swap_switch" bson:"skip_level_swap_switch"`
	SwappingUserNum  int            `json:"swapping_user_num" bson:"swapping_user_num"`
	SwappingUserList []SwappingUser `json:"swapping_user_list" bson:"swapping_user_list"`
}

type SwappingUser struct {
	ServiceId                 *string `json:"service_id" bson:"service_id,omitempty"`
	VehicleId                 *string `json:"vehicle_id" bson:"vehicle_id,omitempty"`
	UserArrivalTime           int64   `json:"user_arrival_time" bson:"user_arrival_time"`
	SwappingTime              *int    `json:"swapping_time" bson:"swapping_time,omitempty"`
	TargetBatteryRatedKwh     *int    `json:"target_battery_rated_kwh" bson:"target_battery_rated_kwh,omitempty"`
	RealTargetBatteryRatedKwh *int    `json:"real_target_battery_rated_kwh" bson:"real_target_battery_rated_kwh,omitempty"`
	BatteryId                 *string `json:"battery_id" bson:"battery_id,omitempty"`
	BatterySoc                int     `json:"battery_soc" bson:"battery_soc"`
	BatteryRatedKwh           int     `json:"battery_rated_kwh" bson:"battery_rated_kwh"`
	RealBatteryRatedKwh       int     `json:"real_battery_rated_kwh" bson:"real_battery_rated_kwh,omitempty"`
	PackMaxTemperature        *int    `json:"pack_max_temperature" bson:"pack_max_temperature,omitempty"`
	BatteryOwnership          *string `json:"battery_ownership" bson:"battery_ownership,omitempty"`
	UserOwnership             *string `json:"user_ownership" bson:"user_ownership,omitempty"`
	BatteryRestLabel          *int    `json:"battery_rest_label" bson:"battery_rest_label,omitempty"`
}

type FmsCompressTaskInfoByConfig struct {
	DeviceCompressTaskId  string `json:"device_compress_task_id" bson:"device_compress_task_id"`
	BatteryCompressTaskId string `json:"battery_compress_task_id" bson:"battery_compress_task_id"`
	ServiceCompressTaskId string `json:"service_compress_task_id" bson:"service_compress_task_id"`
	DeviceResultUrl       string `json:"device_result_url" bson:"device_result_url"`
	BatteryResultUrl      string `json:"battery_result_url" bson:"battery_result_url"`
	ServiceResultUrl      string `json:"service_result_url" bson:"service_result_url"`
}

type FmsCompressTaskInfo struct {
	DeviceCompressTaskId      string                                 `json:"device_compress_task_id" bson:"device_compress_task_id"`
	BatteryCompressTaskId     string                                 `json:"battery_compress_task_id" bson:"battery_compress_task_id"`
	ServiceCompressTaskId     string                                 `json:"service_compress_task_id" bson:"service_compress_task_id"`
	DeviceResultUrl           string                                 `json:"device_result_url" bson:"device_result_url"`
	BatteryResultUrl          string                                 `json:"battery_result_url" bson:"battery_result_url"`
	ServiceResultUrl          string                                 `json:"service_result_url" bson:"service_result_url"`
	ConfigFmsCompressTaskInfo map[string]FmsCompressTaskInfoByConfig `json:"config_fms_compress_task_info" bson:"config_fms_compress_task_info"` // 按照配方打包
}

type MongoPsosTask struct {
	Id                  string              `json:"_id" bson:"_id"`
	Name                string              `json:"name" bson:"name"`
	Remark              string              `json:"remark" bson:"remark"`
	Project             string              `json:"project" bson:"project"`
	SimulationList      []string            `json:"simulation_list" bson:"simulation_list"`
	Status              string              `json:"status" bson:"status"`
	FmsCompressTaskInfo FmsCompressTaskInfo `json:"fms_compress_task_info" bson:"fms_compress_task_info"`
	Creator             string              `json:"creator" bson:"creator"`
	CreateTs            int64               `json:"create_ts" bson:"create_ts"`
	UpdateTs            int64               `json:"update_ts" bson:"update_ts"`
	Date                time.Time           `json:"date" bson:"date"`
}

type MongoPsosSimulation struct {
	Id               string             `json:"_id" bson:"_id"`
	ConfigId         string             `json:"config_id" bson:"config_id"`
	TaskId           string             `json:"task_id" bson:"task_id"`
	IsBaseConfig     bool               `json:"is_base_config" bson:"is_base_config"`
	IsRealDevice     bool               `json:"is_real_device" bson:"is_real_device"` // 区分当前配方是否根据真实设备生成
	SimulationInfo   PsosSimulationInfo `json:"simulation_info" bson:"simulation_info"`
	DeviceInfo       PsosDeviceInfo     `json:"device_info" bson:"device_info"`
	ServiceInfo      PsosServiceInfo    `json:"service_info" bson:"service_info"`
	ScenarioInfo     PsosScenarioInfo   `json:"scenario_info" bson:"scenario_info"`
	Status           string             `json:"status" bson:"status"`
	Progress         float64            `json:"progress" bson:"progress"`
	DeviceResultUrl  string             `json:"device_result_url" bson:"device_result_url"`
	BatteryResultUrl string             `json:"battery_result_url" bson:"battery_result_url"`
	ServiceResultUrl string             `json:"service_result_url" bson:"service_result_url"`
	// Deprecated
	AvgSwappingQueueTime int64 `json:"avg_swapping_queue_time" bson:"avg_swapping_queue_time"`
	// Deprecated
	AvgBatteryChargingTime int64 `json:"avg_battery_charging_time" bson:"avg_battery_charging_time"`
	// Deprecated
	HourlyAvgCapacityUtilization float64             `json:"hourly_avg_capacity_utilization" bson:"hourly_avg_capacity_utilization"`
	BusinessCalculation          BusinessCalculation `json:"business_calculation" bson:"business_calculation"`
	DevicePerformance            DevicePerformance   `json:"device_performance" bson:"device_performance"`
	UserExperience               UserExperience      `json:"user_experience" bson:"user_experience"`
	StartRunningTs               int64               `json:"start_running_ts" bson:"start_running_ts"`
	FinishTs                     int64               `json:"finish_ts" bson:"finish_ts"`
	CreateTs                     int64               `json:"create_ts" bson:"create_ts"`
	UpdateTs                     int64               `json:"update_ts" bson:"update_ts"`
	Date                         time.Time           `json:"date" bson:"date"`
}

type BusinessCalculation struct {
	MarginalContributionMargin float64    `json:"marginal_contribution_margin" bson:"marginal_contribution_margin"`
	Cost                       PsosCost   `json:"cost" bson:"cost"`
	Income                     PsosIncome `json:"income" bson:"income"`
}

type PsosCost struct {
	TotalCost                    float64   `json:"total_cost" bson:"total_cost"`
	NonChargingElectricityCost   float64   `json:"non_charging_electricity_cost" bson:"non_charging_electricity_cost"`
	BatteryElectricityCost       float64   `json:"battery_electricity_cost" bson:"battery_electricity_cost"`
	AvgBatteryChargingtime       float64   `json:"avg_battery_chargingtime" bson:"avg_battery_chargingtime"`
	HourlyAvgBatteryChargingtime []float64 `json:"hourly_avg_battery_chargingtime" bson:"hourly_avg_battery_chargingtime"`
	HourlyBatteryChargingNums    []float64 `json:"hourly_battery_charging_nums" bson:"hourly_battery_charging_nums"`
	HourlyBatteryElectricityCost []float64 `json:"hourly_battery_electricity_cost" bson:"hourly_battery_electricity_cost"`
}

type PsosIncome struct {
	TotalIncome       float64 `json:"total_income" bson:"total_income"`
	SwapServiceIncome float64 `json:"swap_service_income" bson:"swap_service_income"`
	OffPeakIncome     float64 `json:"Off_peak_income" bson:"Off_peak_income"`
}

type DevicePerformance struct {
	Module           PsosModule       `json:"module" bson:"module"`
	Power            PsosPower        `json:"power" bson:"power"`
	EnergyEfficiency EnergyEfficiency `json:"energy_efficiency" bson:"energy_efficiency"`
}

type EnergyEfficiency struct {
	EnergyEfficiency                  float64 `json:"energy_efficiency" bson:"energy_efficiency"`
	DeviceElectricityConsumption      float64 `json:"device_electricity_consumption" bson:"device_electricity_consumption"`
	BatteryElectricityConsumption     float64 `json:"battery_electricity_consumption" bson:"battery_electricity_consumption"`
	ModuleElectricityConsumption      float64 `json:"module_electricity_consumption" bson:"module_electricity_consumption"`
	NonChargingElectricityConsumption float64 `json:"non_charging_electricity_consumption" bson:"non_charging_electricity_consumption"`
	SctElectricityConsumption         float64 `json:"sct_electricity_consumption" bson:"sct_electricity_consumption"`
}

type PsosModule struct {
	ModuleNums                   float64   `json:"module_nums" bson:"module_nums"`
	ModuleUtilizationRatio       float64   `json:"module_utilization_ratio" bson:"module_utilization_ratio"`
	SingleModuleActualWorkTime   float64   `json:"single_module_actual_work_time" bson:"single_module_actual_work_time"`
	SingleModulePowerLimit       float64   `json:"single_module_power_limit" bson:"single_module_power_limit"`
	SingleModuleRatedWorkTime    float64   `json:"single_module_rated_work_time" bson:"single_module_rated_work_time"`
	HourlyModuleUtilizationRatio []float64 `json:"hourly_module_utilization_ratio" bson:"hourly_module_utilization_ratio"`
}

type PsosPower struct {
	AvgPower             float64   `json:"avg_power" bson:"avg_power"`
	CapacityFactor       float64   `json:"capacity_factor" bson:"capacity_factor"`
	HourlyCapacityFactor []float64 `json:"hourly_capacity_factor" bson:"hourly_capacity_factor"`
	HourlyLoadFactor     []float64 `json:"hourly_load_factor" bson:"hourly_load_factor"`
	LoadFactor           float64   `json:"load_factor" bson:"load_factor"`
	MaxPower             float64   `json:"max_power" bson:"max_power"`
	RatedPower           float64   `json:"rated_power" bson:"rated_power"`
}

type UserExperience struct {
	SwapService PsosSwapService `json:"swap_service" bson:"swap_service"`
}

type PsosSwapService struct {
	SwappingQueuetime           float64   `json:"90_swapping_queuetime" bson:"90_swapping_queuetime"`
	SwappingQueuetimeBattery    float64   `json:"90_swapping_queuetime_battery" bson:"90_swapping_queuetime_battery"`
	SwappingQueuetimeUser       float64   `json:"90_swapping_queuetime_user" bson:"90_swapping_queuetime_user"`
	AvgSwappingQueuetime        float64   `json:"avg_swapping_queuetime" bson:"avg_swapping_queuetime"`
	AvgSwappingQueuetimeBattery float64   `json:"avg_swapping_queuetime_battery" bson:"avg_swapping_queuetime_battery"`
	AvgSwappingQueuetimeUser    float64   `json:"avg_swapping_queuetime_user" bson:"avg_swapping_queuetime_user"`
	HourlyAvgSwappingQueuetime  []float64 `json:"hourly_avg_swapping_queuetime" bson:"hourly_avg_swapping_queuetime"`
	HourlySwappingServiceNums   []float64 `json:"hourly_swapping_service_nums" bson:"hourly_swapping_service_nums"`
	SwappingServiceNums         float64   `json:"swapping_service_nums" bson:"swapping_service_nums"`
	SwappedServiceNums          float64   `json:"swapped_service_nums" bson:"swapped_service_nums"`
}

type MongoPsosServiceInfo struct {
	Id              string    `json:"_id" bson:"_id"`
	ResourceId      string    `json:"resource_id" bson:"resource_id"`
	Project         string    `json:"project" bson:"project"`
	Status          int       `json:"status" bson:"status"`
	Duration        int       `json:"duration" bson:"duration"`
	UserArrivalTime int64     `json:"user_arrival_time" bson:"user_arrival_time"`
	Date            time.Time `json:"date" bson:"date"`
}

type MongoPsosConfig struct {
	Id                string             `json:"_id" bson:"_id,omitempty"`
	ConfigName        string             `json:"config_name" bson:"config_name"`
	ConfigNameOrigin  string             `json:"config_name_origin" bson:"config_name_origin"` // 用户原始输入的配置名
	IsRealDevice      bool               `json:"is_real_device" bson:"is_real_device"`         // 区分当前配方是否根据真实设备生成
	Remark            string             `json:"remark" bson:"remark"`
	Creator           string             `json:"creator" bson:"creator"`
	Project           string             `json:"project" bson:"project"`
	SimulationInfo    PsosSimulationInfo `json:"simulation_info" bson:"simulation_info"`
	DeviceInfo        PsosDeviceInfo     `json:"device_info" bson:"device_info"`
	ServiceInfo       PsosServiceInfo    `json:"service_info" bson:"service_info"`
	ScenarioInfo      PsosScenarioInfo   `json:"scenario_info" bson:"scenario_info"`
	BaseBatteryConfig []float64          `json:"base_battery_config" bson:"base_battery_config"`
	CreateTs          int64              `json:"create_ts" bson:"create_ts,omitempty"`
	UpdateTs          int64              `json:"update_ts" bson:"update_ts"`
	Date              time.Time          `json:"date" bson:"date"`
}

type HourServiceCntRatio struct {
	Hour             int     `json:"hour" bson:"hour"`
	HourServiceRatio float64 `json:"hour_service_ratio" bson:"hour_service_ratio"`
}

type SuccessFail struct {
	Duration    float64 `json:"duration" bson:"duration"`
	SuccessFail int     `json:"success_fail" bson:"success_fail"`
	Ratio       float64 `json:"ratio" bson:"ratio"`
}

type RatioMap map[string]float64
type BatteryTypeRatio map[string][]RatioMap
type HourlyRatio map[string][]RatioMap
type HourlyBatteryTypeRatio map[string][]BatteryTypeRatio

type MongoPsosFeature struct {
	Day                           string                   `json:"day" bson:"day"`
	DeviceId                      string                   `json:"device_id" bson:"device_id"`
	BatteryTempResultMap          []HourlyBatteryTypeRatio `json:"battery_temp_result_map" bson:"battery_temp_result_map"`
	SocResultMap                  []HourlyBatteryTypeRatio `json:"soc_result_map" bson:"soc_result_map"`
	BatteryTypeServiceCntRatioMap []RatioMap               `json:"battery_type_service_cnt_ratio_map" bson:"battery_type_service_cnt_ratio_map"`
	CrossLevelBatterySwappingMap  []BatteryTypeRatio       `json:"cross_level_battery_swapping_map" bson:"cross_level_battery_swapping_map"`
	HourServiceCntRatioMap        []HourServiceCntRatio    `json:"hour_service_cnt_ratio_map" bson:"hour_service_cnt_ratio_map"`
	InBatteryTempRatioMap         []HourlyRatio            `json:"in_battery_temp_ratio_map" bson:"in_battery_temp_ratio_map"`
	StartSocGroupRatioMap         []RatioMap               `json:"start_soc_group_ratio_map" bson:"start_soc_group_ratio_map"`
	SuccessFailMap                []SuccessFail            `json:"success_fail_map" bson:"success_fail_map"`
	AvgNonChargingLoss            float64                  `json:"avg_non_charging_loss" bson:"avg_non_charging_loss"`
	Date                          time.Time                `json:"date" bson:"date"`
}

type PsosModelOutput map[string]interface{}

type PsosResult struct {
	PsosModelOutput `bson:",inline"`
	ChargeStrategy  string    `json:"charge_strategy" bson:"charge_strategy"`
	Day             string    `json:"day" bson:"day"`
	DeviceId        string    `json:"device_id" bson:"device_id"`
	Project         string    `json:"project" bson:"project"`
	DeviceInfo      string    `json:"device_info" bson:"device_info"`
	ServiceInfo     string    `json:"service_info" bson:"service_info"`
	ScenarioInfo    string    `json:"scenario_info" bson:"scenario_info"`
	SimulationInfo  string    `json:"simulation_info" bson:"simulation_info"`
	Date            time.Time `json:"date" bson:"date"`
}
