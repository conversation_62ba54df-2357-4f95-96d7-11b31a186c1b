package mongo

import "time"

const (
	FmsDataBaseName             = "fms"
	FmsTaskStatusCollectionName = "task_status"

	// init	初始化
	// executing	执行中
	// success	成功
	// fail_temp	暂时失败(需要重试)
	// fail_upper	失败次数上限(已达重试上限)
	// fail_forever	永久失败(无需重试)
	FmsTaskStatusInit = "init"
)

type FmsTaskStatus struct {
	TaskId   string    `json:"task_id" bson:"task_id"`
	Status   string    `json:"status" bson:"status"`
	CreateTs int64     `json:"create_ts" bson:"create_ts"`
	UpdateTs int64     `json:"update_ts" bson:"update_ts"`
	Date     time.Time `json:"date" bson:"date"`
}
