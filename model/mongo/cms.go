package mongo

import "time"

type CMSRecordV2 struct {
	RequestID        string             `json:"request_id" bson:"request_id"`
	ModelTriggerTime int64              `json:"model_trigger_time" bson:"model_trigger_time"`
	ModelTriggerType int                `json:"model_trigger_type" bson:"model_trigger_type"`
	DeviceID         string             `json:"device_id" bson:"device_id"`
	DeviceInfo       CMSDeviceInfoV2    `json:"device_info" bson:"device_info"`
	BatteryInfo      []CMSBatteryInfoV2 `json:"battery_info" bson:"battery_info"`
	OpRecord         CMSOpRecordV2      `json:"op_record" bson:"op_record"`
	Date             time.Time          `json:"date" bson:"date"`
}

type CMSDeviceInfoV2 struct {
	ModuleType                  string  `json:"module_type" bson:"module_type"`
	PowerDistributionCapacity   int32   `json:"power_distribution_capacity" bson:"power_distribution_capacity"`
	CircuitDistributionCapacity []int32 `json:"circuit_distribution_capacity" bson:"circuit_distribution_capacity"`
	WaterTankTargetTemp         int32   `json:"water_tank_target_temp" bson:"water_tank_target_temp"`
	WaterTankUpperLimit         int32   `json:"water_tank_upper_limit" bson:"water_tank_upper_limit"`
	WaterTankLowerLimit         int32   `json:"water_tank_lower_limit" bson:"water_tank_lower_limit"`
	EnvTemp                     float64 `json:"env_temp" bson:"env_temp"`
	EnvHumidity                 float64 `json:"env_humidity" bson:"env_humidity"`
	WaterTankTemp               float64 `json:"water_tank_temp" bson:"water_tank_temp"`
	WaterTankLiquidLevel        float64 `json:"water_tank_liquid_level" bson:"water_tank_liquid_level"`
	CompressorStatus            int32   `json:"compressor_status" bson:"compressor_status"`
	HeaterStatus                int32   `json:"heater_status" bson:"heater_status"`
	Longitude                   float64 `json:"longitude" bson:"longitude"`
	Latitude                    float64 `json:"latitude" bson:"latitude"`
}

type CMSBatteryInfoV2 struct {
	SlotID                    int32     `json:"slot_id" bson:"slot_id"`
	BatteryID                 string    `json:"battery_id" bson:"battery_id"`
	BatteryType               int32     `json:"battery_type" bson:"battery_type"`
	Module1Status             int32     `json:"module1_status" bson:"module1_status"`
	Module2Status             int32     `json:"module2_status" bson:"module2_status"`
	BatteryRatedCapacity      int32     `json:"battery_rated_capacity" bson:"battery_rated_capacity"`
	ChargingStatus            int32     `json:"charging_status" bson:"charging_status"`
	BatteryUserSoc            float64   `json:"battery_user_soc" bson:"battery_user_soc"`
	BatteryRealSoc            float64   `json:"battery_real_soc" bson:"battery_real_soc"`
	BranchCircuitCurrentLimit int32     `json:"branch_circuit_current_limit" bson:"branch_circuit_current_limit"`
	ChargingCurrent           float64   `json:"charging_current" bson:"charging_current"`
	ChargingVoltage           float64   `json:"charging_voltage" bson:"charging_voltage"`
	PackMaxTemperature        float64   `json:"pack_max_temperature" bson:"pack_max_temperature"`
	StrategyPower             int       `json:"strategy_power" bson:"strategy_power"`
	EffectivePower            float64   `json:"effective_power" bson:"effective_power"`
	AvailableStatus           int       `json:"available_status" bson:"available_status"`
	MaintStatus               int       `json:"maint_status" bson:"maint_status"`
	MaintPermitLabel          int       `json:"maint_permit_label" bson:"maint_permit_label"`
	BranchWorkStatus          int       `json:"branch_work_status" bson:"branch_work_status"`
	UnchargingReason          int       `json:"uncharging_reason" bson:"uncharging_reason"`
	CurrentLimitingReason     int       `json:"current_limiting_reason" bson:"current_limiting_reason"`
	LaunchStatus              int       `json:"launch_status" bson:"launch_status"`
	RemainingLaunchTime       int       `json:"remaining_launch_time" bson:"remaining_launch_time"`
	MaxLaunchCurrent          int       `json:"max_launch_current" bson:"max_launch_current"`
	RemainingLockTime         int       `json:"remaining_lock_time" bson:"remaining_lock_time"`
	LockCurrent               int       `json:"lock_current" bson:"lock_current"`
	MaintZeta                 []float64 `json:"maint_zeta" bson:"maint_zeta"`
}

type CMSOpRecordV2 struct {
	CmsVersion          string `json:"cms_version" bson:"cms_version"`
	CmsErrCode          int32  `json:"cms_err_code" bson:"cms_err_code"`
	CmsMessage          string `json:"cms_message" bson:"cms_message"`
	StrategyExecCode    int32  `json:"strategy_exec_code" bson:"strategy_exec_code"`
	StrategyExecMessage string `json:"strategy_exec_message" bson:"strategy_exec_message"`
	StrategyExecTime    int64  `json:"strategy_exec_time" bson:"strategy_exec_time"`
}
