package mongo

import "time"

const (
	DBDeviceEvent = "device_event"

	CollectionBatteryRefresh = "battery_refresh"
)

type MongoMechanicalSwapEvent struct {
	DeviceId    string            `json:"device_id" bson:"device_id"`
	Rid         string            `json:"rid" bson:"rid"`
	ServiceId   string            `json:"service_id" bson:"service_id"`
	EventTs     int64             `json:"event_ts" bson:"event_ts"`
	EventSource int               `json:"event_source" bson:"event_source"` // 1:换电站， 2: shanman系统
	EventId     string            `json:"event_id" bson:"event_id"`
	Context     map[string]string `json:"context" bson:"context"`
	Date        time.Time         `json:"date" bson:"date"`
}

type MongoPowerSwapEvent struct {
	DeviceId    string            `json:"device_id" bson:"device_id"`
	EventTs     int64             `json:"event_ts" bson:"event_ts"`
	EventSource int               `json:"event_source" bson:"event_source"` // 1:换电站， 2: shanman系统 3: 排队系统
	EventId     string            `json:"event_id" bson:"event_id"`
	Context     map[string]string `json:"context" bson:"context"`
	Date        time.Time         `json:"date" bson:"date"`
}

type BatteryRefreshEvent struct {
	DeviceId                  string  `json:"device_id" bson:"device_id"`
	Project                   string  `json:"project" bson:"project"`
	StartTime                 int64   `json:"start_time" bson:"start_time"`
	EndTime                   int64   `json:"end_time" bson:"end_time"`
	BatteryId                 string  `json:"battery_id" bson:"battery_id"`
	RefreshType               int     `json:"refresh_type" bson:"refresh_type"`
	RefreshResult             int     `json:"refresh_result" bson:"refresh_result"`
	BatteryCapacity           string  `json:"battery_capacity" bson:"battery_capacity"`
	RefreshTargetVersion      string  `json:"refresh_target_version" bson:"refresh_target_version"`
	BatteryRoad               int     `json:"battery_road" bson:"battery_road"`
	OssRequestId              *string `json:"oss_request_id" bson:"oss_request_id"`
	OssCommandBatteryId       *string `json:"oss_command_battery_id" bson:"oss_command_battery_id"`
	OssCommandBatteryCapacity *string `json:"oss_command_battery_capacity" bson:"oss_command_battery_capacity"`
	AuthenticateTimestamp     *int64  `json:"authenticate_timestamp" bson:"authenticate_timestamp"`
	Rid                       *string `json:"rid" bson:"rid"`
}
