package mongo

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ServiceBasic struct {
	ServiceId string `json:"service_id" bson:"service_id"`
	StartTime int64  `json:"start_time" bson:"start_time"`
	EndTime   int64  `json:"end_time" bson:"end_time"`
}

type CameraFaultData struct {
	Project           string         `json:"project" bson:"project"`
	DeviceId          string         `json:"device_id" bson:"device_id"`
	Algorithm         string         `json:"algorithm" bson:"algorithm"`
	FaultServiceCount int            `json:"fault_service_count" bson:"fault_service_count"`
	FaultServiceList  []ServiceBasic `json:"fault_service_list" bson:"fault_service_list"`
	UpdateTs          int64          `json:"update_ts" bson:"update_ts"`
}

type CameraAlarmInfo struct {
	DeviceId         string         `json:"device_id" bson:"device_id"`
	Algorithm        string         `json:"algorithm" bson:"algorithm"`
	FaultServiceList []ServiceBasic `json:"fault_service_list" bson:"fault_service_list"`
}

type CameraAlarmHistory struct {
	Id        primitive.ObjectID `json:"_id" bson:"_id,omitempty"`
	Project   string             `json:"project" bson:"project"`
	AlarmInfo []CameraAlarmInfo  `json:"alarm_info" bson:"alarm_info"`
	AlarmTs   int64              `json:"alarm_ts" bson:"alarm_ts"`
	Date      time.Time          `json:"date" bson:"date"`
}

type SnapshotInfo struct {
	Id            primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	DeviceId      string             `json:"device_id" bson:"device_id"`
	CameraType    string             `json:"camera_type" bson:"camera_type"`
	CreateTs      int64              `json:"create_ts" bson:"create_ts"`
	AlgorithmName string             `json:"algorithm_name" bson:"algorithm_name"`
	FileName      string             `json:"file_name" bson:"file_name"`
	Date          time.Time          `json:"date" bson:"date"`
}
