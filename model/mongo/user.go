package mongo

import "go.mongodb.org/mongo-driver/bson/primitive"

type MongoUserUpgradeInfo struct {
	Id              primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	UserId          string             `json:"user_id" bson:"user_id"`
	OriginalRole    int                `json:"original_role" bson:"original_role"`
	UpgradeRole     int                `json:"upgrade_role" bson:"upgrade_role"`
	ExpireTimestamp int64              `json:"expire_timestamp" bson:"expire_timestamp"`
	UpdateTimestamp int64              `json:"update_timestamp" bson:"update_timestamp"`
	CreateTimestamp int64              `json:"create_timestamp" bson:"create_timestamp"`
}

type MongoUserSubscribeModule struct {
	UserId          string   `json:"user_id" bson:"_id"`
	SubscribeModule []string `json:"subscribe_module" bson:"subscribe_module"`
	UpdateTs        int64    `json:"update_ts" bson:"update_ts"`
}
