package mongo

import "time"

type ActivityStats struct {
	Day                  int64     `json:"day" bson:"day"`
	Project              string    `json:"project" bson:"project"`
	Algorithm            string    `json:"algorithm" bson:"algorithm"`
	DownloadTotalSuccess int64     `json:"download_total_success" bson:"download_total_success"`
	UploadTotalSuccess   int64     `json:"upload_total_success" bson:"upload_total_success"`
	DownloadTotalFailure int64     `json:"download_total_failure" bson:"download_total_failure"`
	UploadTotalFailure   int64     `json:"upload_total_failure" bson:"upload_total_failure"`
	InsertTs             int64     `json:"insert_ts" bson:"insert_ts"`
	UpdateTs             int64     `json:"update_ts" bson:"update_ts"`
	Date                 time.Time `json:"date" bson:"date"`
}

type ActivityDevices struct {
	Day         int64     `json:"day" bson:"day"`
	Project     string    `json:"project" bson:"project"`
	DeviceId    string    `json:"device_id" bson:"device_id"`
	UploadTotal int64     `json:"upload_total" bson:"upload_total"`
	DurationDay int       `json:"duration_day" bson:"duration_day"`
	InsertTs    int64     `json:"insert_ts" bson:"insert_ts"`
	Date        time.Time `json:"date" bson:"date"`
}
