package mongo

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type MongoLogFileUploadHistory struct {
	Id             primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	Description    string             `json:"description,omitempty" bson:"description,omitempty"`
	DeviceId       string             `json:"device_id" bson:"device_id"`
	Project        string             `json:"project" bson:"project"`
	FileGenTime    int64              `json:"file_gen_time" bson:"file_gen_time"`
	FileUploadTime int64              `json:"file_upload_time" bson:"file_upload_time"`
	FileGenStatus  string             `json:"file_gen_status" bson:"file_gen_status"`
	FilePath       string             `json:"file_path" bson:"file_path"`
	LogUrl         string             `json:"log_url" bson:"log_url"`
	Operator       string             `json:"operator" bson:"operator"`
	AllowDownload  bool               `json:"allow_download" bson:"allow_download"`
	MD5            string             `json:"md5,omitempty" bson:"md5,omitempty"`
	MD5CheckPass   *bool              `json:"md5_check_pass,omitempty" bson:"md5_check_pass,omitempty"`
	Date           time.Time          `json:"date,omitempty" bson:"date,omitempty"`
}
