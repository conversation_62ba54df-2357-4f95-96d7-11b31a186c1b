package mongo

type DeviceBlacklist struct {
	Project                   string `json:"project" bson:"project"`
	DeviceId                  string `json:"device_id" bson:"device_id"`
	VersionWorksheetBlacklist bool   `json:"version_worksheet_blacklist" bson:"version_worksheet_blacklist"`
}

type DeviceBlacklistHistory struct {
	Project       string `json:"project" bson:"project"`
	DeviceId      string `json:"device_id" bson:"device_id"`
	Operator      string `json:"operator" bson:"operator"`
	BlacklistType string `json:"blacklist_type" bson:"blacklist_type"`
	Blacklist     bool   `json:"blacklist" bson:"blacklist"`
	OperateTs     int64  `json:"operate_ts" bson:"operate_ts"`
}

type IgnoreDevices struct {
	DeviceId    string `json:"device_id" bson:"device_id"`
	Description string `json:"description" bson:"description"`
	IsActive    bool   `json:"is_active" bson:"is_active"`
	Online      bool   `json:"online" bson:"online"`
}

type DeviceReleaseName struct {
	Id       int64  `json:"_id" bson:"_id"`
	Name     string `json:"name" bson:"name"`
	User     string `json:"user" bson:"user"`
	CreateTs int64  `json:"create_ts" bson:"create_ts"`
}

type DeviceReleaseVersion struct {
	Id          int64  `json:"_id" bson:"_id"`
	Version     string `json:"version" bson:"version"`
	User        string `json:"user" bson:"user"`
	ReleaseNote string `json:"release_note" bson:"release_note"`
	Type        int32  `json:"type" bson:"type"`
	Desc        string `json:"desc" bson:"desc"`
	CreateTs    int64  `json:"create_ts" bson:"create_ts"`
}

type RbDeviceInfo struct {
	DeviceId string `json:"device_id" bson:"_id"`
	Params   []struct {
		Key   int64 `json:"key" bson:"key"`
		Value any   `json:"value" bson:"value"`
	} `json:"params" bson:"params"`
}

type DeviceElectricityPrice struct {
	ResourceId            string  `json:"resource_id" bson:"resource_id"`
	Month                 string  `json:"month" bson:"month"`
	PriceTag              string  `json:"price_tag" bson:"price_tag"`
	Price                 float64 `json:"price" bson:"price"`
	StartDay              string  `json:"start_day" bson:"start_day"`
	EndDay                string  `json:"end_day" bson:"end_day"`
	StartHour             int     `json:"start_hour" bson:"start_hour"`
	EndHour               int     `json:"end_hour" bson:"end_hour"`
	Start                 string  `json:"start" bson:"start"`
	End                   string  `json:"end" bson:"end"`
	ElectricityPriceModel string  `json:"electricity_price_model" bson:"electricity_price_model"`
	OnePricePrice         float64 `json:"one_price_price" bson:"one_price_price"`
}
