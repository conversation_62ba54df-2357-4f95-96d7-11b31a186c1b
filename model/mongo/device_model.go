package mongo

import "time"

const (
	DBDeviceModel = "device_model"

	CollectionEnergy     = "energy"
	CollectionRevenue    = "revenue"
	CollectionEnergyRank = "energy_rank"
)

type DeviceModelEnergy struct {
	Day                     int64     `json:"day" bson:"day"`
	DeviceId                string    `json:"device_id" bson:"device_id"`
	Project                 string    `json:"project" bson:"project"`
	CityCompany             string    `json:"city_company" bson:"city_company"`
	Efficiency              *float64  `json:"efficiency" bson:"efficiency"`
	ChargingEnergyAvailable *float64  `json:"charging_energy_available" bson:"charging_energy_available"`
	ModuleOutputEnergyTotal *float64  `json:"module_output_energy_total" bson:"module_output_energy_total"`
	ServiceCount            *int      `json:"service_count" bson:"service_count"`
	TotalConsumption        *float64  `json:"total_consumption" bson:"total_consumption"`
	ChargeConsumption       *float64  `json:"charge_consumption" bson:"charge_consumption"`
	WaterConsumption        *float64  `json:"water_consumption" bson:"water_consumption"`
	OperationConsumption    *float64  `json:"operation_consumption" bson:"operation_consumption"`
	MechanicalConsumption   *float64  `json:"mechanical_consumption" bson:"mechanical_consumption"`
	LightConsumption        *float64  `json:"light_consumption" bson:"light_consumption"`
	UpsConsumption          *float64  `json:"ups_consumption" bson:"ups_consumption"`
	CoolingConsumption      *float64  `json:"cooling_consumption" bson:"cooling_consumption"`
	RankLevelFinal          *string   `json:"rank_level_final" bson:"rank_level_final"`
	ChargingEnergy          *float64  `json:"charging_energy" bson:"charging_energy"`
	ChargingPileEnergy      *float64  `json:"charging_pile_energy" bson:"charging_pile_energy"`
	HighTemperature         *float64  `json:"high_temperature" bson:"high_temperature"`
	Date                    time.Time `json:"date" bson:"date"`
}

type DeviceModelRevenue struct {
	Day                       int64     `json:"day" bson:"day"`
	DeviceId                  string    `json:"device_id" bson:"device_id"`
	Project                   string    `json:"project" bson:"project"`
	CityCompany               string    `json:"city_company" bson:"city_company"`
	MaxRevenueRate            *float64  `json:"max_revenue_rate" bson:"max_revenue_rate"`
	OffPeakRevenue            *float64  `json:"off_peak_revenue" bson:"off_peak_revenue"`
	EnergyRevenue             *float64  `json:"energy_revenue" bson:"energy_revenue"`
	ChargeRevenue             *float64  `json:"charge_revenue" bson:"charge_revenue"`
	WaterRevenue              *float64  `json:"water_revenue" bson:"water_revenue"`
	OperationRevenue          *float64  `json:"operation_revenue" bson:"operation_revenue"`
	BatteryMaintenanceRevenue *float64  `json:"battery_maintenance_revenue" bson:"battery_maintenance_revenue"`
	BatteryMaintenanceTimes   *float64  `json:"battery_maintenance_times" bson:"battery_maintenance_times"`
	BatteryTotal              *float64  `json:"battery_total" bson:"battery_total"`
	Date                      time.Time `json:"date" bson:"date"`
}

type DeviceModelEnergyRank struct {
	Month               string   `json:"month" bson:"month"`
	Project             string   `json:"project" bson:"project"`
	ServiceCntInterval  string   `json:"service_cnt_interval" bson:"service_cnt_interval"`
	TemperatureInterval string   `json:"temperature_interval" bson:"temperature_interval"`
	ServiceCntLower     float64  `json:"service_cnt_lower" bson:"service_cnt_lower"`
	ServiceCntUpper     float64  `json:"service_cnt_upper" bson:"service_cnt_upper"`
	TemperatureLower    float64  `json:"temperature_lower" bson:"temperature_lower"`
	TemperatureUpper    float64  `json:"temperature_upper" bson:"temperature_upper"`
	Ees01               *float64 `json:"ees_01" bson:"ees_01"`
	Ees02               *float64 `json:"ees_02" bson:"ees_02"`
	Ees03               *float64 `json:"ees_03" bson:"ees_03"`
	Ees04               *float64 `json:"ees_04" bson:"ees_04"`
	Oes01               *float64 `json:"oes_01" bson:"oes_01"`
	Oes02               *float64 `json:"oes_02" bson:"oes_02"`
	Oes03               *float64 `json:"oes_03" bson:"oes_03"`
	Oes04               *float64 `json:"oes_04" bson:"oes_04"`
	Wes01               *float64 `json:"wes_01" bson:"wes_01"`
	Wes02               *float64 `json:"wes_02" bson:"wes_02"`
	Wes03               *float64 `json:"wes_03" bson:"wes_03"`
	Wes04               *float64 `json:"wes_04" bson:"wes_04"`
	HighTempBias        *float64 `json:"high_temp_bias" bson:"high_temp_bias"`
	LowTempBias         *float64 `json:"low_temp_bias" bson:"low_temp_bias"`
}
