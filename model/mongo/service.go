package mongo

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type MongoServiceInfo struct {
	Id                                  primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	ServiceId                           string             `json:"service_id" bson:"service_id"`
	DeviceId                            string             `json:"device_id" bson:"device_id"`
	Date                                time.Time          `json:"date" bson:"date"` // equal to `service_start_time`
	EVBatteryId                         string             `json:"ev_battery_id" bson:"ev_battery_id"`
	ServiceBatteryId                    string             `json:"service_battery_id" bson:"service_battery_id"`
	EvId                                string             `json:"ev_id" bson:"ev_id"`
	EvType                              string             `json:"ev_type" bson:"ev_type"`
	EvBMSBatteryCapacityType            int32              `json:"ev_bms_battery_capacity_type" bson:"ev_bms_battery_capacity_type"`
	EvBMSBatteryUsableCapacityType      int32              `json:"ev_bms_battery_usable_capacity_type" bson:"ev_bms_battery_usable_capacity_type"`
	EvBatterySoc                        float32            `json:"ev_battery_soc" bson:"ev_battery_soc"`
	ServiceBMSBatteryCapacityType       int32              `json:"service_bms_battery_capacity_type" bson:"service_bms_battery_capacity_type"`
	ServiceBMSBatteryUsableCapacityType int32              `json:"service_bms_battery_usable_capacity_type" bson:"service_bms_battery_usable_capacity_type"`
	ServiceBatterySoc                   float32            `json:"service_battery_soc" bson:"service_battery_soc"`
	FinishResult                        int32              `json:"finish_result" bson:"finish_result"`
	IsStuck                             bool               `json:"is_stuck" bson:"is_stuck"`
	StartTime                           int64              `json:"service_start_time" bson:"service_start_time"`
	EndTime                             int64              `json:"service_end_time" bson:"service_end_time"`
	UpdatedTime                         int64              `json:"updated_time" bson:"updated_time"`
}

type MongoServiceInfoV2 struct {
	Id        primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	ServiceId string             `mapstructure:"service_id" bson:"service_id" json:"service_id"`                  // 服务id
	DeviceId  string             `mapstructure:"device_id" bson:"device_id,omitempty" json:"device_id,omitempty"` // 设备id
	EvId      string             `mapstructure:"ev_id" bson:"ev_id,omitempty" json:"ev_id,omitempty"`             // 车辆id
	EvType    string             `mapstructure:"ev_type" bson:"ev_type,omitempty" json:"ev_type,omitempty"`       // 车辆类型
	OrderId   string             `mapstructure:"order_id" bson:"order_id,omitempty" json:"order_id,omitempty"`    // 订单id（暂无）
	Rid       string             `mapstructure:"rid" bson:"rid,omitempty" json:"rid,omitempty"`                   // 预约id

	EVBatteryId                    string   `mapstructure:"ev_battery_id" bson:"ev_battery_id,omitempty" json:"ev_battery_id,omitempty"`                                                                   // 车辆电池id
	EvBMSBatteryCapacityType       *int32   `mapstructure:"ev_bms_battery_capacity_type" bson:"ev_bms_battery_capacity_type,omitempty" json:"ev_bms_battery_capacity_type,omitempty"`                      // 车辆电池实际容量类型
	EvBMSBatteryUsableCapacityType *int32   `mapstructure:"ev_bms_battery_usable_capacity_type" bson:"ev_bms_battery_usable_capacity_type,omitempty" json:"ev_bms_battery_usable_capacity_type,omitempty"` // 车辆电池用户容量类型
	EvBatteryOriginalType          *int32   `mapstructure:"ev_battery_original_type" bson:"ev_battery_original_type,omitempty" json:"ev_battery_original_type,omitempty"`                                  // 车辆电池原始类型
	EvBatterySoc                   *float32 `mapstructure:"ev_battery_soc" bson:"ev_battery_soc,omitempty" json:"ev_battery_soc,omitempty"`                                                                // 车辆电池用户soc
	EvBatteryRealSoc               *float32 `mapstructure:"ev_battery_real_soc" bson:"ev_battery_real_soc,omitempty" json:"ev_battery_real_soc,omitempty"`                                                 // 车辆电池实际soc

	ServiceBatteryId                    string   `mapstructure:"service_battery_id" bson:"service_battery_id,omitempty" json:"service_battery_id,omitempty"`                                                                   // 服务电池id
	ServiceBMSBatteryCapacityType       *int32   `mapstructure:"service_bms_battery_capacity_type" bson:"service_bms_battery_capacity_type,omitempty" json:"service_bms_battery_capacity_type,omitempty"`                      // 服务电池实际容量类型
	ServiceBMSBatteryUsableCapacityType *int32   `mapstructure:"service_bms_battery_usable_capacity_type" bson:"service_bms_battery_usable_capacity_type,omitempty" json:"service_bms_battery_usable_capacity_type,omitempty"` // 服务电池用户容量类型
	ServiceBatteryOriginalType          *int32   `mapstructure:"service_battery_original_type" bson:"service_battery_original_type,omitempty" json:"service_battery_original_type,omitempty"`                                  // 服务电池原始类型
	ServiceBatterySoc                   *float32 `mapstructure:"service_battery_soc" bson:"service_battery_soc,omitempty" json:"service_battery_soc,omitempty"`                                                                // 服务电池用户soc
	ServiceBatteryRealSoc               *float32 `mapstructure:"service_battery_real_soc" bson:"service_battery_real_soc,omitempty" json:"service_battery_real_soc,omitempty"`                                                 // 服务电池实际soc

	SwapFailCode         *int32 `mapstructure:"swap_fail_code" bson:"swap_fail_code,omitempty" json:"swap_fail_code,omitempty"`                         // 换电失败原因 1:站内无可用电池 2:手动选电池不匹配 3:刷写电池BMS失败 4:车辆换电准备失败 5:换电过程设备异常 6:车辆自检失败 7:获取车辆信息失败 8:换电结束异常 9:车站通信断开 10:车辆回答超时 11:车机版本不在Rules.json文件中 12:手动选择电池ID为空 13:手动选择电池软件版本为空 14:手动选择电池在黑名单 15:预留订单,手动选择电池为非预留电池 16:非预留订单,手动选择电池为预留电池 17:手动选择电池容量不匹配车机 18:指定电池ID,自动选电池失败 19:指定电芯类型,手动选择跨级电池 20:机械换电开始前-车辆未在V槽内 21:新电芯电池,需要刷写但是无可刷写版本 23:换电等待过程中,被终止 24:手动选择电池存在本地烟雾告警 25:根据容量选电池失败 26:根据默认规则选电池失败 27:换电回退
	FinishResult         *int32 `mapstructure:"finish_result" bson:"finish_result,omitempty" json:"finish_result,omitempty"`                            // 服务是否正常结束，0:异常结束， 1:正常结束
	IsStuck              *bool  `mapstructure:"is_stuck" bson:"is_stuck,omitempty" json:"is_stuck,omitempty"`                                           // 是否挂车
	StartTime            int64  `mapstructure:"service_start_time" bson:"service_start_time,omitempty" json:"start_time,omitempty"`                     // 服务开始时间
	EndTime              int64  `mapstructure:"service_end_time" bson:"service_end_time,omitempty" json:"end_time,omitempty"`                           // 服务结束时间
	ServiceDuration      int64  `mapstructure:"service_duration" bson:"service_duration,omitempty" json:"service_duration,omitempty"`                   // 服务时长
	EvLeaveTime          int64  `mapstructure:"ev_leave_time" bson:"ev_leave_time,omitempty" json:"ev_leave_time,omitempty"`                            // 车辆驶离时间
	MechanicalFinishTime int64  `mapstructure:"mechanical_finish_time" bson:"mechanical_finish_time,omitempty" json:"mechanical_finish_time,omitempty"` // 机械运动完成时间

	UpdatedTime int64     `json:"updated_time" bson:"updated_time"`
	Date        time.Time `json:"date" bson:"date"` // equal to `service_start_time`
}

type OrderInfo struct {
	Id              string    `json:"_id" bson:"_id,omitempty"`
	Rid             string    `json:"rid" bson:"rid"`
	ServiceId       string    `json:"service_id" bson:"service_id"`
	DeviceId        string    `json:"device_id" bson:"device_id"`
	Project         string    `json:"project" bson:"project"`
	IsAutomatedSwap bool      `json:"is_automated_swap" bson:"is_automated_swap"`
	IsReverseSwap   bool      `json:"is_reverse_swap" bson:"is_reverse_swap"`
	OrderStartTime  int64     `json:"order_start_time" bson:"order_start_time"`
	OrderEndTime    int64     `json:"order_end_time" bson:"order_end_time"`
	ElectricityKwh  float64   `json:"electricity_kwh" bson:"electricity_kwh"`
	UpdateTs        int64     `json:"update_ts" bson:"update_ts"`
	Date            time.Time `json:"date" bson:"date"`
}

type SwapSatisfyData struct {
	Id               string           `json:"_id" bson:"_id,omitempty"`
	OrderId          string           `json:"order_id" bson:"order_id"`
	ServiceId        string           `json:"service_id" bson:"service_id"`
	CommentTime      int64            `json:"comment_time" bson:"comment_time"`
	UserTag          []string         `json:"user_tag" bson:"user_tag"`
	DiagnosisTag     []string         `json:"diagnosis_tag" bson:"diagnosis_tag"`
	Score            int              `json:"score" bson:"score"`
	DeviceId         string           `json:"device_id" bson:"device_id"`
	Project          string           `json:"project" bson:"project"`
	IsValid          bool             `json:"is_valid" bson:"is_valid"`
	Comment          string           `json:"comment" bson:"comment"`
	Reason           string           `json:"reason" bson:"reason"`
	Solution         string           `json:"solution" bson:"solution"`
	ServiceBatteryId string           `json:"service_battery_id" bson:"service_battery_id"`
	EvBatteryId      string           `json:"ev_battery_id" bson:"ev_battery_id"`
	EvId             string           `json:"ev_id" bson:"ev_id"`
	CityCompany      string           `json:"city_company" bson:"city_company"`
	CityCompanyGroup string           `json:"city_company_group" bson:"city_company_group"`
	VehiclePlatform  string           `json:"vehicle_platform" bson:"vehicle_platform"`
	L3Labels         map[string]int64 `json:"l3_labels" bson:"l3_labels"`
	FinalL1Label     string           `json:"final_l1_label" bson:"final_l1_label"`
	FinalL2Label     string           `json:"final_l2_label" bson:"final_l2_label"`
	FinalL3Label     string           `json:"final_l3_label" bson:"final_l3_label"`
	DiyLabelUpdateTs int64            `json:"diy_label_update_ts" bson:"diy_label_update_ts"`
	ReportStatus     string           `json:"report_status" bson:"report_status"`
	UpdateTs         int64            `json:"update_ts" bson:"update_ts"`
	Date             time.Time        `json:"date" bson:"date"`
}

type EventInfo struct {
	EventId   string `json:"event_id" bson:"event_id"`
	Timestamp int64  `json:"timestamp" bson:"timestamp"`
}

type SwapEvent struct {
	Id        primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	DeviceId  string             `json:"device_id" bson:"device_id"`
	ServiceId string             `json:"service_id" bson:"service_id"`
	Events    []EventInfo        `json:"events" bson:"events"`
	UpdateTs  int64              `json:"update_ts" bson:"update_ts"`
	Date      time.Time          `json:"date" bson:"date"`
}

type SwapEventTag struct {
	Project          string `json:"project" bson:"project"`
	EventId          string `json:"event_id" bson:"event_id"`
	EventDescription string `json:"event_description" bson:"event_description"`
}

type SatisfyDiagnosisConfig struct {
	OrderTimeThreshold   int64 `json:"order_time_threshold" bson:"order_time_threshold"`
	ServiceTimeThreshold int64 `json:"service_time_threshold" bson:"service_time_threshold"`
	QueueTimeThreshold   int64 `json:"queue_time_threshold" bson:"queue_time_threshold"`
}

type ServiceDailyCount struct {
	Project string    `json:"project" bson:"project"`
	Count   int64     `json:"count" bson:"count"`
	Day     int64     `json:"day" bson:"day"`
	DayStr  string    `json:"day_str" bson:"day_str"`
	Date    time.Time `json:"date" bson:"date"`
}
