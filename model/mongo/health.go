package mongo

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type DeviceHealthConfig struct {
	AlphaServo  float64 `json:"alpha_servo" bson:"alpha_servo"`
	AlphaSensor float64 `json:"alpha_sensor" bson:"alpha_sensor"`
	AlphaCharge float64 `json:"alpha_charge" bson:"alpha_charge"`
}

type ServoHealthScore struct {
	StackerScore    float64 `json:"stacker_score" bson:"stacker_score"`
	PanScore        float64 `json:"pan_score" bson:"pan_score"`
	PanFreeScore    float64 `json:"pan_free_score" bson:"pan_free_score"`
	PanLoadedScore  float64 `json:"pan_loaded_score" bson:"pan_loaded_score"`
	LiftScore       float64 `json:"lift_score" bson:"lift_score"`
	LiftFreeScore   float64 `json:"lift_free_score" bson:"lift_free_score"`
	LiftLoadedScore float64 `json:"lift_loaded_score" bson:"lift_loaded_score"`
	ForkScore       float64 `json:"fork_score" bson:"fork_score"`
}

type ChargeModuleHealth struct {
	ModuleId    int     `json:"module_id" bson:"module_id"`
	HealthScore float64 `json:"health_score" bson:"health_score"`
}

type CameraData struct {
	CameraType    string `json:"camera_type" bson:"camera_type"`
	PredictResult int    `json:"predict_result" bson:"predict_result"`
}

type SensorHealthData struct {
	CameraSum  float64      `json:"camera_sum" bson:"camera_sum"`
	TotalScore float64      `json:"total_score" bson:"total_score"`
	CameraData []CameraData `json:"camera_data" bson:"camera_data"`
}

type DeviceHealthData struct {
	Id                      primitive.ObjectID   `json:"_id,omitempty" bson:"_id,omitempty"`
	Project                 string               `json:"project" bson:"project"`
	DeviceId                string               `json:"device_id" bson:"device_id"`
	ServoHealthScore        ServoHealthScore     `json:"servo_health_score" bson:"servo_health_score"`
	StackerPanLoadedTorque  float64              `json:"stacker_pan_loaded_torque" bson:"stacker_pan_loaded_torque"`
	StackerPanFreeTorque    float64              `json:"stacker_pan_free_torque" bson:"stacker_pan_free_torque"`
	StackerLiftLoadedTorque float64              `json:"stacker_lift_loaded_torque" bson:"stacker_lift_loaded_torque"`
	StackerLiftFreeTorque   float64              `json:"stacker_lift_free_torque" bson:"stacker_lift_free_torque"`
	ForkTorque              float64              `json:"fork_torque" bson:"fork_torque"`
	ChargeHealthScore       float64              `json:"charge_health_score" bson:"charge_health_score"`
	ChargeModuleHealthScore []ChargeModuleHealth `json:"charge_module_health_score" bson:"charge_module_health_score"`
	SensorData              SensorHealthData     `json:"sensor_data" bson:"sensor_data"`
	WorksheetSensor         bool                 `json:"worksheet_sensor" bson:"worksheet_sensor"`
	WorksheetServo          bool                 `json:"worksheet_servo" bson:"worksheet_servo"`
	WorksheetCharge         bool                 `json:"worksheet_charge" bson:"worksheet_charge"`
	OnlineDay               int                  `json:"online_day" bson:"online_day"`
	DailyServiceCnt         int                  `json:"daily_service_cnt" bson:"daily_service_cnt"`
	TagIsUnattended         string               `json:"tag_is_unattended" bson:"tag_is_unattended"`
	Day                     int64                `json:"day" bson:"day"`
	Date                    time.Time            `json:"date" bson:"date"`
	HealthScoreSort         float64              `json:"health_score_sort,omitempty" bson:"health_score_sort,omitempty"`
	ServoHealthScoreSort    float64              `json:"servo_health_score_sort,omitempty" bson:"servo_health_score_sort,omitempty"`
	ChargeHealthScoreSort   float64              `json:"charge_health_score_sort,omitempty" bson:"charge_health_score_sort,omitempty"`
	SensorHealthScoreSort   float64              `json:"sensor_health_score_sort,omitempty" bson:"sensor_health_score_sort,omitempty"`
}

type TailDeviceData struct {
	Data []struct {
		DeviceId          string  `json:"device_id" bson:"device_id"`
		Description       string  `json:"description" bson:"description"`
		HealthScore       float64 `json:"health_score" bson:"health_score"`
		SensorHealthScore float64 `json:"sensor_health_score" bson:"sensor_health_score"`
		ChargeHealthScore float64 `json:"charge_health_score" bson:"charge_health_score"`
		ServoHealthScore  float64 `json:"servo_health_score" bson:"servo_health_score"`
		IsPatchOrder      bool    `json:"is_patch_order" bson:"is_patch_order"`
	} `json:"data" bson:"data"`
	TotalCount []struct {
		Count int `json:"count" bson:"count"`
	} `json:"total_count" bson:"total_count"`
}

type WorksheetData struct {
	Id              string    `json:"_id" bson:"_id"`
	Project         string    `json:"project" bson:"project"`
	DeviceId        string    `json:"device_id" bson:"device_id"`
	WorksheetStatus string    `json:"worksheet_status" bson:"worksheet_status"`
	WorksheetName   string    `json:"worksheet_name" bson:"worksheet_name"`
	WorksheetType   string    `json:"worksheet_type" bson:"worksheet_type"`
	CityCompany     string    `json:"city_company" bson:"city_company"`
	CreateTime      int64     `json:"create_time" bson:"create_time"`
	Assignee        *string   `json:"assignee,omitempty" bson:"assignee,omitempty"`
	UpdateTime      int64     `json:"update_time" bson:"update_time"`
	Day             int64     `json:"day" bson:"day"`
	Date            time.Time `json:"date" bson:"date"`
}

type WorksheetDailyCount struct {
	WorksheetType string `json:"worksheet_type" bson:"worksheet_type"`
	Day           int64  `json:"day" bson:"day"`
	Count         int    `json:"count" bson:"count"`
}

type WorksheetCloseRate struct {
	WorksheetType string  `json:"worksheet_type" bson:"worksheet_type"`
	CloseRate     float64 `json:"close_rate" bson:"close_rate"`
}
