package mongo

import "time"

type MongoOrder struct {
	DeviceId string `json:"device_id" bson:"device_id"`
	Project  string `json:"project" bson:"project"`
	// 状态变更时间
	Status0Ts   *int64 `json:"status_0_ts" bson:"status_0_ts"`
	Status10Ts  *int64 `json:"status_10_ts" bson:"status_10_ts"`
	Status20Ts  *int64 `json:"status_20_ts" bson:"status_20_ts"`
	Status30Ts  *int64 `json:"status_30_ts" bson:"status_30_ts"`
	Status100Ts *int64 `json:"status_100_ts" bson:"status_100_ts"`
	Status255Ts *int64 `json:"status_255_ts" bson:"status_255_ts"`

	OrderId                         string    `json:"order_id" bson:"order_id"`
	ResourceId                      string    `json:"resource_id" bson:"resource_id"`
	Rid                             string    `json:"rid" bson:"rid"`
	Status                          int64     `json:"status" bson:"status"`
	ServiceType                     int64     `json:"service_type" bson:"service_type"`
	UserId                          string    `json:"user_id" bson:"user_id"`
	OwnerId                         string    `json:"owner_id" bson:"owner_id"`
	StaffId                         string    `json:"staff_id" bson:"staff_id"`
	VehicleId                       string    `json:"vehicle_id" bson:"vehicle_id"`
	Vin                             string    `json:"vin" bson:"vin"`
	PlateNumber                     string    `json:"plate_number" bson:"plate_number"`
	BatteryId                       string    `json:"battery_id" bson:"battery_id"`
	BatteryCapSeries                int64     `json:"battery_cap_series" bson:"battery_cap_series"`
	BatterySoc                      float64   `json:"battery_soc" bson:"battery_soc"`
	BatteryCapLevel                 int64     `json:"battery_cap_level" bson:"battery_cap_level"`
	TargetBatteryId                 string    `json:"target_battery_id" bson:"target_battery_id"`
	TargetBatteryCapSeries          int64     `json:"target_battery_cap_series" bson:"target_battery_cap_series"`
	TargetBatterySoc                float64   `json:"target_battery_soc" bson:"target_battery_soc"`
	TargetBatteryCapLevel           int64     `json:"target_battery_cap_level" bson:"target_battery_cap_level"`
	Energy                          float64   `json:"energy" bson:"energy"`
	PkgVer                          string    `json:"pkg_ver" bson:"pkg_ver"`
	BmsSoftwarePn                   string    `json:"bms_software_pn" bson:"bms_software_pn"`
	BmsHardwarePn                   string    `json:"bms_hardware_pn" bson:"bms_hardware_pn"`
	CdcSoftwarePn                   string    `json:"cdc_software_pn" bson:"cdc_software_pn"`
	CdcHardwarePn                   string    `json:"cdc_hardware_pn" bson:"cdc_hardware_pn"`
	CgwSoftwarePn                   string    `json:"cgw_software_pn" bson:"cgw_software_pn"`
	CgwHardwarePn                   string    `json:"cgw_hardware_pn" bson:"cgw_hardware_pn"`
	VcuSoftwarePn                   string    `json:"vcu_software_pn" bson:"vcu_software_pn"`
	VcuHardwarePn                   string    `json:"vcu_hardware_pn" bson:"vcu_hardware_pn"`
	StartTime                       int64     `json:"start_time" bson:"start_time"`   // 订单开始时间
	FinishTime                      int64     `json:"finish_time" bson:"finish_time"` // // 订单结束时间
	FinishType                      int64     `json:"finish_type" bson:"finish_type"` //0 未知，1 小哥电换电成功，2 小哥电换电失败，3 OSS上报指令超时，4 OSS上报指令失败，5 强制完成（成功），6 强制完成（失败）
	Price                           int64     `json:"price" bson:"price"`
	ServicePriceType                int64     `json:"service_price_type" bson:"service_price_type"` // 服务费类型，0：按次计费，1：按电量计费
	ServicePrice                    int64     `json:"service_price" bson:"service_price"`
	ElectricityPriceType            int64     `json:"electricity_price_type" bson:"electricity_price_type"` // 电费类型，0：按次计费，1：按电量计费
	ElectricityPrice                int64     `json:"electricity_price" bson:"electricity_price"`
	ShouldPrice                     int64     `json:"should_price" bson:"should_price"`
	ShouldServicePrice              int64     `json:"should_service_price" bson:"should_service_price"`
	ShouldElectricityPrice          int64     `json:"should_electricity_price" bson:"should_electricity_price"`
	PaymentType                     int64     `json:"payment_type" bson:"payment_type"`
	AutoPayChannel                  int64     `json:"auto_pay_channel" bson:"auto_pay_channel"`
	IsPaid                          bool      `json:"is_paid" bson:"is_paid"`
	PayTime                         int64     `json:"pay_time" bson:"pay_time"`
	SwapId                          int64     `json:"swap_id" bson:"swap_id"`       // 排队换电号
	OwnerRole                       int64     `json:"owner_role" bson:"owner_role"` // 车主角色
	OrderSource                     int64     `json:"order_source" bson:"order_source"`
	CancelSource                    int64     `json:"cancel_source" bson:"cancel_source"`
	CancelReason                    int64     `json:"cancel_reason" bson:"cancel_reason"`
	EconomyPrice                    int64     `json:"economy_price" bson:"economy_price"`
	AutoSwap                        int64     `json:"auto_swap" bson:"auto_swap"`                   // 自助换电订单：0-未知，1-自助换电站，2-非自助换电站
	CreationTime                    int64     `json:"creation_time" bson:"creation_time"`           // 订单创建时间
	ParkingStartTime                int64     `json:"parking_start_time" bson:"parking_start_time"` // 泊车开始时间
	Channel                         string    `json:"channel" bson:"channel"`                       // 渠道 code
	Client                          string    `json:"client" bson:"client"`                         // 客户端 code
	AutomatedSwapPeopleLeave        bool      `json:"automated_swap_people_leave" bson:"automated_swap_people_leave"`
	AutomatedSwapPeopleLeavedBefore bool      `json:"automated_swap_people_leaved_before" bson:"automated_swap_people_leaved_before"`
	CarBrand                        *string   `json:"car_brand" bson:"car_brand"`
	CarPlatform                     *string   `json:"car_platform" bson:"car_platform"`
	CarModelType                    *string   `json:"car_model_type" bson:"car_model_type"`
	CarPackagePartNumber            *string   `json:"car_package_part_number" bson:"car_package_part_number"`
	CarPackageGlobalVersion         *string   `json:"car_package_global_version" bson:"car_package_global_version"`
	VdpDiagnosisResult              *string   `json:"vdp_diagnosis_result" bson:"vdp_diagnosis_result"`
	Date                            time.Time `json:"date" bson:"date,omitempty"`
}
