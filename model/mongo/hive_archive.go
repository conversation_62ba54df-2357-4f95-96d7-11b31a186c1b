package mongo

import (
	"time"

	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
)

type PLCArchiveStatus struct {
	Type      string    `json:"type" bson:"type"`
	Project   string    `json:"project" bson:"project"`
	DeviceId  string    `json:"device_id" bson:"device_id"`
	ServiceId string    `json:"service_id" bson:"service_id"`
	Status    string    `json:"status" bson:"status"`
	Date      time.Time `json:"date" bson:"date"`
}

type RestoredPUS3PLCData struct {
	umw.TsPUS3PLCRecord `bson:",inline"`
	Date                time.Time `json:"date" bson:"date"`
}

type RestoredPUS4PLCData struct {
	umw.TsPUS4PLCRecord `bson:",inline"`
	Date                time.Time `json:"date" bson:"date"`
}

type RestoredPowerSwap2PLCData struct {
	umw.TsPowerSwap2PLCRecord `bson:",inline"`
	Date                      time.Time `json:"date" bson:"date"`
}

type RestoredFYPUS1PLCData struct {
	umw.TsFYPUS1PLCRecord `bson:",inline"`
	Date                  time.Time `json:"date" bson:"date"`
}
