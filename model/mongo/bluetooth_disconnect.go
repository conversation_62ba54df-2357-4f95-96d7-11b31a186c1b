package mongo

import "time"

type MongoBluetoothDisconnectAlarm struct {
	DeviceId          string    `json:"device_id" bson:"device_id"`
	Project           string    `json:"project" bson:"project"`
	AlarmId           string    `json:"alarm_id" bson:"alarm_id"`
	IsHappenInService bool      `json:"is_happen_in_service" bson:"is_happen_in_service"`
	CreateTs          int64     `json:"create_ts" bson:"create_ts"`
	ClearTs           int64     `json:"clear_ts" bson:"clear_ts"`
	Date              time.Time `json:"date" bson:"date"`
}

type MongoBluetoothDisconnectDailyStat struct {
	StatDay                string `json:"stat_day" bson:"stat_day"`
	DeviceId               string `json:"device_id" bson:"device_id"`
	ActiveDeviceCount      int64  `json:"active_device_count" bson:"active_device_count"`
	AlarmCount             int64  `json:"alarm_count" bson:"alarm_count"`
	AlarmInServiceCount    int64  `json:"alarm_in_service_count" bson:"alarm_in_service_count"`
	AlarmNotInServiceCount int64  `json:"alarm_not_in_service_count" bson:"alarm_not_in_service_count"`
}

type BluetoothDisconnectAlarmInfo struct {
	Data []struct {
		AlarmId           string `json:"alarm_id" bson:"alarm_id"`
		TotalTimes        int    `json:"total_times" bson:"total_times"`
		TotalServiceTimes int    `json:"total_service_times" bson:"total_service_times"`
	} `json:"data" bson:"data"`
	Total []struct {
		Count int `json:"count" bson:"count"`
	} `json:"total" bson:"total"`
}
