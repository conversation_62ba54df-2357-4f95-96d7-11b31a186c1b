package model

import (
	"encoding/json"
	"fmt"

	"github.com/apolloconfig/agollo/v4/component/log"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
)

const (
	PLC       = "Welkin-PLC"
	BACKEND   = "Welkin-Backend"
	DEVICE    = "Welkin-Device"
	SYSTEM    = "Welkin-System"
	QUALITY   = "Welkin-Quality"
	ALGORITHM = "Welkin-Algorithm"
	DIAGNOSIS = "Welkin-Diagnosis"

	AccessTokenTime = 604800
	DayMillisecond  = 24 * 3600000

	ErrorCodeOfNormal  = 0       // 正常图片code
	ErrorCodeOfFailure = 1       // 异常图片code
	MAXIMAGESIZE       = 2097152 //最大图片大小
	MINIMAGESIZE       = 200     //最小图片大小

	// 后台提供给设备的role
	HasExistedUser   = -4 // 已有用户登陆
	NOTALLOWED       = -3 // 设备已激活，无法通过出厂用户登陆
	SSOERROR         = -2 // 内网sso认证异常
	NOPERMISSION     = -1 // sso认证提示用户无权限 / 本地校验无权限
	ASSISTANT        = 1  // 专员
	DEVOPS           = 2  // 维护
	ADMIN            = 3  // 出厂
	DEVELOP          = 4  // 研发
	BROWNDRAGON      = 5  // 棕龙烧写
	BROWNDRAGON_TEMP = 6  // 棕龙烧写（临时）

	SyncFilePath  = "syncFilePath"
	UploadLogFile = "uploadLogFile"

	// TODO 为啥要以文件的类型来区分是否用public还是private
	FILE       = "file"
	IMAGE      = "image"
	NeedPublic = "need_public"

	// steps
	Upload   = "upload"
	Approval = "approval"

	// errors
	ErrDuplicateAlgorithm = "ErrDuplicateAlgorithm"

	SnapshotPrefix = "/warehouse/image/aec/"
	Null           = -1
	Failed         = 2
	Success        = 1

	// algorithms
	BSAService = "BSA-service"
	BSAVehicle = "BSA-vehicle"
	RSDSOpen   = "RSDS-open"
	RSDSClose  = "RSDS-close"
	RSDVViP    = "RSDV_ViP"

	// 飞书通知发送分组
	WelkinCommonAlarmKey   = "welkinCommonAlarm"
	OperationImageAlarmKey = "operationImageAlarm"

	CameraVerifyIntervalSeconds = "cameraVerifyIntervalSeconds"

	FrontendElementRadio = "radio" // 单选框
	FrontendElementInput = "input" // 文本输入框
)

var Role2CnName = map[int]string{
	ASSISTANT:        "专员",
	DEVOPS:           "维护",
	ADMIN:            "出厂",
	DEVELOP:          "研发",
	BROWNDRAGON:      "棕龙烧写",
	BROWNDRAGON_TEMP: "棕龙烧写（临时）",
}

var AiAlgorithmRealIdMap = map[string]map[int]int{
	umw.PowerSwap2: {
		870003: 9,
		870010: 1,
		870062: 6,
		870065: 5,
		870076: 10,
		870080: 16,
		870087: 4,
		870113: 13,
		870124: 15,
		870127: 15,
	},
	umw.PUS3: {
		902308: 6,
		902307: 1,
		902303: 9,
		902310: 5,
		902407: 15,
		902411: 10,
		902422: 13,
	},
}

var AiAlgorithmIdMap = map[string]map[int]int{
	umw.PowerSwap2: {
		9:  870003,
		1:  870010,
		6:  870062,
		5:  870065,
		10: 870076,
		16: 870080,
		4:  870087,
		13: 870113,
		15: 870124,
	},
	umw.PUS3: {
		6:  902308,
		1:  902307,
		9:  902303,
		5:  902310,
		15: 902407,
		10: 902411,
		13: 902422,
	},
}

var NoPfsAlgorithms = make(map[string]map[int]struct{})

var LprEquityMap = map[string]string{
	"0": "不可用",
	"1": "可用",
	"2": "已用",
	"3": "已过期",
	"4": "已转移",
}

var LprStatusMap = map[int]string{
	0: "未知",
	1: "首任车主",
	2: "认证二手车",
	3: "非认证二手车",
}

var StuckAlarmProportion = []float64{0.2, 0.15, 0.1, 0.05, 0.03, 0.02, 0.01, 0}

var StuckAlarmProportionKeys = []string{"20%+", "15%-20%", "10%-15%", "5%-10%", "3%-5%", "2%-3%", "1%-2%", "0%-1%"}

const (
	// 车辆品牌
	EvBrandNIO  = "NIO"
	EvBrandONVO = "ONVO"

	// 车辆类型 deprecated
	EvTypeEC6        = "EC6"
	EvTypeEC7        = "EC7"
	EvTypeES6        = "ES6"
	EvTypeES7        = "ES7"
	EvTypeES8        = "ES8"
	EvTypeET5        = "ET5"
	EvTypeET5T       = "ET5T"
	EvTypeET7        = "ET7"
	EvTypeL60        = "L60"
	EvTypeEL6        = "EL6"
	EvTypeEL7        = "EL7"
	EvTypeEL8        = "EL8"
	EvTypeET5Touring = "ET5 Touring"
)

// VehicleBrandMap 车品牌和车类型映射 deprecated
var VehicleBrandMap = map[string][]string{
	EvBrandNIO:  {EvTypeEC6, EvTypeEC7, EvTypeES6, EvTypeES7, EvTypeES8, EvTypeET5, EvTypeET5T, EvTypeET7, EvTypeEL6, EvTypeEL7, EvTypeEL8, EvTypeET5Touring},
	EvBrandONVO: {EvTypeL60},
}

// VehicleTypeBrandMap 车类型和车品牌隐射
var VehicleTypeBrandMap = make(map[string]string)

func InitConstant(evBrandData interface{}) {
	defer initVehicleBrandMap()
	if evBrandData == nil {
		return
	}
	byteData, err := json.Marshal(evBrandData)
	if err != nil {
		log.Errorf("fail to marshal evBrand config: %v", err)
		return
	}
	var vehicleBrandMap map[string][]string
	err = json.Unmarshal(byteData, &vehicleBrandMap)
	if err != nil {
		log.Errorf("fail to unmarshal evBrand config: %v", err)
		return
	}
	VehicleBrandMap = vehicleBrandMap
	fmt.Println("refresh vehicle brand:", ucmd.ToJsonStrIgnoreErr(VehicleBrandMap))
}

func initVehicleBrandMap() {
	for brand, types := range VehicleBrandMap {
		for _, t := range types {
			VehicleTypeBrandMap[t] = brand
		}
	}
}

var AlarmTypeMap = map[int32]string{
	1: "基础告警",
	2: "电池告警",
	3: "未知告警",
}
var AlarmLevelMap = map[int32]string{
	1: "一级告警",
	2: "二级告警",
	3: "三级告警",
}
var AlarmStateMap = map[int32]string{
	1: "告警清除",
	2: "告警产生",
	3: "未知状态",
}
