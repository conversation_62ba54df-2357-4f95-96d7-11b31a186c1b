package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	DEVICES = "devices"
)

type AlgServiceSuccess struct {
	ServiceId string `bson:"service_id" json:"service_id"`
	DeviceId  string `bson:"device_id" json:"device_id"`
	Success   bool   `bson:"success" json:"success"`
}

type ParametricFormulaSwitch struct {
	DeviceId    string           `bson:"device_id" json:"device_id"`
	AlgorithmOn map[int]struct{} `json:"algorithm_on" bson:"algorithm_on"`
	Date        time.Time        `json:"date" bson:"date"`
}

type DeviceParametricFormula struct {
	DeviceId string                  `bson:"device_id" json:"device_id"`
	Params   []ParametricFormulaInfo `bson:"params" json:"params"`
}

type ParametricFormulaInfo struct {
	Key   int    `bson:"key" json:"key"`
	Value int32  `bson:"value" json:"value"`
	Type  string `bson:"type" json:"type"`
}

type AlgorithmReportInfo struct {
	DeviceId string `bson:"device_id" json:"device_id"`
}

type AlgorithmImageInfo struct {
	Id           primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	ServiceId    string             `json:"service_id" bson:"service_id"`
	Project      string             `json:"project" bson:"-"`
	DeviceId     string             `json:"device_id" bson:"device_id"`
	Description  string             `json:"description" bson:"-"`
	BatteryId    string             `json:"battery_id" bson:"battery_id"`
	ImageURL     string             `json:"image_url" bson:"image_url"`
	ImageName    string             `json:"image_name" bson:"image_name"`
	ImageType    int                `json:"image_type" bson:"image_type"`
	Abnormal     bool               `json:"abnormal" bson:"abnormal"`
	ImageGenTime int64              `json:"image_gen_time" bson:"image_gen_time"`
	Date         time.Time          `json:"date" bson:"date"` //ttl
}

type SlotBatteryInfo struct {
	Project     string                 `json:"project"`
	DeviceId    string                 `json:"device_id"`
	Description string                 `json:"description"`
	Type        int32                  `json:"type"`
	BatteryId   string                 `json:"battery_id"`
	Timestamp   int64                  `json:"timestamp"`
	SlotId      string                 `json:"slot_id"`
	EventId     interface{}            `json:"event_id"`
	Related     map[string]interface{} `json:"related"`
}
type CameraName struct {
	Id   primitive.ObjectID           `json:"_id,omitempty" bson:"_id,omitempty"`
	Data map[string]map[string]string `bson:"data,omitempty"`
}

type PowerGrootDevice struct {
	Id string `json:"_id" bson:"_id"`
}

type MPCOperationTable struct {
	Type          int32  `json:"type" bson:"type"`
	Operation     int64  `json:"operation" bson:"operation"`
	PageCnName    string `json:"page_cn_name" bson:"page_cn_name"`
	PageEnName    string `json:"page_en_name" bson:"page_en_name"`
	ButtonCnName  string `json:"button_cn_name" bson:"button_cn_name"`
	ButtonEnName  string `json:"button_en_name" bson:"button_en_name"`
	ActionCnName  string `json:"action_cn_name" bson:"action_cn_name"`
	ActionEnName  string `json:"action_en_name" bson:"action_en_name"`
	ArgsCnName    string `json:"args_cn_name" bson:"args_cn_name"`
	ArgsEnName    string `json:"args_en_name" bson:"args_en_name"`
	RemarksCnName string `json:"remarks_cn_name" bson:"remarks_cn_name"`
	RemarksEnName string `json:"remarks_en_name" bson:"remarks_en_name"`
}

type OSSOperationTable struct {
	Type          int32  `json:"type" bson:"type"`
	Command       string `json:"command" bson:"command"`
	DescriptionCn string `json:"description_cn" bson:"description_cn"`
	DescriptionEu string `json:"description_eu" bson:"description_eu"`
}

type SapaParkingOccupationPO struct {
	DeviceId                 string    `json:"device_id" bson:"device_id"`
	ServiceDay               string    `json:"service_day" bson:"service_day"`
	Hour                     int       `json:"hour" bson:"hour"`
	BusyRate                 float64   `json:"busy_rate" bson:"busy_rate"`
	TotalSAPACount           int64     `json:"total_sapa_count" bson:"total_sapa_count"`
	SAPAPredictOccupantCount int64     `json:"sapa_predict_occupant_count" bson:"sapa_predict_occupant_count"`
	TotalOrderNum            int64     `json:"total_order_num" bson:"total_order_num"`
	CancelOrderNum           int64     `json:"cancel_order_num" bson:"cancel_order_num"`
	FinishOrderNum           int64     `json:"finish_order_num" bson:"finish_order_num"`
	InterruptNum             int64     `json:"interrupt_num" bson:"interrupt_num"`
	VocNum                   int64     `json:"voc_num" bson:"voc_num"`
	Date                     time.Time `json:"date" bson:"date"`
}

type DeviceHourlyService struct {
	DeviceId        string `json:"device_id" bson:"device_id"`
	ServiceCount70  int    `json:"service_count_70" bson:"service_count_70"`
	ServiceCount100 int    `json:"service_count_100" bson:"service_count_100"`
}

type HourlyOrderInfo struct {
	Id              primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	SwapStationId   string             `json:"swap_station_id" bson:"swap_station_id"`
	SwapStationName string             `json:"swap_station_name" bson:"swap_station_name"`
	DeviceModel     string             `json:"device_model" bson:"device_model"`
	Day             string             `json:"day" bson:"day"`
	Hour            int                `json:"hour" bson:"hour"`
	BatteryType     string             `json:"battery_type" bson:"battery_type"`
	ServiceCount    int                `json:"service_count" bson:"service_count"`
	DayTs           int64              `json:"day_ts" bson:"day_ts"`
	Date            time.Time          `json:"date" bson:"date"`
}

type MongoElectricityPriceData struct {
	BillingMethod  string                      `json:"billing_method" bson:"billing_method"`
	DistrictSplit  string                      `json:"district_split" bson:"district_split"`
	DistrictSwitch string                      `json:"district_switch" json:"district_switch"`
	Month          string                      `json:"month" bson:"month"`
	PriceType      string                      `json:"price_type" bson:"price_type"`
	ProvinceName   string                      `json:"province_name" bson:"province_name"`
	VoltageLevel   string                      `json:"voltage_level" bson:"voltage_level"`
	PriceInfo      []MongoElectricityPriceInfo `json:"price_info" bson:"price_info"`
	ProvinceId     string                      `json:"province_id" bson:"province_id"`
}

type MongoElectricityPriceInfo struct {
	PriceStart string `json:"price_start" bson:"price_start"`
	AgentPrice string `json:"agent_price" bson:"agent_price"`
	PriceTag   string `json:"price_tag" bson:"price_tag"`
	PriceEnd   string `json:"price_end" bson:"price_end"`
	Price      string `json:"price" bson:"price"`
}

type CommonCond struct {
	Page       int64
	Size       int64
	Descending bool
	Sort       string
}
