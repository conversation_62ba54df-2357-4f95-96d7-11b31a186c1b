package model

import (
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
)

type CommonUriParam struct {
	Page       int    `form:"page" json:"page" uri:"page"`
	Size       int    `form:"size" json:"size" uri:"size"`
	Descending bool   `form:"descending" json:"descending" uri:"descending"`
	Sort       string `form:"sort" json:"sort" uri:"sort"`
	Lang       string `form:"lang" json:"lang" uri:"lang"`
}

type CommonUriInTimeRangeParam struct {
	CommonUriParam
	StartTime int64 `form:"start_time" json:"start_time" uri:"start_time"`
	EndTime   int64 `form:"end_time" json:"end_time" uri:"end_time"`
}

type PLCRecordParam struct {
	CommonUriInTimeRangeParam
	ServiceId string `json:"service_id" form:"service_id"`
	PLStepNum string `json:"pl_step_num" form:"pl_step_num"`
	BCStepNum string `json:"bc_step_num" form:"bc_step_num"`
	Axis      string `json:"axis" form:"axis"`
}

type ConverterParam struct {
	CommonUriInTimeRangeParam
	ServiceId string `json:"service_id" form:"service_id" uri:"service_id"`
	PLStepNum *int32 `json:"pl_step_num" form:"pl_step_num" uri:"pl_step_num"`
	BCStepNum *int32 `json:"bc_step_num" form:"bc_step_num" uri:"bc_step_num"`
	Converter string `json:"converter" form:"converter" uri:"converter"`
}

type DIRecordParam struct {
	CommonUriInTimeRangeParam
	ServiceId string `json:"service_id" form:"service_id" uri:"service_id"`
	PLStepNum *int32 `json:"pl_step_num" form:"pl_step_num" uri:"pl_step_num"`
	BCStepNum *int32 `json:"bc_step_num" form:"bc_step_num" uri:"bc_step_num"`
	VarName   string `json:"varname" form:"varname" uri:"varname"`
}

type SensorParam struct {
	CommonUriInTimeRangeParam
	ServiceId string `json:"service_id" form:"service_id" uri:"service_id"`
	PLStepNum *int32 `json:"pl_step_num" form:"pl_step_num" uri:"pl_step_num"`
	BCStepNum *int32 `json:"bc_step_num" form:"bc_step_num" uri:"bc_step_num"`
	VarName   string `json:"varname" form:"varname" uri:"varname"`
}

type ServiceInfoParam struct {
	CommonUriInTimeRangeParam
	DeviceId       string `json:"device_id" form:"device_id" uri:"device_id"`
	DeviceIds      string `json:"device_ids" form:"device_ids" uri:"device_ids"`
	ServiceId      string `json:"service_id" form:"service_id" uri:"service_id"`
	BatteryId      string `json:"battery_id" form:"battery_id" uri:"battery_id"`
	EvId           string `json:"ev_id" form:"ev_id" uri:"ev_id"`
	HasAbnormalImg *bool  `json:"has_abnormal_img" form:"has_abnormal_img" uri:"has_abnormal_img"`
	FinishResult   *int   `json:"finish_result" form:"finish_result" uri:"finish_result"`
	IsStuck        *bool  `json:"is_stuck" form:"is_stuck" uri:"is_stuck"`
	EvType         string `json:"ev_type" form:"ev_type" uri:"ev_type"`
	EvBrand        string `json:"ev_brand" form:"ev_brand" uri:"ev_brand"`
}

type DeviceSESData struct {
	MongoSESRecords []umw.MongoSESRecord
}

type UpLoadImgRequest struct {
	SHA512             string `json:"sha512" form:"sha512" `
	Size               int32  `json:"size" form:"size" `
	CameraType         string `json:"cameraType" form:"cameraType"`
	ServiceID          string `json:"serviceId" form:"serviceId"`
	TS                 int64  `json:"ts" form:"ts" binding:"required"`
	UpLoadTs           int64  `json:"uploadFileTime" form:"uploadFileTime" `
	Type               *int   `json:"type" form:"type" binding:"required"` //设计为指针的原因是需要传默认值0
	ErrCode            *int   `json:"errorCode" form:"errorCode" `         //设计为指针的原因是需要传默认值0
	ErrorCode          int    `json:"error_code" form:"error_code"`
	Md5                string `json:"MD5" form:"MD5" `
	PowerSwapServiceID string `json:"sid" form:"sid"`
}

type UploadImageRequest struct {
	CameraType     *string `json:"camera_type" form:"camera_type"`
	VehicleType    *string `json:"vehicle_type" form:"vehicle_type"`
	BatteryId      *string `json:"battery_id" form:"battery_id"`
	SHA512         string  `json:"sha512" form:"sha512" binding:"required"`
	ServiceID      string  `json:"service_id" form:"service_id"` // 运营图片必传，其它非必传
	TS             int64   `json:"ts" form:"ts" binding:"required"`
	UploadFileTime int64   `json:"upload_file_time" form:"upload_file_time" binding:"required"`
	Type           *int    `json:"type" form:"type" binding:"required"`
	ImgStatusCode  *int    `json:"img_status_code" form:"img_status_code"` // 二、三代必传，一代非必传
	ExtraData      *string `json:"extra_data" form:"extra_data"`
}

type TKEEdgeRequest struct {
	Project   string `json:"project" bson:"project" form:"project"`
	DeviceId  string `json:"device_id" bson:"device_id" form:"device_id"`
	SubSystem string `json:"subsystem" bson:"subsystem" form:"subsystem"`
	Interface string `json:"interface" bson:"interface" form:"interface"`
}
type SaveTkeEdgeJoinInfoRequest struct {
	Project         string `json:"project" bson:"project" form:"project"`
	DeviceId        string `json:"device_id" bson:"device_id" form:"device_id"`
	SubSystem       string `json:"subsystem" bson:"subsystem" form:"subsystem"`
	Interface       string `json:"interface" bson:"interface" form:"interface"`
	DownloadCommand string `json:"download_command" bson:"download_command" form:"download_command"`
	CreateTs        int64  `json:"create_ts" bson:"create_ts" form:"create_ts"`
}

type QRParams struct {
	Project  string `json:"project" form:"project"`
	DeviceId string `json:"device_id" form:"device_id"`
}

type DeviceLoginLogoutParams struct {
	Project          string `json:"project" form:"project"`
	DeviceId         string `json:"device_id" form:"device_id"`
	BusinessType     string `json:"business_type" form:"business_type"`
	RequestType      string `json:"request_type" form:"request_type"`
	UserId           string `json:"user_id" form:"user_id"`
	Password         string `json:"password" form:"password"`
	DeviceIdentifier string `json:"device_identifier" form:"device_identifier"`
}

type OSSAuthParams struct {
	AppId      string `json:"appid" form:"appid"`
	AppSecret  string `json:"appsecret" form:"appsecret"`
	AuthId     string `json:"authid" form:"authid"`
	AuthSecret string `json:"authsecret" form:"authsecret"`
}

type ImageListUriParam struct {
	StartTime  *int64  `json:"start_time" bson:"start_time" uri:"start_time" form:"start_time" binding:"required"`
	EndTime    *int64  `json:"end_time" bson:"end_time" uri:"end_time" form:"end_time" binding:"required"`
	PageSize   int64   `json:"page_size" bson:"page_size" uri:"page_size" form:"page_size"`
	PageNo     int64   `json:"page_no" bson:"page_no" uri:"page_no" form:"page_no"`
	ImageType  *string `json:"image_type" bson:"image_type" uri:"image_type" form:"image_type"`
	Abnormal   *int    `json:"abnormal" bson:"abnormal" uri:"abnormal" form:"abnormal"`
	CameraType *string `json:"camera_type" bson:"camera_type" uri:"camera_type" form:"camera_type"`
}

type ImageParam struct {
	CommonUriInTimeRangeParam
	ServiceId  string `json:"service_id" form:"service_id" uri:"service_id"`
	ImageType  string `json:"image_type" form:"image_type" uri:"image_type"`
	Abnormal   *int   `json:"abnormal" form:"abnormal" uri:"abnormal"`
	CameraType string `json:"camera_type" form:"camera_type" uri:"camera_type"`
}

type ImageQueryParam struct {
	ServiceId string ` json:"service_id" bson:"service_id" form:"service_id"`
	Abnormal  *int   `json:"abnormal" bson:"abnormal" form:"abnormal"`
	StartTime int64  `form:"start_time" json:"start_time" uri:"start_time"`
	EndTime   int64  `form:"end_time" json:"end_time" uri:"end_time"`
}
type OfflineAccountParam struct {
	Project   string   `json:"project" form:"project"`
	DeviceId  string   `json:"device_id" form:"device_id"`
	AccountId []string `json:"account_id" form:"account_id"`
}

type UserListParams struct {
	CommonUriParam
	Username string `json:"username" form:"username" uri:"username"`
	UserId   string `json:"user_id" form:"user_id" uri:"user_id"`
	Role     *int   `json:"role" form:"role" uri:"role"`
}

type UserAddParams struct {
	Username string `json:"username" form:"username" binding:"required"`
	Role     []int  `json:"role" form:"role" binding:"required"`
}

type UserUpdateParams struct {
	Role []int `json:"role" form:"role" binding:"required"`
}

type UserUpgradeApplyRequest struct {
	UserId       string `json:"user_id"`
	OriginalRole int    `json:"original_role"`
	UpgradeRole  int    `json:"upgrade_role"`
}

type UserUpgradeExpandTimeRequest struct {
	UserId     string `json:"user_id"`
	ExpandTime int64  `json:"expand_time"`
}

type NotifyDeviceParams struct {
	Configuration StringKeyValue `json:"configuration" form:"configuration" binding:"required"`
}

type StringKeyValue struct {
	Key   string `json:"key" form:"key" binding:"required"`
	Value string `json:"value" form:"value" binding:"required"`
}

type Node struct {
	Type     int64  `json:"type" form:"type"`
	Name     string `json:"name" form:"name"`
	Path     string `json:"path" form:"path"`
	Children []Node `json:"children" form:"children"`
}

type SyncLogFilePathParams struct {
	Project   string `json:"project" form:"project" bson:"project" binding:"required"`
	DeviceId  string `json:"device_id" form:"device_id" bson:"device_id" binding:"required"`
	SyncTS    int64  `json:"sync_ts" form:"sync_ts" bson:"sync_ts" binding:"required"`
	IsRefresh bool   `json:"is_refresh" bson:"-"`
	LogNode   []Node `json:"log_node" form:"log_node" bson:"log_node" binding:"required"`
}

type UploadLogHistoryParams struct {
	CommonUriParam
	UserId   string `form:"user_id" json:"user_id" uri:"user_id"`
	DeviceId string `form:"device_id" json:"device_id" uri:"device_id"`
}

type LogDirectoryTreeParams struct {
	CommonUriParam
	DeviceId string `form:"device_id" json:"device_id" uri:"device_id"`
}

type SnapshotListParams struct {
	CommonUriParam
	DeviceId      string `form:"device_id" json:"device_id" uri:"device_id"`
	Ts            int64  `form:"ts" json:"ts" uri:"ts"` // deprecated
	Time          string `form:"time" json:"time" uri:"time"`
	AlgorithmName string `form:"algorithm_name" json:"algorithm_name" uri:"algorithm_name"`
	StartSyncTs   int64  `form:"start_sync_ts" json:"start_sync_ts" uri:"start_sync_ts"` // deprecated
	UserId        string `form:"user_id" json:"user_id"`
}

type GetFactoryTestInfoRequest struct {
	StartTs int64 `json:"start_ts" form:"start_ts" bson:"start_ts"`
	EndTs   int64 `json:"end_ts" form:"end_ts" bson:"end_ts"`
}

type RequestFile struct {
	FileGenTime int64  `form:"file_gen_time" json:"file_gen_time"`
	FilePath    string `form:"file_path" json:"file_path"`
}

type ApprovalContent struct {
	Description  string        `form:"description" json:"description"`
	DeviceId     string        `form:"device_id" json:"device_id"`
	Project      string        `form:"project" json:"project"`
	Reason       string        `form:"reason" json:"reason"`
	RequestFiles []RequestFile `form:"request_files" json:"request_files"`
}

type ApprovalCallback struct {
	Context          map[string]interface{} `form:"context" json:"context"`
	FlowInstanceId   string                 `form:"flow_instance_id" json:"flow_instance_id"`
	CurrentNodeName  string                 `form:"current_node_name" json:"current_node_name"`
	PreviousNodeName string                 `form:"previous_node_name" json:"previous_node_name"`
	Status           string                 `form:"status" json:"status"`
}

type ApprovalHistoryParams struct {
	CommonUriParam
	UserId   string `form:"user_id" json:"user_id" uri:"user_id"`
	DeviceId string `form:"device_id" json:"device_id" uri:"device_id"`
}

type VersionBase struct {
	PublishTs int64  `form:"publish_ts" json:"publish_ts"`
	Version   string `form:"version" json:"version"`
}

type VersionAlgorithmModel struct {
	AlgorithmId      int64  `form:"algorithm_id" json:"algorithm_id"`
	AlgorithmName    string `form:"algorithm_name" json:"algorithm_name"`
	AlgorithmVersion string `form:"algorithm_version" json:"algorithm_version"`
	Shadow           bool   `form:"shadow" json:"shadow"`
	UpdatePoint      string `form:"update_point" json:"update_point"`
	HasTestReport    int    `form:"has_test_report" json:"has_test_report"`
	TestReportURL    string `form:"test_report_url" json:"test_report_url"`
}

type VersionInfo struct {
	VersionBase
	PublishVersionId int64                   `form:"publish_version_id" json:"publish_version_id"`
	Models           []VersionAlgorithmModel `form:"models" json:"models"`
}

type VersionUpdateAlgorithmParams struct {
	AlgorithmId      int64  `form:"algorithm_id" json:"algorithm_id"`
	AlgorithmVersion string `form:"algorithm_version" json:"algorithm_version"`
	Shadow           bool   `form:"shadow" json:"shadow"`
}

type VersionAddTestReportParams struct {
	AlgorithmId   int64  `form:"algorithm_id" json:"algorithm_id"`
	TestReportURL string `form:"test_report_url" json:"test_report_url"`
}

type DataVersioningParams struct {
	CommonUriParam
	Algorithms string `form:"algorithms" json:"algorithms"`
	PublishTs  string `form:"publish_ts" json:"publish_ts"`
	IfQM       int    `form:"if_qm" json:"if_qm"`
}

type FactoryDataParam struct {
	CommonUriInTimeRangeParam
	DeviceId string `json:"device_id" form:"device_id" uri:"device_id"`
	Details  bool   `json:"details" form:"details" uri:"details"`
}

type AddNewDatasetMongoRequest struct {
	FileName string `form:"file_name,omitempty" json:"file_name,omitempty" bson:"file_name,omitempty"`
	FileSize int64  `form:"file_size,omitempty" json:"file_size,omitempty" bson:"file_size,omitempty"`
	FileUrl  string `form:"file_url,omitempty" json:"file_url,omitempty" bson:"file_url,omitempty"`
}

type FactoryTestRequestData struct {
	ReportGenTime int64   `json:"report_gen_time" form:"report_gen_time" binding:"required"`
	SHA512        string  `json:"sha512" form:"sha512" binding:"required"`
	Manufacturer  string  `json:"manufacturer" form:"manufacturer"`
	Reporter      string  `json:"reporter" form:"reporter"`
	TestType      string  `json:"test_type" form:"test_type"`
	Category      *int    `json:"category" form:"category"`
	FailCount     *int    `json:"fail_count" form:"fail_count" binding:"required"`
	SuccessRate   float64 `json:"success_rate" form:"success_rate"`
	ServiceInfo   string  `json:"serviceinfo" form:"serviceinfo" binding:"required"`
	ReportDetails string  `json:"report_details" form:"report_details"`
}

type FactoryDeviceStatus struct {
	DeviceId  string `json:"device_id" form:"device_id" binding:"required"`
	IsFactory *bool  `json:"is_factory" form:"is_factory"`
	State     *int   `json:"state" form:"state"`
}

type ConfirmDatasetRequest struct {
	Id int64 `json:"id"`
}

type DeleteDVFileRequest struct {
	FileUrl   string `json:"file_url" bson:"file_url"`
	PublishTs int64  `json:"publish_ts" bson:"publish_ts"`
}

type TankTransRecordParam struct {
	CommonUriInTimeRangeParam
	Slot    string `json:"slot" form:"slot" uri:"slot"`
	TransId string `json:"trans_id" form:"trans_id" uri:"trans_id"`
}

type TankTransDetailParam struct {
	StartTime int64  `form:"start_time" json:"start_time" uri:"start_time"`
	EndTime   int64  `form:"end_time" json:"end_time" uri:"end_time"`
	StepNum   *int   `form:"step_num" json:"step_num" uri:"step_num"`
	Axis      string `form:"axis" json:"axis" uri:"axis"`
}

type BatteryRefreshListRequest struct {
	CommonUriInTimeRangeParam
	BatteryId            string `form:"battery_id" json:"battery_id" uri:"battery_id"`
	RefreshResult        string `form:"refresh_result" json:"refresh_result" uri:"refresh_result"`
	BatteryCapacity      string `form:"battery_capacity" json:"battery_capacity" uri:"battery_capacity"`
	RefreshType          string `form:"refresh_type" json:"refresh_type" uri:"refresh_type"`
	RefreshTargetVersion string `form:"refresh_target_version" json:"refresh_target_version" uri:"refresh_target_version"`
}

type BatteryRefreshListV2Request struct {
	CommonUriInTimeRangeParam
	BatteryId            string `form:"battery_id" json:"battery_id" uri:"battery_id"`
	RefreshResult        *int   `form:"refresh_result" json:"refresh_result" uri:"refresh_result"`
	BatteryCapacity      *int   `form:"battery_capacity" json:"battery_capacity" uri:"battery_capacity"`
	RefreshType          *int   `form:"refresh_type" json:"refresh_type" uri:"refresh_type"`
	RefreshTargetVersion string `form:"refresh_target_version" json:"refresh_target_version" uri:"refresh_target_version"`
}

type InternalImageDataParam struct {
	AlgorithmName string `json:"algorithm_name" form:"algorithm_name" uri:"algorithm_name" binding:"required"`
	StartTime     int64  `json:"start_time" form:"start_time" uri:"start_time" binding:"required"`
	EndTime       int64  `json:"end_time" form:"end_time" uri:"end_time" binding:"required"`
	DeviceId      string `json:"device_id" form:"device_id" uri:"device_id"`
	CameraType    string `json:"camera_type" form:"camera_type" uri:"camera_type"`
	VehicleType   string `json:"vehicle_type" form:"vehicle_type" uri:"vehicle_type"`
	FailureValue  *int   `json:"failure_value" form:"failure_value" uri:"failure_value"`
	PageSize      int    `json:"page_size" form:"page_size" uri:"page_size"`
	PageNo        int    `json:"page_no" form:"page_no" uri:"page_no"`
	Download      int    `json:"download" form:"download" uri:"download"`
}

// 老owl数据接口，切换后删除
type QueryForMgo struct {
	AlgorithmName []int32 `json:"image_type, required" url:"image_type, required" bson:"image_type, required" form:"image_type, required"`
	StartTime     int64   `json:"start_time" url:"start_time" bson:"start_time" form:"start_time"`     //2022-02-03 12:10:09
	EndTime       int64   `json:"end_time" url:"end_time" bson:"end_time" form:"end_time"`             //          //     //2022-02-03 12:10:09
	DeviceID      string  `json:"device_id" url:"device_id" bson:"device_id" form:"device_id"`         //       //2022-02-03 12
	CameraName    string  `json:"camera_name" url:"camera_name" bson:"camera_name" form:"camera_name"` // //2022-02-03 12
	ObjectName    string  `json:"object_name" url:"object_name" bson:"object_name" form:"object_name"` //2022-02-03 12
	PageNumber    int     `json:"page_no" url:"page_no" bson:"page_no" form:"page_no"`                 //2022-02-
	CreateTime    int64   `json:"create_time" url:"create_time" bson:"create_time" form:"create_time"`
	VehicleType   string  `json:"vehicle_type" url:"vehicle_type" bson:"vehicle_type" form:"vehicle_type"`
	IsFailure     int     `json:"is_failure" url:"is_failure" bson:"is_failure" form:"is_failure"`
	PageSize      int     `json:"page_size" url:"page_size" bson:"page_size" form:"page_size"`
}

type RealtimeParam struct {
	CommonUriInTimeRangeParam
	DataId   string `json:"data_id" form:"data_id" binding:"required"`
	Download bool   `json:"download" form:"download"`
}
type DeleteImageRequest struct {
	Id           string `json:"id" form:"id" bson:"id"`
	DeviceId     string `json:"device_id" form:"device_id"`
	DeleteReason string `json:"delete_reason" url:"delete_reason"`
}

type AlgorithmSuccessRateRequest struct {
	AlgorithmId   int    `json:"algorithm_id" form:"algorithm_id"`
	VehicleType   string `json:"vehicle_type" form:"vehicle_type"`
	BatteryType   string `json:"battery_type" form:"battery_type"`
	TimeRange     int    `json:"time_range" form:"time_range"` // 区分白天还是晚上
	StartTime     int64  `json:"start_time" form:"start_time"`
	EndTime       int64  `json:"end_time" form:"end_time"`
	DeviceId      string `json:"device_id" form:"device_id"`
	IgnoreDevices string `json:"ignore_devices" form:"ignore_devices"`
	UsePfs        bool   `json:"use_pfs" form:"use_pfs"`
	Name          string `json:"name" form:"name"`
}

type SnapshotMessage struct {
	CommonUriParam
	Update bool `json:"update" form:"update"`
}

type DiagnosisListRequest struct {
	CommonUriInTimeRangeParam
	DeviceId string `json:"device_id" form:"device_id" uri:"device_id"`
	Type     *int   `json:"type" form:"type" uri:"type"`
	Module   string `json:"module" form:"module" uri:"module"`
}

type UpdateCameraJudgeResultRequest struct {
	DeviceId    string `json:"device_id"`
	CameraType  string `json:"camera_type"`
	JudgeResult *int   `json:"judge_result"`
	InBlacklist *bool  `json:"in_blacklist"`
}

type GetCameraDeviceRequest struct {
	Area           string `json:"area" form:"area"`
	DeviceId       string `json:"device_id" form:"device_id"`
	CameraType     string `json:"camera_type" form:"camera_type"`
	JudgeResult    *int64 `json:"judge_result" form:"judge_result"`
	PredictResult  *int64 `json:"predict_result" form:"predict_result"`
	InBlacklist    *bool  `json:"in_blacklist" form:"in_blacklist"`
	NeedAcceptance *bool  `json:"need_acceptance" form:"need_acceptance"`
	PageNo         int    `json:"page_no" form:"page_no"`
	PageSize       int    `json:"page_size" form:"page_size"`
}

type GetCameraDetailRequest struct {
	DeviceId   string `json:"device_id,omitempty" form:"device_id"`
	CameraType string `json:"camera_type,omitempty" form:"camera_type"`
}
type MongoGetCameraDetails struct {
	ImageGenTime int64  `json:"image_gen_time,omitempty" bson:"image_gen_time"`
	ImageUrl     string `json:"image_url,omitempty" bson:"image_url"`
	CameraType   string `json:"camera_type,omitempty" bson:"camera_type"`
}

type GetCertRecordRequest struct {
	DeviceType   string `form:"device_type" binding:"required"`
	QrCode       string `form:"qr_code"`
	SerialNumber string `form:"serial_number"`
}

type BrownDragonCertRecordData struct {
	UserId           string   `json:"user_id" bson:"user_id" binding:"required"`
	DeviceType       string   `json:"device_type" bson:"device_type" binding:"required"`
	Timestamp        int64    `json:"timestamp" bson:"timestamp" binding:"required"`
	DeviceIdentifier string   `json:"device_identifier" bson:"device_identifier" binding:"required"`
	Ip               string   `json:"ip" bson:"ip"`
	QrCode           string   `json:"qr_code" bson:"qr_code"`
	SerialNumber     string   `json:"serial_number" bson:"serial_number"`
	ResourceId       string   `json:"resource_id" bson:"resource_id"`
	CertType         []string `json:"cert_type" bson:"cert_type" binding:"required"`
	Result           string   `json:"result" bson:"result" binding:"required"`
	ErrString        string   `json:"err_string" bson:"err_string"`
	Step             int      `json:"step" bson:"step" binding:"required"`
}

type GetCountFTTRequest struct {
	Day           int64  `form:"day"`
	AlgorithmName string `form:"algorithm_name"`
}

type TheoreticalFTTRequest struct {
	FTT float64 `json:"ftt"`
}

type MEMSTorqueMessageRequest struct {
	LoadFrom                   string            `json:"loadFrom"`
	EqpName                    string            `json:"eqpName"`
	ModelName                  string            `json:"modelName"`
	ApiType                    string            `json:"apiType"`
	UniqueId                   string            `json:"uniqueId"`
	SerialNumber               string            `json:"serialNumber"`
	CalibrationDate            string            `json:"calibrationDate"`
	CalibrationOrg             string            `json:"calibrationOrg"`
	CalibrationSpecification   string            `json:"calibrationSpecification"`
	Calibrator                 []CalibratorInfo  `json:"calibrator"`
	MeasurementInformationList []MeasurementInfo `json:"measurementInformationList"`
}
type CalibratorInfo struct {
	CalibratorcNo   string `json:"calibratorcNo"`
	CalibrationCode string `json:"calibrationCode"`
}
type MeasurementInfo struct {
	MeasureId      string  `json:"measureId"`
	MeasureFeature string  `json:"measureFeature"`
	MeasureUnit    string  `json:"measureUnit"`
	TargetValue    int     `json:"targetValue"`
	Average        float64 `json:"average"`
}

type NewPowerParams struct {
	Name                 string           `json:"name" binding:"required"`
	Type                 string           `json:"type" binding:"required"`
	DeviceIds            []string         `json:"device_ids" binding:"required"`
	StartTimeReserved    int64            `json:"start_time_reserved" binding:"required"`
	EndTimeReserved      int64            `json:"end_time_reserved" binding:"required"`
	ReservedBatteries    map[string]int32 `json:"reserved_batteries" binding:"required"`
	WinningBidUpCapacity *int32           `json:"winning_bid_up_capacity"`
	WinningBidPrice      *float32         `json:"winning_bid_price"`
}

type AlarmListRequest struct {
	CommonUriInTimeRangeParam
	AlarmType   *int   `json:"alarm_type" form:"alarm_type" uri:"alarm_type"`
	DataId      string `json:"data_id" form:"data_id" uri:"data_id"`
	AlarmLevel  *int   `json:"alarm_level" form:"alarm_level" uri:"alarm_level"`
	State       *int   `json:"state" form:"state" uri:"state"`
	DeviceId    string `json:"device_id" form:"device_id" uri:"device_id"`
	BatteryId   string `json:"battery_id" form:"battery_id" uri:"battery_id"`
	EvBrand     string `json:"ev_brand" form:"ev_brand" uri:"ev_brand"`
	EvType      string `json:"ev_type" form:"ev_type" uri:"ev_type"`
	CarPlatform string `json:"car_platform" form:"car_platform" uri:"car_platform"`
	Download    bool   `json:"download" form:"download"`
}

type AlarmSectionAnalysisRequest struct {
	CommonUriParam
	DeviceIds              string `json:"device_ids" form:"device_ids" uri:"device_ids"`
	SectionType            int    `json:"section_type" form:"section_type" uri:"section_type"`
	Section1StartTimestamp int64  `json:"section_1_start_timestamp" form:"section_1_start_timestamp" uri:"section_1_start_timestamp"`
	Section1EndTimestamp   int64  `json:"section_1_end_timestamp" form:"section_1_end_timestamp" uri:"section_1_end_timestamp"`
	Section2StartTimestamp int64  `json:"section_2_start_timestamp" form:"section_2_start_timestamp" uri:"section_2_start_timestamp"`
	Section2EndTimestamp   int64  `json:"section_2_end_timestamp" form:"section_2_end_timestamp" uri:"section_2_end_timestamp"`
	DataIds                string `json:"data_ids" form:"data_ids" uri:"data_ids"`
	HappenTimes            *int   `json:"happen_times" form:"happen_times" uri:"happen_times"`
	AlarmLevels            string `json:"alarm_levels" form:"alarm_levels" uri:"alarm_levels"`
	InService              *bool  `json:"in_service" form:"in_service"`
}

type GetLprRequest struct {
	CommonUriParam
	DsPlateno         *string `json:"ds_plateno" form:"ds_plateno" uri:"ds_plateno"`
	LprPlateno        *string `json:"lpr_plateno" form:"lpr_plateno" uri:"lpr_plateno"`
	VehicleId         *string `json:"vehicle_id" form:"vehicle_id" uri:"vehicle_id"`
	OwnerStatus       *string `json:"owner_status" form:"owner_status" uri:"owner_status"`
	EquityStockStatus *string `json:"equity_stock_status" form:"equity_stock_status" uri:"equity_stock_status"`
	Download          bool    `json:"download" form:"download" uri:"download"`
}

type AlarmLogRequest struct {
	Md5     string `json:"md5" form:"md5" binding:"required"`
	Total   int    `json:"total" form:"total" binding:"required"`
	PartId  int    `json:"part_id" form:"part_id" binding:"required"`
	AlarmTs int64  `json:"alarm_ts" form:"alarm_ts" binding:"required"`
}

type ProcessStatusRequest struct {
	CommonUriInTimeRangeParam
	Project     *string `form:"project"`
	DeviceId    *string `form:"device_id"`
	ProcessId   *string `form:"process_id"`
	ProcessName *string `form:"process_name"`
	Reason      *int32  `form:"reason"`
	Level       *int32  `form:"level"`
}

type FireAlarmRequest struct {
	CommonUriInTimeRangeParam
	Project  *string `form:"project"`
	DeviceId *string `form:"device_id"`
}

type CMSOrderInfoRequest struct {
	RequestId        string `json:"request_id" binding:"required"`
	DeviceId         string `json:"device_id" binding:"required"`
	ModelTriggerTime int64  `json:"model_trigger_time" binding:"required"`
	OrderPredictType int    `json:"order_predict_type"`
}

type GrootDiagnosisRequest struct {
	CommonUriInTimeRangeParam
	DeviceId      string `json:"device_id" form:"device_id"`
	Levels        string `json:"levels" form:"levels"` // 诊断等级
	Modes         string `json:"modes" form:"modes"`   // 0周期上传 1主动上传
	DiagnosisType string `json:"diagnosis_type" form:"diagnosis_type"`
}

type GetGrootDeviceRequest struct {
	Keyword string `json:"keyword" form:"keyword"`
	Limit   int64  `json:"limit" form:"limit"`
}

type UploadPsosResultRequest struct {
	RequestId             string `json:"request_id" form:"request_id" binding:"required"`
	ModelTriggerTimestamp int64  `json:"model_trigger_timestamp" form:"model_trigger_timestamp"`
}

type PsosSimulationHearRequest struct {
	TaskId       string  `json:"task_id" form:"task_id" binding:"required"`
	SimulationId string  `json:"simulation_id" form:"simulation_id" binding:"required"`
	Progress     float64 `json:"progress" form:"task_id" `
}

type ListPsosTasksRequest struct {
	CommonUriParam
	TaskName string `json:"task_name" form:"task_name" `
	Status   string `json:"status" form:"status" `
	Creator  string `json:"creator" form:"creator" `
	TaskId   string `json:"task_id" form:"task_id" `
}

type ListPsosSimulationRequest struct {
	CommonUriParam
	SimulationId string `json:"simulation_id" form:"simulation_id" `
	TaskId       string `json:"task_id" form:"task_id" `
	ConfigId     string `json:"config_id" form:"config_id" `
}

type StopPsosTaskRequest struct {
	TaskId string `json:"task_id" form:"task_id" binding:"required"`
}

type SAPAAlarmListRequest struct {
	StartTime int64  `form:"start_time" json:"start_time" uri:"start_time"`
	EndTime   int64  `form:"end_time" json:"end_time" uri:"end_time"`
	DeviceId  string `json:"device_id" form:"device_id" uri:"device_id"`
}

type SAPAStatPanelRequest struct {
	StartTime int64  `form:"start_time" json:"start_time" uri:"start_time"`
	EndTime   int64  `form:"end_time" json:"end_time" uri:"end_time"`
	DeviceId  string `json:"device_id" form:"device_id" uri:"device_id"`
}

type ListStuckAlarmRequest struct {
	CommonUriInTimeRangeParam
	DeviceId   *string `json:"device_id" form:"device_id"`
	AlarmId    *string `json:"alarm_id" form:"alarm_id"`
	ServiceId  *string `json:"service_id" form:"service_id"`
	AlarmLevel *int32  `json:"alarm_level" form:"alarm_level"`
}

type ListJiraRequest struct {
	Type string `json:"type" form:"type"`
	Key  string `json:"key" form:"key"`
}

type ListStuckServiceRequest struct {
	CommonUriInTimeRangeParam
	DeviceId       string `json:"device_id" form:"device_id"`
	Project        string `json:"project" form:"project"`
	VehicleId      string `json:"vehicle_id" form:"vehicle_id"`
	AnalysisStatus string `json:"analysis_status" form:"analysis_status"` // 0未分析 1已分析
	AnalysisUser   string `json:"analysis_user" form:"analysis_user"`
	EvBrand        string `json:"ev_brand" form:"ev_brand"`
	EvType         string `json:"ev_type" form:"ev_type"`
}

type UpdateStuckServiceRequest struct {
	ServiceId      string `json:"service_id" form:"service_id" binding:"required"`
	BugUrl         string `json:"bug_url" form:"bug_url"`
	JiraUrl        string `json:"jira_url" form:"jira_url"`
	Aware          bool   `json:"aware" form:"aware"`
	Result         string `json:"result" form:"result"`
	AnalysisStatus int    `json:"analysis_status" form:"analysis_status"`
	AnalysisUser   string `json:"analysis_user" form:"analysis_user"`
}

type GetStuckServiceStepAnalysisRequest struct {
	CommonUriInTimeRangeParam
	Project  string `json:"project" form:"project" binding:"required"`
	DeviceId string `json:"device_id" form:"device_id"`
}

type ListDeviceStuckStatRequest struct {
	CommonUriInTimeRangeParam
	Project  string `json:"project" form:"project"`
	DeviceId string `json:"device_id" form:"device_id"`
}

type ListDeviceBluetoothDisconnectStatRequest struct {
	CommonUriInTimeRangeParam
	Project   string `json:"project" form:"project"`
	DeviceIds string `json:"device_ids" form:"device_ids"`
	IsService *bool  `json:"is_service" form:"is_service"`
}

type GetStuckStatPanelRequest struct {
	CommonUriInTimeRangeParam
	Project  string `json:"project" form:"project"`
	DeviceId string `json:"device_id" form:"device_id"`
}

type GetBluetoothDisconnectPanelRequest struct {
	CommonUriInTimeRangeParam
	Project   string `json:"project" form:"project"`
	IsService *bool  `json:"is_service" form:"is_service"`
	DeviceIds string `json:"device_ids" form:"device_ids"`
}

type GetBluetoothDisconnectAlarmRequest struct {
	CommonUriInTimeRangeParam
	IsService *bool  `json:"is_service" form:"is_service"`
	DeviceIds string `json:"device_ids" form:"device_ids"`
}

type V2GDevice struct {
	DeviceId    string `json:"device_id"`
	ConnectorId string `json:"connector_id"`
}

type V2GRemoteControlRequest struct {
	DeviceList    []V2GDevice `json:"device_list"`
	WorkType      string      `json:"work_type"` // 4:开始充/放电，5:结束充/放电
	WorkMode      string      `json:"work_mode"` // 0:充电，1:放电
	DischargeTime *string     `json:"discharge_time"`
	StopSoc       *string     `json:"stop_soc"`
}

type GetOperationLogRequest struct {
	CommonUriInTimeRangeParam
	Type                 int     `form:"type" json:"type"`
	Lang                 string  `form:"lang" json:"lang"`
	OperationInterface   *string `form:"operation_interface" json:"operation_interface"`
	OperationDescription *string `form:"operation_description" json:"operation_description"`
	Operator             *string `form:"operator" json:"operator"`
}

type UploadMetricRequest struct {
	Timestamp   int64                `json:"timestamp" form:"timestamp" binding:"required"`
	GaugeMetric []map[string]float64 `json:"gauge_metric" form:"gauge_metric"`
}

type UploadEventRequest struct {
	Timestamp int64         `json:"timestamp" form:"timestamp" binding:"required"`
	Event     []DeviceEvent `json:"event" form:"event"`
}

type DeviceEvent struct {
	Info string `json:"info" form:"info"`
}

type ListServiceVisualRequest struct {
	CommonUriInTimeRangeParam
	OrderId         string `json:"order_id" form:"order_id"`
	VehiclePlatform string `json:"vehicle_platform" form:"vehicle_platform"`
	VehicleTypes    string `json:"vehicle_types" form:"vehicle_types"`
	VehicleBrands   string `json:"vehicle_brands" form:"vehicle_brands"`
	DeviceId        string `json:"device_id" form:"device_id"`
	VehicleId       string `json:"vehicle_id" form:"vehicle_id"`
	ServiceResults  string `json:"service_results" form:"service_results"`
}

type GetServiceVisualRequest struct {
}

type ListAlgorithmDailyReportRequest struct {
	CommonUriInTimeRangeParam
	DeviceId  string `json:"device_id" form:"device_id"`
	Algorithm string `json:"algorithm" form:"algorithm"`
}

type ListPowerChargeOrdersRequest struct {
	CommonUriInTimeRangeParam
	OrderId            string `json:"order_id" form:"order_id"`
	DeviceId           string `json:"device_id" form:"device_id"`
	ResourceId         string `json:"resource_id" form:"resource_id"`
	ServiceStopReasons string `json:"service_stop_reasons" form:"service_stop_reasons"`
	OrderStatus        string `json:"order_status" form:"order_status"`
}

type DeviceVersionBlacklistRequest struct {
	Blacklist  bool     `json:"blacklist"`
	DeviceList []string `json:"device_list"`
}

type DeviceVersionWorksheetRequest struct {
	DeviceList           []string `json:"device_list"`
	TargetVersion        string   `json:"target_version"`
	WorksheetDescription string   `json:"worksheet_description"`
}

type RestorePLCRequest struct {
	ServiceStartTime int64  `json:"service_start_time" form:"service_start_time"`
	ServiceId        string `json:"service_id" form:"service_id"`
}
