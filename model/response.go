package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"

	um "git.nevint.com/golang-libs/common-utils/model"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"
)

type AddTotalResponse struct {
	um.Base
	Total int         `json:"total"`
	Data  interface{} `json:"data,omitempty"`
}

type Response struct {
	um.Base
	Data interface{} `json:"data,omitempty"`
}

type AlarmSectionAnalysisVO struct {
	DataId               string  `json:"data_id"`
	DataIdDescription    string  `json:"data_id_description"`
	AlarmLevel           int     `json:"alarm_level"`
	AlarmType            int     `json:"alarm_type"`
	Section1Count        int     `json:"section_1_count"`
	Section2Count        int     `json:"section_2_count"`
	Section2DiveSection1 float64 `json:"section_2_dive_section_1"`
}

type AlarmSectionAnalysisResponse struct {
	um.Base
	Total int64                    `json:"total"`
	Data  []AlarmSectionAnalysisVO `json:"data"`
}

type TTPSData struct {
	Timestamp int64 `json:"timestamp" form:"timestamp"`
	Torque    int32 `json:"torque" form:"torque"`
	Position  int64 `json:"position" form:"position"`
	Speed     int32 `json:"speed" form:"speed"`
}

type PLCRecordsResponse struct {
	um.Base
	Project   string                `json:"project" form:"project"`
	DeviceId  string                `json:"device_id" form:"device_id"`
	ServiceId string                `json:"service_id" form:"service_id"`
	StartTime int64                 `json:"start_time" form:"start_time"`
	EndTime   int64                 `json:"end_time" form:"end_time"`
	Data      map[string][]TTPSData `json:"data" form:"data"`
}

type PLCRecordsForFeatureCalculationResponse struct {
	AddTotalResponse
	Project   string `json:"project" form:"project"`
	DeviceId  string `json:"device_id" form:"device_id"`
	ServiceId string `json:"service_id" form:"service_id"`
	StartTime int64  `json:"start_time" form:"start_time"`
	EndTime   int64  `json:"end_time" form:"end_time"`
}

type SensorData struct {
	Timestamp int64   `json:"timestamp" form:"timestamp"`
	Value     float64 `json:"value" form:"value"`
}

type CommonConverterData struct {
	ErrCode   int `json:"err_code" form:"err_code"`
	Current   int `json:"current" form:"current"`
	Power     int `json:"power" form:"power"`
	Frequency int `json:"frequency" form:"frequency"`
}

type ConverterData struct {
	CommonConverterData
	Timestamp int64 `json:"timestamp" form:"timestamp"`
}

type DIRecordsData struct {
	Timestamp int64 `json:"timestamp" form:"timestamp"`
	Value     int   `json:"value" form:"value"`
}

type OperationLogsData struct {
	um.Base
	Total         int                  `json:"total"`
	Page          int                  `json:"page"`
	Size          int                  `json:"size"`
	StartTime     int64                `json:"start_time"`
	EndTime       int64                `json:"end_time"`
	OperationLogs []MPCOperationLogPS2 `json:"data"`
}

type GetDownloadCommandResponse struct {
	DownloadCommand string `json:"download_command" bson:"download_command" form:"download_command"`
	CreateTs        int64  `json:"create_ts" bson:"create_ts" form:"create_ts"`
}

type OSSAuthResponse struct {
	RequestId  string  `json:"request_id" form:"request_id"`
	ResultCode string  `json:"result_code" form:"result_code"`
	ServerTime int64   `json:"server_time" form:"server_time"`
	Data       OSSData `json:"data" form:"data"`
}

type OSSData struct {
	Timeout      string `json:"timeout" form:"timeout"`
	WorkNo       string `json:"workNo" form:"workNo"`
	AccessToken  string `json:"access_token" form:"access_token"`
	RefreshToken string `json:"refresh_token" form:"refresh_token"`
}

type OAuthResponse struct {
	um.Base
	Project   string      `json:"project,omitempty" form:"project,omitempty"`
	DeviceId  string      `json:"device_id,omitempty" form:"device_id,omitempty"`
	UserId    string      `json:"user_id,omitempty" form:"user_id,omitempty"`
	Role      int         `json:"role" form:"role"`
	TargetURL string      `json:"target_url,omitempty" form:"target_url,omitempty"`
	Data      interface{} `json:"data,omitempty" form:"data,omitempty"`
}

type QRResponse struct {
	um.Base
	Data string `json:"data" form:"data"`
}

type ServiceInfoData struct {
	Description      string `json:"description" form:"description"`
	DeviceId         string `json:"device_id" form:"device_id"`
	ServiceId        string `json:"service_id" form:"service_id"`
	EVBatteryId      string `json:"ev_battery_id" form:"ev_battery_id"`
	ServiceBatteryId string `json:"service_battery_id" form:"service_battery_id"`
	EvId             string `json:"ev_id" form:"ev_id"`
	HasNormalImage   bool   `json:"has_normal_image" form:"has_normal_image"`
	HasAbnormalImage bool   `json:"has_abnormal_image" form:"has_abnormal_image"`
	IsStuck          bool   `json:"is_stuck" form:"is_stuck"`
	StartTime        int64  `json:"service_start_time" form:"service_start_time"`
	EndTime          int64  `json:"service_end_time" form:"service_end_time"`
	UpdatedTime      int64  `json:"updated_time" form:"updated_time"`
	FinishResult     int32  `json:"finish_result" form:"finish_result"`
	EvBrand          string `json:"ev_brand" form:"ev_brand"`
	EvType           string `json:"ev_type" form:"ev_type"`
}

type ServiceHistoryInfoData struct {
	Description      string `json:"description" form:"description"`
	Project          string `json:"project" form:"project"`
	DeviceId         string `json:"device_id" form:"device_id"`
	ServiceId        string `json:"service_id" form:"service_id"`
	EVBatteryId      string `json:"ev_battery_id" form:"ev_battery_id"`
	ServiceBatteryId string `json:"service_battery_id" form:"service_battery_id"`
	EvId             string `json:"ev_id" form:"ev_id"`
	FinishResult     int32  `json:"finish_result" form:"finish_result"`
	StartTime        int64  `json:"service_start_time" form:"service_start_time"`
	EndTime          int64  `json:"service_end_time" form:"service_end_time"`
}

type ServiceInfoResponse struct {
	um.Base
	Total           int               `json:"total"`
	Page            int               `json:"page,omitempty"`
	Size            int               `json:"size,omitempty"`
	StartTime       int64             `json:"start_time"`
	EndTime         int64             `json:"end_time"`
	Project         string            `json:"project"`
	ServiceInfoData []ServiceInfoData `json:"data"`
}

type OfflineAccountData struct {
	AccountId string `json:"account_id" bson:"account_id"`
	Password  string `json:"password" bson:"password"`
}

type OfflineAccountResponse struct {
	um.Base
	Data []OfflineAccountData `json:"data"`
}

type UsersData struct {
	Username    string `json:"username" form:"username"`
	UserId      string `json:"user_id" form:"user_id"`
	Role        []int  `json:"role" form:"role"`
	CreatedTime int64  `json:"created_time" form:"created_time"`
}

type UsersListResponse struct {
	um.Base
	Project string      `json:"project" form:"project"`
	Total   int         `json:"total" form:"total"`
	Page    int         `json:"page" form:"page"`
	Size    int         `json:"size" form:"size"`
	Data    []UsersData `json:"data" form:"data"`
}

type User struct {
	Username    string          `json:"username"`
	UserId      string          `json:"user_id"`
	Role        int             `json:"role"`
	UpgradeInfo UserUpgradeInfo `json:"upgrade_info"`
	CreatedTime int64           `json:"created_time"`
}

type UserUpgradeInfo struct {
	IsUpgrade       bool  `json:"is_upgrade"`
	OriginalRole    int   `json:"original_role"`
	UpgradeRole     int   `json:"upgrade_role"`
	ExpireTimestamp int64 `json:"expire_timestamp"`
}

type GetUserByIdResponse struct {
	um.Base
	User User `json:"data"`
}

type OSSCtlCmdResponse struct {
	RequestId   string `json:"request_id"`
	ResultCode  string `json:"result_code"`
	Message     string `json:"message"`
	DebugMsg    string `json:"debug_msg"`
	DisplayMsg  string `json:"display_msg"`
	ServerTime  int    `json:"server_time"`
	EncryptType int    `json:"encrypt_type"`
}

type LogFileResponse struct {
	um.Base
	LogUrl string `json:"log_url" form:"log_url"`
}

type UploadLogHistoryResponse struct {
	um.Base
	Total int                              `json:"total"`
	Page  int                              `json:"page"`
	Size  int                              `json:"size"`
	Data  []mmgo.MongoLogFileUploadHistory `json:"data"`
}

type LogDirectoryTreeResponse struct {
	um.Base
	Total int                     `json:"total"`
	Page  int                     `json:"page"`
	Size  int                     `json:"size"`
	Data  []SyncLogFilePathParams `json:"data"`
}

type SnapshotData struct {
	DeviceId      string `json:"device_id"`
	ServiceId     string `json:"service_id"`
	AlgorithmName string `json:"algorithm_name"`
	Description   string `json:"description"`
	CreateTs      int64  `json:"create_ts"`
	FileGenStatus string `json:"file_gen_status"`
	AllowDownload bool   `json:"allow_download"`
	SnapshotUrl   string `json:"snapshot_url"`
	FilePath      string `json:"file_path"`
}

type SnapshotListResponse struct {
	um.Base
	Total int            `json:"total"`
	Data  []SnapshotData `json:"data"`
}

type SupplierHttp struct {
	URL    string                 `json:"url"`
	Method string                 `json:"method"`
	Header map[string]string      `json:"header"`
	Body   map[string]interface{} `json:"body"`
}

type SupplierSts struct {
	SessionToken string `json:"sessionToken"`
	SecretId     string `json:"secretId"`
	SecretKey    string `json:"secretKey"`
}

type DomainInfo struct {
	Domain     string `json:"domain"`
	Protocol   string `json:"protocol"`
	DomainAttr struct {
		CDN bool `json:"cdn"`
	} `json:"domain_attr"`
}

type UploadTokenResultData struct {
	PhysicalBucketKey string       `json:"physical_bucket_key"`
	FileKey           string       `json:"file_key"`
	Region            string       `json:"region"`
	SupplierHttp      SupplierHttp `json:"supplier_http"`
	SupplierSts       SupplierSts  `json:"supplier_sts"`
	DomainInfoList    []DomainInfo `json:"domain_info_list"`
}

type FMSFileUploadTokenResponse struct {
	ResultCode string                `json:"result_code"`
	ResultData UploadTokenResultData `json:"result_data"`
}

type FileAuthorizeURLResultData struct {
	URLAuthorizeMap map[string]string `json:"file_URL_authorize_URL_map"`
}

type FMSFileAuthorizeURLResponse struct {
	ResultCode string                     `json:"result_code"`
	ResultData FileAuthorizeURLResultData `json:"result_data"`
}

type CreateMultipartResultData struct {
	Supplier          string       `json:"supplier"`
	Region            string       `json:"region"`
	PhysicalBucketKey string       `json:"physical_bucket_key"`
	UploadId          string       `json:"upload_id"`
	FileKey           string       `json:"file_key"`
	DomainInfoList    []DomainInfo `json:"domain_info_list"`
}

// FMSFileCreateMultipartResponse 创建分片上传任务返回值
type FMSFileCreateMultipartResponse struct {
	ResultCode string                    `json:"result_code"`
	ResultDesc string                    `json:"result_desc"`
	ResultData CreateMultipartResultData `json:"result_data"`
}

type MultipartPartToken struct {
	PartNumber   int          `json:"part_number"`
	SupplierHttp SupplierHttp `json:"supplier_http"`
}

type MultipartPresignURLData struct {
	PartTokenList []MultipartPartToken `json:"part_token_list"`
	ExpireTime    int64                `json:"expire_time"`
}

// FMSFileMultipartPresignURLResponse 获取分片上传预签名链接返回值
type FMSFileMultipartPresignURLResponse struct {
	ResultCode string                  `json:"result_code"`
	ResultDesc string                  `json:"result_desc"`
	ResultData MultipartPresignURLData `json:"result_data"`
}

type PredictResponse struct {
	ResultCode string `json:"resultCode"`
	ResultData string `json:"resultData"`
	ResultDesc string `json:"resultDesc"`
}

type CreateApprovalResultData struct {
	FlowInstanceId string `form:"flow_instance_id" json:"flow_instance_id"`
}

type CreateApprovalResponse struct {
	RequestId  string                   `form:"request_id" json:"request_id"`
	ResultCode string                   `form:"result_code" json:"result_code"`
	ResultData CreateApprovalResultData `form:"data" json:"data"`
}

type GetFactoryTestInfoResponse struct {
	um.Base
	TestData []FactoryTestInfo `json:"testData"`
}
type FactoryTestInfo struct {
	DeviceID    string     `json:"device_id"`
	TestTime    int64      `json:"test_time"`
	Description string     `json:"description"`
	Info        string     `json:"info"`
	TestResult  string     `json:"test_result"`
	Data        []GunValue `json:"data"`
}

type GunValue struct {
	GunID       int     `json:"gun_id"`
	TorqueValue string  `json:"torque_value"`
	FirstValue  float64 `json:"first_value"`
	SecondValue float64 `json:"second_value"`
	ThirdValue  float64 `json:"third_value"`
}
type TestData struct {
	ContainerID     string   `json:"containerID"`
	FactoryName     string   `json:"factoryName"`
	Inspectors      []string `json:"inspectors"`
	ProductionID    string   `json:"productionID"`
	PublishOperator string   `json:"publishOperator"`
	PublishTime     int64    `json:"publishTime"`
	Result          []Result `json:"result"`
	SiteQRcode      string   `json:"siteQRcode"`
	StationID       string   `json:"stationID"`
	Version         string   `json:"version"`
}
type Result struct {
	AutomaticTestResults  string              `json:"automaticTestResults"`
	CompleteTime          int64               `json:"completeTime"`
	ManualDescription     ManualDescription   `json:"manualDescription,omitempty"`
	ManualTestResults     string              `json:"manualTestResults"`
	Number                int                 `json:"number"`
	NumberString          string              `json:"numberString"`
	Operator              string              `json:"operator"`
	SoftwareDescription   SoftwareDescription `json:"softwareDescription,omitempty"`
	TestResultDescription string              `json:"testResultDescription"`
	TestResults           string              `json:"testResults"`
}

type SoftwareDescription struct {
	All int       `json:"all"`
	Res []float64 `json:"res"`
	St  int       `json:"st"`
	Stp string    `json:"stp"`
	Sub int       `json:"sub"`
	Val []float64 `json:"val"`
}
type ManualDescription struct {
	ModuleName []interface{} `json:"moduleName"`
	Result     []string      `json:"result"`
	Target     []interface{} `json:"target"`
}

type LogApprovalHistory struct {
	Project          string        `json:"project" bson:"project"`
	DeviceId         string        `json:"device_id" bson:"device_id"`
	Description      string        `json:"description" bson:"description"`
	Reason           string        `json:"reason" bson:"reason"`
	FlowInstanceId   string        `json:"flow_instance_id" bson:"flow_instance_id"`
	Status           string        `json:"status" bson:"status"`
	UpdatedTime      int64         `json:"updated_time" bson:"updated_time"`
	Details          []RequestFile `json:"details" bson:"details"`
	UserId           string        `json:"user_id" bson:"user_id"`
	CurrentNodeName  string        `json:"current_node_name" bson:"current_node_name"`
	PreviousNodeName string        `json:"previous_node_name" bson:"previous_node_name"`
}

type LogApprovalHistoryResponse struct {
	um.Base
	Total int                  `json:"total"`
	Page  int                  `json:"page"`
	Size  int                  `json:"size"`
	Data  []LogApprovalHistory `json:"data"`
}

type VersionAlgorithmData struct {
	VersionInfo
	CanEdit     bool `json:"can_edit"`
	UpdateCount int  `json:"update_count"`
}

type VersionAlgorithmDataResponse struct {
	um.Base
	Total int                    `json:"total"`
	Data  []VersionAlgorithmData `json:"data"`
}

type VersionAlgorithmNameResponse struct {
	um.Base
	Data []string `json:"data"`
}

type VersionPublishTsResponse struct {
	um.Base
	Data []int64 `json:"data"`
}

type DataVersioningData struct {
	Id               int64          `json:"id"`
	AlgorithmName    string         `json:"algorithm_name"`
	AlgorithmVersion string         `json:"algorithm_version"`
	PublishTs        int64          `json:"publish_ts"`
	HasQMDataset     bool           `json:"has_qm_dataset"`
	CreateTs         int64          `json:"create_ts"`
	TotalSize        int64          `json:"total_size"`
	IfCanEdit        bool           `json:"if_can_edit"`
	FileInfo         []umw.FileInfo `json:"file_info"`
}

type DataVersioningResponse struct {
	um.Base
	Total int                  `json:"total"`
	Data  []DataVersioningData `json:"data"`
}

type ImageResponse struct {
	um.Base
	umw.MongoImage
}

type DeviceStatusResponse struct {
	um.Base
	Total int           `json:"total"`
	Data  []interface{} `json:"data"`
}

type OSSPrimeResponse struct {
	RequestId   string `json:"request_id"`
	ResultCode  string `json:"result_code"`
	Message     string `json:"message"`
	ServerTime  int    `json:"server_time"`
	EncryptType int    `json:"encrypt_type"`
}

type DeviceDetails struct {
	Total   int           `json:"total_results"`
	Results []interface{} `json:"result_list"`
}

type PrimeDeviceDetails struct {
	OSSPrimeResponse
	Data DeviceDetails `json:"data"`
}

type FactoryUploadDataResponse struct {
	um.Base
	RequestId string `json:"request_id" form:"request_id"`
}

type FactoryTestDataDetails struct {
	ID            string  `json:"id"`
	DeviceId      string  `json:"device_id"`
	ReportGenTime int64   `json:"report_gen_time"`
	Manufacturer  string  `json:"manufacturer"`
	Reporter      string  `json:"reporter"`
	TestType      int     `json:"test_type"`
	FailCount     int     `json:"fail_count"`
	SuccessRate   float64 `json:"success_rate"`
	HasReport     bool    `json:"has_report"`
}

type FactoryDataResponse struct {
	um.Base
	Total     int64         `json:"total"`
	Page      int           `json:"page"`
	Size      int           `json:"size"`
	StartTime int64         `json:"start_time"`
	EndTime   int64         `json:"end_time"`
	Project   string        `json:"project"`
	Data      []interface{} `json:"data"`
}

type FactoryTorqueDataDetails struct {
	ID                 string                  `json:"id" form:"id"`
	RequestId          string                  `json:"request_id" form:"request_id"`
	DeviceId           string                  `json:"device_id" form:"device_id"`
	ServicesCount      int                     `json:"services_count" form:"services_count"`
	ServicesValidCount int                     `json:"services_valid_count" form:"services_valid_count"`
	ServicesInfo       []umw.ReportServiceInfo `json:"services_info"`
	StartTime          int64                   `json:"start_time" form:"start_time"`
	EndTime            int64                   `json:"end_time" form:"end_time"`
	ReportGenTime      int64                   `json:"report_gen_time" form:"report_gen_time"`
}

type AddNewDatasetResponse struct {
	um.Base
}

type TankTransData struct {
	TransId         string `json:"trans_id" form:"trans_id"`
	TransStartTS    int64  `json:"trans_start_ts" form:"trans_start_ts"`
	TransEndTS      int64  `json:"trans_end_ts" form:"trans_end_ts"`
	FullChargedSlot string `json:"full_charged_slot" form:"full_charged_slot"`
	DrianedSlot     string `json:"drianed_slot" form:"drianed_slot"`
	EmptySlot       string `json:"empty_slot" form:"empty_slot"`
}

type TankTransResponse struct {
	Response
	TankTransData
}

type BatteryRefreshListResponse struct {
	um.Base
	Total int                `json:"total"`
	Data  []BatteryRefreshVO `json:"data"`
}

type BatteryRefreshVO struct {
	DeviceId             string `json:"device_id"`
	StartTime            int64  `json:"start_time"`
	EndTime              int64  `json:"end_time"`
	BatteryId            string `json:"battery_id"`
	RefreshType          string `json:"refresh_type"`
	RefreshResult        string `json:"refresh_result"`
	BatteryCapacity      string `json:"battery_capacity"`
	RefreshTargetVersion string `json:"refresh_target_version"`
	BatteryRoad          string `json:"battery_road"`

	OssRequestId              *string `json:"oss_request_id"`
	OssCommandBatteryId       *string `json:"oss_command_battery_id"`
	OssCommandBatteryCapacity *string `json:"oss_command_battery_capacity"`
	AuthenticateTimestamp     *int64  `json:"authenticate_timestamp"`
	OrderId                   *string `json:"order_id"`
}

type DetectResponse struct {
	um.Base
	Abnormal    bool   `json:"abnormal"`
	AbnormalImg string `json:"abnormal_img"`
	SHA512      string `json:"sha512"`
}

type ImageTypeListResponse struct {
	um.Base
	Total int                      `json:"total"`
	Data  []map[string]interface{} `json:"data"`
}

type ImageDetails struct {
	ServiceId        string `json:"service_id" form:"service_id"`
	BatteryId        string `json:"battery_id" form:"battery_id"`
	ImageType        int    `json:"image_type" form:"image_type"`
	ImageCNType      string `json:"image_cn_type" form:"image_cn_type"` // to be deprecated
	ImageDescription string `json:"image_description" form:"image_description"`
	ImageSize        int64  `json:"image_size" form:"image_size"`
	ImageName        string `json:"image_name" form:"image_name"`
	ImageURL         string `json:"image_url" form:"image_url"`
	Abnormal         bool   `json:"abnormal" form:"abnormal"`
	CameraType       string `json:"camera_type" form:"camera_type"`
	VehicleType      string `json:"vehicle_type" form:"vehicle_type"`
	ImageGenTime     int64  `json:"image_gen_time" form:"image_gen_time"`
}

type ImageListResponse struct {
	um.Base
	Total     int64          `json:"total"`
	Page      int            `json:"page"`
	Size      int            `json:"size"`
	StartTime int64          `json:"start_time"`
	EndTime   int64          `json:"end_time"`
	Data      []ImageDetails `json:"data"`
}

type InternalImageDataResponse struct {
	um.Base
	Total int                 `json:"total"`
	Data  []InternalImageData `json:"data"`
}
type InternalImageData struct {
	Id           string `json:"id"`
	ImageName    string `json:"image_name"`
	DeviceId     string `json:"device_id"`
	CameraType   string `json:"camera_type"`
	ImageGenTime int64  `json:"image_gen_time"`
	ImageSize    int64  `json:"image_size"`
	ImageUrl     string `json:"image_url"`
}
type AlgorithmNameList struct {
	um.Base
	Data []AlgorithmName `json:"data"`
}
type AlgorithmName struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

// 旧 owl查询返回接口，切换后删除
type DataCollectionResponse struct {
	Id          primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	ImageName   string             `json:"image_name" bson:"image_name"`
	DeviceId    string             `json:"device_id" bson:"device_id"`
	CameraName  string             `json:"camera_name" bson:"camera_name"`
	CreateTime  int64              `json:"create_time" bson:"create_time"`
	Size        int64              `json:"size" bson:"size"`
	VehicleType string             `json:"vehicle_type" bson:"vehicle_type"`
	ImageUrl    string             `json:"image_url" bson:"image_url"`
}

// 旧 owl查询返回接口，切换后删除
type MgoImageInfo struct {
	Id            primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	DeviceId      string             `json:"device_id" bson:"device_id"`
	ImageSize     int64              `json:"image_size" bson:"image_size"`
	ImageMinioUrl string             `json:"image_url" bson:"image_url"`
	CreateTs      int64              `json:"create_ts" bson:"create_ts"`
	VehicleType   string             `json:"vehicle_type" bson:"vehicle_type"`
}

type RealtimeValue struct {
	Timestamp int64       `json:"timestamp"`
	Value     interface{} `json:"value"`
}

type RealtimeV1Data struct {
	Type string          `json:"type"`
	Data []RealtimeValue `json:"data"`
}

type DailySuccessRate struct {
	Day         int64   `json:"day"`
	SuccessRate float64 `json:"success_rate"`
}

type DeviceSuccessRate struct {
	DeviceId    string  `json:"device_id"`
	Description string  `json:"description"`
	SuccessRate float64 `json:"success_rate"`
}

type TroubleShootingList struct {
	DeviceId         string `json:"device_id"`
	RequestId        string `json:"request_id"`
	Module           string `json:"module"`
	Timestamp        int64  `json:"timestamp"`
	CanCauseShutdown bool   `json:"can_cause_shutdown"`
	Type             int    `json:"type"`
}

type DataIdMap struct {
	Timestamp        int64  `json:"timestamp"`
	DataId           string `json:"data_id"`
	CanCauseShutdown bool   `json:"can_cause_shutdown"`
}

type DiagnosisListResponse struct {
	um.Base
	Total     int                   `json:"total"`
	Page      int                   `json:"page"`
	Size      int                   `json:"size"`
	StartTime int64                 `json:"start_time" form:"start_time"`
	EndTime   int64                 `json:"end_time" form:"start_time"`
	Data      []TroubleShootingList `json:"data"`
}

type SnapShotDiagnosisResponse struct {
	um.Base
	DataIdList []DataIdMap                `json:"data_ids_list"`
	Data       *umw.MongoSnapshotRealtime `json:"data"`
}

type CameraInfo struct {
	CameraName   string `json:"camera_name,omitempty" bson:"camera_name,omitempty"`
	CameraNameEn string `json:"camera_name_en,omitempty" bson:"camera_name_en,omitempty"`
	CameraType   string `json:"camera_type,omitempty" bson:"camera_type,omitempty"`
	MaskUrl      string `json:"mask_url,omitempty" bson:"mask_url,omitempty"`
	Algorithm    string `json:"algorithm,omitempty" bson:"algorithm,omitempty"`
}
type CameraInfoResponse struct {
	um.Base
	Data []CameraInfo `json:"data"`
}
type DeviceCameraInfoResponse struct {
	Id                int64            `json:"id"`
	DeviceId          string           `json:"device_id" bson:"device_id" form:"device_id"`
	DeviceName        string           `json:"device_name" bson:"device_name" form:"name"`
	Area              string           `json:"area" bson:"area" form:"area"`
	CameraTotal       int              `json:"camera_total" bson:"camera_total"`
	NormalCameraTotal int              `json:"normal_camera_total" bson:"normal_camera_total"`
	NeedAcceptance    bool             `json:"need_acceptance" bson:"need_acceptance"`
	CameraResult      []CameraTypeInfo `json:"camera_info" bson:"camera_result" form:"camera_result"`
}
type CameraTypeInfo struct {
	CameraName  string `json:"camera_name" bson:"camera_name" form:"camera_name"`
	CameraType  string `json:"camera_type" bson:"camera_type" form:"camera_type"`
	JudgeResult int64  `json:"judge_result" bson:"judge_result" form:"judge_result"`
	JudgeBy     string `json:"judge_by" bson:"judge_by" form:"judge_by"`
	JudgeTs     int64  `json:"judge_ts" bson:"judge_ts" form:"judge_ts"`
	MaskUrl     string `json:"mask_url" bson:"mask_url" form:"mask_url"`
	// 	predict result by AI
	//	0:empty, without ai predict result
	//	1:pass
	//	2:fail
	//	3:exception, invoke ai api get error return
	PredictType    int64 `json:"predict_type" bson:"predict_type" form:"predict_type"`
	PredictResult  int64 `json:"predict_result" bson:"predict_result" form:"predict_result"`
	PredictTs      int64 `json:"predict_ts" bson:"predict_ts" form:"predict_ts"`
	InBlacklist    bool  `json:"in_blacklist" bson:"in_blacklist" form:"in_blacklist"`
	HasImage       bool  `json:"has_image" bson:"has_image"`
	NeedAcceptance bool  `json:"need_acceptance" bson:"need_acceptance"`
}
type GetCameraDeviceResponse struct {
	um.Base
	Total int64                      `json:"total"`
	Data  []DeviceCameraInfoResponse `json:"data"`
}
type AllDeviceCameraInfoResponse struct {
	DeviceId   string `json:"device_id" bson:"device_id" form:"device_id"`
	DeviceName string `json:"device_name" bson:"description" form:"name"`
	Area       string `json:"area" bson:"area" form:"area"`
}

type DeviceInfo struct {
	Project     string `json:"project" bson:"project"`
	Description string `json:"description" bson:"description"`
	DeviceId    string `json:"device_id" bson:"device_id"`
	CityCompany string `json:"city_company" bson:"city_company"`
	Region      string `json:"region" bson:"region"`
}

type DeviceFavoriteResponse struct {
	um.Base
	Total int          `json:"total"`
	Data  []DeviceInfo `json:"data"`
}

type DeviceDetail struct {
	umw.MongoDeviceInfo
	Favorite bool `json:"favorite"`
}

type GetDeviceListResponse struct {
	um.Base
	Total int64          `json:"total"`
	Data  []DeviceDetail `json:"data"`
}

type GetCameraDetailResponse struct {
	um.Base
	Data []CameraDetail `json:"data"`
}

type CameraDetail struct {
	CameraType string `json:"camera_type,omitempty"`
	ImageUrl   string `json:"image_url,omitempty"`
	CreateTS   int64  `json:"create_ts,omitempty"`
}
type CameraImageInfo struct {
	Area        string `json:"area" bson:"area"`
	DeviceId    string `json:"device_id" bson:"device_id"`
	DeviceName  string `json:"device_name" bson:"device_name,omitempty"`
	CameraType  string `json:"camera_type" bson:"camera_type"`
	CameraName  string `json:"camera_name" bson:"camera_name,omitempty"`
	HasImage    bool   `json:"has_image,omitempty" bson:"has_image"`
	JudgeResult int64  `json:"judge_result" bson:"judge_result"`
	JudgeTs     int64  `json:"judge_ts" bson:"judge_ts"`
	JudgeBy     string `json:"judge_by" bson:"judge_by"`
	// 	predict result by AI
	//	0:empty, without ai predict result
	//	1:pass
	//	2:fail
	//	3:exception, invoke ai api get error return
	PredictType   int64 `json:"predict_type" bson:"predict_type"`
	PredictResult int64 `json:"predict_result" bson:"predict_result" form:"predict_result"`
	PredictTs     int64 `json:"predict_ts" bson:"predict_ts" form:"predict_ts"`
	InBlacklist   bool  `json:"in_blacklist" bson:"in_blacklist"`
}
type CameraImageInfoCsvWebResponse struct {
	um.Base
	Total int64             `json:"total"`
	Data  []CameraImageInfo `json:"data"`
}

type DeviceLoggedData struct {
	um.Base
	Data interface{} `json:"device_identifier_list"`
}

type GetCountFTTResponse struct {
	um.Base
	FTT float64 `json:"ftt"`
	Day int64   `json:"day"`
}

type CMSResult struct {
	NeedExecuted                  *bool   `json:"need_executed"`
	PowerDistributionCapacity     int32   `json:"power_distribution_capacity"`
	BranchCircuitCurrentLimit     int32   `json:"branch_circuit_current_limit"`
	Circuit01DistributionCapacity int32   `json:"circuit_01_distribution_capacity"`
	Circuit02DistributionCapacity int32   `json:"circuit_02_distribution_capacity"`
	MaxAllowedModulePower1        int32   `json:"max_allowed_module_power_1"`
	MaxAllowedModulePower2        int32   `json:"max_allowed_module_power_2"`
	OptimizationLabel             int32   `json:"optimization_label"`
	BeforeSwitchCurrent           float64 `json:"before_switch_current"`
	SwitchSoc                     float64 `json:"switch_soc"`
	AfterSwitchCurrent            float64 `json:"after_switch_current"`
	ChargingMode                  string  `json:"charging_mode"`
	SwitchMoment                  float64 `json:"switch_moment"`
	CurrentCurrent                float64 `json:"current_current"`
}

type CMSCalculationResults struct {
	um.Base
	Version       string              `json:"version"`
	BatteryInfo   map[int32]CMSResult `json:"battery_info"`
	BatteryDemand []umw.BatteryDemand `json:"battery_demand"`
}

type NMPCommandData struct {
	Details []NMPCommandDetails `json:"details"`
}

type NMPCommandDetails struct {
	Result string `json:"result"`
}

type NMPCommandResponse struct {
	Data    NMPCommandData `json:"data"`
	Success int            `json:"success"`
	Failure int            `json:"failure"`
}
type MEMSMeasurementResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data []struct {
		LoadFrom string `json:"loadFrom"`
		UniqueId string `json:"uniqueId"`
		Status   string `json:"status"`
		Msg      string `json:"msg"`
	} `json:"data"`
}

type CreateFileVersionResp struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

type CheckedParamsResponse struct {
	um.Base
	Total int            `json:"total"`
	Data  []DeviceParams `json:"data" form:"data"`
}

type DeviceParams struct {
	ID     string              `json:"device_id,omitempty" bson:"_id,omitempty"`
	Params *[]DeploymentParams `json:"params,omitempty" bson:"params,omitempty"`
}

type DeploymentParams struct {
	Key   int64       `json:"key,required" bson:"key,required"`
	Value interface{} `json:"value,required" bson:"value,required"`
	Type  string      `json:"type,required" bson:"type,required"`
}

type GetLprResponse struct {
	um.Base
	Total int64             `json:"total"`
	Data  []umw.VehicleInfo `json:"data"`
}

type AlarmLogResponse struct {
	um.Base
	PartId int `json:"part_id"`
}

type ProcessStatusData struct {
	DeviceId    string `json:"device_id"`
	Description string `json:"description"`
	Project     string `json:"project"`
	Module      string `json:"module"`
	ProcessId   string `json:"process_id"`
	ProcessName string `json:"process_name"`
	Ts          int64  `json:"ts"`
	Reason      int32  `json:"reason"`
	Level       int32  `json:"level"`
}

type ProcessStatusResponse struct {
	um.Base
	Total int64               `json:"total"`
	Data  []ProcessStatusData `json:"data"`
}

type FireAlarmData struct {
	DeviceId    string     `json:"device_id" bson:"device_id"`
	Description string     `json:"description" bson:"description"`
	Project     string     `json:"project" bson:"project"`
	AlarmTs     int64      `json:"alarm_ts" bson:"alarm_ts"`
	UpdateTs    int64      `json:"update_ts" bson:"update_ts"`
	TotalParts  int        `json:"total_parts" bson:"total_parts"`
	Parts       []LogParts `json:"parts" bson:"parts"`
}

type LogParts struct {
	PartID   int    `json:"part_id" bson:"part_id"`
	FileURL  string `json:"file_url" bson:"file_url"`
	UploadTs int64  `json:"upload_ts" bson:"upload_ts"`
}

type FireAlarmResponse struct {
	um.Base
	Total int64           `json:"total"`
	Data  []FireAlarmData `json:"data"`
}

type FireAlarmReportResponse struct {
	um.Base
	BasicInfo      FireAlarmReportBasicInfo `json:"basic_info"`
	AlarmInfos     []AlarmInfo              `json:"alarm_infos"`
	TransferEvents []FireAlarmTransferEvent `json:"transfer_events"`
}

type FireAlarmReportBasicInfo struct {
	DeviceId          string        `json:"device_id"`
	DeviceName        string        `json:"device_name"`
	Area              string        `json:"area"`
	AlarmCreatets     int64         `json:"alarm_createts"`
	Temperature       *float64      `json:"temperature"`
	FireSpace         string        `json:"fire_space"`
	TransferResult    string        `json:"transfer_result"`      //  未执行:unexecuted 执行中:executing 成功success 执行失败:fail
	FallInWaterResult string        `json:"fall_in_water_result"` //  未执行:unexecuted 执行中:executing 成功success 执行失败:fail
	FireAlarmData     FireAlarmData `json:"fire_alarm_data"`
}

type FireAlarmTransferEvent struct {
	Timestamp   int64  `json:"timestamp"`
	EventName   string `json:"event_name"`
	Status      string `json:"status"` // 未执行:unexecuted 执行成功:success, 执行失败:fail
	IsFailEvent bool   `json:"is_fail_event"`
}

type CMSOrderInfo struct {
	ServedService          umw.ServedService          `json:"served_service"`
	ElectricityPriceInfo   []umw.ElectricityPriceInfo `json:"electricity_price_info"`
	ElectricityPriceModel  string                     `json:"electricity_price_model"`
	SwapServingInfo        umw.SwapServingInfo        `json:"swap_serving_info"`
	SwapNavigationInfo     []string                   `json:"swap_navigation_info"`
	SwapQueueInfo          []string                   `json:"swap_queue_info"`
	PredictedService       map[string]interface{}     `json:"predicted_service"`
	PredictedServiceSource string                     `json:"predicted_service_source,omitempty"`
}

type CMSOrderInfoResponse struct {
	um.Base
	Data CMSOrderInfo `json:"data"`
}

type GrootDiagnoseVO struct {
	DeviceId      string   `json:"device_id"`
	Level         int64    `json:"level"`
	Timestamp     int64    `json:"timestamp"`
	ModeType      int64    `json:"mode"`
	DiagnosisType []string `json:"diagnosis_type"`

	TimeoutCommandProperties        TimeoutCommandPropertiesVO        `json:"timeout_command_properties"`
	ScheduledTimeNotStartProperties ScheduledTimeNotStartPropertiesVO `json:"scheduled_time_not_start_properties"`
	FailUploadingServiceProperties  FailUploadingServicePropertiesVO  `json:"fail_uploading_service_properties"`
	StartWithoutS2CloseProperties   StartWithoutS2ClosePropertiesVO   `json:"start_without_s2_close_properties"`
	CPStatusProperties              CPStatusPropertiesVO              `json:"cP_status_properties"`
	TextProperties                  TextPropertiesVO                  `json:"text_properties"`
}

type TimeoutCommandPropertiesVO struct {
	RemoteControlCommand int64  `json:"remote_control_command"`
	OrderId              string `json:"order_id"`
	CommandTimestamp     int64  `json:"command_timestamp"`
	RebootCount          int64  `json:"reboot_count"`
	RebootTimestamp      int64  `json:"reboot_timestamp"`
	Result               string `json:"result"`
}

type ScheduledTimeNotStartPropertiesVO struct {
	IsOrderModeCharging bool   `json:"is_order_mode_charging"`
	AuthType            int64  `json:"auth_type"`
	HasAuthMsg          bool   `json:"has_auth_msg"`
	AuthOkTimestamp     int64  `json:"auth_ok_timestamp"`
	Result              string `json:"result"`
}

type FailUploadingServicePropertiesVO struct {
	StartReportFinishTimestamp int64  `json:"start_report_finish_timestamp"`
	OverReportFinishTimestamp  int64  `json:"over_report_finish_timestamp"`
	Result                     string `json:"result"`
}

type StartWithoutS2ClosePropertiesVO struct {
	IsPwmOut         bool   `json:"is_pwm_out"`
	PwmOutTimestamp  int64  `json:"pwm_out_timestamp"`
	IsS2Close        bool   `json:"is_s2_close"`
	S2CloseTimestamp int64  `json:"s2_close_timestamp"`
	Result           string `json:"result"`
}

type CPStatusPropertiesVO struct {
	CpStatus string `json:"cp_status"`
	Result   string `json:"result"`
}

type TextPropertiesVO struct {
	Text string `json:"text"`
}

type GrootDiagnosisResponse struct {
	um.Base
	Total int64             `json:"total"`
	Data  []GrootDiagnoseVO `json:"data"`
}

type GetGrootDeviceResponse struct {
	um.Base
	Data []string `json:"data"`
}

type UploadPsosResultResponse struct {
	um.Base
	RequestId string `json:"request_id"`
	// power_swap_event_file_url 换电站事件文件
	DeviceData string `json:"device_data"`
	// charge_event_file_url 充电事件文件
	BatteryData string `json:"battery_data"`
	// charge_event_detail_file_url 充电明细文件
	ServiceData string `json:"service_data"`
}

type DeviceInfoVO struct {
	DeviceId    string `json:"device_id"`
	Description string `json:"description"`
	ResourceId  string `json:"resource_id"`
	Project     string `json:"project"`
	CityCompany string `json:"city_company"`
	Area        string `json:"area"`
	UpdatedTime int64  `json:"updated_time"`
}

type CityCompanyMappingResponse struct {
	um.Base
	Data map[string][]DeviceInfoVO `json:"data"`
}

type ListAllDevicesResponse struct {
	um.Base
	Data []DeviceInfoVO `json:"data"`
}

type SAPAAlarmItem struct {
	AlarmTimeStamp int64  `json:"alarm_time_stamp"`
	DeviceName     string `json:"device_name"`
	DeviceId       string `json:"device_id"`
	CityCompany    string `json:"city_company"`
	ImageUrl       string `json:"image_url"`
}

type SAPAAlarmListResponse struct {
	um.Base
	Total int64           `json:"total"`
	Data  []SAPAAlarmItem `json:"data"`
}

type SAPAStat struct {
	Interval       string  `json:"interval"`
	Occupation     float64 `json:"occupation"`
	Order          float64 `json:"order"`
	TotalOrderNum  int64   `json:"total_order_num"`
	CancelOrderNum int64   `json:"cancel_order_num"`
	FinishOrderNum int64   `json:"finish_order_num"`
	InterruptNum   int64   `json:"interrupt_num"`
	VocNum         int64   `json:"voc_num"`
}

type SAPAStatPanel struct {
	TotalVocNum  int64      `json:"total_voc_num"`
	HourlyDetail []SAPAStat `json:"hourly_detail"`
}

type SAPAStatPanelResponse struct {
	um.Base
	Data SAPAStatPanel `json:"data"`
}

type CommonResponse struct {
	um.Base
	Total int64         `json:"total"`
	Data  []interface{} `json:"data"`
}

type MPCOperationLog struct {
	Timestamp int64  `json:"timestamp"`
	Operation int64  `json:"operation"`
	Page      string `json:"page"`
	Button    string `json:"button"`
	Action    string `json:"action"`
	Args      string `json:"args"`
	UserId    string `json:"user_id"`
	Module    string `json:"module"`
	Remark    string `json:"remark"`
}

type MPCOperationLogPS2 struct {
	Timestamp      int64  `json:"timestamp"`
	SendTimestamp  int64  `json:"send_kfk_ts"`
	WriteTimestamp int64  `json:"write_db_ts"`
	Action         int32  `json:"action"`
	Page           string `json:"page"`
	Button         string `json:"button"`
	UserId         string `json:"user_id"`
	Args           string `json:"args"`
}

type OSSOperationLog struct {
	Timestamp     int64              `json:"timestamp"`
	IsFail        bool               `json:"is_fail"`
	FailureReason string             `json:"failure_reason"`
	AbilityCode   string             `json:"ability_code"`
	AbilityParams []umw.AbilityParam `json:"ability_params"`
}

type StuckServiceVO struct {
	ServiceId              string `json:"service_id"`
	VehicleId              string `json:"vehicle_id"`
	EvType                 string `json:"ev_type"`
	EvBrand                string `json:"ev_brand"`
	ServiceStartTime       int64  `json:"service_start_time"`
	ServiceEndTime         int64  `json:"service_end_time"`
	DeviceId               string `json:"device_id"`
	Description            string `json:"description"`
	Project                string `json:"project"`
	OnDuty                 int    `json:"on_duty"`
	AnalysisStatus         int    `json:"analysis_status"`
	AnalysisUser           string `json:"analysis_user"`
	AnalysisUserAvatarUrl  string `json:"analysis_user_avatar_url"`
	SoftwareVersion        string `json:"software_version"`
	CarLeaveTime           int64  `json:"car_leave_time"`
	CarBrand               string `json:"car_brand"`
	CarModel               string `json:"car_model"`
	CarPlatform            string `json:"car_platform"`
	OnDutyPerson           string `json:"on_duty_person"`
	OnDutyPersonAvatarUrl  string `json:"on_duty_person_avatar_url"`
	DeviceManager          string `json:"device_manager"`
	DeviceManagerAvatarUrl string `json:"device_manager_avatar_url"`
	AlarmLocation          string `json:"alarm_location"`
	AlarmStep              int    `json:"alarm_step"`
	AlarmStepName          string `json:"alarm_step_name"`
	HardwareUrl            string `json:"hardware_url"`
	SoftwareUrl            string `json:"software_url"`
	Aware                  bool   `json:"aware"`
	BugUrl                 string `json:"bug_url"`
	JiraUrl                string `json:"jira_url"`
	Result                 string `json:"result"`
}

type ListStuckServiceResponse struct {
	um.Base
	Total int64             `json:"total"`
	Data  []*StuckServiceVO `json:"data"`
}

type StuckAlarmInfo struct {
	No                 int     `json:"no"`
	AlarmIdDescription string  `json:"alarm_id_description"`
	AlarmId            string  `json:"alarm_id" bson:"alarm_id"`
	AlarmType          int32   `json:"alarm_type"`
	AlarmLevel         int32   `json:"alarm_level"`
	StuckTimes         int64   `json:"stuck_times" bson:"stuck_times"`
	TotalTimes         int64   `json:"total_times" bson:"total_times"`
	AlarmRate          float64 `json:"alarm_rate"`
	OwnerSoftware      string  `json:"owner_software"`
	OwnerHardware      string  `json:"owner_hardware"`
}

type StuckAlarmRecord struct {
	No                 int    `json:"no"`
	AlarmIdDescription string `json:"alarm_id_description"`
	AlarmId            string `json:"alarm_id"`
	DeviceId           string `json:"device_id"`
	Description        string `json:"description"`
	AlarmType          int32  `json:"alarm_type"`
	AlarmLevel         int32  `json:"alarm_level"`
	State              int32  `json:"state"`
	CreateTs           int64  `json:"create_ts"`
	ClearTs            int64  `json:"clear_ts"`
	ServiceId          string `json:"service_id"`
	ServiceStartTime   int64  `json:"service_start_time"`
	ServiceEndTime     int64  `json:"service_end_time"`
}

type JiraInfo struct {
	JiraId   string `json:"jira_id"`
	JiraName string `json:"jira_name"`
}

type DeviceStuckRate struct {
	Rate        string `json:"rate"`
	DeviceCount int64  `json:"device_count"`
}

type DeviceStuckServiceData struct {
	StuckTimes int64                     `json:"stuck_times" bson:"stuck_times"`
	TotalTimes int64                     `json:"total_times" bson:"total_times"`
	Devices    []DeviceStuckServiceCount `json:"devices" bson:"devices"`
}

type DeviceStuckServiceCount struct {
	DeviceId    string  `json:"device_id" bson:"device_id"`
	Description string  `json:"description" bson:"-,omitempty"`
	StuckTimes  int64   `json:"stuck_times" bson:"stuck_times"`
	Proportion  float64 `json:"proportion" bson:"proportion"`
}

type StuckAlarmDistributionData struct {
	Rate        string                    `json:"rate"`
	DeviceCount int64                     `json:"device_count"`
	Devices     []DeviceStuckServiceCount `json:"devices"`
}

type ListStuckAlarmDistributionResponse struct {
	um.Base
	AlarmId            string                       `json:"alarm_id"`
	AlarmIdDescription string                       `json:"alarm_id_description"`
	StuckTimes         int64                        `json:"stuck_times"`
	TotalTimes         int64                        `json:"total_times"`
	Data               []StuckAlarmDistributionData `json:"data"`
}

type GetUserAvatarResponse struct {
	ResultCode string `json:"resultCode"`
	ResultDesc string `json:"resultDesc"`
	ResultData struct {
		Data []struct {
			AdAccount string `json:"adAccount"`
			HeadPic   string `json:"headPic"`
		} `json:"data"`
	} `json:"resultData"`
}

type ListStuckAlarmInfoResponse struct {
	AddTotalResponse
	Avatar map[string]string `json:"avatar"`
}

type StuckServiceStep struct {
	StepNum       int      `json:"step_num"`
	StepCode      string   `json:"step_code"`
	StepName      string   `json:"step_name"`
	StepAlarmIds  []string `json:"step_alarm_ids"`
	StepStuckRate float64  `json:"step_stuck_rate"`
}

type GetStuckServiceStepAnalysisResponse struct {
	um.Base
	Data []StuckServiceStep `json:"data"`
}

type DeviceStuckStat struct {
	No                int     `json:"no"`
	DeviceId          string  `json:"device_id"`
	Description       string  `json:"description"`
	StuckRate         float64 `json:"stuck_rate"`
	StuckServiceCount int64   `json:"stuck_service_count"`
	ServiceCount      int64   `json:"service_count"`
	Owner             string  `json:"owner"`
	OwnerAvatarUrl    string  `json:"owner_avatar_url"`
}

type DeviceBluetoothDisconnectStat struct {
	No              int    `json:"no"`
	DeviceId        string `json:"device_id"`
	Description     string `json:"description"`
	TotalAlarmCount int64  `json:"total_alarm_count"`
}

type ListBluetoothDisconnectStatResponse struct {
	um.Base
	Total int64                            `json:"total"`
	Data  []*DeviceBluetoothDisconnectStat `json:"data"`
}

type ListDeviceStuckStatResponse struct {
	um.Base
	Total int64              `json:"total"`
	Data  []*DeviceStuckStat `json:"data"`
}

type DailyStuckStat struct {
	Day               string  `json:"day"`
	TotalServieCount  int64   `json:"total_servie_count"`
	StuckServiceCount int64   `json:"stuck_service_count"`
	StuckRate         float64 `json:"stuck_rate"`
}

type StuckStatPanelInfo struct {
	AverageStuckRate  float64          `json:"average_stuck_rate"`
	TotalServieCount  int64            `json:"total_servie_count"`
	StuckServiceCount int64            `json:"stuck_service_count"`
	StatDayNums       int64            `json:"stat_day_nums"`
	DailyDetails      []DailyStuckStat `json:"daily_details"`
}

type GetStuckStatPanelResponse struct {
	um.Base
	Data StuckStatPanelInfo `json:"data"`
}

type DailyBluetoothDisconnectStat struct {
	Day                             string  `json:"day"`
	AverageBluetoothDisconnectCount float64 `json:"average_bluetooth_disconnect_count"`
	TotalAlarmCount                 int64   `json:"total_alarm_count"`
	ActiveDeviceCount               int64   `json:"active_device_count"`
}

type BluetoothDisconnectStatPanelInfo struct {
	AverageBluetoothDisconnectCount float64                        `json:"average_bluetooth_disconnect_count"`
	TotalAlarmCount                 int64                          `json:"total_alarm_count"`
	StatDayNums                     int64                          `json:"stat_day_nums"`
	DailyDetails                    []DailyBluetoothDisconnectStat `json:"daily_details"`
}

type GetBluetoothDisconnectPanelResponse struct {
	um.Base
	Data BluetoothDisconnectStatPanelInfo `json:"data"`
}

type BluetoothDisconnectAlarmInfo struct {
	No                 int    `json:"no"`
	AlarmIdDescription string `json:"alarm_id_description"`
	AlarmId            string `json:"alarm_id"`
	TotalTimes         int    `json:"total_times"`
	TotalServiceTimes  int    `json:"total_service_times"`
}

type GetBluetoothDisconnectAlarmInfoResponse struct {
	um.Base
	Total int                            `json:"total"`
	Data  []BluetoothDisconnectAlarmInfo `json:"data"`
}

type GetBluetoothDisconnectAlarmDistributionResponse struct {
	um.Base
	AlarmIdDescription string                       `json:"alarm_id_description"`
	AlarmId            string                       `json:"alarm_id"`
	TotalTimes         int64                        `json:"total_times"`
	TotalServiceTimes  int64                        `json:"total_service_times"`
	Data               []StuckAlarmDistributionData `json:"data"`
}

type People struct {
	EmployeeId         string `json:"employee_id"`
	WorkerUserId       string `json:"worker_user_id"`
	Name               string `json:"name"`
	Domain             string `json:"domain"`
	EmployeeStatus     string `json:"employee_status"`
	DataDifference     string `json:"data_difference"`
	EmployeeDifference string `json:"employee_difference"`
	Country            string `json:"country"`
	Area               string `json:"area"`
}

type QueryPeopleData struct {
	Total      int64    `json:"total"`
	PeopleList []People `json:"people_list"`
}

type QueryPeopleResponse struct {
	um.Base
	Data QueryPeopleData `json:"data"`
}

type ServoFault struct {
	Id       string `json:"id"`
	Code     string `json:"code"`
	RealCode string `json:"real_code"`
	CodeName string `json:"code_name"`
}

type AlarmInfo struct {
	AlarmType         int32        `json:"alarm_type"`
	DataId            string       `json:"data_id"`
	AlarmLevel        int32        `json:"alarm_level"`
	DataIdDescription string       `json:"data_id_description"`
	DeviceId          string       `json:"device_id"`
	DeviceName        string       `json:"device_name"`
	CreateTS          int64        `json:"create_ts"`
	ClearTS           int64        `json:"clear_ts"`
	UploadTS          int64        `json:"upload_ts"`
	InsertTS          int64        `json:"insert_ts"`
	BatteryId         string       `json:"battery_id,omitempty"`
	State             int32        `json:"state"`
	IsStuck           bool         `json:"is_stuck"`
	ServoFaultList    []ServoFault `json:"servo_fault_list,omitempty"`
	EvBrand           *string      `json:"ev_brand" bson:"ev_brand"`
	EvType            *string      `json:"ev_type" bson:"ev_type"`
	CarPlatform       *string      `json:"car_platform" bson:"car_platform"`
}

type ListOrderVO struct {
	OrderId         string `json:"order_id"`
	OrderStartTime  int64  `json:"order_start_time"`
	OrderEndTime    int64  `json:"order_end_time"`
	VehicleId       string `json:"vehicle_id"`
	VehicleBrand    string `json:"vehicle_brand"`
	VehicleType     string `json:"vehicle_type"`
	VehiclePlatform string `json:"vehicle_platform"`
	Description     string `json:"description"`
	DeviceId        string `json:"device_id"`
	ServiceResult   string `json:"service_result"`
}

type ListServiceVisualResponse struct {
	um.Base
	Total int64         `json:"total"`
	Data  []ListOrderVO `json:"data"`
}

type OrderDetailVO struct {
	ServiceResult          string `json:"service_result"`
	DeviceId               string `json:"device_id"`
	Description            string `json:"description"`
	ServiceId              string `json:"service_id"`
	OrderId                string `json:"order_id"`
	OrderStartTime         int64  `json:"order_start_time"`
	OrderEndTime           int64  `json:"order_end_time"`
	Project                string `json:"project"`
	VehicleId              string `json:"vehicle_id"`
	VehiclePlatform        string `json:"vehicle_platform"`
	VehicleType            string `json:"vehicle_type"`
	VehicleBrand           string `json:"vehicle_brand"`
	VehicleSoftwareVersion string `json:"vehicle_software_version"`
	VehicleGlobalVersion   string `json:"vehicle_global_version"`
	StuckStatusFromHive    *bool  `json:"stuck_status_from_hive"`
}

type BatteryInfoVO struct {
	VehicleBatteryId       string   `json:"vehicle_battery_id"`
	VehicleBatterySoc      *float32 `json:"vehicle_battery_soc"`
	VehicleBatterySocOss   *float32 `json:"vehicle_battery_soc_oss"`
	VehicleBatteryCapacity *int32   `json:"vehicle_battery_capacity"`
	ServiceBatteryId       string   `json:"service_battery_id"`
	ServiceBatterySoc      *float32 `json:"service_battery_soc"`
	ServiceBatterySocOss   *float32 `json:"service_battery_soc_oss"`
	ServiceBatteryCapacity *int32   `json:"service_battery_capacity"`
}

type EventVO struct {
	EventName      string     `json:"event_name"`
	EventTimestamp int64      `json:"event_timestamp"`
	TimeSpan       int64      `json:"time_span"`
	AlarmLevel     int64      `json:"alarm_level"`
	AlarmReason    []string   `json:"alarm_reason"`
	SubEvents      []EventVO  `json:"sub_events"`
	Flow           *EventFlow `json:"flow,omitempty"`
}

type EventVOV2 struct {
	EventName                 string            `json:"event_name"`
	EventTimestamp            int64             `json:"event_timestamp"`
	EstimateEventEndTimestamp int64             `json:"estimate_event_end_timestamp"`
	Source                    string            `json:"source"`
	AlarmLevel                int               `json:"alarm_level"`
	Alarms                    []EventAlarmVO    `json:"alarms"`
	EventContext              map[string]string `json:"event_context"`
	Executed                  bool              `json:"executed"`
	SubEvents                 []EventVOV2       `json:"sub_events"`
}

type EventAlarmVO struct {
	AlarmName       string `json:"alarm_name"`
	AlarmLevel      int32  `json:"alarm_level"`
	AlarmCreateTime int64  `json:"alarm_create_time"`
	AlarmClearTime  int64  `json:"alarm_clear_time"`
}

type EventFlow struct {
	User         []EventVO `json:"用户"`
	PhoneAPP     []EventVO `json:"手机APP"`
	Vehicle      []EventVO `json:"车辆"`
	VehicleCloud []EventVO `json:"车企云"`
	PowerCloud   []EventVO `json:"能源云"`
	PowerStation []EventVO `json:"换电站"`
	Battery      []EventVO `json:"电池"`
}

type UserExperienceVO struct {
	Score           *int        `json:"score"`
	UserTag         []string    `json:"user_tag"`
	IsReverseSwap   *bool       `json:"is_reverse_swap"`
	IsAutomatedSwap *bool       `json:"is_automated_swap"`
	IsOnDuty        *bool       `json:"is_on_duty"`
	IsMultipleSwap  *bool       `json:"is_multiple_swap"`
	MultiSwap       []MultiSwap `json:"multi_swap"`
	//IsManualIntervention bool       `json:"is_manual_intervention"`
	ServiceStartTime int64 `json:"service_start_time"`
	ServiceEndTime   int64 `json:"service_end_time"`
	OrderStartTime   int64 `json:"order_start_time"`
	OrderEndTime     int64 `json:"order_end_time"`
}

type MultiSwap struct {
	OrderStartTime   int64  `json:"order_start_time"`
	OrderEndTime     int64  `json:"order_end_time"`
	OrderId          string `json:"order_id"`
	ServiceStartTime int64  `json:"service_start_time"`
	ServiceEndTime   int64  `json:"service_end_time"`
	ServiceId        string `json:"service_id"`
	FinishResult     *int32 `json:"finish_result"`
	Project          string `json:"project"`
	Interval         int    `json:"interval"`
}

type GetServiceVisualResponse struct {
	um.Base
	OrderDetail        OrderDetailVO     `json:"order_detail"`
	BatteryInfo        *BatteryInfoVO    `json:"battery_info"`
	EventLine          []EventVO         `json:"event_line"`
	UserExperienceInfo *UserExperienceVO `json:"user_experience_info"`
}

type DiagnosisResult struct {
	VdpResult string `json:"vdp_result"` // 车端检测结果
}

type GetServiceVisualResponseV2 struct {
	um.Base
	OrderDetail        OrderDetailVO     `json:"order_detail"`
	BatteryInfo        *BatteryInfoVO    `json:"battery_info"`
	EventLine          []EventVOV2       `json:"event_line"`
	UserExperienceInfo *UserExperienceVO `json:"user_experience_info"`
	DiagnosisResult    *DiagnosisResult  `json:"diagnosis_result"`
}

type CountStat struct {
	Name                string      `json:"name"`
	Value               float64     `json:"value"`
	Proportion          float64     `json:"proportion"`
	L3LabelDistribution []CountStat `json:"l3_label_distribution"`
}

type DiyLabelStat struct {
	L1LabelSatisfyCount []CountStat                   `json:"l1_label_satisfy_count"`
	L2LabelSatisfyCount []CountStat                   `json:"l2_label_satisfy_count"`
	L1LabelSatisfyAvg   []CountStat                   `json:"l1_label_satisfy_avg"`
	L2LabelSatisfyAvg   []CountStat                   `json:"l2_label_satisfy_avg"`
	L1LabelSatisfyLoss  []CountStat                   `json:"l1_label_satisfy_loss"`
	L2LabelSatisfyLoss  []CountStat                   `json:"l2_label_satisfy_loss"`
	L1LabelCount        *map[string]DiyLabelCountStat `json:"l1_label_count"`
	L2LabelCount        *map[string]DiyLabelCountStat `json:"l2_label_count"`
	L1LabelAvg          *map[string]float64           `json:"l1_label_avg"`
	L2LabelAvg          *map[string]float64           `json:"l2_label_avg"`
}

type DiyLabelCountStat struct {
	Count      int64   `json:"count"`
	Proportion float64 `json:"proportion"`
}

type GetGetSatisfyReportResponse struct {
	um.Base
	DiyLabelStat DiyLabelStat `json:"data"`
}

type ListPowerChargerOrderVO struct {
	DeviceId            string `json:"device_id"`
	Description         string `json:"description"`
	Project             string `json:"project"`
	ResourceId          string `json:"resource_id"`
	OrderId             string `json:"order_id"`
	ConnectorId         string `json:"connector_id"`
	OrderStatus         int64  `json:"order_status"`
	OrderStartTimestamp int64  `json:"order_start_timestamp"`

	OrderEndTimestamp               *int64   `json:"order_end_timestamp"`
	ServiceId                       *string  `json:"service_id"`
	ServiceStartTimestamp           *int64   `json:"service_start_timestamp"`
	ServiceEndTimestamp             *int64   `json:"service_end_timestamp"`
	ChargedEnergyTotal              *float32 `json:"charged_energy_total"`
	CalibratedRealtimeChargedEnergy *float32 `json:"calibrated_realtime_charged_energy"`
	EffectiveChargedEnergy          *float32 `json:"effective_charged_energy"`
	VehicleVin                      *string  `json:"vehicle_vin"`
	EnergyTotalStart                *float32 `json:"energy_total_start"`
	EnergyTotal                     *float32 `json:"energy_total"`
	ServiceStopReason               *int32   `json:"service_stop_reason"`
	ServiceFinishReason             *int32   `json:"service_finish_reason"`
	VehiclePlatform                 *string  `json:"vehicle_platform"`
	VehicleType                     *string  `json:"vehicle_type"`
	CarPackagePartNumber            *string  `json:"car_package_part_number"`
	CarPackageGlobalVersion         *string  `json:"car_package_global_version"`

	Channel                  *string  `json:"channel"`
	Client                   *string  `json:"client"`
	OrderSource              *int64   `json:"order_source"`
	OrderType                *int64   `json:"order_type"`
	ShamanOrderId            *string  `json:"shaman_order_id"`
	ChargerOrderId           *string  `json:"charger_order_id"`
	UserId                   *string  `json:"user_id"`
	OwnerId                  *string  `json:"owner_id"`
	VehicleId                *string  `json:"vehicle_id"`
	Rid                      *string  `json:"rid"`
	GroupId                  *string  `json:"group_id"`
	RefundStatus             *int64   `json:"refund_status"`
	OperatorId               *string  `json:"operator_id"`
	EquipmentOwner           *string  `json:"equipment_owner"`
	RightsType               *int64   `json:"rights_type"`
	PaymentType              *int64   `json:"payment_type"`
	PriceType                *int64   `json:"price_type"`
	OriginalPrice            *int64   `json:"original_price"`
	OriginalServicePrice     *int64   `json:"original_service_price"`
	OriginalElectricityPrice *int64   `json:"original_electricity_price"`
	ActualPrice              *int64   `json:"actual_price"`
	CostPrice                *int64   `json:"cost_price"`
	ServicePrice             *int64   `json:"service_price"`
	ElectricityPrice         *int64   `json:"electricity_price"`
	ExceptionType            *int64   `json:"exception_type"`
	IsPaid                   *bool    `json:"is_paid"`
	ActivityId               *int64   `json:"activity_id"`
	ShouldPrice              *int64   `json:"should_price"`
	ShouldElectricityPrice   *int64   `json:"should_electricity_price"`
	ShouldServicePrice       *int64   `json:"should_service_price"`
	StartCommandId           *string  `json:"start_command_id"`
	StartCommandStatus       *string  `json:"start_command_status"`
	StartCommandTime         *int64   `json:"start_command_time"`
	StopCommandId            *string  `json:"stop_command_id"`
	StopCommandStatus        *string  `json:"stop_command_status"`
	StopCommandTime          *int64   `json:"stop_command_time"`
	Latitude                 *float64 `json:"latitude"`
	Longitude                *float64 `json:"longitude"`
	PayTime                  *int64   `json:"pay_time"`
	ForceCloseSource         *string  `json:"force_close_source"`
	OssForceCloseReason      *int64   `json:"oss_force_close_reason"`
	AutoPayChannel           *int64   `json:"auto_pay_channel"`
}

type ListPowerChargerOrdersResponse struct {
	um.Base
	Total int64                     `json:"total"`
	Data  []ListPowerChargerOrderVO `json:"data"`
}

type GetPowerChargerOrderResponse struct {
	um.Base
	Data ListPowerChargerOrderVO `json:"data"`
}

type PowerChargerOrderEventVO struct {
	EventTimestamp int64             `json:"event_timestamp"`
	EventId        string            `json:"event_id"`
	EventName      string            `json:"event_name"`
	AlarmLevel     int               `json:"alarm_level"` // 0正常 1有告警不影响运营 2有告警影响运营
	AlarmDetails   []AlarmDetailVO   `json:"alarm_details"`
	Context        map[string]string `json:"context"`
}

type AlarmDetailVO struct {
	AlarmCreateTs    int64  `json:"alarm_create_ts"`
	AlarmId          string `json:"alarm_id"`
	AlarmDescription string `json:"alarm_description"`
}

type GetPowerChargerOrderEventsResponse struct {
	um.Base
	Data []PowerChargerOrderEventVO `json:"data"`
}

type PowerChargerOrderRealtimeVO struct {
	Timestamp                       int64   `json:"timestamp"`
	DeviceId                        string  `json:"device_id"`
	ResourceId                      string  `json:"resource_id"`
	OrderId                         string  `json:"order_id"`
	ServiceId                       string  `json:"service_id"`
	ChargedEnergy                   float32 `json:"charged_energy"`
	Soc                             float32 `json:"soc"`
	OutputCurrent                   float32 `json:"output_current"`
	CalibratedRealtimeChargedEnergy float32 `json:"calibrated_realtime_charged_energy"`
	EnergyTotal                     float32 `json:"energy_total"`
	BmsRequestChargeVoltage         float32 `json:"bms_request_charge_voltage"`
	EnergyTotalStart                float32 `json:"energy_total_start"`
	OutputPower                     float32 `json:"output_power"`
	BmsRequestChargeCurrent         float32 `json:"bms_request_charge_current"`
	EffectiveChargedEnergy          float32 `json:"effective_charged_energy"`
	OutputVoltage                   float32 `json:"output_voltage"`
}

type GetPowerChargerOrderRealtimeResponse struct {
	um.Base
	Data []PowerChargerOrderRealtimeVO `json:"data"`
}

type FilterOption struct {
	Key  string `json:"key"`
	Type string `json:"type"`
}

type CreateWorksheetResponse struct {
	RequestId   string      `json:"request_id"`
	ServerTime  int64       `json:"server_time"`
	ResultCode  string      `json:"result_code"`
	EncryptType int         `json:"encrypt_type"`
	Data        interface{} `json:"data"`
}

type DatasightResponse struct {
	RequestID  string        `json:"request_id"`
	ResultCode string        `json:"result_code"`
	ServerTime int           `json:"server_time"`
	Data       []interface{} `json:"data"`
	DisplayMsg string        `json:"display_msg"`
	DebugMsg   string        `json:"debug_msg"`
}
type DatasightPLC struct {
	Data      string `json:"data"`
	Datetime  string `json:"datetime"`
	DeviceID  string `json:"device_id"`
	ServiceID string `json:"service_id"`
	Ts        int64  `json:"ts"`
}
