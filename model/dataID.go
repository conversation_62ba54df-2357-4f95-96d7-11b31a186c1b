package model

import (
	"fmt"

	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
)

func init() {
	for i := 1; i <= 13; i++ {
		DataIDClassModel[umw.PowerSwap2][fmt.Sprintf("%d#电池基本参数", i)] = fmt.Sprintf("%d#Basic Info", i)
	}
	for i := 1; i <= 30; i++ {
		DataIDClassModel[umw.PUS3][fmt.Sprintf("AC/DC%d 基本信息", i)] = fmt.Sprintf("AC/DC%d Basic Info", i)
	}
	for i := 1; i <= 5; i++ {
		DataIDClassModel[umw.PUS3][fmt.Sprintf("%d#子PDU信息", i)] = fmt.Sprintf("%d#Sub PDU Info", i)
	}
	for i := 1; i <= 16; i++ {
		DataIDClassModel[umw.PUS4][fmt.Sprintf("1# AC/DC%d 基本信息", i)] = fmt.Sprintf("1# AC/DC%d Basic Info", i)
		DataIDClassModel[umw.PUS4][fmt.Sprintf("2# AC/DC%d 基本信息", i)] = fmt.Sprintf("2# AC/DC%d Basic Info", i)
	}

	for index := 400001; index <= 400038; index++ {
		ServoFaultPUS3[fmt.Sprintf("%d", index)] = struct{}{}
	}
	for i := 0; i < 23; i++ {
		ServoFaultPS2[fmt.Sprintf("%d", 604006+i*12)] = struct{}{}
	}
}

var DataIDClassModel = map[string]map[string]string{
	umw.PowerSwap2: {
		"充电":   "Charging",
		"系统参数": "System Info",
		"实时信息": "Realtime Info",
	},
	umw.PUS3: {
		"充放电系统":   "Charging & Discharging System",
		"充电柜":     "Charging Cabinet",
		"换电":      "Exchange",
		"换电终端":    "Exchange Terminal",
		"液冷":      "Liquid Cooling",
		"CDC  A仓": "CDC A Slot",
		"CDC  C仓": "CDC C Slot",
		"伺服":      "Servo",
		"换电站用电电表": "Electricity Meter of PowerSwap Station",
		"液冷进线电表":  "Electricity Meter of Liquid Cooled Incoming",
		"温湿度":     "Temperature & Humidity",
		"转接柜点表1":  "Transfer Cabinet Points 1",
		"转接柜点表2":  "Transfer Cabinet Points 2",
		"进线1电表":   "Electricity Meter of Incoming 1",
		"进线2电表":   "Electricity Meter of Incoming 2",
		"通用":      "Common Info",
		"铜排温度":    "Temperature of Copper Bar",
		"液冷基本信息":  "Liquid Cooling Basic Info",
		"电池":     "Battery",
		"BMS A仓": "BMS A Slot",
		"BMS C仓": "BMS C Slot",
	},
	umw.PUS4: {
		"站内充放电系统": "Charging & Discharging System",
		"液冷": "Liquid Cooling",
		"液冷基本信息": "Liquid Cooling Basic Info",
		"1# PDU 基本信息": "1# PDU Basic Info",
		"2# PDU 基本信息": "2# PDU Basic Info",
		"CDC A仓": "CDC A Slot",
		"CDC C仓": "CDC C Slot",
	},
}

// ServoFaultPUS3 三代站伺服告警点
var ServoFaultPUS3 = make(map[string]struct{})

// ServoFaultPS2 二代站伺服告警点
var ServoFaultPS2 = make(map[string]struct{})

// ServoFaultMap 三代站伺服故障码映射表
var ServoFaultMap = map[string]map[string]string{
	"zh": {
		"E101.0": "系统参数异常",
		"E101.1": "2000h/2001h组参数异常",
		"E101.2": "cs总个数变化读写时地址异常",
		"E102.0": "逻辑配置故障",
		"E102.8": "软件版本不匹配",
		"E104.1": "MCU运行超时",
		"E104.2": "电流环运行时间超时",
		"E104.4": "指令更新超时",
		"E120.0": "无法识别的编码器类型",
		"E120.1": "无对应型号电机",
		"E120.2": "无对应型号伺服驱动器",
		"E120.5": "电机与伺服驱动器电流匹配错误",
		"E120.6": "FPGA与电机型号不匹配",
		"E122.0": "多圈绝对值编码器设置错误",
		"E122.1": "DI功能重复分配",
		"E122.2": "DO功能分配故障",
		"E122.3": "旋转模式上限过大",
		"E136.0": "编码器参数错误",
		"E136.1": "编码器通讯错误",
		"E140.0": "加密芯片校验故障",
		"E140.1": "加密芯片校验失败",
		"E150.0": "STO信号输入保护",
		"E150.1": "STO输入异常",
		"E150.2": "Buffer5V电压检测异常",
		"E150.3": "STO前级光耦检测失败",
		"E150.4": "PWM Buffer硬件诊断失败",
		"E201.0": "P相过流",
		"E201.1": "U相过流",
		"E201.2": "V相过流",
		"E201.4": "N相过流",
		"E208.0": "MCU位置指令更新过快",
		"E208.2": "编码器通讯超时",
		"E208.3": "电流采样故障",
		"E208.4": "FPGA电流环运算超时",
		"E210.0": "输出对地短路",
		"E234.0": "飞车保护",
		"E400.0": "主回路电过压",
		"E410.0": "主回路电欠压",
		"E420.0": "缺相故障",
		"E430.0": "控制电源欠压",
		"E500.0": "电机超速",
		"E500.1": "速度反馈溢出",
		"E500.2": "FPGA位置反馈脉冲过速",
		"E602.0": "角度辨识堵转",
		"E602.2": "角度辨识UVW相序接反",
		"E605.0": "使能速度过高",
		"E620.0": "电机过载",
		"E630.0": "电机堵转",
		"E640.0": "逆变IGBT过温",
		"E640.1": "续流二极管过温",
		"E650.0": "散热器过热",
		"E660.0": "风冷电机温度过高",
		"E661.0": "自动增益过低",
		"E731.0": "编码器电池失效",
		"E733.0": "编码器多圈计数错误",
		"E735.0": "编码器多圈计数溢出",
		"E740.2": "绝对值编码器错误",
		"E740.3": "绝对值编码器单圈解算错误",
		"E740.6": "编码器写入故障",
		"E755.0": "尼康编码器通讯故障",
		"E765.0": "尼康编码器超限故障",
		"E760.0": "编码器过热",
		"E939.0": "电机动力线断线",
		"E939.1": "U 相断线",
		"E939.2": "V 相断线",
		"E939.3": "W 相断线",
		"EA33.0": "编码器读写校验异常",
		"EB00.0": "位置偏差过大",
		"EB00.1": "位置偏差溢出",
		"EB01.1": "位置指令增量单次过大",
		"EB01.2": "位置指令增量持续过大",
		"EB01.3": "指令溢出",
		"EB01.4": "旋转模式指令超过单圈位置最大",
		"EE08.0": "同步信号丢失",
		"EE08.1": "状态切换异常",
		"EE08.2": "IRQ丢失",
		"EE08.3": "网线连接不可靠",
		"EE08.4": "数据丢帧保护异常",
		"EE08.5": "数据帧转发异常",
		"EE08.6": "数据更新超时异常",
		"EE09.0": "软限位位置设定错误",
		"EE09.1": "原点位置设定错误",
		"EE09.2": "齿轮比超限",
		"EE09.3": "无同步信号",
		"EE09.5": "PDO映射超限",
		"EE11.0": "ESI校验错误",
		"EE11.1": "总线读取e2prom失败",
		"EE11.2": "总线更新e2prom失败",
		"EE12.0": "EtherCAT外设异常",
		"EE13.0": "同步周期设定错误",
		"EE15.0": "同步周期误差过大",
		"E108.0": "写入存储参数故障",
		"E108.1": "读取存储参数故障",
		"E108.2": "写e2prom校验错误",
		"E108.3": "读e2prom校验错误",
		"E108.4": "单个参数存储频繁",
		"E120.9": "电机与伺服驱动器功率不匹配",
		"E121.0": "伺服ON指令无效故障",
		"E600.0": "惯量辨识失败",
		"E601.0": "原点回归警告",
		"E601.1": "原点复归开关异常",
		"E601.2": "原点回归模式设定错误",
		"E730.0": "编码器电池警告",
		"E900.0": "紧急停机",
		"E902.0": "DI设置无效",
		"E902.1": "DO设置无效",
		"E902.2": "转矩到达设置无效警告",
		"E908.0": "机型识别校验码失败",
		"E909.0": "电机过载警告",
		"E920.0": "再生泄放电阻过载",
		"E922.0": "外接再生泄放电阻阻值过小",
		"E924.0": "泄放管过温警告",
		"E941.0": "变更参数需重新上电生效",
		"E942.0": "参数存储频繁",
		"E950.0": "正向超程警告",
		"E952.0": "反向超程警告",
		"EA41.0": "转矩波动补偿失败",
		"E902.3": "原点回归模式设定错误",
	},
}
