package service_visual

import (
	"context"
	"fmt"
	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	"git.nevint.com/welkin2/welkin-backend/domain/alarm"
	"git.nevint.com/welkin2/welkin-backend/domain/common"
	"git.nevint.com/welkin2/welkin-backend/domain/event"
	"git.nevint.com/welkin2/welkin-backend/domain/order"
	"git.nevint.com/welkin2/welkin-backend/domain/realtime"
	"git.nevint.com/welkin2/welkin-backend/domain/service"
	"git.nevint.com/welkin2/welkin-backend/domain/service_event"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	"git.nevint.com/welkin2/welkin-backend/util"
	"sort"
	"time"
)

type ServiceVisualService struct {
	OrderDO          *order.OrderDO
	ServiceDO        *service.ServiceDO
	ServiceEventDO   *service_event.ServiceEventDO
	EventDO          *event.EventDO
	AlarmDO          *alarm.AlarmDO
	SatisfyServiceDO *service.Service
	SatisfyDO        *service.Satisfy
	RealtimeDO       *realtime.RealtimeDO
}

func (s *ServiceVisualService) ListOrder(ctx context.Context, cond order.ListOrderCond) ([]model.ListOrderVO, int64, error) {
	orderDOs, total, err := s.OrderDO.ListOrder(ctx, cond)
	if err != nil {
		return nil, 0, err
	}
	data := []model.ListOrderVO{}
	for _, orderDO := range orderDOs {
		data = append(data, convertOrderDO2ListOrderVO(orderDO))
	}
	return data, total, nil
}

func (s *ServiceVisualService) GetServiceVisualByOrderId(ctx context.Context, project, orderId string) (model.GetServiceVisualResponse, error) {
	// 加载order do
	// 加载service do
	// 加载satisfy do
	// 加载service event do
	// 加载alarm do
	// 组装
	result := model.GetServiceVisualResponse{}
	orderDO, err := s.OrderDO.GetOrderByOrderID(ctx, project, orderId)
	if err != nil {
		return result, err
	}

	serviceDOs, total, err := s.ServiceDO.ListServices(ctx, service.ListServiceCond{
		Project: project,
		Rids:    []string{orderDO.Rid},
	})
	if err != nil {
		return result, err
	}
	var serviceDO *service.ServiceDO
	if total > 0 {
		serviceDO = &(serviceDOs[len(serviceDOs)-1])
	}

	s.SatisfyServiceDO = &service.Service{OrderId: orderId, Project: project}
	s.SatisfyDO = &service.Satisfy{OrderId: orderId}
	diagnosisResult, _, ssErr := s.SatisfyServiceDO.GetSatisfyDiagnoseResult(ctx)
	satisfyData, sErr := s.SatisfyDO.GetSatisfyDataById(ctx)
	if ssErr != nil || sErr != nil {
		log.CtxLog(ctx).Errorf("GetServiceVisualByOrderId fail, GetSatisfyDiagnoseResult err: %v, GetSatisfyDataById err: %v", ssErr, sErr)
	}
	var realtimeDO []realtime.RealtimeDO
	if serviceDO != nil {
		cond := realtime.ListRealtimeCond{
			Project:   project,
			DeviceId:  orderDO.DeviceId,
			DataIds:   realtime.OnDutyStatusDataId[project],
			StartTime: serviceDO.ServiceStartTime,
			EndTime:   serviceDO.ServiceEndTime,
		}
		realtimeList, _, rErr := s.RealtimeDO.ListRealtime(ctx, cond)
		if rErr != nil {
			log.CtxLog(ctx).Errorf("GetServiceVisualByOrderId fail, ListRealtime err: %v, cond: %s", rErr, ucmd.ToJsonStrIgnoreErr(cond))
		}
		realtimeDO = realtimeList
	}
	result.UserExperienceInfo = convertSatisfyDO2VO(satisfyData, diagnosisResult, serviceDO, orderDO, realtimeDO)

	serviceEventDOs, _, err := s.ServiceEventDO.ListServiceEvents(ctx, service_event.ListServiceEventsCond{
		Project: project,
		Rid:     orderDO.Rid,
	})
	if err != nil {
		return result, err
	}
	sort.Slice(serviceEventDOs, func(i, j int) bool { return serviceEventDOs[i].EventTs < serviceEventDOs[j].EventTs })

	// 未结束的情况 用过去很远的时间
	serviceEventEndTs := int64(9927657431523)
	if orderDO.GetEndTimestamp() != 0 {
		serviceEventEndTs = orderDO.FinishTime
	}
	alarmDOs, _, err := s.AlarmDO.ListAlarms(ctx, alarm.ListAlarmCond{
		StartTs:  orderDO.CreationTime,
		EndTs:    serviceEventEndTs,
		Project:  project,
		DeviceId: &orderDO.DeviceId,
	})
	if err != nil {
		return result, err
	}
	fmt.Println(fmt.Sprintf("%v", alarmDOs))

	result.EventLine = s.organizeEvents(ctx, project, serviceEventDOs, alarmDOs)
	result.OrderDetail = model.OrderDetailVO{
		ServiceResult: orderDO.GetOrderStatusForServiceVisual(),
		DeviceId:      orderDO.DeviceId,
		Description:   orderDO.Description,
		//ServiceId:              serviceDO.ServiceId,
		OrderId:                orderDO.OrderId,
		OrderStartTime:         orderDO.CreationTime,
		OrderEndTime:           orderDO.GetEndTimestamp(),
		Project:                orderDO.Project,
		VehicleId:              orderDO.VehicleId,
		VehiclePlatform:        orderDO.CarPlatform,
		VehicleType:            orderDO.CarModelType,
		VehicleBrand:           orderDO.CarBrand,
		VehicleSoftwareVersion: orderDO.CarPackagePartNumber,
		VehicleGlobalVersion:   orderDO.CarPackageGlobalVersion,
	}
	if serviceDO != nil {
		result.OrderDetail.ServiceId = serviceDO.ServiceId
		result.BatteryInfo = &model.BatteryInfoVO{
			VehicleBatteryId:       serviceDO.VehicleBatteryId,
			VehicleBatterySoc:      serviceDO.VehicleBatterySoc,
			VehicleBatterySocOss:   serviceDO.VehicleBatteryRealSoc,
			VehicleBatteryCapacity: common.ConvertBatteryUserType(serviceDO.VehicleBatteryType),
			ServiceBatteryId:       serviceDO.ServiceBatteryId,
			ServiceBatterySoc:      serviceDO.ServiceBatterySoc,
			ServiceBatterySocOss:   serviceDO.ServiceBatteryRealSoc,
			ServiceBatteryCapacity: common.ConvertBatteryUserType(serviceDO.ServiceBatteryType),
		}
	}
	return result, nil
}

func (s *ServiceVisualService) GetServiceVisualByOrderIdV2(ctx context.Context, project, orderId string) (model.GetServiceVisualResponseV2, error) {
	// 加载order do
	// 加载service do
	// 加载satisfy do
	// 加载 event do
	// 加载alarm do
	// 组装
	result := model.GetServiceVisualResponseV2{}
	orderDO, err := s.OrderDO.GetOrderByOrderID(ctx, project, orderId)
	if err != nil {
		return result, err
	}

	serviceDOs, total, err := s.ServiceDO.ListServices(ctx, service.ListServiceCond{
		Project: project,
		Rids:    []string{orderDO.Rid},
	})
	if err != nil {
		return result, err
	}
	var serviceDO *service.ServiceDO
	if total > 0 {
		serviceDO = &(serviceDOs[len(serviceDOs)-1])
	}

	s.SatisfyServiceDO = &service.Service{OrderId: orderId, Project: project}
	s.SatisfyDO = &service.Satisfy{OrderId: orderId}
	diagnosisResult, _, ssErr := s.SatisfyServiceDO.GetSatisfyDiagnoseResult(ctx)
	satisfyData, sErr := s.SatisfyDO.GetSatisfyDataById(ctx)
	if ssErr != nil || sErr != nil {
		log.CtxLog(ctx).Errorf("GetServiceVisualByOrderId fail, GetSatisfyDiagnoseResult err: %v, GetSatisfyDataById err: %v", ssErr, sErr)
	}
	var realtimeDO []realtime.RealtimeDO
	if serviceDO != nil {
		cond := realtime.ListRealtimeCond{
			Project:   project,
			DeviceId:  orderDO.DeviceId,
			DataIds:   realtime.OnDutyStatusDataId[project],
			StartTime: serviceDO.ServiceStartTime,
			EndTime:   serviceDO.ServiceEndTime,
		}
		realtimeList, _, rErr := s.RealtimeDO.ListRealtime(ctx, cond)
		if rErr != nil {
			log.CtxLog(ctx).Errorf("GetServiceVisualByOrderId fail, ListRealtime err: %v, cond: %s", rErr, ucmd.ToJsonStrIgnoreErr(cond))
		}
		realtimeDO = realtimeList
	}
	result.UserExperienceInfo = convertSatisfyDO2VO(satisfyData, diagnosisResult, serviceDO, orderDO, realtimeDO)

	// 未结束的情况 用过去很远的时间
	serviceEventEndTs := int64(9927657431523)
	if orderDO.GetEndTimestamp() != 0 {
		serviceEventEndTs = orderDO.FinishTime
	}

	serviceEventDOs, _, err := s.EventDO.ListEvents(ctx, event.ListEventsCond{
		Project:  project,
		DeviceId: orderDO.DeviceId,
		// 往后加一点时间，有车辆驶离
		StartTs: orderDO.CreationTime,
		EndTs:   serviceEventEndTs + 1000*120,
		Limit:   1000,
	})
	if err != nil {
		return result, err
	}
	sort.Slice(serviceEventDOs, func(i, j int) bool { return serviceEventDOs[i].EventTs < serviceEventDOs[j].EventTs })

	alarmDOs, _, err := s.AlarmDO.ListAlarms(ctx, alarm.ListAlarmCond{
		StartTs:  orderDO.CreationTime,
		EndTs:    serviceEventEndTs,
		Project:  project,
		DeviceId: &orderDO.DeviceId,
	})
	if err != nil {
		return result, err
	}
	fmt.Println(fmt.Sprintf("%v", alarmDOs))

	result.EventLine = s.organizeEventsV2(ctx, project, serviceEventDOs, alarmDOs)
	result.OrderDetail = model.OrderDetailVO{
		ServiceResult: orderDO.GetOrderStatusForServiceVisual(),
		DeviceId:      orderDO.DeviceId,
		Description:   orderDO.Description,
		//ServiceId:              serviceDO.ServiceId,
		OrderId:                orderDO.OrderId,
		OrderStartTime:         orderDO.CreationTime,
		OrderEndTime:           orderDO.GetEndTimestamp(),
		Project:                orderDO.Project,
		VehicleId:              orderDO.VehicleId,
		VehiclePlatform:        orderDO.CarPlatform,
		VehicleType:            orderDO.CarModelType,
		VehicleBrand:           orderDO.CarBrand,
		VehicleSoftwareVersion: orderDO.CarPackagePartNumber,
		VehicleGlobalVersion:   orderDO.CarPackageGlobalVersion,
	}
	if serviceDO != nil {
		result.OrderDetail.ServiceId = serviceDO.ServiceId
		result.BatteryInfo = &model.BatteryInfoVO{
			VehicleBatteryId:       serviceDO.VehicleBatteryId,
			VehicleBatterySoc:      serviceDO.VehicleBatterySoc,
			VehicleBatterySocOss:   serviceDO.VehicleBatteryRealSoc,
			VehicleBatteryCapacity: common.ConvertBatteryUserType(serviceDO.VehicleBatteryType),
			ServiceBatteryId:       serviceDO.ServiceBatteryId,
			ServiceBatterySoc:      serviceDO.ServiceBatterySoc,
			ServiceBatterySocOss:   serviceDO.ServiceBatteryRealSoc,
			ServiceBatteryCapacity: common.ConvertBatteryUserType(serviceDO.ServiceBatteryType),
		}
		result.OrderDetail.StuckStatusFromHive = serviceDO.IsStuck
	}
	if orderDO.VdpDiagnosisResult != nil {
		result.DiagnosisResult = &model.DiagnosisResult{
			VdpResult: *orderDO.VdpDiagnosisResult,
		}
	}
	return result, nil
}

func (s *ServiceVisualService) organizeEvents(ctx context.Context, project string, serviceEventDOs []*service_event.ServiceEventDO, alarmDOs []alarm.AlarmDO) []model.EventVO {
	eventLine := []model.EventVO{}
	for _, serviceEventDO := range serviceEventDOs {
		source := "换电站上报"
		if serviceEventDO.EventSource == 2 {
			source = "shaman系统获取"
		}
		fmt.Println(fmt.Sprintf("%v 来源:%v 事件:%v", time.UnixMilli(serviceEventDO.EventTs), source, serviceEventDO.EventName))
	}
	eventNameMap := map[string]*service_event.ServiceEventDO{}
	for _, serviceEventDO := range serviceEventDOs {
		eventNameMap[serviceEventDO.EventName] = serviceEventDO
	}

	// 组织一级事件
	// 用户下单 -> 订单创建
	// -----排队叫号 -- 获取不到
	// 泊车进站 -> 泊车开始
	// 启动换电 -> 启动换电按键使能
	// 机械换电 -> 推杆一次定位
	// 换电完成 -> 换电完成
	// 出站驶离 -> 换电完成车辆驶离
	firsLevelEventName := map[string][]string{
		"PowerSwap2": {"订单创建", "泊车开始", "启动换电按键使能", "服务电池出仓", "换电完成", "换电完成车辆驶离"},
		"PUS3":       {"订单创建", "泊车开始", "启动换电按键使能", "推杆一次定位", "换电完成", "换电完成车辆驶离"},
		"PUS4":       {"订单创建", "泊车开始", "启动换电按键使能", "推杆一次定位", "换电完成", "换电完成车辆驶离"},
	}
	firstBusinessLevel := []string{"用户下单", "泊车进站", "启动换电", "机械换电", "换电完成", "出站驶离"}
	firstLevel := firsLevelEventName[project]
	for i, businessName := range firstBusinessLevel {
		eventDO, found := eventNameMap[firstLevel[i]]
		if found {
			eventLine = append(eventLine, model.EventVO{
				EventName:      businessName,
				EventTimestamp: eventDO.EventTs,
				SubEvents:      []model.EventVO{},
			})
		}
	}

	// 组织二级事件 先只搞机械换电
	for i, eventVO := range eventLine {
		if eventVO.EventName != "机械换电" {
			continue
		}
		eventLine[i].SubEvents = append(eventLine[i].SubEvents, model.EventVO{
			EventName:      "机械换电",
			EventTimestamp: eventVO.EventTimestamp,
			SubEvents:      []model.EventVO{},
			Flow: &model.EventFlow{
				User:         []model.EventVO{},
				PhoneAPP:     []model.EventVO{},
				Vehicle:      []model.EventVO{},
				VehicleCloud: []model.EventVO{},
				PowerCloud:   []model.EventVO{},
				PowerStation: []model.EventVO{},
				Battery:      []model.EventVO{},
			},
		})
	}
	// 组织 三,四 级事件 先只搞机械换电
	for _, eventVO := range eventLine {
		if eventVO.EventName != "机械换电" {
			continue
		}
		mechanicalEvents := []model.EventVO{}
		start := 0
		end := len(serviceEventDOs) - 1
		for i, serviceEventDO := range serviceEventDOs {
			if serviceEventDO.EventName == firstLevel[3] {
				start = i
			}
			if serviceEventDO.EventName == firstLevel[4] {
				end = i
			}
		}
		for _, serviceEventDO := range serviceEventDOs[start : end+1] {
			mechanicalEvents = append(mechanicalEvents, model.EventVO{
				EventName:      serviceEventDO.EventName,
				EventTimestamp: serviceEventDO.EventTs,
			})
		}
		eventVO.SubEvents[0].Flow.PowerStation = append(eventVO.SubEvents[0].Flow.PowerStation, model.EventVO{
			EventName:      "机械换电开始",
			EventTimestamp: eventVO.EventTimestamp,
			SubEvents:      mechanicalEvents,
		})
		swapFinishEvent, found := eventNameMap["换电完成"]
		if found {
			eventVO.SubEvents[0].Flow.PowerStation = append(eventVO.SubEvents[0].Flow.PowerStation, model.EventVO{
				EventName:      "机械换电完成",
				EventTimestamp: swapFinishEvent.EventTs,
			})
		}
	}

	// 处理耗时与告警
	for i, _ := range eventLine {
		if i == len(eventLine)-1 {
			continue
		}
		eventLine[i].TimeSpan = eventLine[i+1].EventTimestamp - eventLine[i].EventTimestamp
	}
	for i, _ := range eventLine {
		for j, _ := range eventLine[i].SubEvents {
			if j == len(eventLine[i].SubEvents)-1 {
				if i+1 <= len(eventLine)-1 {
					eventLine[i].SubEvents[j].TimeSpan = eventLine[i+1].EventTimestamp - eventLine[i].SubEvents[j].EventTimestamp
				}
				continue
			}
			eventLine[i].SubEvents[j].TimeSpan = eventLine[i].SubEvents[j+1].EventTimestamp - eventLine[i].SubEvents[j].EventTimestamp
		}
	}
	for i, _ := range eventLine {
		for j, _ := range eventLine[i].SubEvents {
			if eventLine[i].SubEvents[j].Flow != nil {
				for k, _ := range eventLine[i].SubEvents[j].Flow.PowerStation {
					if k+1 == len(eventLine[i].SubEvents[j].Flow.PowerStation) {
						if j+1 <= len(eventLine[i].SubEvents)-1 {
							eventLine[i].SubEvents[j].Flow.PowerStation[k].TimeSpan = eventLine[i].SubEvents[j+1].EventTimestamp - eventLine[i].SubEvents[j].Flow.PowerStation[k].EventTimestamp
						}
						continue
					}
					eventLine[i].SubEvents[j].Flow.PowerStation[k].TimeSpan = eventLine[i].SubEvents[j].Flow.PowerStation[k+1].EventTimestamp - eventLine[i].SubEvents[j].Flow.PowerStation[k].EventTimestamp
				}
			}
		}
	}
	for i, _ := range eventLine {
		for j, _ := range eventLine[i].SubEvents {
			if eventLine[i].SubEvents[j].Flow != nil {
				for k, _ := range eventLine[i].SubEvents[j].Flow.PowerStation {
					for p, _ := range eventLine[i].SubEvents[j].Flow.PowerStation[k].SubEvents {
						if p+1 == len(eventLine[i].SubEvents[j].Flow.PowerStation[k].SubEvents) {
							continue
						}
						eventLine[i].SubEvents[j].Flow.PowerStation[k].SubEvents[p].TimeSpan = eventLine[i].SubEvents[j].Flow.PowerStation[k].SubEvents[p+1].EventTimestamp - eventLine[i].SubEvents[j].Flow.PowerStation[k].SubEvents[p].EventTimestamp
					}
				}
			}
		}
	}

	for i, _ := range eventLine {
		for j, _ := range eventLine[i].SubEvents {
			if eventLine[i].SubEvents[j].Flow != nil {
				for k, _ := range eventLine[i].SubEvents[j].Flow.PowerStation {
					for p, _ := range eventLine[i].SubEvents[j].Flow.PowerStation[k].SubEvents {
						if eventLine[i].SubEvents[j].Flow.PowerStation[k].SubEvents[p].TimeSpan == 0 {
							continue
						}
						// 告警等级 排序
						sort.Slice(alarmDOs, func(i, j int) bool {
							if alarmDOs[i].AlarmLevel > alarmDOs[j].AlarmLevel {
								return alarmDOs[i].CreateTs < alarmDOs[j].CreateTs
							}
							return alarmDOs[i].AlarmLevel > alarmDOs[j].AlarmLevel
						})
						for _, alarmDO := range alarmDOs {
							if eventLine[i].SubEvents[j].Flow.PowerStation[k].SubEvents[p].EventTimestamp <= alarmDO.CreateTs &&
								alarmDO.CreateTs <= eventLine[i].SubEvents[j].Flow.PowerStation[k].SubEvents[p].EventTimestamp+eventLine[i].SubEvents[j].Flow.PowerStation[k].SubEvents[p].TimeSpan {
								eventLine[i].SubEvents[j].Flow.PowerStation[k].SubEvents[p].AlarmLevel = 1
								alarmCreateTime := time.UnixMilli(alarmDO.CreateTs)
								eventLine[i].SubEvents[j].Flow.PowerStation[k].SubEvents[p].AlarmReason = append(eventLine[i].SubEvents[j].Flow.PowerStation[k].SubEvents[p].AlarmReason, fmt.Sprintf("%v %v", alarmCreateTime.In(util.GetTimeLoc()).Format("2006-01-02 15:04:05"), alarmDO.DataIdDescription))
								eventLine[i].SubEvents[j].Flow.PowerStation[k].AlarmLevel = 1
								eventLine[i].SubEvents[j].AlarmLevel = 1
								eventLine[i].AlarmLevel = 1
							}
						}
					}
				}
			}
		}
	}

	return eventLine
}

func (s *ServiceVisualService) organizeEventsV2(ctx context.Context, project string, eventDOs []*event.EventDO, alarmDOs []alarm.AlarmDO) []model.EventVOV2 {
	eventLine := []model.EventVOV2{}
	log.CtxLog(ctx).Infof("调试1 eventDOs:%v alarmDOs:%v", ucmd.ToJsonStrIgnoreErr(eventDOs), ucmd.ToJsonStrIgnoreErr(alarmDOs))
	if len(eventDOs) == 0 {
		return eventLine
	}
	if !(project == umw.PowerSwap2 || project == umw.PUS3 || project == umw.PUS4) {
		return eventLine
	}

	eventMap := map[string]*event.EventDO{}
	for _, eventDO := range eventDOs {
		// 相同的取第一个
		_, found := eventMap[eventDO.EventId]
		if found {
			continue
		}
		eventMap[eventDO.EventId] = eventDO
	}
	// 清理最后一个事件
	cutCur := len(eventDOs) - 1
	for i, eventDO := range eventDOs {
		if eventDO.EventId == ProjectEventLevel1[project][Level1s[len(Level1s)-1]] {
			cutCur = i
		}
	}
	eventDOs = eventDOs[:cutCur+1]
	log.CtxLog(ctx).Infof("调试2 eventDOs:%v alarmDOs:%v", ucmd.ToJsonStrIgnoreErr(eventDOs), ucmd.ToJsonStrIgnoreErr(alarmDOs))

	// 组装固有一级事件
	for _, level1 := range Level1s {
		event := model.EventVOV2{
			EventName: level1,
		}
		eventDO, found := eventMap[ProjectEventLevel1[project][level1]]
		if found {
			event.Executed = true
			event.EventTimestamp = eventDO.EventTs
		}
		eventLine = append(eventLine, event)
	}

	// 组装二级事件
	// [)
	for i, _ := range eventLine {
		if eventLine[i].Executed == false {
			continue
		}
		if i == len(eventLine)-1 {
			for _, eventDO := range eventDOs {
				if eventDO.EventTs >= eventLine[i].EventTimestamp {
					eventLine[i].SubEvents = append(eventLine[i].SubEvents, model.EventVOV2{
						EventName:      eventDO.EventName,
						EventTimestamp: eventDO.EventTs,
						Source:         eventDO.GetEventSourceName(),
						EventContext:   eventDO.Context,
						Executed:       true,
					})
				}
			}
			continue
		}
		for _, eventDO := range eventDOs {
			if eventDO.EventTs >= eventLine[i].EventTimestamp && (eventDO.EventTs < eventLine[i+1].EventTimestamp || eventLine[i+1].Executed == false) {
				eventLine[i].SubEvents = append(eventLine[i].SubEvents, model.EventVOV2{
					EventName:      eventDO.EventName,
					EventTimestamp: eventDO.EventTs,
					Source:         eventDO.GetEventSourceName(),
					EventContext:   eventDO.Context,
					Executed:       true,
				})
			}
		}
	}

	// 组装预估结束时间
	for i, _ := range eventLine {
		if eventLine[i].Executed == false {
			continue
		}
		if i == len(eventLine)-1 {
			continue
		}
		eventLine[i].EstimateEventEndTimestamp = eventLine[i+1].EventTimestamp
	}
	for i, _ := range eventLine {
		for j, _ := range eventLine[i].SubEvents {
			if j == len(eventLine[i].SubEvents)-1 {
				continue
			}
			eventLine[i].SubEvents[j].EstimateEventEndTimestamp = eventLine[i].SubEvents[j+1].EventTimestamp
		}
	}

	// 组装告警
	// 告警等级 排序
	sort.Slice(alarmDOs, func(i, j int) bool {
		return alarmDOs[i].CreateTs < alarmDOs[j].CreateTs
	})
	for i, _ := range eventLine {
		if eventLine[i].Executed == false {
			continue
		}
		for j, _ := range eventLine[i].SubEvents {
			for _, alarmDO := range alarmDOs {
				if alarmDO.AlarmLevel <= 1 {
					continue
				}
				if j == len(eventLine[i].SubEvents)-1 {
					if i == len(eventLine)-1 {
						if alarmDO.CreateTs >= eventLine[i].SubEvents[j].EventTimestamp {
							if int(alarmDO.AlarmLevel) >= eventLine[i].SubEvents[j].AlarmLevel {
								eventLine[i].SubEvents[j].AlarmLevel = int(alarmDO.AlarmLevel)
							}
							if int(alarmDO.AlarmLevel) >= eventLine[i].AlarmLevel {
								eventLine[i].AlarmLevel = int(alarmDO.AlarmLevel)
							}
							eventLine[i].SubEvents[j].Alarms = append(eventLine[i].SubEvents[j].Alarms, model.EventAlarmVO{
								AlarmName:       alarmDO.DataIdDescription,
								AlarmLevel:      alarmDO.AlarmLevel,
								AlarmCreateTime: alarmDO.CreateTs,
								AlarmClearTime:  alarmDO.ClearTs,
							})
						}
						continue
					}

					if alarmDO.CreateTs >= eventLine[i].SubEvents[j].EventTimestamp && (eventLine[i+1].Executed == true && len(eventLine[i+1].SubEvents) > 0 && alarmDO.CreateTs < eventLine[i+1].SubEvents[0].EventTimestamp) {
						if int(alarmDO.AlarmLevel) >= eventLine[i].SubEvents[j].AlarmLevel {
							eventLine[i].SubEvents[j].AlarmLevel = int(alarmDO.AlarmLevel)
						}
						if int(alarmDO.AlarmLevel) >= eventLine[i].AlarmLevel {
							eventLine[i].AlarmLevel = int(alarmDO.AlarmLevel)
						}
						eventLine[i].SubEvents[j].Alarms = append(eventLine[i].SubEvents[j].Alarms, model.EventAlarmVO{
							AlarmName:       alarmDO.DataIdDescription,
							AlarmLevel:      alarmDO.AlarmLevel,
							AlarmCreateTime: alarmDO.CreateTs,
							AlarmClearTime:  alarmDO.ClearTs,
						})
					}
					continue
				}
				if alarmDO.CreateTs >= eventLine[i].SubEvents[j].EventTimestamp &&
					alarmDO.CreateTs < eventLine[i].SubEvents[j+1].EventTimestamp {
					if int(alarmDO.AlarmLevel) >= eventLine[i].SubEvents[j].AlarmLevel {
						eventLine[i].SubEvents[j].AlarmLevel = int(alarmDO.AlarmLevel)
					}
					if int(alarmDO.AlarmLevel) >= eventLine[i].AlarmLevel {
						eventLine[i].AlarmLevel = int(alarmDO.AlarmLevel)
					}
					eventLine[i].SubEvents[j].Alarms = append(eventLine[i].SubEvents[j].Alarms, model.EventAlarmVO{
						AlarmName:       alarmDO.DataIdDescription,
						AlarmLevel:      alarmDO.AlarmLevel,
						AlarmCreateTime: alarmDO.CreateTs,
						AlarmClearTime:  alarmDO.ClearTs,
					})
				}
			}
		}
	}

	//for _, eventDO := range eventDOs {
	//	eventLine = append(eventLine, model.EventVOV2{
	//		EventName:      eventDO.EventName,
	//		EventTimestamp: eventDO.EventTs,
	//		Source:         fmt.Sprintf("%v", eventDO.EventSource),
	//		EventContext:   eventDO.Context,
	//	})
	//}

	return eventLine
}

var Level1s = []string{"用户下单", "排队叫号", "泊车进站", "换电准备", "机械换电", "出站驶离"}

var ProjectEventLevel1 = map[string]map[string]string{
	umw.PowerSwap2: {
		"用户下单": "order_create",
		"排队叫号": "queue_call",
		"泊车进站": "1200",
		"换电准备": "1201",
		"机械换电": "1206",
		"出站驶离": "1228",
	},
	umw.PUS3: {
		"用户下单": "order_create",
		"排队叫号": "queue_call",
		"泊车进站": "802000",
		"换电准备": "802001",
		"机械换电": "802004",
		"出站驶离": "805009",
	},
	umw.PUS4: {
		"用户下单": "order_create",
		"排队叫号": "queue_call",
		"泊车进站": "982001",
		"换电准备": "982002",
		"机械换电": "982005",
		"出站驶离": "982037",
	},
}
