package service_visual

import (
	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/domain/order"
	"git.nevint.com/welkin2/welkin-backend/domain/realtime"
	"git.nevint.com/welkin2/welkin-backend/domain/service"
	"git.nevint.com/welkin2/welkin-backend/model"
	"git.nevint.com/welkin2/welkin-backend/util"
)

func convertOrderDO2ListOrderVO(orderDO *order.OrderDO) model.ListOrderVO {
	description := ""
	deviceInfo, exist := cache.PowerSwapCache.GetSingleDevice(orderDO.DeviceId)
	if exist {
		description = deviceInfo.Description
	}
	orderVO := model.ListOrderVO{
		OrderId:         orderDO.OrderId,
		OrderStartTime:  orderDO.CreationTime,
		OrderEndTime:    orderDO.FinishTime,
		VehicleId:       orderDO.VehicleId,
		VehicleBrand:    orderDO.CarBrand,
		VehicleType:     orderDO.CarModelType,
		VehiclePlatform: orderDO.CarPlatform,
		Description:     description,
		DeviceId:        orderDO.DeviceId,
		ServiceResult:   orderDO.GetOrderStatusForServiceVisual(),
	}
	return orderVO
}

func convertSatisfyDO2VO(satisfyData *service.Satisfy, satisfyDiagnose *service.SatisfyDiagnoseResult, serviceDO *service.ServiceDO, orderDO *order.OrderDO, realtimeDO []realtime.RealtimeDO) (res *model.UserExperienceVO) {
	res = &model.UserExperienceVO{
		OrderStartTime: orderDO.CreationTime,
		OrderEndTime:   orderDO.GetEndTimestamp(),
	}
	if orderDO.Energy < 0 {
		isReverseSwap := true
		res.IsReverseSwap = &isReverseSwap
	} else if orderDO.Energy > 0 {
		isReverseSwap := false
		res.IsReverseSwap = &isReverseSwap
	}
	if orderDO.AutoSwap == 1 {
		isAutomatedSwap := true
		res.IsAutomatedSwap = &isAutomatedSwap
	} else if orderDO.AutoSwap == 2 {
		isAutomatedSwap := false
		res.IsAutomatedSwap = &isAutomatedSwap
	}
	if serviceDO != nil {
		res.ServiceStartTime = serviceDO.ServiceStartTime
		res.ServiceEndTime = serviceDO.ServiceEndTime
	}
	if satisfyData != nil {
		res.Score = &satisfyData.Score
		res.UserTag = satisfyData.UserTag
	}
	if satisfyDiagnose != nil && len(satisfyDiagnose.MultipleSwap) > 0 {
		isMultipleSwap := true
		res.IsMultipleSwap = &isMultipleSwap
		for _, record := range satisfyDiagnose.MultipleSwap {
			res.MultiSwap = append(res.MultiSwap, model.MultiSwap{
				OrderStartTime:   record.OrderStartTime,
				OrderEndTime:     record.OrderEndTime,
				OrderId:          record.OrderId,
				ServiceStartTime: record.ServiceStartTime,
				ServiceEndTime:   record.ServiceEndTime,
				ServiceId:        record.ServiceId,
				FinishResult:     record.FinishResult,
				Project:          record.Project,
				Interval:         record.Interval,
			})
		}
	}
	for _, record := range realtimeDO {
		if util.ParseInt(record.Data[realtime.OnDutyStatusDataId[orderDO.Project]]) == realtime.OnDutyStatusUnAttended {
			isOnDuty := false
			res.IsOnDuty = &isOnDuty
		} else if util.ParseInt(record.Data[realtime.OnDutyStatusDataId[orderDO.Project]]) == realtime.OnDutyStatusAttended {
			isOnDuty := true
			res.IsOnDuty = &isOnDuty
			break
		}
	}
	return
}
