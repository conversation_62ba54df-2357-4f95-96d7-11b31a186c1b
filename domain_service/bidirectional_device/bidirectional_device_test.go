package bidirectional_device

import (
	"fmt"
	"net/http"
	"os"
	"testing"

	"github.com/gin-gonic/gin"
	gocache "github.com/patrickmn/go-cache"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	ulog "git.nevint.com/golang-libs/common-utils/logger"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	domain_alarm "git.nevint.com/welkin2/welkin-backend/domain/alarm"
	domain_device "git.nevint.com/welkin2/welkin-backend/domain/device"
	domain_realtime "git.nevint.com/welkin2/welkin-backend/domain/realtime"
	log "git.nevint.com/welkin2/welkin-backend/logger"
)

var w client.Watcher
var ctx *gin.Context

func init() {
	fmt.Println("init test")
	os.Setenv("APOLLO_META", "https://apollo-config-cn-stg.nioint.com")
	os.Setenv("ENV", "stg")
	os.Setenv("APOLLO_ACCESSKEY_SECRET", "NIO-ENCRYPT-START_70078af90a9896516b6364767b1a45c20fb96b870f9c958a17b782935828ec8d452c7435b1801d4a17a31a2f7734c88d_NIO-ENCRYPT-END")
	cfg := &ucfg.Config{}
	ucfg.GetConfigApollo("config.json", true, cfg, ucfg.NeedValidate(true), ucfg.ServiceName("ppd-welkin-backend"), ucfg.Cluster(ucfg.GetWelkinApolloCluster()))
	defer ulog.Final()
	cfg.Log.DirPath = "/tmp/logs"
	config.Cfg = cfg
	log.Init(cfg.Log, true)
	logger := log.Logger.Named("activity")
	w = client.NewWatcherByParam(config.Cfg, logger)
	cache.CacheRealtimeDataID(w.Mongodb().Client)
	cache.PowerSwapCache = &cache.DeviceInfoCache{
		Cache:         gocache.New(gocache.NoExpiration, gocache.NoExpiration),
		ResourceCache: gocache.New(gocache.NoExpiration, gocache.NoExpiration),
	}
	cache.PowerSwapCache.RefreshDeviceInfoCache(w.Mongodb().Client)
	ctx = &gin.Context{Request: &http.Request{Header: make(http.Header)}}
	ctx.Request.Header.Set("X-Request-ID", "test")

	cache.InitAlarmInfoCache()
	cache.StuckAlarmInfoCache.Logger = logger
	cache.StuckAlarmInfoCache.RefreshStuckAlarmInfoCache(w.Mongodb().Client)
	cache.DeviceAlarmInfoCache.Logger = logger
	cache.DeviceAlarmInfoCache.RefreshDeviceAlarmInfoCache(w.Mongodb().Client)

	InitOnce()
}

func TestGetDeviceBasicInfo(t *testing.T) {
	// Initialize the service with mocked dependencies
	b := &BidirectionalDeviceService{
		DeviceDO: &domain_device.Device{},
	}
	cond := BidirectionalDeviceCond{
		Project:   umw.PUS3,
		DeviceId:  "PS-NIO-efd6e196-0fb4a06c",
		StartTime: 1742482800000,
		EndTime:   1742482800000,
	}
	res, err := b.getDeviceBasicInfo(ctx, cond)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(ucmd.ToJsonStrIgnoreErr(res))
}
func TestGetDeviceFaultData(t *testing.T) {
	b := &BidirectionalDeviceService{
		DeviceDO: &domain_device.Device{},
		AlarmDO:  &domain_alarm.AlarmDO{},
	}

	cond := BidirectionalDeviceCond{
		Project:   umw.PUS3,
		DeviceId:  "PS-NIO-efd6e196-0fb4a06c",
		StartTime: 1740758400000,
		EndTime:   1742400000000,
	}

	alarms, faultDetail, faultTrend, err := b.getDeviceFaultData(ctx, cond)
	if err != nil {
		t.Errorf("GetDeviceFaultData returned unexpected error: %v", err)
	}

	fmt.Println("alarms len:", len(alarms))
	fmt.Println("faultDetail:", ucmd.ToJsonStrIgnoreErr(faultDetail))
	fmt.Println("faultTrend:", ucmd.ToJsonStrIgnoreErr(faultTrend))
}

func TestGetDeviceReliability(t *testing.T) {
	b := &BidirectionalDeviceService{
		DeviceDO: &domain_device.Device{},
		AlarmDO:  &domain_alarm.AlarmDO{},
	}
	cond := []BidirectionalDeviceCond{
		{
			Project:   umw.PUS3,
			DeviceId:  "PS-NIO-efd6e196-0fb4a06c",
			StartTime: 1740758400000,
			EndTime:   1742400000000,
		},
	}
	res, err := b.getDeviceReliability(ctx, cond)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(ucmd.ToJsonStrIgnoreErr(res))
}

func TestGetDeviceRealtimeData(t *testing.T) {
	b := &BidirectionalDeviceService{
		DeviceDO:   &domain_device.Device{},
		AlarmDO:    &domain_alarm.AlarmDO{},
		RealtimeDO: &domain_realtime.RealtimeDO{},
	}
	// cond := BidirectionalDeviceCond{
	// 	Project:   umw.PUS3,
	// 	DeviceId:  "PS-NIO-efd6e196-0fb4a06c",
	// 	StartTime: 1742313600000,
	// 	EndTime:   1742400000000,
	// }
	// powerDistribution, energyManagementRevenue, err := b.getDeviceRealtimeData(ctx, cond)
	// if err != nil {
	// 	t.Fatal(err)
	// }
	// fmt.Println("powerDistribution", ucmd.ToJsonStrIgnoreErr(powerDistribution))
	// fmt.Println("energyManagementRevenue", ucmd.ToJsonStrIgnoreErr(energyManagementRevenue))

	cond := BidirectionalDeviceCond{
		Project:   umw.PUS4,
		DeviceId:  "PUS-NIO-095fdc2a-5840a9b6",
		StartTime: 1742313600000,
		EndTime:   1742400000000,
	}
	powerDistribution, energyManagementRevenue, err := b.getDeviceRealtimeData(ctx, cond)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println("powerDistribution", ucmd.ToJsonStrIgnoreErr(powerDistribution))
	fmt.Println("energyManagementRevenue", ucmd.ToJsonStrIgnoreErr(energyManagementRevenue))
}

func TestGetDeviceRealtimeDataDaily(t *testing.T) {
	b := &BidirectionalDeviceService{
		DeviceDO:   &domain_device.Device{},
		AlarmDO:    &domain_alarm.AlarmDO{},
		RealtimeDO: &domain_realtime.RealtimeDO{},
	}
	// cond := BidirectionalDeviceCond{
	// 	Project:   umw.PUS3,
	// 	DeviceId:  "PS-NIO-efd6e196-0fb4a06c",
	// 	StartTime: 1742313600000,
	// 	EndTime:   1742400000000,
	// }
	// dataIdMap := make(map[string]bool)
	// for dataId := range RealtimePCUElectricityMeterPower[cond.Project] {
	// 	dataIdMap[dataId] = true
	// }
	// for dataId := range RealtimeModuleWorkingStatus[cond.Project] {
	// 	dataIdMap[dataId] = true
	// }
	// for dataId := range RealtimeModuleActivePower[cond.Project] {
	// 	dataIdMap[dataId] = true
	// }
	// res, err := b.getDeviceRealtimeDaily(ctx, cond, dataIdMap)
	// if err != nil {
	// 	t.Fatal(err)
	// }
	// fmt.Println("res:", len(res))

	cond := BidirectionalDeviceCond{
		Project:   umw.PUS4,
		DeviceId:  "PUS-NIO-095fdc2a-5840a9b6",
		StartTime: 1742313600000,
		EndTime:   1742400000000,
	}
	dataIdMap := make(map[string]bool)
	for dataId := range RealtimePCUElectricityMeterPower[cond.Project] {
		dataIdMap[dataId] = true
	}
	for dataId := range RealtimeModuleWorkingStatus[cond.Project] {
		dataIdMap[dataId] = true
	}
	for dataId := range RealtimeModuleActivePower[cond.Project] {
		dataIdMap[dataId] = true
	}
	fmt.Println("实时数据点数:", len(dataIdMap))
	res, err := b.getDeviceRealtimeDaily(ctx, cond, dataIdMap)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println("res:", len(res))
}
