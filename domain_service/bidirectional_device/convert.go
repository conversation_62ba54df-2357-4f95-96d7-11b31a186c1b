package bidirectional_device

import (
	"fmt"
	"sort"

	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	domain_common "git.nevint.com/welkin2/welkin-backend/domain/common"
	domain_device "git.nevint.com/welkin2/welkin-backend/domain/device"
	"git.nevint.com/welkin2/welkin-backend/util"
)

func convertBasicInfoVO(deviceInfo umw.MongoDeviceInfo, batteryInfo map[int]domain_device.BatteryInfo, rbParams []domain_device.RbDeviceParam) BasicInfoVO {
	res := BasicInfoVO{
		DeviceId:    deviceInfo.DeviceId,
		Project:     deviceInfo.Project,
		Description: deviceInfo.Description,
		ResourceId:  deviceInfo.ResourceId,
	}
	if deviceInfo.IsHighwaySwap == 1 {
		res.IsHighwaySwap = true
	}
	batteryCount := make(map[string]int64)
	for _, info := range batteryInfo {
		originType := int32(info.BatteryType)
		userCapacity := domain_common.ConvertBatteryUserType(&originType)
		if userCapacity != nil {
			// 70和75放在一起
			if *userCapacity == 70 || *userCapacity == 75 {
				batteryCount["70/75kWh"]++
			} else {
				batteryCount[fmt.Sprintf("%dkWh", *userCapacity)]++
			}
		}
	}
	for k, v := range batteryCount {
		res.BatteryCount = append(res.BatteryCount, BatteryCount{
			BatteryType: k,
			Count:       v,
		})
	}
	sort.Slice(res.BatteryCount, func(i, j int) bool {
		return res.BatteryCount[i].BatteryType < res.BatteryCount[j].BatteryType
	})
	if len(rbParams) == 0 {
		return res
	}
	var charingPileCount, addPileCount int64
	for k, v := range rbParams[0].Params {
		if RbParamChargingPileCount[deviceInfo.Project][k] {
			tmp := util.ParseInt(v)
			if tmp > 0 {
				charingPileCount += int64(tmp)
			}
		} else if RbParamAddPileCount[deviceInfo.Project][k] {
			tmp := util.ParseInt(v)
			if tmp > 0 {
				addPileCount += int64(tmp)
			}
		}
	}
	res.ChargingPileCount = &charingPileCount
	res.AddPileCount = &addPileCount
	return res
}

// 将多天/多个设备的PowerDistributionVO进行聚合，取平均
func convertPowerDistributionVO(powerDistributionVOs []PowerDistributionVO) PowerDistributionVO {
	res := PowerDistributionVO{}
	count := float64(len(powerDistributionVOs))
	if count == 0 {
		return res
	}
	var moduleUseDetailMap map[int][]string
	if count == 1 {
		moduleUseDetailMap = make(map[int][]string)
		for _, record := range powerDistributionVOs[0].ModuleUse {
			sort.Slice(record.Detail, func(i, j int) bool {
				return ModuleSortSequence[record.Detail[i]] < ModuleSortSequence[record.Detail[j]]
			})
			moduleUseDetailMap[record.Hour] = record.Detail
		}
	}

	realOutputPower := make(map[int]float64)
	moduleUse := make(map[int]float64)
	modulelyOutputPower := make(map[string]float64)
	modulelyHourlyOutputPower := make(map[string]map[int]float64)
	for _, vo := range powerDistributionVOs {
		for _, record := range vo.RealOutputPower {
			realOutputPower[record.Hour] += record.Value
		}
		for _, record := range vo.ModuleUse {
			moduleUse[record.Hour] += record.Value
		}
		for _, record := range vo.ModuleOutputPower {
			modulelyOutputPower[record.Module] += record.Value
			for _, hourlyValue := range record.HourlyValue {
				if modulelyHourlyOutputPower[record.Module] == nil {
					modulelyHourlyOutputPower[record.Module] = make(map[int]float64)
				}
				modulelyHourlyOutputPower[record.Module][hourlyValue.Hour] += hourlyValue.Value
			}
		}
	}
	for hour := range 24 {
		res.RealOutputPower = append(res.RealOutputPower, HourlyValue{
			Hour:  hour,
			Value: util.RoundFloat(realOutputPower[hour]/count, 2),
		})
		item := ModuleUse{
			HourlyValue: HourlyValue{
				Hour:  hour,
				Value: util.RoundFloat(moduleUse[hour]/count, 2),
			},
		}
		if moduleUseDetailMap != nil {
			item.Detail = moduleUseDetailMap[hour]
		}
		res.ModuleUse = append(res.ModuleUse, item)
	}
	for module := range modulelyOutputPower {
		item := ModuleOutputPower{
			Module: module,
			Value:  util.RoundFloat(modulelyOutputPower[module]/count, 2),
		}
		for hour := range 24 {
			item.HourlyValue = append(item.HourlyValue, HourlyValue{
				Hour:  hour,
				Value: util.RoundFloat(modulelyHourlyOutputPower[module][hour]/count, 2),
			})
		}
		res.ModuleOutputPower = append(res.ModuleOutputPower, item)
		sort.Slice(res.ModuleOutputPower, func(i, j int) bool {
			return ModuleSortSequence[res.ModuleOutputPower[i].Module] < ModuleSortSequence[res.ModuleOutputPower[j].Module]
		})
	}
	return res
}

// 将多天/多个设备的EnergyManagementRevenueVO进行聚合，取平均
func convertEnergyManagementRevenueVO(energyManagementRevenueVOs []EnergyManagementRevenueVO) EnergyManagementRevenueVO {
	res := EnergyManagementRevenueVO{}
	count := float64(len(energyManagementRevenueVOs))
	if count == 0 {
		return res
	}
	chargeValue := make(map[int]float64)
	dischargeValue := make(map[int]float64)
	chargeCost := make(map[int]float64)
	dischargeRevenue := make(map[int]float64)
	for _, vo := range energyManagementRevenueVOs {
		for _, record := range vo.ChargeInfo {
			chargeValue[record.Hour] += record.Value
			chargeCost[record.Hour] += record.CostEstimate
		}
		for _, record := range vo.DischargeInfo {
			dischargeValue[record.Hour] += record.Value
			dischargeRevenue[record.Hour] += record.CostEstimate
		}
	}
	for hour := range 24 {
		res.ChargeInfo = append(res.ChargeInfo, ChargeInfo{
			HourlyValue: HourlyValue{
				Hour:  hour,
				Value: util.RoundFloat(chargeValue[hour]/count, 2),
			},
			CostEstimate: util.RoundFloat(chargeCost[hour]/count, 2),
		})
		res.DischargeInfo = append(res.DischargeInfo, ChargeInfo{
			HourlyValue: HourlyValue{
				Hour:  hour,
				Value: util.RoundFloat(dischargeValue[hour]/count, 2),
			},
			CostEstimate: util.RoundFloat(dischargeRevenue[hour]/count, 2),
		})
	}
	return res
}
