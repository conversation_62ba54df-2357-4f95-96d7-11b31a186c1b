package bidirectional_device

import (
	"fmt"
	"sync"

	"go.mongodb.org/mongo-driver/bson"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/client"
	domain_alarm "git.nevint.com/welkin2/welkin-backend/domain/alarm"
)

const (
	ModuleEfficiency              = "efficiency"
	ModuleReliability             = "reliability"
	ModulePowerDistribution       = "power_distribution"
	ModuleEnergyManagementRevenue = "energy_management_revenue"

	AlarmWater         = "alarm_water"         // 水冷类
	AlarmCommunication = "alarm_communication" // 通信类
	AlarmBMS           = "alarm_bms"           // BMS类
	AlarmModule        = "alarm_module"        // 模块类
	AlarmSoftware      = "alarm_software"      // 站控软件类
)

var AlarmCategoryDesc = map[string]string{
	AlarmWater:         "水冷类",
	AlarmCommunication: "通信类",
	AlarmBMS:           "BMS类",
	AlarmModule:        "模块类",
	AlarmSoftware:      "站控软件类",
}

var once sync.Once

var (
	AlarmPatterns = make(map[string]map[string][]string)
	AlarmNeeded   = make(map[string]map[string][]string) // {"PUS3": {"alarm_water": ["699931", "699919"]}}

	RealtimePCUElectricityMeterPower = make(map[string]map[string]bool)   // PCU电表总功率
	RealtimeSCTChargerOutputPower    = make(map[string]map[string]bool)   // SCT充电桩输出功率（暂时不用）
	RealtimeModuleWorkingStatus      = make(map[string]map[string]string) // 模块工作状态（状态信息）
	RealtimeModuleActivePower        = make(map[string]map[string]string) // 模块有功功率
	RealtimeModuleOutputVoltage      = make(map[string]map[string]string) // 模块输出电压
	RealtimeModuleOutputCurrent      = make(map[string]map[string]string) // 模块输出电流
	RealtimeNeeded                   = make(map[string]map[string]bool)

	// 模块排序顺序
	ModuleSortSequence = make(map[string]int)

	// 充电桩配置根数
	RbParamChargingPileCount = map[string]map[int64]bool{
		umw.PUS3: {
			902561: true,
			902661: true,
			902761: true,
			902861: true,
		},
		umw.PUS4: {
			973062: true,
			973162: true,
			973262: true,
			973362: true,
			973462: true,
			973562: true,
			973662: true,
			973762: true,
			973862: true,
			973962: true,
			974062: true,
			974162: true,
			974262: true,
			974362: true,
			974462: true,
			974562: true,
		},
	}

	// 站桩共建桩配置根数
	RbParamAddPileCount = map[string]map[int64]bool{
		umw.PUS3: {902426: true},
		umw.PUS4: {972041: true},
	}
)

func InitOnce() {
	once.Do(func() {
		fmt.Println("init bidirectional_device model")

		AlarmPatterns[umw.PUS3] = map[string][]string{
			AlarmWater:         {"MCS-#.*水冷.*仓压力开关保护报警", "MCS-#.*水冷.*#压力开关保护报警", "MCS-#.*水冷.*泵停止超时", "MCS-.*水冷.*流量开关故障", "MCS-.*水冷后仓流量过低"},
			AlarmCommunication: {".*CDC与BMS通信故障", "PLC与货叉伺服通讯断开", "PLC与#.*号枪头伺服通讯断开", "MCS与HPC #.*水冷通信故障", "MCS与#.*水冷通信故障", "MCS与PLC通信故障", "MCS与.*# PCU通信故障", "MCS与.*CDC通信故障", ".*CMC与电表.*通信故障", ".*CDC与PCU通信故障", ".*CDC电池包唤醒失败"},
			AlarmBMS:           {".*BMS电池.*告警.*", ".*BMS电压不匹配", "^[CA].*BMS.*故障$", ".*BMS电池允许充电状态为禁止", ".*BMS电池直流允许充电状态为禁止", ".*BMS故障等级大于3"},
			AlarmModule:        {".*AC/DC.*模块输出端口过压", ".*PCU启动AC/DC.*模块失败", ".*模块PFC缓起继电器故障"},
			AlarmSoftware:      {".*CDC电池过流故障", ".*CDC直流功率线路断线", ".*水冷 前仓.*水泵变频器故障.*"},
		}
		// TODO: PUS4的告警pattern需要确认
		AlarmPatterns[umw.PUS4] = map[string][]string{
			AlarmWater:         {"MCS 水冷.*压力开关保护报警", "MCS-#.*水冷.*#压力开关保护报警", "MCS 水冷.*泵停止超时", "MCS 水冷.*流量开关故障", "MCS 水冷后仓流量过低"},
			AlarmCommunication: {".*CDC与BMS通信故障", "PLC与货叉伺服通讯断开", "PLC与#.*号枪头伺服通讯断开", "MCS与HPC #.*水冷通信故障", "MCS与#.*水冷通信故障", "MCS与PLC通信故障", "MCS与.*# PCU通信故障", "MCS与.*CDC通信故障", ".*CMC与电表.*通信故障", ".*CDC与PCU通信故障", ".*CDC电池包唤醒失败"},
			AlarmBMS:           {".*BMS电池.*告警.*", ".*BMS电压不匹配", "^[CA].*BMS.*故障$", ".*BMS电池允许充电状态为禁止", ".*BMS电池直流允许充电状态为禁止", ".*BMS故障等级大于3"},
			AlarmModule:        {".*AC/DC.*模块输出端口过压", ".*PCU启动AC/DC.*模块失败", ".*模块PFC缓起继电器故障"},
			AlarmSoftware:      {".*CDC电池过流故障", ".*CDC直流功率线路断线", ".*水冷 前仓.*水泵变频器故障.*"},
		}
		searchAlarmId := func(project, alaryType string) {
			patterns := AlarmPatterns[project][alaryType]
			or := make([]bson.M, 0)
			for _, pattern := range patterns {
				or = append(or, bson.M{"var_cn_name": bson.M{"$regex": pattern}})
			}
			filter := bson.D{{"$or", or}}
			var alarms []umw.MongoDeviceAlarmInfo
			_, err := client.GetWatcher().Mongodb().NewMongoEntry(filter).FindMany(umw.Device2Cloud, fmt.Sprintf("alarm-%s", ucmd.RenameProjectDB(project)), nil, &alarms)
			if err != nil {
				panic(err)
			}
			for _, alarm := range alarms {
				AlarmNeeded[project][alaryType] = append(AlarmNeeded[project][alaryType], alarm.DataId)
			}
		}
		initAlarmNeeded := func(project string) {
			AlarmNeeded[project] = make(map[string][]string)
			searchAlarmId(project, AlarmWater)
			searchAlarmId(project, AlarmCommunication)
			searchAlarmId(project, AlarmBMS)
			searchAlarmId(project, AlarmModule)
			searchAlarmId(project, AlarmSoftware)
		}
		initAlarmNeeded(umw.PUS3)
		initAlarmNeeded(umw.PUS4)

		RealtimePCUElectricityMeterPower[umw.PUS3] = map[string]bool{
			"104509": true,
			"104525": true,
		}
		RealtimePCUElectricityMeterPower[umw.PUS4] = map[string]bool{
			"852060": true,
			"902860": true,
		}
		RealtimeSCTChargerOutputPower[umw.PUS3] = map[string]bool{
			"200026": true,
			"200106": true,
			"200186": true,
			"200266": true,
		}
		RealtimeSCTChargerOutputPower[umw.PUS4] = map[string]bool{
			"134023": true,
			"144023": true,
			"154023": true,
			"164023": true,
			"174023": true,
			"184023": true,
			"194023": true,
			"204023": true,
			"220423": true,
			"221423": true,
			"222423": true,
			"223423": true,
			"224423": true,
			"225423": true,
			"226423": true,
			"227423": true,
		}
		RealtimeModuleWorkingStatus[umw.PUS3] = make(map[string]string)
		RealtimeModuleActivePower[umw.PUS3] = make(map[string]string)
		RealtimeModuleOutputVoltage[umw.PUS3] = make(map[string]string)
		RealtimeModuleOutputCurrent[umw.PUS3] = make(map[string]string)
		moduleSequence := 1
		for id := range 10 {
			moduleName := fmt.Sprintf("1# AC/DC%d", id+1)
			ModuleSortSequence[moduleName] = moduleSequence
			moduleSequence++
			RealtimeModuleWorkingStatus[umw.PUS3][fmt.Sprintf("%d", 102000+id*10)] = moduleName
			RealtimeModuleActivePower[umw.PUS3][fmt.Sprintf("%d", 100011+id*30)] = moduleName
			RealtimeModuleOutputVoltage[umw.PUS3][fmt.Sprintf("%d", 100003+id*30)] = moduleName
			RealtimeModuleOutputCurrent[umw.PUS3][fmt.Sprintf("%d", 100004+id*30)] = moduleName
		}
		RealtimeModuleWorkingStatus[umw.PUS4] = make(map[string]string)
		RealtimeModuleActivePower[umw.PUS4] = make(map[string]string)
		RealtimeModuleOutputVoltage[umw.PUS4] = make(map[string]string)
		RealtimeModuleOutputCurrent[umw.PUS4] = make(map[string]string)
		for id := range 12 {
			moduleName := fmt.Sprintf("1# PCU-AC/DC%d", id+1)
			ModuleSortSequence[moduleName] = moduleSequence
			moduleSequence++
			RealtimeModuleWorkingStatus[umw.PUS4][fmt.Sprintf("%d", 851201+id*20)] = moduleName
			RealtimeModuleActivePower[umw.PUS4][fmt.Sprintf("%d", 864812+id*2000)] = moduleName
			RealtimeModuleOutputVoltage[umw.PUS4][fmt.Sprintf("%d", 864804+id*2000)] = moduleName
			RealtimeModuleOutputCurrent[umw.PUS4][fmt.Sprintf("%d", 864805+id*2000)] = moduleName
		}
		initRealtimeNeeded := func(project string) {
			RealtimeNeeded[project] = make(map[string]bool)
			for k := range RealtimePCUElectricityMeterPower[project] {
				RealtimeNeeded[project][k] = true
			}
			for k := range RealtimeModuleWorkingStatus[project] {
				RealtimeNeeded[project][k] = true
			}
			for k := range RealtimeModuleActivePower[project] {
				RealtimeNeeded[project][k] = true
			}
			for k := range RealtimeModuleOutputVoltage[project] {
				RealtimeNeeded[project][k] = true
			}
			for k := range RealtimeModuleOutputCurrent[project] {
				RealtimeNeeded[project][k] = true
			}
		}
		initRealtimeNeeded(umw.PUS3)
		initRealtimeNeeded(umw.PUS4)
	})
}

type BidirectionalDeviceRequest struct {
	DeviceIds string `json:"device_ids" form:"device_ids"`
	StartTime int64  `json:"start_time" form:"start_time"`
	EndTime   int64  `json:"end_time" form:"end_time"`
	Module    string `json:"module" form:"module"`
}

type BidirectionalDeviceInfoVO struct {
	BasicInfo               []BasicInfoVO             `json:"basic_info"`
	Reliability             ReliabilityVO             `json:"reliability"`
	PowerDistribution       PowerDistributionVO       `json:"power_distribution"`
	EnergyManagementRevenue EnergyManagementRevenueVO `json:"energy_management_revenue"`
}

type BasicInfoVO struct {
	Project           string         `json:"project"`
	DeviceId          string         `json:"device_id"`
	Description       string         `json:"description"`
	ResourceId        string         `json:"resource_id"`
	IsHighwaySwap     bool           `json:"is_highway_swap"`
	ChargingPileCount *int64         `json:"charging_pile_count"`
	AddPileCount      *int64         `json:"add_pile_count"`
	BatteryCount      []BatteryCount `json:"battery_count"`
}

type BatteryCount struct {
	BatteryType string `json:"battery_type"`
	Count       int64  `json:"count"`
}

type ReliabilityVO struct {
	SwapFtt     *float64      `json:"swap_ftt"`
	ChargeFtt   *float64      `json:"charge_ftt"`
	FaultDetail []FaultDetail `json:"fault_detail"`
	FaultTrend  []FaultTrend  `json:"fault_trend"`
}

type FaultDetail struct {
	Category    string `json:"category"`
	Description string `json:"description"`
	FaultCount  int64  `json:"fault_count"`
}

type FaultTrend struct {
	Day   int64 `json:"day"`
	Count int64 `json:"count"`
}

type BidirectionalAlarm struct {
	domain_alarm.AlarmDO
	Category   string `json:"category"`
	DeviceName string `json:"device_name"`
}

type PowerDistributionVO struct {
	RealOutputPower   []HourlyValue       `json:"real_output_power"`
	ModuleUse         []ModuleUse         `json:"module_use"`
	ModuleOutputPower []ModuleOutputPower `json:"module_output_power"`
}

type ModuleOutputPower struct {
	Module      string        `json:"module"`
	Value       float64       `json:"value"`
	HourlyValue []HourlyValue `json:"hourly_value"`
}

type HourlyValue struct {
	Hour  int     `json:"hour"`
	Value float64 `json:"value"`
}

type ModuleUse struct {
	HourlyValue
	Detail []string `json:"detail"`
}

type EnergyManagementRevenueVO struct {
	ChargeInfo    []ChargeInfo `json:"charge_info"`
	DischargeInfo []ChargeInfo `json:"discharge_info"`
}

type ChargeInfo struct {
	HourlyValue
	CostEstimate float64 `json:"cost_estimate"`
}
