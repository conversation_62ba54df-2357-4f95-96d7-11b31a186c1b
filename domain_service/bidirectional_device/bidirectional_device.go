package bidirectional_device

import (
	"archive/zip"
	"bytes"
	"context"
	"encoding/csv"
	"fmt"
	"math"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	domain_alarm "git.nevint.com/welkin2/welkin-backend/domain/alarm"
	domain_device "git.nevint.com/welkin2/welkin-backend/domain/device"
	domain_realtime "git.nevint.com/welkin2/welkin-backend/domain/realtime"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	"git.nevint.com/welkin2/welkin-backend/util"
)

type BidirectionalDeviceService struct {
	DeviceDO   *domain_device.Device
	AlarmDO    *domain_alarm.AlarmDO
	RealtimeDO *domain_realtime.RealtimeDO
}

type BidirectionalDeviceCond struct {
	Project   string
	DeviceId  string
	StartTime int64
	EndTime   int64
}

// getDeviceBasicInfo 查询双向站设备基本信息
func (b *BidirectionalDeviceService) getDeviceBasicInfo(ctx context.Context, cond BidirectionalDeviceCond) (res BasicInfoVO, err error) {
	deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(cond.DeviceId)
	if !found {
		log.CtxLog(ctx).Errorf("device not found: %s", cond.DeviceId)
		return res, fmt.Errorf("device not found: %s", cond.DeviceId)
	}
	// 设备基本信息
	deviceDO := domain_device.Device{
		DeviceId: cond.DeviceId,
		Project:  cond.Project,
	}
	// 电池信息
	batteryInfo, err := deviceDO.GetSlotBatteryInfo(cond.EndTime)
	if err != nil {
		log.CtxLog(ctx).Errorf("getDeviceBasicInfo, fail to get slot battery info: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return res, err
	}
	// 桩数量（赤兔）
	params := make([]int64, 0)
	for param := range RbParamAddPileCount[cond.Project] {
		params = append(params, param)
	}
	for param := range RbParamChargingPileCount[cond.Project] {
		params = append(params, param)
	}
	rbCond := domain_device.RbDeviceParamsCond{
		Project:   cond.Project,
		Devices:   []string{cond.DeviceId},
		ParamKeys: params,
	}
	rbParams, err := b.DeviceDO.ListRbDeviceParams(ctx, rbCond)
	if err != nil {
		log.CtxLog(ctx).Errorf("getDeviceBasicInfo, fail to list rb device params: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(rbCond))
		return res, err
	}
	res = convertBasicInfoVO(*deviceInfo, batteryInfo, rbParams)
	return res, nil
}

// getDeviceFaultData 查询单个设备故障数据
func (b *BidirectionalDeviceService) getDeviceFaultData(ctx context.Context, cond BidirectionalDeviceCond) (allAlarms []BidirectionalAlarm, dailyFaultCount map[int64]int, faultCategoryCount map[string]int, err error) {
	g := ucmd.NewErrGroup(ctx, 5)
	mu := sync.Mutex{}
	days := util.SplitTimeByDay(cond.StartTime, cond.EndTime)
	dailyFaultCount = make(map[int64]int)
	faultCategoryCount = make(map[string]int)
	deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(cond.DeviceId)
	var deviceName string
	if found {
		deviceName = deviceInfo.Description
	}
	// 每日故障
	for _, day := range days {
		g.GoRecover(func() error {
			for category, dataIds := range AlarmNeeded[cond.Project] {
				alarmCond := domain_alarm.ListAlarmCond{
					StartTs:  day.StartTime,
					EndTs:    day.EndTime,
					Project:  cond.Project,
					DeviceId: &cond.DeviceId,
					AlarmIds: dataIds,
				}
				alarms, _, err := b.AlarmDO.ListAlarms(ctx, alarmCond)
				if err != nil {
					log.CtxLog(ctx).Errorf("getDeviceFaultData, fail to list alarms: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(alarmCond))
					return err
				}
				mu.Lock()
				for _, alarm := range alarms {
					allAlarms = append(allAlarms, BidirectionalAlarm{
						AlarmDO:    alarm,
						Category:   category,
						DeviceName: deviceName,
					})
				}
				faultCategoryCount[category] += len(alarms)
				dailyFaultCount[day.StartTime] += len(alarms)
				mu.Unlock()
			}
			return nil
		})
	}
	if err = g.Wait(); err != nil {
		log.CtxLog(ctx).Errorf("getDeviceFaultData, fail to get device fault data: %v", err)
		return
	}
	return
}

// getDeviceReliability 设备可靠性看板数据
func (b *BidirectionalDeviceService) getDeviceReliability(ctx context.Context, conds []BidirectionalDeviceCond) (res ReliabilityVO, err error) {
	if len(conds) == 0 {
		return
	}
	g := ucmd.NewErrGroup(ctx)
	mu := sync.Mutex{}
	// 告警数据
	g.GoRecover(func() error {
		dailyFaultCountAll := make(map[int64]int)
		faultCategoryCountAll := make(map[string]int)
		for _, cond := range conds {
			_, dailyFaultCount, faultCategoryCount, gErr := b.getDeviceFaultData(ctx, cond)
			if gErr != nil {
				log.CtxLog(ctx).Errorf("getDeviceReliability, fail to get device fault data: %v, cond: %s", gErr, ucmd.ToJsonStrIgnoreErr(cond))
				return gErr
			}
			for day, count := range dailyFaultCount {
				dailyFaultCountAll[day] += count
			}
			for category, count := range faultCategoryCount {
				faultCategoryCountAll[category] += count
			}
		}
		mu.Lock()
		defer mu.Unlock()
		for category, count := range faultCategoryCountAll {
			res.FaultDetail = append(res.FaultDetail, FaultDetail{
				Category:    category,
				Description: AlarmCategoryDesc[category],
				FaultCount:  int64(count),
			})
		}
		sort.Slice(res.FaultDetail, func(i, j int) bool {
			return res.FaultDetail[i].Category < res.FaultDetail[j].Category
		})
		for day, count := range dailyFaultCountAll {
			res.FaultTrend = append(res.FaultTrend, FaultTrend{
				Day:   day,
				Count: int64(count),
			})
		}
		sort.Slice(res.FaultTrend, func(i, j int) bool {
			return res.FaultTrend[i].Day < res.FaultTrend[j].Day
		})
		return nil
	})

	// ftt计算
	g.GoRecover(func() error {
		// 通过resource id调用galaxylite接口
		var deviceIds, swapList, chargerList []string
		for _, cond := range conds {
			deviceIds = append(deviceIds, cond.DeviceId)
			deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(cond.DeviceId)
			if found {
				swapList = append(swapList, deviceInfo.ResourceId)
			}
		}
		var chargeInfos []umw.MongoDeviceInfo
		_, gErr := client.GetWatcher().Mongodb().NewMongoEntry(bson.D{{"device_id", bson.M{"$in": deviceIds}}}).FindMany(umw.OAuthDB, "charger_basic_info", nil, &chargeInfos)
		if gErr != nil {
			log.CtxLog(ctx).Errorf("getDeviceReliability, fail to get charger basic info: %v", gErr)
			return gErr
		}
		for _, charger := range chargeInfos {
			if charger.ResourceId == "" {
				continue
			}
			chargerList = append(chargerList, charger.ResourceId)
		}
		var swapFttVal, chargeFttVal *float64
		// 站ftt
		swapFtt, gErr := client.QueryGalaxyIndictor(ctx, "ftt", conds[0].StartTime, conds[0].EndTime, nil, []client.IndicatorDimOption{
			{
				DimName:  "device_id",
				DimValue: strings.Join(swapList, ","),
			},
		})
		if gErr != nil {
			log.CtxLog(ctx).Errorf("getDeviceReliability, fail to get swap ftt: %v", gErr)
			// 调用galaxylite接口失败，记录日志直接返回
			return nil
		}
		if swapFtt != nil && len(swapFtt.ResultList) > 0 && swapFtt.ResultList[0].IndicatorValue != "" {
			val := util.ParseFloat(swapFtt.ResultList[0].IndicatorValue)
			swapFttVal = &val
		}
		// 桩ftt
		chargeFtt, gErr := client.QueryGalaxyIndictor(ctx, "nio_charger_mtt", conds[0].StartTime, conds[0].EndTime, nil, []client.IndicatorDimOption{
			{
				DimName:  "device_id",
				DimValue: strings.Join(chargerList, ","),
			},
		})
		if gErr != nil {
			log.CtxLog(ctx).Errorf("getDeviceReliability, fail to get charge ftt: %v", gErr)
			return nil
		}
		if chargeFtt != nil && len(chargeFtt.ResultList) > 0 && chargeFtt.ResultList[0].IndicatorValue != "" {
			val := util.ParseFloat(chargeFtt.ResultList[0].IndicatorValue)
			chargeFttVal = &val
		}
		mu.Lock()
		defer mu.Unlock()
		res.SwapFtt = util.RoundFloatPtr(swapFttVal, 2)
		res.ChargeFtt = util.RoundFloatPtr(chargeFttVal, 2)
		return nil
	})
	if err = g.Wait(); err != nil {
		log.CtxLog(ctx).Errorf("getDeviceReliability, fail to get device reliability: %v", err)
		return
	}
	return
}

type RealtimePoint struct {
	RealtimeSum   float64
	RealtimeCount float64
}

// getDeviceRealtimeDaily 获取双向站需要的实时数据点，最多24小时
func (b *BidirectionalDeviceService) getDeviceRealtimeDaily(ctx context.Context, cond BidirectionalDeviceCond, dataIdMap map[string]bool) (res []domain_realtime.RealtimeDO, err error) {
	if cond.EndTime-cond.StartTime > 24*3600*1000 {
		return nil, fmt.Errorf("getDeviceRealtimeDaily, the time range is too long: %d", cond.EndTime-cond.StartTime)
	}
	var ossDataId, welkinDataId string
	for id := range dataIdMap {
		if _, ok := cache.OSSRealtimeData[cond.Project][id]; ok {
			ossDataId += id + ","
			continue
		}
		if _, ok := cache.WelkinRealtimeData[cond.Project][id]; ok {
			welkinDataId += id + ","
			continue
		}
	}
	ossDataId = strings.Trim(ossDataId, ",")
	welkinDataId = strings.Trim(welkinDataId, ",")

	// fmt.Println("ossDataId:", ucmd.ToJsonStrIgnoreErr(ossDataId), "welkinDataId:", ucmd.ToJsonStrIgnoreErr(welkinDataId))
	gInner := ucmd.NewErrGroup(ctx)
	var realtimeOss, realtimeWelkin []domain_realtime.RealtimeDO
	if len(ossDataId) != 0 {
		gInner.GoRecover(func() error {
			// 实时数据和状态数据混在一起，可能会有8640*2条数据
			condOss := domain_realtime.ListRealtimeCond{
				CommonCond: model.CommonCond{
					Page: 1,
					Size: 19999,
				},
				Project:   cond.Project,
				DeviceId:  cond.DeviceId,
				DataIds:   ossDataId,
				StartTime: cond.StartTime,
				EndTime:   cond.EndTime,
			}
			var gErr error
			realtimeOss, _, gErr = b.RealtimeDO.ListRealtime(ctx, condOss)
			if gErr != nil {
				log.CtxLog(ctx).Errorf("getDeviceRealtimeDaily, fail to list realtime err: %v, cond: %s", gErr, ucmd.ToJsonStrIgnoreErr(cond))
				return gErr
			}
			return nil
		})
	}
	if len(welkinDataId) != 0 {
		gInner.GoRecover(func() error {
			condWelkin := domain_realtime.ListRealtimeCond{
				CommonCond: model.CommonCond{
					Page: 1,
					Size: 9999,
				},
				Project:   cond.Project,
				DeviceId:  cond.DeviceId,
				DataIds:   welkinDataId,
				StartTime: cond.StartTime,
				EndTime:   cond.EndTime,
			}
			var gErr error
			realtimeWelkin, _, gErr = b.RealtimeDO.ListRealtime(ctx, condWelkin)
			if gErr != nil {
				log.CtxLog(ctx).Errorf("getDeviceRealtimeDaily, fail to list realtime err: %v, cond: %s", gErr, ucmd.ToJsonStrIgnoreErr(cond))
				return gErr
			}
			return nil
		})
	}
	if err = gInner.Wait(); err != nil {
		return
	}

	// fmt.Println("oss len:", len(realtimeOss), "\noss data len:", len(realtimeOss[0].Data), "\nwelkin len:", len(realtimeWelkin), "\nwelkin data len:", len(realtimeWelkin[0].Data), "\ncond:", ucmd.ToJsonStrIgnoreErr(cond))
	// 一整天的所有实时数据
	res = append(realtimeOss, realtimeWelkin...)
	return
}

// getDeviceRealtimeData 查询单个设备实时数据看板
func (b *BidirectionalDeviceService) getDeviceRealtimeData(ctx context.Context, cond BidirectionalDeviceCond) (powerDistribution PowerDistributionVO, energyManagementRevenue EnergyManagementRevenueVO, err error) {
	g := ucmd.NewErrGroup(ctx, 10)
	days := util.SplitTimeByDay(cond.StartTime, cond.EndTime)
	everydayPowerDistributionVOs := make([]PowerDistributionVO, 0)
	everydayEnergyManagementRevenueVOs := make([]EnergyManagementRevenueVO, 0)
	var mu sync.Mutex
	for _, day := range days {
		g.GoRecover(func() error {
			start := time.Now()
			hourlyRealOutputPower := make(map[int]RealtimePoint)
			hourlyModuleUseRaw := make(map[int]map[string]RealtimePoint)        // 小时-模块-工作状态点个数/总点数
			hourlyModuleUse := make(map[int]int)                                // 每小时使用模块数
			hourlyModuleDetail := make(map[int][]string)                        // 每小时具体使用了哪些模块
			modulelyHourlyOutputPower := make(map[string]map[int]RealtimePoint) // 每个模块每小时的功率
			hourlyCharge := make(map[int]float64)                               // 每小时充电量
			hourlyDischarge := make(map[int]float64)                            // 每小时放电量
			cond := BidirectionalDeviceCond{
				Project:   cond.Project,
				DeviceId:  cond.DeviceId,
				StartTime: day.StartTime,
				EndTime:   day.EndTime,
			}
			realtimeList, gErr := b.getDeviceRealtimeDaily(ctx, cond, RealtimeNeeded[cond.Project])
			if gErr != nil {
				log.CtxLog(ctx).Errorf("getDeviceRealtimeData, fail to get device realtime data: %v, cond: %s", gErr, ucmd.ToJsonStrIgnoreErr(cond))
				return gErr
			}
			for _, realtime := range realtimeList {
				ts := int64(util.ParseInt(realtime.Data["timestamp"]))
				if len(fmt.Sprintf("%d", ts)) != 13 {
					continue
				}
				hour := time.UnixMilli(ts).Hour()
				// PCU电表总功率
				for dataId := range RealtimePCUElectricityMeterPower[cond.Project] {
					if val, ok := realtime.Data[dataId]; ok && val != nil {
						tmp := hourlyRealOutputPower[hour]
						valFloat := util.ParseFloat(val)
						if !math.IsNaN(valFloat) {
							tmp.RealtimeSum += valFloat
							tmp.RealtimeCount++
						}
						hourlyRealOutputPower[hour] = tmp
					}
				}
				// 模块工作状态
				for dataId, module := range RealtimeModuleWorkingStatus[cond.Project] {
					if val, ok := realtime.Data[dataId]; ok && val != nil {
						if hourlyModuleUseRaw[hour] == nil {
							hourlyModuleUseRaw[hour] = make(map[string]RealtimePoint)
						}
						tmp := hourlyModuleUseRaw[hour][module]
						if util.ParseInt(val) == 2 {
							tmp.RealtimeSum++
						}
						tmp.RealtimeCount++
						hourlyModuleUseRaw[hour][module] = tmp
					}
				}
				// 模块功率
				for dataId, module := range RealtimeModuleActivePower[cond.Project] {
					if val, ok := realtime.Data[dataId]; ok && val != nil {
						if modulelyHourlyOutputPower[module] == nil {
							modulelyHourlyOutputPower[module] = make(map[int]RealtimePoint)
						}
						tmp := modulelyHourlyOutputPower[module][hour]
						valFloat := util.ParseFloat(val)
						if !math.IsNaN(valFloat) {
							tmp.RealtimeSum += valFloat
							tmp.RealtimeCount++
						}
						modulelyHourlyOutputPower[module][hour] = tmp
					}
				}
				// 模块输出电压&电流 (电压的dataId=电流的dataId-1)
				for currentDataId := range RealtimeModuleOutputCurrent[cond.Project] {
					dataIdInt := util.ParseInt(currentDataId)
					voltageDataId := fmt.Sprintf("%d", dataIdInt-1)
					current, ok := realtime.Data[currentDataId]
					if !ok || current == nil {
						continue
					}
					voltage, ok := realtime.Data[voltageDataId]
					if !ok || voltage == nil {
						continue
					}
					currentVal := util.ParseFloat(current)
					voltageVal := util.ParseFloat(voltage)
					if currentVal > 0 && !math.IsNaN(voltageVal) {
						// 电流为正，表示充电，单位kWh
						hourlyCharge[hour] += currentVal * voltageVal * 10 / 3.6e6
					} else if currentVal < 0 && !math.IsNaN(voltageVal) {
						// 电流为负，表示放电，单位kWh
						hourlyDischarge[hour] += currentVal * voltageVal * 10 / 3.6e6
					}
				}
			}
			for hour, points := range hourlyModuleUseRaw {
				for module, point := range points {
					// 工作状态的数据点超过一半，则该小时视作使用了该模块
					if point.RealtimeSum*2 > point.RealtimeCount {
						hourlyModuleUse[hour]++
						hourlyModuleDetail[hour] = append(hourlyModuleDetail[hour], module)
					}
				}
			}

			// 整合功率分配看板
			var powerDistributionVO PowerDistributionVO
			for hour := range 24 {
				hourlyValue := HourlyValue{
					Hour: hour,
				}
				if hourlyRealOutputPower[hour].RealtimeCount > 0 {
					hourlyValue.Value = hourlyRealOutputPower[hour].RealtimeSum / hourlyRealOutputPower[hour].RealtimeCount
				}
				powerDistributionVO.RealOutputPower = append(powerDistributionVO.RealOutputPower, hourlyValue)
				powerDistributionVO.ModuleUse = append(powerDistributionVO.ModuleUse, ModuleUse{
					HourlyValue: HourlyValue{
						Hour:  hour,
						Value: float64(hourlyModuleUse[hour]),
					},
					Detail: hourlyModuleDetail[hour],
				})
			}
			for module, hourlyData := range modulelyHourlyOutputPower {
				moduleOutputPower := ModuleOutputPower{
					Module: module,
				}
				var total, count float64
				for hour := range 24 {
					point := hourlyData[hour]
					total += point.RealtimeSum
					count += point.RealtimeCount
					hourlyValue := HourlyValue{
						Hour: hour,
					}
					if point.RealtimeCount > 0 {
						hourlyValue.Value = point.RealtimeSum / point.RealtimeCount
					}
					moduleOutputPower.HourlyValue = append(moduleOutputPower.HourlyValue, hourlyValue)
				}
				if count > 0 {
					moduleOutputPower.Value = total / count
				}
				powerDistributionVO.ModuleOutputPower = append(powerDistributionVO.ModuleOutputPower, moduleOutputPower)
			}

			// 整合能源管理收益看板
			var energyManagementRevenueVO EnergyManagementRevenueVO

			electricityPrice, err := b.DeviceDO.GetDeviceElectricityPrice(g.Ctx, domain_device.DeviceElectricityPriceCond{DeviceId: cond.DeviceId, Day: time.UnixMilli(cond.StartTime).Format("2006-01-02")})
			if err != nil {
				log.CtxLog(ctx).Warnf("getDeviceRealtimeData, fail to get device electricity price: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
			}
			for hour, charge := range hourlyCharge {
				energyManagementRevenueVO.ChargeInfo = append(energyManagementRevenueVO.ChargeInfo, ChargeInfo{
					HourlyValue: HourlyValue{
						Hour:  hour,
						Value: charge,
					},
					CostEstimate: charge * electricityPrice.PriceDetail[hour].Price,
				})
			}
			for hour, discharge := range hourlyDischarge {
				energyManagementRevenueVO.DischargeInfo = append(energyManagementRevenueVO.DischargeInfo, ChargeInfo{
					HourlyValue: HourlyValue{
						Hour:  hour,
						Value: discharge,
					},
					CostEstimate: discharge * electricityPrice.PriceDetail[hour].Price,
				})
			}

			mu.Lock()
			defer mu.Unlock()
			everydayPowerDistributionVOs = append(everydayPowerDistributionVOs, powerDistributionVO)
			everydayEnergyManagementRevenueVOs = append(everydayEnergyManagementRevenueVOs, energyManagementRevenueVO)
			log.CtxLog(ctx).Infof("getDeviceRealtimeData, get device realtime data, device_id: %s, day: %s, cost: %v", cond.DeviceId, day.Day, time.Since(start))
			return nil
		})
	}
	if err = g.Wait(); err != nil {
		log.CtxLog(ctx).Errorf("getDeviceRealtimeData, fail to get device realtime data: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
	}
	powerDistribution = convertPowerDistributionVO(everydayPowerDistributionVOs)
	energyManagementRevenue = convertEnergyManagementRevenueVO(everydayEnergyManagementRevenueVOs)
	return
}

// GetBidirectionalInfo 查询双向站看板数据
func (b *BidirectionalDeviceService) GetBidirectionalInfo(ctx context.Context, request BidirectionalDeviceRequest) (res BidirectionalDeviceInfoVO, err error) {
	var deviceDetails []umw.MongoDeviceInfo
	for _, deviceId := range strings.Split(request.DeviceIds, ",") {
		deviceInfo, _ := cache.PowerSwapCache.GetSingleDevice(deviceId)
		if deviceInfo != nil {
			deviceDetails = append(deviceDetails, *deviceInfo)
		}
	}
	g := ucmd.NewErrGroup(ctx)
	var mu sync.Mutex
	// 获取设备基本信息
	g.GoRecover(func() error {
		var basicInfoVOs []BasicInfoVO
		for _, device := range deviceDetails {
			cond := BidirectionalDeviceCond{
				Project:   device.Project,
				DeviceId:  device.DeviceId,
				StartTime: request.EndTime,
				EndTime:   request.EndTime,
			}
			basicInfoVO, gErr := b.getDeviceBasicInfo(g.Ctx, cond)
			if gErr != nil {
				log.CtxLog(ctx).Errorf("GetBidirectionalInfo, fail to get device basic info: %v, device: %s", gErr, ucmd.ToJsonStrIgnoreErr(device))
				return gErr
			}
			basicInfoVOs = append(basicInfoVOs, basicInfoVO)
		}
		mu.Lock()
		defer mu.Unlock()
		res.BasicInfo = basicInfoVOs
		return nil
	})
	// 获取设备可靠性数据
	g.GoRecover(func() error {
		start := time.Now()
		conds := make([]BidirectionalDeviceCond, 0)
		for _, device := range deviceDetails {
			conds = append(conds, BidirectionalDeviceCond{
				Project:   device.Project,
				DeviceId:  device.DeviceId,
				StartTime: request.StartTime,
				EndTime:   request.EndTime,
			})
		}
		reliabilityVO, gErr := b.getDeviceReliability(g.Ctx, conds)
		if gErr != nil {
			log.CtxLog(ctx).Errorf("GetBidirectionalInfo, fail to get device reliability: %v, request: %s", gErr, ucmd.ToJsonStrIgnoreErr(request))
			return gErr
		}
		log.CtxLog(ctx).Infof("GetBidirectionalInfo, get device reliability cost: %v", time.Since(start))
		mu.Lock()
		defer mu.Unlock()
		res.Reliability = reliabilityVO
		return nil
	})
	// 获取实时数据
	g.GoRecover(func() error {
		start := time.Now()
		powerDistributionVOs := make([]PowerDistributionVO, 0)
		energyManagementRevenueVOs := make([]EnergyManagementRevenueVO, 0)
		var deviceMu sync.Mutex
		deviceGroup := ucmd.NewErrGroup(ctx, 3)
		for _, device := range deviceDetails {
			deviceGroup.GoRecover(func() error {
				cond := BidirectionalDeviceCond{
					Project:   device.Project,
					DeviceId:  device.DeviceId,
					StartTime: request.StartTime,
					EndTime:   request.EndTime,
				}
				powerDistributionVO, energyManagementRevenueVO, gErr := b.getDeviceRealtimeData(g.Ctx, cond)
				if gErr != nil {
					log.CtxLog(ctx).Errorf("GetBidirectionalInfo, fail to get device realtime data: %v, cond: %s", gErr, ucmd.ToJsonStrIgnoreErr(cond))
					return gErr
				}
				deviceMu.Lock()
				defer deviceMu.Unlock()
				powerDistributionVOs = append(powerDistributionVOs, powerDistributionVO)
				energyManagementRevenueVOs = append(energyManagementRevenueVOs, energyManagementRevenueVO)
				return nil
			})
		}
		if dErr := deviceGroup.Wait(); dErr != nil {
			log.CtxLog(ctx).Errorf("GetBidirectionalInfo, fail to get device realtime data: %v, request: %s", dErr, ucmd.ToJsonStrIgnoreErr(request))
			return dErr
		}
		log.CtxLog(ctx).Infof("GetBidirectionalInfo, get device realtime data cost: %v", time.Since(start))
		mu.Lock()
		defer mu.Unlock()
		res.PowerDistribution = convertPowerDistributionVO(powerDistributionVOs)
		res.EnergyManagementRevenue = convertEnergyManagementRevenueVO(energyManagementRevenueVOs)
		// 放电电量和收益改成正的
		for i := range res.EnergyManagementRevenue.DischargeInfo {
			res.EnergyManagementRevenue.DischargeInfo[i].Value = math.Abs(res.EnergyManagementRevenue.DischargeInfo[i].Value)
			res.EnergyManagementRevenue.DischargeInfo[i].CostEstimate = math.Abs(res.EnergyManagementRevenue.DischargeInfo[i].CostEstimate)
		}
		return nil
	})
	if err = g.Wait(); err != nil {
		log.CtxLog(ctx).Errorf("GetBidirectionalInfo, fail to get bidirectional info: %v, request: %s", err, ucmd.ToJsonStrIgnoreErr(request))
		return
	}
	return
}

// DownloadBidirectionalInfo 下载双向站看板数据
func (b *BidirectionalDeviceService) DownloadBidirectionalInfo(ctx *gin.Context, request BidirectionalDeviceRequest) (err error) {
	var deviceDetails []umw.MongoDeviceInfo
	for _, deviceId := range strings.Split(request.DeviceIds, ",") {
		deviceInfo, _ := cache.PowerSwapCache.GetSingleDevice(deviceId)
		if deviceInfo != nil {
			deviceDetails = append(deviceDetails, *deviceInfo)
		}
	}

	switch request.Module {
	case ModuleReliability:
		// 下载告警数据
		var allAlarms []BidirectionalAlarm
		var mu sync.Mutex
		g := ucmd.NewErrGroup(ctx, 5)
		for _, deviceInfo := range deviceDetails {
			g.GoRecover(func() error {
				cond := BidirectionalDeviceCond{
					Project:   deviceInfo.Project,
					DeviceId:  deviceInfo.DeviceId,
					StartTime: request.StartTime,
					EndTime:   request.EndTime,
				}
				alarms, _, _, gErr := b.getDeviceFaultData(ctx, cond)
				if gErr != nil {
					log.CtxLog(ctx).Errorf("DownloadBidirectionalInfo, fail to get device fault data: %v, cond: %s", gErr, ucmd.ToJsonStrIgnoreErr(cond))
					return err
				}
				mu.Lock()
				defer mu.Unlock()
				allAlarms = append(allAlarms, alarms...)
				return nil
			})
		}
		if err = g.Wait(); err != nil {
			log.CtxLog(ctx).Errorf("DownloadBidirectionalInfo, fail to get all device fault data: %v, request: %s", err, ucmd.ToJsonStrIgnoreErr(request))
			return
		}
		fileName := fmt.Sprintf("%s_%s_%s_%d.csv", ModuleReliability, util.TsString(request.StartTime), util.TsString(request.EndTime), time.Now().UnixMilli())
		csvRecords := make([][]string, len(allAlarms)+1)
		csvRecords[0] = []string{"故障分类", "设备名称", "设备ID", "设备类型", "告警描述", "告警ID", "告警类型", "告警级别", "告警状态", "告警产生时间", "告警消除时间"}
		for i, r := range allAlarms {
			csvRecords[i+1] = []string{
				AlarmCategoryDesc[r.Category],
				r.DeviceName,
				r.DeviceId,
				r.Project,
				r.DataIdDescription,
				r.DataId,
				model.AlarmTypeMap[r.AlarmType],
				model.AlarmLevelMap[r.AlarmLevel],
				model.AlarmStateMap[r.State],
				util.TsString(r.CreateTs),
				util.TsString(r.ClearTs),
			}
		}
		buffer := &bytes.Buffer{}
		buffer.WriteString("\xEF\xBB\xBF")
		cw := csv.NewWriter(buffer)
		if err = cw.WriteAll(csvRecords); err != nil {
			log.CtxLog(ctx).Errorf("DownloadBidirectionalInfo, fail to write to csv, err: %v", err)
			return
		}
		ctx.Writer.Header().Set("Content-Type", "text/csv")
		util.Download(ctx, fileName, buffer.Bytes())
		return
	case ModulePowerDistribution, ModuleEnergyManagementRevenue:
		// 下载实时数据点
		// Setup the response headers for zip file
		fileName := fmt.Sprintf("%s_%s.zip", request.Module, time.Now().Format("20060102150405"))
		ctx.Writer.Header().Set("Content-Type", "application/zip")
		ctx.Writer.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename*=utf-8''%s", url.QueryEscape(fileName)))
		ctx.Writer.WriteHeader(http.StatusOK)

		// Create zip writer
		zipWriter := zip.NewWriter(ctx.Writer)
		defer func() {
			if err := zipWriter.Close(); err != nil {
				log.CtxLog(ctx).Errorf("DownloadBidirectionalInfo, failed to close zip writer: %v", err)
			}
		}()

		days := util.SplitTimeByDay(request.StartTime, request.EndTime)
		for _, deviceInfo := range deviceDetails {
			var mu sync.Mutex
			g := ucmd.NewErrGroup(ctx, 5)
			dataIdMap := make(map[string]bool)
			if request.Module == ModulePowerDistribution {
				for dataId := range RealtimePCUElectricityMeterPower[deviceInfo.Project] {
					dataIdMap[dataId] = true
				}
				for dataId := range RealtimeModuleWorkingStatus[deviceInfo.Project] {
					dataIdMap[dataId] = true
				}
				for dataId := range RealtimeModuleActivePower[deviceInfo.Project] {
					dataIdMap[dataId] = true
				}
			} else if request.Module == ModuleEnergyManagementRevenue {
				for dataId := range RealtimeModuleOutputCurrent[deviceInfo.Project] {
					dataIdMap[dataId] = true
				}
				for dataId := range RealtimeModuleOutputVoltage[deviceInfo.Project] {
					dataIdMap[dataId] = true
				}
			}
			var dataIds []string
			for dataId := range dataIdMap {
				dataIds = append(dataIds, dataId)
			}
			var realtimeAll []domain_realtime.RealtimeDO
			for _, day := range days {
				g.GoRecover(func() error {
					cond := BidirectionalDeviceCond{
						Project:   deviceInfo.Project,
						DeviceId:  deviceInfo.DeviceId,
						StartTime: day.StartTime,
						EndTime:   day.EndTime,
					}
					realtimeList, gErr := b.getDeviceRealtimeDaily(ctx, cond, dataIdMap)
					if gErr != nil {
						log.CtxLog(ctx).Errorf("DownloadBidirectionalInfo, fail to get device %s data: %v, cond: %s", request.Module, gErr, ucmd.ToJsonStrIgnoreErr(cond))
						return gErr
					}
					mu.Lock()
					defer mu.Unlock()
					realtimeAll = append(realtimeAll, realtimeList...)
					return nil
				})
			}
			if err = g.Wait(); err != nil {
				log.CtxLog(ctx).Errorf("DownloadBidirectionalInfo, fail to get all device %s data: %v, request: %s", request.Module, err, ucmd.ToJsonStrIgnoreErr(request))
				return
			}
			csvRecords, err := b.RealtimeDO.GenerateRealtimeCsv(ctx, deviceInfo.Project, dataIds, realtimeAll)
			if err != nil {
				log.CtxLog(ctx).Errorf("DownloadBidirectionalInfo, fail to generate %s csv records: %v, device: %s", request.Module, err, ucmd.ToJsonStrIgnoreErr(deviceInfo))
				return err
			}
			// fmt.Println("csvRecords len:", len(csvRecords), "device:", ucmd.ToJsonStrIgnoreErr(deviceInfo))
			fileName := fmt.Sprintf("%s_%s_%s_%s_%d.csv", request.Module, deviceInfo.DeviceId, util.TsString(request.StartTime), util.TsString(request.EndTime), time.Now().UnixMilli())

			// Create a buffer for the CSV content
			buffer := &bytes.Buffer{}
			buffer.WriteString("\xEF\xBB\xBF") // UTF-8 BOM
			cw := csv.NewWriter(buffer)
			if err = cw.WriteAll(csvRecords); err != nil {
				log.CtxLog(ctx).Errorf("DownloadBidirectionalInfo, fail to write to csv, err: %v", err)
				return err
			}
			cw.Flush()

			// Create a new file in the zip archive
			header := &zip.FileHeader{
				Name:     fileName,
				Method:   zip.Deflate,
				Modified: time.Now(),
			}
			fileWriter, err := zipWriter.CreateHeader(header)
			if err != nil {
				log.CtxLog(ctx).Errorf("DownloadBidirectionalInfo, failed to create file in zip: %v", err)
				return err
			}

			// Write the CSV content to the file in the zip archive
			if _, err := fileWriter.Write(buffer.Bytes()); err != nil {
				log.CtxLog(ctx).Errorf("DownloadBidirectionalInfo, failed to write CSV content to zip file: %v", err)
				return err
			}
		}
		return
	default:
		err = fmt.Errorf("DownloadBidirectionalInfo, invalid module: %s", request.Module)
		return
	}
}
