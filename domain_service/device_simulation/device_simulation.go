package device_simulation

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/avast/retry-go"
	"github.com/rs/xid"
	"go.mongodb.org/mongo-driver/bson"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	"git.nevint.com/welkin2/welkin-backend/domain/psos"
	"git.nevint.com/welkin2/welkin-backend/domain/psos_scheduler"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"
	"git.nevint.com/welkin2/welkin-backend/util"
)

type DeviceSimulationService struct {
	ConfigDO    *psos.ConfigDO
	SchedulerDO *psos_scheduler.PsosSchedulerDO
}

func (d *DeviceSimulationService) startSimulation(ctx context.Context, cfgReq psos.PsosBatchConfigRequest, chargeStrategy string) {
	g := ucmd.NewErrGroup(ctx, 1)
	for _, deviceId := range cfgReq.Devices {
		g.GoRecover(func() error {
			beginTime := time.Now()
			retryErr := retry.Do(func() error {
				configVO := psos.PsosSingleConfigRequest{
					CommonPsosConfig: cfgReq.CommonPsosConfig,
					DeviceId:         deviceId,
				}
				configDO := psos.ConvertSingleConfigVO2DO(ctx, configVO, "")
				configPOMap, err := configDO.GetDeviceConfig(ctx)
				if err != nil {
					log.CtxLog(ctx).Errorf("startSimulation, fail to get device config: %v, request: %s", err, ucmd.ToJsonStrIgnoreErr(configVO))
					return err
				}
				configPO := configPOMap[deviceId]
				configPO = configDO.PrepareUserParams(ctx, configPO, "auto")
				configPO = configDO.PrepareAutoParams(ctx, configPO)
				err = executeSimulation(ctx, configPO, chargeStrategy)
				if err != nil && strings.HasPrefix(err.Error(), "skip") {
					// psos算法返回err_code不为0时，不重试
					log.CtxLog(ctx).Errorf("startSimulation, psos service error, project: %s, device_id: %s, charge_strategy: %s, err: %v", configPO.Project, configPO.DeviceInfo.DeviceId, chargeStrategy, err)
					return nil
				}
				return err
			}, []retry.Option{
				retry.Delay(time.Second * 5),
				retry.Attempts(6),
				retry.LastErrorOnly(true),
			}...)
			if retryErr != nil {
				log.CtxLog(ctx).Errorf("startSimulation, fail to execute simulation, err: %v", retryErr)
				return retryErr
			}
			log.CtxLog(ctx).Infof("startSimulation, finish simulation, project: %s, device_id: %s, charge_strategy: %s, time use: %v", cfgReq.Project, deviceId, chargeStrategy, time.Now().Sub(beginTime))
			return nil
		})
	}
	g.Wait()
	log.CtxLog(ctx).Infof("startSimulation, finish to run all simulations, project: %s, chargeStrategy: %s", cfgReq.Project, chargeStrategy)
	return
}

func executeSimulation(ctx context.Context, cfg mmgo.MongoPsosConfig, chargeStrategy string) (err error) {
	// 传给算法的时间戳用秒 🤷🏻‍
	cfg.SimulationInfo.SimulationStartTime = cfg.SimulationInfo.SimulationStartTime / 1000
	cfg.SimulationInfo.SimulationPeriod = cfg.SimulationInfo.SimulationPeriod / 1000
	for i, _ := range cfg.ServiceInfo.SwappingUserList {
		cfg.ServiceInfo.SwappingUserList[i].UserArrivalTime = cfg.ServiceInfo.SwappingUserList[i].UserArrivalTime / 1000
	}
	// 忽略电池仓内不支持的电池
	for i, record := range cfg.DeviceInfo.OperationStrategyInfo.BatteryInfo {
		if record.BatteryRatedKwh != nil && !ValidBatteryRatedKwh[*record.BatteryRatedKwh] {
			cfg.DeviceInfo.OperationStrategyInfo.BatteryInfo[i] = mmgo.PsosBatteryInfo{
				SlotId: record.SlotId,
			}
		}
	}
	simulationConfig := psos_scheduler.SimulationConfig{
		TaskId:         fmt.Sprintf("%s_%s", PsosAutoTask, xid.New().String()),
		SimulationId:   fmt.Sprintf("%s_%s", PsosAutoSimulation, xid.New().String()),
		Ignore:         true,
		DeviceInfo:     cfg.DeviceInfo,
		ServiceInfo:    cfg.ServiceInfo,
		ScenarioInfo:   cfg.ScenarioInfo,
		SimulationInfo: cfg.SimulationInfo,
	}
	jsonData, err := json.Marshal(simulationConfig)
	if err != nil {
		return
	}
	resp, err := http.Post(fmt.Sprintf("%v/psos", config.Cfg.Welkin.PsosUrl), "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("http request psos fail, err: %v", err)
	}
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("http request psos fail status code:%v", resp.StatusCode)
	}
	defer resp.Body.Close()
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("io.ReadAll, err: %v", err)
	}
	var psosResp map[string]interface{}
	if err = json.Unmarshal(data, &psosResp); err != nil {
		return fmt.Errorf("skip, fail to unmarshal psos response, err: %v, body: %s", err, string(data))
	}
	errCode := util.ParseInt(psosResp["err_code"])
	if errCode != 0 {
		return fmt.Errorf("skip, resp err code not equal 0 message:%v request data:%s", ucmd.ToJsonStrIgnoreErr(psosResp), ucmd.ToJsonStrIgnoreErr(simulationConfig))
	}
	day := time.Unix(cfg.SimulationInfo.SimulationStartTime, 0).Format("20060102")
	record := mmgo.PsosResult{
		ChargeStrategy:  chargeStrategy,
		Day:             day,
		DeviceId:        cfg.DeviceInfo.DeviceId,
		Project:         cfg.Project,
		DeviceInfo:      ucmd.ToJsonStrIgnoreErr(cfg.DeviceInfo),
		ServiceInfo:     ucmd.ToJsonStrIgnoreErr(cfg.ServiceInfo),
		ScenarioInfo:    ucmd.ToJsonStrIgnoreErr(cfg.ScenarioInfo),
		SimulationInfo:  ucmd.ToJsonStrIgnoreErr(cfg.SimulationInfo),
		PsosModelOutput: util.Flatten(psosResp),
		Date:            time.Now(),
	}
	err = client.GetWatcher().Mongodb().NewMongoEntry(bson.D{{"device_id", cfg.DeviceInfo.DeviceId}, {"day", day}, {"charge_strategy", chargeStrategy}}).UpdateOne(umw.Algorithm, psos.CollectionResult, bson.M{"$set": record}, true)
	return
}

var stgDeviceMap = map[string][]string{
	umw.PowerSwap2: {"PS-NIO-31da205c-64ba4b36"},
	umw.PUS3:       {"PS-NIO-3285ff15-7f564f27", "PS-NIO-32197d7a-05ab2468"},
	umw.PUS4:       {"PUS-NIO-095fdc2a-5840a9b6", "PUS-NIO-8f804946-f7d028c2"},
}

// StartSimulationAllDevices 全量设备仿真
func (d *DeviceSimulationService) StartSimulationAllDevices(ctx context.Context, cond SimulationAllDevicesCond) {
	var deviceInfo []umw.MongoDeviceInfo
	filter := bson.D{
		{"project", cond.Project},
		{"is_active", true},
	}
	if len(cond.ExcludeDevices) > 0 {
		filter = append(filter, bson.E{Key: "device_id", Value: bson.D{{"$nin", cond.ExcludeDevices}}})
	}
	if len(cond.IncludeDevices) > 0 {
		filter = append(filter, bson.E{Key: "device_id", Value: bson.D{{"$in", cond.IncludeDevices}}})
	}
	_, err := client.GetWatcher().Mongodb().NewMongoEntry(filter).FindMany(umw.OAuthDB, umw.DeviceBaseInfo, nil, &deviceInfo)
	if err != nil {
		log.CtxLog(ctx).Errorf("StartSimulationAllDevices, fail to find devices, err: %v, filter: %s", err, ucmd.ToJsonStrIgnoreErr(filter))
		return
	}
	devices := make([]string, 0)
	for _, record := range deviceInfo {
		devices = append(devices, record.DeviceId)
	}
	if ucmd.GetEnv() != "prod" {
		devices = stgDeviceMap[cond.Project]
	}
	// 避免测试时产生太多仿真
	if cond.Limit != nil {
		limit := *cond.Limit
		if limit > len(devices) {
			limit = len(devices)
		}
		devices = devices[:limit]
	}
	batteryExchangeSwitch := 1
	if cond.Project == umw.PowerSwap2 {
		batteryExchangeSwitch = 0
	}
	operationStartHour := 0
	operationEndHour := 24
	silentModeSwitch := 0
	cfgReq := psos.PsosBatchConfigRequest{
		Devices: devices,
		CommonPsosConfig: psos.CommonPsosConfig{
			Project:            cond.Project,
			StartTs:            cond.StartTs,
			EndTs:              cond.EndTs,
			CmsSwitch:          cond.CmsSwitch,
			OperationStartHour: &operationStartHour,
			OperationEndHour:   &operationEndHour,
			NotfullySwapSwitch: &psos.NotfullySwapSwitch{
				SwitchValue:   1,
				SocLowerLimit: psos.BatterySocLowerLimit[cond.Project],
				SocUpperLimit: psos.BatterySocUpperLimit[cond.Project],
			},
			SilentModeSwitch:      &silentModeSwitch,
			BatteryExchangeSwitch: &batteryExchangeSwitch,
		},
	}
	if cond.BatteryRestSwitch != nil {
		cfgReq.BatteryRestSwitch = &psos.BatteryRestSwitch{
			SwitchValue:              *cond.BatteryRestSwitch,
			DefaultRestCurrent:       0,
			DefaultHangingDuration:   0,
			DefaultHangingStep:       10,
			DefaultHangingCurrentMax: 50,
		}
	}
	d.startSimulation(ctx, cfgReq, cond.ChargeStrategy)
	return
}
