package device_simulation

const (
	ChargeStrategyNonStra   = "non_stra"
	ChargeStrategyOnlyEps   = "only_eps"
	ChargeStrategyEpsSilent = "eps_silent"

	PsosAutoTask       = "auto-task"
	PsosAutoSimulation = "auto-sim"
)

var ValidBatteryRatedKwh = map[int]bool{
	50:  true,
	70:  true,
	75:  true,
	100: true,
	150: true,
	60:  true,
	85:  true,
}

type SimulationAllDevicesCond struct {
	Limit             *int     // 测试用，限制仿真数量
	ExcludeDevices    []string // 补数据用，排除站点
	IncludeDevices    []string // 补数据用，包含站点
	Project           string
	ChargeStrategy    string
	StartTs           int64
	EndTs             int64
	CmsSwitch         *int
	BatteryRestSwitch *int
}
