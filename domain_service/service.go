package domain_service

import (
	"git.nevint.com/welkin2/welkin-backend/domain/alarm"
	domain_device "git.nevint.com/welkin2/welkin-backend/domain/device"
	"git.nevint.com/welkin2/welkin-backend/domain/energy"
	"git.nevint.com/welkin2/welkin-backend/domain/event"
	"git.nevint.com/welkin2/welkin-backend/domain/order"
	"git.nevint.com/welkin2/welkin-backend/domain/psos"
	"git.nevint.com/welkin2/welkin-backend/domain/psos_scheduler"
	"git.nevint.com/welkin2/welkin-backend/domain/realtime"
	"git.nevint.com/welkin2/welkin-backend/domain/revenue"
	"git.nevint.com/welkin2/welkin-backend/domain/service"
	"git.nevint.com/welkin2/welkin-backend/domain/service_event"
	"git.nevint.com/welkin2/welkin-backend/domain_service/bidirectional_device"
	"git.nevint.com/welkin2/welkin-backend/domain_service/device_model"
	"git.nevint.com/welkin2/welkin-backend/domain_service/device_simulation"
	"git.nevint.com/welkin2/welkin-backend/domain_service/service_visual"
)

var serviceVisual *service_visual.ServiceVisualService

func GetServiceVisualService() *service_visual.ServiceVisualService {
	if serviceVisual == nil {
		serviceVisual = &service_visual.ServiceVisualService{
			OrderDO:          &order.OrderDO{},
			ServiceDO:        &service.ServiceDO{},
			ServiceEventDO:   &service_event.ServiceEventDO{},
			EventDO:          &event.EventDO{},
			AlarmDO:          &alarm.AlarmDO{},
			SatisfyServiceDO: &service.Service{},
			SatisfyDO:        &service.Satisfy{},
			RealtimeDO:       &realtime.RealtimeDO{},
		}
	}
	return serviceVisual
}

var deviceModel *device_model.DeviceModelService

func GetDeviceModelService() *device_model.DeviceModelService {
	if deviceModel == nil {
		deviceModel = &device_model.DeviceModelService{
			ServiceDO: &service.ServiceDO{},
			DeviceDO:  &domain_device.Device{},
			EnergyDO:  &energy.EnergyDO{},
			RevenueDO: &revenue.RevenueDO{},
		}
	}
	return deviceModel
}

var deviceSimulation *device_simulation.DeviceSimulationService

func GetDeviceSimulation() *device_simulation.DeviceSimulationService {
	if deviceSimulation == nil {
		deviceSimulation = &device_simulation.DeviceSimulationService{
			ConfigDO:    &psos.ConfigDO{},
			SchedulerDO: &psos_scheduler.PsosSchedulerDO{},
		}
	}
	return deviceSimulation
}

var bidirectionalDevice *bidirectional_device.BidirectionalDeviceService

func GetBidirectionalDeviceService() *bidirectional_device.BidirectionalDeviceService {
	bidirectional_device.InitOnce()
	if bidirectionalDevice == nil {
		bidirectionalDevice = &bidirectional_device.BidirectionalDeviceService{
			DeviceDO:   &domain_device.Device{},
			AlarmDO:    &alarm.AlarmDO{},
			RealtimeDO: &realtime.RealtimeDO{},
		}
	}
	return bidirectionalDevice
}
