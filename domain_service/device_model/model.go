package device_model

import "git.nevint.com/welkin2/welkin-backend/model"

type ProjectValue struct {
	Project string   `json:"project"`
	Value   *float64 `json:"value"`
}

type DeviceModelDetail struct {
	Value  *float64       `json:"value"`
	Detail []ProjectValue `json:"detail"`
}

type ProjectInfo struct {
	Project         string `json:"project"`
	Total           int64  `json:"total"`
	DailyOrderCount int64  `json:"daily_order_count"`
}

type DeviceModelProjectDetail struct {
	Value  int64         `json:"value"`
	Detail []ProjectInfo `json:"detail"`
}

type DeviceModelOverviewVO struct {
	TotalSwapTimes    int64                    `json:"total_swap_times"`
	EnergyUpdateTime  int64                    `json:"energy_update_time"`
	RevenueUpdateTime int64                    `json:"revenue_update_time"`
	Device            DeviceModelProjectDetail `json:"device"`
	Profit            DeviceModelDetail        `json:"profit"`
	Health            DeviceModelDetail        `json:"health"`
	Energy            DeviceModelDetail        `json:"energy"`
}

type EnergyDistribution struct {
	EnergyRange string `json:"energy_range"`
	DeviceTotal int    `json:"device_total"`
}

type DailyEnergy struct {
	Day        int64   `json:"day"`
	Energy     float64 `json:"energy"`
	OrderCount int     `json:"order_count"`
}

type TailDeviceEnergy struct {
	Id          int     `json:"id"`
	DeviceId    string  `json:"device_id"`
	Description string  `json:"description"`
	Energy      float64 `json:"energy"`
}

type EnergyOverviewVO struct {
	Energy             DeviceModelDetail    `json:"energy"`
	YtdEnergy          DeviceModelDetail    `json:"ytd_energy"`
	EnergyDistribution []EnergyDistribution `json:"energy_distribution"`
	DailyEnergy        []DailyEnergy        `json:"daily_energy"`
	TailDevices        []TailDeviceEnergy   `json:"tail_devices"`
}

type EnergyDetailVO struct {
	DeviceId                string   `json:"device_id"`
	Description             string   `json:"description"`
	Project                 string   `json:"project"`
	Day                     int64    `json:"day"`
	Energy                  *float64 `json:"energy"`
	TotalConsumption        *float64 `json:"total_consumption"`
	ChargeConsumption       *float64 `json:"charge_consumption"`
	WaterConsumption        *float64 `json:"water_consumption"`
	OperationConsumption    *float64 `json:"operation_consumption"`
	MechanicalConsumption   *float64 `json:"mechanical_consumption"`
	LightConsumption        *float64 `json:"light_consumption"`
	UpsConsumption          *float64 `json:"ups_consumption"`
	CoolingConsumption      *float64 `json:"cooling_consumption"`
	NonChargeConsumption    *float64 `json:"non_charge_consumption"`
	ModuleOutputEnergyTotal *float64 `json:"module_output_energy_total"`
	ChargingEnergy          *float64 `json:"charging_energy"`
	ChargingEnergyAvailable *float64 `json:"charging_energy_available"`
	ChargingPileEnergy      *float64 `json:"charging_pile_energy"`
}

type RevenueInfo struct {
	Revenue                   *float64 `json:"revenue"`
	Progress                  *float64 `json:"progress"`
	OffPeakRevenue            *float64 `json:"off_peak_revenue"`
	EnergyRevenue             *float64 `json:"energy_revenue"`
	BatteryMaintenanceRevenue *float64 `json:"battery_maintenance_revenue"`
	DeviceDailyRevenue        *float64 `json:"device_daily_revenue"`
	UpdateDay                 int64    `json:"update_day"`
}

type AnnualizedRevenueInfo struct {
	RevenueInfo
	ExpectedAnnualizedRevenue *float64 `json:"expected_annualized_revenue"`
}

type DailyRevenue struct {
	Day                       int64   `json:"day"`
	OffPeakRevenue            float64 `json:"off_peak_revenue"`
	EnergyRevenue             float64 `json:"energy_revenue"`
	BatteryMaintenanceRevenue float64 `json:"battery_maintenance_revenue"`
}

type TailDeviceRevenue struct {
	Id          int     `json:"id"`
	DeviceId    string  `json:"device_id"`
	Description string  `json:"description"`
	Revenue     float64 `json:"revenue"`
}

type RevenueOverviewVO struct {
	AnnualizedRevenue AnnualizedRevenueInfo `json:"annualized_revenue"`
	Revenue           RevenueInfo           `json:"revenue"`
	RevenueTrend      []DailyRevenue        `json:"revenue_trend"`
	TailDevices       []TailDeviceRevenue   `json:"tail_devices"`
}

type RevenueDetailVO struct {
	DeviceId                  string   `json:"device_id"`
	Description               string   `json:"description"`
	Project                   string   `json:"project"`
	Day                       int64    `json:"day"`
	MaxRevenueRate            *float64 `json:"max_revenue_rate"`
	TotalRevenue              *float64 `json:"total_revenue"`
	OffPeakRevenue            *float64 `json:"off_peak_revenue"`
	EnergyRevenue             *float64 `json:"energy_revenue"`
	BatteryMaintenanceRevenue *float64 `json:"battery_maintenance_revenue"`
	BatteryMaintenanceTimes   *float64 `json:"battery_maintenance_times"`
}

type DeviceModelRequest struct {
	model.CommonUriInTimeRangeParam
	Project     string `form:"project"`
	CityCompany string `form:"city_company"`
	DeviceId    string `form:"device_id"`
	Download    bool   `form:"download"`
}

type SingleDeviceRequest struct {
	model.CommonUriInTimeRangeParam
	Day int64 `form:"day"`
}

type DailyOffPeakRevenue struct {
	Day                    int64    `json:"day"`
	ModuleDamageLoss       *float64 `json:"module_damage_loss"`
	BatteryMaintenanceLoss *float64 `json:"battery_maintenance_loss"`
	BatteryRestLoss        *float64 `json:"battery_rest_loss"`
	OffPeakRevenue         *float64 `json:"off_peak_revenue"`
}

type DailyBatteryMaintenanceRevenue struct {
	Day                          int64    `json:"day"`
	BatteryMaintenanceTimes      *float64 `json:"battery_maintenance_times"`
	BatteryMaintenancePercentage *float64 `json:"battery_maintenance_percentage"`
}

type DailyEnergyRevenue struct {
	Day              int64    `json:"day"`
	ChargeRevenue    *float64 `json:"charge_revenue"`
	WaterRevenue     *float64 `json:"water_revenue"`
	OperationRevenue *float64 `json:"operation_revenue"`
}

type SingleDeviceRevenueVO struct {
	TotalRevenue                    *float64                         `json:"total_revenue"`
	OffPeakRevenue                  *float64                         `json:"off_peak_revenue"`
	OffPeakRevenueChange            *float64                         `json:"off_peak_revenue_change"`
	EnergyRevenue                   *float64                         `json:"energy_revenue"`
	EnergyRevenueChange             *float64                         `json:"energy_revenue_change"`
	BatteryMaintenanceRevenue       *float64                         `json:"battery_maintenance_revenue"`
	BatteryMaintenanceRevenueChange *float64                         `json:"battery_maintenance_revenue_change"`
	Suggestion                      string                           `json:"suggestion"`
	RevenueTrend                    []DailyRevenue                   `json:"revenue_trend"`
	OffPeakRevenueTrend             []DailyOffPeakRevenue            `json:"off_peak_revenue_trend"`
	BatteryMaintenanceRevenueTrend  []DailyBatteryMaintenanceRevenue `json:"battery_maintenance_revenue_trend"`
	EnergyRevenueTrend              []DailyEnergyRevenue             `json:"energy_revenue_trend"`
}

type SingleDeviceEnergyVO struct {
	Energy                     *float64      `json:"energy"`
	TotalConsumption           *float64      `json:"total_consumption"`
	ChargeConsumption          *float64      `json:"charge_consumption"`
	ChargeConsumptionChange    *float64      `json:"charge_consumption_change"`
	WaterConsumption           *float64      `json:"water_consumption"`
	OperationConsumption       *float64      `json:"operation_consumption"`
	MechanicalConsumption      *float64      `json:"mechanical_consumption"`
	LightConsumption           *float64      `json:"light_consumption"`
	UpsConsumption             *float64      `json:"ups_consumption"`
	CoolingConsumption         *float64      `json:"cooling_consumption"`
	NonChargeConsumption       *float64      `json:"non_charge_consumption"`
	NonChargeConsumptionChange *float64      `json:"non_charge_consumption_change"`
	ModuleOutputEnergyTotal    *float64      `json:"module_output_energy_total"`
	ChargingEnergy             *float64      `json:"charging_energy"`
	ChargingEnergyAvailable    *float64      `json:"charging_energy_available"`
	ChargingPileEnergy         *float64      `json:"charging_pile_energy"`
	Suggestion                 string        `json:"suggestion"`
	DailyEnergy                []DailyEnergy `json:"daily_energy"`
}
