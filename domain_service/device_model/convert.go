package device_model

import (
	"sort"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/domain/energy"
	"git.nevint.com/welkin2/welkin-backend/domain/health"
	"git.nevint.com/welkin2/welkin-backend/domain/revenue"
	"git.nevint.com/welkin2/welkin-backend/util"
)

func convertDeviceServiceVO(days float64, serviceCount map[string]int64, deviceCount map[string]int64) DeviceModelProjectDetail {
	vo := DeviceModelProjectDetail{}
	for _, project := range deviceModelProjects {
		projectInfo := ProjectInfo{
			Project:         project,
			Total:           deviceCount[project],
			DailyOrderCount: int64(float64(serviceCount[project]) / days),
		}
		vo.Detail = append(vo.Detail, projectInfo)
		vo.Value += deviceCount[project]
	}
	return vo
}

func convertHealthScore2VO(project string, do health.HealthScores) ProjectValue {
	vo := ProjectValue{
		Project: project,
		Value:   util.RoundFloatPtr(&do.HealthScore, 2),
	}
	return vo
}

func convertEnergyDO2VO(do energy.EnergyDO) EnergyDetailVO {
	vo := EnergyDetailVO{
		DeviceId:                do.DeviceId,
		Project:                 do.Project,
		Day:                     do.Day,
		TotalConsumption:        util.RoundFloatPtr(do.TotalConsumption, 2),
		ChargeConsumption:       util.RoundFloatPtr(do.ChargeConsumption, 2),
		WaterConsumption:        util.RoundFloatPtr(do.WaterConsumption, 2),
		OperationConsumption:    util.RoundFloatPtr(do.OperationConsumption, 2),
		MechanicalConsumption:   util.RoundFloatPtr(do.MechanicalConsumption, 2),
		LightConsumption:        util.RoundFloatPtr(do.LightConsumption, 2),
		UpsConsumption:          util.RoundFloatPtr(do.UpsConsumption, 2),
		CoolingConsumption:      util.RoundFloatPtr(do.CoolingConsumption, 2),
		NonChargeConsumption:    util.RoundFloatPtr(do.NonChargeConsumption, 2),
		ModuleOutputEnergyTotal: util.RoundFloatPtr(do.ModuleOutputEnergyTotal, 2),
		ChargingEnergy:          util.RoundFloatPtr(do.ChargingEnergy, 2),
		ChargingEnergyAvailable: util.RoundFloatPtr(do.ChargingEnergyAvailable, 2),
		ChargingPileEnergy:      util.RoundFloatPtr(do.ChargingPileEnergy, 2),
	}
	if do.Efficiency != nil && *do.Efficiency > 0 {
		efficiencyPercentage := util.RoundFloat(*do.Efficiency*100, 2)
		vo.Energy = &efficiencyPercentage
	}
	deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(do.DeviceId)
	if found {
		vo.Description = deviceInfo.Description
	}
	return vo
}

func convertRevenueDO2VO(do revenue.RevenueDO) RevenueDetailVO {
	vo := RevenueDetailVO{
		DeviceId:                  do.DeviceId,
		Project:                   do.Project,
		Day:                       do.Day,
		MaxRevenueRate:            util.RoundFloatPtr(do.MaxRevenueRate, 2),
		TotalRevenue:              util.RoundFloatPtr(do.TotalRevenue, 2),
		OffPeakRevenue:            util.RoundFloatPtr(do.OffPeakRevenue, 2),
		EnergyRevenue:             util.RoundFloatPtr(do.EnergyRevenue, 2),
		BatteryMaintenanceRevenue: util.RoundFloatPtr(do.BatteryMaintenanceRevenue, 2),
		BatteryMaintenanceTimes:   util.RoundFloatPtr(do.BatteryMaintenanceTimes, 2),
	}
	if do.MaxRevenueRate != nil {
		maxRevenueRatePercentage := util.RoundFloat(*do.MaxRevenueRate*100, 2)
		vo.MaxRevenueRate = &maxRevenueRatePercentage
	}
	deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(do.DeviceId)
	if found {
		vo.Description = deviceInfo.Description
	}
	return vo
}

func convertEnergyOverview2DeviceModelDetail(do []energy.EnergyOverview) DeviceModelDetail {
	energyOverviewMap := make(map[string]energy.EnergyOverview)
	for _, record := range do {
		if record.Project == nil {
			continue
		}
		energyOverviewMap[*record.Project] = record
	}
	res := DeviceModelDetail{}
	totalConsumption := 0.0
	totalCharge := 0.0
	for _, project := range deviceModelProjects {
		energyOverview, found := energyOverviewMap[project]
		if !found {
			res.Detail = append(res.Detail, ProjectValue{
				Project: project,
			})
			continue
		}
		totalConsumption += energyOverview.TotalConsumption
		totalCharge += energyOverview.TotalCharge
		efficiency := energyOverview.Efficiency * 100
		res.Detail = append(res.Detail, ProjectValue{
			Project: project,
			Value:   util.RoundFloatPtr(&efficiency, 2),
		})
	}
	if totalConsumption == 0 {
		return res
	}
	efficiency := totalCharge / totalConsumption * 100
	res.Value = util.RoundFloatPtr(&efficiency, 2)
	return res
}

func convertRevenueOverview2DeviceModelDetail(do []revenue.RevenueOverview) DeviceModelDetail {
	revenueOverviewMap := make(map[string]revenue.RevenueOverview)
	for _, record := range do {
		if record.Project == nil {
			continue
		}
		revenueOverviewMap[*record.Project] = record
	}
	res := DeviceModelDetail{}
	totalRevenue := 0.0
	count := 0
	for _, project := range deviceModelProjects {
		revenueOverview, found := revenueOverviewMap[project]
		if !found {
			res.Detail = append(res.Detail, ProjectValue{
				Project: project,
			})
			continue
		}
		totalRevenue += revenueOverview.TotalRevenue
		count += revenueOverview.Count
		dailyRevenue := revenueOverview.TotalRevenue / float64(revenueOverview.Count)
		res.Detail = append(res.Detail, ProjectValue{
			Project: project,
			Value:   util.RoundFloatPtr(&dailyRevenue, 2),
		})
	}
	if count == 0 {
		return res
	}
	dailyRevenue := totalRevenue / float64(count)
	res.Value = util.RoundFloatPtr(&dailyRevenue, 2)
	return res
}

func convertEnergyOverview2TailDevice(do []energy.EnergyOverview) []TailDeviceEnergy {
	var res []TailDeviceEnergy
	for i, record := range do {
		if record.DeviceId == nil {
			continue
		}
		item := TailDeviceEnergy{
			Id:       i + 1,
			DeviceId: *record.DeviceId,
			Energy:   util.RoundFloat(record.Efficiency*100, 2),
		}
		deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(*record.DeviceId)
		if found {
			item.Description = deviceInfo.Description
		}
		res = append(res, item)
	}
	return res
}

func convertEnergyOverview2EnergyDistribution(do []energy.EnergyOverview) []EnergyDistribution {
	energyDistributionMap := make(map[string]int)
	for _, record := range do {
		if record.Efficiency > 0.9 {
			energyDistributionMap["(90%,+\u221E)"]++
		} else if record.Efficiency > 0.88 {
			energyDistributionMap["(88%,90%]"]++
		} else if record.Efficiency > 0.85 {
			energyDistributionMap["(85%,88%]"]++
		} else if record.Efficiency > 0.8 {
			energyDistributionMap["(80%,85%]"]++
		} else if record.Efficiency > 0.7 {
			energyDistributionMap["(70%,80%]"]++
		} else if record.Efficiency > 0.6 {
			energyDistributionMap["(60%,70%]"]++
		} else {
			energyDistributionMap["(-\u221E,60%]"]++
		}
	}
	var res []EnergyDistribution
	for energyRange, count := range energyDistributionMap {
		res = append(res, EnergyDistribution{
			EnergyRange: energyRange,
			DeviceTotal: count,
		})
	}
	sort.Slice(res, func(i, j int) bool {
		return res[i].EnergyRange < res[j].EnergyRange
	})
	return res
}

func convertEnergyOverview2DailyEnergy(do []energy.EnergyOverview) []DailyEnergy {
	var res []DailyEnergy
	for _, record := range do {
		res = append(res, DailyEnergy{
			Day:        *record.Day,
			Energy:     util.RoundFloat(record.Efficiency*100, 2),
			OrderCount: record.ServiceCount,
		})
	}
	return res
}

func convertRevenueOverview2TailDevice(do []revenue.RevenueOverview) []TailDeviceRevenue {
	var res []TailDeviceRevenue
	for i, record := range do {
		if record.DeviceId == nil {
			continue
		}
		item := TailDeviceRevenue{
			Id:       i + 1,
			DeviceId: *record.DeviceId,
			Revenue:  util.RoundFloat(record.TotalRevenue, 2),
		}
		deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(*record.DeviceId)
		if found {
			item.Description = deviceInfo.Description
		}
		res = append(res, item)
	}
	return res
}

func convertRevenueOverview2AnnualizedRevenueInfo(do []revenue.RevenueOverview, updateTime int64, validDays int) AnnualizedRevenueInfo {
	var res AnnualizedRevenueInfo
	if len(do) == 0 {
		return res
	}
	res.UpdateDay = updateTime
	count := 0
	totalRevenue := 0.0
	offPeakRevenue := 0.0
	energyRevenue := 0.0
	batteryMaintenanceRevenue := 0.0
	for _, record := range do {
		totalRevenue += record.TotalRevenue
		offPeakRevenue += record.OffPeakRevenue
		energyRevenue += record.EnergyRevenue
		batteryMaintenanceRevenue += record.BatteryMaintenanceRevenue
		count += record.Count
	}
	if count != 0 {
		deviceDailyRevenue := totalRevenue / float64(count)
		res.DeviceDailyRevenue = util.RoundFloatPtr(&deviceDailyRevenue, 2)
	}
	if validDays != 0 {
		expectedAnnualizedRevenue := totalRevenue / float64(validDays) * 365
		res.ExpectedAnnualizedRevenue = util.RoundFloatPtr(&expectedAnnualizedRevenue, 2)
	}
	progress := totalRevenue / getTargetYearlyRevenue() * 100
	res.Progress = util.RoundFloatPtr(&progress, 2)
	res.Revenue = util.RoundFloatPtr(&totalRevenue, 2)
	res.OffPeakRevenue = util.RoundFloatPtr(&offPeakRevenue, 2)
	res.EnergyRevenue = util.RoundFloatPtr(&energyRevenue, 2)
	res.BatteryMaintenanceRevenue = util.RoundFloatPtr(&batteryMaintenanceRevenue, 2)
	return res
}

func convertRevenueOverview2RevenueInfo(do []revenue.RevenueOverview, updateTime int64, validDays int) RevenueInfo {
	var res RevenueInfo
	if len(do) == 0 {
		return res
	}
	res.UpdateDay = updateTime
	count := 0
	totalRevenue := 0.0
	offPeakRevenue := 0.0
	energyRevenue := 0.0
	batteryMaintenanceRevenue := 0.0
	for _, record := range do {
		totalRevenue += record.TotalRevenue
		offPeakRevenue += record.OffPeakRevenue
		energyRevenue += record.EnergyRevenue
		batteryMaintenanceRevenue += record.BatteryMaintenanceRevenue
		count += record.Count
	}
	if count != 0 {
		deviceDailyRevenue := totalRevenue / float64(count)
		res.DeviceDailyRevenue = util.RoundFloatPtr(&deviceDailyRevenue, 2)
	}
	if validDays != 0 {
		progress := totalRevenue / (getTargetYearlyRevenue() / 365 * float64(validDays)) * 100
		res.Progress = util.RoundFloatPtr(&progress, 2)
	}
	res.Revenue = util.RoundFloatPtr(&totalRevenue, 2)
	res.OffPeakRevenue = util.RoundFloatPtr(&offPeakRevenue, 2)
	res.EnergyRevenue = util.RoundFloatPtr(&energyRevenue, 2)
	res.BatteryMaintenanceRevenue = util.RoundFloatPtr(&batteryMaintenanceRevenue, 2)
	return res
}

func convertRevenueOverview2DailyRevenue(do revenue.RevenueOverview) DailyRevenue {
	return DailyRevenue{
		Day:                       *do.Day,
		OffPeakRevenue:            util.RoundFloat(do.OffPeakRevenue, 2),
		EnergyRevenue:             util.RoundFloat(do.EnergyRevenue, 2),
		BatteryMaintenanceRevenue: util.RoundFloat(do.BatteryMaintenanceRevenue, 2),
	}
}

func convertRevenueDO2DailyOffPeakRevenue(do revenue.RevenueDO) DailyOffPeakRevenue {
	return DailyOffPeakRevenue{
		Day:            do.Day,
		OffPeakRevenue: util.RoundFloatPtr(do.OffPeakRevenue, 2),
	}
}

func convertRevenueDO2DailyBatteryMaintenanceRevenue(do revenue.RevenueDO) DailyBatteryMaintenanceRevenue {
	vo := DailyBatteryMaintenanceRevenue{
		Day:                          do.Day,
		BatteryMaintenanceTimes:      util.RoundFloatPtr(do.BatteryMaintenanceTimes, 2),
		BatteryMaintenancePercentage: nil,
	}
	if do.BatteryTotal != nil && do.BatteryMaintenanceTimes != nil && *do.BatteryTotal != 0 {
		batteryMaintenancePercentage := util.RoundFloat(*do.BatteryMaintenanceTimes / *do.BatteryTotal * 100, 2)
		vo.BatteryMaintenancePercentage = &batteryMaintenancePercentage
	}
	return vo
}

func convertRevenueDO2DailyEnergyRevenue(do revenue.RevenueDO) DailyEnergyRevenue {
	return DailyEnergyRevenue{
		Day:              do.Day,
		ChargeRevenue:    util.RoundFloatPtr(do.ChargeRevenue, 2),
		WaterRevenue:     util.RoundFloatPtr(do.WaterRevenue, 2),
		OperationRevenue: util.RoundFloatPtr(do.OperationRevenue, 2),
	}
}
