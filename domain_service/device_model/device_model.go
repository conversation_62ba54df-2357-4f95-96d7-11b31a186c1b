package device_model

import (
	"context"
	"fmt"
	"git.nevint.com/welkin2/welkin-backend/client"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"sort"
	"strings"
	"sync"
	"time"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	"git.nevint.com/welkin2/welkin-backend/config"
	domain_device "git.nevint.com/welkin2/welkin-backend/domain/device"
	"git.nevint.com/welkin2/welkin-backend/domain/energy"
	"git.nevint.com/welkin2/welkin-backend/domain/health"
	"git.nevint.com/welkin2/welkin-backend/domain/revenue"
	"git.nevint.com/welkin2/welkin-backend/domain/service"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/util"
)

type DeviceModelService struct {
	ServiceDO *service.ServiceDO
	DeviceDO  *domain_device.Device
	EnergyDO  *energy.EnergyDO
	RevenueDO *revenue.RevenueDO
}

// 计入单站模型计算的设备类型
var deviceModelProjects = []string{umw.PowerSwap2, umw.PUS3, umw.PUS4}

// 目标年化收益
var targetYearlyRevenue float64 = 189000000

func getTargetYearlyRevenue() float64 {
	// 初始化配置参数
	deviceModelConfig, ok := config.Cfg.ExtraConfig["deviceModel"].(map[string]interface{})
	if !ok {
		return targetYearlyRevenue
	}
	if yearlyRevenue := util.ParseFloat(deviceModelConfig["targetYearlyRevenue"]); yearlyRevenue != -1 {
		targetYearlyRevenue = yearlyRevenue
	}
	return targetYearlyRevenue
}

// GetOverviewYTD 单站模型总览数据
func (d *DeviceModelService) GetOverviewYTD(ctx context.Context) (DeviceModelOverviewVO, error) {
	startTs := time.Date(time.Now().Year(), 1, 1, 0, 0, 0, 0, time.Local).UnixMilli()
	endTs := time.Now().UnixMilli()
	days := float64(endTs-startTs) / (24 * 60 * 60 * 1000)
	res := DeviceModelOverviewVO{}
	mu := sync.Mutex{}
	g := ucmd.NewErrGroup(ctx, 10)
	// 换电次数
	serviceCount := make(map[string]int64)
	g.GoRecover(func() error {
		var gErr error
		serviceCount, gErr = d.prepareServiceOverview(ctx, startTs, endTs)
		if gErr != nil {
			log.CtxLog(ctx).Errorf("fail to prepare service, err: %v", gErr)
			return gErr
		}
		mu.Lock()
		defer mu.Unlock()
		for _, count := range serviceCount {
			res.TotalSwapTimes += count
		}
		return nil
	})
	// 设备数据
	deviceCount := make(map[string]int64)
	g.GoRecover(func() error {
		var gErr error
		deviceCount, gErr = d.DeviceDO.CountDevices(ctx)
		if gErr != nil {
			log.CtxLog(ctx).Errorf("fail to count device, err: %v", gErr)
			return gErr
		}
		return nil
	})
	// 单站能效
	g.GoRecover(func() error {
		energyByProject, err := d.EnergyDO.AggregateEnergy(ctx, energy.AggregateEnergyCond{StartTime: startTs, EndTime: endTs}, "project")
		if err != nil {
			log.CtxLog(ctx).Errorf("fail to prepare energy, err: %v", err)
			return err
		}
		mu.Lock()
		defer mu.Unlock()
		res.Energy = convertEnergyOverview2DeviceModelDetail(energyByProject)
		return nil
	})
	// 健康度
	g.GoRecover(func() error {
		healthVO, err := d.prepareHealthOverview(ctx, startTs, endTs)
		if err != nil {
			log.CtxLog(ctx).Errorf("fail to prepare health, err: %v", err)
			return err
		}
		mu.Lock()
		defer mu.Unlock()
		res.Health = healthVO
		return nil
	})
	// 单站经营收益
	g.GoRecover(func() error {
		revenueByProject, err := d.RevenueDO.AggregateRevenue(ctx, revenue.AggregateRevenueCond{StartTime: startTs, EndTime: endTs}, revenue.AggregateGroupByProject)
		if err != nil {
			log.CtxLog(ctx).Errorf("fail to prepare energy, err: %v", err)
			return err
		}
		mu.Lock()
		defer mu.Unlock()
		res.Profit = convertRevenueOverview2DeviceModelDetail(revenueByProject)
		return nil
	})
	// 更新时间
	g.GoRecover(func() error {
		energyUpdateTime, err := d.EnergyDO.GetEnergyUpdateTime(ctx, startTs, endTs)
		if err != nil {
			log.CtxLog(ctx).Errorf("fail to get energy update time, err: %v", err)
			return err
		}
		revenueUpdateTime, err := d.RevenueDO.GetRevenueUpdateTime(ctx, startTs, endTs)
		if err != nil {
			log.CtxLog(ctx).Errorf("fail to get revenue update time, err: %v", err)
			return err
		}
		mu.Lock()
		defer mu.Unlock()
		res.EnergyUpdateTime = energyUpdateTime
		res.RevenueUpdateTime = revenueUpdateTime
		return nil
	})

	if err := g.Wait(); err != nil {
		log.CtxLog(ctx).Errorf("GetOverviewYTD failed, err: %v", err)
		return DeviceModelOverviewVO{}, err
	}
	res.Device = convertDeviceServiceVO(days, serviceCount, deviceCount)

	return res, nil
}

// 返回的map：设备类型->总单量
func (d *DeviceModelService) prepareServiceOverview(ctx context.Context, startTs, endTs int64) (map[string]int64, error) {
	res := make(map[string]int64)
	endTime := time.UnixMilli(endTs)
	endDay := time.Date(endTime.Year(), endTime.Month(), endTime.Day(), 0, 0, 0, 0, time.Local).UnixMilli()
	for _, project := range deviceModelProjects {
		// 当日换电次数
		serviceCountToday, err := d.ServiceDO.CountService(ctx, service.CountServiceCond{Project: project, ServiceStartTime: endDay, ServiceEndTime: endTs})
		if err != nil {
			log.CtxLog(ctx).Errorf("fail to count service, err: %v, project: %s", err, project)
			return nil, err
		}
		// 历史换电次数
		filter := bson.D{{"project", project}, {"day", bson.M{"$gte": startTs, "$lt": endDay}}}
		pipeline := mongo.Pipeline{
			{{"$match", filter}},
			{{"$group", bson.M{"_id": nil, "count": bson.M{"$sum": "$count"}}}},
		}
		var result []bson.M
		err = client.GetWatcher().Mongodb().NewMongoEntry().Aggregate(umw.ServiceInfo, service.CollectionServiceDailyCount, pipeline, &result)
		if err != nil {
			log.CtxLog(ctx).Errorf("fail to aggregate service count, err: %v, project: %s", err, project)
			return nil, err
		}
		serviceCountHistory := 0
		if len(result) > 0 {
			serviceCountHistory = util.ParseInt(result[0]["count"])
		}
		res[project] = serviceCountToday + int64(serviceCountHistory)
	}
	return res, nil
}

// todo: 支持多种设备类型，现在先写死
func (d *DeviceModelService) prepareHealthOverview(ctx context.Context, startTs, endTs int64) (DeviceModelDetail, error) {
	prepare := func(project string) (ProjectValue, error) {
		healthDO, err := health.NewDeviceHealth(project)
		if err != nil {
			return ProjectValue{}, err
		}
		healthScore, err := healthDO.CalculateHealthScore(ctx, startTs, endTs)
		if err != nil {
			return ProjectValue{}, err
		}
		return convertHealthScore2VO(project, healthScore), nil
	}
	pus3Health, err := prepare(umw.PUS3)
	if err != nil {
		return DeviceModelDetail{}, err
	}
	return DeviceModelDetail{
		Value: util.RoundFloatPtr(pus3Health.Value, 2),
		Detail: []ProjectValue{
			{Project: umw.PowerSwap2},
			pus3Health,
			{Project: umw.PUS4},
		},
	}, nil
}

// ListEnergy 单站模型能效明细列表
func (d *DeviceModelService) ListEnergy(ctx context.Context, cond energy.ListEnergyCond) ([]EnergyDetailVO, int64, error) {
	energyDOs, total, err := d.EnergyDO.ListEnergy(ctx, cond)
	if err != nil {
		log.CtxLog(ctx).Errorf("ListEnergy failed, err: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return nil, 0, err
	}
	vo := make([]EnergyDetailVO, 0)
	for _, do := range energyDOs {
		vo = append(vo, convertEnergyDO2VO(do))
	}
	return vo, total, nil
}

// ListRevenue 单站模型收益明细列表
func (d *DeviceModelService) ListRevenue(ctx context.Context, cond revenue.ListRevenueCond) ([]RevenueDetailVO, int64, error) {
	revenueDOs, total, err := d.RevenueDO.ListRevenue(ctx, cond)
	if err != nil {
		log.CtxLog(ctx).Errorf("ListRevenue failed, err: %v, cond: %s", err, ucmd.ToJsonStrIgnoreErr(cond))
		return nil, 0, err
	}
	vo := make([]RevenueDetailVO, 0)
	for _, do := range revenueDOs {
		vo = append(vo, convertRevenueDO2VO(do))
	}
	return vo, total, nil
}

// GetEnergyOverview 能效总览
func (d *DeviceModelService) GetEnergyOverview(ctx context.Context, cond energy.AggregateEnergyCond, topN int) (EnergyOverviewVO, error) {
	res := EnergyOverviewVO{}
	g := ucmd.NewErrGroup(ctx, 10)
	mu := sync.Mutex{}
	// 能效
	g.GoRecover(func() error {
		energyOverview, err := d.EnergyDO.AggregateEnergy(ctx, cond, "project")
		if err != nil {
			log.CtxLog(ctx).Errorf("GetEnergyOverview, fail to aggregate energy by project, err: %v", err)
			return err
		}
		mu.Lock()
		defer mu.Unlock()
		res.Energy = convertEnergyOverview2DeviceModelDetail(energyOverview)
		return nil
	})
	// ytd能效
	g.GoRecover(func() error {
		ytdCond := energy.AggregateEnergyCond{
			StartTime:   time.Date(time.Now().Year(), 1, 1, 0, 0, 0, 0, time.Local).UnixMilli(),
			EndTime:     time.Now().UnixMilli(),
			Project:     cond.Project,
			CityCompany: cond.CityCompany,
			DeviceId:    cond.DeviceId,
		}
		ytdEnergyOverview, err := d.EnergyDO.AggregateEnergy(ctx, ytdCond, "project")
		if err != nil {
			log.CtxLog(ctx).Errorf("GetEnergyOverview, fail to aggregate ytd energy by project, err: %v", err)
			return err
		}
		mu.Lock()
		defer mu.Unlock()
		res.YtdEnergy = convertEnergyOverview2DeviceModelDetail(ytdEnergyOverview)
		return nil
	})
	// 尾部站点 + 能效分布
	g.GoRecover(func() error {
		energyByDevice, err := d.EnergyDO.AggregateEnergy(ctx, cond, "device_id")
		if err != nil {
			log.CtxLog(ctx).Errorf("GetEnergyOverview, fail to aggregate energy by device_id, err: %v", err)
			return err
		}
		// 筛选有效站点（能效>=0）
		var filteredEnergyByDevice []energy.EnergyOverview
		for _, record := range energyByDevice {
			if record.Efficiency > 0 {
				filteredEnergyByDevice = append(filteredEnergyByDevice, record)
			}
		}
		sort.Slice(filteredEnergyByDevice, func(i, j int) bool {
			return filteredEnergyByDevice[i].Efficiency < filteredEnergyByDevice[j].Efficiency
		})
		if topN > len(filteredEnergyByDevice) {
			topN = len(filteredEnergyByDevice)
		}
		mu.Lock()
		defer mu.Unlock()
		res.TailDevices = convertEnergyOverview2TailDevice(filteredEnergyByDevice[:topN])
		res.EnergyDistribution = convertEnergyOverview2EnergyDistribution(filteredEnergyByDevice)
		return nil
	})
	// 不同单量下的能效占比
	g.GoRecover(func() error {
		energyByDay, err := d.EnergyDO.AggregateEnergy(ctx, cond, "day")
		if err != nil {
			log.CtxLog(ctx).Errorf("GetEnergyOverview, fail to aggregate energy by day, err: %v", err)
			return err
		}
		sort.Slice(energyByDay, func(i, j int) bool {
			return *energyByDay[i].Day < *energyByDay[j].Day
		})
		mu.Lock()
		defer mu.Unlock()
		res.DailyEnergy = convertEnergyOverview2DailyEnergy(energyByDay)
		return nil
	})
	if err := g.Wait(); err != nil {
		log.CtxLog(ctx).Errorf("GetEnergyOverview goroutine failed, err: %v", err)
		return EnergyOverviewVO{}, err
	}
	return res, nil
}

// GetRevenueOverview 收益总览
func (d *DeviceModelService) GetRevenueOverview(ctx context.Context, cond revenue.AggregateRevenueCond, topN int) (RevenueOverviewVO, error) {
	res := RevenueOverviewVO{}
	g := ucmd.NewErrGroup(ctx, 10)
	mu := sync.Mutex{}
	// 年化收益
	g.GoRecover(func() error {
		ytdCond := revenue.AggregateRevenueCond{
			StartTime:   time.Date(time.Now().Year(), 1, 1, 0, 0, 0, 0, time.Local).UnixMilli(),
			EndTime:     time.Now().UnixMilli(),
			Project:     cond.Project,
			CityCompany: cond.CityCompany,
			DeviceId:    cond.DeviceId,
		}
		ytdRevenueOverview, err := d.RevenueDO.AggregateRevenue(ctx, ytdCond, revenue.AggregateGroupByProject)
		if err != nil {
			log.CtxLog(ctx).Errorf("GetRevenueOverview, fail to aggregate ytd revenue by project, err: %v", err)
			return err
		}
		updateTime, err := d.RevenueDO.GetRevenueUpdateTime(ctx, ytdCond.StartTime, ytdCond.EndTime)
		if err != nil {
			log.CtxLog(ctx).Errorf("GetRevenueOverview, fail to get ytd revenue update time, err: %v", err)
			return err
		}
		validDays, err := d.RevenueDO.CountValidDays(ctx, ytdCond)
		if err != nil {
			log.CtxLog(ctx).Errorf("GetRevenueOverview, fail to count ytd valid days, err: %v", err)
			return err
		}
		mu.Lock()
		defer mu.Unlock()
		res.AnnualizedRevenue = convertRevenueOverview2AnnualizedRevenueInfo(ytdRevenueOverview, updateTime, validDays)
		return nil
	})
	// 所选区间收益
	g.GoRecover(func() error {
		revenueOverview, err := d.RevenueDO.AggregateRevenue(ctx, cond, revenue.AggregateGroupByProject)
		if err != nil {
			log.CtxLog(ctx).Errorf("GetRevenueOverview, fail to aggregate revenue by project, err: %v", err)
			return err
		}
		updateTime, err := d.RevenueDO.GetRevenueUpdateTime(ctx, cond.StartTime, cond.EndTime)
		if err != nil {
			log.CtxLog(ctx).Errorf("GetRevenueOverview, fail to get revenue update time, err: %v", err)
			return err
		}
		validDays, err := d.RevenueDO.CountValidDays(ctx, cond)
		if err != nil {
			log.CtxLog(ctx).Errorf("GetRevenueOverview, fail to count valid days, err: %v", err)
			return err
		}
		mu.Lock()
		defer mu.Unlock()
		res.Revenue = convertRevenueOverview2RevenueInfo(revenueOverview, updateTime, validDays)
		return nil
	})
	// 尾部站点
	g.GoRecover(func() error {
		revenueByDevice, err := d.RevenueDO.AggregateRevenue(ctx, cond, revenue.AggregateGroupByDeviceId)
		if err != nil {
			log.CtxLog(ctx).Errorf("GetRevenueOverview, fail to aggregate revenue by device_id, err: %v", err)
			return err
		}
		sort.Slice(revenueByDevice, func(i, j int) bool {
			return revenueByDevice[i].TotalRevenue < revenueByDevice[j].TotalRevenue
		})
		if topN > len(revenueByDevice) {
			topN = len(revenueByDevice)
		}
		mu.Lock()
		defer mu.Unlock()
		res.TailDevices = convertRevenueOverview2TailDevice(revenueByDevice[:topN])
		return nil
	})
	// 收益变化趋势
	g.GoRecover(func() error {
		revenueByDay, err := d.RevenueDO.AggregateRevenue(ctx, cond, revenue.AggregateGroupByDay)
		if err != nil {
			log.CtxLog(ctx).Errorf("GetRevenueOverview, fail to aggregate revenue by day, err: %v", err)
			return err
		}
		sort.Slice(revenueByDay, func(i, j int) bool {
			return *revenueByDay[i].Day < *revenueByDay[j].Day
		})
		dailyRevenue := make([]DailyRevenue, 0)
		for _, record := range revenueByDay {
			dailyRevenue = append(dailyRevenue, convertRevenueOverview2DailyRevenue(record))
		}
		mu.Lock()
		defer mu.Unlock()
		res.RevenueTrend = dailyRevenue
		return nil
	})
	if err := g.Wait(); err != nil {
		log.CtxLog(ctx).Errorf("GetRevenueOverview goroutine failed, err: %v", err)
		return RevenueOverviewVO{}, err
	}
	return res, nil
}

func (d *DeviceModelService) GetSingleDeviceRevenue(ctx context.Context, cond revenue.AggregateRevenueCond, day int64) (SingleDeviceRevenueVO, error) {
	var res SingleDeviceRevenueVO
	g := ucmd.NewErrGroup(ctx, 10)
	mu := sync.Mutex{}
	// 收益数据
	g.GoRecover(func() error {
		revenueToday, err := d.RevenueDO.GetRevenue(ctx, revenue.GetRevenueCond{
			Day:      day,
			Project:  cond.Project,
			DeviceId: cond.DeviceId,
		})
		if err != nil {
			log.CtxLog(ctx).Errorf("GetSingleDeviceRevenue, fail to get today revenue, err: %v", err)
			return err
		}
		revenueYesterday, err := d.RevenueDO.GetRevenue(ctx, revenue.GetRevenueCond{
			Day:      day - 24*time.Hour.Milliseconds(),
			Project:  cond.Project,
			DeviceId: cond.DeviceId,
		})
		if err != nil {
			log.CtxLog(ctx).Errorf("GetSingleDeviceRevenue, fail to get yesterday revenue, err: %v", err)
		}
		mu.Lock()
		defer mu.Unlock()
		res.TotalRevenue = util.RoundFloatPtr(revenueToday.TotalRevenue, 2)
		res.OffPeakRevenue = util.RoundFloatPtr(revenueToday.OffPeakRevenue, 2)
		res.EnergyRevenue = util.RoundFloatPtr(revenueToday.EnergyRevenue, 2)
		res.BatteryMaintenanceRevenue = util.RoundFloatPtr(revenueToday.BatteryMaintenanceRevenue, 2)
		if revenueYesterday != nil {
			if revenueToday.OffPeakRevenue != nil && revenueYesterday.OffPeakRevenue != nil {
				offPeakRevenueChange := util.RoundFloat(*revenueToday.OffPeakRevenue-*revenueYesterday.OffPeakRevenue, 2)
				res.OffPeakRevenueChange = &offPeakRevenueChange
			}
			if revenueToday.EnergyRevenue != nil && revenueYesterday.EnergyRevenue != nil {
				energyRevenueChange := util.RoundFloat(*revenueToday.EnergyRevenue-*revenueYesterday.EnergyRevenue, 2)
				res.EnergyRevenueChange = &energyRevenueChange
			}
			if revenueToday.BatteryMaintenanceRevenue != nil && revenueYesterday.BatteryMaintenanceRevenue != nil {
				batteryMaintenanceRevenueChange := util.RoundFloat(*revenueToday.BatteryMaintenanceRevenue-*revenueYesterday.BatteryMaintenanceRevenue, 2)
				res.BatteryMaintenanceRevenueChange = &batteryMaintenanceRevenueChange
			}
		}
		return nil
	})
	// 收益变化趋势
	g.GoRecover(func() error {
		revenueByDay, err := d.RevenueDO.AggregateRevenue(ctx, cond, revenue.AggregateGroupByDay)
		if err != nil {
			log.CtxLog(ctx).Errorf("GetRevenueOverview, fail to aggregate revenue by day, err: %v", err)
			return err
		}
		sort.Slice(revenueByDay, func(i, j int) bool {
			return *revenueByDay[i].Day < *revenueByDay[j].Day
		})
		dailyRevenue := make([]DailyRevenue, 0)
		for _, record := range revenueByDay {
			dailyRevenue = append(dailyRevenue, convertRevenueOverview2DailyRevenue(record))
		}
		mu.Lock()
		defer mu.Unlock()
		res.RevenueTrend = dailyRevenue
		return nil
	})
	// 三种收益表
	g.GoRecover(func() error {
		dailyRevenue, _, err := d.RevenueDO.ListRevenue(ctx, revenue.ListRevenueCond{
			StartTime: cond.StartTime,
			EndTime:   cond.EndTime,
			Project:   cond.Project,
			DeviceId:  cond.DeviceId,
		})
		if err != nil {
			log.CtxLog(ctx).Errorf("SingleDeviceRevenueVO, fail to list revenue, err: %v", err)
			return err
		}
		sort.Slice(dailyRevenue, func(i, j int) bool {
			return dailyRevenue[i].Day < dailyRevenue[j].Day
		})
		dailyOffPeakRevenue := make([]DailyOffPeakRevenue, 0)
		dailyBatteryMaintenanceRevenue := make([]DailyBatteryMaintenanceRevenue, 0)
		dailyEnergyRevenue := make([]DailyEnergyRevenue, 0)
		for _, record := range dailyRevenue {
			dailyOffPeakRevenue = append(dailyOffPeakRevenue, convertRevenueDO2DailyOffPeakRevenue(record))
			dailyBatteryMaintenanceRevenue = append(dailyBatteryMaintenanceRevenue, convertRevenueDO2DailyBatteryMaintenanceRevenue(record))
			dailyEnergyRevenue = append(dailyEnergyRevenue, convertRevenueDO2DailyEnergyRevenue(record))
		}
		mu.Lock()
		defer mu.Unlock()
		res.OffPeakRevenueTrend = dailyOffPeakRevenue
		res.BatteryMaintenanceRevenueTrend = dailyBatteryMaintenanceRevenue
		res.EnergyRevenueTrend = dailyEnergyRevenue
		return nil
	})
	// 站点建议
	g.GoRecover(func() error {
		suggestions, err := d.RevenueDO.CalculateDeviceSuggestion(ctx, revenue.AggregateRevenueCond{
			StartTime: day,
			EndTime:   day + 24*time.Hour.Milliseconds(),
			DeviceId:  cond.DeviceId,
		})
		if err != nil {
			log.CtxLog(ctx).Errorf("SingleDeviceRevenueVO, fail to calculate device suggestion, err: %v", err)
			return err
		}
		suggestion := "站点运行良好，各方面均在前70%"
		if len(suggestions) > 0 {
			suggestion = strings.Join(suggestions, "; ")
		}
		mu.Lock()
		defer mu.Unlock()
		res.Suggestion = suggestion
		return nil
	})
	if err := g.Wait(); err != nil {
		log.CtxLog(ctx).Errorf("GetSingleDeviceRevenue goroutine failed, err: %v, cond: %s, day: %d", err, ucmd.ToJsonStrIgnoreErr(cond), day)
		return SingleDeviceRevenueVO{}, err
	}
	return res, nil
}

func (d *DeviceModelService) GetSingleDeviceEnergy(ctx context.Context, cond energy.AggregateEnergyCond, day int64) (SingleDeviceEnergyVO, error) {
	var res SingleDeviceEnergyVO
	g := ucmd.NewErrGroup(ctx, 10)
	mu := sync.Mutex{}
	// 能效数据
	g.GoRecover(func() error {
		energyToday, err := d.EnergyDO.GetEnergy(ctx, energy.GetEnergyCond{
			Day:      day,
			Project:  cond.Project,
			DeviceId: cond.DeviceId,
		})
		if err != nil {
			log.CtxLog(ctx).Errorf("GetSingleDeviceEnergy, fail to get today energy, err: %v", err)
			return err
		}
		energyYesterday, err := d.EnergyDO.GetEnergy(ctx, energy.GetEnergyCond{
			Day:      day - 24*time.Hour.Milliseconds(),
			Project:  cond.Project,
			DeviceId: cond.DeviceId,
		})
		if err != nil {
			log.CtxLog(ctx).Errorf("GetSingleDeviceEnergy, fail to get yesterday energy, err: %v", err)
			return err
		}
		suggestion := "该设备能效表现良好"
		if energyToday.RankLevelFinal != nil && *energyToday.RankLevelFinal == "5" {
			energyRankDO := &energy.EnergyRankDO{}
			rankDO, gErr := energyRankDO.GetEnergyRank(ctx, energy.GetEnergyRankCond{
				Project:      energyToday.Project,
				Ts:           energyToday.Day,
				Temperature:  energyToday.HighTemperature,
				ServiceCount: energyToday.ServiceCount,
			})
			if gErr != nil {
				log.CtxLog(ctx).Errorf("GetSingleDeviceEnergy, fail to get energy rank, err: %v", gErr)
				return gErr
			}
			var ees04, wes04, oes04 string
			if rankDO.Ees04 != nil {
				ees04 = fmt.Sprintf("%.2f", *rankDO.Ees04*100)
			} else {
				ees04 = "null"
			}
			if rankDO.Wes04 != nil {
				wes04 = fmt.Sprintf("%.2f", *rankDO.Wes04)
			} else {
				wes04 = "null"
			}
			if rankDO.Oes04 != nil {
				oes04 = fmt.Sprintf("%.2f", *rankDO.Oes04)
			} else {
				oes04 = "null"
			}
			suggestion = fmt.Sprintf("该站点在同温度和同单量区间的站点里，能效较差，4级能效标准：%s%%，其中，水箱能耗标准%skWh，剩余运营能耗标准%skWh", ees04, wes04, oes04)
		}
		mu.Lock()
		defer mu.Unlock()
		res.TotalConsumption = util.RoundFloatPtr(energyToday.TotalConsumption, 2)
		res.ChargeConsumption = util.RoundFloatPtr(energyToday.ChargeConsumption, 2)
		res.WaterConsumption = util.RoundFloatPtr(energyToday.WaterConsumption, 2)
		res.OperationConsumption = util.RoundFloatPtr(energyToday.OperationConsumption, 2)
		res.MechanicalConsumption = util.RoundFloatPtr(energyToday.MechanicalConsumption, 2)
		res.LightConsumption = util.RoundFloatPtr(energyToday.LightConsumption, 2)
		res.UpsConsumption = util.RoundFloatPtr(energyToday.UpsConsumption, 2)
		res.CoolingConsumption = util.RoundFloatPtr(energyToday.CoolingConsumption, 2)
		res.NonChargeConsumption = util.RoundFloatPtr(energyToday.NonChargeConsumption, 2)
		res.ModuleOutputEnergyTotal = util.RoundFloatPtr(energyToday.ModuleOutputEnergyTotal, 2)
		res.ChargingEnergy = util.RoundFloatPtr(energyToday.ChargingEnergy, 2)
		res.ChargingEnergyAvailable = util.RoundFloatPtr(energyToday.ChargingEnergyAvailable, 2)
		res.ChargingPileEnergy = util.RoundFloatPtr(energyToday.ChargingPileEnergy, 2)
		if energyToday.Efficiency != nil && *energyToday.Efficiency > 0 {
			efficiencyPercentage := util.RoundFloat(*energyToday.Efficiency*100, 2)
			res.Energy = &efficiencyPercentage
		}
		if energyYesterday != nil {
			if energyToday.ChargeConsumption != nil && energyYesterday.ChargeConsumption != nil {
				chargeConsumptionChange := util.RoundFloat(*energyToday.ChargeConsumption-*energyYesterday.ChargeConsumption, 2)
				res.ChargeConsumptionChange = &chargeConsumptionChange
			}
			if energyToday.NonChargeConsumption != nil && energyYesterday.NonChargeConsumption != nil {
				nonChargeConsumptionChange := util.RoundFloat(*energyToday.NonChargeConsumption-*energyYesterday.NonChargeConsumption, 2)
				res.NonChargeConsumptionChange = &nonChargeConsumptionChange
			}
		}
		res.Suggestion = suggestion
		return nil
	})
	// 能效变化趋势
	g.GoRecover(func() error {
		energyByDay, err := d.EnergyDO.AggregateEnergy(ctx, cond, "day")
		if err != nil {
			log.CtxLog(ctx).Errorf("GetSingleDeviceEnergy, fail to aggregate energy by day, err: %v", err)
			return err
		}
		sort.Slice(energyByDay, func(i, j int) bool {
			return *energyByDay[i].Day < *energyByDay[j].Day
		})
		mu.Lock()
		defer mu.Unlock()
		res.DailyEnergy = convertEnergyOverview2DailyEnergy(energyByDay)
		return nil
	})
	if err := g.Wait(); err != nil {
		log.CtxLog(ctx).Errorf("GetSingleDeviceEnergy goroutine failed, err: %v, cond: %s, day: %d", err, ucmd.ToJsonStrIgnoreErr(cond), day)
		return SingleDeviceEnergyVO{}, err
	}
	return res, nil
}
