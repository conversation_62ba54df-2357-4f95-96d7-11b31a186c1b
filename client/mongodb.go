package client

import (
	"context"
	"crypto/tls"
	"crypto/x509"
	"encoding/json"
	"errors"
	"fmt"
	mmgo "git.nevint.com/welkin2/welkin-backend/model/mongo"
	"io/ioutil"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"
	"go.uber.org/zap"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	um "git.nevint.com/golang-libs/common-utils/model"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	"git.nevint.com/welkin2/welkin-backend/model"
	"git.nevint.com/welkin2/welkin-backend/util"
)

var defaultTimeout = 20 * time.Second

type MongoEntry interface {
	GetOne(dbName, colName string, opts ...MongoOptions) (bson.Raw, error)
	ListAll(dbName, colName string, ordered Ordered) ([]byte, error)
	ListAllPLC(dbName, colName string, ordered Ordered) ([]byte, error)
	ListByPagination(dbName, colName string, page Pagination, ordered Ordered) ([]byte, int64, error)
	InsertOne(dbName, colName string, record interface{}, idx ...IndexOption) error
	InsertMany(dbName, colName string, records []interface{}, idx ...IndexOption) error
	AppendOne(dbName, colName string, record interface{}, idx ...IndexOption) error
	ReplaceOne(dbName, colName string, record interface{}, upsert bool, idx ...IndexOption) error
	UpdateOne(dbName, colName string, update interface{}, upsert bool, idx ...IndexOption) error
	UpdateManyBulk(dbName, colName string, models []mongo.WriteModel, idx ...IndexOption) error
	UpdateMany(dbName, colName string, update interface{}) error
	UpdateAndGetOne(dbName, colName string, update interface{}) (bson.Raw, error)
	UpdateById(dbName, colName string, id, update interface{}) error
	DeleteOne(dbName, colName string) error
	Count(dbName, colName string) (int64, error)
	FindOne(dbName, colName string, opts *options.FindOneOptions, res interface{}) error
	FindMany(dbName, colName string, opts *options.FindOptions, res interface{}) (total int64, err error)
	Aggregate(dbName, colName string, pipeline mongo.Pipeline, res interface{}) error
	Distinct(dbName, colName string, field string) ([]interface{}, error)
	GetPercentage(dbName, colName string, field string, top bool, percent float64) ([]interface{}, error)
	UserInfo
	DeviceInfo
	DiagnosisInfo
	AlgorithmImage
}

type MongoOptions struct {
	Projection   *bson.M
	Timeout      *time.Duration
	IndexOptions []IndexOption
	Skip         *int64
	Limit        *int64
	*Ordered
}

type mongoEntry struct {
	client *mongo.Client
	filter bson.D
	baseInfo
	algorithmImage
}

// GetOne return the single record
func (m *mongoEntry) GetOne(dbName, colName string, mogOpts ...MongoOptions) (bson.Raw, error) {
	var (
		opts       MongoOptions
		customOpts *options.FindOneOptions
		timeout    time.Duration
	)
	if len(mogOpts) != 0 {
		opts = mogOpts[0]
	}
	if opts.Timeout != nil {
		timeout = *opts.Timeout
	} else {
		timeout = defaultTimeout
	}
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	if len(m.filter) == 0 {
		return nil, errors.New("`filter` cannot be empty")
	}
	if opts.Projection != nil {
		customOpts = options.FindOne().SetProjection(opts.Projection)
	}

	cur := m.client.Database(dbName).Collection(colName).FindOne(ctx, m.filter, customOpts)
	if cur.Err() != nil {
		if cur.Err() == mongo.ErrNoDocuments {
			return nil, nil
		}
		return nil, fmt.Errorf("find one, filter: %v, err: %v", m.filter, cur.Err())
	}
	return cur.DecodeBytes()
}

// ListAll list all mongo records, support filter and show in order
func (m *mongoEntry) ListAll(dbName, colName string, ordered Ordered) ([]byte, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	cur, err := m.client.Database(dbName).Collection(colName).Find(
		ctx, m.filter, options.Find().SetSort(newSort(ordered)))
	if err != nil {
		return nil, fmt.Errorf("find many records, filter: %v, err: %v", m.filter, err)
	}
	var responseData []map[string]interface{}
	if err = cur.All(ctx, &responseData); err != nil {
		return nil, fmt.Errorf("decode many records, filter: %v, err: %v", m.filter, err)
	}
	return json.Marshal(responseData)
}

// ListAllPLC list all plc records, support filter and show in order
// plc records are saved in a single db, which is connected as plcClient
func (m *mongoEntry) ListAllPLC(dbName, colName string, ordered Ordered) ([]byte, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 100*time.Second)
	defer cancel()

	cur, err := m.client.Database(dbName).Collection(colName).Find(
		ctx, m.filter, options.Find().SetSort(newSort(ordered)))
	if err != nil {
		return nil, fmt.Errorf("find many plc records, filter: %v, err: %v", m.filter, err)
	}
	var responseData []map[string]interface{}
	if err = cur.All(ctx, &responseData); err != nil {
		return nil, fmt.Errorf("decode many plc records, filter: %v, err: %v", m.filter, err)
	}
	return json.Marshal(responseData)
}

// ListByPagination list many mongo records by pagination, support filter and show in order
func (m *mongoEntry) ListByPagination(dbName, colName string, size Pagination, ordered Ordered) ([]byte, int64, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	var total int64
	collection := m.client.Database(dbName).Collection(colName)
	cur, err := collection.Find(ctx, m.filter, options.Find().SetSort(newSort(ordered)).SetLimit(size.Limit).SetSkip(size.Offset))
	if err != nil {
		return nil, total, fmt.Errorf("find many records, filter: %v, err: %v", m.filter, err)
	}

	var responseData []map[string]interface{}
	if err = cur.All(ctx, &responseData); err != nil {
		return nil, total, fmt.Errorf("decode many records, filter: %v, err: %v", m.filter, err)
	}
	total, err = collection.CountDocuments(ctx, m.filter)
	if err != nil {
		return nil, total, fmt.Errorf("count documents, err: %v", err)
	}
	byteData, err := json.Marshal(responseData)
	return byteData, total, err
}

// FindOne find one mongo record, support find options
func (m *mongoEntry) FindOne(dbName, colName string, opts *options.FindOneOptions, res interface{}) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	collection := m.client.Database(dbName).Collection(colName)
	return collection.FindOne(ctx, m.filter, opts).Decode(res)
}

// FindMany find many mongo records, support find options
func (m *mongoEntry) FindMany(dbName, colName string, opts *options.FindOptions, res interface{}) (total int64, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	collection := m.client.Database(dbName).Collection(colName)
	cur, err := collection.Find(ctx, m.filter, opts)
	if err != nil {
		return total, fmt.Errorf("find many records, err: %v", err)
	}

	if err = cur.All(ctx, res); err != nil {
		return total, fmt.Errorf("decode many records, err: %v", err)
	}
	total, err = collection.CountDocuments(ctx, m.filter)
	if err != nil {
		return total, fmt.Errorf("count documents, err: %v", err)
	}
	return
}

// InsertOne can insert single record into db, throw exception if the document found by filter
func (m *mongoEntry) InsertOne(dbName, colName string, record interface{}, idx ...IndexOption) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if len(m.filter) == 0 {
		return errors.New("`filter` cannot be empty")
	}

	if err := createIndex(ctx, m.client, dbName, colName, idx...); err != nil {
		return fmt.Errorf("create index, err: %v", err)
	}

	cur := m.client.Database(dbName).Collection(colName).FindOne(ctx, m.filter)
	if cur.Err() != nil {
		if cur.Err() == mongo.ErrNoDocuments {
			_, err := m.client.Database(dbName).Collection(colName).InsertOne(ctx, record)
			if err != nil {
				return fmt.Errorf("insert one, filter: %v, err: %v", m.filter, err)
			}
			return nil
		}
		return fmt.Errorf("find one, filter: %v, err: %v", m.filter, cur.Err())
	}
	return fmt.Errorf("insert terminated, filter: %v, the record existed", m.filter)
}

func (m *mongoEntry) AppendOne(dbName, colName string, record interface{}, idx ...IndexOption) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := createIndex(ctx, m.client, dbName, colName, idx...); err != nil {
		return fmt.Errorf("create index, err: %v", err)
	}
	_, err := m.client.Database(dbName).Collection(colName).InsertOne(ctx, record)
	return err
}

func (m *mongoEntry) UpdateById(dbName, colName string, id, update interface{}) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	_, err := m.client.Database(dbName).Collection(colName).UpdateByID(ctx, id, update)
	if err != nil {
		return fmt.Errorf("update terminated, _id: %v, err: %v", id, err)
	}
	return nil
}

// InsertMany can insert many records into db
func (m *mongoEntry) InsertMany(dbName, colName string, records []interface{}, idx ...IndexOption) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := createIndex(ctx, m.client, dbName, colName, idx...); err != nil {
		return fmt.Errorf("create index, err: %v", err)
	}
	// If false, the next writes will still be executed after one fails.
	// Default is true.
	ordered := false
	_, err := m.client.Database(dbName).Collection(colName).InsertMany(
		ctx, records, &options.InsertManyOptions{Ordered: &ordered})
	return err
}

// ReplaceOne can replace the existed record, at the same time,
// a new document will be inserted if upsert is set `true` and the filter does not match any documents in the collection
func (m *mongoEntry) ReplaceOne(dbName, colName string, record interface{}, upsert bool, idx ...IndexOption) error {
	ctx, cancel := context.WithTimeout(context.Background(), 200*time.Second)
	defer cancel()

	if len(m.filter) == 0 {
		return errors.New("`filter` cannot be empty")
	}

	if upsert {
		if err := createIndex(ctx, m.client, dbName, colName, idx...); err != nil {
			return fmt.Errorf("create index, err: %v", err)
		}
	}
	_, err := m.client.Database(dbName).Collection(colName).ReplaceOne(ctx, m.filter, record, options.Replace().SetUpsert(upsert))
	if err != nil {
		return fmt.Errorf("replace terminated, filter: %v, err: %v", m.filter, err)
	}
	return nil
}

// UpdateOne can update the existed record, at the same time,
// a new document will be inserted if `upsert` is set true and the filter does not match any documents in the collection
func (m *mongoEntry) UpdateOne(dbName, colName string, update interface{}, upsert bool, idx ...IndexOption) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if len(m.filter) == 0 {
		return errors.New("`filter` cannot be empty")
	}

	if upsert {
		if err := createIndex(ctx, m.client, dbName, colName, idx...); err != nil {
			return fmt.Errorf("create index, err: %v", err)
		}
	}

	res, err := m.client.Database(dbName).Collection(colName).UpdateOne(ctx, m.filter, update, options.Update().SetUpsert(upsert))
	if err != nil {
		return fmt.Errorf("update terminated, filter: %v, err: %v", m.filter, err)
	}
	if !upsert && res.MatchedCount == 0 {
		return fmt.Errorf("update terminated, filter: %v, no record found", m.filter)
	}
	return nil
}

func (m *mongoEntry) UpdateManyBulk(dbName, colName string, models []mongo.WriteModel, idx ...IndexOption) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := createIndex(ctx, m.client, dbName, colName, idx...); err != nil {
		return fmt.Errorf("create index, err: %v", err)
	}
	opts := options.BulkWrite().SetOrdered(false)
	_, err := m.client.Database(dbName).Collection(colName).BulkWrite(ctx, models, opts)
	return err
}

func (m *mongoEntry) UpdateMany(dbName, colName string, update interface{}) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if len(m.filter) == 0 {
		return errors.New("`filter` cannot be empty")
	}
	_, err := m.client.Database(dbName).Collection(colName).UpdateMany(ctx, m.filter, update)
	if err != nil {
		return fmt.Errorf("update many terminated, filter: %v, err: %v", m.filter, err)
	}
	return nil
}

// DeleteOne delete the single record by filter
func (m *mongoEntry) DeleteOne(dbName, colName string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if len(m.filter) == 0 {
		return errors.New("`filter` cannot be empty")
	}

	_, err := m.client.Database(dbName).Collection(colName).DeleteOne(ctx, m.filter)
	return err
}

func (m *mongoEntry) UpdateAndGetOne(dbName, colName string, update interface{}) (bson.Raw, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if len(m.filter) == 0 {
		return nil, errors.New("`filter` cannot be empty")
	}

	cur := m.client.Database(dbName).Collection(colName).FindOneAndUpdate(ctx, m.filter, update)
	if cur.Err() != nil {
		return nil, fmt.Errorf("failed to update, err: %v", cur.Err())
	}
	return cur.DecodeBytes()
}

func (m *mongoEntry) Count(dbName, colName string) (int64, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	return m.client.Database(dbName).Collection(colName).CountDocuments(ctx, m.filter)
}

func (m *mongoEntry) Aggregate(dbName, colName string, pipeline mongo.Pipeline, res interface{}) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	cursor, err := m.client.Database(dbName).Collection(colName).Aggregate(ctx, pipeline)
	if err != nil {
		return err
	}
	if err = cursor.All(ctx, res); err != nil {
		return err
	}
	return nil
}

func (m *mongoEntry) GetPercentage(dbName, colName string, field string, top bool, percent float64) (res []interface{}, err error) {
	descending := 1
	if !top {
		descending = -1
	}
	pipeline := mongo.Pipeline{
		bson.D{{"$match", m.filter}},
		bson.D{{"$sort", bson.M{field: descending}}},
		bson.D{{"$group", bson.M{
			"_id":          primitive.Null{},
			"sortedValues": bson.M{"$push": fmt.Sprintf("$%s", field)},
		}}},
		bson.D{{"$project", bson.M{
			"slice": bson.M{
				"$slice": bson.A{
					"$sortedValues",
					bson.M{"$multiply": bson.A{
						-1,
						bson.M{
							"$ceil": bson.M{"$multiply": bson.A{
								bson.M{"$size": "$sortedValues"},
								percent / 100,
							}},
						},
					}},
				},
			},
		}}},
	}
	var result []struct {
		Slice []interface{} `bson:"slice"`
	}
	err = m.Aggregate(dbName, colName, pipeline, &result)
	if len(result) > 0 {
		res = result[0].Slice
	}
	return
}

func (m *mongoEntry) Distinct(dbName, colName string, field string) ([]interface{}, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	return m.client.Database(dbName).Collection(colName).Distinct(ctx, field, m.filter)
}

type MongoClient struct {
	Client *mongo.Client
	Logger *zap.SugaredLogger
}

func (c *MongoClient) GetOne(dbName, colName string, filter bson.D, mogOpts ...MongoOptions) (bson.Raw, error) {
	var (
		opts       MongoOptions
		customOpts *options.FindOneOptions
	)
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if len(mogOpts) != 0 {
		opts = mogOpts[0]
	}
	if opts.Projection != nil {
		customOpts = options.FindOne().SetProjection(opts.Projection)
	}

	cur := c.Client.Database(dbName).Collection(colName).FindOne(ctx, filter, customOpts)
	if cur.Err() != nil {
		if cur.Err() == mongo.ErrNoDocuments {
			return nil, nil
		}
		return nil, fmt.Errorf("find one, filter: %v, err: %v", filter, cur.Err())
	}
	return cur.DecodeBytes()
}

func (c *MongoClient) ListAll(dbName, colName string, filter bson.D, mogOpts ...MongoOptions) ([]byte, error) {
	var (
		opts       MongoOptions
		customOpts *options.FindOptions
	)
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if len(mogOpts) != 0 {
		opts = mogOpts[0]
	}
	if opts.Projection != nil {
		customOpts = options.Find().SetProjection(opts.Projection)
	}

	cur, err := c.Client.Database(dbName).Collection(colName).Find(ctx, filter, customOpts)
	if err != nil {
		return nil, fmt.Errorf("find many records, filter: %v, err: %v", filter, err)
	}
	var responseData []map[string]interface{}
	if err = cur.All(ctx, &responseData); err != nil {
		return nil, fmt.Errorf("decode many records, filter: %v, err: %v", filter, err)
	}
	return json.Marshal(responseData)
}

func GetMongoClient(conf ucfg.MongoSchema, logger *zap.SugaredLogger) *MongoClient {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	cli := &MongoClient{Logger: logger}
	cliOpts := options.Client().ApplyURI(conf.URI)
	cliOpts.SetHeartbeatInterval(30 * time.Second)
	cliOpts.SetMaxPoolSize(500)
	if conf.TLS {
		rootPEM, err := ioutil.ReadFile(conf.CA)
		if err != nil {
			cli.Logger.Panicf("failed to read mongodb ca: %v", err)
		}
		roots := x509.NewCertPool()
		if ok := roots.AppendCertsFromPEM(rootPEM); !ok {
			cli.Logger.Panicf("mongodb ca appendCertsFromPEM is not OK")
		}
		tlsConfig := &tls.Config{
			RootCAs:            roots,
			InsecureSkipVerify: true,
		}
		cliOpts.SetTLSConfig(tlsConfig)
	}
	// 调试使用 开启mongo sql日志打印
	//var logMonitor = event.CommandMonitor{
	//	Started: func(ctx context.Context, startedEvent *event.CommandStartedEvent) {
	//		log.Printf("mongo reqId:%d start on db:%s cmd:%s sql:%+v", startedEvent.RequestID, startedEvent.DatabaseName,
	//			startedEvent.CommandName, startedEvent.Command)
	//	},
	//	Succeeded: func(ctx context.Context, succeededEvent *event.CommandSucceededEvent) {
	//		log.Printf("mongo reqId:%d exec cmd:%s success duration %d ns", succeededEvent.RequestID,
	//			succeededEvent.CommandName, succeededEvent.DurationNanos)
	//	},
	//	Failed: func(ctx context.Context, failedEvent *event.CommandFailedEvent) {
	//		log.Printf("mongo reqId:%d exec cmd:%s failed duration %d ns", failedEvent.RequestID,
	//			failedEvent.CommandName, failedEvent.DurationNanos)
	//	},
	//}
	//// cmd monitor set
	//cliOpts.SetMonitor(&logMonitor)
	// 调试使用 开启mongo sql日志打印
	var err error
	cli.Client, err = mongo.Connect(ctx, cliOpts)
	if err != nil {
		cli.Logger.Panicf("mongodb connect error: %v", err)
	}
	if err = cli.Client.Ping(ctx, readpref.Primary()); err != nil {
		cli.Logger.Panicf("mongodb ping error: %v", err)
	}
	cli.Logger.Infof("succeeded to connect to mongodb client.")
	return cli
}

type FilterOptions struct {
	Project          string
	DeviceId         string
	ServiceId        string
	UserId           string
	BatteryId        string
	EvId             string
	ModelTriggerTime int64
	StartTime        int64
	EndTime          int64
	Username         string
	FilePath         string
	Role             *int
	Abnormal         *int
	Module           string
	Type             *int

	TimeKey string
}

type Ordered struct {
	Key        string // default is '_id'
	Descending bool
}

type Size struct {
	Limit  int64
	Offset int64
}

type Pagination struct {
	Limit  int64
	Offset int64
}

type UpdateOptions struct {
	IsActive bool
	IsLogin  bool
}

type IndexOption struct {
	Name        string
	Fields      bson.D
	ExpiredTime int32
	Unique      bool
}

func (c *MongoClient) NewMongoEntry(opts ...bson.D) MongoEntry {
	entry := &mongoEntry{
		client:         c.Client,
		filter:         bson.D{},
		baseInfo:       baseInfo{c.Client},
		algorithmImage: algorithmImage{c.Client},
	}
	if len(opts) != 0 {
		entry.filter = opts[0]
	}
	return entry
}

func (c *MongoClient) UpdateOfflineAccountInfo(dbName, collection string, record umw.MongoOfflineAccount) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	_, err := c.Client.Database(dbName).Collection(collection).ReplaceOne(
		ctx, bson.M{"device_id": record.DeviceId}, record, options.Replace().SetUpsert(true))
	if err != nil {
		c.Logger.Errorf("failed to update, database: %s, collection: %s, device_id: %s, err: %v", dbName, collection, record.DeviceId, err)
		return err
	}
	c.Logger.Infof("succeeded to update, database: %s, collection: %s, device_id: %s", dbName, collection, record.DeviceId)
	return nil
}

func newSort(o Ordered) bson.M {
	// default: ordered by `_id` and descending is true
	if o.Key == "" {
		o.Key = "_id"
	}
	res := bson.M{o.Key: -1}
	if !o.Descending {
		res[o.Key] = 1
	}
	return res
}

func createIndex(ctx context.Context, cli *mongo.Client, db, collection string, indexOptions ...IndexOption) error {
	if len(indexOptions) == 0 {
		// no need to create index
		return nil
	}

	indexes, err := cli.Database(db).Collection(collection).Indexes().ListSpecifications(ctx)
	if err != nil {
		return err
	}
	existedIndexes := make(map[string]bool)
	for _, index := range indexes {
		existedIndexes[index.Name] = true
	}

	for _, index := range indexOptions {
		if existedIndexes[index.Name] {
			// no need to create duplicated
			continue
		}
		if len(index.Fields) == 0 {
			return fmt.Errorf("failed to create index: %s, `field` must be specified", index.Name)
		}
		opts := options.Index().SetName(index.Name)
		if index.Unique {
			opts.SetUnique(true)
		}
		if index.ExpiredTime != 0 {
			opts.SetExpireAfterSeconds(index.ExpiredTime)
		}

		_, err = cli.Database(db).Collection(collection).Indexes().CreateOne(ctx, mongo.IndexModel{Keys: index.Fields, Options: opts})
		if err != nil {
			return err
		}
	}
	return nil
}

func (c *MongoClient) FindManyImagesByProject(project, deviceIds string, filter bson.D) (responseData map[string][]umw.MongoImageInfo, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	dbName := fmt.Sprintf("%s-%s", umw.ImageInfo, ucmd.RenameProjectDB(project))

	devicesList := make([]string, 0)
	if deviceIds != "" {
		devicesList = strings.Split(deviceIds, ",")
	} else {
		devicesList, err = c.Client.Database(dbName).ListCollectionNames(ctx, bson.M{"name": bson.M{"$regex": "(PS|PUS)-NIO-"}})
		if err != nil {
			c.Logger.Errorf("failed to get collections, filter: %v, err: %v", filter, err)
			return
		}
	}

	if len(devicesList) == 0 {
		c.Logger.Warnf("db: %s, collections no found", dbName)
		return
	}

	var mu sync.Mutex
	responseData = make(map[string][]umw.MongoImageInfo)
	execFunc := func(i int) {
		var res []umw.MongoImageInfo
		cur, err := c.Client.Database(dbName).Collection(devicesList[i]).Find(ctx, filter, options.Find().SetSort(bson.M{"image_gen_time": -1}))
		if err != nil {
			c.Logger.Errorf("failed to get image info, collection: %s, filter: %v, err: %v", devicesList[i], filter, err)
			return
		}
		if err = cur.All(ctx, &res); err != nil {
			c.Logger.Errorf("failed to get image info, collection: %s, filter: %v, err: %v", devicesList[i], filter, err)
			return
		}
		mu.Lock()
		if len(res) != 0 {
			if responseData[devicesList[i]] == nil {
				responseData[devicesList[i]] = make([]umw.MongoImageInfo, 0)
			}
			responseData[devicesList[i]] = res
		}
		defer mu.Unlock()
	}
	ucmd.ParallelizeExec(len(devicesList), execFunc, 500)

	if err != nil && len(responseData) != 0 {
		return responseData, nil
	}

	return responseData, err
}

func (c *MongoClient) DownloadImage(filter bson.M, pageSize int64, pageNo int64, collections *[]string) (*[]umw.MongoImage, error, int64) {
	resultImage := make([]umw.MongoImage, 0)

	for _, collectionName := range *collections {
		collection := c.Client.Database(umw.Images).Collection(collectionName)
		ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
		defer cancel()

		cursor, err := collection.Find(ctx, filter)
		images := make([]umw.MongoImage, 0)
		err = cursor.All(ctx, &images)
		if err != nil {
			continue
			// return nil, err
		}
		resultImage = append(resultImage, images...)
	}

	end := int64(len(resultImage))
	if pageNo*pageSize < end {
		end = pageNo * pageSize
	}
	result := resultImage[int((pageNo-1)*pageSize):end]

	return &result, nil, end
}

func (c *MongoClient) GetDownloadCommand() (res model.GetDownloadCommandResponse, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	collection := c.Client.Database("tke").Collection("download_command")
	err = collection.FindOne(ctx, make(bson.M)).Decode(&res)
	if err != nil {
		return
	}
	return
}

func (c *MongoClient) SaveTkeEdgeJoinInfo(req model.TKEEdgeRequest, command string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	collection := c.Client.Database("tke").Collection("join_info")
	request := model.SaveTkeEdgeJoinInfoRequest{
		Project:         req.Project,
		DeviceId:        req.DeviceId,
		SubSystem:       req.SubSystem,
		Interface:       req.Interface,
		DownloadCommand: command,
		CreateTs:        time.Now().UnixMilli(),
	}
	_, err := collection.InsertOne(ctx, request)
	if err != nil {
		return err
	}
	return nil
}
func (c *MongoClient) UpdateTkeEdgeDownloadCommand(oldCommand, command string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	collection := c.Client.Database("tke").Collection("download_command")
	filter := make(bson.M)
	filter["download_command"] = oldCommand
	update := make(bson.M)
	update["$set"] = bson.M{"download_command": command, "create_ts": time.Now().UnixMilli()}
	result := collection.FindOneAndUpdate(ctx, filter, update)
	if result.Err() != nil {
		return result.Err()
	}
	return nil
}

func (c *MongoClient) InsertTkeEdgeDownloadCommand(command string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	collection := c.Client.Database("tke").Collection("download_command")
	_, err := collection.InsertOne(ctx, bson.M{"download_command": command, "create_ts": time.Now().UnixMilli()})
	if err != nil {
		return err
	}
	return nil
}

func (c *MongoClient) UpdateDeviceStatus(dbName, collection string, record umw.MongoDeviceInfo) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	update := bson.M{"$set": bson.M{"updated_time": time.Now().UnixMilli(), "is_login": record.IsLogin}}
	if record.IsActive == true {
		update = bson.M{"$set": bson.M{"updated_time": time.Now().UnixMilli(), "is_active": true, "is_login": record.IsLogin}}
	}
	_, err := c.Client.Database(dbName).Collection(collection).UpdateByID(ctx, record.Id, update)
	if err != nil {
		c.Logger.Errorf("failed to make device active, database: %s, collection: %s, device_id: %s, err: %v", dbName, collection, record.DeviceId, err)
		return err
	}
	c.Logger.Infof("succeeded to make device active, database: %s, collection: %s, device_id: %s", dbName, collection, record.DeviceId)
	return nil
}

func (c *MongoClient) GetDeviceList(project string, device string, fuzzyDeviceId string, fuzzyDeviceIdOrName string, province string, city, area string, pageSize int64, pageNo int64) ([]umw.MongoDeviceInfo, error, int64) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	collection := c.Client.Database(umw.OAuthDB).Collection(umw.DeviceBaseInfo)
	filter := make(bson.M)
	filter["project"] = project
	if ucmd.GetEnv() == "prod" {
		filter["is_active"] = true
	}
	if device != "" {
		//filter["$or"] = []bson.M{{"name": bson.M{"$regex": deviceQuery, "$options": "$i"}}, {"_id": bson.M{"$regex": deviceQuery, "$options": "$i"}}}
		filter["device_id"] = device
	}
	if fuzzyDeviceId != "" {
		filter["device_id"] = bson.M{"$regex": fmt.Sprintf(".*%v.*", fuzzyDeviceId)}
	}
	if fuzzyDeviceIdOrName != "" {
		filter["$or"] = bson.A{
			bson.M{"device_id": bson.M{"$regex": fmt.Sprintf(".*%v.*", fuzzyDeviceIdOrName)}},
			bson.M{"description": bson.M{"$regex": fmt.Sprintf(".*%v.*", fuzzyDeviceIdOrName)}},
		}
	}
	if province != "" {
		filter["region"] = bson.M{"$regex": province}
	}
	if city != "" {
		filter["region"] = bson.M{"$regex": city}
	}
	if area != "" {
		filter["area"] = area
	}
	total, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, err, 0
	}
	cursor, err := collection.Find(ctx, filter, options.Find().SetSkip((pageNo-1)*pageSize).SetLimit(pageSize))
	if err != nil {
		return nil, err, 0
	}
	var res []umw.MongoDeviceInfo
	err = cursor.All(ctx, &res)
	if err != nil {
		return nil, err, 0
	}
	return res, nil, total
}

func (c *MongoClient) GetDeviceListMapping(project string, deviceId ...string) (devicesData []umw.MongoDeviceInfo, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	filter := bson.D{{"project", project}}
	if len(deviceId) != 0 && deviceId[0] != "" {
		filter = append(filter, bson.E{Key: "device_id", Value: deviceId[0]})
	}
	collName := umw.DeviceBaseInfo
	if util.DeviceIsPowerCharger(project) {
		collName = "charger_basic_info"
	}
	cur, err := c.Client.Database(umw.OAuthDB).Collection(collName).Find(
		ctx, filter, options.Find().SetProjection(bson.M{"device_id": 1, "description": 1, "is_active": 1, "_id": 0}))
	if err != nil {
		c.Logger.Errorf("failed to get device basic info mapping, err: %v", err)
		return
	}
	if err = cur.All(ctx, &devicesData); err != nil {
		c.Logger.Errorf("failed to get device basic info mapping, err: %v", err)
		return
	}

	c.Logger.Infof("succeeded to get device basic info mapping, total: %d", len(devicesData))
	return
}

func (c *MongoClient) GetAllDevices() (devicesData []umw.MongoDeviceInfo, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
	defer cancel()

	allDevices := []umw.MongoDeviceInfo{}
	filter := bson.D{}
	Limit := int64(2000)
	Offset := int64(0)
	for {
		cur, err := c.Client.Database("oauth").Collection("device_basic_info").Find(ctx, filter, options.Find().SetSort(bson.M{"_id": -1}).SetSkip(Offset).SetLimit(Limit))
		if err != nil {
			return nil, err
		}
		var devices []umw.MongoDeviceInfo
		if err = cur.All(ctx, &devices); err != nil {
			return nil, err
		}
		allDevices = append(allDevices, devices...)

		if len(devices) < int(Limit) {
			break
		}
		Offset = Offset + int64(len(devices))
	}

	c.Logger.Infof("succeeded to get all device info, total: %d", len(allDevices))
	return allDevices, nil
}

func (c *MongoClient) GetAllChargers() (devicesData []umw.MongoDeviceInfo, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
	defer cancel()

	allDevices := []umw.MongoDeviceInfo{}
	filter := bson.D{}
	Limit := int64(2000)
	Offset := int64(0)
	for {
		cur, err := c.Client.Database(umw.OAuthDB).Collection("charger_basic_info").Find(ctx, filter, options.Find().SetSort(bson.M{"_id": -1}).SetSkip(Offset).SetLimit(Limit))
		if err != nil {
			return nil, err
		}
		var devices []umw.MongoDeviceInfo
		if err = cur.All(ctx, &devices); err != nil {
			return nil, err
		}
		allDevices = append(allDevices, devices...)

		if len(devices) < int(Limit) {
			break
		}
		Offset = Offset + int64(len(devices))
	}

	c.Logger.Infof("succeeded to get all device info, total: %d", len(allDevices))
	return allDevices, nil
}

func (c *MongoClient) GetDeviceFactoryStatus(project string, deviceId ...string) (devicesData []umw.MongoDeviceFactoryInfo, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	filter := bson.D{{"project", project}}
	if len(deviceId) != 0 && deviceId[0] != "" {
		filter = append(filter, bson.E{Key: "device_id", Value: deviceId[0]})
	}
	cur, err := c.Client.Database(umw.FactoryData).Collection(umw.DeviceFactoryInfo).Find(
		ctx, filter, options.Find().SetSort(bson.M{"updated_time": -1}).SetProjection(bson.M{"device_id": 1, "description": 1, "state": 1, "is_factory": 1, "_id": 0}))
	if err != nil {
		c.Logger.Errorf("failed to get device factory info, err: %v", err)
		return
	}
	if err = cur.All(ctx, &devicesData); err != nil {
		c.Logger.Errorf("failed to get device factory info, err: %v", err)
		return
	}

	c.Logger.Infof("succeeded to get device factory info, total: %d", len(devicesData))
	return
}

func (c *MongoClient) UpdateLogFileUploadHistory(dbName, collection, step string, record mmgo.MongoLogFileUploadHistory) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	indexOption := IndexOption{Name: "device_path_combine", Fields: bson.D{{"device_id", -1}, {"file_path", -1}}}
	if err := createIndex(ctx, c.Client, dbName, collection, indexOption); err != nil {
		return fmt.Errorf("create index, err: %v", err)
	}

	filter := bson.D{bson.E{Key: "device_id", Value: record.DeviceId}, bson.E{Key: "file_path", Value: record.FilePath}}
	col := c.Client.Database(dbName).Collection(collection)
	if step == model.Upload {
		// 用户发起上传文件请求
		if record.FileGenStatus == "生成中" {
			record.FileGenTime = time.Now().UnixMilli()
			_, err := col.InsertOne(ctx, record)
			if err != nil {
				c.Logger.Errorf("insert upload log file history, collection: %s, filter: %v, err: %v", collection, filter, err)
				return err
			}
			c.Logger.Infof("succeeded to insert upload log file history, collection: %s, filter: %v", collection, filter)
		} else {
			// 设备侧将日志文件上传至云端存储成功
			filter = append(filter, bson.E{Key: "file_gen_status", Value: "生成中"})
			count, err := col.CountDocuments(ctx, filter)
			if err != nil {
				c.Logger.Errorf("update upload log file history, collection: %s, filter: %v, err: %v", collection, filter, err)
				return err
			}
			// 找到对应记录，则更新file_gen_status
			if count > 0 {
				update := bson.M{"file_gen_status": record.FileGenStatus, "file_upload_time": record.FileUploadTime, "log_url": record.LogUrl}
				if record.AllowDownload {
					update["allow_download"] = record.AllowDownload
				}
				if record.MD5CheckPass != nil {
					update["md5_check_pass"] = record.MD5CheckPass
				}
				_, err := col.UpdateMany(ctx, filter, bson.M{"$set": update})
				if err != nil {
					c.Logger.Errorf("update upload log file history, collection: %s, filter: %v, err: %v", collection, filter, err)
					return err
				}
				c.Logger.Infof("succeeded to update upload log file history, collection: %s, filter: %v", collection, filter)
			} else {
				// 找不到对应记录（可能原因是设备侧直接上传文件，没有通过天宫发起请求），则全量插入数据作为补偿
				record.FileGenTime = time.Now().UnixMilli()
				_, err := col.InsertOne(ctx, record)
				if err != nil {
					c.Logger.Errorf("insert upload log file history, collection: %s, filter: %v, err: %v", collection, filter, err)
					return err
				}
				c.Logger.Infof("succeeded to insert upload log file history, collection: %s, filter: %v", collection, filter)
			}
		}
	} else if step == model.Approval {
		// workflow审批通过，允许用户下载，此时需要将allow_download置为true
		filter = append(append(append(filter,
			bson.E{Key: "operator", Value: record.Operator}),
			bson.E{Key: "file_gen_time", Value: record.FileGenTime}),
			bson.E{Key: "file_gen_status", Value: "已上传"})
		count, err := col.CountDocuments(ctx, filter)
		if err != nil {
			c.Logger.Errorf("update upload log file history, collection: %s, filter: %v, err: %v", collection, filter, err)
			return err
		}
		if count == 0 {
			c.Logger.Errorf("insert upload log file history, collection: %s, filter: %v, err: no record found", collection, filter)
			return errors.New("no record found")
		}
		// 找到对应记录，则更新allow_download
		_, err = col.UpdateMany(ctx, filter, bson.M{"$set": bson.M{"allow_download": record.AllowDownload}})
		if err != nil {
			c.Logger.Errorf("update upload log file history, collection: %s, filter: %v, err: %v", collection, filter, err)
			return err
		}
		c.Logger.Infof("succeeded to update upload log file history, collection: %s, filter: %v", collection, filter)
	}

	return nil
}

func (c *MongoClient) UpdateLogInfoDirectoryTree(dbName, collection string, record model.SyncLogFilePathParams) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	indexOption := IndexOption{Name: "device_id", Fields: bson.D{{"device_id", 1}}, Unique: true}
	if err := createIndex(ctx, c.Client, dbName, collection, indexOption); err != nil {
		return fmt.Errorf("create index, err: %v", err)
	}

	filter := bson.M{"device_id": record.DeviceId}
	_, err := c.Client.Database(dbName).Collection(collection).ReplaceOne(ctx, filter, record, options.Replace().SetUpsert(true))
	if err != nil {
		c.Logger.Errorf("update log directory tree, collection: %s, filter: %v, err: %v", collection, filter, err)
		return err
	}
	c.Logger.Infof("succeeded to update log directory tree, collection: %s, filter: %v", collection, filter)
	return nil
}

func (c *MongoClient) UpdateSnapshotUploadHistory(dbName, collectionName string, record mmgo.MongoLogFileUploadHistory) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	indexOptions := []IndexOption{
		{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 7 * 24 * 3600},
		{Name: "device_path_combine", Fields: bson.D{{"device_id", -1}, {"file_path", -1}}},
	}
	if err := createIndex(ctx, c.Client, dbName, collectionName, indexOptions...); err != nil {
		return fmt.Errorf("create index, err: %v", err)
	}

	filter := bson.D{bson.E{Key: "device_id", Value: record.DeviceId}, bson.E{Key: "file_path", Value: record.FilePath}}
	col := c.Client.Database(dbName).Collection(collectionName)
	if record.FileGenStatus == "未生成" {
		count, err := col.CountDocuments(ctx, filter)
		if err != nil {
			c.Logger.Errorf("count snapshot err: %s, filter: %v", err.Error(), filter)
			return err
		}
		// 如果已有记录，则不插入
		if count > 0 {
			return nil
		}
		_, err = col.InsertOne(ctx, record)
		if err != nil {
			c.Logger.Errorf("insert upload snapshot history, collection: %s, filter: %v, err: %v", collectionName, filter, err)
			return err
		}
	} else if record.FileGenStatus == "生成中" {
		filter = append(filter, bson.E{Key: "file_gen_status", Value: "未生成"})
		update := bson.M{"file_gen_status": record.FileGenStatus, "file_gen_time": record.FileGenTime, "date": time.UnixMilli(record.FileGenTime)}
		res := col.FindOneAndUpdate(ctx, filter, bson.M{"$set": update})
		if res.Err() != nil && res.Err() != mongo.ErrNoDocuments {
			c.Logger.Errorf("update snapshot info err: %s, filter: %v", res.Err().Error(), filter)
			return res.Err()
		}
	} else {
		// 设备侧将日志文件上传至云端存储成功
		filter = append(filter, bson.E{Key: "file_gen_status", Value: "生成中"})
		update := bson.M{"file_gen_status": record.FileGenStatus, "file_upload_time": record.FileUploadTime, "log_url": record.LogUrl, "allow_download": record.AllowDownload}
		res := col.FindOneAndUpdate(ctx, filter, bson.M{"$set": update})
		if res.Err() != nil && res.Err() != mongo.ErrNoDocuments {
			c.Logger.Errorf("update snapshot info err: %s, filter: %s", res.Err().Error(), filter)
			return res.Err()
		}
	}
	return nil
}

func (c *MongoClient) GetSnapshotList(dbName, collectionName string, record model.SnapshotListParams) ([]mmgo.SnapshotInfo, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	ts := time.UnixMilli(record.Ts)
	startTs, endTs := ts.Add(-time.Minute*5), ts.Add(time.Minute*5)
	filter := bson.D{
		{"device_id", record.DeviceId},
		{"algorithm_name", record.AlgorithmName},
		{"create_ts", bson.M{"$gte": startTs.UnixMilli(), "$lte": endTs.UnixMilli()}},
	}
	cursor, err := c.Client.Database(dbName).Collection(collectionName).Find(ctx, filter)
	if err != nil {
		c.Logger.Errorf("get snapshot list err: %s", err.Error())
		return nil, err
	}
	var snapshotList []mmgo.SnapshotInfo
	if err = cursor.All(ctx, &snapshotList); err != nil {
		c.Logger.Errorf("parse cursor err: %s", err.Error())
		return nil, err
	}
	return snapshotList, nil
}

func (c *MongoClient) GetSnapshotInfoList(dbName, collectionName string, snapshotList []string, ordered Ordered) (snapshotInfoList []mmgo.MongoLogFileUploadHistory, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if len(snapshotList) == 0 {
		return
	}
	filter := bson.M{"file_path": bson.M{"$in": snapshotList}}
	cursor, err := c.Client.Database(dbName).Collection(collectionName).Find(ctx, filter, options.Find().SetSort(newSort(ordered)))
	if err != nil {
		c.Logger.Errorf("get snapshot info list err: %s", err.Error())
		return
	}
	if err = cursor.All(ctx, &snapshotInfoList); err != nil {
		c.Logger.Errorf("parse cursor err: %s", err.Error())
		return
	}
	return
}

func (c *MongoClient) InsertApprovalHistory(dbName, collection, userId, flowInstanceId string, content model.ApprovalContent) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	var details string
	jsonData, err := json.Marshal(content.RequestFiles)
	if err != nil {
		c.Logger.Errorf("failed to marshal requests_files, err: %v", err)
	} else {
		details = string(jsonData)
	}
	record := bson.M{"description": content.Description, "device_id": content.DeviceId, "details": details, "project": content.Project,
		"reason": content.Reason, "user_id": userId, "flow_instance_id": flowInstanceId, "status": "processing",
		"updated_time": time.Now().UnixMilli(), "current_node_name": "流程发起成功", "previous_node_name": "流程发起成功"}
	_, err = c.Client.Database(dbName).Collection(collection).InsertOne(ctx, record)
	if err != nil {
		c.Logger.Errorf("failed to insert, database: %s, collection: %s, flow_instance_id: %s, err: %v", dbName, collection, flowInstanceId, err)
		return err
	}
	c.Logger.Infof("succeeded to insert, database: %s, collection: %s, flow_instance_id: %s", dbName, collection, flowInstanceId)
	return nil
}

func (c *MongoClient) UserAuthApprovalFinished(userId string, role ...int) (ok bool, approvers []string, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	days7BeforeTS := time.Now().UnixMilli() - 7*24*3600*1000
	filter := bson.M{
		"user_id":      userId,
		"status":       bson.M{"$nin": []string{"success", "deny"}},
		"created_time": bson.M{"$gte": days7BeforeTS},
	}
	if len(role) == 1 {
		filter["role"] = role[0]
	} else if len(role) > 1 {
		filter["role"] = bson.M{"$in": role}
	}
	var res umw.MongoUserAuthApproval
	err = c.Client.Database(umw.OAuthDB).Collection(umw.UserAuthApproval).FindOne(ctx, filter,
		options.FindOne().SetProjection(bson.M{"_id": 0, "approvers": 1})).Decode(&res)
	if err == mongo.ErrNoDocuments {
		c.Logger.Infof("user role application finished, filter: %v", filter)
		return true, nil, nil
	}
	if err == nil {
		approvers = res.Approvers
		c.Logger.Warnf("user role application is in process, filter: %v", filter)
	} else {
		c.Logger.Errorf("cannot confirm user role application status, filter: %v, err: %v", filter, err)
	}
	return false, approvers, err
}

func (c *MongoClient) DeviceAuthApprovalFinished(userId, identifier string) (ok bool, approvers []string, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	days7BeforeTS := time.Now().UnixMilli() - 7*24*3600*1000
	filter := bson.M{
		"user_id":           userId,
		"device_identifier": identifier,
		"status":            bson.M{"$nin": []string{"success", "deny"}},
		"created_time":      bson.M{"$gte": days7BeforeTS},
	}
	var res umw.MongoDeviceAuthApproval
	err = c.Client.Database(umw.FactoryData).Collection(umw.DeviceAuthApproval).FindOne(ctx, filter, options.FindOne().SetProjection(bson.M{"_id": 0, "approvers": 1})).Decode(&res)
	if err == mongo.ErrNoDocuments {
		c.Logger.Infof("device identifier application finished, filter: %v", filter)
		return true, nil, nil
	}
	if err == nil {
		approvers = res.Approvers
		c.Logger.Warnf("device identifier application is in process, filter: %v", filter)
	} else {
		c.Logger.Errorf("cannot confirm device identifier application status, filter: %v, err: %v", filter, err)
	}
	return false, approvers, err
}

func (c *MongoClient) UpdateApprovalHistoryV1(dbName, collection string, content model.ApprovalCallback) (umw.MongoLogApprovalHistory, error) {
	var responseData umw.MongoLogApprovalHistory
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	filter := bson.M{"flow_instance_id": content.FlowInstanceId}
	update := bson.M{"$set": bson.M{"status": content.Status,
		"current_node_name": content.CurrentNodeName, "previous_node_name": content.PreviousNodeName,
		"updated_time": time.Now().UnixMilli()}}
	err := c.Client.Database(dbName).Collection(collection).FindOneAndUpdate(ctx, filter, update).Decode(&responseData)
	if err != nil {
		c.Logger.Errorf("update approval history, collection: %s, flow_instance_id: %s, err: %v", collection, content.FlowInstanceId, err)
		return responseData, err
	}
	c.Logger.Infof("succeeded to update approval history, collection: %s, flow_instance_id: %s", collection, content.FlowInstanceId)
	return responseData, nil
}

func (c *MongoClient) GetFactoryTestInfo(req model.GetFactoryTestInfoRequest) (res []umw.MongoTestBasicInfo, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	filter := make(bson.M)
	filter["test_time"] = bson.M{"$gte": req.StartTs, "$lte": req.EndTs}
	cur, err := c.Client.Database(umw.TestData).Collection(umw.TestBasicInfo).Find(ctx, filter)
	if err != nil {
		c.Logger.Errorf("get log file directory tree, database: %s, collection: %s, err: %v", "testData", "test_basic_info", err)
		return
	}

	if err = cur.All(ctx, &res); err != nil {
		c.Logger.Errorf("get log file directory tree, database: %s, collection: %s, err: %v", "testData", "test_basic_info", err)
		return
	}
	return
}

func (c *MongoClient) getID(dbName, collection string) (int64, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	var result umw.MongoIDGen
	err := c.Client.Database(dbName).Collection(umw.IDGen).FindOneAndUpdate(
		ctx,
		bson.M{"name": collection},
		bson.M{"$inc": bson.M{"next_id": 1}}).Decode(&result)
	if err == mongo.ErrNoDocuments {
		_, err = c.Client.Database(dbName).Collection(umw.IDGen).InsertOne(ctx, bson.M{"name": collection, "next_id": 2})
		if err != nil {
			return -1, err
		}
		return 1, err
	} else if err != nil {
		return -1, err
	}
	return result.NextId, err
}

func (c *MongoClient) InsertPublishVersion(dbName, collectionPublishVersion, collectionVersionInfo, project string, version model.VersionBase) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	indexOption := IndexOption{Name: "publish_ts", Fields: bson.D{{"publish_ts", 1}}}
	if err := createIndex(ctx, c.Client, dbName, collectionPublishVersion, indexOption); err != nil {
		return fmt.Errorf("create index, err: %v", err)
	}

	// 先判断同一个发布时间是否已经有大版本
	conflict, err := c.containDocument(dbName, collectionPublishVersion, bson.D{
		{"publish_ts", version.PublishTs},
		{"project", project},
	})
	if err != nil {
		c.Logger.Errorf("check version publish ts err: %v, db %s, collection: %s", err, dbName, collectionPublishVersion)
		return err
	}
	if conflict {
		c.Logger.Warnf("cannot insert duplicate publish_ts: %d", version.PublishTs)
		return errors.New(fmt.Sprintf("cannot insert duplicate publish_ts: %d", version.PublishTs))
	}
	// 写入大版本信息到publish-version
	publishVersionId, err := c.getID(dbName, collectionPublishVersion)
	if err != nil {
		c.Logger.Errorf("get id, db: %s, collection: %s, err: %v", dbName, collectionPublishVersion, err)
		return err
	}
	mongoPublishVersion := umw.MongoPublishVersion{
		Id:        publishVersionId,
		Project:   project,
		PublishTs: version.PublishTs,
		Version:   version.Version,
	}
	_, err = c.Client.Database(dbName).Collection(collectionPublishVersion).InsertOne(ctx, mongoPublishVersion)
	if err != nil {
		c.Logger.Errorf("insert new publish version, db: %s, collection: %s, version id: %d, err: %v", dbName, collectionPublishVersion, publishVersionId, err)
		return err
	}
	// 根据上一个大版本的算法列表生成新版本的算法列表
	lastVersion, err := c.getNeighborPublishVersion(dbName, collectionPublishVersion, mongoPublishVersion, -1)
	if err != nil {
		c.Logger.Errorf("get last publish version err: %v, db: %s, collection: %s", err, dbName, collectionPublishVersion)
		return err
	}
	if len(lastVersion) == 1 {
		// 获取上一个大版本的算法信息
		filter := make(bson.M)
		filter["publish_version_id"] = lastVersion[0].Id
		var cursor *mongo.Cursor
		cursor, err = c.Client.Database(dbName).Collection(collectionVersionInfo).Find(ctx, filter)
		if err != nil {
			c.Logger.Errorf("get last version info err: %v", err)
			return err
		}
		var lastVersionInfos []umw.MongoVersionInfo
		if err = cursor.All(ctx, &lastVersionInfos); err != nil {
			c.Logger.Errorf("decode from cursor err: %v", err)
			return err
		}
		// 复制到新大版本
		var newVersionInfos []interface{}
		for _, lastVersionInfo := range lastVersionInfos {
			var algorithmId int64
			algorithmId, err = c.getID(dbName, collectionVersionInfo)
			if err != nil {
				c.Logger.Errorf("get id, db: %s, collection: %s, err: %v", dbName, collectionVersionInfo, err)
				return err
			}
			newVersionInfos = append(newVersionInfos, umw.MongoVersionInfo{
				Id:               algorithmId,
				PublishVersionId: publishVersionId,
				AlgorithmName:    lastVersionInfo.AlgorithmName,
				AlgorithmVersion: lastVersionInfo.AlgorithmVersion,
				Shadow:           lastVersionInfo.Shadow,
				UpdatePoint:      "0,0,0",
			})
		}
		if len(newVersionInfos) > 0 {
			err = c.NewMongoEntry().InsertMany(dbName, collectionVersionInfo, newVersionInfos, []IndexOption{
				{Name: "algorithm_name"},
				{Name: "publish_version_id"},
			}...)
			if err != nil {
				c.Logger.Errorf("insert new version info err: %v, db: %s, collection: %s", err, dbName, collectionVersionInfo)
				return err
			}
		}
	}
	c.Logger.Infof("succeeded to insert new publish version, db: %s, collection: %s & %s, version id: %d", dbName, collectionPublishVersion, collectionVersionInfo, publishVersionId)
	return nil
}

func (c *MongoClient) UpdatePublishVersion(dbName, collectionPublishVersion, collectionVersionInfo, project string, versionInfo model.VersionInfo) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	indexOptions := []IndexOption{
		{Name: "algorithm_name", Fields: bson.D{{"algorithm_name", 1}}},
		{Name: "publish_version_id", Fields: bson.D{{"publish_version_id", 1}}},
	}
	if err := createIndex(ctx, c.Client, dbName, collectionVersionInfo, indexOptions...); err != nil {
		return fmt.Errorf("create index, err: %v", err)
	}

	// 先判断修改后的发布时间是否与数据库冲突
	conflict, err := c.containDocument(dbName, collectionPublishVersion, bson.D{
		{"project", project},
		{"publish_ts", versionInfo.PublishTs},
		{"_id", bson.D{{"$ne", versionInfo.PublishVersionId}}},
	})
	if err != nil {
		c.Logger.Errorf("check version publish ts err: %v, db %s, collection: %s", err, dbName, collectionPublishVersion)
		return err
	}
	if conflict {
		c.Logger.Warnf("cannot insert duplicate publish_ts: %d", versionInfo.PublishTs)
		return errors.New(fmt.Sprintf("cannot insert duplicate publish_ts: %d", versionInfo.PublishTs))
	}
	// 修改版本信息
	filter := bson.D{{"_id", versionInfo.PublishVersionId}}
	update := bson.D{{"$set", bson.D{{"publish_ts", versionInfo.PublishTs}, {"version", versionInfo.Version}}}}
	c.Client.Database(dbName).Collection(collectionPublishVersion).FindOneAndUpdate(ctx, filter, update)
	// 更新相邻版本的算法高亮
	// 查询当前版本已有的所有算法
	collection := c.Client.Database(dbName).Collection(collectionVersionInfo)
	cursor, err := collection.Find(ctx, bson.M{"publish_version_id": versionInfo.PublishVersionId})
	if err != nil {
		c.Logger.Errorf(err.Error())
		return err
	}
	var algorithmDataList []umw.MongoVersionInfo
	if err = cursor.All(ctx, &algorithmDataList); err != nil {
		c.Logger.Errorf("parse cursor err: %v", err)
		return err
	}
	// 更新算法高亮
	for _, algoData := range algorithmDataList {
		algoModel := model.VersionUpdateAlgorithmParams{
			AlgorithmId:      algoData.Id,
			AlgorithmVersion: algoData.AlgorithmVersion,
			Shadow:           algoData.Shadow,
		}
		if err = c.updateAlgorithmVersion(dbName, collectionPublishVersion, collectionVersionInfo, versionInfo.PublishVersionId, algoModel); err != nil {
			c.Logger.Errorf("update algorithm highlight err: %v", err)
			return err
		}
	}
	// 新增算法
	existAlgorithms := make([]string, 0)
	newAlgorithms := make([]string, 0)
	for _, algorithmModel := range versionInfo.Models {
		// 先判断数据库里同一个版本下是否已经存在同样的算法名
		conflict, err = c.containDocument(dbName, collectionVersionInfo, bson.D{
			{"algorithm_name", algorithmModel.AlgorithmName},
			{"publish_version_id", versionInfo.PublishVersionId},
		})
		if err != nil {
			c.Logger.Errorf("check algorithm name err: %v, db: %s, collection: %s", err, dbName, collectionVersionInfo)
			return err
		}
		if conflict {
			existAlgorithms = append(existAlgorithms, algorithmModel.AlgorithmName)
			continue
		} else {
			newAlgorithms = append(newAlgorithms, algorithmModel.AlgorithmName)
		}
		// 不存在相同算法名，则写入数据库
		var algorithmId int64
		algorithmId, err = c.getID(dbName, collectionVersionInfo)
		if err != nil {
			c.Logger.Errorf("get id, db: %s, collection: %s, err: %v", dbName, collectionVersionInfo, err)
			return err
		}
		_, err = collection.InsertOne(ctx, umw.MongoVersionInfo{
			Id:               algorithmId,
			PublishVersionId: versionInfo.PublishVersionId,
			AlgorithmName:    algorithmModel.AlgorithmName,
			AlgorithmVersion: algorithmModel.AlgorithmVersion,
			Shadow:           algorithmModel.Shadow,
			UpdatePoint:      "1,1,1",
		})
		if err != nil {
			c.Logger.Errorf("insert algorithm, db: %s, collection: %s, algorithm id: %d, err: %v", dbName, collectionVersionInfo, algorithmId, err)
			return err
		}
		// 将空算法写入其他满足“发布时间>当前时间”的大版本
		filter = bson.D{
			{"project", project},
			{"_id", bson.D{{"$ne", versionInfo.PublishVersionId}}},
			{"publish_ts", bson.D{{"$gt", time.Now().UnixMilli()}}},
		}
		cursor, err = c.Client.Database(dbName).Collection(collectionPublishVersion).Find(ctx, filter)
		if err != nil {
			c.Logger.Errorf("get publish version err: %v, db: %s, collection: %s", err, dbName, collectionPublishVersion)
			return err
		}
		var publishVersions []umw.MongoPublishVersion
		if err = cursor.All(ctx, &publishVersions); err != nil {
			c.Logger.Errorf("decode from cursor err: %v", err)
			return err
		}
		if len(publishVersions) == 0 {
			continue
		}
		var newEmptyAlgorithms []interface{}
		for _, version := range publishVersions {
			algorithmId, err = c.getID(dbName, collectionVersionInfo)
			if err != nil {
				c.Logger.Errorf("get id, db: %s, collection: %s, err: %v", dbName, collectionVersionInfo, err)
				return err
			}
			newEmptyAlgorithm := umw.MongoVersionInfo{
				Id:               algorithmId,
				PublishVersionId: version.Id,
				AlgorithmName:    algorithmModel.AlgorithmName,
			}
			if version.PublishTs > versionInfo.PublishTs {
				newEmptyAlgorithm.AlgorithmVersion = algorithmModel.AlgorithmVersion
				newEmptyAlgorithm.UpdatePoint = "0,0,0"
				newEmptyAlgorithm.Shadow = algorithmModel.Shadow
			}
			newEmptyAlgorithms = append(newEmptyAlgorithms, newEmptyAlgorithm)
		}
		_, err = collection.InsertMany(ctx, newEmptyAlgorithms)
		if err != nil {
			c.Logger.Errorf("insert many empty algorithms err: %v, db: %s, collection: %s", err, dbName, collectionVersionInfo)
			return err
		}
	}
	if len(existAlgorithms) != 0 {
		c.Logger.Warnf("same algorithms exist, version id: %d, algorithms: %v", versionInfo.PublishVersionId, existAlgorithms)
		return errors.New(fmt.Sprintf("%s:%s:%s", model.ErrDuplicateAlgorithm, strings.Join(existAlgorithms, ","), strings.Join(newAlgorithms, ",")))
	}
	c.Logger.Infof("succeeded to update publish version, db: %s, collection: %s", dbName, collectionVersionInfo)
	return nil
}

func (c *MongoClient) GetAlgorithmData(dbName, collectionPublishVersion, collectionVersionInfo, project string, commonUri model.CommonUriParam) (algorithmData []model.VersionAlgorithmData, total int64, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 根据project到publish-version查询一共有多少个大版本
	filter := make(bson.M)
	filter["project"] = project
	total, err = c.Client.Database(dbName).Collection(collectionPublishVersion).CountDocuments(ctx, filter)
	if err != nil {
		c.Logger.Errorf("count versions err: %v, db: %s, collection: %s", err, dbName, collectionPublishVersion)
		return
	}
	// 根据project到publish-version中获取指定page上的size条数据
	cursor, err := c.Client.Database(dbName).Collection(collectionPublishVersion).Aggregate(ctx, mongo.Pipeline{
		bson.D{{"$match", bson.D{{"project", project}}}},
		bson.D{{"$sort", bson.D{{"publish_ts", -1}}}},
		bson.D{{"$skip", (commonUri.Page - 1) * commonUri.Size}},
		bson.D{{"$limit", commonUri.Size}},
	})
	if err != nil {
		c.Logger.Errorf("get publish version id error: %v, db: %s, collection: %s", err, dbName, collectionPublishVersion)
		return
	}
	var versions []umw.MongoPublishVersion
	if err = cursor.All(ctx, &versions); err != nil {
		c.Logger.Errorf("decode from cursor err: %v", err)
		return
	}
	// 根据publish_version_id到version-info中获取算法信息
	collection := c.Client.Database(dbName).Collection(collectionVersionInfo)
	for _, version := range versions {
		cursor, err = collection.Find(ctx, bson.M{"publish_version_id": version.Id})
		if err != nil {
			c.Logger.Errorf("get version info err: %v, db: %s, collection: %s", err, dbName, collectionVersionInfo)
			return
		}
		var algorithms []umw.MongoVersionInfo
		if err = cursor.All(ctx, &algorithms); err != nil {
			c.Logger.Errorf("decode from cursor err: %v", err)
			return
		}
		var models []model.VersionAlgorithmModel
		updateCount := 0
		for _, algorithm := range algorithms {
			models = append(models, model.VersionAlgorithmModel{
				AlgorithmId:      algorithm.Id,
				AlgorithmName:    algorithm.AlgorithmName,
				AlgorithmVersion: algorithm.AlgorithmVersion,
				Shadow:           algorithm.Shadow,
				UpdatePoint:      algorithm.UpdatePoint,
				HasTestReport:    algorithm.HasTestReport,
				TestReportURL:    algorithm.TestReportURL,
			})
			// 根据UpdatePoint个数计算updateCount
			if len(algorithm.UpdatePoint) > 0 && algorithm.UpdatePoint != "0,0,0" {
				updateCount += 1
			}
		}
		algorithmData = append(algorithmData, model.VersionAlgorithmData{
			VersionInfo: model.VersionInfo{
				VersionBase: model.VersionBase{
					PublishTs: version.PublishTs,
					Version:   version.Version,
				},
				PublishVersionId: version.Id,
				Models:           models,
			},
			CanEdit:     time.Now().UnixMilli() < version.PublishTs,
			UpdateCount: updateCount,
		})
	}
	return
}

func (c *MongoClient) GetAlgorithmNames(dbName, collectionPublishVersion, collectionVersionInfo, project string) (algorithmNames []string, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 获取所有对应project的publish_version_id
	match := bson.D{}
	if project != "" {
		match = bson.D{{"project", project}}
	}
	cursor, err := c.Client.Database(dbName).Collection(collectionPublishVersion).Aggregate(ctx, mongo.Pipeline{
		bson.D{{"$match", match}},
		bson.D{{"$project", bson.D{{"_id", 1}}}},
	})
	type idResult struct {
		Id int64 `bson:"_id"`
	}
	var idResults []idResult
	if err = cursor.All(ctx, &idResults); err != nil {
		c.Logger.Errorf("decode from cursor err: %v", err)
		return
	}
	algorithmIds := make([]int64, 0)
	for _, id := range idResults {
		algorithmIds = append(algorithmIds, id.Id)
	}
	// 获取算法名全表
	filter := bson.D{{"publish_version_id", bson.D{{"$in", algorithmIds}}}}
	algorithmsRaw, err := c.Client.Database(dbName).Collection(collectionVersionInfo).Distinct(ctx, "algorithm_name", filter)
	if err != nil {
		c.Logger.Errorf("get distinct algorithm_name err: %v", err)
		return
	}
	for _, algorithmName := range algorithmsRaw {
		algorithmNames = append(algorithmNames, algorithmName.(string))
	}
	return
}

func (c *MongoClient) GetPublishTimestamps(dbName, collection string) (timestamps []int64, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	timestampsRaw, err := c.Client.Database(dbName).Collection(collection).Distinct(ctx, "publish_ts", bson.D{})
	if err != nil {
		c.Logger.Errorf("get distinct publish_ts err: %v", err)
		return
	}
	for _, ts := range timestampsRaw {
		tsInt, ok := ts.(int64)
		if !ok {
			c.Logger.Errorf("fail to convert ts to int64 from type: %v", reflect.TypeOf(ts))
			continue
		}
		timestamps = append(timestamps, tsInt)
	}
	return
}

func (c *MongoClient) UpdateAlgorithmData(dbName, collectionPublishVersion, collectionVersionInfo string, publishVersionId int64, algorithmModel model.VersionUpdateAlgorithmParams) error {
	err := c.updateAlgorithmVersion(dbName, collectionPublishVersion, collectionVersionInfo, publishVersionId, algorithmModel)
	if err != nil {
		c.Logger.Errorf("upload algorithm data err: %v", err)
		return err
	}

	c.Logger.Infof("succeeded to update algorithm data, db: %s, collection: %s", dbName, collectionVersionInfo)
	return nil
}

func (c *MongoClient) updateAlgorithmVersion(dbName, collectionPublishVersion, collectionVersionInfo string, publishVersionId int64, algorithmModel model.VersionUpdateAlgorithmParams) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 获取目标版本的发布时间
	var currentVersion umw.MongoPublishVersion
	err := c.Client.Database(dbName).Collection(collectionPublishVersion).FindOne(ctx, bson.M{"_id": publishVersionId}).Decode(&currentVersion)
	if err != nil {
		c.Logger.Errorf("decode publish version err: %v, db: %s, collection: %s", err, dbName, collectionPublishVersion)
		return err
	}
	// 获取algorithmId对应的版本信息
	var currentVersionInfo umw.MongoVersionInfo
	err = c.Client.Database(dbName).Collection(collectionVersionInfo).FindOne(ctx, bson.M{"_id": algorithmModel.AlgorithmId}).Decode(&currentVersionInfo)
	if err != nil {
		c.Logger.Errorf("get version info err: %v, db: %s, collection: %s", err, dbName, collectionVersionInfo)
		return err
	}
	// 获取上一个大版本
	lastVersion, err := c.getNeighborPublishVersion(dbName, collectionPublishVersion, currentVersion, -1)
	if err != nil {
		c.Logger.Errorf("get last publish version err: %v, db: %s, collection: %s", err, dbName, collectionPublishVersion)
		return err
	}
	// 找到上一个大版本的同一个算法，比较算法版本号
	updatePoint := "1,1,1"
	var lastAlgorithmModel umw.MongoVersionInfo
	if len(lastVersion) == 1 {
		filter := make(bson.M)
		filter["publish_version_id"] = lastVersion[0].Id
		filter["algorithm_name"] = currentVersionInfo.AlgorithmName
		err = c.Client.Database(dbName).Collection(collectionVersionInfo).FindOne(ctx, filter).Decode(&lastAlgorithmModel)
		if err != nil {
			if err != mongo.ErrNoDocuments {
				c.Logger.Errorf("decode version info err: %v, db: %s, collection: %s", err, dbName, collectionVersionInfo)
				return err
			}
		} else {
			updatePoint = util.CalculateUpdatePoint(algorithmModel.AlgorithmVersion, lastAlgorithmModel.AlgorithmVersion)
		}
	}
	// 更新数据库
	update := bson.D{
		{
			"$set", bson.D{
				{"algorithm_version", algorithmModel.AlgorithmVersion},
				{"shadow", algorithmModel.Shadow},
				{"update_point", updatePoint},
			},
		},
	}
	var result umw.MongoVersionInfo
	err = c.Client.Database(dbName).Collection(collectionVersionInfo).FindOneAndUpdate(ctx, bson.M{"_id": algorithmModel.AlgorithmId}, update).Decode(&result)
	if err != nil {
		c.Logger.Errorf("update algorithm data err: %v, db: %s, collection: %s", err, dbName, collectionVersionInfo)
		return err
	}

	// 获取下一个大版本
	nextVersion, err := c.getNeighborPublishVersion(dbName, collectionPublishVersion, currentVersion, 1)
	if err != nil {
		c.Logger.Errorf("get next publish version err: %v, db: %s, collection: %s", err, dbName, collectionPublishVersion)
		return err
	}
	if len(nextVersion) == 1 {
		var nextAlgorithmModel umw.MongoVersionInfo
		filter := make(bson.M)
		filter["publish_version_id"] = nextVersion[0].Id
		filter["algorithm_name"] = currentVersionInfo.AlgorithmName
		err = c.Client.Database(dbName).Collection(collectionVersionInfo).FindOne(ctx, filter).Decode(&nextAlgorithmModel)
		if err != nil {
			c.Logger.Errorf("decode version info err: %v, db: %s, collection: %s", err, dbName, collectionVersionInfo)
			return err
		}
		updatePoint = util.CalculateUpdatePoint(nextAlgorithmModel.AlgorithmVersion, algorithmModel.AlgorithmVersion)
		err = c.Client.Database(dbName).Collection(collectionVersionInfo).FindOneAndUpdate(
			ctx,
			bson.M{"_id": nextAlgorithmModel.Id},
			bson.D{{"$set", bson.D{{"update_point", updatePoint}}}}).Decode(&result)
		if err != nil {
			c.Logger.Errorf("update algorithm data err: %v, db: %s, collection: %s", err, dbName, collectionVersionInfo)
			return err
		}
	}
	return nil
}

type DataVersionSort []umw.MongoDataVersioningData

func (d DataVersionSort) Len() int { return len(d) }

func (d DataVersionSort) Less(i, j int) bool {
	return d[i].PublishVersion.PublishTs > d[j].PublishVersion.PublishTs
}

func (d DataVersionSort) Swap(i, j int) { d[i], d[j] = d[j], d[i] }

func (c *MongoClient) GetDataVersioningData(dbName, collectionPublishVersion, collectionVersionInfo string, dataVersioningParams model.DataVersioningParams) (dataVersioningData []model.DataVersioningData, total int, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	var results DataVersionSort
	// 连表查询
	lookupValue := bson.D{
		{"from", collectionPublishVersion},
		{"localField", "publish_version_id"},
		{"foreignField", "_id"},
		{"as", "publish_version"},
	}
	unwindValue := bson.D{
		{"path", "$publish_version"},
		{"preserveNullAndEmptyArrays", true},
	}
	matchValue := bson.D{}
	matchValue = append(matchValue, bson.E{Key: "algorithm_version", Value: bson.D{{"$ne", ""}}})
	if dataVersioningParams.Algorithms != "" {
		algorithms := strings.Split(dataVersioningParams.Algorithms, ",")
		matchValue = append(matchValue, bson.E{Key: "algorithm_name", Value: bson.D{{"$in", algorithms}}})
	}
	if dataVersioningParams.PublishTs != "" {
		timestampsRaw := strings.Split(dataVersioningParams.PublishTs, ",")
		timestamps := make([]int64, 0)
		for _, tsRaw := range timestampsRaw {
			var ts int
			ts, err = strconv.Atoi(tsRaw)
			if err != nil {
				c.Logger.Errorf("parse uri publish_ts err: %v", err)
				return
			}
			timestamps = append(timestamps, int64(ts))
		}
		matchValue = append(matchValue, bson.E{Key: "publish_version.publish_ts", Value: bson.D{{"$in", timestamps}}})
	}
	matchValue = append(matchValue, bson.E{Key: "update_point", Value: bson.D{{"$ne", "0,0,0"}}})
	switch dataVersioningParams.IfQM {
	case 0:
		cursor, err := c.Client.Database(dbName).Collection(collectionVersionInfo).Aggregate(ctx, mongo.Pipeline{
			bson.D{{"$lookup", lookupValue}},
			bson.D{{"$unwind", unwindValue}},
			bson.D{{"$match", matchValue}},
		})
		if err != nil {
			c.Logger.Errorf("get data versioning data err: %v, db: %s, collection publish version: %s, collection version info: %s", err, dbName, collectionPublishVersion, collectionVersionInfo)
			break
		}
		if err = cursor.All(ctx, &results); err != nil {
			c.Logger.Errorf("decode data versioning data err: %v, db: %s, collection publish version: %s, collection version info: %s", err, dbName, collectionPublishVersion, collectionVersionInfo)
			break
		}
		cursorQM, err := c.Client.Database(dbName).Collection("qm_dataset").Aggregate(ctx, mongo.Pipeline{
			bson.D{{"$match", matchValue}}})
		if err != nil {
			c.Logger.Errorf("get data versioning data err: %v, db: %s, collection publish version: %s, collection version info: %s", err, dbName, collectionPublishVersion, collectionVersionInfo)
			break
		}
		for cursorQM.TryNext(ctx) {
			var curr umw.QMDataset
			err = cursorQM.Decode(&curr)
			if err != nil {
				continue
			}
			prev := umw.MongoDataVersioningData{
				Id:               curr.Id,
				AlgorithmName:    curr.AlgorithmName,
				AlgorithmVersion: curr.AlgorithmVersion,
				PublishVersion:   umw.MongoPublishVersion{PublishTs: curr.PublishTs},
				CreateTs:         curr.CreateTs,
				FileInfo:         curr.FileInfo,
				HasQMDataset:     true,
			}
			results = append(results, prev)
		}
	case 1:
		cursorQM, err := c.Client.Database(dbName).Collection("qm_dataset").Aggregate(ctx, mongo.Pipeline{
			bson.D{{"$match", matchValue}}})
		if err != nil {
			c.Logger.Errorf("get data versioning data err: %v, db: %s, collection publish version: %s, collection version info: %s", err, dbName, collectionPublishVersion, collectionVersionInfo)
			break
		}
		for cursorQM.TryNext(ctx) {
			var curr umw.QMDataset
			err = cursorQM.Decode(&curr)
			if err != nil {
				continue
			}
			prev := umw.MongoDataVersioningData{
				Id:               curr.Id,
				AlgorithmName:    curr.AlgorithmName,
				AlgorithmVersion: curr.AlgorithmVersion,
				PublishVersion:   umw.MongoPublishVersion{PublishTs: curr.PublishTs},
				CreateTs:         curr.CreateTs,
				FileInfo:         curr.FileInfo,
				HasQMDataset:     true,
			}
			results = append(results, prev)
		}
	case 2:
		cursor, err := c.Client.Database(dbName).Collection(collectionVersionInfo).Aggregate(ctx, mongo.Pipeline{
			bson.D{{"$lookup", lookupValue}},
			bson.D{{"$unwind", unwindValue}},
			bson.D{{"$match", matchValue}},
		})
		if err != nil {
			c.Logger.Errorf("get data versioning data err: %v, db: %s, collection publish version: %s, collection version info: %s", err, dbName, collectionPublishVersion, collectionVersionInfo)
			break
		}
		if err = cursor.All(ctx, &results); err != nil {
			c.Logger.Errorf("decode data versioning data err: %v, db: %s, collection publish version: %s, collection version info: %s", err, dbName, collectionPublishVersion, collectionVersionInfo)
			break
		}
	}
	total = len(results)
	sort.Sort(results)
	start := (dataVersioningParams.Page - 1) * dataVersioningParams.Size
	for i := start; i < start+dataVersioningParams.Size && i < len(results); i++ {
		dataVersioningData = append(dataVersioningData, model.DataVersioningData{
			Id:               results[i].Id,
			AlgorithmName:    results[i].AlgorithmName,
			AlgorithmVersion: results[i].AlgorithmVersion,
			PublishTs:        results[i].PublishVersion.PublishTs,
			HasQMDataset:     results[i].HasQMDataset,
			CreateTs:         results[i].CreateTs,
			FileInfo:         results[i].FileInfo,
		})
	}
	return
}

func (c *MongoClient) AddAlgorithmTestReport(dbName, collection string, testReportParams model.VersionAddTestReportParams) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	var result umw.MongoVersionInfo
	err := c.Client.Database(dbName).Collection(collection).FindOneAndUpdate(
		ctx,
		bson.M{"_id": testReportParams.AlgorithmId},
		bson.D{{"$set", bson.D{
			{"test_report_url", testReportParams.TestReportURL},
			{"has_test_report", 2},
		}}},
	).Decode(&result)
	if err != nil {
		c.Logger.Errorf("add algorithm test report err: %v, db: %s, collection: %s", err, dbName, collection)
		return err
	}
	return nil
}

func (c *MongoClient) FindManyFactoryData(dbName, collection string, ordered Ordered, size Size,
	opts ...FilterOptions) (responseData interface{}, total int64, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	filter := bson.D{}
	if len(opts) != 0 {
		startTime, endTime := opts[0].StartTime, opts[0].EndTime
		if collection == umw.TestReport {
			if startTime != 0 || endTime != 0 {
				if startTime != 0 && endTime == 0 {
					filter = append(filter, bson.E{Key: "report_gen_time", Value: bson.M{"$gte": startTime}})
				} else if startTime == 0 && endTime != 0 {
					filter = append(filter, bson.E{Key: "report_gen_time", Value: bson.M{"$lte": endTime}})
				} else {
					filter = append(filter, bson.E{Key: "report_gen_time", Value: bson.M{"$gte": startTime, "$lte": endTime}})
				}
			}
		} else {
			if startTime != 0 || endTime != 0 {
				if startTime != 0 && endTime == 0 {
					filter = append(filter, bson.E{Key: "insert_ts", Value: bson.M{"$gte": startTime}})
				} else if startTime == 0 && endTime != 0 {
					filter = append(filter, bson.E{Key: "insert_ts", Value: bson.M{"$lte": endTime}})
				} else {
					filter = append(filter, bson.E{Key: "insert_ts", Value: bson.M{"$gte": startTime, "$lte": endTime}})
				}
			}
		}

		if opts[0].DeviceId != "" {
			filter = append(filter, bson.E{Key: "device_id", Value: opts[0].DeviceId})
		}
		if opts[0].Type != nil {
			filter = append(filter, bson.E{Key: "category", Value: *opts[0].Type})
		}
		if opts[0].Project != "" {
			filter = append(filter, bson.E{Key: "project", Value: opts[0].Project})
		}
	}
	total, err = c.Client.Database(dbName).Collection(collection).CountDocuments(ctx, filter)
	if err != nil {
		c.Logger.Errorf("get factory data, database: %s, collection: %s, err: %v", dbName, collection, err)
		return
	}
	cur, err := c.Client.Database(dbName).Collection(collection).Find(ctx, filter, options.Find().SetSort(newSort(ordered)).SetSkip(size.Offset).SetLimit(size.Limit))
	if err != nil {
		c.Logger.Errorf("get factory data, database: %s, collection: %s, err: %v", dbName, collection, err)
		return
	}
	if collection == umw.TestReport {
		responseData = make([]umw.MongoTestReportData, 0)
	} else {
		responseData = make([]umw.MongoTorqueReportData, 0)
	}
	if err = cur.All(ctx, &responseData); err != nil {
		c.Logger.Errorf("get factory data, database: %s, collection: %s, err: %v", dbName, collection, err)
		return
	}
	c.Logger.Infof("succeeded to get factory data, database: %s, collection: %s", dbName, collection)
	return
}

func (c *MongoClient) FindFactoryDataByID(dbName, collection, reportId string) (result *mongo.SingleResult, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	objID, err := primitive.ObjectIDFromHex(reportId)
	if err != nil {
		c.Logger.Errorf("failed to parse factory report id, err: %v", err)
		return
	}
	return c.Client.Database(dbName).Collection(collection).FindOne(ctx, bson.M{"_id": objID}), nil
}

func (c *MongoClient) FindTorqueFeatureValues(dbName, collection, requestId string) (responseData []umw.MongoTorqueFeatureCalculation, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	cur, err := c.Client.Database(dbName).Collection(collection).Find(ctx, bson.M{"request_id": requestId})
	if err != nil {
		c.Logger.Errorf("failed to get torque feature values, request_id: %s, err: %v", requestId, err)
		return
	}
	if err = cur.All(ctx, &responseData); err != nil {
		c.Logger.Errorf("failed to get torque feature values, request_id: %s, err: %v", requestId, err)
		return
	}
	c.Logger.Infof("succeeded to get torque feature values, request_id: %s", requestId)
	return
}

func (c *MongoClient) getNeighborPublishVersion(dbName, collection string, currentVersion umw.MongoPublishVersion, lastOrNext int) (neighborVersion []umw.MongoPublishVersion, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// lastOrNext：-1，返回上一个大版本；1，返回下一个大版本
	var compare string
	if lastOrNext == -1 {
		compare = "$lt"
	} else {
		compare = "$gt"
	}
	matchStage := bson.D{
		{"$match", bson.D{
			{"project", currentVersion.Project},
			{"publish_ts", bson.D{{compare, currentVersion.PublishTs}}},
		},
		},
	}
	sortStage := bson.D{{"$sort", bson.D{{"publish_ts", lastOrNext}}}}
	limitStage := bson.D{{"$limit", 1}}
	cursor, err := c.Client.Database(dbName).Collection(collection).Aggregate(ctx, mongo.Pipeline{matchStage, sortStage, limitStage})
	if err = cursor.All(ctx, &neighborVersion); err != nil {
		c.Logger.Errorf("decode from cursor err: %v", err)
		return
	}
	return
}

func (c *MongoClient) containDocument(dbName, collectionName string, filter bson.D) (conflict bool, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	var count int64
	count, err = c.Client.Database(dbName).Collection(collectionName).CountDocuments(ctx, filter)
	if err != nil {
		return
	}
	conflict = count >= 1
	return
}

func (c *MongoClient) InsertAlgorithmDataset(req model.AddNewDatasetMongoRequest, id, createTs int64, size int64) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	var collection *mongo.Collection
	switch size {
	case 1:
		collection = c.Client.Database("algorithm").Collection("qm_dataset")
	case 2:
		collection = c.Client.Database("algorithm").Collection("version-info")
	}
	filter := make(bson.M)
	filter["_id"] = id
	filter["file_info.file_name"] = req.FileName
	count, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return err
	}
	var res *mongo.SingleResult
	if count == 0 {
		res = collection.FindOneAndUpdate(ctx, bson.M{"_id": id}, bson.M{"$push": bson.M{"file_info": req}, "$set": bson.M{"create_ts": createTs}})
	} else {
		res = collection.FindOneAndUpdate(ctx, filter, bson.M{"$set": bson.M{"file_info.$.file_url": req.FileUrl, "create_ts": createTs}})
	}
	if res.Err() != nil {
		return res.Err()
	}
	return nil
}

func (c *MongoClient) ChangeAlgorithmHasTestReportStatus(id int64) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	collection := c.Client.Database("algorithm").Collection("version-info")
	filter := make(bson.M)
	filter["_id"] = id
	res := collection.FindOneAndUpdate(ctx, filter, bson.M{"$set": bson.M{"has_test_report": 1}})
	if res.Err() != nil {
		return res.Err()
	}
	return nil
}
func (c *MongoClient) AddQMDataset(alName, alVersion string, publishTs int64, fileRequest model.AddNewDatasetMongoRequest) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	collection := c.Client.Database("algorithm").Collection("qm_dataset")
	filter := make(bson.M)
	filter["algorithm_name"] = alName
	filter["algorithm_version"] = alVersion
	filter["publish_ts"] = publishTs
	count, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return err
	}
	if count == 0 {
		id, err := c.getID("algorithm", "version-info")
		if err != nil {
			return err
		}
		insert := umw.QMDataset{
			Id:               id,
			AlgorithmName:    alName,
			AlgorithmVersion: alVersion,
			PublishTs:        publishTs,
			CreateTs:         time.Now().UnixMilli(),
		}
		_, err = collection.InsertOne(ctx, insert)
		if err != nil {
			return err
		}
	}
	res := collection.FindOneAndUpdate(ctx, filter, bson.M{"$push": bson.M{"file_info": fileRequest}})
	if res.Err() != nil {
		return res.Err()
	}
	return nil
}

func (c *MongoClient) CheckIfQM(id int) (int64, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	collection := c.Client.Database("algorithm").Collection("version-info")
	count, err := collection.CountDocuments(ctx, bson.M{"_id": id})
	if err != nil {
		return -1, err
	}
	return count + 1, nil
}

func (c *MongoClient) DeleteDVFile(req model.DeleteDVFileRequest) (int64, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	QMcollection := c.Client.Database("algorithm").Collection("qm_dataset")
	DVcollection := c.Client.Database("algorithm").Collection("version-info")
	count, err := DVcollection.CountDocuments(ctx, bson.M{"file_info.file_url": req.FileUrl})
	if err != nil {
		return -1, err
	}
	filter := make(bson.M)
	update := make(bson.M)
	filter["file_info.file_url"] = req.FileUrl
	update["$pull"] = bson.M{"file_info": bson.M{"file_url": req.FileUrl}}
	var res bson.M
	var collection *mongo.Collection
	if count > 0 {
		collection = DVcollection
		count = 1
	} else {
		collection = QMcollection
	}
	err = collection.FindOne(ctx, filter).Decode(&res)
	if err != nil {
		return -1, err
	}
	if len(res["file_info"].(bson.A)) == 1 {
		update["$set"] = bson.M{"create_ts": 0}
	}
	_, err = collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return -1, err
	}
	return count + 1, nil
}

func (c *MongoClient) FindTankTransDetail(transId string) (umw.MongoTankTransferServiceInfo, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	var responseData umw.MongoTankTransferServiceInfo
	filter := bson.M{"trans_id": transId}
	err := c.Client.Database(umw.Device2Cloud).Collection(umw.TTrans).FindOne(ctx, filter).Decode(&responseData)
	if err != nil {
		c.Logger.Errorf("failed to get data, trans_id: %s, err: %v", transId, err)
	} else {
		c.Logger.Infof("succeeded to get data, trans_id: %s", transId)
	}
	return responseData, err
}

func (c *MongoClient) UpdateTankTransServiceInfo(project, deviceId string, records []model.TankTransData) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	var description string
	devicesInfo, err := c.GetDeviceListMapping(project, deviceId)
	if err != nil {
		c.Logger.Warnf("failed to get device name, err: %v", err)
	} else if len(devicesInfo) == 0 {
		c.Logger.Warnf("device %s no found", deviceId)
	} else {
		description = devicesInfo[0].Description
	}
	indexOptions := []IndexOption{
		{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 180 * 24 * 3600},
		{Name: "trans_id", Fields: bson.D{{"trans_id", 1}}, Unique: true},
	}
	if err := createIndex(ctx, c.Client, umw.Device2Cloud, umw.TTrans, indexOptions...); err != nil {
		c.Logger.Errorf("create index, err: %v", err)
		return
	}

	execFunc := func(i int) {
		var res umw.MongoTankTransferServiceInfo
		filter := bson.M{"trans_id": records[i].TransId}
		err := c.Client.Database(umw.Device2Cloud).Collection(umw.TTrans).FindOne(ctx, filter).Decode(&res)
		if err == mongo.ErrNoDocuments {
			var date time.Time
			if records[i].TransStartTS != 0 {
				date = util.ConvertTime(records[i].TransStartTS)
			} else if records[i].TransEndTS != 0 {
				date = util.ConvertTime(records[i].TransEndTS)
			} else {
				date = time.Now()
			}
			record := umw.MongoTankTransferServiceInfo{
				Date:            date,
				Project:         project,
				DeviceId:        deviceId,
				Description:     description,
				TransId:         records[i].TransId,
				FullChargedSlot: records[i].FullChargedSlot,
				DrianedSlot:     records[i].DrianedSlot,
				EmptySlot:       records[i].EmptySlot,
				TransStartTS:    records[i].TransStartTS,
				TransEndTS:      records[i].TransEndTS,
				CreateTime:      time.Now(),
				UpdateTime:      time.Now(),
			}
			_, err = c.Client.Database(umw.Device2Cloud).Collection(umw.TTrans).InsertOne(ctx, record)
			if err != nil {
				c.Logger.Errorf("insert new tank transfer service info, trans_id: %s, err: %v", records[i].TransId, err)
			}
		} else {
			update := bson.M{}
			if res.TransStartTS == 0 && records[i].TransStartTS != 0 {
				update["trans_start_ts"] = records[i].TransStartTS
				if res.Date.UnixMilli() > records[i].TransStartTS {
					update["date"] = util.ConvertTime(records[i].TransStartTS)
				}
			}
			if res.TransEndTS == 0 && records[i].TransEndTS != 0 {
				update["trans_end_ts"] = records[i].TransEndTS
				if update["date"] == nil && res.Date.UnixMilli() > records[i].TransEndTS {
					update["date"] = util.ConvertTime(records[i].TransEndTS)
				}
			}
			if len(update) != 0 {
				update["update_time"] = time.Now()
				err := c.Client.Database(umw.Device2Cloud).Collection(umw.TTrans).FindOneAndUpdate(
					ctx, filter, bson.M{"$set": update})
				if err != nil {
					c.Logger.Errorf("update tank transfer service info, trans_id: %s, err: %v", records[i].TransId, err)
				}
			}
		}
	}
	ucmd.ParallelizeExec(len(records), execFunc)

	return
}
func (c *MongoClient) CountDocuments(dbName, collectionName string, filter bson.M) (int64, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	collection := c.Client.Database(dbName).Collection(collectionName)
	count, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return -1, err
	}
	return count, nil
}

func (c *MongoClient) Distinct(dbName, collectionName string, filter bson.M, field string) (int64, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	collection := c.Client.Database(dbName).Collection(collectionName)
	all, err := collection.Distinct(ctx, field, filter)
	if err != nil {
		return -1, err
	}
	return int64(len(all)), nil
}

func (c *MongoClient) DeleteImageInfo(dbName, collectionName string, filter bson.M) (data umw.MongoImageInfo, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	res := c.Client.Database(dbName).Collection(collectionName).FindOneAndDelete(ctx, filter)
	if res.Err() != nil {
		c.Logger.Errorf("UpdateOne err: %s", res.Err().Error())
		return
	}
	err = res.Decode(&data)
	if err != nil {
		c.Logger.Errorf("Decode err: %s", res.Err().Error())
		return
	}
	return
}

func (c *MongoClient) GetPieInfo(dbName, collectionName string) (pieInfoData []umw.PieInfoData, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	cursor, err := c.Client.Database(dbName).Collection(collectionName).Find(ctx, bson.M{})
	if err != nil {
		c.Logger.Errorf("get pie info err: %v, db: %s, collection: %s", err, dbName, collectionName)
		return
	}
	if err = cursor.All(ctx, &pieInfoData); err != nil {
		c.Logger.Errorf("decode from cursor err: %v", err)
		return
	}
	return
}

func (c *MongoClient) InsertOrUpdatePieInfo(dbName, collectionName string, newPieInfo umw.PieInfoData) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if newPieInfo.Id == 0 {
		id, err := c.getID(dbName, collectionName)
		if err != nil {
			c.Logger.Errorf("get id, db: %s, collection: %s, err: %v", dbName, collectionName, err)
			return err
		}
		newPieInfo.Id = id
	}
	_, err := c.Client.Database(dbName).Collection(collectionName).ReplaceOne(ctx, bson.M{"_id": newPieInfo.Id}, newPieInfo, options.Replace().SetUpsert(true))
	if err != nil {
		c.Logger.Errorf("update pie info err: %s, db: %s, collection: %s", err.Error(), dbName, collectionName)
		return err
	}
	return nil
}

func (c *MongoClient) DeletePieInfo(dbName, collectionName string, id int64) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	res := c.Client.Database(dbName).Collection(collectionName).FindOneAndDelete(ctx, bson.M{"_id": id})
	if res.Err() != nil {
		c.Logger.Errorf("delete pie info err: %s, db: %s, collection: %s", res.Err().Error(), dbName, collectionName)
		return res.Err()
	}

	return nil
}

type AlgCnt struct {
	DeviceId string `bson:"device_id"`
	Count    int64  `bson:"count"`
}

type SplitAlg struct {
	Count    int64  `bson:"count"`
	DeviceId string `bson:"device_id"`
	FirstOut *int   `bson:"first_out"`
	LastOut  *int   `bson:"last_out"`
}

func (c *MongoClient) countDistinct(ctx context.Context, collection *mongo.Collection, pipeline mongo.Pipeline) (count int64, err error) {
	cursor, err := collection.Aggregate(ctx, pipeline)
	if err != nil {
		c.Logger.Errorf("aggregate err: %s, collection: %s", err.Error(), collection.Name())
		return
	}
	var res []AlgCnt
	if err = cursor.All(ctx, &res); err != nil {
		c.Logger.Errorf("parse from cursor err: %s", err.Error())
		return
	}
	if len(res) == 0 {
		return
	}
	count = res[0].Count
	return
}

func (c *MongoClient) countByDevice(ctx context.Context, collection *mongo.Collection, pipeline mongo.Pipeline) (deviceCount map[string]int64, err error) {
	cursor, err := collection.Aggregate(ctx, pipeline)
	if err != nil {
		c.Logger.Errorf("aggregate err: %s, collection: %s", err.Error(), collection.Name())
		return
	}
	var res []AlgCnt
	if err = cursor.All(ctx, &res); err != nil {
		c.Logger.Errorf("parse from cursor err: %s", err.Error())
		return
	}
	deviceCount = make(map[string]int64)
	for _, ac := range res {
		deviceCount[ac.DeviceId] = ac.Count
	}
	return
}

func (c *MongoClient) GetAlgorithmCount(dbName string, createDay time.Time, imgTypeList []string) (data umw.AlgorithmCountInfo, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	if len(imgTypeList) == 0 {
		return
	}
	devicesList, err := c.Client.Database(dbName).ListCollectionNames(ctx, bson.M{"name": bson.M{"$regex": "PS-NIO-"}})
	if err != nil {
		c.Logger.Errorf("failed to get collections, err: %v", err)
		return
	}
	if len(devicesList) == 0 {
		c.Logger.Warnf("db: %s, collections no found", dbName)
		return
	}

	// 获取照片数
	inList := make(bson.A, len(imgTypeList))
	for i, tp := range imgTypeList {
		imgType, _ := strconv.Atoi(tp)
		inList[i] = imgType
	}
	endDay := createDay.AddDate(0, 0, 1)
	filter := bson.M{
		"image_type":     bson.M{"$in": inList},
		"image_gen_time": bson.M{"$gte": createDay.UnixMilli(), "$lt": endDay.UnixMilli()},
	}
	groupStage := bson.D{
		{"_id", primitive.Null{}},
		{"count", bson.D{{"$sum", "$image_size"}}},
	}
	pipeline := mongo.Pipeline{
		bson.D{{"$match", filter}},
		bson.D{{"$group", groupStage}},
	}

	var mu sync.Mutex
	execFunc := func(i int) {
		collection := c.Client.Database(dbName).Collection(devicesList[i])
		count, err := c.countDistinct(ctx, collection, pipeline)
		if err != nil {
			c.Logger.Errorf("fail to count image, collection: %s, filter: %v", devicesList[i], filter)
			return
		}
		mu.Lock()
		defer mu.Unlock()
		data.ImageCount += count
		if count > 0 {
			data.DeviceCount++
		}
	}
	ucmd.ParallelizeExec(len(devicesList), execFunc)
	return
}

func (c *MongoClient) GetServiceOrderCount(dbName, collectionName string, startTime int64, deviceId ...string) (total int64, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	startDay := time.UnixMilli(startTime)
	filter := bson.M{
		"date": bson.M{"$gte": startDay, "$lt": startDay.AddDate(0, 0, 1)},
	}
	if len(deviceId) > 0 {
		filter["device_id"] = deviceId[0]
	}
	total, err = c.Client.Database(dbName).Collection(collectionName).CountDocuments(ctx, filter)
	if err != nil {
		c.Logger.Errorf("count service order err: %s", err.Error())
		return
	}
	return
}

func makeAlgorithmResultFilter(opts model.AlgorithmSuccessRateRequest) bson.M {
	filter := bson.M{}
	if opts.TimeRange == 1 {
		// 白天
		filter["date"] = bson.M{"$gte": time.UnixMilli(opts.StartTime).Add(8 * time.Hour), "$lte": time.UnixMilli(opts.StartTime).Add(18 * time.Hour)}
	} else if opts.TimeRange == 2 {
		// 晚上
		filter["$or"] = bson.A{
			bson.M{"date": bson.M{"$gte": time.UnixMilli(opts.StartTime), "$lt": time.UnixMilli(opts.StartTime).Add(8 * time.Hour)}},
			bson.M{"date": bson.M{"$gt": time.UnixMilli(opts.StartTime).Add(18 * time.Hour), "$lt": time.UnixMilli(opts.StartTime).Add(24 * time.Hour)}},
		}
	} else {
		// 整天
		filter["date"] = bson.M{"$gte": time.UnixMilli(opts.StartTime), "$lt": time.UnixMilli(opts.StartTime).Add(24 * time.Hour)}
	}
	if opts.VehicleType != "" {
		filter["vehicle_type"] = opts.VehicleType
	}
	if opts.BatteryType != "" {
		filter["battery_type"] = opts.BatteryType
	}
	if opts.DeviceId != "" {
		filter["device_id"] = opts.DeviceId
	}
	return filter
}

func makeSuccessRateFilter(opts model.AlgorithmSuccessRateRequest) bson.M {
	filter := makeAlgorithmResultFilter(opts)
	filter["errorcode"] = 0
	return filter
}

func IsNoPfsAlgorithm(project string, algId int) bool {
	_, ok := model.NoPfsAlgorithms[project][algId]
	return ok
}

func (c *MongoClient) GetDeviceTotal(dbName string, opts model.AlgorithmSuccessRateRequest) (total int, devices map[string]struct{}, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	devices = make(map[string]struct{})
	collectionName := fmt.Sprintf("%s_%s", umw.IntAlgorithmMap[opts.AlgorithmId], strconv.Itoa(int(time.UnixMilli(opts.StartTime).Month())))
	filter := makeSuccessRateFilter(opts)
	deviceList, err := c.Client.Database(dbName).Collection(collectionName).Distinct(ctx, "device_id", filter)
	if err != nil {
		c.Logger.Errorf("fail to get distinct devices: %v, opts: %+v", err, opts)
		return
	}
	for _, info := range deviceList {
		deviceId, ok := info.(string)
		if ok {
			devices[deviceId] = struct{}{}
		}
	}
	total = len(devices)
	return
}

func (c *MongoClient) GetAlgorithmSuccessRate(dbName, project string, opts model.AlgorithmSuccessRateRequest, pfsMap map[int]map[string]struct{}) (total int64, successRate float64, err error, extraData []interface{}) {
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	collectionName := fmt.Sprintf("%s_%s", umw.IntAlgorithmMap[opts.AlgorithmId], strconv.Itoa(int(time.UnixMilli(opts.StartTime).Month())))
	collection := c.Client.Database(dbName).Collection(collectionName)
	g := ucmd.NewErrGroup(ctx)

	// 计算总数
	g.GoRecover(func() error {
		var gErr error
		filter := makeSuccessRateFilter(opts)
		switch opts.AlgorithmId {
		case 1, 10, 15:
			// errorcode不为0的总数据量
			if len(pfsMap) == 0 || IsNoPfsAlgorithm(project, opts.AlgorithmId) { // 计算全量数据 或 参数配方筛选开启时无参数配方开关的算法
				total, gErr = collection.CountDocuments(ctx, filter)
			} else {
				cursor, fErr := collection.Find(ctx, filter, &options.FindOptions{Projection: bson.M{"device_id": 1}})
				if fErr != nil {
					c.Logger.Errorf("find total err: %v, filter: %v", fErr, filter)
					return fErr
				}
				var dailyData []umw.MongoAlgorithmDailyReport
				if fErr = cursor.All(ctx, &dailyData); fErr != nil {
					c.Logger.Errorf("fail to parse algorithm daily report, err: %v, filter: %v", fErr, filter)
					return fErr
				}
				for _, report := range dailyData {
					if _, ok := pfsMap[opts.AlgorithmId][report.DeviceId]; ok {
						total++
					}
				}
			}
		case 2, 3, 5, 6, 7, 8, 9, 11, 12, 13, 16:
			// 去重service_id
			if len(pfsMap) == 0 || IsNoPfsAlgorithm(project, opts.AlgorithmId) {
				pipeline := mongo.Pipeline{
					bson.D{{"$match", filter}},
					bson.D{{"$group", bson.M{"_id": "$service_id"}}},
					bson.D{{"$group", bson.M{"_id": primitive.Null{}, "count": bson.M{"$sum": 1}}}},
				}
				total, gErr = c.countDistinct(ctx, collection, pipeline)
			} else {
				pipeline := mongo.Pipeline{
					bson.D{{"$match", filter}},
					bson.D{{"$group", bson.M{"_id": bson.M{"device_id": "$device_id", "service_id": "$service_id"}}}},
					bson.D{{"$group", bson.M{"_id": "$_id.device_id", "count": bson.M{"$sum": 1}}}},
					bson.D{{"$project", bson.M{"_id": 0, "device_id": "$_id", "count": 1}}},
				}
				deviceCount, cErr := c.countByDevice(ctx, collection, pipeline)
				if cErr != nil {
					c.Logger.Errorf("fail to count by device: %v, filter: %v", cErr, filter)
					return cErr
				}
				for deviceId, cnt := range deviceCount {
					if _, ok := pfsMap[opts.AlgorithmId][deviceId]; ok {
						total += cnt
					}
				}
			}
		default:
			c.Logger.Warnf("unsupported algorithm id: %d", opts.AlgorithmId)
		}
		if gErr != nil {
			c.Logger.Errorf("count total err: %s", gErr.Error())
			return gErr
		}
		return nil
	})

	// 计算成功数
	var successCount int64
	g.GoRecover(func() error {
		var gErr error
		filter := makeSuccessRateFilter(opts)
		switch opts.AlgorithmId {
		case 1, 10, 15:
			if opts.AlgorithmId == 1 {
				// output_value=1
				filter["output_value"] = 1
			} else {
				// output_value=0
				filter["output_value"] = 0
			}

			if len(pfsMap) == 0 || IsNoPfsAlgorithm(project, opts.AlgorithmId) {
				successCount, gErr = collection.CountDocuments(ctx, filter)
			} else {
				cursor, fErr := collection.Find(ctx, filter, &options.FindOptions{Projection: bson.M{"device_id": 1}})
				if fErr != nil {
					c.Logger.Errorf("find total err: %v, filter: %v", fErr, filter)
					return fErr
				}
				var dailyData []umw.MongoAlgorithmDailyReport
				if fErr = cursor.All(ctx, &dailyData); fErr != nil {
					c.Logger.Errorf("fail to parse algorithm daily report, err: %v, filter: %v", fErr, filter)
					return fErr
				}
				for _, report := range dailyData {
					if _, ok := pfsMap[opts.AlgorithmId][report.DeviceId]; ok {
						successCount++
					}
				}
			}
		case 2, 3, 5, 7, 8, 9, 11, 12, 13:
			condition := make(bson.M)
			output := make(bson.M)
			if opts.AlgorithmId == 3 {
				// 同一service_id，output_value=1出现次数>=1
				output = bson.M{"$sum": "$output_value"}
				condition = bson.M{"$gt": bson.A{"$output", 0}}
			} else if opts.AlgorithmId == 12 {
				// 同一service_id，output_value=0出现次数>=1
				output = bson.M{"$min": "$output_value"}
				condition = bson.M{"$eq": bson.A{"$output", 0}}
			} else {
				// 同一service_id，output_value=1出现次数=0
				output = bson.M{"$sum": "$output_value"}
				condition = bson.M{"$eq": bson.A{"$output", 0}}
			}
			if len(pfsMap) == 0 || IsNoPfsAlgorithm(project, opts.AlgorithmId) {
				pipeline := mongo.Pipeline{
					bson.D{{"$match", filter}},
					bson.D{{"$group", bson.D{
						{"_id", "$service_id"},
						{"output", output},
					}}},
					bson.D{{"$group", bson.D{
						{"_id", primitive.Null{}},
						{"count", bson.D{
							{"$sum", bson.M{"$cond": bson.A{
								condition,
								1,
								0,
							}}},
						}},
					}}},
				}
				successCount, gErr = c.countDistinct(ctx, collection, pipeline)
			} else {
				pipeline := mongo.Pipeline{
					bson.D{{"$match", filter}},
					bson.D{{"$group", bson.M{
						"_id":    bson.M{"device_id": "$device_id", "service_id": "$service_id"},
						"output": output,
					}}},
					bson.D{{"$group", bson.M{
						"_id": "$_id.device_id",
						"count": bson.M{
							"$sum": bson.M{"$cond": bson.A{
								condition,
								1,
								0,
							}},
						},
					}}},
					bson.D{{"$project", bson.M{"_id": 0, "device_id": "$_id", "count": 1}}},
				}
				deviceSuccessCount, cErr := c.countByDevice(ctx, collection, pipeline)
				if cErr != nil {
					c.Logger.Errorf("fail to count by device: %v, filter: %v", cErr, filter)
					return cErr
				}
				for deviceId, cnt := range deviceSuccessCount {
					if _, ok := pfsMap[opts.AlgorithmId][deviceId]; ok {
						successCount += cnt
					}
				}
			}
		case 6:
			var successLast int64
			pipeline := mongo.Pipeline{
				bson.D{{"$match", filter}},
				bson.D{{"$sort", bson.M{"date": 1}}},
				bson.D{{"$group", bson.D{
					{"_id", "$service_id"},
					{"count", bson.M{"$sum": 1}},
					{"device_id", bson.M{"$first": "$device_id"}},
					{"first", bson.M{"$first": "$$ROOT"}},
					{"last", bson.M{"$last": "$$ROOT"}},
				}}},
				bson.D{{"$project", bson.D{
					{"_id", 0},
					{"device_id", 1},
					{"count", 1},
					{"first_out", "$first.output_value"},
					{"last_out", "$last.output_value"},
				}}},
			}
			cursor, aErr := collection.Aggregate(ctx, pipeline, options.Aggregate().SetAllowDiskUse(true))
			if aErr != nil {
				c.Logger.Errorf("aggregate err: %s, collection: %s", aErr.Error(), collection.Name())
				return aErr
			}
			var res []SplitAlg
			if aErr = cursor.All(ctx, &res); aErr != nil {
				c.Logger.Errorf("parse from cursor err: %s", aErr.Error())
				return aErr
			}
			for _, sa := range res {
				_, ok := pfsMap[opts.AlgorithmId][sa.DeviceId]
				if len(pfsMap) == 0 || IsNoPfsAlgorithm(project, opts.AlgorithmId) || ok {
					if sa.FirstOut != nil && *sa.FirstOut == 0 {
						successCount++
					}
					if sa.Count == 2 && sa.LastOut != nil && *sa.LastOut == 0 {
						successLast++
					}
				}
			}
			extraData = append(extraData, successLast)
		case 16:
			var successLast int64
			pipeline := mongo.Pipeline{
				bson.D{{"$match", filter}},
				bson.D{{"$group", bson.D{
					{"_id", "$service_id"},
					{"device_id", bson.M{"$first": "$device_id"}},
					{"first", bson.M{"$first": "$$ROOT"}},
				}}},
				bson.D{{"$project", bson.D{
					{"_id", 0},
					{"device_id", 1},
					{"extra_data", "$first.extra_data"},
				}}},
			}
			var res []bson.D
			var rsdsData struct {
				DeviceId  string `json:"device_id" bson:"device_id"`
				ExtraData struct {
					OpenDoorAlarmTS  int64 `json:"open_door_alarm_ts" bson:"open_door_alarm_ts"`
					CloseDoorAlarmTS int64 `json:"close_door_alarm_ts" bson:"close_door_alarm_ts"`
				} `json:"extra_data" bson:"extra_data"`
			}
			cursor, aErr := collection.Aggregate(ctx, pipeline, options.Aggregate().SetAllowDiskUse(true))
			if aErr != nil {
				c.Logger.Errorf("aggregate err: %s, collection: %s", aErr.Error(), collection.Name())
				return aErr
			}
			if aErr = cursor.All(ctx, &res); aErr != nil {
				c.Logger.Errorf("parse from cursor err: %s", aErr.Error())
				return aErr
			}
			for i := range res {
				byteData, _ := bson.Marshal(res[i])
				if err = bson.Unmarshal(byteData, &rsdsData); err != nil {
					c.Logger.Warnf("unexpected extra_data: %+v", res[i])
					continue
				}

				_, ok := pfsMap[opts.AlgorithmId][rsdsData.DeviceId]
				if len(pfsMap) == 0 || IsNoPfsAlgorithm(project, opts.AlgorithmId) || ok {
					if rsdsData.ExtraData.OpenDoorAlarmTS == 0 {
						successCount++
					}
					if rsdsData.ExtraData.CloseDoorAlarmTS == 0 {
						successLast++
					}
				}
			}
			extraData = append(extraData, successLast)
		default:
			c.Logger.Warnf("unsupported algorithm id: %d", opts.AlgorithmId)
		}
		if gErr != nil {
			c.Logger.Errorf("count success err: %s", gErr.Error())
			return gErr
		}
		return nil
	})

	if err = g.Wait(); err != nil {
		return
	}
	if total == 0 {
		return
	}
	//c.Logger.Infof("total: %d, successCount: %d", total, successCount)
	successRate = float64(successCount) / float64(total)
	//fmt.Printf("day: %v, total: %v, success: %v, extra: %v\n", opts.StartTime, total, successCount, extraData)
	return
}

func (c *MongoClient) GetAlgorithmSuccessRateLowest(dbName, project string, opts model.AlgorithmSuccessRateRequest, pfsMap map[int]map[string]struct{}) (deviceSuccessRate []model.DeviceSuccessRate, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	collectionName := fmt.Sprintf("%s_%s", umw.IntAlgorithmMap[opts.AlgorithmId], strconv.Itoa(int(time.UnixMilli(opts.StartTime).Month())))
	collection := c.Client.Database(dbName).Collection(collectionName)
	filter := makeSuccessRateFilter(opts)

	// 计算总数
	deviceCount := make(map[string]int64)
	switch opts.AlgorithmId {
	case 1, 10, 15:
		// errorcode不为0的总数据量
		pipeline := mongo.Pipeline{
			bson.D{{"$match", filter}},
			bson.D{{"$group", bson.M{"_id": "$device_id", "count": bson.M{"$sum": 1}}}},
			bson.D{{"$project", bson.M{"_id": 0, "device_id": "$_id", "count": 1}}},
		}
		deviceCount, err = c.countByDevice(ctx, collection, pipeline)
	case 2, 3, 5, 6, 7, 8, 9, 11, 12, 13, 16:
		// 去重service_id
		pipeline := mongo.Pipeline{
			bson.D{{"$match", filter}},
			bson.D{{"$group", bson.M{"_id": bson.M{"device_id": "$device_id", "service_id": "$service_id"}}}},
			bson.D{{"$group", bson.M{"_id": "$_id.device_id", "count": bson.M{"$sum": 1}}}},
			bson.D{{"$project", bson.M{"_id": 0, "device_id": "$_id", "count": 1}}},
		}
		deviceCount, err = c.countByDevice(ctx, collection, pipeline)
	default:
		c.Logger.Warnf("unsupported algorithm id: %d", opts.AlgorithmId)
	}
	if err != nil {
		c.Logger.Errorf("count total err: %s", err.Error())
		return
	}

	// 计算成功数
	deviceSuccessCount := make(map[string]int64)
	switch opts.AlgorithmId {
	case 1, 10, 15:
		if opts.AlgorithmId == 1 {
			// output_value=1
			filter["output_value"] = 1
		} else {
			// output_value=0
			filter["output_value"] = 0
		}
		pipeline := mongo.Pipeline{
			bson.D{{"$match", filter}},
			bson.D{{"$group", bson.M{"_id": "$device_id", "count": bson.M{"$sum": 1}}}},
			bson.D{{"$project", bson.M{"_id": 0, "device_id": "$_id", "count": 1}}},
		}
		deviceSuccessCount, err = c.countByDevice(ctx, collection, pipeline)
	case 2, 3, 5, 7, 8, 9, 11, 12, 13:
		condition := make(bson.M)
		output := make(bson.M)
		if opts.AlgorithmId == 3 {
			// 同一service_id，output_value=1出现次数>=1
			output = bson.M{"$sum": "$output_value"}
			condition = bson.M{"$gt": bson.A{"$output", 0}}
		} else if opts.AlgorithmId == 12 {
			// 同一service_id，output_value=0出现次数>=1
			output = bson.M{"$min": "$output_value"}
			condition = bson.M{"$eq": bson.A{"$output", 0}}
		} else {
			// 同一service_id，output_value=1出现次数=0
			output = bson.M{"$sum": "$output_value"}
			condition = bson.M{"$eq": bson.A{"$output", 0}}
		}
		pipeline := mongo.Pipeline{
			bson.D{{"$match", filter}},
			bson.D{{"$group", bson.M{
				"_id":    bson.M{"device_id": "$device_id", "service_id": "$service_id"},
				"output": output,
			}}},
			bson.D{{"$group", bson.M{
				"_id": "$_id.device_id",
				"count": bson.M{
					"$sum": bson.M{"$cond": bson.A{
						condition,
						1,
						0,
					}},
				},
			}}},
			bson.D{{"$project", bson.M{"_id": 0, "device_id": "$_id", "count": 1}}},
		}
		deviceSuccessCount, err = c.countByDevice(ctx, collection, pipeline)
	case 6:
		pipeline := mongo.Pipeline{
			bson.D{{"$match", filter}},
			bson.D{{"$sort", bson.M{"date": 1}}},
			bson.D{{"$group", bson.D{
				{"_id", "$service_id"},
				{"count", bson.M{"$sum": 1}},
				{"device_id", bson.M{"$first": "$device_id"}},
				{"first", bson.M{"$first": "$$ROOT"}},
				{"last", bson.M{"$last": "$$ROOT"}},
			}}},
			bson.D{{"$project", bson.D{
				{"_id", 0},
				{"device_id", 1},
				{"count", 1},
				{"first_out", "$first.output_value"},
				{"last_out", "$last.output_value"},
			}}},
		}
		var cursor *mongo.Cursor
		cursor, err = collection.Aggregate(ctx, pipeline)
		if err != nil {
			c.Logger.Errorf("aggregate err: %s, collection: %s", err.Error(), collection.Name())
			return
		}
		var res []SplitAlg
		if err = cursor.All(ctx, &res); err != nil {
			c.Logger.Errorf("parse from cursor err: %s", err.Error())
			return
		}
		if opts.Name == model.BSAService {
			for _, sa := range res {
				if sa.FirstOut != nil && *sa.FirstOut == 0 {
					deviceSuccessCount[sa.DeviceId]++
				}
			}
		} else if opts.Name == model.BSAVehicle {
			for _, sa := range res {
				if sa.Count == 2 && sa.LastOut != nil && *sa.LastOut == 0 {
					deviceSuccessCount[sa.DeviceId]++
				}
			}
		} else {
			err = fmt.Errorf("invalid bsa name, opts: %+v", opts)
			c.Logger.Error(err)
			return
		}
	case 16:
		pipeline := mongo.Pipeline{
			bson.D{{"$match", filter}},
			bson.D{{"$group", bson.D{
				{"_id", "$service_id"},
				{"device_id", bson.M{"$first": "$device_id"}},
				{"first", bson.M{"$first": "$$ROOT"}},
			}}},
			bson.D{{"$project", bson.D{
				{"_id", 0},
				{"device_id", 1},
				{"extra_data", "$first.extra_data"},
			}}},
		}
		var res []bson.D
		var rsdsData struct {
			DeviceId  string `json:"device_id" bson:"device_id"`
			ExtraData struct {
				OpenDoorAlarmTS  int64 `json:"open_door_alarm_ts" bson:"open_door_alarm_ts"`
				CloseDoorAlarmTS int64 `json:"close_door_alarm_ts" bson:"close_door_alarm_ts"`
			} `json:"extra_data" bson:"extra_data"`
		}
		var cursor *mongo.Cursor
		cursor, err = collection.Aggregate(ctx, pipeline, options.Aggregate().SetAllowDiskUse(true))
		if err != nil {
			c.Logger.Errorf("aggregate err: %s, collection: %s", err.Error(), collection.Name())
			return
		}
		if err = cursor.All(ctx, &res); err != nil {
			c.Logger.Errorf("parse from cursor err: %s", err.Error())
			return
		}
		if opts.Name == model.RSDSOpen {
			for i := range res {
				byteData, _ := bson.Marshal(res[i])
				if err = bson.Unmarshal(byteData, &rsdsData); err != nil {
					c.Logger.Warnf("unexpected extra_data: %+v", res[i])
					continue
				}
				if rsdsData.ExtraData.OpenDoorAlarmTS == 0 {
					deviceSuccessCount[rsdsData.DeviceId]++
				}
			}
		} else if opts.Name == model.RSDSClose {
			for i := range res {
				byteData, _ := bson.Marshal(res[i])
				if err = bson.Unmarshal(byteData, &rsdsData); err != nil {
					c.Logger.Warnf("unexpected extra_data: %+v", res[i])
					continue
				}
				if rsdsData.ExtraData.CloseDoorAlarmTS == 0 {
					deviceSuccessCount[rsdsData.DeviceId]++
				}
			}
		} else {
			err = fmt.Errorf("invalid bsa name, opts: %+v", opts)
			c.Logger.Error(err)
			return
		}
	default:
		c.Logger.Warnf("unsupported algorithm id: %d", opts.AlgorithmId)
	}
	if err != nil {
		c.Logger.Errorf("count success err: %s", err.Error())
		return
	}

	//fmt.Println("device count: ", len(deviceCount))
	//fmt.Println("device success count: ", len(deviceSuccessCount))
	deviceAll := make([]model.DeviceSuccessRate, len(deviceSuccessCount))
	idx := 0
	for deviceId, successCount := range deviceSuccessCount {
		_, ok := pfsMap[opts.AlgorithmId][deviceId]
		if !IsNoPfsAlgorithm(project, opts.AlgorithmId) && len(pfsMap) > 0 && !ok {
			continue
		}
		deviceAll[idx] = model.DeviceSuccessRate{
			DeviceId:    deviceId,
			SuccessRate: float64(successCount) / float64(deviceCount[deviceId]),
		}
		idx++
	}
	sort.Slice(deviceAll, func(i, j int) bool {
		return deviceAll[i].SuccessRate < deviceAll[j].SuccessRate || (deviceAll[i].SuccessRate == deviceAll[j].SuccessRate && deviceAll[i].DeviceId < deviceAll[j].DeviceId)
	})
	ignoreDevices := make(map[string]struct{})
	for _, deviceId := range strings.Split(opts.IgnoreDevices, ",") {
		ignoreDevices[deviceId] = struct{}{}
	}
	deviceSuccessRate = make([]model.DeviceSuccessRate, 0)
	area := ucmd.GetArea()
	for i := 0; len(deviceSuccessRate) < 10 && i < len(deviceAll); i++ {
		if _, ok := ignoreDevices[deviceAll[i].DeviceId]; !ok {
			rawData, err := c.NewMongoEntry(bson.D{bson.E{Key: "device_id", Value: deviceAll[i].DeviceId}}).GetOne(umw.OAuthDB, umw.DeviceBaseInfo)
			//rawData, err := c.NewMongoEntry(bson.D{bson.E{Key: "device_id", Value: deviceAll[i].DeviceId}}).GetOne(umw.OAuthDB, "device_basic_info_prod")
			if err != nil {
				c.Logger.Errorf("get device description err: %s", err.Error())
				continue
			}
			if rawData == nil {
				c.Logger.Errorf("invalid device id: %s", deviceAll[i].DeviceId)
				continue
			}
			var data umw.MongoDeviceInfo
			if err = bson.Unmarshal(rawData, &data); err != nil {
				c.Logger.Errorf("failed to unmarshal device %s info, err: %v", deviceAll[i].DeviceId, err)
				continue
			}
			deviceAll[i].Description = data.Description
			if area == um.Europe {
				descList := strings.Split(data.Description, "|")
				if len(descList) >= 2 {
					deviceAll[i].Description = strings.Trim(descList[1], " ")
				}
			}
			deviceSuccessRate = append(deviceSuccessRate, deviceAll[i])
		}
	}
	return
}

func (c *MongoClient) GetAlgorithmAecData(dbName string, opts model.AlgorithmSuccessRateRequest) (codeNotNull, codeZero int64, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	collectionName := fmt.Sprintf("%s_%s", umw.IntAlgorithmMap[opts.AlgorithmId], strconv.Itoa(int(time.UnixMilli(opts.StartTime).Month())))
	collection := c.Client.Database(dbName).Collection(collectionName)
	g := ucmd.NewErrGroup(ctx)
	g.GoRecover(func() error {
		var gErr error
		filter := bson.M{
			"date":      bson.M{"$gte": time.UnixMilli(opts.StartTime), "$lt": time.UnixMilli(opts.StartTime).AddDate(0, 0, 1)},
			"errorcode": bson.M{"$ne": nil},
		}
		pipeline := mongo.Pipeline{
			bson.D{{"$match", filter}},
			bson.D{{"$group", bson.M{"_id": "$service_id"}}},
			bson.D{{"$group", bson.M{"_id": primitive.Null{}, "count": bson.M{"$sum": 1}}}},
		}
		codeNotNull, gErr = c.countDistinct(ctx, collection, pipeline)
		if gErr != nil {
			c.Logger.Errorf("get errorcode not null err: %s", gErr.Error())
			return gErr
		}
		return nil
	})
	g.GoRecover(func() error {
		var gErr error
		filter := bson.M{
			"date":      bson.M{"$gte": time.UnixMilli(opts.StartTime), "$lt": time.UnixMilli(opts.StartTime).AddDate(0, 0, 1)},
			"errorcode": 0,
		}
		pipeline := mongo.Pipeline{
			bson.D{{"$match", filter}},
			bson.D{{"$group", bson.M{"_id": "$service_id"}}},
			bson.D{{"$group", bson.M{"_id": primitive.Null{}, "count": bson.M{"$sum": 1}}}},
		}
		codeZero, gErr = c.countDistinct(ctx, collection, pipeline)
		if gErr != nil {
			c.Logger.Errorf("get errorcode zero err: %s", gErr.Error())
			return gErr
		}
		return nil
	})
	if err = g.Wait(); err != nil {
		return
	}
	return
}

func (c *MongoClient) GetAlgorithmVersion(dbName, collectionPublishVersion, collectionVersionInfo string, project string) (ts int64, data map[string]umw.MongoVersionInfo, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	cursor, err := c.Client.Database(dbName).Collection(collectionPublishVersion).Aggregate(ctx, mongo.Pipeline{
		bson.D{{"$match", bson.D{
			{"project", project},
			{"publish_ts", bson.M{"$lt": time.Now().UnixMilli()}},
		}}},
		bson.D{{"$sort", bson.D{{"publish_ts", -1}}}},
		bson.D{{"$limit", 1}},
	})
	if err != nil {
		c.Logger.Errorf("get version id err: %s", err.Error())
		return
	}
	var res []umw.MongoPublishVersion
	if err = cursor.All(ctx, &res); err != nil {
		c.Logger.Errorf("parse from cursor err: %s", err.Error())
		return
	}
	if len(res) == 0 {
		return
	}
	ts = res[0].PublishTs
	cursor, err = c.Client.Database(dbName).Collection(collectionVersionInfo).Find(ctx, bson.M{"publish_version_id": res[0].Id})
	if err != nil {
		c.Logger.Errorf("get version info err: %s", err.Error())
		return
	}
	var versionRes []umw.MongoVersionInfo
	if err = cursor.All(ctx, &versionRes); err != nil {
		c.Logger.Errorf("parse from cursor err: %s", err.Error())
		return
	}
	data = make(map[string]umw.MongoVersionInfo)
	for _, info := range versionRes {
		data[info.AlgorithmName] = info
	}
	return
}

func (c *MongoClient) GetAlgorithmUpdateTs(algName string) (updateTs int64, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	cursor, err := c.Client.Database(umw.Algorithm).Collection(umw.PublishVersionInfo).Aggregate(ctx, mongo.Pipeline{
		{{"$match", bson.M{"algorithm_name": algName}}},
		{{"$lookup", bson.D{
			{"from", umw.PublishVersion},
			{"localField", "publish_version_id"},
			{"foreignField", "_id"},
			{"as", "publish_version"},
		}}},
		{{"$unwind", bson.D{
			{"path", "$publish_version"},
			{"preserveNullAndEmptyArrays", true},
		}}},
		{{"$project", bson.M{"algorithm_version": 1, "update_point": 1, "publish_version.publish_ts": 1}}},
		{{"$sort", bson.M{"publish_version.publish_ts": -1}}},
	})
	var versionInfo []umw.MongoDataVersioningData
	if err = cursor.All(ctx, &versionInfo); err != nil {
		return
	}
	for _, version := range versionInfo {
		if version.AlgorithmVersion != "0.0.0" && version.AlgorithmVersion != "" && version.UpdatePoint != "0,0,0" {
			updateTs = version.PublishVersion.PublishTs
			return
		}
	}
	return
}

func (c *MongoClient) UpdateSnapshotDiagnosisResultById(objId primitive.ObjectID, project, diagnosisResult, deviceLog string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	update := bson.M{}
	if diagnosisResult != "" {
		update["diagnosis_result"] = diagnosisResult
	}
	if deviceLog != "" {
		update["device_log"] = deviceLog
	}
	dbName := fmt.Sprintf("%s-%s", umw.Diagnosis, strings.ToLower(project))
	_, err := c.Client.Database(dbName).Collection(umw.SnapshotRealtime).UpdateByID(ctx, objId, bson.M{"$set": update})
	if err != nil {
		c.Logger.Errorf("update snapshot diagnosis data, collection: %s, err: %v", umw.SnapshotRealtime, err)
		return err
	}

	c.Logger.Infof("succeeded to update snapshot diagnosis data, collection: %s", umw.SnapshotRealtime)
	return nil
}

func (c *MongoClient) InsertBrowndragonDeviceInfo(dbName, collectionName string, deviceInfo umw.MongoBrownDragonDeviceInfo) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	indexOption := IndexOption{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 180 * 24 * 3600}
	if err := createIndex(ctx, c.Client, dbName, collectionName, indexOption); err != nil {
		return fmt.Errorf("create index, err: %v", err)
	}

	if _, err := c.Client.Database(dbName).Collection(collectionName).InsertOne(ctx, deviceInfo); err != nil {
		c.Logger.Errorf("insert browndragon device info err: %s", err.Error())
		return err
	}
	c.Logger.Infof("succeed to insert browndragon device info")
	return nil
}

func (c *MongoClient) FindCameraInfo(project string, filter bson.M) (response []model.CameraInfo, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	dbName := "camera_management"
	collectionName := ucmd.RenameProjectDB(project) + "_camera_info"
	cursor, err := c.Client.Database(dbName).Collection(collectionName).Find(ctx, filter)
	if err != nil {
		c.Logger.Errorf("get camera info err: %s", err.Error())
		return
	}
	if err = cursor.All(ctx, &response); err != nil {
		c.Logger.Errorf("parse res err: %s", err.Error())
		return
	}
	return
}

// MakeCameraManagementFilter 构建摄像头管理模块的mongo查询filter
func MakeCameraManagementFilter(request model.GetCameraDeviceRequest) bson.D {
	res := bson.D{{"has_image", true}}
	if request.Area != "" {
		res = append(res, bson.E{Key: "area", Value: request.Area})
	}
	if request.DeviceId != "" {
		res = append(res, bson.E{Key: "device_id", Value: bson.M{"$in": strings.Split(request.DeviceId, ",")}})
	}
	if request.CameraType != "" {
		res = append(res, bson.E{Key: "camera_type", Value: bson.M{"$in": strings.Split(request.CameraType, ",")}})
	}
	if request.JudgeResult != nil {
		if *request.JudgeResult < 2 {
			res = append(res, bson.E{Key: "judge_result", Value: *request.JudgeResult})
		} else {
			res = append(res, bson.E{Key: "judge_result", Value: bson.M{"$gte": 2}})
		}
	}
	if request.PredictResult != nil {
		res = append(res, bson.E{Key: "predict_type", Value: *request.PredictResult})
	}
	if request.InBlacklist != nil {
		res = append(res, bson.E{Key: "in_blacklist", Value: *request.InBlacklist})
	}
	if request.NeedAcceptance != nil {
		res = append(res, bson.E{Key: "need_acceptance", Value: *request.NeedAcceptance})
	}
	return res
}

func (c *MongoClient) GetDeviceInfo(deviceId string) (res umw.MongoDeviceInfo, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	err = c.Client.Database("oauth").Collection("device_basic_info").FindOne(ctx, bson.M{"device_id": deviceId}).Decode(&res)
	return
}

func (c *MongoClient) GetCameraDeviceInfo(project string, request model.GetCameraDeviceRequest, area, lang string) (res []model.DeviceCameraInfoResponse, total int64, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	filter := MakeCameraManagementFilter(request)
	pipeline := mongo.Pipeline{
		{{"$match", filter}},
		{{"$group", bson.M{
			"_id":           bson.M{"device_id": "$device_id", "area": "$area"},
			"camera_result": bson.M{"$push": "$$ROOT"},
		}}},
		{{"$project", bson.M{
			"_id":           0,
			"device_id":     "$_id.device_id",
			"area":          "$_id.area",
			"camera_result": 1,
		}}},
		{{"$sort", bson.M{"device_id": 1}}},
		bson.D{{"$skip", (request.PageNo - 1) * request.PageSize}},
		bson.D{{"$limit", request.PageSize}},
	}
	cursor, err := c.Client.Database("camera_management").Collection(fmt.Sprintf("%s_device_camera_data", ucmd.RenameProjectDB(project))).Aggregate(ctx, pipeline)
	if err != nil {
		c.Logger.Errorf("fail to get camera device info, err: %v, filter: %+v, project: %s", err, filter, project)
		return
	}
	if err = cursor.All(ctx, &res); err != nil {
		c.Logger.Errorf("fail to parse camera device info, err: %v, filter: %+v, project: %s", err, filter, project)
		return
	}

	// 摄像头信息
	cameraInfo, err := c.FindCameraInfo(project, bson.M{})
	if err != nil {
		return
	}
	cameraInfoMap := make(map[string]model.CameraInfo)
	for _, ci := range cameraInfo {
		cameraInfoMap[ci.CameraType] = ci
	}
	for i := range res {
		needAcceptance := false
		for j, result := range res[i].CameraResult {
			res[i].CameraResult[j].MaskUrl = cameraInfoMap[result.CameraType].MaskUrl
			res[i].CameraResult[j].PredictResult = result.PredictType
			if lang == "en" {
				res[i].CameraResult[j].CameraName = cameraInfoMap[result.CameraType].CameraNameEn
			} else {
				res[i].CameraResult[j].CameraName = cameraInfoMap[result.CameraType].CameraName
			}
			if result.JudgeResult == 1 { // 人工判断合格
				res[i].NormalCameraTotal += 1
			}
			if result.NeedAcceptance {
				needAcceptance = true
			}
		}
		res[i].CameraTotal = len(cameraInfoMap)
		res[i].NeedAcceptance = needAcceptance
	}

	devices, err := c.Client.Database("camera_management").Collection(fmt.Sprintf("%s_device_camera_data", ucmd.RenameProjectDB(project))).Distinct(ctx, "device_id", filter)
	if err != nil {
		c.Logger.Errorf("fail to get camera device total, err: %v, filter: %+v, project: %s", err, filter, project)
		return
	}
	total = int64(len(devices))

	// 二代站国内欧标站
	if area != um.Europe {
		for i, info := range res {
			if _, ok := umw.EuropeanStandardStationMap[info.DeviceId]; ok {
				for m, _ := range info.CameraResult {
					res[i].CameraResult[m].MaskUrl = "https://api-owl-minio.nioint.com/cameramaskimg/eu/" + res[i].CameraResult[m].CameraType + ".png"
				}
			}
		}
	}
	return
}

func (c *MongoClient) FindOneCameraNewestImage(project, deviceId, cameraType, area string) (map[string]umw.MongoImageInfo, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	dbName := "imageinfo-" + ucmd.RenameProjectDB(project)
	collectionName := deviceId
	collection := c.Client.Database(dbName).Collection(collectionName)
	var mu sync.Mutex
	responseData := make(map[string]umw.MongoImageInfo)
	cameraData := strings.Split(cameraType, ",")
	execFunc := func(i int) {
		newFilter := make(bson.M)
		newFilter["device_id"] = deviceId
		if cameraData[i] == "BUSS-3346" && area == um.Europe {
			newFilter["image_type"] = bson.M{"$in": []int{2, 4}}
		} else {
			if cameraData[i] == "FSSG-2T47" {
				newFilter["image_gen_time"] = bson.M{"$gte": 1699372800000}
			}
			newFilter["camera_type"] = cameraData[i]
		}

		var curr umw.MongoImageInfo
		err := collection.FindOne(ctx, newFilter, options.FindOne().SetSort(bson.M{"image_gen_time": -1})).Decode(&curr)
		if err != nil {
			//fmt.Println(err)
			if err.Error() != "mongo: no documents in result" {
				return
			}
		}
		mu.Lock()
		responseData[cameraData[i]] = curr
		defer mu.Unlock()
	}
	ucmd.ParallelizeExec(len(cameraData), execFunc, 8)
	return responseData, nil
}

func (c *MongoClient) FindOneCameraNewestCCImage(project, deviceId, cameraType, area string, timeRange ...int64) (map[string]umw.MongoImageInfo, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	dbName := "imageinfo-" + ucmd.RenameProjectDB(project)
	collectionName := deviceId
	collection := c.Client.Database(dbName).Collection(collectionName)
	responseData := make(map[string]umw.MongoImageInfo)
	cameraData := strings.Split(cameraType, ",")
	var mu sync.Mutex
	execFunc := func(i int) {
		newFilter := make(bson.M)
		newFilter["image_type"] = 29
		newFilter["camera_type"] = cameraData[i]
		if len(timeRange) >= 2 {
			newFilter["image_gen_time"] = bson.M{"$gte": timeRange[0], "$lt": timeRange[1]}
		}
		var curr umw.MongoImageInfo
		err := collection.FindOne(ctx, newFilter, options.FindOne().SetSort(bson.M{"image_gen_time": -1})).Decode(&curr)
		if err != nil {
			//fmt.Println(err)
			if err.Error() != "mongo: no documents in result" {
				return
			}
		}
		mu.Lock()
		responseData[cameraData[i]] = curr
		defer mu.Unlock()
	}
	ucmd.ParallelizeExec(len(cameraData), execFunc, 10)
	return responseData, nil
}

func (c *MongoClient) GetCameraType(project string) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	dbName := "camera_management"
	collectionName := ucmd.RenameProjectDB(project) + "_camera_info"
	cursor, err := c.Client.Database(dbName).Collection(collectionName).Find(ctx, bson.M{})
	if err != nil {
		return "", err
	}
	var res []model.CameraTypeInfo
	err = cursor.All(ctx, &res)
	if err != nil {
		return "", err
	}
	var resp string
	for _, data := range res {
		resp += data.CameraType + ","
	}
	if len(resp) > 0 {
		resp = resp[:len(resp)-1]
	}
	return resp, nil
}

type EmptyImageCamera struct {
	DeviceId    string   `json:"device_id" bson:"device_id"`
	CameraTypes []string `json:"camera_types" bson:"camera_types"`
}

// GetEmptyImageCamera 找到device_camera_data中没有照片的记录
func (c *MongoClient) GetEmptyImageCamera(project string) (res []EmptyImageCamera, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	pipeline := mongo.Pipeline{
		{{"$match", bson.M{"has_image": false}}},
		{{"$group", bson.M{
			"_id":          "$device_id",
			"camera_types": bson.M{"$push": "$camera_type"},
		}}},
		{{"$project", bson.M{"_id": 0, "device_id": "$_id", "camera_types": 1}}},
	}
	cursor, err := c.Client.Database("camera_management").Collection(fmt.Sprintf("%s_device_camera_data", ucmd.RenameProjectDB(project))).Aggregate(ctx, pipeline)
	if err != nil {
		c.Logger.Errorf("fail to get empty image cameras, err: %v", err)
		return
	}
	if err = cursor.All(ctx, &res); err != nil {
		c.Logger.Errorf("fail to parse empty image cameras, err: %v", err)
		return
	}
	return
}

// CheckCameraNewImage 从imageinfo中查找是否有新的照片
func (c *MongoClient) CheckCameraNewImage(collection *mongo.Collection, area, cameraType, project string) bool {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	newFilter := make(bson.M)
	newFilter["image_type"] = 29
	newFilter["camera_type"] = cameraType
	cnt, fErr := collection.CountDocuments(ctx, newFilter)
	if fErr != nil {
		//fmt.Println(err)
		c.Logger.Warnf("fail to count images, err: %v", fErr)
		return false
	}
	return cnt > 0
}

// UpdateDeviceImageInfo 更新camera_management中的has_image字段
func (c *MongoClient) UpdateDeviceImageInfo(project, area string, records []EmptyImageCamera) {
	for _, record := range records {
		dbName := "imageinfo-" + ucmd.RenameProjectDB(project)
		collectionName := record.DeviceId
		collection := c.Client.Database(dbName).Collection(collectionName)
		var wg sync.WaitGroup
		wg.Add(len(record.CameraTypes))
		for i := range record.CameraTypes {
			go func(cameraType string) {
				defer wg.Done()
				if c.CheckCameraNewImage(collection, area, cameraType, project) {
					if fErr := c.NewMongoEntry(bson.D{{"device_id", record.DeviceId}, {"camera_type", cameraType}}).UpdateOne(
						"camera_management",
						fmt.Sprintf("%s_device_camera_data", ucmd.RenameProjectDB(project)),
						bson.M{"$set": bson.M{"has_image": true}},
						false,
					); fErr != nil {
						c.Logger.Errorf("fail to update device camera info, err: %v", fErr)
						return
					}
				}
			}(record.CameraTypes[i])
		}
		wg.Wait()
	}
	return
}

func (c *MongoClient) GetVehicleBattery(dbName, collectionName string, startDay time.Time) (vehicleList, batteryList []string, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	getName := func(fieldName string) (res []umw.MongoAlgorithmDailyReport, err error) {
		matchStage := bson.D{
			{"$match", bson.M{
				"date":      bson.M{"$gte": startDay, "$lt": startDay.AddDate(0, 0, 1)},
				"errorcode": 0,
			}},
		}
		groupStage := bson.D{
			{"$group", bson.M{
				"_id": fmt.Sprintf("$%s", fieldName),
			}},
		}
		projectStage := bson.D{
			{"$project", bson.M{
				"_id":     0,
				fieldName: "$_id",
			}},
		}
		sortStage := bson.D{{"$sort", bson.D{{fieldName, 1}}}}
		pipeline := mongo.Pipeline{matchStage, groupStage, projectStage, sortStage}
		cursor, err := c.Client.Database(dbName).Collection(collectionName).Aggregate(ctx, pipeline)
		if err != nil {
			c.Logger.Errorf("fail to get %s, err: %v", fieldName, err)
			return
		}
		if err = cursor.All(ctx, &res); err != nil {
			c.Logger.Errorf("parse res err: %s", err.Error())
			return
		}
		return
	}

	res, err := getName("vehicle_type")
	if err != nil {
		return
	}
	for _, report := range res {
		vehicleList = append(vehicleList, report.VehicleType)
	}

	res, err = getName("battery_type")
	if err != nil {
		return
	}
	for _, report := range res {
		batteryList = append(batteryList, report.BatteryType)
	}
	return
}

func (c *MongoClient) GetServiceIdSuccessMap(dbName string, day int64, algId int, serviceMap *sync.Map, deviceId ...string) (res map[string]model.AlgServiceSuccess, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 90*time.Second)
	defer cancel()

	res = make(map[string]model.AlgServiceSuccess)
	startDay := time.UnixMilli(day)
	collectionName := fmt.Sprintf("%s_%s", umw.IntAlgorithmMap[algId], strconv.Itoa(int(time.UnixMilli(day).Month())))
	filter := bson.M{
		"date":      bson.M{"$gte": startDay, "$lt": startDay.AddDate(0, 0, 1)},
		"errorcode": 0,
	}
	if len(deviceId) > 0 {
		filter["device_id"] = deviceId[0]
	}

	var pipeline mongo.Pipeline
	if algId == 6 { // BSA同一订单只取部分结果
		pipeline = mongo.Pipeline{
			bson.D{{"$match", filter}},
			bson.D{{"$sort", bson.M{"date": 1}}},
			bson.D{{"$group", bson.D{
				{"_id", "$service_id"},
				{"device_id", bson.M{"$first": "$device_id"}},
				{"first", bson.M{"$first": "$$ROOT"}},
			}}},
			bson.D{{"$project", bson.M{
				"_id":        0,
				"service_id": "$_id",
				"device_id":  "$device_id",
				"success":    bson.M{"$cond": bson.A{bson.M{"$eq": bson.A{"$first.output_value", 0}}, true, false}},
			}}},
		}
	} else if algId == 16 {
		pipeline = mongo.Pipeline{
			bson.D{{"$match", filter}},
			bson.D{{"$group", bson.D{
				{"_id", "$service_id"},
				{"device_id", bson.M{"$first": "$device_id"}},
				{"first", bson.M{"$first": "$$ROOT"}},
			}}},
			bson.D{{"$project", bson.M{
				"_id":        0,
				"service_id": "$_id",
				"device_id":  "$device_id",
				"success":    bson.M{"$cond": bson.A{bson.M{"$eq": bson.A{"$first.extra_data.open_door_alarm_ts", 0}}, true, false}},
			}}},
		}
	} else {
		output, condition := makeCondition(algId)
		pipeline = mongo.Pipeline{
			bson.D{
				{"$match", filter},
			},
			bson.D{
				{"$group", bson.M{
					"_id":       "$service_id",
					"device_id": bson.M{"$first": "$device_id"},
					"output":    output,
				}},
			},
			bson.D{
				{"$project", bson.M{
					"_id":        0,
					"service_id": "$_id",
					"device_id":  "$device_id",
					"success":    bson.M{"$cond": bson.A{condition, true, false}},
				}},
			},
		}
	}

	cursor, err := c.Client.Database(dbName).Collection(collectionName).Aggregate(ctx, pipeline, options.Aggregate().SetAllowDiskUse(true))
	if err != nil {
		c.Logger.Errorf("fail to get algorithm service success info, err: %v, db: %s, collection: %s, algId: %v", err, dbName, collectionName, algId)
		return
	}
	var resList []model.AlgServiceSuccess
	if err = cursor.All(ctx, &resList); err != nil {
		c.Logger.Errorf("fail to parse cursor, err: %v", err)
		return
	}
	//fmt.Printf("collectionName:%s,day:%d,result amount:%d\n", collectionName, day, len(resList))
	for _, record := range resList {
		//fmt.Printf("%+v\n", record)
		res[record.ServiceId] = record
		serviceMap.Store(record.ServiceId, struct{}{})
	}
	return
}

func makeCondition(algId int) (output, condition bson.M) {
	output, condition = make(bson.M), make(bson.M)
	switch algId {
	case 1, 10, 15:
		if algId == 1 {
			// output_value=1
			output = bson.M{"$max": "$output_value"}
			condition = bson.M{"$eq": bson.A{"$output", 1}}
		} else {
			// output_value=0
			output = bson.M{"$min": "$output_value"}
			condition = bson.M{"$eq": bson.A{"$output", 0}}
		}
	case 2, 3, 5, 6, 7, 8, 9, 11, 12:
		if algId == 3 {
			// 同一service_id，output_value=1出现次数>=1
			output = bson.M{"$sum": "$output_value"}
			condition = bson.M{"$gt": bson.A{"$output", 0}}
		} else if algId == 12 {
			// 同一service_id，output_value=0出现次数>=1
			output = bson.M{"$min": "$output_value"}
			condition = bson.M{"$eq": bson.A{"$output", 0}}
		} else {
			// 同一service_id，output_value=1出现次数=0
			output = bson.M{"$sum": "$output_value"}
			condition = bson.M{"$eq": bson.A{"$output", 0}}
		}
	}
	return
}

func (c *MongoClient) GetParametricFormulaSwitch(project string, day int64) (result map[int]map[string]struct{}, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	dbName := "algorithm"
	collectionName := fmt.Sprintf("parametric-formula-switch-%s", strings.ToLower(project))
	result = make(map[int]map[string]struct{})
	filter := bson.M{
		"date": util.TimeDay(day),
	}
	cursor, err := c.Client.Database(dbName).Collection(collectionName).Find(ctx, filter)
	if err != nil {
		c.Logger.Errorf("fail to get algorithm service success info, err: %v, db: %s, collection: %s", err, dbName, collectionName)
		return
	}
	var resultList []model.ParametricFormulaSwitch
	if err = cursor.All(ctx, &resultList); err != nil {
		c.Logger.Errorf("fail to parse cursor, err: %v", err)
		return
	}
	for _, record := range resultList {
		for aid := range record.AlgorithmOn {
			if result[aid] == nil {
				result[aid] = make(map[string]struct{})
			}
			result[aid][record.DeviceId] = struct{}{}
		}
	}
	return
}

func (c *MongoClient) GetYesterdayParametricFormulaSwitch(project string, day int64, fttAlgList []int) (map[int]map[string]struct{}, []interface{}, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	date := util.TimeDay(day)
	collection := c.Client.Database(project).Collection(model.DEVICES)
	fttAlgRealIdList := make([]int, len(fttAlgList))
	for i, id := range fttAlgList {
		fttAlgRealIdList[i] = model.AiAlgorithmIdMap[project][id]
	}
	pipeline := mongo.Pipeline{
		bson.D{
			{
				"$project",
				bson.M{
					"device_id": "$_id",
					"_id":       0,
					"params": bson.M{
						"$filter": bson.M{
							"input": "$params",
							"as":    "param",
							"cond": bson.M{
								"$in": []interface{}{"$$param.key", fttAlgRealIdList},
							},
						},
					},
				},
			},
		},
	}
	cursor, err := collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, nil, err
	}
	var res []model.DeviceParametricFormula
	err = cursor.All(ctx, &res)
	if err != nil {
		return nil, nil, err
	}
	yesterdayPfsList := make([]interface{}, 0)
	yesterdayPfsMap := make(map[int]map[string]struct{})
	if project == umw.PowerSwap2 {
		yesterdayPfsMap[870124] = make(map[string]struct{})
	}
	for _, info := range res {
		wpvFlag := true
		pfs := model.ParametricFormulaSwitch{
			DeviceId:    info.DeviceId,
			AlgorithmOn: make(map[int]struct{}),
			Date:        date,
		}
		for _, param := range info.Params {
			//fmt.Println(param.Key)
			flag, err := util.JudgeParamSwitchOn(param)
			if err != nil {
				return nil, nil, err
			}
			if param.Key == 870124 || param.Key == 870127 {
				wpvFlag = wpvFlag && flag
			} else {
				if flag {
					pfs.AlgorithmOn[param.Key] = struct{}{}
					if yesterdayPfsMap[param.Key] == nil {
						yesterdayPfsMap[param.Key] = make(map[string]struct{})
					}
					yesterdayPfsMap[param.Key][info.DeviceId] = struct{}{}
				}
			}
		}
		if project == umw.PowerSwap2 && wpvFlag {
			pfs.AlgorithmOn[870124] = struct{}{}
			yesterdayPfsMap[870124][info.DeviceId] = struct{}{}
		}
		yesterdayPfsList = append(yesterdayPfsList, pfs)
	}
	return yesterdayPfsMap, yesterdayPfsList, nil
}

func (c *MongoClient) GetRequestId(deviceId string) (requestId string, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	torqueReportList, err := c.GetTorqueReportList(deviceId)

	for _, report := range torqueReportList {
		cnt, err := c.Client.Database(umw.FactoryData).Collection(umw.TorqueFeatureCalculation).CountDocuments(ctx, bson.M{"request_id": report.RequestId})
		if err != nil {
			c.Logger.Errorf("fail to count documents, err: %v, request_id: %s", report.RequestId)
			continue
		}
		if cnt == 0 {
			return report.RequestId, nil
		}
	}
	return
}

func (c *MongoClient) GetTorqueReportList(deviceId string) (torqueReportList []umw.MongoTorqueReportData, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	cursor, err := c.Client.Database(umw.FactoryData).Collection(umw.TorqueReport).Find(ctx, bson.D{{"device_id", deviceId}}, options.Find().SetSort(bson.M{"insert_ts": -1}).SetProjection(bson.M{"request_id": 1, "servicesinfo": 1}))
	if err != nil {
		c.Logger.Errorf("fail to get torque_report list: %v", err)
		return
	}
	if err = cursor.All(ctx, &torqueReportList); err != nil {
		c.Logger.Errorf("fail to unmarshal torque_report list, err: %v", err)
		return
	}
	return
}
func (c *MongoClient) TorqueDataFindOne(dbName, collectionName string, filter bson.M) (res umw.MongoTestReportData, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	err = c.Client.Database(dbName).Collection(collectionName).FindOne(ctx, filter).Decode(&res)
	return
}
func (c *MongoClient) TorqueDataFind(dbName, collectionName string, filter bson.M) (res []umw.MongoTestReportData, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	cursor, err := c.Client.Database(dbName).Collection(collectionName).Find(ctx, filter)
	if err != nil {
		return
	}
	err = cursor.All(ctx, &res)
	if err != nil {
		return
	}
	return
}

func (c *MongoClient) CalculateAecCpuUsage(dbName, algName string, day int64) (cpuUsage []float64, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	var res []struct {
		CpuAvg  float64 `bson:"cpu_avg"`
		CpuAvg2 float64 `bson:"cpu_avg_2"`
	}
	matchStage := bson.D{{"$match", bson.M{
		"date": bson.M{"$gte": time.UnixMilli(day), "$lt": time.UnixMilli(day).AddDate(0, 0, 1)},
		"cpu_used": bson.M{
			"$exists": true,
			"$not":    bson.M{"$elemMatch": bson.M{"$lt": 0}},
		},
	}}}
	var cursor *mongo.Cursor
	if algName == "BSA" {
		cpuUsage = make([]float64, 2)
		cursor, err = c.Client.Database(dbName).Collection(fmt.Sprintf("%s_%d", algName, int(time.UnixMilli(day).Month()))).Aggregate(ctx, mongo.Pipeline{
			matchStage,
			{{"$sort", bson.M{"date": 1}}},
			{{"$group", bson.M{
				"_id":             "$service_id",
				"first_cpu_used":  bson.M{"$first": "$cpu_used"},
				"second_cpu_used": bson.M{"$last": "$cpu_used"},
			}}},
			{{"$project", bson.M{
				"cpu_used_avg":   bson.M{"$avg": "$first_cpu_used"},
				"cpu_used_avg_2": bson.M{"$avg": "$second_cpu_used"},
			}}},
			{{"$group", bson.M{
				"_id":       primitive.Null{},
				"cpu_avg":   bson.M{"$avg": "$cpu_used_avg"},
				"cpu_avg_2": bson.M{"$avg": "$cpu_used_avg_2"},
			}}},
		})
		if err != nil {
			return
		}
		if err = cursor.All(ctx, &res); err != nil {
			return
		}
		if len(res) > 0 {
			cpuUsage[0] = res[0].CpuAvg
			cpuUsage[1] = res[0].CpuAvg2
		}
		return
	}
	cursor, err = c.Client.Database(dbName).Collection(fmt.Sprintf("%s_%d", algName, int(time.UnixMilli(day).Month()))).Aggregate(ctx, mongo.Pipeline{
		matchStage,
		{{"$project", bson.M{
			"cpu_used_avg": bson.M{"$avg": "$cpu_used"},
		}}},
		{{"$group", bson.M{
			"_id":     primitive.Null{},
			"cpu_avg": bson.M{"$avg": "$cpu_used_avg"},
		}}},
	})
	if err != nil {
		return
	}
	if err = cursor.All(ctx, &res); err != nil {
		return
	}
	if len(res) > 0 {
		cpuUsage = append(cpuUsage, res[0].CpuAvg)
	}
	return
}

func (c *MongoClient) GetCheckedParams(project, deviceId string) ([]model.DeviceParams, error) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*30)
	defer cancel()

	filter := bson.M{}
	if deviceId != "" {
		filter = bson.M{"_id": bson.M{"$in": strings.Split(deviceId, ",")}}
	}
	cursor, err := c.Client.Database(project).Collection(umw.Devices).Find(ctx, filter, options.Find().SetProjection(bson.M{"_id": 1, "params": 1}))
	if err != nil {
		return nil, err
	}
	deviceList := make([]model.DeviceParams, 0)
	if err = cursor.All(ctx, &deviceList); err != nil {
		return nil, err
	}
	garbleMap := make(map[int64]int64)
	if project == umw.PowerSwap2 {
		// value=key-112446，不知道为什么要这么定义
		// 查找赤兔参数点：在<project>.params根据description搜索
		garbleMap = map[int64]int64{
			870003: 757657, //人体识别状态
			870010: 757664, //车轮定位
			870062: 757616, //电池上表面异物识别
			870064: 757618, //AI功能开关
			870065: 757619, //枪头掉落检测开关
			870074: 757628, //推杆自适应改装轮毂开关
			870080: 757634, //AI检测卷帘门开关状态
			870076: 757630, //泊车过程人体检测功能开关
			870087: 757641, //车辆进站方向检测
			870112: 757666, //SES功能开关
			870113: 757667, //SAPA功能开关
			870124: 757678, //车辆压推杆检测
			870127: 757681, //启用WPV影响鉴权结果
			870137: 757691, //平台灯控制开关
		}
	} else if project == umw.PUS3 {
		garbleMap = map[int64]int64{
			902303: 789857, // 人体识别状态
			902307: 789861, // 车轮定位
			902308: 789862, // 电池上表面异物识别
			902309: 789863, // AI功能
			902310: 789864, // 枪头掉落检测开关
			902407: 789961, // 车辆压推杆检测
			902411: 789965, // 泊车过程中的行人检测
			902422: 789976, // 泊车占位开关
			902435: 789989, // 平台灯功能开关
			902436: 789990, // 车牌识别算法开关
			902409: 789963, // 换电外电池倒仓使能
		}
	} else if project == umw.PUS4 {
		garbleMap = map[int64]int64{
			972011: 859565, // 人体识别状态
			972013: 859567, //车轮定位
			972014: 859568, //电池上表面异物识别
			972015: 859569, //AI功能
			972023: 859577, // 压推杆检测
			972026: 859580, //泊车过程中的行人检测
			972037: 859591, //泊车占位开关
			972025: 859579, //电池倒仓使能
		}
	}
	newDeviceList := make([]model.DeviceParams, 0)
	for _, device := range deviceList {
		params := make([]model.DeploymentParams, 0)
		if device.Params != nil {
			for _, param := range *device.Params {
				if _, ok := garbleMap[param.Key]; ok {
					params = append(params, model.DeploymentParams{
						Key:   garbleMap[param.Key],
						Value: param.Value,
						Type:  param.Type,
					})
				}
			}
		}
		if len(params) == 0 {
			newDeviceList = append(newDeviceList, model.DeviceParams{
				ID: device.ID,
			})
		} else {
			newDeviceList = append(newDeviceList, model.DeviceParams{
				ID:     device.ID,
				Params: &params,
			})
		}
	}
	return newDeviceList, err
}

type AlgServiceTime struct {
	ServiceId string `json:"service_id" bson:"service_id"`
	EndTime   int64  `json:"end_time" bson:"end_time"`
}

func (c *MongoClient) GetBSASortedList(dbName string, filter bson.D, startDay time.Time) (res []AlgServiceTime, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*30)
	defer cancel()

	pipeline := mongo.Pipeline{
		bson.D{{"$match", filter}},
		bson.D{{"$group", bson.D{
			{"_id", "$service_id"},
			{"first", bson.M{"$first": "$$ROOT"}},
		}}},
		bson.D{{"$project", bson.D{
			{"service_id", "$_id"},
			{"end_time", "$first.end_time"},
		}}},
		bson.D{{"$sort", bson.M{"end_time": 1}}},
	}
	cursor, err := c.Client.Database(dbName).Collection(fmt.Sprintf("BSA_%s", strconv.Itoa(int(startDay.Month())))).Aggregate(ctx, pipeline, options.Aggregate().SetAllowDiskUse(true))
	if err != nil {
		c.Logger.Errorf("aggregate err: %s, collection: %s", err.Error(), fmt.Sprintf("BSA_%s", strconv.Itoa(int(startDay.Month()))))
		return
	}
	if err = cursor.All(ctx, &res); err != nil {
		c.Logger.Errorf("parse from cursor err: %s", err.Error())
		return
	}
	return
}

func (c *MongoClient) UpsertSapaParkingOccupation(dbName, colName string, filter bson.D, update interface{}, idx ...IndexOption) error {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	if len(idx) != 0 {
		if err := createIndex(ctx, c.Client, dbName, colName, idx...); err != nil {
			return fmt.Errorf("create index failed: %v", err)
		}
	}
	_, err := c.Client.Database(dbName).Collection(colName).UpdateOne(ctx, filter, bson.M{"$set": update}, options.Update().SetUpsert(true))
	return err
}

// CountServices 计算一段时间内的订单数量
func (c *MongoClient) CountServices(project string, startTime, endTime int64, opts ...bson.M) (int64, error) {
	var serviceCnt int64
	serviceFilter := make(bson.M)
	if len(opts) > 0 && len(opts[0]) > 0 {
		serviceFilter = opts[0]
	}
	serviceFilter["date"] = bson.M{"$gte": time.UnixMilli(startTime), "$lte": time.UnixMilli(endTime)}

	for collection := range util.ParseTimeRangeList(startTime, endTime) {
		cnt, err := c.CountDocuments(fmt.Sprintf("%s-%s", umw.ServiceInfo, ucmd.RenameProjectDB(project)), collection, serviceFilter)
		if err != nil {
			c.Logger.Errorf("fail to count services, err: %v, filter: %s, project: %s", err, ucmd.ToJsonStrIgnoreErr(serviceFilter), project)
			return 0, err
		}
		serviceCnt += cnt
	}
	return serviceCnt, nil
}

// GetServiceTime 获取serviceList中所有订单的信息
func (c *MongoClient) GetServiceTime(dbName, collectionName string, serviceList []string) (map[string]umw.MongoServiceInfo, error) {
	filter := bson.D{
		{"service_id", bson.M{"$in": serviceList}},
	}
	byteData, err := c.NewMongoEntry(filter).ListAll(dbName, collectionName, Ordered{})
	if err != nil {
		c.Logger.Errorf("fail to list all services, err: %v, filter: %s", err, ucmd.ToJsonStrIgnoreErr(filter))
		return nil, err
	}
	var list []umw.MongoServiceInfo
	if err = json.Unmarshal(byteData, &list); err != nil {
		c.Logger.Errorf("fail to unmarshal service info, err: %v, filter: %s", err, ucmd.ToJsonStrIgnoreErr(filter))
		return nil, err
	}
	res := make(map[string]umw.MongoServiceInfo)
	for _, item := range list {
		res[item.ServiceId] = item
	}
	return res, err
}

// GetDeviceHourlyService 计算一段时间内每个站的70度电池和100度电池的服务数量
func (c *MongoClient) GetDeviceHourlyService(ctx context.Context, project string, startTime, endTime time.Time) ([]model.DeviceHourlyService, error) {
	collections := util.ParseTimeRangeList(startTime.UnixMilli(), startTime.UnixMilli())
	var collectionName string
	for coll := range collections {
		collectionName = coll
	}
	pipeline := mongo.Pipeline{
		bson.D{{"$match", bson.M{
			"date":                                bson.M{"$lte": endTime, "$gte": startTime},
			"ev_bms_battery_usable_capacity_type": bson.M{"$exists": true},
		}}},
		bson.D{{"$group", bson.M{
			"_id": "$device_id",
			"service_count_70": bson.M{
				"$sum": bson.M{
					"$cond": bson.A{
						bson.M{
							"$in": bson.A{"ev_bms_battery_usable_capacity_type", bson.A{0, 1, 3}},
						},
						1,
						0,
					},
				},
			},
			"service_count_100": bson.M{
				"$sum": bson.M{
					"$cond": bson.A{
						bson.M{
							"ev_bms_battery_usable_capacity_type": 2,
						},
						1,
						0,
					},
				},
			},
		}}},
		bson.D{{"$project", bson.M{
			"_id":               0,
			"device_id":         "$_id",
			"service_count_70":  1,
			"service_count_100": 1,
		}}},
	}
	cursor, err := c.Client.Database(fmt.Sprintf("%s-%s", umw.ServiceInfo, ucmd.RenameProjectDB(project))).Collection(collectionName).Aggregate(ctx, pipeline)
	if err != nil {
		c.Logger.Errorf("fail to aggregate: %v, pipeline: %s", err, ucmd.ToJsonStrIgnoreErr(pipeline))
		return nil, err
	}
	var res []model.DeviceHourlyService
	if err = cursor.All(ctx, &res); err != nil {
		c.Logger.Errorf("fail to unmarshal cursor: %v, pipeline: %s", err, ucmd.ToJsonStrIgnoreErr(pipeline))
		return nil, err
	}
	return res, nil
}

func (c *MongoClient) UpdateOneWithIdx(dbName, colName string, filter bson.D, update interface{}, upsert bool, idx ...IndexOption) (err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	if len(idx) != 0 {
		if err := createIndex(ctx, c.Client, dbName, colName, idx...); err != nil {
			return fmt.Errorf("create index failed: %v", err)
		}
	}
	_, err = c.Client.Database(dbName).Collection(colName).UpdateOne(ctx, filter, bson.M{"$set": update}, options.Update().SetUpsert(upsert))
	return
}

func (c *MongoClient) UpdateOne(dbName, colName string, filter bson.D, update interface{}, upsert ...bool) (err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	var forceUpdate bool
	if len(upsert) != 0 {
		forceUpdate = upsert[0]
	}

	_, err = c.Client.Database(dbName).Collection(colName).UpdateOne(ctx, filter, bson.M{"$set": update}, options.Update().SetUpsert(forceUpdate))
	return
}
