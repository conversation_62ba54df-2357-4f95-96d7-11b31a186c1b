package client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"reflect"
	"strings"
	"time"

	"github.com/avast/retry-go"
	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkdrive "github.com/larksuite/oapi-sdk-go/v3/service/drive/v1"
	larkwiki "github.com/larksuite/oapi-sdk-go/v3/service/wiki/v2"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	"git.nevint.com/golang-libs/common-utils/larkservice"
	"git.nevint.com/welkin2/welkin-backend/model"
	"git.nevint.com/welkin2/welkin-backend/util"
)

type WikiClient struct {
	*lark.Client
	appId       string
	appSecret   string
	spaceId     string
	shareFolder string
}

func NewWikiClient(appId, appSecret string) *WikiClient {
	return &WikiClient{
		Client:    lark.NewClient(appId, appSecret),
		appId:     appId,
		appSecret: appSecret,
	}
}

func (wc *WikiClient) SpaceId(spaceId string) *WikiClient {
	wc.spaceId = spaceId
	return wc
}

func (wc *WikiClient) ShareFolder(shareFolder string) *WikiClient {
	wc.shareFolder = shareFolder
	return wc
}

// CreateVersion 创建一个文档版本
func (wc *WikiClient) CreateVersion(wikiToken, versionName string) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second * 10)
	defer cancel()
	// 获取知识库节点信息
	gReq := larkwiki.NewGetNodeSpaceReqBuilder().
		Token(wikiToken).
		Build()
	gResp, err := wc.Wiki.Space.GetNode(ctx, gReq)
	if err != nil {
		return fmt.Errorf("fail to get wiki node info, err: %v, wikiToken: %s, versionName: %s", err, wikiToken, versionName)
	}
	if !gResp.Success() {
		return fmt.Errorf("get wiki node info, lark server err, code: %v, msg: %v, requestId: %v, wikiToken: %s, versionName: %s", gResp.Code, gResp.Msg, gResp.RequestId(), wikiToken, versionName)
	}
	// 创建版本
	accessToken, err := larkservice.GetTenantAccessToken(wc.appId, wc.appSecret)
	if err != nil {
		return fmt.Errorf("fail to get access token, err: %v, wikiToken: %s", err, wikiToken)
	}
	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:    fmt.Sprintf("https://open.feishu.cn/open-apis/drive/v1/files/%s/versions", *gResp.Data.Node.ObjToken),
		Method: "POST",
		Header: map[string]string{
			"Authorization": fmt.Sprintf("Bearer %s", accessToken),
			"Content-Type": "application/json",
		},
		RequestBody: map[string]interface{}{
			"name": versionName,
			"obj_type": *gResp.Data.Node.ObjType,
		},
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		return fmt.Errorf("fail to create file version, err: %v, wikiToken: %s, versionName: %s", err, wikiToken, versionName)
	}
	defer body.Close()
	data, _ := io.ReadAll(body)
	if statusCode/100 != 2 {
		return fmt.Errorf("status code: %d, err: %s", statusCode, string(data))
	}
	var cResp model.CreateFileVersionResp
	if err = json.Unmarshal(data, &cResp); err != nil {
		return fmt.Errorf("fail to unmarshal response, err: %v, body: %s", err, string(data))
	}
	if cResp.Code != 0 {
		return fmt.Errorf("create file version, lark server err, code: %v, msg: %v, wikiToken: %s, versionName: %s", cResp.Code, cResp.Msg, wikiToken, versionName)
	}
	return nil
}

// ImportFile 上传并导入本地文件
func (wc *WikiClient) ImportFile(downloadUrl, filename, objType string) (url string, extraInfo []string, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second * 30)
	defer cancel()

	// 下载workflow附件
	body, dErr := util.DownloadFile(downloadUrl)
	if dErr != nil {
		err = fmt.Errorf("fail to download file: %s, err: %v", downloadUrl, dErr)
		return
	}
	parts := strings.Split(filename, ".")
	fileExtension := parts[len(parts)-1]

	// 上传素材
	extra := fmt.Sprintf("{\"obj_type\": \"%s\", \"file_extension\": \"%s\"}", objType, fileExtension)
	req := larkdrive.NewUploadAllMediaReqBuilder().
		Body(larkdrive.NewUploadAllMediaReqBodyBuilder().
			FileName(filename).
			ParentType("ccm_import_open").
			ParentNode("").
			Size(len(body)).
			Extra(extra).
			File(bytes.NewReader(body)).
			Build()).
		Build()
	var resp *larkdrive.UploadAllMediaResp
	err = retry.Do(func() error {
		var uErr error
		resp, uErr = wc.Drive.Media.UploadAll(ctx, req)
		if uErr != nil {
			return fmt.Errorf("fail to upload media, err: %v, filename: %s, extension: %s, objType: %s", uErr, filename, fileExtension, objType)
		}
		if !resp.Success() {
			return fmt.Errorf("upload media, lark server err, code: %v, msg: %v, requestId: %v, filename: %s, extension: %s, objType: %s", resp.Code, resp.Msg, resp.RequestId(), filename, fileExtension, objType)
		}
		if resp.Data.FileToken == nil {
			return fmt.Errorf("upload media, empty file token, filename: %s, extension: %s, objType: %s", filename, fileExtension, objType)
		}
		return nil
	}, []retry.Option{
		retry.Delay(time.Second),
		retry.Attempts(3),
		retry.LastErrorOnly(true),
	}...)
	if err != nil {
		return
	}

	// 导入素材
	importReq := larkdrive.NewCreateImportTaskReqBuilder().
		ImportTask(larkdrive.NewImportTaskBuilder().
			FileExtension(fileExtension).
			FileToken(*resp.Data.FileToken).
			Type(objType).
			Point(larkdrive.NewImportTaskMountPointBuilder().MountType(1).MountKey(wc.shareFolder).Build()).
			Build()).
		Build()
	var importResp *larkdrive.CreateImportTaskResp
	err = retry.Do(func() error {
		var iErr error
		importResp, iErr = wc.Drive.ImportTask.Create(ctx, importReq)
		if iErr != nil {
			return fmt.Errorf("fail to import task, err: %v, fileToken: %s, filename: %s, extension: %s, objType: %s", iErr, *resp.Data.FileToken, filename, fileExtension, objType)
		}
		if !importResp.Success() {
			return fmt.Errorf("import task, lark server err, code: %v, msg: %v, requestId: %v, fileToken: %s, filename: %s, extension: %s, objType: %s", importResp.Code, importResp.Msg, importResp.RequestId(), *resp.Data.FileToken, filename, fileExtension, objType)
		}
		if importResp.Data.Ticket == nil {
			return fmt.Errorf("import task, empty ticket, fileToken: %s, filename: %s, extension: %s, objType: %s", *resp.Data.FileToken, filename, fileExtension, objType)
		}
		return nil
	}, []retry.Option{
		retry.Delay(time.Second),
		retry.Attempts(3),
		retry.LastErrorOnly(true),
	}...)

	// 等待导入结束
	for {
		waitReq := larkdrive.NewGetImportTaskReqBuilder().
			Ticket(*importResp.Data.Ticket).
			Build()
		var waitResp *larkdrive.GetImportTaskResp
		waitResp, err = wc.Drive.ImportTask.Get(ctx, waitReq)
		if err != nil {
			err = fmt.Errorf("fail to get import task, err: %v, ticket: %s, fileToken: %s, filename: %s, extension: %s, objType: %s", err, *importResp.Data.Ticket, *resp.Data.FileToken, filename, fileExtension, objType)
			return
		}
		if !waitResp.Success() {
			err = fmt.Errorf("get import task, lark server err, code: %v, msg: %v, requestId: %v, ticket: %s, fileToken: %s, filename: %s, extension: %s, objType: %s", waitResp.Code, waitResp.Msg, waitResp.RequestId(), *importResp.Data.Ticket, *resp.Data.FileToken, filename, fileExtension, objType)
			return
		}
		if waitResp.Data.Result != nil {
			if jobStatus := waitResp.Data.Result.JobStatus; jobStatus != nil {
				if *jobStatus == 0 {
					url = *waitResp.Data.Result.Url
					extraInfo = waitResp.Data.Result.Extra
					break
				} else if *jobStatus > 2 {
					err = fmt.Errorf("get import task, import error, result: %+v, ticket: %s, fileToken: %s, filename: %s, extension: %s, objType: %s", *waitResp.Data.Result, *importResp.Data.Ticket, *resp.Data.FileToken, filename, fileExtension, objType)
					return
				} else {
					time.Sleep(time.Second)
				}
			} else {
				err = fmt.Errorf("get import task, empty job status, result: %+v, ticket: %s, fileToken: %s, filename: %s, extension: %s, objType: %s", *waitResp.Data.Result, *importResp.Data.Ticket, *resp.Data.FileToken, filename, fileExtension, objType)
				return
			}
		} else {
			err = fmt.Errorf("get import task, empty result, ticket: %s, fileToken: %s, filename: %s, extension: %s, objType: %s", *importResp.Data.Ticket, *resp.Data.FileToken, filename, fileExtension, objType)
			return
		}
	}
	return
}

// UploadFile 上传20MB以内的文件
func (wc *WikiClient) UploadFile(downloadUrl, filename string) (token string, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second * 30)
	defer cancel()
	// 下载workflow附件
	body, err := util.DownloadFile(downloadUrl)
	if err != nil {
		return "", fmt.Errorf("fail to download file: %s, filename: %s, err: %v", downloadUrl, filename, err)
	}
	req := larkdrive.NewUploadAllFileReqBuilder().
		Body(larkdrive.NewUploadAllFileReqBodyBuilder().
			FileName(filename).
			ParentType(`explorer`).
			ParentNode(wc.shareFolder).
			Size(len(body)).
			File(bytes.NewReader(body)).
			Build()).
		Build()
	resp, err := wc.Drive.File.UploadAll(ctx, req)
	if err != nil {
		return "", fmt.Errorf("fail to upload file, err: %v, downloadUrl: %s, filename: %s", err, downloadUrl, filename)
	}
	if !resp.Success() {
		return "", fmt.Errorf("upload file, lark server err, code: %v, msg: %v, requestId: %v, downloadUrl: %s, filename: %s", resp.Code, resp.Msg, resp.RequestId(), downloadUrl, filename)
	}
	if resp.Data.FileToken != nil {
		token = *resp.Data.FileToken
	}
	return
}

// GetParentNode 获取父节点token
func (wc *WikiClient) GetParentNode(targetWikiToken, fileClass string) (parentNode string, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second * 10)
	defer cancel()
	req := larkwiki.NewListSpaceNodeReqBuilder().
		SpaceId(wc.spaceId).
		PageSize(20).
		ParentNodeToken(targetWikiToken).
		Build()
	resp, err := wc.Wiki.SpaceNode.List(ctx, req)
	if err != nil {
		return "", fmt.Errorf("fail to get parent node, err: %v, targetWikiToken: %s", err, targetWikiToken)
	}
	if !resp.Success() {
		return "", fmt.Errorf("get parent node, lark server err, code: %v, msg: %v, requestId: %v, targetWikiToken: %s", resp.Code, resp.Msg, resp.RequestId(), targetWikiToken)
	}
	for _, node := range resp.Data.Items {
		if node.Title != nil && *node.Title == fileClass {
			if node.NodeToken != nil {
				parentNode = *node.NodeToken
				return
			}
		}
	}
	return "", fmt.Errorf("cannot find fileClass: %s, targetWikiToken: %s", fileClass, targetWikiToken)
}

// GetFileInfo 获取云文档信息
func (wc *WikiClient) GetFileInfo(fileToken string) (title string, objType string, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second * 20)
	defer cancel()
	var pageToken *string
	var resp *larkdrive.ListFileResp
	for {
		reqBuilder := larkdrive.NewListFileReqBuilder().
			PageSize(50).
			FolderToken(wc.shareFolder)
		if pageToken != nil {
			reqBuilder = reqBuilder.PageToken(*pageToken)
		}
		resp, err = wc.Drive.File.List(ctx, reqBuilder.Build())
		if err != nil {
			err = fmt.Errorf("fail to get file info, err: %v, fileToken: %s", err, fileToken)
			return
		}
		if !resp.Success() {
			err = fmt.Errorf("get file info, lark server err, code: %v, msg: %v, requestId: %v, fileToken: %s", resp.Code, resp.Msg, resp.RequestId(), fileToken)
			return
		}
		for _, f := range resp.Data.Files {
			if f.Token != nil && *f.Token == fileToken {
				title = *f.Name
				objType = *f.Type
				return
			}
		}
		if resp.Data.HasMore == nil || !*resp.Data.HasMore {
			break
		}
		pageToken = resp.Data.NextPageToken
		if pageToken == nil {
			break
		}
	}
	err = fmt.Errorf("fail to find file in shared folder, fileToken: %s", fileToken)
	return
}

// GetWikiNodeInfo 获取知识库节点信息
func (wc *WikiClient) GetWikiNodeInfo(nodeToken string) (title, objType, parentToken string, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second * 10)
	defer cancel()
	req := larkwiki.NewGetNodeSpaceReqBuilder().
		Token(nodeToken).
		Build()
	resp, err := wc.Wiki.Space.GetNode(ctx, req)
	if err != nil {
		err = fmt.Errorf("fail to get wiki node info, err: %v, nodeToken: %s", err, nodeToken)
		return
	}
	if !resp.Success() {
		err = fmt.Errorf("get wiki node info, lark server err, code: %v, msg: %v, requestId: %v, nodeToken: %s", resp.Code, resp.Msg, resp.RequestId(), nodeToken)
		return
	}
	if resp.Data.Node.Title != nil {
		title = *resp.Data.Node.Title
	}
	if resp.Data.Node.ObjType != nil {
		objType = *resp.Data.Node.ObjType
	}
	if resp.Data.Node.ParentNodeToken != nil {
		parentToken = *resp.Data.Node.ParentNodeToken
	}
	return
}

// MoveDocToWiki 将云文档移动到知识库
func (wc *WikiClient) MoveDocToWiki(fileToken, objType, parentToken string) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second * 30)
	defer cancel()
	req := larkwiki.NewMoveDocsToWikiSpaceNodeReqBuilder().
		SpaceId(wc.spaceId).
		Body(larkwiki.NewMoveDocsToWikiSpaceNodeReqBodyBuilder().
			ParentWikiToken(parentToken).
			ObjType(objType).
			ObjToken(fileToken).
			Build()).
		Build()
	var resp *larkwiki.MoveDocsToWikiSpaceNodeResp
	err := retry.Do(func() error {
		var err error
		resp, err = wc.Wiki.SpaceNode.MoveDocsToWiki(ctx, req)
		if err != nil {
			return fmt.Errorf("fail to move docs to wiki, err: %v, fileToken: %s, objType: %s, targetWikiToken: %s", err, fileToken, objType, parentToken)
		}
		if !resp.Success() {
			return fmt.Errorf("lark server err, code: %v, msg: %v, fileToken: %v, fileUrl: %s, objType: %s, targetWikiToken: %s", resp.Code, resp.Msg, resp.RequestId(), fileToken, objType, parentToken)
		}
		return nil
	}, []retry.Option{
		retry.Delay(time.Second),
		retry.Attempts(3),
		retry.LastErrorOnly(true),
	}...)
	if err != nil {
		return "", err
	}

	respData := resp.Data
	wikiToken := respData.WikiToken
	for wikiToken == nil {
		if respData.TaskId == nil {
			return "", fmt.Errorf("fail to get task id, resp data: %v", respData)
		}
		// 操作尚未完成
		taskReq := larkwiki.NewGetTaskReqBuilder().
			TaskId(*respData.TaskId).
			TaskType("move").
			Build()
		taskResp, tErr := wc.Wiki.Task.Get(ctx, taskReq)
		if tErr != nil {
			return "", fmt.Errorf("fail to get task, err: %v, taskId: %s", err, *respData.TaskId)
		}
		if !taskResp.Success() {
			return "", fmt.Errorf("lark server err, code: %v, msg: %v, requestId: %v, taskId: %s", taskResp.Code, taskResp.Msg, taskResp.RequestId(), *respData.TaskId)
		}
		if len(taskResp.Data.Task.MoveResult) > 0 {
			res := taskResp.Data.Task.MoveResult[0]
			statusCode := *res.Status
			if statusCode > 0 {
				time.Sleep(time.Second)
				continue
			} else if statusCode < 0 {
				return "", fmt.Errorf("get task status code < 0, taskId: %v", *respData.TaskId)
			}
			wikiToken = res.Node.NodeToken
		} else {
			return "", fmt.Errorf("fail to get move result, taskId: %v", *respData.TaskId)
		}
	}
	return *wikiToken, nil
}

// DeleteShortcuts 删除待审核目录下的所有快捷方式
func (wc *WikiClient) DeleteShortcuts() (err error) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second * 20)
	defer cancel()
	var pageToken *string
	var resp *larkdrive.ListFileResp
	for {
		reqBuilder := larkdrive.NewListFileReqBuilder().
			PageSize(50).
			FolderToken(wc.shareFolder)
		if pageToken != nil {
			reqBuilder = reqBuilder.PageToken(*pageToken)
		}
		resp, err = wc.Drive.File.List(ctx, reqBuilder.Build())
		if err != nil {
			err = fmt.Errorf("fail to delete shortcuts, err: %v", err)
			return
		}
		if !resp.Success() {
			err = fmt.Errorf("delete shortcuts, lark server err, code: %v, msg: %v, requestId: %v", resp.Code, resp.Msg, resp.RequestId())
			return
		}
		for _, f := range resp.Data.Files {
			if f.Type != nil && *f.Type == "shortcut" {
				if f.Token == nil {
					continue
				}
				err = retry.Do(
					func() error {
						return wc.DeleteFile(*f.Token, *f.Type)
					},
					[]retry.Option{
						retry.Delay(time.Second),
						retry.Attempts(3),
						retry.LastErrorOnly(true),
					}...)
				if err != nil {
					return fmt.Errorf("fail to delete shortcut, err: %v, fileToken: %s", err, *f.Token)
				}
			}
		}
		if resp.Data.HasMore == nil || !*resp.Data.HasMore {
			break
		}
		pageToken = resp.Data.NextPageToken
		if pageToken == nil {
			break
		}
	}
	return
}

// DeleteFile 删除文件
func (wc *WikiClient) DeleteFile(token, fileType string) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second * 20)
	defer cancel()
	req := larkdrive.NewDeleteFileReqBuilder().
		FileToken(token).
		Type(fileType).
		Build()
	resp, err := wc.Drive.File.Delete(ctx, req)
	if err != nil {
		return fmt.Errorf("fail to delete file, err: %v, fileToken: %s, fileType: %s", err, token, fileType)
	}
	if !resp.Success() {
		return fmt.Errorf("delete file, lark server err, code: %v, msg: %v, requestId: %v, fileToken: %s, fileType: %s", resp.Code, resp.Msg, resp.RequestId(), token, fileType)
	}
	if resp.Data.TaskId == nil {
		return nil
	}
	tReq := larkdrive.NewTaskCheckFileReqBuilder().
		TaskId(*resp.Data.TaskId).
		Build()
	finish := false
	for !finish {
		tResp, tErr := wc.Drive.File.TaskCheck(ctx, tReq)
		if tErr != nil {
			return fmt.Errorf("fail to check task, err: %v, fileToken: %s, fileType: %s, taskId: %s", tErr, token, fileType, *resp.Data.TaskId)
		}
		if !tResp.Success() {
			return fmt.Errorf("check task, lark server err, code: %v, msg: %v, requestId: %v, fileToken: %s, fileType: %s, taskId: %s", tResp.Code, tResp.Msg, tResp.RequestId(), token, fileType, *resp.Data.TaskId)
		}
		if tResp.Data.Status != nil {
			if *tResp.Data.Status == "success" {
				finish = true
			} else if *tResp.Data.Status == "fail" {
				return fmt.Errorf("check task, status fail, fileToken: %s, fileType: %s, taskId: %s", token, fileType, *resp.Data.TaskId)
			} else {
				time.Sleep(time.Millisecond * 200)
			}
		}
	}
	return nil
}

// MoveWiki 移动知识库文件
func (wc *WikiClient) MoveWiki(origSpaceId, wikiToken, targetSpaceId, targetParent string) (token string, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second * 10)
	defer cancel()
	req := larkwiki.NewMoveSpaceNodeReqBuilder().
		SpaceId(origSpaceId).
		NodeToken(wikiToken).
		Body(larkwiki.NewMoveSpaceNodeReqBodyBuilder().
			TargetParentToken(targetParent).
			TargetSpaceId(targetSpaceId).
			Build()).
		Build()
	resp, err := wc.Wiki.SpaceNode.Move(ctx, req)
	if err != nil {
		return "", fmt.Errorf("fail to move wiki, err: %v, wikiToken: %s, targetParent: %s", err, wikiToken, targetParent)
	}
	if !resp.Success() {
		return "", fmt.Errorf("move wiki, lark server err, code: %v, msg: %v, requestId: %v, wikiToken: %s, targetParent: %s", resp.Code, resp.Msg, resp.RequestId(), wikiToken, targetParent)
	}
	if resp.Data.Node.NodeToken != nil {
		token = *resp.Data.Node.NodeToken
	}
	return
}

// ChangePermission 修改文件权限
func (wc *WikiClient) ChangePermission(fileToken, fileType string, permission larkservice.LarkFilePermission) (err error) {
	accessToken, err := larkservice.GetTenantAccessToken(wc.appId, wc.appSecret)
	if err != nil {
		return fmt.Errorf("fail to get access token, err: %v, fileToken: %s, fileType: %s, permission: %+v", err, fileToken, fileType, permission)
	}
	requestBody := make(map[string]interface{})
	pType := reflect.TypeOf(permission)
	pValue := reflect.ValueOf(permission)
	for i := 0; i < pType.NumField(); i++ {
		field := pType.Field(i)
		value := pValue.FieldByName(field.Name).String()
		if value != "" {
			jsonTag := field.Tag.Get("json")
			requestBody[jsonTag] = value
		}
	}
	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:    fmt.Sprintf("https://open.feishu.cn/open-apis/drive/v2/permissions/%s/public?type=%s", fileToken, fileType),
		Method: "PATCH",
		Header: map[string]string{
			"Authorization": fmt.Sprintf("Bearer %s", accessToken),
			"Content-Type": "application/json; charset=utf-8",
		},
		RequestBody: requestBody,
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		return fmt.Errorf("fail to change file permission, err: %v, fileToken: %s, fileType: %s, permission: %+v", err, fileToken, fileType, permission)
	}
	defer body.Close()
	data, _ := io.ReadAll(body)
	if statusCode/100 != 2 {
		return fmt.Errorf("status code: %d, err: %s, fileToken: %s, fileType: %s, permission: %+v", statusCode, string(data), fileToken, fileType, permission)
	}
	var cResp model.CreateFileVersionResp
	if err = json.Unmarshal(data, &cResp); err != nil {
		return fmt.Errorf("fail to unmarshal response, err: %v, body: %s, fileToken: %s, fileType: %s, permission: %+v", err, string(data), fileToken, fileType, permission)
	}
	if cResp.Code != 0 {
		return fmt.Errorf("change file permission, lark server err, code: %v, msg: %v, fileToken: %s, fileType: %s, permission: %+v", cResp.Code, cResp.Msg, fileToken, fileType, permission)
	}

	return
}
