package client

import (
	"bytes"
	"database/sql"
	"io/ioutil"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	udao "git.nevint.com/golang-libs/common-utils/dao"
	ulog "git.nevint.com/golang-libs/common-utils/logger"
	um "git.nevint.com/golang-libs/common-utils/model"
	"git.nevint.com/welkin2/welkin-backend/config"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
)

type Router struct {
	*gin.Engine
	Watcher
}

type Watcher interface {
	Mongodb() *MongoClient
	MongodbSupportTimeseries() *MongoClient
	PLCMongodb() *MongoClient
	RbMongodb() *MongoClient
	TDEngine() *sql.DB
	Redis() *udao.RedisClient
	Grpc() *GRPCClient
}

type watcher struct {
	Config *ucfg.Config
	Logger *zap.SugaredLogger
}

var globalWatcher *watcher

func GetWatcher() Watcher {
	if globalWatcher != nil {
		return globalWatcher
	}
	globalWatcher = &watcher{
		Config: config.Cfg,
		Logger: log.Logger,
	}
	return globalWatcher
}

func NewWatcherByParam(cfg *ucfg.Config, log *zap.SugaredLogger) Watcher {
	return &watcher{
		Config: cfg,
		Logger: log,
	}
}

func NewWatcher(e *gin.Engine, cfg *ucfg.Config, verbose bool) Watcher {
	logger := log.Logger.Named("Watcher")
	logger.Info("Init service API group")
	e.Use(gin.Recovery())
	e.Use(ulog.AccessLoggerForGin())
	e.Use(LogWithRequestId())
	e.Use(LogAbnormalRequest())
	// if the verbose is true, set the gin mode to debug
	if verbose {
		// print the request body verbosely
		reqLogger := ulog.NewHttpLogger(logger)
		e.Use(reqLogger.HttpLogger())
	} else {
		gin.SetMode(gin.ReleaseMode)
	}
	return &watcher{
		Config: cfg,
		Logger: logger,
	}
}
func LogWithRequestId() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := c.Request.Header.Get("X-Request-ID")
		c.Writer.Header().Set("X-Request-ID", requestID)
		logger := log.Logger.With("request_id", requestID)
		c.Set("logger", logger)
		c.Next()
	}
}

// LogAbnormalRequest
// response 非200 打印日志
// 时延过长的
// 配置需要记录的
func LogAbnormalRequest() gin.HandlerFunc {
	return func(c *gin.Context) {
		startTime := time.Now()
		// 获取body数据用于打印，取完数据放回body 避免后续使用为空
		body, err := ioutil.ReadAll(c.Request.Body)
		if err != nil {
			log.CtxLog(c).Errorf("ioutil.ReadAll err:%v", err)
			um.FailWithInternalServerError(c, &model.Response{}, err.Error())
			c.Abort()
		}
		c.Request.Body = ioutil.NopCloser(bytes.NewBuffer(body))

		c.Next()

		status := c.Writer.Status()
		// 配置忽略的路径
		if _, ok := config.TestData.IgnorePaths[c.Request.RequestURI]; !ok {
			// 非200
			if status != http.StatusOK {
				keyValue := map[string]any{}
				for key, value := range c.Keys {
					keyValue[key] = value
				}
				log.CtxLog(c).Errorf("response_not_200. url:%v body:%v headers:%v ctx keys:%v", c.Request.URL.String(), string(body), cmd.ToJsonStrIgnoreErr(c.Request.Header), cmd.ToJsonStrIgnoreErr(keyValue))
			}
			// 时延过大
			if time.Since(startTime) > 2*time.Second {
				keyValue := map[string]any{}
				for key, value := range c.Keys {
					keyValue[key] = value
				}
				log.CtxLog(c).Errorf("request cost over 2s. url:%v body:%v headers:%v ctx keys:%v", c.Request.URL.String(), string(body), cmd.ToJsonStrIgnoreErr(c.Request.Header), cmd.ToJsonStrIgnoreErr(keyValue))
			}
		}
		// 配置需要打印，用于调试
		for _, logPath := range config.TestData.LogPaths {
			keyValue := map[string]any{}
			for key, value := range c.Keys {
				keyValue[key] = value
			}
			if strings.Contains(c.Request.RequestURI, logPath) {
				log.CtxLog(c).Errorf("config path to log. url:%v body:%v headers:%v ctx keys:%v", c.Request.URL.String(), string(body), cmd.ToJsonStrIgnoreErr(c.Request.Header), cmd.ToJsonStrIgnoreErr(keyValue))
			}
		}
	}
}

var (
	mogConn                  sync.Once
	mogSupportTimeseriesConn sync.Once
	plcMogConn               sync.Once
	rbMogConn                sync.Once
	tdConn                   sync.Once
	redisConn                sync.Once
	grpcConn                 sync.Once

	mogClient                  = new(MongoClient)
	mogSupportTimeseriesClient = new(MongoClient)
	plcMogClient               = new(MongoClient)
	rbMogClient                = new(MongoClient)
	tdClient                   = new(sql.DB)
	redisClient                = new(udao.RedisClient)
	grpcClient                 = new(GRPCClient)
)

func (w *watcher) Mongodb() *MongoClient {
	mogConn.Do(func() {
		mogClient = GetMongoClient(w.Config.Mongodb.Backend, w.Logger.Named("MongoDB backend client"))
	})
	return mogClient
}

func (w *watcher) MongodbSupportTimeseries() *MongoClient {
	mogSupportTimeseriesConn.Do(func() {
		mogSupportTimeseriesClient = GetMongoClient(w.Config.Mongodb.BackendSupportTimeseries, w.Logger.Named("MongoSupportTimeseriesDB backend client"))
	})
	return mogSupportTimeseriesClient
}

func (w *watcher) PLCMongodb() *MongoClient {
	plcMogConn.Do(func() {
		plcMogClient = GetMongoClient(w.Config.Mongodb.Plc, log.Logger.Named("MongoDB PLC client"))
	})
	return plcMogClient
}

func (w *watcher) RbMongodb() *MongoClient {
	rbMogConn.Do(func() {
		rbMogClient = GetMongoClient(w.Config.Mongodb.Rb, log.Logger.Named("MongoDB Redrabbit client"))
	})
	return rbMogClient
}

func (w *watcher) TDEngine() *sql.DB {
	tdConn.Do(func() {
		var err error
		tdClient, err = udao.NewTDClient(&ucfg.TDConfig{
			Protocol: w.Config.TDEngine.Protocol,
			Host:     w.Config.TDEngine.Host,
			Username: w.Config.TDEngine.Username,
			Password: w.Config.TDEngine.Password,
			Port:     w.Config.TDEngine.Port,
		})
		if err != nil {
			w.Logger.Panicf("init tdengine client, err: %v", err)
		} else {
			w.Logger.Infof("succeeded to init tdengine client")
		}
	})
	return tdClient
}

func (w *watcher) Redis() *udao.RedisClient {
	redisConn.Do(func() {
		var err error
		redisClient, err = udao.NewRedisClient(&ucfg.RedisConfig{
			RedisType:      w.Config.Redis.RedisType,
			MaxIdle:        20,
			MaxActive:      100,
			IdleTimeout:    10000,
			ConnectTimeout: 10000,
			ReadTimeout:    10000,
			WriteTimeout:   10000,
			Address:        w.Config.Redis.Address,
			Password:       w.Config.Redis.Password,
			DB:             w.Config.Redis.DB,
		})
		if err != nil {
			w.Logger.Panicf("init %s redis client, err: %v", w.Config.Redis.RedisType, err)
		} else {
			w.Logger.Infof("succeeded to init %s redis client", w.Config.Redis.RedisType)
		}
	})
	return redisClient
}

func (w *watcher) Grpc() *GRPCClient {
	grpcConn.Do(func() {
		grpcClient = GetGrpcConn(log.Logger.Named("RPCClient"))
	})
	return grpcClient
}

func Cors(headers ...map[string]string) gin.HandlerFunc {
	return func(c *gin.Context) {
		method := c.Request.Method
		if len(headers) > 1 {
			c.AbortWithStatusJSON(http.StatusMethodNotAllowed, gin.H{"message": "headers can only be set by one map!"})
		} else if len(headers) == 1 {
			for k, v := range headers[0] {
				c.Header(k, v)
			}
		} else {
			// 必须，接受指定域的请求，可以使用*不加以限制，但不安全
			c.Header("Access-Control-Allow-Origin", "*")
			//context.Header("Access-Control-Allow-Origin", context.GetHeader("Origin"))
			// 必须，设置服务器支持的所有跨域请求的方法
			c.Header("Access-Control-Allow-Methods", "POST, GET, PUT, DELETE, OPTIONS")
			// 服务器支持的所有头信息字段，不限于浏览器在"预检"中请求的字段
			c.Header("Access-Control-Allow-Headers", "Content-Type, Content-Length, Token")
			// 可选，设置XMLHttpRequest的响应对象能拿到的额外字段
			c.Header("Access-Control-Expose-Headers", "Access-Control-Allow-Headers, Token")
			// 可选，是否允许后续请求携带认证信息Cookie，该值只能是true，不需要则不设置
			c.Header("Access-Control-Allow-Credentials", "true")
		}
		// 放行所有OPTIONS方法
		if method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}
		c.Next()
	}
}

func StatusCheck() gin.HandlerFunc {
	return func(c *gin.Context) {
		var response um.Base
		um.SuccessWithMessageForGin(c, &response, "ok", http.StatusOK)
		return
	}
}
