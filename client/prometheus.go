package client

import (
	"git.nevint.com/golang-libs/common-utils/prometheus"
	"git.nevint.com/welkin2/welkin-backend/util"
)

var Prometheus *prometheus.Prometheus

func GetPrometheus() *prometheus.Prometheus {
	if Prometheus == nil {
		Prometheus = prometheus.NewPrometheus("welkin", "/prometheus", util.CustomMetricsList...)
	}

	// 业务需要添加的打点类型在这加
	_, exist := Prometheus.MetricCollectors["psos_statistic_count"]
	if !exist {
		Prometheus.AddMetrics([]*prometheus.Metric{
			&prometheus.Metric{
				ID:     "psos_statistic_count",
				Name:   "psos_statistic_count",
				Help:   "psos statistic count",
				Type:   "gauge_vec",
				Labels: []string{"count_type"}},
		}, "welkin")
	}
	_, exist = Prometheus.MetricCollectors["psos_simulation_time_cost"]
	if !exist {
		Prometheus.AddMetrics([]*prometheus.Metric{
			&prometheus.Metric{
				ID:     "psos_simulation_time_cost",
				Name:   "psos_simulation_time_cost",
				Help:   "psos simulation time cost",
				Type:   "summary_vec",
				Labels: []string{"task_id"}},
		}, "welkin")
	}
	_, exist = Prometheus.MetricCollectors[DeviceGaugeMetric]
	if !exist {
		Prometheus.AddMetrics([]*prometheus.Metric{
			&prometheus.Metric{
				ID:     DeviceGaugeMetric,
				Name:   DeviceGaugeMetric,
				Help:   DeviceGaugeMetric,
				Type:   "gauge_vec",
				Labels: []string{"device_id", "device_project", "metric_type"}},
		}, "welkin")
	}

	return Prometheus
}

const (
	DeviceGaugeMetric = "device_gauge_metric"
)
