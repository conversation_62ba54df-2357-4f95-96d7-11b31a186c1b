package client

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
)

type Report interface {
	InsertOrUpdate(requestId string, idx ...IndexOption) error
}

var _ Report = (*TestReport)(nil)

type TestReport struct {
	Client *mongo.Client
	Data   *umw.MongoTestReportData
}

func (t *TestReport) InsertOrUpdate(requestId string, idx ...IndexOption) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := createIndex(ctx, t.Client, umw.FactoryData, umw.TestReport, idx...); err != nil {
		return fmt.Errorf("create index, err: %v", err)
	}

	_, err :=t.Client.Database(umw.FactoryData).Collection(umw.TestReport).ReplaceOne(
		ctx, bson.M{"request_id": requestId}, t.Data, options.Replace().SetUpsert(true))
	return err
}

type TorqueReport struct {
	Client *mongo.Client
	Data   *umw.MongoTorqueReportData
}

func (t *TorqueReport) InsertOrUpdate(requestId string, idx ...IndexOption) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if t.Data.ReportGenTime != 0 {
		// 生成扭矩报告并更新数据
		update := bson.M{
			"services_valid_count": t.Data.ServicesValidCount,
			"report":               t.Data.Report,
			"report_gen_time":      t.Data.ReportGenTime,
		}
		err := t.Client.Database(umw.FactoryData).Collection(umw.TorqueReport).FindOneAndUpdate(
			ctx, bson.M{"request_id": requestId}, bson.M{"$set": update}).Err()
		if err != nil {
			return fmt.Errorf("update torque report data, err: %v", err)
		}
		return nil
	}

	if err := createIndex(ctx, t.Client, umw.FactoryData, umw.TorqueReport, idx...); err != nil {
		return fmt.Errorf("create index, err: %v", err)
	}
	_, err := t.Client.Database(umw.FactoryData).Collection(umw.TorqueReport).ReplaceOne(
		ctx, bson.M{"request_id": requestId}, t.Data, options.Replace().SetUpsert(true))
	return err
}
