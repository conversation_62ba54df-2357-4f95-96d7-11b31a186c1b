package client

import (
	"os"
	"testing"

	ucfg "git.nevint.com/golang-libs/common-utils/config"
	"git.nevint.com/golang-libs/common-utils/larkservice"
	ulog "git.nevint.com/golang-libs/common-utils/logger"
	log "git.nevint.com/welkin2/welkin-backend/logger"
)

func SetEnvTest() {
	os.Setenv("APOLLO_META", "https://apollo-config-cn-test.nioint.com")
	os.Setenv("K8S_ENV", "test")
	os.Setenv("AREA", "China")
	os.Setenv("LOCAL_LOG", "/tmp/logs")
}

func newWiki() *WikiClient {
	cfg := &ucfg.Config{}
	ucfg.GetConfigApollo("config.json", true, cfg, ucfg.NeedValidate(true), ucfg.ServiceName("ppd-welkin-backend"), ucfg.Cluster(ucfg.GetWelkinApolloCluster()))
	defer ulog.Final()
	cfg.Log.DirPath = "/tmp/logs"
	log.Init(cfg.Log, true)
	return NewWikiClient(cfg.WikiBot.AppId, cfg.WikiBot.AppSecret).SpaceId(cfg.WikiBot.SpaceId).ShareFolder(cfg.WikiBot.ShareFolder)
}

func TestCreateVersion(t *testing.T) {
	SetEnvTest()
	wc := newWiki()
	wikiToken := "AVHDw9016ic73okSsGjcokt2nab"
	versionName := "V1.2"
	err := wc.CreateVersion(wikiToken, versionName)
	if err != nil {
		t.Fatal(err)
	}
}

func TestGetParentNode(t *testing.T) {
	SetEnvTest()
	wc := newWiki()
	targetWikiToken := "RGNdw9Ph8iL0AKklAKxcLR5sneh"
	fileClass := "流程&程序文件"
	nodeToken, err := wc.GetParentNode(targetWikiToken, fileClass)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(nodeToken)
}

func TestGetWikiNodeInfo(t *testing.T) {
	SetEnvTest()
	wc := newWiki()
	targetWikiToken := "RGNdw9Ph8iL0AKklAKxcLR5sneh"
	title, objType, parentToken, err := wc.GetWikiNodeInfo(targetWikiToken)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(title, objType, parentToken)
}

func TestMoveDocToWiki(t *testing.T) {
	SetEnvTest()
	wc := newWiki()
	fileUrl := "https://nio.feishu.cn/docx/LOHPdQcmFoO8htxzpAPckJW2nlc"
	objType := "docx"
	parentToken := "RGNdw9Ph8iL0AKklAKxcLR5sneh"
	token, err := wc.MoveDocToWiki(fileUrl, objType, parentToken)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(token)
}

func TestUploadFile(t *testing.T) {
	SetEnvTest()
	wc := newWiki()
	downloadUrl := "https://groot-test.nioint.com/api/box/download?slug=64d1fe6bc9fcbf959ac80084"
	filename := "test_excel.xlsx"
	url, err := wc.UploadFile(downloadUrl, filename)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(url)
}

func TestGetFileInfo(t *testing.T) {
	SetEnvTest()
	wc := newWiki()
	fileToken := "UDZNdTreBozKo0x00F9cradrnsf"
	title, objType, err := wc.GetFileInfo(fileToken)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(title, objType)
}

func TestDeleteFile(t *testing.T) {
	SetEnvTest()
	wc := newWiki()
	fileToken := "HFTsdOiwdoy7Zaxbja6caEfLn9b"
	fileType := "docx"
	err := wc.DeleteFile(fileToken, fileType)
	if err != nil {
		t.Fatal(err)
	}
}

func TestDeleteShortcuts(t *testing.T) {
	SetEnvTest()
	wc := newWiki()
	err := wc.DeleteShortcuts()
	if err != nil {
		t.Fatal(err)
	}
}

func TestMoveWiki(t *testing.T) {
	SetEnvTest()
	wc := newWiki()
	wikiToken := "PSMMwM6nSixN32kYyvTcckFrnQh"
	targetSpaceId := "7261824234941333507"
	targetParent := "MgWbwNEdtivON6kMe31cIHnQnvf"
	token, err := wc.MoveWiki(wc.spaceId, wikiToken, targetSpaceId, targetParent)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(token)
}

func TestChangePermission(t *testing.T) {
	SetEnvTest()
	wc := newWiki()
	fileToken := "CtfCwe1B9iBjRvkzjujcdZRrn0A"
	fileType := "wiki"
	permission := larkservice.LarkFilePermission{
		ExternalAccessEntity:     "",
		SecurityEntity:           "only_full_access",
		CommentEntity:            "anyone_can_edit",
		ShareEntity:              "same_tenant",
		ManageCollaboratorEntity: "collaborator_full_access",
		LinkShareEntity:          "tenant_readable",
		CopyEntity:               "anyone_can_edit",
	}
	err := wc.ChangePermission(fileToken, fileType, permission)
	if err != nil {
		t.Fatal(err)
	}
}
