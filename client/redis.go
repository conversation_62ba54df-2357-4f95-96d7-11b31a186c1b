package client

import (
	"encoding/json"
	"fmt"

	"github.com/gomodule/redigo/redis"
	"go.mongodb.org/mongo-driver/bson"

	udao "git.nevint.com/golang-libs/common-utils/dao"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
)

type Tags struct {
	Description string
	Project     string
}

func GetDeviceTag(rdc udao.RedisConn, mc *MongoClient, deviceId string) (Tags, error) {
	var tags Tags
	reply, err := redis.Bytes(rdc.Do("hget", "welkin/devicesMap", deviceId))
	if err == nil && len(reply) != 0 {
		err = json.Unmarshal(reply, &tags)
	}
	if err != nil || len(reply) == 0 {
		// get device info from mongodb
		rawData, err := mc.GetOne(umw.OAuthDB, umw.DeviceBaseInfo, bson.D{bson.E{Key: "device_id", Value: deviceId}},
			MongoOptions{Projection: &bson.M{"project": 1, "description": 1, "_id": 0}})
		if err != nil {
			return tags, fmt.Errorf("get device data from mongo, err: %v", err)
		}
		if rawData == nil {
			return tags, nil
		}
		if err = bson.Unmarshal(rawData, &tags); err != nil {
			return tags, fmt.Errorf("unmarshal device data, err: %v", err)
		}
	}
	return tags, nil
}

func GetRedisConn() redis.Conn {
	var conn redis.Conn
	if GetWatcher().Redis().Type == "cluster" {
		conn = GetWatcher().Redis().CPool.Get()
	} else {
		conn = GetWatcher().Redis().Pool.Get()
	}
	return conn
}
