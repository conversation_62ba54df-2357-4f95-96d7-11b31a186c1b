package client

import (
	"context"
	"encoding/json"
	"fmt"
	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	"git.nevint.com/welkin2/welkin-backend/config"
	"os"
	"strings"
	"testing"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"golang.org/x/sync/errgroup"

	ucfg "git.nevint.com/golang-libs/common-utils/config"
	ulog "git.nevint.com/golang-libs/common-utils/logger"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	"git.nevint.com/welkin2/welkin-backend/util"
)

func SetEnvStg() {
	os.Setenv("APOLLO_META", "https://apollo-config-cn-stg.nioint.com")
	os.Setenv("K8S_ENV", "stg")
	os.Setenv("AREA", "China")
	os.Setenv("LOCAL_LOG", "/tmp/logs")
}

func newMongo() *MongoClient {
	cfg := &ucfg.Config{}
	os.Setenv("APOLLO_META", "https://apollo-config-cn-stg.nioint.com")
	os.Setenv("ENV", "stg")
	ucfg.GetConfigApollo("config.json", true, cfg, ucfg.NeedValidate(true), ucfg.ServiceName("ppd-welkin-backend"), ucfg.Cluster(ucfg.GetWelkinApolloCluster()))
	defer ulog.Final()
	cfg.Log.DirPath = "/tmp/logs"
	config.Cfg = cfg
	log.Init(cfg.Log, true)
	return GetMongoClient(cfg.Mongodb.Backend, log.Logger.Named("test mongo"))
}

func TestInsertAlgDaily(t *testing.T) {
	c := newMongo()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	algId := 8
	day := "2023-04-25"
	project := "PowerSwap2"
	codeList := []int{0, 1}
	startDay, _ := time.ParseInLocation("2006-01-02", day, time.Local)
	startTs := startDay.UnixMilli()

	dbName := fmt.Sprintf("algorithm-daily-report-%s", strings.ToLower(project))
	collectionName := fmt.Sprintf("%s_%d", umw.IntAlgorithmMap[algId], startDay.Month())
	deviceIdList := []string{"PS-NIO-ea24a8e7-a666fe29", "PS-NIO-96edf9c0-d2f672ac", "PS-NIO-d314a7c9-c4e4d169", "PS-NIO-a06de8b5-f9194b62", "PS-NIO-881d84b0-9b20682e", "PS-NIO-98180588-01169467", "PS-NIO-92903784-2506bbc5", "PS-NIO-47be1492-6ec242e2", "PS-NIO-e04a4233-3c2adf35", "PS-NIO-2095958b-a19c8fff", "PS-NIO-e0c8044f-212bfa0f"}
	report := []interface{}{
		// 晚上，共4个设备，4个订单，1单output有0有1，一单output1
		umw.MongoAlgorithmDailyReport{
			Project:     project,
			DeviceId:    deviceIdList[0],
			Name:        algId,
			Version:     "v1.0.0",
			ServiceId:   "PS-NIO-1",
			VehicleType: "es6",
			BatteryType: "100",
			OutputValue: &codeList[0],
			StartTime:   startTs,
			EndTime:     startTs + 10,
			Errorcode:   &codeList[0],
		},
		umw.MongoAlgorithmDailyReport{
			Project:     project,
			DeviceId:    deviceIdList[1],
			Name:        algId,
			Version:     "v1.0.0",
			ServiceId:   "PS-NIO-2",
			VehicleType: "es6",
			BatteryType: "100",
			OutputValue: &codeList[0],
			StartTime:   startTs + 19*time.Hour.Milliseconds(),
			EndTime:     startTs + 19*time.Hour.Milliseconds() + 10,
			Errorcode:   &codeList[0],
		},
		umw.MongoAlgorithmDailyReport{
			Project:     project,
			DeviceId:    deviceIdList[1],
			Name:        algId,
			Version:     "v1.0.0",
			ServiceId:   "PS-NIO-2",
			VehicleType: "es6",
			BatteryType: "100",
			OutputValue: &codeList[1],
			StartTime:   startTs + 19*time.Hour.Milliseconds(),
			EndTime:     startTs + 19*time.Hour.Milliseconds() + 10,
			Errorcode:   &codeList[0],
		},
		umw.MongoAlgorithmDailyReport{
			Project:     project,
			DeviceId:    deviceIdList[2],
			Name:        algId,
			Version:     "v1.0.0",
			ServiceId:   "PS-NIO-3",
			VehicleType: "es6",
			BatteryType: "100",
			OutputValue: &codeList[1],
			StartTime:   startTs + 19*time.Hour.Milliseconds() + 1,
			EndTime:     startTs + 19*time.Hour.Milliseconds() + 10,
			Errorcode:   &codeList[0],
		},
		umw.MongoAlgorithmDailyReport{
			Project:     project,
			DeviceId:    deviceIdList[3],
			Name:        algId,
			Version:     "v1.0.0",
			ServiceId:   "PS-NIO-4",
			VehicleType: "es6",
			BatteryType: "100",
			OutputValue: &codeList[0],
			StartTime:   startTs + 19*time.Hour.Milliseconds() + 2,
			EndTime:     startTs + 19*time.Hour.Milliseconds() + 10,
			Errorcode:   &codeList[0],
		},
		// 白天，共7个设备，7个订单，1单车型电池变化，output全为0
		umw.MongoAlgorithmDailyReport{
			Project:     project,
			DeviceId:    deviceIdList[4],
			Name:        algId,
			Version:     "v1.0.0",
			ServiceId:   "PS-NIO-11",
			VehicleType: "es8",
			BatteryType: "100B",
			OutputValue: &codeList[0],
			StartTime:   startTs + 10*time.Hour.Milliseconds(),
			EndTime:     startTs + 10*time.Hour.Milliseconds() + 10,
			Errorcode:   &codeList[0],
		},
		umw.MongoAlgorithmDailyReport{
			Project:     project,
			DeviceId:    deviceIdList[5],
			Name:        algId,
			Version:     "v1.0.0",
			ServiceId:   "PS-NIO-12",
			VehicleType: "es6",
			BatteryType: "100",
			OutputValue: &codeList[0],
			StartTime:   startTs + 10*time.Hour.Milliseconds() + 1,
			EndTime:     startTs + 10*time.Hour.Milliseconds() + 10,
			Errorcode:   &codeList[0],
		},
		umw.MongoAlgorithmDailyReport{
			Project:     project,
			DeviceId:    deviceIdList[6],
			Name:        algId,
			Version:     "v1.0.0",
			ServiceId:   "PS-NIO-13",
			VehicleType: "es6",
			BatteryType: "100",
			OutputValue: &codeList[0],
			StartTime:   startTs + 10*time.Hour.Milliseconds() + 1,
			EndTime:     startTs + 10*time.Hour.Milliseconds() + 10,
			Errorcode:   &codeList[0],
		},
		umw.MongoAlgorithmDailyReport{
			Project:     project,
			DeviceId:    deviceIdList[7],
			Name:        algId,
			Version:     "v1.0.0",
			ServiceId:   "PS-NIO-14",
			VehicleType: "es6",
			BatteryType: "100",
			OutputValue: &codeList[0],
			StartTime:   startTs + 10*time.Hour.Milliseconds() + 1,
			EndTime:     startTs + 10*time.Hour.Milliseconds() + 10,
			Errorcode:   &codeList[0],
		},
		umw.MongoAlgorithmDailyReport{
			Project:     project,
			DeviceId:    deviceIdList[8],
			Name:        algId,
			Version:     "v1.0.0",
			ServiceId:   "PS-NIO-15",
			VehicleType: "es6",
			BatteryType: "100",
			OutputValue: &codeList[0],
			StartTime:   startTs + 10*time.Hour.Milliseconds() + 1,
			EndTime:     startTs + 10*time.Hour.Milliseconds() + 10,
			Errorcode:   &codeList[0],
		},
		umw.MongoAlgorithmDailyReport{
			Project:     project,
			DeviceId:    deviceIdList[9],
			Name:        algId,
			Version:     "v1.0.0",
			ServiceId:   "PS-NIO-16",
			VehicleType: "es6",
			BatteryType: "100",
			OutputValue: &codeList[0],
			StartTime:   startTs + 10*time.Hour.Milliseconds() + 1,
			EndTime:     startTs + 10*time.Hour.Milliseconds() + 10,
			Errorcode:   &codeList[0],
		},
		umw.MongoAlgorithmDailyReport{
			Project:     project,
			DeviceId:    deviceIdList[10],
			Name:        algId,
			Version:     "v1.0.0",
			ServiceId:   "PS-NIO-17",
			VehicleType: "es6",
			BatteryType: "100",
			OutputValue: &codeList[0],
			StartTime:   startTs + 10*time.Hour.Milliseconds() + 1,
			EndTime:     startTs + 10*time.Hour.Milliseconds() + 10,
			Errorcode:   &codeList[0],
		},
	}
	_, err := c.Client.Database(dbName).Collection(collectionName).InsertMany(ctx, report)
	if err != nil {
		t.Fatalf("insert err: %v", err)
	}
}

func TestGetServiceOrderCount(t *testing.T) {
	c := newMongo()
	startTime := int64(1681920000000)
	dbName := "serviceinfo-pus3"
	collectionNames := util.ParseTimeRangeList(startTime, startTime)
	start := time.Now()
	for collectionName := range collectionNames {
		total, err := c.GetServiceOrderCount(dbName, collectionName, startTime)
		if err != nil {
			t.Fatalf("GetServiceOrderCount err: %v", err)
		}
		//t.Logf("order list: %v\n", orderList)
		t.Logf("collection: %s total: %d\n", collectionName, total)
	}
	t.Logf("time: %v\n", time.Now().Sub(start))
}

func TestGetAlgorithmAecData(t *testing.T) {
	c := newMongo()
	start, err := time.ParseInLocation("2006-01-02", "2023-04-26", time.Local)
	if err != nil {
		t.Fatalf("parst time err: %v", err)
	}
	opts := model.AlgorithmSuccessRateRequest{
		AlgorithmId: 6,
		StartTime:   start.UnixMilli(),
	}
	g := errgroup.Group{}
	g.SetLimit(5)
	t0 := time.Now()
	repeat := 20
	for i := 0; i < repeat; i++ {
		g.Go(func() error {
			startTs := time.Now()
			codeNotNull, codeZero, err := c.GetAlgorithmAecData("algorithm-daily-report-powerswap2", opts)
			if err != nil {
				t.Errorf("get algorithm aec data err: %v", err)
				return err
			}
			e := time.Now().Sub(startTs)
			t.Logf("codeNotNull: %d, codeZero: %d, time: %v", codeNotNull, codeZero, e)
			return nil
		})
		//startTs := time.Now()
		//codeNotNull, codeZero, err := c.GetAlgorithmAecData("algorithm-daily-report-powerswap2",  opts)
		//if err != nil {
		//	t.Errorf("get algorithm aec data err: %v", err)
		//}
		//e := time.Now().Sub(startTs)
		//t.Logf("codeNotNull: %d, codeZero: %d, time: %v", codeNotNull, codeZero, e)
	}
	//time.Sleep(100 * time.Second)
	if err := g.Wait(); err != nil {
		t.Fatalf("get algorithm aec data err: %v", err)
	}
	t.Logf("time: %v", time.Now().Sub(t0))
}

func TestGetAlgorithmCount(t *testing.T) {
	c := newMongo()
	start := time.Now()
	data, err := c.GetAlgorithmCount("imageinfo-pus3", util.TimeDay(1682989083928), []string{"2", "4"})
	if err != nil {
		t.Fatalf("get algorithm count err: %v", err)
	}
	t.Logf("data: %v, time: %v", data, time.Now().Sub(start))
}

func TestGetAlgorithmShadow(t *testing.T) {
	c := newMongo()
	ts, shadowMap, err := c.GetAlgorithmVersion(umw.Algorithm, umw.PublishVersion, umw.PublishVersionInfo, "PUS3")
	if err != nil {
		t.Fatalf("GetAlgorithmShadow err: %v", err)
	}
	t.Logf("shadow map: %v, len: %d, update ts: %d", shadowMap, len(shadowMap), ts)
}

//func TestGetAlgorithmSuccessRate(t *testing.T) {
//	c := newMongo()
//	algId := 6
//	dbName := "algorithm-daily-report-powerswap2"
//	opts := model.AlgorithmSuccessRateRequest{
//		AlgorithmId: algId,
//		StartTime:   1682006400000,
//	}
//	repeat := 1
//	start := time.Now()
//	g := errgroup.Group{}
//	for i := 0; i < repeat; i++ {
//		g.Go(func() error {
//			total, successRate, err, _ := c.GetAlgorithmSuccessRate(dbName, opts, nil)
//			if err != nil {
//				return err
//			}
//			t.Logf("total: %d, success rate: %v", total, successRate)
//			return nil
//		})
//	}
//	if err := g.Wait(); err != nil {
//		t.Fatalf("GetAlgorithmSuccessRate err: %v", err)
//	}
//	t.Logf("time: %v", time.Now().Sub(start))
//}

//func TestGetAlgorithmSuccessRateLowest(t *testing.T) {
//	c := newMongo()
//	algId := 6
//	dbName := "algorithm-daily-report-powerswap2"
//	opts := model.AlgorithmSuccessRateRequest{
//		AlgorithmId: algId,
//		StartTime:   1682352000000,
//		//IgnoreDevices: "PS-NIO-d2253012-2",
//	}
//	start := time.Now()
//	mp, err := c.GetAlgorithmSuccessRateLowest(dbName, opts, nil)
//	if err != nil {
//		t.Fatalf("GetAlgorithmSuccessRateLowest err: %v", err)
//	}
//	t.Log(mp)
//	t.Logf("time: %v", time.Now().Sub(start))
//}

func TestBsonMBsonD(t *testing.T) {
	c := newMongo()
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	dbName, collectionName := "algorithm-daily-report-powerswap2", "BSA_4"
	commandD := bson.D{
		{"explain", bson.D{
			{"find", collectionName},
			{"filter", bson.D{
				{"version", "2.1.1"},
				{"device_id", "PS-NIO-5cee5444-d7e03912"},
			}},
		}},
	}
	commandM := bson.D{
		{"explain", bson.D{
			{"find", collectionName},
			{"filter", bson.M{
				"version":   "2.1.1",
				"device_id": "PS-NIO-5cee5444-d7e03912",
			}},
		}},
	}
	var res bson.M
	err := c.Client.Database(dbName).RunCommand(ctx, commandD).Decode(&res)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Printf("commandD: winning plan: %v\nrejected plans: %v\n", res["queryPlanner"].(bson.M)["winningPlan"], res["queryPlanner"].(bson.M)["rejectedPlans"])

	err = c.Client.Database(dbName).RunCommand(ctx, commandM).Decode(&res)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Printf("commandM: winning plan: %v\nrejected plans: %v\n", res["queryPlanner"].(bson.M)["winningPlan"], res["queryPlanner"].(bson.M)["rejectedPlans"])
}

func TestListDevices(t *testing.T) {
	c := newMongo()
	byteData, err := c.NewMongoEntry(bson.D{{"project", "PowerSwap2"}}).ListAll(umw.OAuthDB, umw.DeviceBaseInfo, Ordered{})
	if err != nil {
		t.Fatal(err)
	}
	var res []umw.DeviceFTT
	if err = json.Unmarshal(byteData, &res); err != nil {
		t.Fatal(err)
	}
	t.Log(len(res))
}

func TestInsertFttData(t *testing.T) {
	c := newMongo()
	records := make([]interface{}, 0)
	for ts := 1684080000000; ts < 1686412800000; ts += 86400000 {
		records = append(records, umw.MongoFTT{Day: int64(ts), FTT: 0.88, Project: "PowerSwap2"})
	}
	for ts := 1686499200000; ts < 1687104000000; ts += 86400000 {
		records = append(records, umw.MongoFTT{Day: int64(ts), FTT: 0.88, Project: "PowerSwap2"})
	}
	err := c.NewMongoEntry().InsertMany(umw.Algorithm, umw.FTT, records)
	if err != nil {
		t.Fatal(err)
	}
}

func TestGetAlgorithmUpdateTs(t *testing.T) {
	SetEnvStg()
	c := newMongo()
	updateTs, err := c.GetAlgorithmUpdateTs("BSA2")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(updateTs)
}

func TestCalculateAecCpuUsage(t *testing.T) {
	SetEnvStg()
	c := newMongo()
	algName := "BBCC"
	startDay := "2023-08-06"
	start, err := time.ParseInLocation("2006-01-02", startDay, time.Local)
	if err != nil {
		t.Fatal(err)
	}
	//t.Log(start.UnixMilli(), start.AddDate(0, 0, 1).UnixMilli())
	cpuUsage, err := c.CalculateAecCpuUsage("algorithm-daily-report-powerswap2", algName, start.UnixMilli())
	if err != nil {
		t.Fatal(err)
	}
	t.Log(cpuUsage)
}

func TestMongoClient_GetDeviceHourlyService(t *testing.T) {
	c := newMongo()
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*30)
	defer cancel()
	endTime := time.UnixMilli(1716051600000)
	startTime := endTime.Add(-time.Hour)
	fmt.Println(startTime, startTime.UnixMilli(), endTime, endTime.UnixMilli())
	res, err := c.GetDeviceHourlyService(ctx, umw.PowerSwap2, startTime, endTime)
	if err != nil {
		t.Error(err)
	}
	count := 0
	for _, item := range res {
		count += item.ServiceCount100 + item.ServiceCount70
	}
	fmt.Println(count)
}

func TestMongoEntry_GetPercentage(t *testing.T) {
	c := newMongo()
	res, err := c.NewMongoEntry(bson.D{
		{"day", 1729699200000},
		{"sensor_data", bson.M{"$exists": true}},
		{"servo_health_score", bson.M{"$exists": true}},
		{"charge_health_score", bson.M{"$exists": true}},
	}).GetPercentage(umw.Algorithm, "health_data", "servo_health_score.pan_score", false, 5)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("res:", ucmd.ToJsonStrIgnoreErr(res))
}
