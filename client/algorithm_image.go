package client

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	"git.nevint.com/welkin2/welkin-backend/util"
)

type AlgorithmImage interface {
	InsertManySesRecords([]umw.MongoSESRecord) error
	ImagesIsNormal(dbName, colName, serviceId string) (error, error)
}

type algorithmImage struct {
	client *mongo.Client
}

func (a *algorithmImage) InsertManySesRecords(records []umw.MongoSESRecord) (me error) {
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Minute)
	defer cancel()

	indexOptions := []IndexOption{
		{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 90 * 24 * 3600},
		{Name: "ses_unique_index", Fields: bson.D{{"device_id", 1}, {"model_trigger_time", 1}}, Unique: true},
	}
	if err := createIndex(ctx, a.client, umw.SESRecord, umw.SESRecord, indexOptions...); err != nil {
		return fmt.Errorf("create index, err: %v", err)
	}
	execFunc := func(i int) {
		records[i].Timestamp = time.Now().UnixMilli()
		records[i].Date = util.ConvertTime(records[i].Timestamp)
		_, err := a.client.Database(umw.SESRecord).Collection(umw.SESRecord).ReplaceOne(ctx,
			bson.D{bson.E{Key: "device_id", Value: records[i].DeviceId},
				bson.E{Key: "model_trigger_time", Value: records[i].ModelTriggerTime}}, records[i], options.Replace().SetUpsert(true))
		if err != nil {
			me = err
		}
	}
	ucmd.ParallelizeExec(len(records), execFunc)

	return me
}

func(a *algorithmImage) ImagesIsNormal(dbName, colName, serviceId string) (error, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	err1 := a.client.Database(dbName).Collection(colName).FindOne(ctx,
		bson.M{"service_id": serviceId, "abnormal": true}).Err()

	err2 := a.client.Database(dbName).Collection(colName).FindOne(ctx,
		bson.M{"service_id": serviceId, "abnormal": false}).Err()
	return err1, err2
}
