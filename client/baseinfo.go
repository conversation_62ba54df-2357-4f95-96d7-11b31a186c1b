package client

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	"git.nevint.com/welkin2/welkin-backend/model"
)

type UserInfo interface {
	UpdateUserRole(userId string, role int) error
	UpsertBrownDragonUser(record umw.MongoUserInfo) error
}

type DeviceInfo interface {
	ChangeFactoryDeviceStatus(deviceId string, state int, record interface{}) error
}

type DiagnosisInfo interface {
	ListTroubleEvents(project string, filter ...bson.D) (map[string]map[int32]map[string]string, error)
	GetSnapshotRealtime(project, deviceId, requestId string) (model.SnapShotDiagnosisResponse, error)
	ListFireAlarmRecords(filter bson.D, page Pagination, ordered Ordered) (model.FireAlarmResponse, error)
}

type baseInfo struct {
	client *mongo.Client
}

func (b *baseInfo) UpdateUserRole(userId string, role int) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	filter := bson.M{"user_id": userId}
	update := bson.M{"$set": bson.M{"role": []int{role}, "updated_time": time.Now().UnixMilli()}}
	result, err := b.client.Database(umw.OAuthDB).Collection(umw.UserBaseInfo).UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	if result.MatchedCount == 0 {
		return errors.New("the user does not exist, update operation is terminated")
	}
	return nil
}

func (b *baseInfo) UpsertBrownDragonUser(record umw.MongoUserInfo) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if len(record.Role) == 0 || (record.Role[0] != 5 && record.Role[0] != 6) {
		return fmt.Errorf("invalid role: %v", record.Role)
	}

	indexOption := IndexOption{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 180 * 24 * 3600}
	if err := createIndex(ctx, b.client, umw.OAuthDB, umw.UserBaseInfo, indexOption); err != nil {
		return fmt.Errorf("create user info index for brown dragon, err: %v", err)
	}

	record.Date = time.Now()
	record.CreatedTime = record.Date.UnixMilli()
	record.UpdatedTime = record.Date.UnixMilli()
	cur := b.client.Database(umw.OAuthDB).Collection(umw.UserBaseInfo).FindOne(ctx, bson.M{"user_id": record.UserId})
	if cur.Err() != nil {
		if cur.Err() == mongo.ErrNoDocuments {
			_, err := b.client.Database(umw.OAuthDB).Collection(umw.UserBaseInfo).InsertOne(ctx, record)
			if err != nil {
				return fmt.Errorf("insert one user info for brown dragon, err: %v", err)
			}
			return nil
		}
		return fmt.Errorf("find one user info for brown dragon, err: %v", cur.Err())
	}
	var existedRecord umw.MongoUserInfo
	if err := cur.Decode(&existedRecord); err != nil {
		return fmt.Errorf("decode user info err: %v", err)
	}
	record.Projects = append(record.Projects, existedRecord.Projects...)
	newProjects := make([]interface{}, 0)
	for _, p := range record.Projects {
		newProjects = append(newProjects, p)
	}
	update := bson.M{"updated_time": record.UpdatedTime, "projects": ucmd.Deduplication(newProjects), "date": record.Date}
	_, err := b.client.Database(umw.OAuthDB).Collection(umw.UserBaseInfo).UpdateByID(ctx, existedRecord.Id, bson.M{"$set": update})
	if err != nil {
		return fmt.Errorf("update user info for brown dragon, user_id: %s, err: %v", record.UserId, err)
	}
	return nil
}

// ChangeFactoryDeviceStatus allow one change factory device status manually,
// but the operation skipped if new status is the same as the old
func (b *baseInfo) ChangeFactoryDeviceStatus(deviceId string, state int, record interface{}) (err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	cur := b.client.Database(umw.FactoryData).Collection(umw.DeviceFactoryInfo).FindOne(ctx,
		bson.M{"device_id": deviceId}, options.FindOne().SetSort(bson.M{"updated_time": -1}))
	if cur.Err() != nil {
		if cur.Err() == mongo.ErrNoDocuments {
			_, err = b.client.Database(umw.FactoryData).Collection(umw.DeviceFactoryInfo).InsertOne(ctx, record)
			if err != nil {
				return fmt.Errorf("insert one factory device, err: %v", err)
			}
			return nil
		}
		return fmt.Errorf("find one factory device, err: %v", cur.Err())
	}
	var res umw.MongoDeviceFactoryInfo
	if err = cur.Decode(&res); err != nil {
		return fmt.Errorf("decode factory device, err: %v", err)
	}
	if res.State == state {
		return fmt.Errorf("update terminate: %d no changed", state)
	}

	_, err = b.client.Database(umw.FactoryData).Collection(umw.DeviceFactoryInfo).InsertOne(ctx, record)
	return err
}

func (b *baseInfo) GetSnapshotRealtime(project, deviceId, requestId string) (model.SnapShotDiagnosisResponse, error) {
	var responseData model.SnapShotDiagnosisResponse

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	dbName := fmt.Sprintf("%s-%s", umw.Diagnosis, strings.ToLower(project))
	filter := bson.D{bson.E{Key: "device_id", Value: deviceId}, bson.E{Key: "request_id", Value: requestId}}
	cur, err := b.client.Database(dbName).Collection(umw.SnapshotRealtime).Find(ctx, filter)
	if err != nil {
		return responseData, fmt.Errorf("find many realtime records, filter: %v, err: %v", filter, err)
	}
	var realtime []umw.MongoSnapshotRealtime
	if err = cur.All(ctx, &realtime); err != nil {
		return responseData, fmt.Errorf("decode many realtime records, filter: %v, err: %v", filter, err)
	}
	if len(realtime) != 0 {
		responseData.Data = &realtime[0]
	}

	filter = append(filter, bson.E{Key: "type", Value: 1})
	cur, err = b.client.Database(dbName).Collection(umw.DiagnosisInfo).Find(ctx, filter)
	if err != nil {
		return responseData, fmt.Errorf("find many diagnosis records, filter: %v, err: %v", filter, err)
	}
	var infos []umw.MongoDiagnosisInfo
	if err = cur.All(ctx, &infos); err != nil {
		return responseData, fmt.Errorf("decode many diagnosis records, filter: %v, err: %v", filter, err)
	}

	responseData.DataIdList = make([]model.DataIdMap, 0)
	for _, item := range infos {
		responseData.DataIdList = append(responseData.DataIdList, model.DataIdMap{
			Timestamp:        item.Timestamp,
			DataId:           item.DataId,
			CanCauseShutdown: item.CanCauseShutdown,
		})
	}
	return responseData, nil
}

func (b *baseInfo) ListTroubleEvents(project string, filter ...bson.D) (map[string]map[int32]map[string]string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	var newFilter bson.D
	if len(filter) != 0 {
		newFilter = filter[0]
	}
	dbName := fmt.Sprintf("%s-%s", umw.Diagnosis, strings.ToLower(project))
	cur, err := b.client.Database(dbName).Collection(umw.DiagnosisInfo).Find(ctx, newFilter)
	if err != nil {
		return nil, fmt.Errorf("find many trouble events, filter: %v, err: %v", newFilter, err)
	}
	var responseData []umw.MongoDiagnosisInfo
	if err = cur.All(ctx, &responseData); err != nil {
		return nil, fmt.Errorf("decode many trouble events, filter: %v, err: %v", newFilter, err)
	}
	resultMap := make(map[string]map[int32]map[string]string)
	for _, item := range responseData {
		if resultMap[item.RequestId] == nil {
			resultMap[item.RequestId] = make(map[int32]map[string]string)
		}
		if resultMap[item.RequestId][item.Type] == nil {
			resultMap[item.RequestId][item.Type] = make(map[string]string)
		}
		if resultMap[item.RequestId][item.Type][item.DeviceId] == "" || (item.CanCauseShutdown &&
			strings.HasSuffix(resultMap[item.RequestId][item.Type][item.DeviceId], "false")) {
			resultMap[item.RequestId][item.Type][item.DeviceId] = fmt.Sprintf("%s#%v", item.Module, item.CanCauseShutdown)
		}
	}
	return resultMap, nil
}

func (b *baseInfo) ListFireAlarmRecords(filter bson.D, page Pagination, ordered Ordered) (model.FireAlarmResponse, error) {
	var response model.FireAlarmResponse
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	cursor, err := b.client.Database(umw.DiagnosisManagement).Collection(umw.FireAlarm).Aggregate(ctx, mongo.Pipeline{
		bson.D{{"$match", filter}},
		bson.D{{"$group", bson.M{"_id": bson.M{"alarm_ts": "$alarm_ts", "device_id": "$device_id"}}}},
		bson.D{{"$group", bson.M{"_id": primitive.Null{}, "count": bson.M{"$sum": 1}}}},
	})
	if err != nil {
		return response, err
	}
	var res []struct{
		Count int64 `bson:"count"`
	}
	if err = cursor.All(ctx, &res); err != nil {
		return response, err
	}
	if len(res) == 0 {
		return response, nil
	}
	response.Total = res[0].Count
	cursor, err = b.client.Database(umw.DiagnosisManagement).Collection(umw.FireAlarm).Aggregate(ctx, mongo.Pipeline{
		bson.D{{"$match", filter}},
		bson.D{{"$group", bson.M{
			"_id": bson.M{"alarm_ts": "$alarm_ts", "device_id": "$device_id"},
			"parts": bson.M{"$push": bson.M{
				"part_id": "$part_id",
				"file_url": "$file_url",
				"upload_ts": "$insert_ts",
			}},
			"project": bson.M{"$first": "$project"},
			"total_parts": bson.M{"$first": "$total_parts"},
			"update_ts": bson.M{"$max": "$insert_ts"},
		}}},
		bson.D{{"$project", bson.M{
			"_id": 0,
			"device_id": "$_id.device_id",
			"alarm_ts": "$_id.alarm_ts",
			"project": "$project",
			"total_parts": "$total_parts",
			"update_ts": "$update_ts",
			"parts": "$parts",
		}}},
		bson.D{{"$sort", newSort(ordered)}},
		bson.D{{"$skip", page.Offset}},
		bson.D{{"$limit", page.Limit}},
	})
	if err != nil {
		return response, err
	}
	var records []model.FireAlarmData
	if err = cursor.All(ctx, &records); err != nil {
		return response, err
	}
	response.Data = records
	return response, nil
}
