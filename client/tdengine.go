package client

import (
	"database/sql"
	"errors"
	"fmt"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"reflect"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"

	udao "git.nevint.com/golang-libs/common-utils/dao"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/model"
)

type TDWatcher struct {
	TDClient     *sql.DB
	RedisClient  *udao.RedisClient
	DeviceId     string
	StartTs      *int64
	EndTs        *int64
	FilterFields []string
	Offset       int
	Limit        int
	Descending   bool
	Logger       *zap.SugaredLogger
}

func renameRealtimeField(varName, dataId string) string {
	var name string
	varList := strings.Split(varName, ".")
	if len(varList) != 0 {
		name = varList[len(varList)-1]
		name = strings.Replace(name, "[", "", -1)
		name = strings.Replace(name, "]", "", -1)
	} else {
		name = "undefined"
	}
	return fmt.Sprintf("%s_%s", name, dataId)
}

// GetFields can get tdengine fields from redis, you should set dataId as '*' if you want all ids.
func (t *TDWatcher) GetFields(dbName, stbName, dataId string, safely bool) (reflect.Value, map[string]map[string]string, error) {
	cacheData := make(map[string]cache.DeviceRealtimeDataId)
	if dbName == "device2welkin_realtime" {
		if stbName == "realtime_pus3" {
			for id, item := range cache.WelkinRealtimePS3Data {
				cacheData[id] = cache.DeviceRealtimeDataId{
					DataId:  id,
					VarType: item.VarType,
				}
			}
		}
	} else if dbName == "device2oss_realtime" {
		if stbName == "realtime_pus3" {
			cacheData = cache.OSSRealtimePS3Data
		} else if stbName == "realtime_powerswap2" {
			cacheData = cache.RealtimePS2Data
		}
	} else if dbName == "device2cloud_realtime" {
		for id, item := range cache.WelkinRealtimePS3Data {
			cacheData[id] = cache.DeviceRealtimeDataId{
				DataId:   id,
				VarType:  item.VarType,
				Variable: renameRealtimeField(item.Variable, item.DataId),
			}
		}
	}
	if len(cacheData) == 0 {
		return reflect.Value{}, nil, fmt.Errorf("invalid db: %s, stb: %s", dbName, stbName)
	}

	dataIdList := make([]string, 0)
	if dataId == "*" {
		for id := range cacheData {
			dataIdList = append(dataIdList, id)
		}
	} else {
		dataIdList = strings.Split(dataId, ",")
	}

	dataIdFieldMap := make(map[string]map[string]string)
	sf := []reflect.StructField{CreateStruct("ts", reflect.TypeOf(time.Now()))}
	for _, id := range dataIdList {
		if _, has := cacheData[id]; !has {
			t.Logger.Warnf("data_id: %s not found", id)
			continue
		}
		var variable string
		if dbName == "device2cloud_realtime" {
			variable = cacheData[id].Variable
			dataIdFieldMap[id] = map[string]string{
				"var_type": cacheData[id].VarType,
				"variable": cacheData[id].Variable,
			}
		} else if dbName == "device2welkin_realtime" {
			variable = fmt.Sprintf("r%s", id)
			dataIdFieldMap[id] = map[string]string{
				"var_type": cacheData[id].VarType,
			}
		} else {
			variable = fmt.Sprintf("r%s", id)
		}
		t.FilterFields = append(t.FilterFields, variable)
		switch cacheData[id].VarType {
		case "float":
			if safely {
				sf = append(sf, CreateStruct(variable, reflect.TypeOf(model.NullFloat64{})))
			} else {
				sf = append(sf, CreateStruct(variable, reflect.TypeOf(0.0)))
			}
		case "int", "int32":
			if safely {
				sf = append(sf, CreateStruct(variable, reflect.TypeOf(model.NullInt32{})))
			} else {
				sf = append(sf, CreateStruct(variable, reflect.TypeOf(0)))
			}
		default:
			if safely {
				sf = append(sf, CreateStruct(variable, reflect.TypeOf(model.NullString{})))
			} else {
				sf = append(sf, CreateStruct(variable, reflect.TypeOf("")))
			}
		}
	}
	if (dbName == "device2cloud_realtime" || dbName == "device2welkin_realtime") && len(dataIdFieldMap) == 0 {
		return reflect.Value{}, nil, errors.New("data_id is invalid")
	}
	return reflect.New(reflect.StructOf(sf)).Elem(), dataIdFieldMap, nil
}

func (t *TDWatcher) GetRealtimeFields(dbName, stbName, dataId string, safely bool) (reflect.Value, map[string]struct{}, error) {
	cacheData := make(map[string]cache.DeviceRealtimeDataId)
	if dbName == "device2welkin_realtime" {
		if stbName == "realtime_pus3" {
			cacheData = cache.WelkinRealtimePS3Data
		} else if stbName == "realtime_pus4" {
			cacheData = cache.WelkinRealtimePS4Data
		} else if stbName == "realtime_fypus1" {
			cacheData = cache.WelkinRealtimeFY1Data
		}
	} else if dbName == "device2oss_realtime" {
		if stbName == "realtime_pus3" {
			cacheData = cache.OSSRealtimePS3Data
		} else if stbName == "realtime_powerswap2" {
			cacheData = cache.RealtimePS2Data
		} else if stbName == "realtime_pus4" {
			cacheData = cache.OSSRealtimePS4Data
		} else if stbName == "realtime_fypus1" {
			cacheData = cache.OSSRealtimeFY1Data
		}
	}
	if len(cacheData) == 0 {
		return reflect.Value{}, nil, fmt.Errorf("invalid db: %s, stb: %s", dbName, stbName)
	}
	dataIdList := make([]string, 0)
	if dataId == "*" {
		for id := range cacheData {
			dataIdList = append(dataIdList, id)
		}
	} else {
		dataIdList = strings.Split(dataId, ",")
	}
	dataIdMap := make(map[string]struct{})
	sf := []reflect.StructField{CreateStruct("ts", reflect.TypeOf(time.Now()))}
	for _, id := range dataIdList {
		if _, has := cacheData[id]; !has {
			t.Logger.Warnf("data_id: %s not found", id)
			continue
		}
		dataIdMap[id] = struct{}{}
		variable := fmt.Sprintf("r%s", id)
		t.FilterFields = append(t.FilterFields, variable)
		switch cacheData[id].VarType {
		case "float":
			if safely {
				sf = append(sf, CreateStruct(variable, reflect.TypeOf(model.NullFloat64{})))
			} else {
				sf = append(sf, CreateStruct(variable, reflect.TypeOf(0.0)))
			}
		case "int32":
			if safely {
				sf = append(sf, CreateStruct(variable, reflect.TypeOf(model.NullInt32{})))
			} else {
				sf = append(sf, CreateStruct(variable, reflect.TypeOf(0)))
			}
		case "int":
			if safely {
				sf = append(sf, CreateStruct(variable, reflect.TypeOf(model.NullInt64{})))
			} else {
				sf = append(sf, CreateStruct(variable, reflect.TypeOf(0)))
			}
		default:
			if safely {
				sf = append(sf, CreateStruct(variable, reflect.TypeOf(model.NullString{})))
			} else {
				sf = append(sf, CreateStruct(variable, reflect.TypeOf("")))
			}
		}
	}
	if len(t.FilterFields) == 0 {
		return reflect.Value{}, nil, fmt.Errorf("invalid data id")
	}
	return reflect.New(reflect.StructOf(sf)).Elem(), dataIdMap, nil
}

func (t *TDWatcher) FilterDataByFields(dbName string) (int64, *sql.Rows, error) {
	tbName := strings.ToLower(strings.Replace(t.DeviceId, "-", "_", -1))
	filterAnd := make([]string, 0)
	if t.StartTs != nil {
		filterAnd = append(filterAnd, fmt.Sprintf("ts >= %d", *t.StartTs))
	}
	if t.EndTs != nil {
		filterAnd = append(filterAnd, fmt.Sprintf("ts <= %d", *t.EndTs))
	}
	var order string
	if t.Descending {
		order = "DESC"
	} else {
		order = "ASC"
	}
	condition := fmt.Sprintf("ORDER BY ts %s", order)
	if t.Limit != 0 {
		condition = fmt.Sprintf("%s LIMIT %d", condition, t.Limit)
	}
	if t.Offset != 0 {
		condition = fmt.Sprintf("%s OFFSET %d", condition, t.Offset)
	}

	count, err := udao.NewTDEntry(t.TDClient).Count(dbName, tbName, strings.Join(filterAnd, " AND "))
	if err != nil {
		if strings.Contains(err.Error(), "Table does not exist") {
			t.Logger.Warnf("get data: %s %v", tbName, err)
			return 0, nil, nil
		}
		return 0, nil, fmt.Errorf("get columns fail, err: %v", err)
	}
	sql := fmt.Sprintf("SELECT ts, %s FROM %s.%s WHERE %s %s",
		strings.Join(t.FilterFields, ","), dbName, tbName, strings.Join(filterAnd, " AND "), condition)
	rows, err := t.TDClient.Query(sql)
	// fmt.Println(fmt.Sprintf("SELECT ts, %s FROM %s.%s WHERE %s %s", strings.Join(t.FilterFields, ","), dbName, tbName, strings.Join(filterAnd, " AND "), condition))
	log.Logger.Infof("td engine query. sql:%v", sql)
	return count, rows, err
}

func (t *TDWatcher) GetTankTransferDataList(slot, transId string) ([]model.TankTransData, error) {
	fields, err := model.GetFieldName(umw.TDTankTransferRecord{})
	if err != nil {
		return nil, fmt.Errorf("get field name error: %v", err)
	}

	responseData := make([]model.TankTransData, 0)
	tbName := strings.Replace(strings.ToLower(t.DeviceId), "-", "_", -1)
	filterAnd := make([]string, 0)
	if t.StartTs != nil {
		filterAnd = append(filterAnd, fmt.Sprintf("ts >= %d", *t.StartTs))
	}
	if t.EndTs != nil {
		filterAnd = append(filterAnd, fmt.Sprintf("ts <= %d", *t.EndTs))
	}
	querySQL := fmt.Sprintf("SELECT %s FROM %s.%s WHERE %s",
		strings.Join(fields, ","), umw.Device2CloudTTrans, tbName, strings.Join(filterAnd, " AND "))
	if slot != "" {
		sf := make([]string, 0)
		for _, s := range strings.Split(slot, ",") {
			sf = append(sf, []string{
				fmt.Sprintf("full_charged_slot = '%s'", s),
				fmt.Sprintf("drianed_slot = '%s'", s),
				fmt.Sprintf("empty_slot = '%s'", s)}...)

		}
		querySQL = fmt.Sprintf("%s AND (%s)", querySQL, strings.Join(sf, " OR "))
	}
	if transId != "" {
		querySQL = fmt.Sprintf("%s AND trans_id = '%s'", querySQL, transId)
	}

	var rows *sql.Rows
	rows, err = t.TDClient.Query(querySQL)
	if err != nil {
		if strings.Contains(err.Error(), "Table does not exist") {
			t.Logger.Warnf("get tank transfer record data: %v", err)
			return responseData, nil
		}
		return responseData, fmt.Errorf("get tank transfer record data: %v", err)
	}

	responseDataMap := make(map[string]model.TankTransData)
	for rows.Next() {
		var ttr umw.TDTankTransferRecord
		elem := reflect.ValueOf(&ttr).Elem()
		columns := ReflectFields(elem)
		err = rows.Scan(columns...)
		if err != nil {
			t.Logger.Errorf("failed to scan tank transfer data from tdengine, err: %v", err)
			continue
		}
		if _, has := responseDataMap[ttr.TransId]; !has {
			responseDataMap[ttr.TransId] = model.TankTransData{
				TransId:         ttr.TransId,
				FullChargedSlot: ttr.FullChargedSlot,
				DrianedSlot:     ttr.DrianedSlot,
				EmptySlot:       ttr.EmptySlot,
			}
		}
		if ttr.StepNum != 1 && ttr.StepNum != 6 {
			// 1: start event
			// 6: end event
			continue
		}
		currentData := responseDataMap[ttr.TransId]
		if ttr.StepNum == 1 {
			// count start_time
			if currentData.TransStartTS == 0 {
				currentData.TransStartTS = ttr.Timestamp.UnixMilli()
				responseDataMap[ttr.TransId] = currentData
			} else if ttr.Timestamp.UnixMilli() < currentData.TransStartTS {
				currentData.TransStartTS = ttr.Timestamp.UnixMilli()
				responseDataMap[ttr.TransId] = currentData
			}
		} else if ttr.StepNum == 6 {
			// count end_time
			if ttr.Timestamp.UnixMilli() > currentData.TransEndTS {
				currentData.TransEndTS = ttr.Timestamp.UnixMilli()
				responseDataMap[ttr.TransId] = currentData
			}
		}
	}

	for _, v := range responseDataMap {
		if v.TransStartTS != 0 {
			// 若开始时间存在，计算当前时间与开始时间相差是否超过2分钟，否则表示未结束
			if time.Now().UnixMilli()-v.TransStartTS < 2*60*1000 && v.TransEndTS != 0 {
				v.TransEndTS = 0
			}
		} else {
			if v.TransEndTS != 0 && time.Now().UnixMilli()-v.TransEndTS < 30000 {
				// 若开始时间不存在，但结束时间存在，计算当前时间与结束时间相差是否超过30秒，否则表示未结束
				v.TransEndTS = 0
			}
		}
		responseData = append(responseData, v)
	}
	return responseData, nil
}

// GetTankTransferDetail 因为指定了service_id，所以为了以防数据遗漏，时间范围扩大10分钟
func (t *TDWatcher) GetTankTransferDetail(transId string, step *int) (map[string][]model.TTPSData, error) {
	fields, err := model.GetFieldName(umw.TDTankTransferRecord{})
	if err != nil {
		return nil, fmt.Errorf("get field name error: %v", err)
	}

	responseData := make(map[string][]model.TTPSData)
	tbName := strings.Replace(strings.ToLower(t.DeviceId), "-", "_", -1)
	filterAnd := []string{fmt.Sprintf("trans_id = '%s'", transId)}
	if step != nil {
		filterAnd = append(filterAnd, fmt.Sprintf("step_num = %d", *step))
	}
	if t.StartTs != nil {
		filterAnd = append(filterAnd, fmt.Sprintf("ts >= %d", *t.StartTs-5*60*1000))
	}
	if t.EndTs != nil {
		filterAnd = append(filterAnd, fmt.Sprintf("ts <= %d", *t.EndTs+5*60*1000))
	}

	rows, err := t.TDClient.Query(fmt.Sprintf("SELECT %s FROM %s.%s WHERE %s",
		strings.Join(fields, ","), umw.Device2CloudTTrans, tbName, strings.Join(filterAnd, " AND ")))
	if err != nil {
		if strings.Contains(err.Error(), "Table does not exist") {
			t.Logger.Warnf("get tank transfer data error, err: %v", err)
			return responseData, nil
		}
		return responseData, fmt.Errorf("get tank transfer data error, err: %v", err)
	}
	for rows.Next() {
		var ttr umw.TDTankTransferRecord
		elem := reflect.ValueOf(&ttr).Elem()
		columns := ReflectFields(elem)
		err = rows.Scan(columns...)
		if err != nil {
			t.Logger.Errorf("failed to scan tank transfer data from tdengine, err: %v", err)
			continue
		}
		axisDataList := strings.Split(ttr.AxisRecord, ";")
		if len(axisDataList) != 3 {
			t.Logger.Warnf("invalid axis record, trans_id: %s, axis data: %s", transId, ttr.AxisRecord)
			continue
		}
		for _, ax := range axisDataList {
			per := strings.Split(ax, ",")
			if len(per) != 4 {
				// 轴一：1,0,2,3
				// 轴二：2,-1,1,0
				// 轴三：3,-2,1,1
				// 轴号,位置,速度,扭矩
				t.Logger.Warnf("invalid axis record, trans_id: %s, axis data: %s", transId, ttr.AxisRecord)
				continue
			}
			if responseData[per[0]] == nil {
				responseData[per[0]] = make([]model.TTPSData, 0)
			}
			position, _ := strconv.ParseInt(per[1], 10, 64)
			speed, _ := strconv.ParseInt(per[2], 10, 32)
			torque, _ := strconv.ParseInt(per[3], 10, 32)
			responseData[per[0]] = append(responseData[per[0]], model.TTPSData{
				Timestamp: ttr.Timestamp.UnixMilli(),
				Torque:    int32(torque),
				Position:  position,
				Speed:     int32(speed),
			})
		}
	}
	return responseData, nil
}

func ReflectFields(elem reflect.Value) []interface{} {
	numCols := elem.NumField()
	columns := make([]interface{}, numCols)
	for i := 0; i < numCols; i++ {
		columns[i] = elem.Field(i).Addr().Interface()
	}
	return columns
}

func CreateStruct(field string, fieldType reflect.Type) reflect.StructField {
	return reflect.StructField{
		Name: strings.Title(field),
		Type: fieldType,
		Tag:  reflect.StructTag(fmt.Sprintf("`json:%q`", field)),
	}
}
