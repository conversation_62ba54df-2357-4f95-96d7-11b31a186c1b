package client

import (
	"encoding/base64"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"

	"git.nevint.com/welkin2/welkin-backend/algorithm/proto"
	"git.nevint.com/welkin2/welkin-backend/model"
)

//var (
//	conn *grpc.ClientConn
//	once sync.Once
//)

type GRPCClient struct {
	promCollectors map[string]prometheus.Collector

	Logger *zap.SugaredLogger
}

func GetGrpcConn(logger *zap.SugaredLogger) *GRPCClient {
	//once.Do(func() {
	//	var err error
	//	conn, err = grpc.Dial(grpcConfig.Addr, grpc.WithTransportCredentials(insecure.NewCredentials()))
	//	if err != nil {
	//		logger.Panicf("failed to connect RPC server, err: %v", err)
	//	}
	//	logger.Infof("RPC connected")
	//})
	return &GRPCClient{Logger: logger}
}

func (r *GRPCClient) DetectAbnormalImage(ctx *gin.Context, buffer []byte, sha512 string) (detectResponse model.DetectResponse, err error) {
	conn, err := grpc.Dial("[::]:5000", grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		r.Logger.Errorf("dial grpc connection err: %v", err)
		return
	}
	defer conn.Close()
	client := proto.NewDetectImageClient(conn)
	req := proto.DetectRequest{
		File:   base64.StdEncoding.EncodeToString(buffer),
		Sha512: sha512,
	}
	resp, err := client.DetectAbnormalImage(ctx, &req, grpc.WaitForReady(true))
	if err != nil {
		return
	}
	if resp.ErrCode != 0 {
		err = fmt.Errorf("rpc server return err code is %d", resp.ErrCode)
		return
	}

	detectResponse = model.DetectResponse{
		Abnormal:    resp.Abnormal,
		AbnormalImg: resp.AbnormalImg,
		SHA512:      resp.Sha512,
	}
	return
}
