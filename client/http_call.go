package client

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"time"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"

	"git.nevint.com/welkin2/welkin-backend/config"
	log "git.nevint.com/welkin2/welkin-backend/logger"
)

func GetSatisfyDiyLabel(ctx context.Context) (map[string]map[string][]string, error) {
	url := fmt.Sprintf("%v/satisfaction_label/label", config.Cfg.Welkin.AlgorithmUrl)
	resp, err := http.Get(url)
	if err != nil {
		log.CtxLog(ctx).Errorf("http.Get err. url:%v err:%v", url, err)
		return nil, err
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.CtxLog(ctx).Errorf("ioutil.ReadAll err. url:%v err:%v", url, err)
		return nil, err
	}
	var data map[string]map[string][]string
	err = json.Unmarshal(body, &data)
	if err != nil {
		log.CtxLog(ctx).Errorf("json.Unmarshal. err:%v", err)
		return nil, err
	}
	return data, nil
}

func Send2Broker(ctx context.Context, requestBody map[string]interface{}, bizId string) error {
	url := fmt.Sprintf("%s/plc/broker/v2/welkin-gen/%s", config.Cfg.Welkin.BackendUrl, bizId)
	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:    url,
		Method: "POST",
		Header: map[string]string{
			"Content-Type": "application/json",
		},
		RequestBody: requestBody,
	})
	log.CtxLog(ctx).Infof("Send2Broker bizId:%v body:%v", bizId, ucmd.ToJsonStrIgnoreErr(requestBody))
	body, statusCode, err := ct.Do()
	if err != nil {
		return err
	}
	defer body.Close()
	data, dErr := io.ReadAll(body)
	if dErr != nil {
		return dErr
	}
	if statusCode != http.StatusOK {
		return fmt.Errorf("status code: %d, body: %s", statusCode, string(data))
	}
	return nil
}

type IndicatorDimOption struct {
	DimCode  string `json:"dim_code,omitempty"`
	DimName  string `json:"dim_name"`
	DimValue string `json:"dim_value"`
}

type GalaxyIndicatorRequest struct {
	IndicatorCodes []string             `json:"indicator_codes"`
	DimOptions     []IndicatorDimOption `json:"dim_options"`
	FilterDims     []IndicatorDimOption `json:"filter_dims"`
	DataTimeStart  int64                `json:"data_time_start"`
	DataTimeEnd    int64                `json:"data_time_end"`
	PageSize       string               `json:"page_size"`
	PageNo         string               `json:"page_no"`
}

type OperationDataResponse struct {
	RequsetID      string                   `json:"request_id"`
	ResultCode     string                   `json:"result_code"`
	Message        string                   `json:"message"`
	Data           map[string]IndicatorData `json:"data"`
	DebugMessage   string                   `json:"debug_msg"`
	DisplayMessage string                   `json:"display_msg"`
}

type IndicatorData struct {
	TotalResults int  `json:"total_results"`
	HasNext      bool `json:"has_next"`
	ResultList   []struct {
		Dimcode        string `json:"dim_code"`
		Dimname        string `json:"dim_name"`
		DimValue       string `json:"dim_value"`
		IndicatorValue string `json:"indicator_value"`
	} `json:"result_list"`
}

func QueryGalaxyIndictor(ctx context.Context, field string, startTime, endTime int64, dimOptions, dimFilters []IndicatorDimOption) (res *IndicatorData, err error) {
	ts := time.Now().Unix()
	path := "/pe/indicator/v1/in/indicators/detail"
	requestBody := map[string]any{
		"indicator_codes": []string{field},
		"dim_options":     dimOptions,
		"filter_dims":     dimFilters,
		"data_time_start": startTime,
		"data_time_end":   endTime,
		"page_no":         "1",
		"page_size":       "10000",
	}
	sign := ucmd.Sign{
		Timestamp:   ts,
		AppId:       config.Cfg.Sentry.AppId,
		AppSecret:   config.Cfg.Sentry.AppSecret,
		ContentType: "application/json",
		Method:      "POST",
		Path:        path,
		URLParams:   map[string]any{"hash_type": "sha256"},
		BodyParams:  requestBody,
	}
	sn := sign.Generate()
	if sn == "" {
		err = fmt.Errorf("QueryGalaxyIndictor, gen sign fail")
		log.CtxLog(ctx).Error(err)
		return
	}
	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL: fmt.Sprintf("%s%s?app_id=%s&sign=%s&timestamp=%d&hash_type=sha256",
			config.Cfg.OSS.PowUrl, path, config.Cfg.Sentry.AppId, sn, ts),
		Method: "POST",
		Header: map[string]string{
			"Content-Type":       "application/json",
			"X-User-Tenant-Code": "NIO",
		},
		RequestBody: requestBody,
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		log.CtxLog(ctx).Errorf("QueryGalaxyIndictor: %v", err)
		return
	}
	if statusCode != 200 {
		err = fmt.Errorf("QueryGalaxyIndictor, http status code: %d", statusCode)
		log.CtxLog(ctx).Error(err)
		return
	}
	defer body.Close()
	var operationDataResponse OperationDataResponse
	if err = json.NewDecoder(body).Decode(&operationDataResponse); err != nil {
		log.CtxLog(ctx).Errorf("QueryGalaxyIndictor, json decode err: %v", err)
		return nil, err
	}
	resData := operationDataResponse.Data[field]
	return &resData, nil
}

type Weather struct {
	Province         string `json:"province"`          // 省份
	City             string `json:"city"`              // 城市
	AdCode           string `json:"adcode"`            // 区域编码
	Weather          string `json:"weather"`           // 天气现象
	Temperature      string `json:"temperature"`       // 实时温度
	WindDirection    string `json:"winddirection"`     // 风向
	WindPower        string `json:"windpower"`         // 风力等级
	Humidity         string `json:"humidity"`          // 空气湿度
	ReportTime       string `json:"reporttime"`        // 数据发布时间
	TemperatureFloat string `json:"temperature_float"` // 温度浮点数值
	HumidityFloat    string `json:"humidity_float"`    // 湿度浮点数值
}

type WeatherResponse struct {
	Status   string    `json:"status"`   // 返回状态
	Count    string    `json:"count"`    // 返回结果总数目
	Info     string    `json:"info"`     // 返回的状态信息
	InfoCode string    `json:"infocode"` // 状态码
	Lives    []Weather `json:"lives"`    // 实况天气数据信息
}

func GetTemperature(ctx context.Context, cityId string) (Weather, error) {
	url := fmt.Sprintf("https://restapi.amap.com/v3/weather/weatherInfo?city=%v&key=4ecc676315c942b6e77085b1ce9837a5", cityId)
	resp, err := http.Get(url)
	if err != nil {
		log.CtxLog(ctx).Errorf("获取天气信息失败: %v", err)
		return Weather{}, err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.CtxLog(ctx).Errorf("读取响应体失败: %v", err)
		return Weather{}, err
	}
	log.CtxLog(ctx).Infof("获取天气信息成功: body:%v url:%v", string(body), url)
	var weatherResponse WeatherResponse
	if err := json.Unmarshal(body, &weatherResponse); err != nil {
		log.CtxLog(ctx).Errorf("解析JSON响应失败: %v body:%v url:%v", err, string(body), url)
		return Weather{}, err
	}
	if weatherResponse.Status != "1" {
		return Weather{}, fmt.Errorf("获取天气信息失败: %s", weatherResponse.Info)
	}
	if len(weatherResponse.Lives) == 0 {
		return Weather{}, fmt.Errorf("获取天气信息失败: 没有天气数据")
	}
	return weatherResponse.Lives[0], nil
}
