package client

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
)

type LoginHistory interface {
	InsertLogin(string) error
	UpdateLogout(string) error
	GetLatestLoginInfo() (interface{}, error)
}

var _ LoginHistory = (*CommonLoginHistory)(nil)

type CommonLoginHistory struct {
	Client     *mongo.Client
	Method     string  // local: 用户名密码方式，feishu: 飞书扫码方式
	Username   string
	UserId     string
	Project    string
	DeviceIdr  string
	Role       int
}

func (c *CommonLoginHistory) InsertLogin(collection string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	_, err := c.Client.Database(umw.OAuthDB).Collection(collection).InsertOne(ctx, umw.MongoUserLoginHistory{
		Username:    c.Username,
		UserId:      c.UserId,
		Email:       fmt.Sprintf("%<EMAIL>", c.UserId),
		Role:        []int{c.Role},
		Project:     c.Project,
		DeviceId:    c.DeviceIdr,
		Method:      c.Method,
		CurrentRole: c.Role,
		LoginTime:   time.Now().UnixMilli(),
	})
	return err
}

func (c *CommonLoginHistory) UpdateLogout(collection string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	cur, err := c.Client.Database(umw.OAuthDB).Collection(collection).Find(ctx,
		bson.M{"device_id": c.DeviceIdr}, options.Find().SetSort(bson.M{"_id": -1}).SetLimit(1))
	if err != nil {
		return fmt.Errorf("find one record, err: %v", err)
	}
	records := make([]umw.MongoUserLoginHistory, 0)
	if err = cur.All(ctx, &records); err != nil {
		return fmt.Errorf("decode records, err: %v", err)
	}
	if len(records) == 0 || records[0].LogoutTime != 0 {
		return nil
	}
	_, err = c.Client.Database(umw.OAuthDB).Collection(collection).UpdateByID(
		ctx, records[0].Id, bson.M{"$set": bson.M{"logout_time": time.Now().UnixMilli()}})
	return err
}

func (c *CommonLoginHistory) GetLatestLoginInfo() (interface{}, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	response := make([]map[string]interface{}, 0)
	cur, err := c.Client.Database(umw.OAuthDB).Collection(umw.DeviceBaseInfo).Find(ctx,
		bson.M{"is_login": true}, options.Find().SetProjection(bson.M{"device_id": 1, "_id": 0}))
	if err != nil {
		return response, fmt.Errorf("find many device records, err: %v", err)
	}
	records := make([]umw.MongoDeviceInfo, 0)
	if err = cur.All(ctx, &records); err != nil {
		return response, fmt.Errorf("decode device records, err: %v", err)
	}
	if len(records) == 0 {
		return response, nil
	}
	for _, record := range records {
		cur, err = c.Client.Database(umw.OAuthDB).Collection(umw.LoginHistory).Find(ctx,
			bson.M{"device_id": record.DeviceId},
			options.Find().SetSort(bson.M{"_id": -1}).SetProjection(bson.M{"user_id": 1, "device_id": 1, "login_time": 1, "_id": 0}).SetLimit(1))
		if err != nil {
			return response, fmt.Errorf("find device: %s logged info, err: %v", record.DeviceId, err)
		}
		lr := make([]umw.MongoUserLoginHistory, 0)
		if err = cur.All(ctx, &lr); err != nil {
			return response, fmt.Errorf("decode device: %s logged info, err: %v", record.DeviceId, err)
		}
		if len(lr) == 0 {
			continue
		}
		if c.UserId == "" || c.UserId == lr[0].UserId{
			response = append(response, map[string]interface{}{
				"login_time":        lr[0].LoginTime,
				"user_id":           lr[0].UserId,
				"device_identifier": lr[0].DeviceId,
			})
		}
	}
	return response, nil
}

var _ LoginHistory = (*BrownDragonLoginHistory)(nil)

type BrownDragonLoginHistory struct {
	Client     *mongo.Client
	Username   string
	UserId     string
	DeviceIdr  string
	Role       int
	Factory    int
}

func (b *BrownDragonLoginHistory) InsertLogin(collection string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	_, err := b.Client.Database(umw.FactoryData).Collection(collection).InsertOne(ctx, umw.MongoBrownDragonLoginHistory{
		Username:         b.Username,
		UserId:           b.UserId,
		Role:             b.Role,
		Factory:          b.Factory,
		DeviceIdentifier: b.DeviceIdr,
		LoginTime:        time.Now().UnixMilli(),
	})
	return err
}

func (b *BrownDragonLoginHistory) UpdateLogout(collection string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	cur, err := b.Client.Database(umw.FactoryData).Collection(collection).Find(ctx,
		bson.M{"device_identifier": b.DeviceIdr}, options.Find().SetSort(bson.M{"_id": -1}).SetLimit(1))
	if err != nil {
		return fmt.Errorf("find one record, err: %v", err)
	}
	records := make([]umw.MongoBrownDragonLoginHistory, 0)
	if err = cur.All(ctx, &records); err != nil {
		return fmt.Errorf("decode records, err: %v", err)
	}
	if len(records) == 0 || records[0].LogoutTime != 0 {
		return nil
	}
	_, err = b.Client.Database(umw.FactoryData).Collection(collection).UpdateByID(
		ctx, records[0].Id, bson.M{"$set": bson.M{"logout_time": time.Now().UnixMilli()}})
	return err
}

func (b *BrownDragonLoginHistory) GetLatestLoginInfo() (interface{}, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	response := make([]map[string]interface{}, 0)
	filter := bson.M{"login_time": bson.M{"$ne": 0}, "logout_time": 0}
	deviceIdentifiers, err := b.Client.Database(umw.FactoryData).Collection(umw.BrowndragonLoginHistory).Distinct(ctx,
		"device_identifier", filter)
	if err != nil {
		return response, fmt.Errorf("distinct device identifiers, err: %v", err)
	}
	for _, idr := range deviceIdentifiers {
		cur, err := b.Client.Database(umw.FactoryData).Collection(umw.BrowndragonLoginHistory).Find(ctx,
			bson.M{"device_identifier": idr.(string)},
			options.Find().SetSort(bson.M{"_id": -1}).SetProjection(bson.M{"user_id": 1, "device_identifier": 1, "login_time": 1, "_id": 0}).SetLimit(1))
		if err != nil {
			return nil, fmt.Errorf("find device: %v logged info, err: %v", idr, err)
		}
		lr := make([]umw.MongoBrownDragonLoginHistory, 0)
		if err = cur.All(ctx, &lr); err != nil {
			return response, fmt.Errorf("decode device: %v logged info, err: %v", idr, err)
		}
		if b.UserId == "" || b.UserId == lr[0].UserId{
			response = append(response, map[string]interface{}{
				"login_time":        lr[0].LoginTime,
				"user_id":           lr[0].UserId,
				"device_identifier": lr[0].DeviceIdentifier,
			})
		}
	}
	return response, nil
}