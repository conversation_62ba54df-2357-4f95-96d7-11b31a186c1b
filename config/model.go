package config

type ImageCheck struct {
	AlarmThreshold int `mapstructure:"alarmThreshold"`
	CheckHour      int `mapstructure:"checkHour"`
}

// TestDataConfig 存在只有生产环境才调的通的下游的情况，该配置用于测试环境的数据构造，迎合设备的测试
type TestDataConfig struct {
	AccountUpgrade      AccountUpgrade         `json:"account_upgrade"`
	CmsPredictedService map[string]interface{} `json:"cms_predicted_service"`
	LogPaths            []string               `json:"log_paths"`
	IgnorePaths         map[string]struct{}    `json:"ignore_paths"`
}

type AccountUpgrade struct {
	DeviceOwner         string `json:"device_owner"`
	DeviceManager       string `json:"device_manager"`
	OnlineOperator      string `json:"online_operator"`
	DurationMillisecond int64  `json:"duration_millisecond"`
}

type AlgorithmConfig struct {
	SnapshotConfig SnapshotConfig `json:"snapshot"`
}

type Description struct {
	Zh string `json:"zh"`
	En string `json:"en"`
}

type InternationalDescription map[string]Description

type SnapshotConfig map[string]InternationalDescription
