package config

import (
	"github.com/apolloconfig/agollo/v4/storage"

	"git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	"git.nevint.com/golang-libs/common-utils/config/apollo"

	"git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
)

var (
	Cfg          *ucfg.Config
	AlgorithmCfg AlgorithmConfig
)
var Apollo *apollo.Apollo
var ApolloTestDataConfig *apollo.Apollo
var TestData TestDataConfig

func InitConfig() {
	var err error
	Apollo, err = apollo.ApolloBuilder("config.json", apollo.ConfigTypeJson).ConfigIsBackupConfig(true).Build()
	if err != nil {
		panic(err.Error())
	}
	initTestDataConfig()
}

func initTestDataConfig() {
	var err error
	ApolloTestDataConfig, err = apollo.ApolloBuilder("test_data.json", apollo.ConfigTypeJson).ConfigIsBackupConfig(true).Build()
	if err != nil {
		panic(err.Error())
	}
	err = ApolloTestDataConfig.LoadConfig(&TestData)
	if err != nil {
		panic(err.Error())
	}
	ApolloTestDataConfig.AddListener(TestDataConfigChangeListener{})
}

type ConfigChangeListener struct {
}
type TestDataConfigChangeListener struct {
}

func (c ConfigChangeListener) OnChange(event *storage.ChangeEvent) {
	logger.Logger.Infof("apollo config OnChange. value:%v", cmd.ToJsonStrIgnoreErr(event))
}

func (c ConfigChangeListener) OnNewestChange(event *storage.FullChangeEvent) {
	logger.Logger.Infof("apollo config c. value:%v", cmd.ToJsonStrIgnoreErr(event))
	tmpCfg := &ucfg.Config{}
	err := Apollo.LoadConfig(tmpCfg)
	if err != nil {
		logger.Logger.Errorf("Apollo.LoadConfig err. apollo config c. value:%v err:%v", cmd.ToJsonStrIgnoreErr(event), err)
		return
	}
	Cfg = tmpCfg
	model.InitConstant(Cfg.ExtraConfig["evBrand"])
	logger.Logger.Infof("cfg change to:%v", cmd.ToJsonStrIgnoreErr(Cfg))
}

func (c TestDataConfigChangeListener) OnChange(event *storage.ChangeEvent) {
	logger.Logger.Infof("apollo config TestDataConfigChangeListener OnChange. value:%v", cmd.ToJsonStrIgnoreErr(event))
}

func (c TestDataConfigChangeListener) OnNewestChange(event *storage.FullChangeEvent) {
	logger.Logger.Infof("apollo TestDataConfigChangeListener. value:%v", cmd.ToJsonStrIgnoreErr(event))
	tmpCfg := TestDataConfig{}
	err := ApolloTestDataConfig.LoadConfig(&tmpCfg)
	if err != nil {
		logger.Logger.Errorf("Apollo.LoadConfig err. apollo config TestDataConfigChangeListener. value:%v err:%v", cmd.ToJsonStrIgnoreErr(event), err)
		return
	}
	TestData = tmpCfg
	logger.Logger.Infof("TestDataConfigChangeListener cfg change to:%v", cmd.ToJsonStrIgnoreErr(TestData))
}
