package router

import (
	"context"
	"net/http"
	"os"
	"os/signal"
	"syscall"

	ucfg "git.nevint.com/golang-libs/common-utils/config"
	log "git.nevint.com/welkin2/welkin-backend/logger"
)

func SetupSignalHandler() (stop<PERSON>han <-chan struct{}) {
	stop := make(chan struct{})
	c := make(chan os.Signal, 2)
	signal.Notify(c,
		os.Interrupt,
		syscall.SIGINT, // Ctrl+C
		syscall.SIGQUIT,
		syscall.SIGHUP,
		syscall.SIGTERM,
		syscall.SIGSEGV, // FullDerp
		syscall.SIGABRT, // Abnormal termination
		syscall.SIGILL,  // illegal instruction
		syscall.SIGFPE)  // floating point - this is why we can't have nice things
	go func() {
		<-c
		close(stop)
		<-c
		os.Exit(1) // second signal. Exit directly.
	}()

	return stop
}

func Run(conf ucfg.HTTPServer) {
	serverLogger := log.Logger.Named("Server")
	srv := &http.Server{
		Addr:    conf.ListenAddr,
		Handler: route,
	}
	go func() {
		// service connections
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			serverLogger.Infof("listen: %s\n", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server with
	// a timeout of 10 seconds.
	quit := SetupSignalHandler()
	<-quit
	serverLogger.Info("Shutdown Welkin Backend Server ...")

	ctx, cancel := context.WithTimeout(context.Background(), conf.GraceShutdownPeriod.Duration)
	defer cancel()
	if err := srv.Shutdown(ctx); err != nil {
		serverLogger.Info("Server Shutdown:", err)
	}

	select {
	case <-ctx.Done():
		serverLogger.Infof("timeout of %d seconds.", conf.GraceShutdownPeriod)
	}
	serverLogger.Info("Server exiting")
}
