package router

import (
	"os"

	"github.com/gin-gonic/gin"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	niosso "git.nevint.com/golang-libs/common-utils/nio-sso"
	uprom "git.nevint.com/golang-libs/common-utils/prometheus"

	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/constant"
	"git.nevint.com/welkin2/welkin-backend/exec"
	"git.nevint.com/welkin2/welkin-backend/pkg/task"
	"git.nevint.com/welkin2/welkin-backend/util"
)

var route = new(client.Router)

func Init(conf *ucfg.Config, verbose bool) {
	e := gin.New()
	route = &client.Router{Engine: e, Watcher: client.NewWatcher(e, conf, verbose)}

	// init sso
	sso := exec.NewSSO(conf)

	// init rbac
	rbac := exec.NewRBAC(conf)

	route.Use(client.Cors())
	route.GET("/status", client.StatusCheck())
	prom := uprom.NewPrometheus("welkin", "/prometheus", util.CustomMetricsList...)
	client.Prometheus = prom
	prom.Use(route.Engine)

	area := ucmd.GetArea()
	// add session middleware for all routes
	baseGroup := route.Group("/core", sso.SessionMiddleware("welkin-session", 7*24*3600))
	// set login route out of sso.AuthMiddleware
	baseGroup.GET("/system/sso/login", sso.Login)

	systemGroup := baseGroup.Group("/system")
	system := exec.NewSystemHandler(route.Watcher, conf)
	{
		systemGroup.POST("/user-info/approval/callback", system.BrownDragonUserApprovalCallback())
		systemGroup.Use(sso.AuthMiddleware(niosso.HostExclude("pp-welkin.nio.com")))
		//systemGroup.GET("/sso/login", sso.Login)
		systemGroup.POST("/sso/logout", sso.Logout)
		systemGroup.GET("/user-info/:project/list", system.GetUserList())
		systemGroup.POST("/user-info/:project/:user_id", system.CreateNewUser())
		systemGroup.PUT("/user-info/:project/:user_id", system.UpdateUser())
		systemGroup.DELETE("/user-info/:project/:user_id", system.DeleteUser())
		//systemGroup.GET("/sso/accessToken", system.SsoAccessToken())
		systemGroup.GET("/menus/list", rbac.FetchUserAuthorities(), system.GetMenuList())
	}

	plcGroup := baseGroup.Group("/plc")
	plc := exec.NewPLCHandler(route.Watcher)
	{
		plcGroup.GET("/plc-record/:project/:device_id", plc.GetPLCRecords())
		plcGroup.GET("/:type/:project/:device_id/archive_status", plc.GetPLCArchiveStatus())
		plcGroup.POST("/:type/:project/:device_id/restore", plc.RestorePLC())
		plcGroup.GET("/plc-record/:project/:device_id/restored", plc.GetRestoredPLCRecords())
		plcGroup.GET("/sensor/:project/:device_id", plc.GetSensor(area))
		plcGroup.GET("/converter/:project/:device_id", plc.GetConverter(area))
		plcGroup.GET("/di/:project/:device_id", plc.GetDIRecords(area))
		plcGroup.GET("/op-log/:project/:device_id", plc.GetOperationLog()) // 二代站操作日志
		plcGroup.GET("/plc-record/:project/exists", plc.GetPLCExists())
	}

	realtimeGroup := baseGroup.Group("/realtime")
	realtime := exec.NewRealtimeHandler(route.Watcher)
	{
		realtimeGroup.GET("/v1/:project/:device_id", realtime.GetRealtime())                   // 获取实时数据，供测试团队使用
		realtimeGroup.GET("/v2/:project/:device_id", realtime.RealtimeDataList())              // 三、四代站实时数据筛选（welkin+oss）
		realtimeGroup.GET("/v2/welkin/:project/:device_id", realtime.WelkinRealtimeDataList()) // 三代站welkin实时数据筛选
		realtimeGroup.GET("/v2/oss/:project/:device_id", realtime.OSSRealtimeDataList())       // oss实时数据筛选
		realtimeGroup.GET("/v1/:project/dataIdList", realtime.RealtimeIdList())                // 实时数据点筛选下拉列表
		realtimeGroup.GET("/v1/:project/pcu/electricity-data", realtime.GetElectricityData())
		realtimeGroup.GET("/v1/:project/pdu/km-status", realtime.GetPDUKMStatus())
		realtimeGroup.GET("/v1/:project/bms/soc-data", realtime.GetBmsSocData())
		realtimeGroup.GET("/v1/:project/acdc-data", realtime.GetACDCData())
		realtimeGroup.GET("/v1/:project/sct-data", realtime.GetSCTData())
	}

	deviceGroup := route.Group("/device")
	v1Group := deviceGroup.Group("/v1")
	v2Group := deviceGroup.Group("/v2")
	device := exec.NewDeviceHandler(route.Watcher, conf)
	{
		v1Group.POST("/join/tke/edge", device.PostJoinEdgeScriptHandler())                               // 腾讯云边缘脚本下载
		v1Group.POST("/upload-image/:project/:device_id", device.UploadImageV1(area, prom))              // 图片上传
		v1Group.POST("/upload-data/:project/:device_id/:biz_id", device.UploadData(area, prom))          // 上传数据
		v1Group.POST("/upload-data-list/:project/:device_id/:biz_id", device.UploadDataList(area, prom)) // 上传list数据

		v1Group.GET("/mqtt/data_upload/debug", device.GetMqttUploadData()) // 获取设备上传到mqtt的数据，用户测试查看

		v1Group.GET("/:project/device-status", device.GetDeviceStatus())
		v1Group.GET("/factory/:project/device-status", device.GetDeviceStatus("factory"))                                  // 在出厂检验前判断设备是否已经出厂
		v1Group.POST("/factory/:project/device-status", device.UpdateDeviceFactoryStatus())                                // 更新设备出厂状态
		v1Group.GET("/devices/list/:project", device.GetDeviceList())                                                      // 获取设备列表
		v1Group.GET("/devices/list/factory", device.GetFactoryDeviceList())                                                // 获取stg设备列表
		v1Group.GET("/devices/list/:project/name-mapping", device.GetDeviceListMapping())                                  // 供设备ID/名称筛选项使用
		v1Group.GET("/devices/list/city_company_mapping", device.GetCityCompanyListMapping())                              // 区域公司和设备列表的映射
		v1Group.GET("/devices/list_all", device.ListAllDevices())                                                          // 列出所有设备
		v1Group.POST("/devices/upload-csv", device.UploadDevicesCsv())                                                     // 上传csv解析设备信息
		v1Group.GET("/service-info/:project/list", device.ServiceInfoList())                                               // 获取服务列表
		v1Group.GET("/service-info/:project/:device_id/:service_id/details", device.ServiceInfoDetails())                  // 获取服务列表详情
		v1Group.GET("/tank-transfer-record/:project/:device_id/list", device.TankTransferRecordList(area))                 // 获取单设备某个时间段内的倒仓列表
		v1Group.GET("/tank-transfer-record/:project/:device_id/:trans_id/details", device.TankTransferRecordDetails(area)) // 获取某次倒仓详情
		v1Group.GET("/battery_refresh/:project/:device_id", device.BatteryRefresh())                                       // 获取电池刷写记录

		v1Group.POST("/login", device.Login())                          // 用户登陆
		v1Group.POST("/logout", device.Logout())                        // 用户登出
		v1Group.GET("/logged-devices", device.LoggedDevices())          // 用户已登陆的设备列表
		v1Group.POST("/offline-account", device.UpdateOfflineAccount()) // 更新离线帐户
		v1Group.POST("/passport/qr", device.GetPassportQR())            // 获取飞书登陆二维码

		// 用户账号权限升级 (设备调用)
		v1Group.POST("/:project/:device_id/user/:user_id/upgrade/apply", system.UserUpgradeApply())            // 用户权限升级申请
		v1Group.POST("/:project/:device_id/user/:user_id/upgrade/roll_back", system.UserUpgradeRollback())     // 用户权限恢复
		v1Group.POST("/:project/:device_id/user/:user_id/upgrade/expand_time", system.UserUpgradeExpandTime()) // 用户权限升级加时
		v1Group.GET("/:project/:device_id/user/:user_id", system.GetUserById())                                // 查询用户权限
		v1Group.POST("/account_upgrade/callback", device.AccountUpgradeCallback())                             // 用户权限升级申请workflow工单结束回调

		v1Group.POST("/log-info/:project/:device_id/oss/command", device.NotifyRemoteCommand())                     // 通过oss给设备发指令
		v1Group.POST("/log-info/upload-file", device.UploadLogFile(area))                                           // 日志文件上传
		v1Group.POST("/log-info/sync-file-path", device.SyncLogFilePath())                                          // 同步目录树
		v1Group.GET("/log-info/download-file/authUrl", device.DownloadAuthURL())                                    // 下载授权后的文件
		v1Group.POST("/log-info/download-file/approval", device.DownloadApproval())                                 // 发起审批
		v1Group.POST("/log-info/download-file/approval/callback", device.DownloadApprovalCallback())                // 审批流程回调
		v1Group.GET("/log-info/download-file/approval-history/:project/list", device.DownloadApprovalHistoryList()) // 获取文件下载审批进度
		v1Group.GET("/log-info/upload-history/:project/list", device.GetUploadHistoryList())                        // 获取日志上传记录
		v1Group.GET("/log-info/directory-tree/:project/list", device.GetDirectoryTreeList())                        // 获取日志目录树
		v1Group.GET("/file/download", device.DownloadFile())                                                        // 下载
		v1Group.GET("/snapshot/:project/list", device.GetSnapshotList(prom))                                        // 获取snapshot列表
		v1Group.GET("/snapshot/:project/name-mapping", device.GetSnapshotNameMapping())                             // 获取snapshot筛选的告警
		v1Group.POST("/snapshot/:project/larkcard", device.SendSnapshotInfo())                                      // 根据laputa上的条件配置，发送算法快照
		v1Group.PUT("/favorites", device.UpdateFavorite())                                                          // 新增/删除设备收藏
		v1Group.GET("/favorites", device.GetFavoriteList())                                                         // 获取用户的设备收藏列表
		v1Group.GET("/battery-history/image", device.FilterImageByBatteryInfo())                                    // 获取上下表面图片相关电池历史
		v1Group.GET("/battery-history/image/download", device.DownloadImageByBatteryInfo())
		v1Group.GET("/battery-history/service", device.FilterServiceByBatteryInfo()) // 获取订单相关电池历史
		v1Group.GET("/battery-history/slotEvent", device.FilterSlotByBatteryInfo())  // 获取电池在站内仓位的变化
		v1Group.POST("/v2g/oss/command", device.V2GRemoteControl())                  // v2g充放电指令下发

		v1Group.POST("/push_metric/:project/:device_id", device.PushMetric()) // 设备打点数据上传，用于监控设备指标，以及搭建grafana看板
		v1Group.POST("/push_event/:project/:device_id", device.PushEvent())   // 设备事件上传

		// 换电画像
		v1Group.GET("/service-visual/:project/list", device.ListServiceVisual())              // 获取换订单列表
		v1Group.GET("/service-visual/:project/:order_id/detail", device.GetServiceVisual())   // 获取换订单列表
		v2Group.GET("/service-visual/:project/:order_id/detail", device.GetServiceVisualV2()) // 获取换订单列表

		// 单站模型
		v1Group.GET("/model/overview", device.GetModelOverview())                                   // 获取单站模型首页数据
		v1Group.GET("/model/energy/overview", device.GetEnergyOverview())                           // 获取单站模型能效总览
		v1Group.GET("/model/energy/list", device.GetEnergyList())                                   // 获取单站模型能效明细列表
		v1Group.GET("/model/revenue/overview", device.GetRevenueOverview())                         // 获取单站模型收益总览
		v1Group.GET("/model/revenue/list", device.GetRevenueList())                                 // 获取单站模型收益明细列表
		v1Group.GET("/model/energy/overview/:project/:device_id", device.GetEnergySingleDevice())   // 获取单站模型单站能效详情
		v1Group.GET("/model/revenue/overview/:project/:device_id", device.GetRevenueSingleDevice()) // 获取单站模型单站收益详情

		// 桩相关
		v1Group.GET("/power_charger/order/enum", device.GetPowerChargerEnum()) // 获取桩相关的枚举值
		v1Group.GET("/power_charger/:project/order/list", device.ListPowerChargerOrders())
		v1Group.GET("/power_charger/:project/order/:order_id", device.GetPowerChargerOrderByOrderId())
		v1Group.GET("/power_charger/:project/order/:order_id/events", device.GetPowerChargerOrderEvents())
		v1Group.GET("/power_charger/:project/order/:order_id/realtime", device.GetPowerChargerOrderRealtime())

		// 设备版本管理
		v1Group.GET("/version/:project/overview", device.GetDeviceVersionOverview())          // 版本分布看板
		v1Group.GET("/version/:project/filter-option", device.GetDeviceVersionFilterOption()) // 版本列表筛选项
		v1Group.POST("/version/:project/list", device.ListDeviceVersion())                    // 设备版本列表
		v1Group.POST("/version/:project/blacklist", device.ChangeDeviceVersionBlacklist())    // 加入/移出黑名单
		v1Group.POST("/version/:project/blacklist/list", device.ListDeviceVersionBlacklist()) // 获取黑名单站点
		v1Group.POST("/version/:project/check", device.CheckDeviceVersion())                  // 检查设备版本是否为目标版本
		v1Group.POST("/version/:project/worksheet", device.IssueDeviceVersionWorksheet())     // 下发工单
		v1Group.GET("/version/:project/prod-plm", device.GetDeviceProdPlm())                  // 下发工单的版本可选项

		// 双向站看板
		v1Group.GET("/bidirectional/list", device.ListBidirectionalDevices())                 // 获取所有双向站
		v1Group.GET("/bidirectional/subscription", device.GetBidirectionalSubscription())     // 获取看板订阅信息
		v1Group.POST("/bidirectional/subscription", device.UpdateBidirectionalSubscription()) // 修改看板订阅信息
		v1Group.GET("/bidirectional/info", device.GetBidirectionalInfo())                     // 看板数据查询
		v1Group.GET("/bidirectional/info/download", device.DownloadBidirectionalInfo())       // 看板数据下载
	}

	imageGroup := route.Group("/image")
	imageV1Group := imageGroup.Group("/v1")
	image := exec.NewImageHandler(route.Watcher, conf, area)
	{
		imageV1Group.POST("/operation/:project/:device_id", image.UploadImage(prom, "operation"))   // 运营图片上传
		imageV1Group.POST("/internal/:project/:device_id", image.UploadImage(prom, "internal"))     // 非运营图片上传
		imageV1Group.GET("/:project/:device_id/list", image.GetSpecifiedDeviceImages())             // 获取单站图片列表
		imageV1Group.GET("/:project/list", image.GetImagesByProject())                              // 获取单类型所有站的图片列表
		imageV1Group.PUT("/:project/delete/image", image.DeleteImage())                             // 欧洲用 删除匿名失败的照片
		imageV1Group.GET("/type/list", image.GetImageTypeList())                                    // 获取图片类型列表
		imageV1Group.GET("/algorithm/name-mapping", image.GetImageAlgorithmMapping())               // 数据获取算法下拉框
		imageV1Group.GET("/:project/camera/info", image.GetCameraInfo())                            // 摄像头管理，获取摄像头相关信息下拉框
		imageV1Group.POST("/:project/camera/judge", image.UpdateCameraJudgeResult())                // 摄像头管理，人工判定和标记黑名单摄像头
		imageV1Group.POST("/:project/camera/clear/blacklist", image.ClearCameraBlacklist())         // 摄像头管理，清空摄像头黑名单
		imageV1Group.GET("/:project/camera/device", image.GetCameraDevice())                        // 摄像头管理，获取满足筛选条件的站点
		imageV1Group.GET("/:project/camera/details", image.GetCameraDetails())                      // 摄像头管理，获取站点详细的照片
		imageV1Group.GET("/:project/camera/table/info", image.GetCameraTableInfo())                 // 摄像头管理，导出摄像头数据
		imageV1Group.POST("/camera/sync", image.SyncCameraTable())                                  // 摄像头管理，定时任务同步站-摄像头数据【deprecated】
		imageV1Group.GET("/:project/camera/acceptance-result", image.GetCameraAcceptanceResult())   // 摄像头管理，查询验收结果
		imageV1Group.POST("/:project/camera/acceptance-result", image.SendCameraAcceptanceResult()) // 摄像头管理，发送验收结果
	}

	qualityGroup := route.Group("/quality")
	quality := exec.NewQualityHandler(route.Watcher, conf)
	{
		qualityGroup.GET("/factory/test/info", quality.GetFactoryTestInfo())
		qualityGroup.POST("/factory/test-report/:project/:device_id", quality.UploadFactoryData(prom))                // 上传出厂/落站测试报告
		qualityGroup.POST("/factory/torque-report/:category/:project/:device_id", quality.CreateTorqueRecord())       // 生成扭矩报告（内部测试）
		qualityGroup.POST("/factory/torque-report/:category/:project/generate", quality.GenerateTorqueRecord(prom))   // 手动生成扭矩报告
		qualityGroup.GET("/factory/services/:category/:project", quality.GetServiceForTorque())                       // 查询服务信息（扭矩报告）
		qualityGroup.GET("/factory/test-report/:category/:project/list", quality.FactoryDataList(umw.TestReport))     // 查询出厂/落站测试报告
		qualityGroup.GET("/factory/torque-report/:category/:project/list", quality.FactoryDataList(umw.TorqueReport)) // 查询出厂/落站扭矩报告
		qualityGroup.GET("/factory/test-report/download/:report_id", quality.DownloadFactoryTestData())               // 下载测试报告
		qualityGroup.GET("/factory/plc-record/:project/:device_id", quality.GetPLCForFeatureCalculation())            // 获取扭矩计算所需的plc数据
		qualityGroup.GET("/factory/torque-report/download/:report_id", quality.DownloadFactoryTorqueData(area))       // 生成并下载扭矩报告
		qualityGroup.POST("/factory/device/register/callback", quality.DeviceRegisterCallback())
		qualityGroup.POST("/factory/browndragon/cert-record", quality.UploadCertRecord()) // 新增棕龙烧写记录
		qualityGroup.GET("/factory/browndragon/cert-record", quality.GetCertRecord())     // 查询棕龙烧写记录

		qualityGroup.GET("/test", quality.SyncTorqueData()) //
	}

	algorithmGroup := route.Group("/algorithm")
	algorithmV1Group := algorithmGroup.Group("/v1")
	algorithm := exec.NewAlgorithmHandler(route.Watcher, conf.Sentry.AppId, conf.OSS, conf)
	{
		route.POST("/ses/v1/records", algorithm.UploadDeviceRecords())
		algorithmV1Group.POST("/version-management/publish-version/:project", algorithm.AddNewPublishVersion())                                        // 新增大版本
		algorithmV1Group.PUT("/version-management/version/:project/:publish_version_id", algorithm.UpdatePublishVersion())                             // 更新大版本且新增算法
		algorithmV1Group.GET("/version-management/version/algorithm-data/:project", algorithm.GetAlgorithmData())                                      // 获取算法列表
		algorithmV1Group.PUT("/version-management/version/algorithm-data/:project/:publish_version_id/:algorithm_id", algorithm.UpdateAlgorithmData()) // 更新算法
		algorithmV1Group.GET("/version-management/version/algorithm-data/algorithm-name/list", algorithm.GetAlgorithmNames())                          // 获取算法名称
		algorithmV1Group.PUT("/version-management/version/algorithm-data/test-report/:project/:algorithm_id", algorithm.AddAlgorithmTestReport())
		algorithmV1Group.GET("/data-versioning/publish-ts/list", algorithm.GetPublishTimestamps()) // 获取所有发布时间
		algorithmV1Group.GET("/data-versioning", algorithm.GetDataVersioningData())                // 获取data versioning列表
		algorithmV1Group.POST("/data-versioning/dataset/info", algorithm.AddNewDataset(area))
		algorithmV1Group.POST("/data-versioning/dataset/confirm", algorithm.ConfirmDataset())
		algorithmV1Group.POST("/data-versioning/qm/dataset", algorithm.AddQMDatasetInfo(area))
		algorithmV1Group.DELETE("/data-versioning/file", algorithm.DeleteDVFile(area))

		algorithmV1Group.POST("/pie/info/:project/", algorithm.UpdatePieInfo())
		algorithmV1Group.DELETE("/pie/info/:project/:pie_id", algorithm.DeletePieInfo())
		algorithmV1Group.GET("/algorithm-visual/:project/list", algorithm.GetAlgorithmVisualData())              // 获取算法可视化列表
		algorithmV1Group.GET("/algorithm-visual/success-rate/:project/all", algorithm.GetSuccessRateAll())       // 获取所有站点成功率
		algorithmV1Group.GET("/algorithm-visual/success-rate/:project/trend", algorithm.GetSuccessRateTrend())   // 获取成功率变化趋势
		algorithmV1Group.GET("/algorithm-visual/success-rate/:project/lowest", algorithm.GetSuccessRateLowest()) // 获取成功率最低TOP10站点
		algorithmV1Group.GET("/algorithm-visual/vehicle-battery/:project/list", algorithm.GetVehicleBattery())   // 获取所有车辆类型和电池类型
		algorithmV1Group.GET("/ftt/:project", algorithm.GetAlgorithmFTT())                                       // 获取昨日算法FTT
		algorithmV1Group.GET("/pangea/ftt/:project", algorithm.GetCountAlgorithmFTT())                           // 获取昨日算法FTT
		algorithmV1Group.POST("/ftt/:project/list", algorithm.UpdateFTTList())                                   // 更新FTT算法列表， 前端不使用，仅用于手动更新
		algorithmV1Group.GET("/aec/:project/list", algorithm.GetAecStatus())                                     // 获取AEC运行状况

		algorithmV1Group.POST("/lpr/:project/data-report", algorithm.LPRDataReport()) // 车牌识别数据上传
		algorithmV1Group.GET("/lpr/data-report/list", algorithm.GetLPRDataReport())   // 获取车牌识别数据列表

		algorithmV1Group.POST("/cms/:project/order-info", algorithm.GetCMSOrderInfo())  // 获取cms特征数据
		algorithmV1Group.POST("/cms/:project/result", algorithm.StoreCMSResult())       // 存储cms执行结果
		algorithmV1Group.GET("/cms/:project/:device_id/query", algorithm.ListCMSData()) // 查询cms数据
		algorithmV1Group.GET("/eps/query", algorithm.ListEPSData())                     // 查询新eps数据

		algorithmV1Group.POST("/psos/result", algorithm.StorePsosResult())                                     // 上传PSOS算法运行结果
		algorithmV1Group.GET("/psos/task/list", algorithm.ListPsosTasks())                                     // psos task列表
		algorithmV1Group.GET("/psos/task/query_name", algorithm.QueryPsosTaskName())                           // psos task name 模糊搜索
		algorithmV1Group.GET("/psos/task/query_config_id", algorithm.QueryPsosConfigId())                      // psos 获取全部 config id
		algorithmV1Group.GET("/psos/task/simulation/list", algorithm.ListPsosSimulations())                    // psos simulation列表
		algorithmV1Group.GET("/psos/task/simulation/:simulation_id", algorithm.GetPsosSimulationById())        // 通过id获取psos simulation
		algorithmV1Group.GET("/psos/task/:task_id/graph_data", algorithm.GetPsosTaskGraphData())               // 获取task的图表数据
		algorithmV1Group.POST("/psos/task/:task_id/stop", algorithm.StopPsosTask())                            // 停止任务
		algorithmV1Group.POST("/psos/:task_id/:simulation_id/heart_beat", algorithm.PsosSimulationHeartbeat()) // psos仿真任务心跳
		algorithmV1Group.GET("/psos/battery-enum", algorithm.BatteryEnum())                                    // 预览电池配比枚举结果
		algorithmV1Group.GET("/psos/battery-config/:source", algorithm.CalculateBatteryConfig())               // 获取平均电池配比
		algorithmV1Group.POST("/psos/task/simulation/gen_report", algorithm.PsosSimulationGenReport())         // 生成仿真报告
		algorithmV1Group.POST("/psos/config/single", algorithm.PsosSingleConfig())                             // 新建/编辑配置，单配方
		algorithmV1Group.POST("/psos/config/batch", algorithm.PsosBatchConfig())                               // 新建配置，批量生成
		algorithmV1Group.GET("/psos/battery-config/generate", algorithm.PsosBatteryInfo())                     // 电池快速配置
		algorithmV1Group.GET("/psos/service-list/generate", algorithm.PsosServiceList())                       // 订单快速配置
		algorithmV1Group.GET("/psos/config/list", algorithm.ListPsosConfigs())                                 // 查询配置列表
		algorithmV1Group.DELETE("/psos/config/:config_id", algorithm.DeletePsosConfig())                       // 删除配置
		algorithmV1Group.GET("/psos/config/real-device/:project/:device_id", algorithm.GetPsosDeviceParam())   // 查询单站真实配置
		algorithmV1Group.GET("/psos/config/:config_id/detail", algorithm.GetPsosConfig())                      // 编辑时查询配置详细信息
		algorithmV1Group.POST("/psos/task/by_config", algorithm.NewTaskByConfig())                             // 新建仿真任务，已有配方生成
		algorithmV1Group.POST("/psos/watch_dog", algorithm.PsosWatchDog())                                     // 触发看门狗
		algorithmV1Group.POST("/psos/task/run", algorithm.PsosRunAllDevices())                                 // 一键运行所有设备仿真

		algorithmV1Group.GET("/sapa/alarm/list", algorithm.SAPAAlarmList())             // 获取泊车占位告警列表
		algorithmV1Group.GET("/sapa/alarm/download", algorithm.DownloadSAPAAlarm())     // 下载泊车占位告警列表
		algorithmV1Group.GET("/sapa/stat_panel", algorithm.SAPAStatPanel())             // 获取泊车占位看板
		algorithmV1Group.GET("/sapa/stat_panel/download", algorithm.DownloadSAPAStat()) // 下载泊车占位看板

		algorithmV1Group.POST("/activity/calculate", algorithm.CalculateActivityStats())    // 计算活跃度统计数据
		algorithmV1Group.GET("/activity/:project/stats", algorithm.ListActivityStats())     // 查询数据量统计数据
		algorithmV1Group.GET("/activity/:project/devices", algorithm.ListActivityDevices()) // 查询流量用完站点名单

		algorithmV1Group.GET("/daily-report/:project/list", algorithm.ListDailyReport()) // 获取算法结果列表
	}

	diagnosisGroup := route.Group("/diagnosis")
	diagnosisV1Group := diagnosisGroup.Group("/v1")
	diagnosis := exec.NewDiagnosisHandler(route.Watcher, conf)
	{
		diagnosisV1Group.GET("/troubleshooting/:project/list", diagnosis.TroubleEventsList())
		diagnosisV1Group.GET("/troubleshooting/:project/snapshot/:device_id/:request_id/details", diagnosis.GetSnapshotDiagnosis())
		diagnosisV1Group.PUT("/troubleshooting/:project/snapshot/:device_id/:_id", diagnosis.UpdateSnapshotDiagnosisResult())
		diagnosisV1Group.GET("/alarm/:project/dataIdList", diagnosis.AlarmIdList())                // 告警下拉列表
		diagnosisV1Group.GET("/alarm/:project/list", diagnosis.AlarmList())                        // 全设备告警列表
		diagnosisV1Group.GET("/alarm/:project/:device_id/list", diagnosis.AlarmListByDevice())     // 单设备告警列表
		diagnosisV1Group.GET("/alarm/:project/section_analysis", diagnosis.AlarmSectionAnalysis()) // 告警区间分析
		diagnosisV1Group.POST("/remote/operation/command", diagnosis.RemoteOperationCommand())
		diagnosisV1Group.GET("/log-analysis/:project/list", diagnosis.ListLogAnalysis())
		route.GET("/sync/system/checked/params", diagnosis.GetCheckedParams())
		diagnosisV1Group.POST("/fire-alarm/:project/:device_id", diagnosis.UploadAlarmLog(area)) // 上传消防告警日志
		diagnosisV1Group.GET("/process-status", diagnosis.ListProcessStatus())                   // 获取进程运行状态
		diagnosisV1Group.GET("/fire-alarm", diagnosis.ListFireAlarm())                           // 获取消防告警数据
		diagnosisV1Group.GET("/op-log/:project/:device_id", diagnosis.GetOperationLog())         // 获取操作日志（站+OSS）

		diagnosisV1Group.GET("/groot", diagnosis.ListGroot())                 // 获取groot充电桩诊断数据
		diagnosisV1Group.GET("/groot/device_id", diagnosis.ListGrootDevice()) // 模糊匹配groot充电桩id

		// 蓝牙断连看板，这类需求真的不合理，直接数仓1天人力搞定
		// TMD 浪费前后端4天人力，还不合理使用在线数据库，而且蓝牙没什么问题后，就没价值了，钱多烧得慌
		// 真的是什么烂活都接 🤷🏻‍
		// 即使我们没有数分资源，后端去了解下数仓搭看板写写SQL也能搞定。但是这里研发一般不参与需求讨论，直接给研发派活并且大概方案敲定后给到研发
		diagnosisV1Group.GET("/bluetooth_disconnect/stat_panel", diagnosis.GetBluetoothDisconnectStatPanel())                       // 获取蓝牙断连统计面板数据
		diagnosisV1Group.GET("/bluetooth_disconnect/device/list", diagnosis.ListDeviceBluetoothDisconnectStat())                    // 设备蓝牙断连排行
		diagnosisV1Group.GET("/bluetooth_disconnect/:project/alarm/list", diagnosis.GetBluetoothDisconnectAlarmInfo())              // 获取蓝牙断连告警列表
		diagnosisV1Group.GET("/bluetooth_disconnect/:project/alarm/:alarm_id", diagnosis.GetBluetoothDisconnectAlarmDistribution()) // 获取蓝牙断连单一告警分布

		diagnosisV1Group.GET("/stuck/stat_panel", diagnosis.GetStuckStatPanel())                                          // 获取挂车统计面板数据
		diagnosisV1Group.GET("/stuck/device_stat/list", diagnosis.ListDeviceStuckStat())                                  // 获取设备维度挂车统计信息
		diagnosisV1Group.GET("/stuck/service_step", diagnosis.GetStuckServiceStepAnalysis())                              // 获取订单步骤分析信息
		diagnosisV1Group.GET("/stuck/service", diagnosis.ListStuckService())                                              // 获取挂车订单列表
		diagnosisV1Group.POST("/stuck/service/update", diagnosis.UpdateStuckService())                                    // 更新挂车订单列表
		diagnosisV1Group.GET("/stuck/:project/alarm_id", diagnosis.ListStuckAlarmId())                                    // 挂车告警搜索下拉列表
		diagnosisV1Group.GET("/jira/list", diagnosis.ListJira())                                                          // jira/bug搜索
		diagnosisV1Group.GET("/stuck/:project/alarm/list", diagnosis.ListStuckAlarmInfo())                                // 挂车告警列表信息
		diagnosisV1Group.GET("/stuck/:project/alarm", diagnosis.ListStuckAlarms())                                        // 挂车告警维度信息
		diagnosisV1Group.GET("/stuck/:project/alarm/:alarm_id", diagnosis.ListStuckAlarmDistribution())                   // 单一告警分布信息
		diagnosisV1Group.POST("/stuck/asyn_excute_hive_stuck_service", diagnosis.AsynExcuteHiveStuckService())            // 异步执行处理数仓算出的挂车单
		diagnosisV1Group.POST("/stuck/asyn_excute_stuck_service_daily_stat", diagnosis.AsynExcuteStuckServiceDailyStat()) // 异步执行挂车天级数据

		diagnosisV1Group.GET("/health/:project/devices", diagnosis.GetHealthWeeklyData())                       // 获取设备健康度周级数据
		diagnosisV1Group.GET("/health/:project/tail", diagnosis.GetHealthTailDevices())                         // 获取设备健康度尾部站点
		diagnosisV1Group.GET("/health/:project/devices/detail", diagnosis.GetHealthDetail())                    // 查询/下载设备健康度明细
		diagnosisV1Group.GET("/health/:project/:device_id/trend", diagnosis.GetSingleDeviceHealthTrend())       // 单站健康度详情
		diagnosisV1Group.GET("/health/:project/:device_id/servo", diagnosis.GetSingleDeviceServoHealth())       // 单站伺服健康度详情
		diagnosisV1Group.GET("/health/:project/:device_id/sensor", diagnosis.GetSingleDeviceSensorHealth())     // 单站传感器健康度详情
		diagnosisV1Group.GET("/health/:project/:device_id/charge", diagnosis.GetSingleDeviceChargeHealth())     // 单站充电模块健康度详情
		diagnosisV1Group.GET("/health/:project/worksheet/list", diagnosis.ListHealthWorksheet())                // 工单列表
		diagnosisV1Group.GET("/health/:project/worksheet/statistics", diagnosis.GetHealthWorksheetStatistics()) // 工单统计信息

		diagnosisV1Group.GET("/satisfy/list", diagnosis.ListSatisfyData())                                    // 满意度订单列表
		diagnosisV1Group.POST("/satisfy/list", diagnosis.ListSatisfyDataV2())                                 // 满意度订单列表 post,get参数太多
		diagnosisV1Group.GET("/satisfy/list/download", diagnosis.DownloadListSatisfyData())                   // 下载满意度订单列表
		diagnosisV1Group.GET("/satisfy/:project/:order_id/service-info", diagnosis.GetServiceInfoByOrderId()) // 根据订单id查询服务订单基本信息
		diagnosisV1Group.GET("/satisfy/diagnose-result", diagnosis.GetSatisfyDiagnoseResult())                // 诊断卡片
		diagnosisV1Group.GET("/satisfy/detail", diagnosis.GetSatisfyDetail())                                 // 详细信息
		diagnosisV1Group.GET("/satisfy/log", diagnosis.GetSatisfySwapLog())                                   // 换电日志
		diagnosisV1Group.POST("/satisfy/sync", diagnosis.SyncSatisfyData())                                   // 诊断数据刷写
		diagnosisV1Group.GET("/satisfy/user-tag/name-mapping", diagnosis.ListUserTags())                      // 用户标签列表
		diagnosisV1Group.GET("/satisfy/diagnosis-tag/name-mapping", diagnosis.ListDiagnosisTags())            // 诊断标签列表
		diagnosisV1Group.GET("/satisfy/label/list", diagnosis.ListLabel())
		diagnosisV1Group.GET("/satisfy/report", diagnosis.GetSatisfyReport())
		diagnosisV1Group.GET("/satisfy/report/download", diagnosis.DownloadSatisfyReport())
		diagnosisV1Group.POST("/satisfy/report_status", diagnosis.UpdateReportStatus())     // 更新自定义标签采用转态
		diagnosisV1Group.POST("/satisfy/label/asyn_excute", diagnosis.AsynExcuteDiyLabel()) // 异步执行自定义标签

	}

	larkGroup := route.Group("/lark")
	larkV1Group := larkGroup.Group("/v1")
	wf := exec.NewWorkflowHandler(conf)
	{
		larkV1Group.POST("/move-wiki/callback", wf.LarkMoveWikiCallback())
	}

	fcrdGroup := route.Group("/fcrd")
	fcrdV1Group := fcrdGroup.Group("/v1")
	fcrd := exec.NewFcrd(conf, route.Watcher)
	{
		if os.Getenv("AREA") == "Europe" {
			// 每天2:00, 12:00, 21:00 点执行, fetch Denmark fcrd hourly revenue data
			fcrd.AddCronJobs(task.GetCronTask())
		}
		fcrdV1Group.POST("/fcrd-management/:project/new-power-params", fcrd.CreateNewPowerParams())
		fcrdV1Group.POST("/fcrd-management/:project/new-power-params/:command_id", fcrd.UpdateNewPowerParams())
		fcrdV1Group.POST("/fcrd-management/:project/oss/command/:category", fcrd.IssueFCRDCommand())
		fcrdV1Group.GET("/fcrd-management/:project/power-params-record", fcrd.ListPowerParamsRecord())
		fcrdV1Group.GET("/fcrd-management/:project/:device_id/power-params-record", fcrd.GetPowerParamsRecord())
		fcrdV1Group.GET("/fcrd-management/:project/:device_id/realtime-data-history", fcrd.GetRealtimeDataHistory())
		fcrdV1Group.GET("/fcrd-management/:project/:device_id/winning-bid-history", fcrd.GetWinningBidHistory())
		fcrdV1Group.GET("/fcrd-management/:project/:device_id/current-realtime", fcrd.GetCurrentRealtime())
		fcrdV1Group.GET("/fcrd-management/:project/netherlandsAfrr-realtime", fcrd.GetNetherlandsRealtime())
		fcrdV1Group.GET("/fcrd-management/:project/netherlandsEPrice", fcrd.GetNetherlandsEPrice())
		fcrdV1Group.GET("/fcrd-management/:project/netherlandsImbalancePrice", fcrd.GetNetherlandsImbalancePrice())
		fcrdV1Group.GET("/fcrd-management/:project/fcrdPrice", fcrd.GetFcrdPrice())
		fcrdV1Group.GET("/fcrd-management/:project/:device_id/revenue", fcrd.RevenueHistory())
		fcrdV1Group.GET("/fcrd-management/:project/:device_id/revenue/download", fcrd.RevenueHistoryDownload())

	}

	fmsGroup := route.Group("/fms")
	fmsV1Group := fmsGroup.Group("/v1")
	fms := exec.NewFMSHandler(conf, area)
	{
		fmsV1Group.POST("/client/upload-file", fms.UploadOneFile())
		fmsV1Group.POST("/task/callback", fms.CallBack())
		fmsV1Group.POST("/client/upload-multipart", fms.UploadOneFileMultipart())
		fmsV1Group.POST("/client/delete-file", fms.DeleteFileList())
	}

	internalGroup := route.Group("/internal")
	internalV1Group := internalGroup.Group("/v1")

	internal := exec.NewInternalHandler(conf)
	{
		internalV1Group.POST("/mock/nmp/command", internal.IssueCommand2Station(area))
		internalV1Group.POST("/generateNewSign", internal.GenerateNewSign())
		internalV1Group.POST("/requestWithSign", internal.RequestWithSign())

		internalV1Group.GET("/people/query", internal.QueryPeople())                              // 员工信息查询
		internalV1Group.POST("/csv_parse", internal.CsvParse())                                   // csv文件解析
		internalV1Group.GET("/electricity-price", internal.GetElectricityPrice())                 // 获取电价信息
		internalV1Group.GET("/electricity-price/organized", internal.GetElectricityByOrganized()) // 获取组织好的电价信息
		internalV1Group.GET("/vehicle/name-mapping", internal.GetVehicleType())                   // 获取车辆品牌和类型
		internalV1Group.GET("/basic/name-mapping", internal.GetBasicNameMapping())                // 天宫通用映射表
		internalV1Group.GET("/basic/name-mapping/by-key", internal.GetBasicNameMappingByKey())    // 通过不同的key获取天宫映射表

		internalV1Group.POST("/push/redrabbit-params", internal.PushRedRabbitParams()) // 推送赤兔数据到kafka，供数仓消费
		internalV1Group.POST("/push/mazu-params", internal.PushMazuParams())           // 推送赤兔mazu数据到kafka，供运维发送告警工单

		internalV1Group.POST("/sync/device-proto", internal.SyncDeviceProto())                 // 同步设备点表
		internalV1Group.POST("/async/power_charge_devices", internal.SyncPowerChargeDevices()) // 同步全量桩设备信息
	}

	euroDataInfo := exec.NewEuroDataInfoHandler(route.Watcher, constant.Country)
	{
		route.GET("/euroPriceData", euroDataInfo.GetEuroPriceData())
		route.GET("/euroWindSolarData", euroDataInfo.GetEuroWindSolarData())
		route.GET("/NetherlandsImbalancePrice", euroDataInfo.GetNetherlandsImbalancePrice())
		route.GET("/Netherlandsafrr", euroDataInfo.GetNetherlandsAfrr())
		route.GET("/FcrdPrice", euroDataInfo.GetFcrdPrice())
	}

}
