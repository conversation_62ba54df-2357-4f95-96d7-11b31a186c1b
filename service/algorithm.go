package service

import (
	"fmt"
	"io"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	"go.uber.org/zap"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	"git.nevint.com/welkin2/welkin-backend/util"
)

type Algorithm struct {
	URL            string
	Logger         *zap.SugaredLogger
	PromCollectors map[string]prometheus.Collector
}

func (a *Algorithm) SendServiceInfoForTorqueFeatureCalculation(ctx *gin.Context, requestId, deviceId string, category int, servicesInfo []umw.ReportServiceInfo) {
	project := ctx.Param("project")
	requestBody := map[string]interface{}{
		"request_id":   requestId,
		"service_info": servicesInfo,
	}

	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:    fmt.Sprintf("%s/v1/grpc/%d/torque-calculation/%s/%s", a.URL, category, project, deviceId),
		Method: "POST",
		Header: map[string]string{
			"Content-Type": "application/json",
		},
		RequestBody: requestBody,
	})
	body, statusCode, err := ct.Do()
	failCnt := a.PromCollectors[util.FailSendServiceInfo.ID].(*prometheus.CounterVec)
	if err != nil {
		failCnt.WithLabelValues(project).Inc()
		a.Logger.Errorf("send service info for torque feature calculation, err: %v", err)
		return
	}
	defer body.Close()
	data, dErr := io.ReadAll(body)
	if dErr != nil {
		failCnt.WithLabelValues(project).Inc()
		a.Logger.Errorf("send service info for torque feature calculation, err: %v", dErr)
		return
	}
	if statusCode != http.StatusOK {
		failCnt.WithLabelValues(project).Inc()
		a.Logger.Errorf("send service info for torque feature calculation, err: %v, url: %s", string(data), ct.URL)
		return
	}
	a.Logger.Infof(
		"succeeded to send service info for torque feature calculation, category: %d, request body: %v", category, requestBody)
	return
}
