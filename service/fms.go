package service

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"git.nevint.com/welkin2/welkin-backend/config"
	"git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/util"
	"github.com/gin-gonic/gin"
	"io"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	um "git.nevint.com/golang-libs/common-utils/model"
	"git.nevint.com/welkin2/welkin-backend/model"
)

type FMS struct {
	URL          string
	AppId        string
	AppSecret    string
	ClientId     string
	ClientSecret string
	PriBucketKey string
	PubBucketKey string
	Header       map[string]string
	Logger       *zap.SugaredLogger
}

var globalFMS *FMS

func GetFMS() *FMS {
	if globalFMS == nil {
		globalFMS = &FMS{
			URL:          config.Cfg.FMS.Url,
			AppId:        config.Cfg.Sentry.AppId,
			AppSecret:    config.Cfg.Sentry.AppSecret,
			ClientId:     config.Cfg.FMS.ClientId,
			ClientSecret: config.Cfg.FMS.ClientSecret,
			PriBucketKey: config.Cfg.FMS.PriBucketKey,
			PubBucketKey: config.Cfg.FMS.PubBucketKey,
			Header: map[string]string{
				"Content-Type": "application/json;charset=UTF-8",
			},
			Logger: logger.Logger.Named("FMS"),
		}
	}
	return globalFMS
}

func (f *FMS) GetFileUploadToken(fileDir, fileName, ftype, buffer, area string) (model.FMSFileUploadTokenResponse, error) {
	var response model.FMSFileUploadTokenResponse
	var digest string
	if area == um.Europe {
		digest = ucmd.GenSha256(buffer)
	} else {
		digest = ucmd.GenMd5(buffer)
	}
	requestBody := map[string]interface{}{
		"client_id":     f.ClientId,
		"client_secret": f.ClientSecret,
		"file_dir":      fileDir,
		"file_name":     fileName,
		"file_checksum": digest,
		"uuid":          false,
	}
	// 国内使用获取预签名链接类型token
	if area != um.Europe {
		requestBody["token_type"] = "presign"
	}
	switch ftype {
	case model.IMAGE:
		requestBody["bucket_key"] = f.PubBucketKey
	case model.NeedPublic:
		requestBody["bucket_key"] = f.PubBucketKey
	default:
		requestBody["bucket_key"] = f.PriBucketKey
	}
	ts := time.Now().Unix()
	path := "/service/file/getUploadToken"
	sign := ucmd.Sign{
		Timestamp:   ts,
		AppId:       f.AppId,
		AppSecret:   f.AppSecret,
		ContentType: f.Header["Content-Type"],
		Method:      "POST",
		Path:        path,
		URLParams:   map[string]interface{}{"hash_type": "sha256"},
		BodyParams:  requestBody,
	}
	sn := sign.Generate()
	if sn == "" {
		f.Logger.Errorf("get file upload token, err: `sign` is empty")
		return response, errors.New("`sign` is empty")
	}

	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:    fmt.Sprintf("%s%s?app_id=%s&sign=%s&timestamp=%d&hash_type=sha256", f.URL, path, f.AppId, sn, ts),
		Method: "POST",
		Header: map[string]string{
			"Content-Type": f.Header["Content-Type"],
		},
		RequestBody: requestBody,
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		f.Logger.Errorf("get file upload token, err: %v", err)
		return response, err
	}
	defer body.Close()
	data, dErr := ioutil.ReadAll(body)
	if dErr != nil {
		f.Logger.Errorf("get file upload token, err: %s", dErr)
		return response, dErr
	}
	if statusCode != http.StatusOK {
		f.Logger.Errorf("get file upload token, err: %s", string(data))
		return response, errors.New(string(data))
	}
	if err = json.Unmarshal(data, &response); err != nil {
		f.Logger.Errorf("get file upload token, err: %v", err)
		return response, err
	}
	if response.ResultCode != "success" {
		f.Logger.Errorf("get file upload token, err: %v", string(data))
		return response, errors.New(string(data))
	}
	f.Logger.Infof("succeeded to get file upload token, file_dir: %s, file_name: %s", fileDir, fileName)
	return response, nil
}

func (f *FMS) GetFileAuthorizeURL(urlList []string) (model.FMSFileAuthorizeURLResponse, error) {
	var response model.FMSFileAuthorizeURLResponse
	requestBody := map[string]interface{}{
		"file_URL_list": urlList,
		"expire_sec":    600,
		"client_id":     f.ClientId,
		"client_secret": f.ClientSecret,
	}
	ts := time.Now().Unix()
	path := "/service/file/authorizeURL"
	sign := ucmd.Sign{
		Timestamp:   ts,
		AppId:       f.AppId,
		AppSecret:   f.AppSecret,
		ContentType: f.Header["Content-Type"],
		Method:      "POST",
		Path:        path,
		URLParams:   map[string]interface{}{"hash_type": "sha256"},
		BodyParams:  requestBody,
	}
	sn := sign.Generate()
	if sn == "" {
		f.Logger.Errorf("get file authorize url, err: `sign` is empty")
		return response, errors.New("`sign` is empty")
	}

	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:    fmt.Sprintf("%s%s?app_id=%s&sign=%s&timestamp=%d&hash_type=sha256", f.URL, path, f.AppId, sn, ts),
		Method: "POST",
		Header: map[string]string{
			"Content-Type": f.Header["Content-Type"],
		},
		RequestBody: requestBody,
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		f.Logger.Errorf("get file authorize url, err: %v", err)
		return response, err
	}
	defer body.Close()
	data, dErr := ioutil.ReadAll(body)
	if dErr != nil {
		f.Logger.Errorf("get file authorize url, err: %s", dErr)
		return response, dErr
	}
	if statusCode != http.StatusOK {
		f.Logger.Errorf("get file authorize url, err: %s", string(data))
		return response, errors.New(string(data))
	}
	if err = json.Unmarshal(data, &response); err != nil {
		f.Logger.Errorf("get file authorize url, err: %v", err)
		return response, err
	}
	if response.ResultCode != "success" {
		f.Logger.Errorf("get file authorize url, err: %v", string(data))
		return response, errors.New(string(data))
	}
	f.Logger.Infof("succeeded to get file authorize url: %s", ucmd.ToJsonStrIgnoreErr(response.ResultData))
	return response, nil
}

func (f *FMS) UploadFile(path string, header map[string]string, reader io.Reader) error {
	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:    path,
		Method: "PUT",
		Header: header,
		Reader: reader,
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		f.Logger.Errorf("upload file, err: %v", err)
		return err
	}
	defer body.Close()
	data, dErr := ioutil.ReadAll(body)
	if dErr != nil {
		f.Logger.Errorf("upload file, err: %s", dErr)
		return dErr
	}
	if statusCode != http.StatusOK {
		f.Logger.Errorf("upload file, code: %d, err: %s", statusCode, string(data))
		return errors.New(string(data))
	}
	f.Logger.Infof("succeeded to upload file, path: %s", strings.Split(path, "?")[0])
	return nil
}

func (f *FMS) SubmitCompressTask(destFileUrl string, srcFileUrls []string) (string, error) {
	requestBody := map[string]interface{}{
		"client_id":     f.ClientId,
		"client_secret": f.ClientSecret,
		"task_list": []interface{}{
			map[string]interface{}{
				"cmd": "compression",
				"cmd_param": map[string]interface{}{
					"format": "zip",
					//"encryption_key": "1234",
					"flatten_dir": true,
				},
				"dest_file_url":    destFileUrl,
				"src_file_url_set": srcFileUrls,
				"callback":         fmt.Sprintf("%v/fms/v1/task/callback", config.Cfg.Welkin.BackendUrl),
			},
		},
	}

	ts := time.Now().Unix()
	path := "/service/file/submitTask"
	sign := ucmd.Sign{
		Timestamp:   ts,
		AppId:       f.AppId,
		AppSecret:   f.AppSecret,
		ContentType: f.Header["Content-Type"],
		Method:      "POST",
		Path:        path,
		URLParams:   map[string]interface{}{"hash_type": "sha256"},
		BodyParams:  requestBody,
	}
	sn := sign.Generate()
	if sn == "" {
		return "", errors.New("sign.Generate() empty")
	}
	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:    fmt.Sprintf("%s%v?app_id=%s&sign=%s&timestamp=%d&hash_type=sha256", f.URL, path, f.AppId, sn, ts),
		Method: "POST",
		Header: map[string]string{
			"Content-Type": f.Header["Content-Type"],
		},
		RequestBody: requestBody,
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		f.Logger.Errorf("get file upload token, err: %v", err)
		return "", err
	}
	defer body.Close()
	data, err := ioutil.ReadAll(body)
	if err != nil {
		f.Logger.Errorf("ioutil.ReadAll, err: %s", err)
		return "", err
	}
	if statusCode != http.StatusOK {
		return "", errors.New(fmt.Sprintf("statusCode not 200. status code:%v", statusCode))
	}
	var resp FmsSubmitTaskResp
	err = json.Unmarshal(data, &resp)
	if err != nil {
		return "", err
	}
	if resp.ResultCode != "success" {
		return "", errors.New(fmt.Sprintf("resp code not success. req:%v resp:%v", ucmd.ToJsonStrIgnoreErr(requestBody), ucmd.ToJsonStrIgnoreErr(resp)))
	}
	if len(resp.ResultData) != 1 {
		return "", errors.New("no ResultData")
	}
	return resp.ResultData[0].TaskId, nil
}

type FmsSubmitTaskResp struct {
	ResultCode string `json:"result_code"`
	ResultDesc string `json:"result_desc"`
	ResultData []struct {
		TaskId string `json:"task_id"`
	} `json:"result_data"`
	ServerTime int    `json:"server_time"`
	TxId       string `json:"tx_id"`
	Success    bool   `json:"success"`
}

// 目前测试使用 查询fms task
func (f *FMS) QueryTask(taskIds []string) error {
	requestBody := map[string]interface{}{
		"client_id":     f.ClientId,
		"client_secret": f.ClientSecret,
		"task_id_list":  taskIds,
	}

	ts := time.Now().Unix()
	path := "/service/file/listTaskByTaskIdList"
	sign := ucmd.Sign{
		Timestamp:   ts,
		AppId:       f.AppId,
		AppSecret:   f.AppSecret,
		ContentType: f.Header["Content-Type"],
		Method:      "POST",
		Path:        path,
		URLParams:   map[string]interface{}{"hash_type": "sha256"},
		BodyParams:  requestBody,
	}
	sn := sign.Generate()
	if sn == "" {
		return errors.New("sign.Generate() empty")
	}
	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:    fmt.Sprintf("%s%v?app_id=%s&sign=%s&timestamp=%d&hash_type=sha256", f.URL, path, f.AppId, sn, ts),
		Method: "POST",
		Header: map[string]string{
			"Content-Type": f.Header["Content-Type"],
		},
		RequestBody: requestBody,
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		f.Logger.Errorf("get file upload token, err: %v", err)
		return err
	}
	defer body.Close()
	data, err := ioutil.ReadAll(body)
	if err != nil {
		f.Logger.Errorf("ioutil.ReadAll, err: %s", err)
		return err
	}
	if statusCode != http.StatusOK {
		return errors.New(fmt.Sprintf("statusCode not 200. status code:%v", statusCode))
	}
	println(data)
	return nil
}

// 目前测试使用 列出文件
func (f *FMS) ListFile(ftype string) {
	requestBody := map[string]interface{}{
		"client_id":     f.ClientId,
		"client_secret": f.ClientSecret,
		"aggregate":     false, //平铺模式
		//"bucket_key": "xxx",
		//"marker":   "", //分页标记
		"max_keys": 10,
		"prefix":   "welkin/algorithm/",
	}
	switch ftype {
	case model.IMAGE:
		requestBody["bucket_key"] = f.PubBucketKey
	case model.NeedPublic:
		requestBody["bucket_key"] = f.PubBucketKey
	default:
		requestBody["bucket_key"] = f.PriBucketKey
	}
	ts := time.Now().Unix()
	path := "/service/file/list"
	sign := ucmd.Sign{
		Timestamp:   ts,
		AppId:       f.AppId,
		AppSecret:   f.AppSecret,
		ContentType: f.Header["Content-Type"],
		Method:      "POST",
		Path:        path,
		URLParams:   map[string]interface{}{"hash_type": "sha256"},
		BodyParams:  requestBody,
	}
	sn := sign.Generate()
	if sn == "" {
		f.Logger.Errorf("get file upload token, err: `sign` is empty")
		//return response, errors.New("`sign` is empty")
	}
	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:    fmt.Sprintf("%s%v?app_id=%s&sign=%s&timestamp=%d&hash_type=sha256", f.URL, path, f.AppId, sn, ts),
		Method: "POST",
		Header: map[string]string{
			"Content-Type": f.Header["Content-Type"],
		},
		RequestBody: requestBody,
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		f.Logger.Errorf("get file upload token, err: %v", err)
	}
	defer body.Close()
	data, err := ioutil.ReadAll(body)
	if err != nil {
		f.Logger.Errorf("ioutil.ReadAll, err: %s", err)
	}
	if statusCode != http.StatusOK {
		f.Logger.Errorf("get file upload token, status code: %s", statusCode)
	}
	println(data)
}

func (f *FMS) DeleteFile(c *gin.Context, fileUrlList []string) error {
	requestBody := map[string]interface{}{
		"client_id":     f.ClientId,
		"client_secret": f.ClientSecret,
		"file_url_list": fileUrlList,
	}
	ts := time.Now().Unix()
	path := "/service/file/delete"
	sign := ucmd.Sign{
		Timestamp:   ts,
		AppId:       f.AppId,
		AppSecret:   f.AppSecret,
		ContentType: f.Header["Content-Type"],
		Method:      "POST",
		Path:        path,
		URLParams:   map[string]interface{}{"hash_type": "sha256"},
		BodyParams:  requestBody,
	}
	sn := sign.Generate()
	if sn == "" {
		logger.CtxLog(c).Errorf("delete file, err: `sign` is empty")
		return fmt.Errorf("delete file, err: `sign` is empty")
	}

	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:    fmt.Sprintf("%s%s?app_id=%s&sign=%s&timestamp=%d&hash_type=sha256", f.URL, path, f.AppId, sn, ts),
		Method: "POST",
		Header: map[string]string{
			"Content-Type": f.Header["Content-Type"],
		},
		RequestBody: requestBody,
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		logger.CtxLog(c).Errorf("delete file, err: %v", err)
		return err
	}
	defer body.Close()
	data, dErr := io.ReadAll(body)
	if dErr != nil {
		logger.CtxLog(c).Errorf("delete file, err: %s", dErr)
		return dErr
	}
	if statusCode != http.StatusOK {
		logger.CtxLog(c).Errorf("delete file, err: %s", string(data))
		return fmt.Errorf("delete file, err: %s", string(data))
	}
	response := make(map[string]interface{})
	if err = json.Unmarshal(data, &response); err != nil {
		logger.CtxLog(c).Errorf("delete file, fail to unmarshal response, err: %v", err)
		return err
	}
	if response["result_code"] != "success" {
		logger.CtxLog(c).Errorf("delete file, err: %v", string(data))
		return fmt.Errorf("delete file, err: %v", string(data))
	}
	logger.CtxLog(c).Infof("succeeded to delete files, file url list: %s", ucmd.ToJsonStrIgnoreErr(fileUrlList))
	return nil
}

const chunkSize = 20 * 1 << 20 // 20MB

// UploadMultipart 分片上传文件
func (f *FMS) UploadMultipart(c *gin.Context, fileHeader *multipart.FileHeader, path string) (fileUrl string, err error) {
	file, err := fileHeader.Open()
	if err != nil {
		logger.CtxLog(c).Errorf("fail to open file: %v", err)
		return
	}
	defer file.Close()
	createMultipartResp, err := f.createMultipart(c, fileHeader, path)
	if err != nil {
		logger.CtxLog(c).Errorf("fail to create multipart file: %v", err)
		return
	}
	if createMultipartResp.ResultCode != "success" {
		logger.CtxLog(c).Errorf("create multipart file, resp code not success, path:%v, filename:%v, resp:%v", path, fileHeader.Filename, ucmd.ToJsonStrIgnoreErr(createMultipartResp))
		return "", fmt.Errorf("resp code not success, path:%v, filename:%v, resp:%v", path, fileHeader.Filename, ucmd.ToJsonStrIgnoreErr(createMultipartResp))
	}
	g := ucmd.NewErrGroup(context.Background(), 2)
	fileSize := fileHeader.Size
	chunkTotal := int(fileSize / chunkSize)
	if fileSize%chunkSize != 0 {
		chunkTotal += 1
	}
	presignData, err := f.getMultipartPresignURL(c, createMultipartResp.ResultData.FileKey, createMultipartResp.ResultData.UploadId, chunkTotal)
	if err != nil {
		logger.CtxLog(c).Errorf("fail to get multipart presign url: %v", err)
		return
	}
	if presignData.ResultCode != "success" {
		err = fmt.Errorf("get multipart presign url, resp code not success, createMultipartResp: %s, resp:%s", ucmd.ToJsonStrIgnoreErr(createMultipartResp.ResultData), ucmd.ToJsonStrIgnoreErr(presignData))
		logger.CtxLog(c).Error(err)
		return
	}

	partList := make([]MultipartPart, 0)
	mu := sync.Mutex{}
	for chunkIndex := 0; int64(chunkIndex*chunkSize) < fileSize; chunkIndex++ {
		// 计算当前分片的起始和结束位置
		start := int64(chunkIndex) * chunkSize
		end := start + chunkSize - 1
		if end >= fileSize {
			end = fileSize - 1
		}
		// 读取当前分片
		chunk := make([]byte, end-start+1)
		n, rErr := file.ReadAt(chunk, start)
		if rErr != nil && rErr != io.EOF {
			logger.CtxLog(c).Error("fail to read chunk: %v", rErr)
			return "", rErr
		}
		chunk = chunk[:n]
		partToken := presignData.ResultData.PartTokenList[chunkIndex]
		partToken.SupplierHttp.Header["Content-Type"] = "application/octet-stream"

		// 上传到fms
		g.GoRecover(func() error {
			partRes, gErr := f.uploadPart(c, partToken, chunk)
			if gErr != nil {
				logger.CtxLog(c).Error("fail to upload part: %v, partToken: %s", err, ucmd.ToJsonStrIgnoreErr(partToken))
				return gErr
			}
			mu.Lock()
			defer mu.Unlock()
			partList = append(partList, partRes)
			return nil
		})
	}
	if err = g.Wait(); err != nil {
		logger.CtxLog(c).Errorf("goroutine err: %v", err)
		return
	}
	// 合并分片
	if err = f.CompleteMultipart(c, createMultipartResp.ResultData.FileKey, createMultipartResp.ResultData.UploadId, partList); err != nil {
		logger.CtxLog(c).Errorf("fail to complete multipart: %v", err)
		return
	}
	for _, item := range createMultipartResp.ResultData.DomainInfoList {
		if item.DomainAttr.CDN {
			fileUrl = fmt.Sprintf("%s://%s/%s", item.Protocol, item.Domain, createMultipartResp.ResultData.FileKey)
			break
		}
	}
	return
}

// 创建分片上传任务
func (f *FMS) createMultipart(c *gin.Context, fileHeader *multipart.FileHeader, fileDir string) (response model.FMSFileCreateMultipartResponse, err error) {
	file, err := fileHeader.Open()
	if err != nil {
		logger.CtxLog(c).Errorf("fail to open file: %v", err)
		return
	}
	defer file.Close()
	buffer := bytes.NewBuffer(nil)
	if _, err = io.Copy(buffer, file); err != nil {
		logger.CtxLog(c).Errorf("fail to copy buffer: %v", err)
		return
	}
	var digest string
	if ucmd.GetArea() == um.Europe {
		digest = util.GenSha256Byte(buffer.Bytes())
	} else {
		digest = util.GenMd5Byte(buffer.Bytes())
	}
	requestBody := map[string]interface{}{
		"client_id":     f.ClientId,
		"client_secret": f.ClientSecret,
		"bucket_key":    f.PubBucketKey,
		"file_dir":      fileDir,
		"file_name":     fileHeader.Filename,
		"file_checksum": digest,
		"uuid":          false,
	}
	ts := time.Now().Unix()
	path := "/service/file/upload/createMultipartUpload"
	sign := ucmd.Sign{
		Timestamp:   ts,
		AppId:       f.AppId,
		AppSecret:   f.AppSecret,
		ContentType: f.Header["Content-Type"],
		Method:      "POST",
		Path:        path,
		URLParams:   map[string]interface{}{"hash_type": "sha256"},
		BodyParams:  requestBody,
	}
	sn := sign.Generate()
	if sn == "" {
		logger.CtxLog(c).Errorf("create multipart, err: `sign` is empty")
		return response, fmt.Errorf("create multipart, err: `sign` is empty")
	}

	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:    fmt.Sprintf("%s%s?app_id=%s&sign=%s&timestamp=%d&hash_type=sha256", f.URL, path, f.AppId, sn, ts),
		Method: "POST",
		Header: map[string]string{
			"Content-Type": f.Header["Content-Type"],
		},
		RequestBody: requestBody,
	})
	//fmt.Println(ct.URL)
	//fmt.Println(ucmd.ToJsonStrIgnoreErr(requestBody))
	body, statusCode, err := ct.Do()
	if err != nil {
		logger.CtxLog(c).Errorf("create multipart, err: %v", err)
		return
	}
	defer body.Close()
	data, dErr := io.ReadAll(body)
	if dErr != nil {
		logger.CtxLog(c).Errorf("create multipart, err: %s", dErr)
		return response, dErr
	}
	if statusCode != http.StatusOK {
		logger.CtxLog(c).Errorf("create multipart, err: %s", string(data))
		return response, fmt.Errorf("create multipart, err: %s", string(data))
	}
	if err = json.Unmarshal(data, &response); err != nil {
		logger.CtxLog(c).Errorf("create multipart, fail to unmarshal response: %v, data: %s", err, string(data))
		return
	}
	if response.ResultCode != "success" {
		logger.CtxLog(c).Errorf("create multipart, err: %v", string(data))
		return response, fmt.Errorf("create multipart, err: %v", string(data))
	}
	logger.CtxLog(c).Infof("succeeded to create multipart, file_dir: %s, file_name: %s", fileDir, fileHeader.Filename)
	return
}

// 获取分片上传预签名链接
func (f *FMS) getMultipartPresignURL(c *gin.Context, fileKey, uploadId string, chunkTotal int) (response model.FMSFileMultipartPresignURLResponse, err error) {
	partList := make([]map[string]interface{}, chunkTotal)
	for i := range chunkTotal {
		partList[i] = map[string]interface{}{"part_number": i + 1}
	}
	requestBody := map[string]interface{}{
		"bucket_key":    f.PubBucketKey,
		"client_id":     f.ClientId,
		"client_secret": f.ClientSecret,
		"file_key":      fileKey,
		"upload_id":     uploadId,
		"part_list":     partList,
	}
	// 国内使用获取预签名链接类型token
	if ucmd.GetArea() != um.Europe {
		requestBody["token_type"] = "presign"
	}
	ts := time.Now().Unix()
	path := "/service/file/upload/presignUploadPart"
	sign := ucmd.Sign{
		Timestamp:   ts,
		AppId:       f.AppId,
		AppSecret:   f.AppSecret,
		ContentType: f.Header["Content-Type"],
		Method:      "POST",
		Path:        path,
		URLParams:   map[string]interface{}{"hash_type": "sha256"},
		BodyParams:  requestBody,
	}
	sn := sign.Generate()
	if sn == "" {
		logger.CtxLog(c).Errorf("multipart presign url, err: `sign` is empty")
		return response, fmt.Errorf("multipart presign url, err: `sign` is empty")
	}

	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:    fmt.Sprintf("%s%s?app_id=%s&sign=%s&timestamp=%d&hash_type=sha256", f.URL, path, f.AppId, sn, ts),
		Method: "POST",
		Header: map[string]string{
			"Content-Type": f.Header["Content-Type"],
		},
		RequestBody: requestBody,
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		logger.CtxLog(c).Errorf("multipart presign url, err: %v", err)
		return
	}
	defer body.Close()
	data, dErr := io.ReadAll(body)
	if dErr != nil {
		logger.CtxLog(c).Errorf("multipart presign url, err: %s", dErr)
		return response, dErr
	}
	if statusCode != http.StatusOK {
		logger.CtxLog(c).Errorf("multipart presign url, err: %s", string(data))
		return response, fmt.Errorf("multipart presign url, err: %s", string(data))
	}
	if err = json.Unmarshal(data, &response); err != nil {
		logger.CtxLog(c).Errorf("multipart presign url, err: %v", err)
		return
	}
	if response.ResultCode != "success" {
		logger.CtxLog(c).Errorf("multipart presign url, err: %v", string(data))
		return response, fmt.Errorf("multipart presign url, err: %v", string(data))
	}
	logger.CtxLog(c).Infof("succeeded to multipart presign url, file_key: %s, upload_id: %s, chunkTotal: %d", fileKey, uploadId, chunkTotal)
	return
}

type MultipartPart struct {
	PartNumber int    `json:"part_number"`
	Etag       string `json:"etag"`
}

// 上传分片文件
func (f *FMS) uploadPart(c *gin.Context, partToken model.MultipartPartToken, chunk []byte) (res MultipartPart, err error) {
	chunkReader := io.NopCloser(bytes.NewReader(chunk))
	defer chunkReader.Close()
	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:    partToken.SupplierHttp.URL,
		Method: "PUT",
		Header: partToken.SupplierHttp.Header,
		Reader: chunkReader,
	})
	req, err := http.NewRequest("PUT", ct.URL, ct.Reader)
	if err != nil {
		logger.CtxLog(c).Errorf("upload part, fail to create request: %v", err)
		return
	}
	req.ContentLength = int64(len(chunk))
	for k, v := range ct.Header {
		req.Header.Set(k, v)
	}
	ct.Client.Timeout = time.Minute * 3
	resp, err := ct.Client.Do(req)
	if err != nil {
		logger.CtxLog(c).Errorf("upload part, err: %v", err)
		return
	}
	defer resp.Body.Close()
	data, dErr := io.ReadAll(resp.Body)
	if dErr != nil {
		logger.CtxLog(c).Errorf("upload part, fail to read body: %v", dErr)
		return res, dErr
	}
	if resp.StatusCode != http.StatusOK {
		logger.CtxLog(c).Errorf("upload part, code: %d, err: %s", resp.StatusCode, string(data))
		return res, fmt.Errorf("upload part, code: %d, err: %s", resp.StatusCode, string(data))
	}
	res.PartNumber = partToken.PartNumber
	res.Etag = resp.Header.Get("ETag")
	logger.CtxLog(c).Infof("succeeded to upload part, path: %s, res: %s", strings.Split(partToken.SupplierHttp.URL, "?")[0], ucmd.ToJsonStrIgnoreErr(res))
	return
}

// CompleteMultipart 完成分片上传任务
func (f *FMS) CompleteMultipart(c *gin.Context, fileKey, uploadId string, partList []MultipartPart) error {
	requestBody := map[string]interface{}{
		"bucket_key":    f.PubBucketKey,
		"client_id":     f.ClientId,
		"client_secret": f.ClientSecret,
		"file_key":      fileKey,
		"upload_id":     uploadId,
		"part_list":     partList,
	}
	// 国内使用获取预签名链接类型token
	if ucmd.GetArea() != um.Europe {
		requestBody["token_type"] = "presign"
	}
	ts := time.Now().Unix()
	path := "/service/file/upload/completeMultipartUpload"
	sign := ucmd.Sign{
		Timestamp:   ts,
		AppId:       f.AppId,
		AppSecret:   f.AppSecret,
		ContentType: f.Header["Content-Type"],
		Method:      "POST",
		Path:        path,
		URLParams:   map[string]interface{}{"hash_type": "sha256"},
		BodyParams:  requestBody,
	}
	sn := sign.Generate()
	if sn == "" {
		logger.CtxLog(c).Errorf("complete multipart, err: `sign` is empty")
		return fmt.Errorf("complete multipart, err: `sign` is empty")
	}

	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:    fmt.Sprintf("%s%s?app_id=%s&sign=%s&timestamp=%d&hash_type=sha256", f.URL, path, f.AppId, sn, ts),
		Method: "POST",
		Header: map[string]string{
			"Content-Type": f.Header["Content-Type"],
		},
		RequestBody: requestBody,
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		logger.CtxLog(c).Errorf("complete multipart, err: %v", err)
		return err
	}
	defer body.Close()
	data, dErr := io.ReadAll(body)
	if dErr != nil {
		logger.CtxLog(c).Errorf("complete multipart, err: %s", dErr)
		return dErr
	}
	if statusCode != http.StatusOK {
		logger.CtxLog(c).Errorf("complete multipart, err: %s", string(data))
		return fmt.Errorf("complete multipart, err: %s", string(data))
	}
	response := make(map[string]interface{})
	if err = json.Unmarshal(data, &response); err != nil {
		logger.CtxLog(c).Errorf("complete multipart, fail to unmarshal response, err: %v", err)
		return err
	}
	if response["result_code"] != "success" {
		logger.CtxLog(c).Errorf("complete multipart, err: %v", string(data))
		return fmt.Errorf("complete multipart, err: %v", string(data))
	}
	logger.CtxLog(c).Infof("succeeded to complete multipart, file_key: %s, upload_id: %s", fileKey, uploadId)
	return nil
}
