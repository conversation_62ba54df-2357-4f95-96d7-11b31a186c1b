package service

import (
	"encoding/json"
	"fmt"
	"time"

	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"

	"git.nevint.com/welkin2/welkin-backend/pb"
)

type ProtoBuffer struct {
	logger *zap.SugaredLogger
}

func (p *ProtoBuffer) EncodeRemoteOperationMessage(requestId, key string, body interface{}) ([]byte, error) {
	payload, err := json.Marshal([]map[string]interface{}{
		{
			"request_id": requestId,
			"key": key,
			"data": body,
		},
	})
	if err != nil {
		return nil, fmt.Errorf("marshal payload failed, err: %v", err)
	}
	message := &pb.Message{
		Version: proto.Int32(1),
		PublishTs: proto.Int64(time.Now().UnixMilli()),
		Ttl:     proto.Int64(100),
		SubType: proto.String("welkin_scanario_1"),
		Type:    pb.Message_CONTROL_COMMAND.Enum(),
		Params:  []*pb.Message_ParamType{
			{Key: proto.String("RemoteOperation"), Value: payload},
		},
	}
	return proto.Marshal(message)
}
