package service

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"strings"
	"time"

	"github.com/avast/retry-go"
	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	"git.nevint.com/golang-libs/common-utils/larkservice"
	"git.nevint.com/golang-libs/common-utils/lock"
	um "git.nevint.com/golang-libs/common-utils/model"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	domain_device "git.nevint.com/welkin2/welkin-backend/domain/device"
	domain_image "git.nevint.com/welkin2/welkin-backend/domain/image"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"git.nevint.com/welkin2/welkin-backend/model"
	"git.nevint.com/welkin2/welkin-backend/util"
)

type ImageBuffer struct {
	FMS
	OSS
	MongoClient *client.MongoClient
	GRPCClient  *client.GRPCClient

	DeviceId       string
	Buffer         *bytes.Buffer
	ImageData      umw.MongoImageInfo
	Logger         *zap.SugaredLogger
	PromCollectors map[string]prometheus.Collector
	Ctx            *gin.Context
}

func (ib *ImageBuffer) UploadOneImage(area, lang, project, appType string, ts int64) error {
	// 给oss用的告警图片单独上传到一个路径
	go func() {
		defer ucmd.RecoverPanic()
		err := ib.uploadOneImage4OssAlarmUse(area, project)
		if err != nil {
			log.CtxLog(ib.Ctx).Errorf("uploadOneImage4OssAlarmUse image to fms, err: %s", err.Error())
		}
	}()
	date := time.UnixMilli(ts)
	filePath := fmt.Sprintf("%s/%s/%d", ib.ImageData.DeviceId, strings.Split(util.DecodeTime(date), " ")[0], date.Hour())
	if appType == "internal" {
		if ib.ImageData.Abnormal {
			filePath = fmt.Sprintf("failure/%s", filePath)
		} else {
			imgTypeName := strings.Split(umw.ImageTypeDescriptionMap[util.GetLang(area, lang)][ib.ImageData.ImageType], " ")[0]
			if imgTypeName == "-" {
				imgTypeName = "default"
			}
			filePath = fmt.Sprintf("%s/%s", imgTypeName, filePath)
		}
	}

	fileDir := fmt.Sprintf("/image/%s/%s/%s/", appType, project, filePath)
	res, err := ib.FMS.GetFileUploadToken(fileDir, ib.ImageData.ImageName, model.IMAGE, ib.Buffer.String(), area)
	if err != nil {
		log.CtxLog(ib.Ctx).Errorf("upload %s image to fms, err: %s, fileDir: %s", appType, err.Error(), fileDir)
		return err
	}
	rd := res.ResultData
	rd.SupplierHttp.Header["Content-Type"] = "image/jpeg"
	if err = ib.FMS.UploadFile(rd.SupplierHttp.URL, rd.SupplierHttp.Header, bytes.NewReader(ib.Buffer.Bytes())); err != nil {
		log.CtxLog(ib.Ctx).Errorf("upload %s image to fms, err: %s", appType, err.Error())
		return err
	}
	for _, item := range rd.DomainInfoList {
		if item.DomainAttr.CDN {
			ib.ImageData.ImageURL = fmt.Sprintf("%s://%s/%s", item.Protocol, item.Domain, rd.FileKey)
			break
		}
	}
	if ib.ImageData.ImageURL == "" {
		log.CtxLog(ib.Ctx).Errorf("upload %s image terminated, data: %v, err: image url is empty!", appType, ib.ImageData)
		return errors.New("image url is empty")
	}
	log.CtxLog(ib.Ctx).Infof("succeeded to upload image to fms, image_url: %s", ib.ImageData.ImageURL)

	if appType == "operation" {
		log.CtxLog(ib.Ctx).Infof("start to send image to oss, image_url: %s", ib.ImageData.ImageURL)
		ib.ImageData.SendToOSSTime = time.Now().UnixMilli()
		errCode := model.ErrorCodeOfNormal
		if ib.ImageData.Abnormal {
			errCode = model.ErrorCodeOfFailure
		}
		go ib.OSS.SendImage(project, ib.ImageData.DeviceId, ib.ImageData.ServiceId,
			ib.ImageData.ImageURL, ib.ImageData.BatteryId, ib.ImageData.ImageType, errCode)
	}

	ib.ImageData.WriteDBTime = time.Now().UnixMilli()
	dbName := fmt.Sprintf("%s-%s", umw.ImageInfo, ucmd.RenameProjectDB(project))
	indexOptions := []client.IndexOption{
		{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 540 * 24 * 3600},
		{Name: "service_id", Fields: bson.D{{"service_id", 1}}},
		{Name: "image_gen_time", Fields: bson.D{{"image_gen_time", 1}}},
	}
	err = ib.MongoClient.NewMongoEntry(bson.D{
		bson.E{Key: "service_id", Value: ib.ImageData.ServiceId},
		bson.E{Key: "image_gen_time", Value: ib.ImageData.ImageGenTime},
		bson.E{Key: "image_type", Value: ib.ImageData.ImageType},
		bson.E{Key: "abnormal", Value: ib.ImageData.Abnormal},
		bson.E{Key: "camera_type", Value: ib.ImageData.CameraType},
	}).ReplaceOne(dbName, ib.DeviceId, ib.ImageData, true, indexOptions...)
	if err != nil {
		log.CtxLog(ib.Ctx).Errorf("insert %s image info to mongo, err: %v", appType, err)
		return err
	}
	log.CtxLog(ib.Ctx).Infof("succeeded to upload %s image, service_id: %s, image_name: %s, image_upload_time: %d",
		appType, ib.ImageData.ServiceId, ib.ImageData.ImageName, ib.ImageData.ImageUploadTime)

	if ib.ImageData.BatteryId != "" && ib.ImageData.ImageType > 0 && ib.ImageData.ImageType < 5 {
		// 上下表面图片：1,2,3,4
		go ib.UpdateBatteryRecordWithImageScope()
	}
	// cc算法触发
	if ib.ImageData.ImageType == 29 && ib.ImageData.CameraType != "" {
		// 更新摄像头管理照片数据
		if err = ib.UpdateCameraData(project); err != nil {
			log.CtxLog(ib.Ctx).Errorf("fail to update cc camera data: %v, camera: %s", err, ib.ImageData.CameraType)
			return err
		}
		// 运行cc算法
		go ib.CalculateCCImageV2(project)
		// mpc手动上传的cc照片，需要进行验收
		return ib.VerifyCamera(project)
	}
	return err
}

// 约定好路径规则上传图片 给oss告警用
// 规则: {address}/welkin/image/alarm2oss/{project}/{device_id}/{file_name}
func (ib *ImageBuffer) uploadOneImage4OssAlarmUse(area, project string) error {
	// SAPA失败告警 单独传照片到指定约定路径
	if !(ib.ImageData.ImageType == 33 && ib.ImageData.Abnormal == true) {
		return nil
	}
	fileDir := fmt.Sprintf("/image/alarm2oss/%v/%v/", project, ib.DeviceId)
	res, err := ib.FMS.GetFileUploadToken(fileDir, ib.ImageData.ImageName, model.IMAGE, ib.Buffer.String(), area)
	if err != nil {
		log.CtxLog(ib.Ctx).Errorf("upload image to fms, err: %s, fileDir: %s", err.Error(), fileDir)
		return err
	}
	rd := res.ResultData
	rd.SupplierHttp.Header["Content-Type"] = "image/jpeg"
	if err = ib.FMS.UploadFile(rd.SupplierHttp.URL, rd.SupplierHttp.Header, bytes.NewReader(ib.Buffer.Bytes())); err != nil {
		log.CtxLog(ib.Ctx).Errorf("upload image to fms, err: %s", err.Error())
		return err
	}
	for _, item := range rd.DomainInfoList {
		if item.DomainAttr.CDN {
			ib.ImageData.ImageURL = fmt.Sprintf("%s://%s/%s", item.Protocol, item.Domain, rd.FileKey)
			break
		}
	}
	if ib.ImageData.ImageURL == "" {
		log.CtxLog(ib.Ctx).Errorf("upload image terminated, data: %v, err: image url is empty!", ib.ImageData)
		return errors.New("image url is empty")
	}
	log.CtxLog(ib.Ctx).Infof("succeeded to upload image to fms for oss alarm use, image_url: %s", ib.ImageData.ImageURL)

	return nil
}

func (ib *ImageBuffer) VerifyCamera(project string) error {
	if ib.ImageData.ExtraData == nil {
		return nil
	}
	extraData, ok := ib.ImageData.ExtraData.(*domain_image.CCExtraData)
	if !ok {
		log.CtxLog(ib.Ctx).Errorf("fail to parse extra data, expected: domain_image.CCExtraData, get: %T", ib.ImageData.ExtraData)
		return fmt.Errorf("fail to parse extra data")
	}
	if extraData.CCType != domain_image.CCTypeMPC {
		return nil
	}
	// 将站点摄像头验收状态改为待验收
	collName := fmt.Sprintf("%s_device_camera_data", ucmd.RenameProjectDB(project))
	filter := bson.D{{"device_id", ib.DeviceId}, {"camera_type", ib.ImageData.CameraType}}
	update := bson.M{"$set": bson.M{"need_acceptance": true}}
	if err := client.GetWatcher().Mongodb().NewMongoEntry(filter).UpdateOne(domain_image.DbCamera, collName, update, false); err != nil {
		log.CtxLog(ib.Ctx).Errorf("fail to update camera data: %v, filter: %s", err, ucmd.ToJsonStrIgnoreErr(filter))
		return err
	}
	// 加锁，只有第一个抢到锁的才能发送飞书通知
	lockKey := fmt.Sprintf("ccMPC/%s/%s", project, ib.DeviceId)
	lockValue := time.Now().UnixMilli()
	conn := client.GetRedisConn()
	defer conn.Close()
	locker := lock.NewRedisLock(conn)
	interval, ok := config.Cfg.ExtraConfig[model.CameraVerifyIntervalSeconds].(float64)
	var ccWaitSeconds int
	if !ok {
		ccWaitSeconds = 3600
	} else {
		ccWaitSeconds = int(interval)
	}
	if err := locker.LockByLua(lockKey, lockValue, ccWaitSeconds); err != nil {
		log.CtxLog(ib.Ctx).Warnf("fail to LockByLua, err: %v", err)
		if !errors.Is(err, um.LockFail) {
			log.CtxLog(ib.Ctx).Errorf("fail to lock remoteConfig, err: %v", err)
			return err
		}
		return nil
	}
	log.CtxLog(ib.Ctx).Infof("cameraVerifyIntervalSeconds: %d", ccWaitSeconds)
	// mpc上点击上传cc照片，一批可能有多张分别上传，当收到第一张后，等待30秒，并把这期间所有的mpc cc照片作为同一批数据推送飞书卡片
	go func() {
		defer ucmd.RecoverPanic()
		<-time.After(time.Duration(ccWaitSeconds) * time.Second)
		filter = bson.D{
			{"image_gen_time", bson.M{"$gte": time.Now().UnixMilli() - int64(ccWaitSeconds*1000*2), "$lt": time.Now().UnixMilli()}},
			{"image_type", 29},
			{"extra_data.cc_type", domain_image.CCTypeMPC},
		}
		log.CtxLog(ib.Ctx).Infof("find cc images to send, filter: %s", ucmd.ToJsonStrIgnoreErr(filter))
		var res []umw.MongoImageInfo
		_, err := client.GetWatcher().Mongodb().NewMongoEntry(filter).FindMany(fmt.Sprintf("%s-%s", umw.ImageInfo, ucmd.RenameProjectDB(project)), ib.DeviceId, options.Find().SetSort(bson.M{"image_gen_time": 1}), &res)
		if err != nil {
			log.CtxLog(ib.Ctx).Errorf("fail to find cc images: %v, filter: %s", err, ucmd.ToJsonStrIgnoreErr(filter))
			return
		}
		if len(res) == 0 {
			log.CtxLog(ib.Ctx).Errorf("fail to find cc images: no image, filter: %s", ucmd.ToJsonStrIgnoreErr(filter))
			return
		}
		uploadTs := res[0].ImageGenTime
		deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(ib.DeviceId)
		deviceName := ""
		if found {
			deviceName = deviceInfo.Description
		}
		cameraInfo, err := client.GetWatcher().Mongodb().FindCameraInfo(project, bson.M{})
		if err != nil {
			log.CtxLog(ib.Ctx).Errorf("fail to find camera info: %v", err)
			return
		}
		cameraInfoMap := make(map[string]model.CameraInfo)
		for _, ci := range cameraInfo {
			cameraInfoMap[ci.CameraType] = ci
		}
		cameraNameMap := make(map[string]struct{})
		for _, record := range res {
			cameraData := cameraInfoMap[record.CameraType]
			cameraNameMap[cameraData.CameraName] = struct{}{}
		}
		cameraNames := make([]string, 0)
		for cn := range cameraNameMap {
			cameraNames = append(cameraNames, cn)
		}
		// 验收发起人
		d := domain_device.Device{
			Project:  project,
			DeviceId: ib.DeviceId,
		}
		userInfo, err := d.GetCurrentLoginUser(ib.Ctx)
		if err != nil {
			log.CtxLog(ib.Ctx).Errorf("fail to get current login user: %v", err)
			return
		}

		ic := larkservice.NewInfoCard()
		ic.HeaderName = fmt.Sprintf("[%s] 摄像头待验收", project)
		ic.AppendElement(larkservice.ElementKeyVal, []larkservice.KeyValMessage{
			{Key: "设备ID", Val: ib.DeviceId},
			{Key: "设备名称", Val: deviceName},
			{Key: "上传人", Val: userInfo.UserId},
			{Key: "上传时间", Val: time.UnixMilli(uploadTs).Format("2006-01-02 15:04:05")},
		})
		ic.AppendElement(larkservice.ElementDetail, []larkservice.DetailMessage{
			{Msg: fmt.Sprintf("**待验收摄像头**：%s", strings.Join(cameraNames, "、"))},
		})
		ic.AppendElement(larkservice.ElementButton, []larkservice.Button{
			{Text: "点击前往天宫平台", Url: fmt.Sprintf("%s/owl/camera-management?project=%s&need_acceptance=true", config.Cfg.Welkin.FrontendUrl, project)},
		})
		cardContent, err := ic.Build()
		if err != nil {
			log.CtxLog(ib.Ctx).Errorf("send acceptance result, make card err: %v", err)
			return
		}
		receivers := config.Cfg.AlarmReceiver[domain_image.CameraAcceptanceReceiverKey]
		err = larkservice.SendAlarmCard(cardContent, receivers)
		if err != nil {
			log.CtxLog(ib.Ctx).Errorf("send acceptance result, fail to send card: %v", err)
			return
		}
	}()
	return nil
}

func (ib *ImageBuffer) DetectAndUploadPowerSwapAbnormalImg(ctx *gin.Context, area string, ts int64) {
	// 通过rpc调用python服务
	failCnt := ib.PromCollectors[util.FailDetectAbnormalImage.ID].(*prometheus.CounterVec)
	detectResponse, err := ib.GRPCClient.DetectAbnormalImage(ctx, ib.Buffer.Bytes(), ib.ImageData.ImageSHA512)
	if err != nil {
		failCnt.WithLabelValues(ctx.Param("device_id"), fmt.Sprintf("%d", ib.ImageData.ImageType)).Inc()
		log.CtxLog(ib.Ctx).Errorf("fail to detect abnormal image, err: %v, image name: %s", err, ib.ImageData.ImageName)
		return
	}

	if detectResponse.Abnormal {
		abnormalImg, err := base64.StdEncoding.DecodeString(detectResponse.AbnormalImg)
		if err != nil {
			log.CtxLog(ib.Ctx).Errorf("decode abnormal image err: %s", err.Error())
			return
		}
		failImgSha512 := util.GenSHA512ByBytes(abnormalImg)
		if failImgSha512 != detectResponse.SHA512 {
			log.CtxLog(ib.Ctx).Errorf("sha512 check fail, expected: %s, get: %s", detectResponse.SHA512, failImgSha512)
			return
		}

		log.CtxLog(ib.Ctx).Infof("succeeded to detect image: %s, abnormal: %v", ib.ImageData.ImageName, detectResponse.Abnormal)
		ib.Buffer = bytes.NewBuffer(abnormalImg)
		ib.ImageData.ImageSize = int64(len(abnormalImg))
		ib.ImageData.ImageSHA512 = failImgSha512
		ib.ImageData.ImageName = strings.Replace(ib.ImageData.ImageName, ".", "_failure.", -1)
		ib.ImageData.Abnormal = detectResponse.Abnormal
		ib.ImageData.ImageGenTime = time.Now().UnixMilli()
		ib.ImageData.ImageUploadTime = ib.ImageData.ImageGenTime
		ib.ImageData.Date = util.ConvertTime(ib.ImageData.ImageGenTime)
		if err = ib.UploadOneImage(area, ctx.Query("lang"), umw.PowerSwap, "operation", ts); err != nil {
			log.CtxLog(ib.Ctx).Errorf("failed to upload PowerSwap abnormal image, err: %s", err.Error())
			return
		}

		log.CtxLog(ib.Ctx).Infof("succeeded to upload PowerSwap abnormal image, image name: %s", ib.ImageData.ImageName)
		return
	}
	log.CtxLog(ib.Ctx).Infof("the image is normal, no need to upload, image name: %s", ib.ImageData.ImageName)
	return
}

func (ib *ImageBuffer) UpdateBatteryRecordWithImageScope() {
	indexOptions := []client.IndexOption{
		{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 540 * 24 * 3600},
		{Name: "service_id", Fields: bson.D{{"service_id", 1}}},
		{Name: "battery_id", Fields: bson.D{{"battery_id", 1}}},
		{Name: "image_gen_time", Fields: bson.D{{"image_gen_time", 1}}},
	}
	err := ib.MongoClient.NewMongoEntry(bson.D{
		bson.E{Key: "service_id", Value: ib.ImageData.ServiceId},
		bson.E{Key: "image_gen_time", Value: ib.ImageData.ImageGenTime},
		bson.E{Key: "image_type", Value: ib.ImageData.ImageType},
		bson.E{Key: "abnormal", Value: ib.ImageData.Abnormal},
	}).ReplaceOne(umw.BatteryManagement, "image", model.AlgorithmImageInfo{
		ServiceId:    ib.ImageData.ServiceId,
		DeviceId:     ib.ImageData.DeviceId,
		BatteryId:    ib.ImageData.BatteryId,
		ImageURL:     ib.ImageData.ImageURL,
		ImageName:    ib.ImageData.ImageName,
		ImageType:    ib.ImageData.ImageType,
		Abnormal:     ib.ImageData.Abnormal,
		ImageGenTime: ib.ImageData.ImageGenTime,
		Date:         ib.ImageData.Date,
	}, true, indexOptions...)
	if err != nil {
		log.CtxLog(ib.Ctx).Errorf("update battery image info to mongo, type: %d, err: %v", ib.ImageData.ImageType, err)
	}
}

type CCResponse struct {
	um.Base
	PredictResult int64 `json:"predict_result"`
	PredictType   int64 `json:"predict_type"`
}

// CalculateCCImageV2 cc算法计算
func (ib *ImageBuffer) CalculateCCImageV2(project string) {
	rawData, err := client.GetWatcher().Mongodb().NewMongoEntry(bson.D{{"camera_type", ib.ImageData.CameraType}}).GetOne("camera_management", fmt.Sprintf("%s_camera_info", ucmd.RenameProjectDB(project)))
	if err != nil {
		client.GetPrometheus().MetricCollectors[util.FailCCCalculation.ID].(*prometheus.CounterVec).WithLabelValues("", project).Inc()
		log.CtxLog(ib.Ctx).Errorf("get camera info err: %s", err.Error())
		return
	}
	var cameraInfo struct {
		CameraType string `json:"camera_type,omitempty" bson:"camera_type,omitempty"`
		Algorithm  string `json:"algorithm,omitempty" bson:"algorithm,omitempty"`
	}
	if err = bson.Unmarshal(rawData, &cameraInfo); err != nil {
		client.GetPrometheus().MetricCollectors[util.FailCCCalculation.ID].(*prometheus.CounterVec).WithLabelValues("", project).Inc()
		log.CtxLog(ib.Ctx).Errorf("fail to unmarshal camera info, err: %v", err)
		return
	}
	if cameraInfo.Algorithm == "" {
		log.CtxLog(ib.Ctx).Warnf("calculate cc, invalid algorithm, camera info: %s", ucmd.ToJsonStrIgnoreErr(cameraInfo))
		return
	}
	defer func() {
		if err != nil {
			log.CtxLog(ib.Ctx).Errorf("calculate cc unexpected error: %v, algorithm: %s, project: %s", err, cameraInfo.Algorithm, project)
			client.GetPrometheus().MetricCollectors[util.FailCCCalculation.ID].(*prometheus.CounterVec).WithLabelValues(cameraInfo.Algorithm, project).Inc()
		}
	}()

	requestBody := map[string]interface{}{
		"image_url": ib.ImageData.ImageURL,
		"algorithm": cameraInfo.Algorithm,
		"project":   project,
	}
	var resp CCResponse
	randomDelay := func(n uint, err error, config *retry.Config) time.Duration {
		var interval int64 = 10000
		base := 1000 + int64(n)*interval
		sleepDuration := rand.Int63n(interval) + base
		return time.Duration(sleepDuration) * time.Millisecond
	}
	err = retry.Do(func() error {
		ct := ucmd.NewHttpClient(ucmd.HttpClient{
			URL:    fmt.Sprintf("%s/cc/v1/cc-calculation", config.Cfg.Welkin.AlgorithmUrl),
			Method: "POST",
			Header: map[string]string{
				"Content-Type": "application/json;charset=utf-8",
			},
			RequestBody: requestBody,
		})
		body, statusCode, retryErr := ct.Do()
		if retryErr != nil {
			log.CtxLog(ib.Ctx).Errorf("fail to request cc calculation, err: %v, url: %s, requst: %s", retryErr, ct.URL, ucmd.ToJsonStrIgnoreErr(requestBody))
			return retryErr
		}
		defer body.Close()
		data, retryErr := io.ReadAll(body)
		if retryErr != nil {
			log.CtxLog(ib.Ctx).Errorf("fail to read body cc calculation, err: %v, url: %s, requst: %s, response: %s", retryErr, ct.URL, ucmd.ToJsonStrIgnoreErr(requestBody), string(data))
			return retryErr
		}
		if statusCode != http.StatusOK {
			retryErr = fmt.Errorf("fail to request cc calculation, status code: %d, url: %s, requst: %s, response: %s", statusCode, ct.URL, ucmd.ToJsonStrIgnoreErr(requestBody), string(data))
			log.CtxLog(ib.Ctx).Error(retryErr)
			return retryErr
		}

		if retryErr = json.Unmarshal(data, &resp); err != nil {
			log.CtxLog(ib.Ctx).Errorf("fail to unmarshal cc response, err: %v, url: %s, requst: %s, response: %s", retryErr, ct.URL, ucmd.ToJsonStrIgnoreErr(requestBody), string(data))
			return retryErr
		}
		if resp.ErrCode != 0 {
			log.CtxLog(ib.Ctx).Warnf("cc calculation error code not 0, resp: %s, url: %s, requst: %s, response: %s", ucmd.ToJsonStrIgnoreErr(resp), ct.URL, ucmd.ToJsonStrIgnoreErr(requestBody), string(data))
		}
		return nil
	}, []retry.Option{
		retry.DelayType(randomDelay),
		retry.Attempts(3),
		retry.LastErrorOnly(true),
	}...)
	if err != nil {
		return
	}

	// 算法结果predict_result需要加1，来适配旧逻辑
	resp.PredictResult += 1
	// 更新算法结果
	filter := bson.D{
		{"device_id", ib.DeviceId},
		{"camera_type", ib.ImageData.CameraType},
	}
	update := bson.M{"$set": bson.M{
		"device_id":      ib.DeviceId,
		"camera_type":    ib.ImageData.CameraType,
		"has_image":      true,
		"predict_type":   resp.PredictType,
		"predict_result": resp.PredictResult,
		"predict_ts":     time.Now().UnixMilli(),
	}}
	err = client.GetWatcher().Mongodb().NewMongoEntry(filter).UpdateOne("camera_management", fmt.Sprintf("%s_device_camera_data", ucmd.RenameProjectDB(project)), update, true)
	if err != nil {
		log.CtxLog(ib.Ctx).Errorf("fail to store cc result, request: %s, device_id: %s, predict_result: %d, err: %v", ucmd.ToJsonStrIgnoreErr(requestBody), ib.DeviceId, resp.PredictResult, err)
		return
	}
	if err = updateDeviceSensorHealth(ib.Ctx, cameraInfo.Algorithm, cameraInfo.CameraType, resp.PredictResult, time.Now().UnixMilli(), ib.DeviceId, project); err != nil {
		log.CtxLog(ib.Ctx).Errorf("fail to update device sensor health: %v, request: %s, device_id: %s, project: %s, predict_result: %d, algorithm: %s", err, ucmd.ToJsonStrIgnoreErr(requestBody), ib.DeviceId, project, resp.PredictResult, cameraInfo.Algorithm)
		return
	}
}

// 将cc算法得出的摄像头健康度信息写入数据库
func updateDeviceSensorHealth(c *gin.Context, algorithm, cameraType string, predictResult int64, timestamp int64, deviceId, project string) (err error) {
	log.CtxLog(c).Infof("algorithm: %s, cameraType: %s, predictResult: %d, timestamp: %d, deviceId %s, project: %s", algorithm, cameraType, predictResult, timestamp, deviceId, project)
	switch algorithm {
	case "socc":
		// 1：绿色，2：黄色，3：红色
		if predictResult < 1 || predictResult > 3 {
			log.CtxLog(c).Warnf("invalid %s predict result: %d", cameraType, predictResult)
			return
		}
		var score float64
		if predictResult == 1 {
			score = 100
		} else if predictResult == 2 {
			score = 70
		} else if predictResult == 3 {
			score = 50
		}
		t := time.UnixMilli(timestamp)
		zeroTime := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location()).UnixMilli()
		filter := bson.D{
			{"day", zeroTime},
			{"device_id", deviceId},
		}
		update := bson.M{
			"$set": bson.M{
				"project":   project,
				"device_id": deviceId,
				"day":       zeroTime,
				"date":      t,
			},
			"$inc": bson.M{
				"sensor_data.camera_sum":  1,
				"sensor_data.total_score": score,
			},
			"$push": bson.M{
				"sensor_data.camera_data": bson.M{
					"camera_type":    cameraType,
					"predict_result": predictResult,
				},
			},
		}
		return client.GetWatcher().PLCMongodb().NewMongoEntry(filter).UpdateOne(umw.Algorithm, "health_data", update, true)
	default:
		log.CtxLog(c).Warnf("update sensor health, invalid algorithm: %s, project: %s", algorithm, project)
	}
	return nil
}

// UpdateCameraData 更新摄像头数据
func (ib *ImageBuffer) UpdateCameraData(project string) error {
	filter := bson.D{
		{"device_id", ib.DeviceId},
		{"camera_type", ib.ImageData.CameraType},
	}
	count, err := client.GetWatcher().Mongodb().NewMongoEntry(filter).Count("camera_management", fmt.Sprintf("%s_device_camera_data", ucmd.RenameProjectDB(project)))
	if err != nil {
		log.CtxLog(ib.Ctx).Errorf("fail to count camera data: %v, filter: %s", err, ucmd.ToJsonStrIgnoreErr(filter))
		return err
	}
	update := bson.M{
		"device_id":   ib.DeviceId,
		"camera_type": ib.ImageData.CameraType,
		"has_image":   true,
	}
	if count == 0 {
		update["judge_result"] = 0
		update["predict_result"] = 0
		update["in_blacklist"] = false
		update["need_acceptance"] = false
	}
	deviceInfo, found := cache.PowerSwapCache.GetSingleDevice(ib.DeviceId)
	if found && deviceInfo.Area != "" {
		update["area"] = deviceInfo.Area
	}
	err = client.GetWatcher().Mongodb().NewMongoEntry(filter).UpdateOne("camera_management", fmt.Sprintf("%s_device_camera_data", ucmd.RenameProjectDB(project)), bson.M{"$set": update}, true)
	if err != nil {
		log.CtxLog(ib.Ctx).Errorf("fail to store cc result, device_id: %s, project %s, err: %v", ib.DeviceId, project, err)
		return err
	}
	return nil
}
