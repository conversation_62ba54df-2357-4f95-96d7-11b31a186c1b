package service

import (
	"context"
	"fmt"
	"git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	ulog "git.nevint.com/golang-libs/common-utils/logger"
	"git.nevint.com/welkin2/welkin-backend/config"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"github.com/rs/xid"
	"os"
	"testing"
)

func InitProdEnv() {
	os.Setenv("APOLLO_META", "https://apollo-config-cn.nioint.com")
	os.Setenv("ENV", "prod")
	cfg := &ucfg.Config{}
	ucfg.GetConfigApollo("config.json", true, cfg, ucfg.NeedValidate(true), ucfg.ServiceName("ppd-welkin-backend"), ucfg.Cluster(ucfg.GetWelkinApolloCluster()))
	defer ulog.Final()
	cfg.Log.DirPath = "/tmp/logs"
	config.Cfg = cfg
	log.Init(cfg.Log, true)
}

func TestSendControlCommand2Device(t *testing.T) {
	InitProdEnv()
	ctx := context.Background()
	deviceId := "NPC-NIO-2fa1a09d-0cd45095"
	header := map[string]string{
		"X-User-ID":    "shawn.wu1",
		"Content-Type": "application/json",
		"X-User-Type":  "Welkin",
		"X-Request-ID": xid.New().String(),
	}
	body := map[string]interface{}{
		"ability_operates": []map[string]interface{}{
			{"ability_code": "4", // 4开始 5结束
				"ability_params": map[string]interface{}{
					"connector_id": "B23239511GNX",
					"work_mode":    "1", // 0充电 1放电
				},
			},
		},
	}
	println(cmd.ToJsonStrIgnoreErr(body))
	resp, err := SendControlCommand2Device(ctx, deviceId, header, body)
	fmt.Println(resp)
	fmt.Println(err)
}
