package service

import (
	"context"
	"fmt"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	ulog "git.nevint.com/golang-libs/common-utils/logger"
	"git.nevint.com/welkin2/welkin-backend/config"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
	larkdocx "github.com/larksuite/oapi-sdk-go/v3/service/docx/v1"
	"os"
	"testing"

	lark "github.com/larksuite/oapi-sdk-go/v3"
)

func SetEnvStg() {
	os.Setenv("APOLLO_META", "https://apollo-config-cn-stg.nioint.com")
	os.Setenv("K8S_ENV", "stg")
	os.Setenv("AREA", "China")
	os.Setenv("LOCAL_LOG", "/tmp/logs")
}

func intiTest() {
	os.Setenv("APOLLO_META", "https://apollo-config-cn-stg.nioint.com")
	os.Setenv("K8S_ENV", "stg")
	cfg := &ucfg.Config{}
	ucfg.GetConfigApollo("config.json", true, cfg, ucfg.NeedValidate(true), ucfg.ServiceName("ppd-welkin-backend"), ucfg.Cluster(ucfg.GetWelkinApolloCluster()))
	defer ulog.Final()
	cfg.Log.DirPath = "/Users/<USER>/Documents/GoProject/welkin-backend/config"
	config.Cfg = cfg
	log.Init(cfg.Log, true)
}

func TestCreateWorkflowInstance(t *testing.T) {
	intiTest()
	id, err := CreateWorkflowInstance(context.Background(), "525c6189-9317-41d2-a78c-d6d92c0528b8", "shawn.wu1", map[string]string{
		"project":      "PUS3",
		"device_id":    "ddewdwede",
		"duration":     "1",
		"device_owner": "william.shen2",
	})
	fmt.Println(id)
	fmt.Println(err)
}

func TestLarkFile(t *testing.T) {
	SetEnvStg()

	client := lark.NewClient("********************", "7qM3o9cW3BJ7o5q4nn1uJhte8O3eZmdm")
	req := larkdocx.NewListDocumentBlockReqBuilder().
		DocumentId("VIJFdgnWRoruGjxEw8RcXdhAnzc").
		PageSize(100).
		Build()
	resp, err := client.Docx.DocumentBlock.List(context.Background(), req)
	if err != nil {
		fmt.Println(err)
		return
	}
	if !resp.Success() {
		fmt.Println(resp.Code, resp.Msg, resp.RequestId())
		return
	}
	fmt.Println(resp.RequestId())
	fmt.Println(larkcore.Prettify(resp))
	fmt.Println(len(resp.Data.Items))
}
