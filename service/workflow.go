package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"git.nevint.com/welkin2/welkin-backend/config"
	"git.nevint.com/welkin2/welkin-backend/logger"
	"io/ioutil"
	"net/http"
	"time"

	"go.uber.org/zap"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	um "git.nevint.com/golang-libs/common-utils/model"
	"git.nevint.com/welkin2/welkin-backend/model"
	"git.nevint.com/welkin2/welkin-backend/util"
)

const (
	WorkflowStatusProcessing = "processing"
	WorkflowStatusSuccess    = "success"
	WorkflowStatusDeny       = "deny"
	WorkflowStatusTimeout    = "timeout"
	WorkflowStatusRevoke     = "revoke"
	WorkflowStatusError      = "error"
	WorkflowStatusCancel     = "cancel"
)

type WF struct {
	URL       string
	AppId     string
	AppSecret string
	FlowCode  string
	Header    map[string]string
	Logger    *zap.SugaredLogger
}

func (w *WF) PublishApproval(ctx context.Context, userId string, context model.ApprovalContent) (string, error) {
	metaTable := createApprovalTableMeta(context.RequestFiles)
	requestFiles, err := json.Marshal(metaTable)
	if err != nil {
		logger.CtxLog(ctx).Errorf("failed to marsh request_files, err: %v", err)
		return "", err
	}
	requestBody := map[string]interface{}{
		"flow_code": w.FlowCode,
		"creator":   userId,
		"context": map[string]string{
			"description":   context.Description,
			"device_id":     context.DeviceId,
			"project":       context.Project,
			"reason":        context.Reason,
			"request_files": string(requestFiles),
		},
	}
	ts := time.Now().Unix()
	path := "/api/v1/instance/create"
	sign := ucmd.Sign{
		Timestamp:   ts,
		AppId:       w.AppId,
		AppSecret:   w.AppSecret,
		ContentType: w.Header["Content-Type"],
		Method:      "POST",
		Path:        path,
		URLParams:   map[string]interface{}{"hash_type": "sha256"},
		BodyParams:  requestBody,
	}
	sn := sign.Generate()
	if sn == "" {
		logger.CtxLog(ctx).Errorf("publish approval, err: `sign` is empty")
		return "", errors.New("`sign` is empty")
	}

	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:    fmt.Sprintf("%s%s?app_id=%s&sign=%s&timestamp=%d&hash_type=sha256", w.URL, path, w.AppId, sn, ts),
		Method: "POST",
		Header: map[string]string{
			"Content-Type": w.Header["Content-Type"],
			"X-Domain-Id":  userId,
		},
		RequestBody: requestBody,
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		logger.CtxLog(ctx).Errorf("publish approval, err: %v body:%v uid:%v", err, body, userId)
		return "", err
	}
	defer body.Close()
	data, dErr := ioutil.ReadAll(body)
	if dErr != nil {
		logger.CtxLog(ctx).Errorf("publish approval, err: %s body:%v uid:%v", dErr, body, userId)
		return "", dErr
	}
	if statusCode != http.StatusOK {
		logger.CtxLog(ctx).Errorf("publish approval, err: %s body:%v uid:%v", string(data), body, userId)
		return "", errors.New(string(data))
	}
	var response model.CreateApprovalResponse
	if err = json.Unmarshal(data, &response); err != nil {
		logger.CtxLog(ctx).Errorf("publish approval, err: %v", err)
		return "", err
	}
	if response.ResultCode != "success" {
		logger.CtxLog(ctx).Errorf("publish approval, err: %v", string(data))
		return "", errors.New(string(data))
	}
	logger.CtxLog(ctx).Infof("succeeded to publish approval, flow_instance_id: %s", response.ResultData.FlowInstanceId)
	return response.ResultData.FlowInstanceId, nil
}

func createApprovalTableMeta(requestFiles []model.RequestFile) map[string]interface{} {
	column1, column2 := "文件上传时间", "文件路径"
	if ucmd.GetArea() == um.Europe {
		column1, column2 = "File Upload Time", "File Path"
	}
	metaTable := map[string]interface{}{
		"columns": []string{column1, column2},
	}

	dataSource := make([]interface{}, 0)
	for _, f := range requestFiles {
		dataSource = append(dataSource, []string{util.DecodeTime(time.UnixMilli(f.FileGenTime)), f.FilePath})
	}
	metaTable["dataSource"] = dataSource

	return metaTable
}

func CreateWorkflowInstance(ctx context.Context, flowCode, userId string, context map[string]string) (string, error) {
	requestBody := map[string]interface{}{
		"flow_code": flowCode,
		"creator":   userId,
		"context":   context,
	}
	ts := time.Now().Unix()
	path := "/api/v1/instance/create"
	sign := ucmd.Sign{
		Timestamp:   ts,
		AppId:       config.Cfg.Sentry.AppId,
		AppSecret:   config.Cfg.Sentry.AppSecret,
		ContentType: "application/json;charset=UTF-8",
		Method:      "POST",
		Path:        path,
		URLParams:   map[string]interface{}{"hash_type": "sha256"},
		BodyParams:  requestBody,
	}
	sn := sign.Generate()
	if sn == "" {
		logger.CtxLog(ctx).Errorf("CreateWorkflowInstance err: `sign` is empty")
		return "", errors.New("`sign` is empty")
	}
	logger.CtxLog(ctx).Infof("CreateWorkflowInstance http. body:%v", requestBody)
	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:    fmt.Sprintf("%s%s?app_id=%s&sign=%s&timestamp=%d&hash_type=sha256", config.Cfg.Workflow.Url, path, config.Cfg.Sentry.AppId, sn, ts),
		Method: "POST",
		Header: map[string]string{
			"Content-Type": "application/json;charset=UTF-8",
			"X-Domain-Id":  userId,
		},
		RequestBody: requestBody,
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		logger.CtxLog(ctx).Errorf("CreateWorkflowInstance, err: %v", err)
		return "", err
	}
	defer body.Close()
	data, dErr := ioutil.ReadAll(body)
	if dErr != nil {
		logger.CtxLog(ctx).Errorf("CreateWorkflowInstance, err: %s", dErr)
		return "", dErr
	}
	if statusCode != http.StatusOK {
		logger.CtxLog(ctx).Errorf("CreateWorkflowInstance, err: %s", string(data))
		return "", errors.New(string(data))
	}
	var response model.CreateApprovalResponse
	if err = json.Unmarshal(data, &response); err != nil {
		logger.CtxLog(ctx).Errorf("CreateWorkflowInstance, err: %v", err)
		return "", err
	}
	if response.ResultCode != "success" {
		logger.CtxLog(ctx).Errorf("publish approval, err: %v", string(data))
		return "", errors.New(string(data))
	}
	logger.CtxLog(ctx).Infof("succeeded to CreateWorkflowInstance, flow_instance_id: %s", response.ResultData.FlowInstanceId)
	return response.ResultData.FlowInstanceId, nil
}

func QueryWorkflowInstance(ctx context.Context, flowId, userId string) (QueryWorkflowInstanceResp, error) {
	var response QueryWorkflowInstanceResp
	ts := time.Now().Unix()
	path := fmt.Sprintf("/api/v1/instance/%v", flowId)
	requestBody := map[string]interface{}{}
	sign := ucmd.Sign{
		Timestamp:   ts,
		AppId:       config.Cfg.Sentry.AppId,
		AppSecret:   config.Cfg.Sentry.AppSecret,
		ContentType: "application/json;charset=UTF-8",
		Method:      "GET",
		Path:        path,
		BodyParams:  requestBody,
		URLParams:   map[string]interface{}{"hash_type": "sha256"},
	}
	sn := sign.Generate()
	if sn == "" {
		logger.CtxLog(ctx).Errorf("CreateWorkflowInstance err: `sign` is empty")
		return response, errors.New("`sign` is empty")
	}
	headers := map[string]string{
		"Content-Type": "application/json;charset=UTF-8",
		"X-Domain-Id":  userId,
	}
	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:         fmt.Sprintf("%s%s?app_id=%s&sign=%s&timestamp=%d&hash_type=sha256", config.Cfg.Workflow.Url, path, config.Cfg.Sentry.AppId, sn, ts),
		Method:      "GET",
		Header:      headers,
		RequestBody: requestBody,
	})
	logger.CtxLog(ctx).Infof("QueryWorkflowInstance http.flowId:%v header:%v", flowId, headers)
	body, statusCode, err := ct.Do()
	if err != nil {
		logger.CtxLog(ctx).Errorf("CreateWorkflowInstance, err: %v", err)
		return response, err
	}
	defer body.Close()
	data, dErr := ioutil.ReadAll(body)
	if dErr != nil {
		logger.CtxLog(ctx).Errorf("CreateWorkflowInstance, err: %s", dErr)
		return response, dErr
	}
	if statusCode != http.StatusOK {
		logger.CtxLog(ctx).Errorf("CreateWorkflowInstance, err: %s", string(data))
		return response, errors.New(string(data))
	}
	logger.CtxLog(ctx).Infof("workflow resp:%v", string(data))
	if err = json.Unmarshal(data, &response); err != nil {
		logger.CtxLog(ctx).Errorf("CreateWorkflowInstance, err: %v", err)
		return response, err
	}
	if response.ResultCode != "success" {
		logger.CtxLog(ctx).Errorf("publish approval, err: %v", string(data))
		return response, errors.New(string(data))
	}
	logger.CtxLog(ctx).Infof("succeeded to QueryWorkflowInstance, resp: %v", ucmd.ToJsonStrIgnoreErr(response))
	return response, nil
}

type QueryWorkflowInstanceResp struct {
	RequestId  string `json:"request_id"`
	ResultCode string `json:"result_code"`
	Data       struct {
		FlowCode        string `json:"flow_code"`
		FlowName        string `json:"flow_name"`
		FlowDescription string `json:"flow_description"`
		ViewTemplate    struct {
			Form []interface{} `json:"form"`
			List []struct {
				Name     string `json:"name"`
				Type     string `json:"type"`
				Field    string `json:"field"`
				Children []struct {
					Name     string `json:"name"`
					Type     string `json:"type"`
					Field    string `json:"field"`
					TypeCode string `json:"type_code"`
				} `json:"children"`
			} `json:"list"`
			Detail []struct {
				Type       string `json:"type"`
				Properties struct {
					Project struct {
						Name       string `json:"name"`
						Type       string `json:"type"`
						Title      string `json:"title"`
						Properties struct {
						} `json:"properties"`
						XComponent string `json:"x-component"`
						XDecorator string `json:"x-decorator"`
					} `json:"project"`
					Duration struct {
						Name       string `json:"name"`
						Type       string `json:"type"`
						Title      string `json:"title"`
						Properties struct {
						} `json:"properties"`
						XComponent string `json:"x-component"`
						XDecorator string `json:"x-decorator"`
					} `json:"duration"`
					DeviceId struct {
						Name       string `json:"name"`
						Type       string `json:"type"`
						Title      string `json:"title"`
						Properties struct {
						} `json:"properties"`
						XComponent string `json:"x-component"`
						XDecorator string `json:"x-decorator"`
					} `json:"device_id"`
					DeviceOwner struct {
						Name       string `json:"name"`
						Type       string `json:"type"`
						Title      string `json:"title"`
						Properties struct {
						} `json:"properties"`
						XComponent string `json:"x-component"`
						XDecorator string `json:"x-decorator"`
					} `json:"device_owner"`
					DeviceManager struct {
						Name       string `json:"name"`
						Type       string `json:"type"`
						Title      string `json:"title"`
						Properties struct {
						} `json:"properties"`
						XComponent string `json:"x-component"`
						XDecorator string `json:"x-decorator"`
					} `json:"device_manager"`
					OnlineOperator struct {
						Name       string `json:"name"`
						Type       string `json:"type"`
						Title      string `json:"title"`
						Properties struct {
						} `json:"properties"`
						XComponent string `json:"x-component"`
						XDecorator string `json:"x-decorator"`
					} `json:"online_operator"`
				} `json:"properties"`
			} `json:"detail"`
			Notice []struct {
				Name     string `json:"name"`
				Type     string `json:"type"`
				Field    string `json:"field"`
				TypeCode string `json:"type_code"`
			} `json:"notice"`
		} `json:"view_template"`
		FlowTemplate struct {
			Flow []struct {
				Pa     string   `json:"_pa"`
				Name   string   `json:"name"`
				Next   []string `json:"next"`
				Type   string   `json:"type,omitempty"`
				Kind   string   `json:"_kind,omitempty"`
				Config struct {
					Error  []interface{} `json:"error,omitempty"`
					Finish []struct {
						Action string `json:"action"`
						Config struct {
							Path    string        `json:"path"`
							Query   []interface{} `json:"query"`
							Method  string        `json:"method"`
							Headers []interface{} `json:"headers"`
							Timeout int           `json:"timeout"`
						} `json:"config"`
					} `json:"finish,omitempty"`
				} `json:"config,omitempty"`
				Operator []struct {
					Type     int    `json:"type"`
					Field    string `json:"field"`
					GroupId  int    `json:"group_id"`
					TypeName string `json:"type_name"`
				} `json:"operator,omitempty"`
				OtherConfig struct {
					Context struct {
						Project struct {
							Visible  bool `json:"visible"`
							Editable bool `json:"editable"`
						} `json:"project"`
						Duration struct {
							Visible  bool `json:"visible"`
							Editable bool `json:"editable"`
						} `json:"duration"`
						DeviceId struct {
							Visible  bool `json:"visible"`
							Editable bool `json:"editable"`
						} `json:"device_id"`
						DeviceOwner struct {
							Visible  bool `json:"visible"`
							Editable bool `json:"editable"`
						} `json:"device_owner"`
						DeviceManager struct {
							Visible  bool `json:"visible"`
							Editable bool `json:"editable"`
						} `json:"device_manager"`
						OnlineOperator struct {
							Visible  bool `json:"visible"`
							Editable bool `json:"editable"`
						} `json:"online_operator"`
					} `json:"context,omitempty"`
					Nopeople struct {
						Enable bool `json:"enable"`
					} `json:"nopeople,omitempty"`
					MultiDeny   string `json:"multi_deny,omitempty"`
					ApprovalBtn struct {
					} `json:"approval_btn,omitempty"`
					MultiAccept  string `json:"multi_accept,omitempty"`
					Notification struct {
						EmailEnable  bool `json:"email_enable"`
						FeishuEnable bool `json:"feishu_enable"`
					} `json:"notification,omitempty"`
					DisplayInH5            string `json:"display_in_h5,omitempty"`
					MultiApproval          string `json:"multi_approval,omitempty"`
					AssignRuleWhenRuleInOr string `json:"assign_rule_when_rule_in_or,omitempty"`
				} `json:"other_config,omitempty"`
				FlowInstanceNodeRule  string `json:"flow_instance_node_rule,omitempty"`
				FlowInstanceNodeTitle string `json:"flow_instance_node_title,omitempty"`
			} `json:"flow"`
		} `json:"flow_template"`
		Assistants       []interface{} `json:"assistants"`
		FlowVersionId    string        `json:"flow_version_id"`
		IsTestForRelease bool          `json:"is_test_for_release"`
		ProjectId        string        `json:"project_id"`
		MetaDataType     int           `json:"meta_data_type"`
		IsCreatedInHome  bool          `json:"is_created_in_home"`
		Config           struct {
			Icon        string        `json:"icon"`
			Title       string        `json:"title"`
			KeyWords    []interface{} `json:"keyWords"`
			MultiDeny   string        `json:"multiDeny"`
			Description string        `json:"description"`
			JobTransfer struct {
				InstanceTransfer          bool `json:"instanceTransfer"`
				ProcessingTransfer        bool `json:"processingTransfer"`
				ProcessingForcastTransfer bool `json:"processingForcastTransfer"`
			} `json:"jobTransfer"`
			MultiAccept  string `json:"multiAccept"`
			Notification struct {
				EmailEnable         bool `json:"emailEnable"`
				FeishuEnable        bool `json:"feishuEnable"`
				EndNodeEmailEnable  bool `json:"endNodeEmailEnable"`
				EndNodeFeishuEnable bool `json:"endNodeFeishuEnable"`
			} `json:"notification"`
			ViewersType  string `json:"viewersType"`
			CreatorLimit struct {
				AllowType      string        `json:"allowType"`
				IgnoreType     string        `json:"ignoreType"`
				CreatorsAllow  []interface{} `json:"creatorsAllow"`
				CreatorsIgnore []interface{} `json:"creatorsIgnore"`
			} `json:"creatorLimit"`
			DisplayInH5     string `json:"displayInH5"`
			ProcessLevel    string `json:"processLevel"`
			BusinessOwner   string `json:"businessOwner"`
			MultiApproval   string `json:"multiApproval"`
			AllowCopyFlow   string `json:"allowCopyFlow"`
			AdvanceSettings struct {
				AutoDuplicate []interface{} `json:"autoDuplicate"`
			} `json:"advanceSettings"`
			CustomPageLink struct {
				Pc       string `json:"pc"`
				Mobile   string `json:"mobile"`
				Disabled bool   `json:"disabled"`
			} `json:"customPageLink"`
			DetailSyncType    string `json:"detailSyncType"`
			BusinessCategory  string `json:"businessCategory"`
			RerunWhenRevoke   string `json:"rerunWhenRevoke"`
			CustomContentLink struct {
				Pc       string `json:"pc"`
				Mobile   string `json:"mobile"`
				Disabled bool   `json:"disabled"`
			} `json:"customContentLink"`
			IsAllowRevocation  bool          `json:"isAllowRevocation"`
			MobileViewEnabled  bool          `json:"mobileViewEnabled"`
			NotificationConfig []interface{} `json:"notificationConfig"`
			PermissionSettings struct {
				PermissionLevel string `json:"permissionLevel"`
			} `json:"permissionSettings"`
			UploadSpecificDir struct {
			} `json:"uploadSpecificDir"`
			BatchOperateDetail  []string `json:"batchOperateDetail"`
			BatchOperateEnabled bool     `json:"batchOperateEnabled"`
			TimeoutLimitSetting struct {
				Enabled    bool `json:"enabled"`
				Autopass   bool `json:"autopass"`
				ExpireDays int  `json:"expireDays"`
			} `json:"timeoutLimitSetting"`
			IsAllowBatchApprove bool `json:"isAllowBatchApprove"`
		} `json:"config"`
		FlowInstanceId string `json:"flow_instance_id"`
		Creator        string `json:"creator"`
		Status         string `json:"status"`
		CreatedTime    int64  `json:"created_time"`
		StatusName     string `json:"status_name"`
		Context        struct {
			Project                     string `json:"project"`
			UserId                      string `json:"user_id"`
			Duration                    string `json:"duration"`
			DeviceId                    string `json:"device_id"`
			DeviceOwner                 string `json:"device_owner"`
			UpgradeRole                 string `json:"upgrade_role"`
			OriginalRole                string `json:"original_role"`
			DeviceManager               string `json:"device_manager"`
			OnlineOperator              string `json:"online_operator"`
			UpgradeRoleCn               string `json:"upgrade_role_cn"`
			OriginalRoleCn              string `json:"original_role_cn"`
			WorkflowCreator             string `json:"workflow_creator"`
			WorkflowCreatedTime         string `json:"workflow_created_time"`
			WorkflowCostCenterId        string `json:"workflow_cost_center_id"`
			WorkflowCreatorCompany      string `json:"workflow_creator_company"`
			WorkflowCreatorDepartmentCn string `json:"workflow_creator_department_cn"`
			WorkflowCreatorDepartmentEn string `json:"workflow_creator_department_en"`
		} `json:"context"`
		MetaData []struct {
			Name     string `json:"name"`
			Type     string `json:"type"`
			Field    string `json:"field"`
			TypeCode string `json:"type_code"`
		} `json:"meta_data"`
		Nodes []struct {
			Id                    int      `json:"id"`
			FlowInstanceId        string   `json:"flow_instance_id"`
			FlowInstanceNodeId    string   `json:"flow_instance_node_id"`
			Status                string   `json:"status"`
			FlowTemplateNodeId    string   `json:"flow_template_node_id"`
			FlowInstanceNodeType  string   `json:"flow_instance_node_type"`
			StartTime             int64    `json:"start_time"`
			EndTime               int64    `json:"end_time"`
			FlowInstanceNodeTitle string   `json:"flow_instance_node_title,omitempty"`
			Next                  []string `json:"next"`
			Tasks                 []struct {
				TaskId    string  `json:"task_id,omitempty"`
				Status    string  `json:"status,omitempty"`
				TaskType  string  `json:"task_type,omitempty"`
				StartTime int64   `json:"start_time,omitempty"`
				EndTime   int64   `json:"end_time,omitempty"`
				Memo      *string `json:"memo,omitempty"`
				ExtraInfo struct {
					ApproveAttachments []interface{} `json:"approve_attachments,omitempty"`
					Comment            string        `json:"comment,omitempty"`
					QueryRule          string        `json:"query_rule,omitempty"`
				} `json:"extra_info"`
				Operator struct {
					WorkerUserId string `json:"worker_user_id,omitempty"`
					UserName     string `json:"user_name,omitempty"`
					EnglishName  string `json:"english_name,omitempty"`
					NameEn       string `json:"name_en,omitempty"`
					GroupId      string `json:"group_id,omitempty"`
				} `json:"operator"`
				ActureOperator struct {
					WorkerUserId string `json:"worker_user_id"`
					UserName     string `json:"user_name"`
					EnglishName  string `json:"english_name,omitempty"`
					NameEn       string `json:"name_en,omitempty"`
					GroupId      string `json:"group_id,omitempty"`
				} `json:"acture_operator,omitempty"`
			} `json:"tasks"`
			VersionId int `json:"version_id"`
			ExtraInfo struct {
			} `json:"extra_info"`
			FlowInstanceNodeName string `json:"flow_instance_node_name"`
			FlowInstanceNodeRule string `json:"flow_instance_node_rule,omitempty"`
		} `json:"nodes"`
		Version   int `json:"version"`
		ExtraInfo struct {
			TestConfig struct {
				CreatedInHome bool `json:"created_in_home"`
			} `json:"test_config"`
			BizId    string `json:"biz_id"`
			TimeOut  int64  `json:"time_out"`
			FlowCode string `json:"flow_code"`
			FlowName string `json:"flow_name"`
			Language struct {
				ZhCN struct {
					Flow struct {
						EndTitle                 string `json:"end.title"`
						FlowName                 string `json:"flow_name"`
						FlowDescription          string `json:"flow_description"`
						Fqop60PZLJfNDzqJi2PTitle string `json:"_fqop60_p_z_l_jf_n_dzq_ji2_p.title"`
					} `json:"flow"`
					Public struct {
						LabelAsk                         string `json:"label.ask"`
						ButtonAsk                        string `json:"button.ask"`
						LabelTips                        string `json:"label.tips"`
						ButtonDeny                       string `json:"button.deny"`
						ButtonMore                       string `json:"button.more"`
						ButtonReply                      string `json:"button.reply"`
						LabelLaunch                      string `json:"label.launch"`
						ButtonAccept                     string `json:"button.accept"`
						ButtonCancel                     string `json:"button.cancel"`
						ButtonRemind                     string `json:"button.remind"`
						ButtonRevoke                     string `json:"button.revoke"`
						ButtonSubmit                     string `json:"button.submit"`
						ButtonUpload                     string `json:"button.upload"`
						LabelCompany                     string `json:"label.company"`
						LabelSponsor                     string `json:"label.sponsor"`
						LabelSummary                     string `json:"label.summary"`
						ButtonConfirm                    string `json:"button.confirm"`
						ButtonAddSign                    string `json:"button.add_sign"`
						ButtonTransfer                   string `json:"button.transfer"`
						LabelFlowDeny                    string `json:"label.flow_deny"`
						MessageSuccess                   string `json:"message.success"`
						LabelApplyTime                   string `json:"label.apply_time"`
						LabelAttachment                  string `json:"label.attachment"`
						LabelDepartment                  string `json:"label.department"`
						LabelFlowError                   string `json:"label.flow_error"`
						LabelNodeAsked                   string `json:"label.node_asked"`
						LabelAddSignTo                   string `json:"label.add_sign_to"`
						LabelApproveEnd                  string `json:"label.approve_end"`
						LabelCostCenter                  string `json:"label.cost_center"`
						LabelFlowRevoke                  string `json:"label.flow_revoke"`
						LabelInstanceId                  string `json:"label.instance_id"`
						LabelNodeDenied                  string `json:"label.node_denied"`
						LabelOrNodeTip                   string `json:"label.or_node_tip"`
						LabelTransferTo                  string `json:"label.transfer_to"`
						MessagePlsInput                  string `json:"message.pls_input"`
						LabelAndNodeTip                  string `json:"label.and_node_tip"`
						LabelFlowContent                 string `json:"label.flow_content"`
						LabelFlowInvalid                 string `json:"label.flow_invalid"`
						LabelFlowSuccess                 string `json:"label.flow_success"`
						LabelFlowTimeout                 string `json:"label.flow_timeout"`
						LabelLaunchAgain                 string `json:"label.launch_again"`
						LabelNodePending                 string `json:"label.node_pending"`
						LabelNodeReplyed                 string `json:"label.node_replyed"`
						LabelNodeTimeout                 string `json:"label.node_timeout"`
						LabelReasonTempl                 string `json:"label.reason_templ"`
						MessagePlsChoose                 string `json:"message.pls_choose"`
						LabelAuthorizedBy                string `json:"label.authorized_by"`
						LabelFlowProgress                string `json:"label.flow_progress"`
						LabelNodeAccepted                string `json:"label.node_accepted"`
						LabelNodeInserted                string `json:"label.node_inserted"`
						LabelNodeLaunched                string `json:"label.node_launched"`
						MessageRemindSent                string `json:"message.remind_sent"`
						LabelApproveDetail               string `json:"label.approve_detail"`
						MessageNextApprove               string `json:"message.next_approve"`
						LabelFlowProcessing              string `json:"label.flow_processing"`
						MessageSearchPeople              string `json:"message.search_people"`
						LabelConfirmToTempl              string `json:"label.confirm_to_templ"`
						LabelApprovalComments            string `json:"label.approval_comments"`
						MessageSearchNoresult            string `json:"message.search_noresult"`
						MessageGetNextApprove            string `json:"message.get_next_approve"`
						MessageRemindTooOften            string `json:"message.remind_too_often"`
						LabelNodeLaunchedAgain           string `json:"label.node_launched_again"`
						MessageFileUploadError           string `json:"message.file_upload_error"`
						LabelAuthorizedBySibmit          string `json:"label.authorized_by_sibmit"`
						MessageChooseCostCenter          string `json:"message.choose_cost_center"`
						MessageStillSomeApprove          string `json:"message.still_some_approve"`
						LabelAuthorizedByApproval        string `json:"label.authorized_by_approval"`
						MessageAllApproveComplete        string `json:"message.all_approve_complete"`
						MessageAttachmentOpenOnPc        string `json:"message.attachment_open_on_pc"`
						MessageAttachmentSizeLimit       string `json:"message.attachment_size_limit"`
						MessageAttachmentUploadLimit     string `json:"message.attachment_upload_limit"`
						MessageAttachmentNameLengthLimit string `json:"message.attachment_name_length_limit"`
						MessageConfirmToRemoveAttachment string `json:"message.confirm_to_remove_attachment"`
					} `json:"public"`
					Disabled  bool `json:"disabled"`
					Completed bool `json:"completed"`
					MetaData  struct {
						Project        string `json:"project"`
						Duration       string `json:"duration"`
						DeviceId       string `json:"device_id"`
						DeviceOwner    string `json:"device_owner"`
						DeviceManager  string `json:"device_manager"`
						OnlineOperator string `json:"online_operator"`
					} `json:"meta_data"`
					Notification struct {
						EmailLabelAgent                       string `json:"email.label.agent"`
						FeishuLabelAgent                      string `json:"feishu.label.agent"`
						FeishuLabelFlowdeny                   string `json:"feishu.label.flowdeny"`
						FeishuLabelAskremind                  string `json:"feishu.label.askremind"`
						FeishuLabelFlowerror                  string `json:"feishu.label.flowerror"`
						FeishuLabelFlowaccept                 string `json:"feishu.label.flowaccept"`
						FeishuLabelFlowremind                 string `json:"feishu.label.flowremind"`
						FeishuLabelAddsigntask                string `json:"feishu.label.addsigntask"`
						FeishuLabelFlowtimeout                string `json:"feishu.label.flowtimeout"`
						FeishuLabelQaaskremind                string `json:"feishu.label.qaaskremind"`
						FeishuLabelTransfertask               string `json:"feishu.label.transfertask"`
						EmailInteractiveLabelKey              string `json:"email.interactive.label.key"`
						EmailInteractiveLabelOther            string `json:"email.interactive.label.other"`
						FeishuLabelFlowawaitprocess           string `json:"feishu.label.flowawaitprocess"`
						EmailInteractiveLabelCompany          string `json:"email.interactive.label.company"`
						EmailInteractiveLabelCreator          string `json:"email.interactive.label.creator"`
						EmailInteractiveLabelHelptip          string `json:"email.interactive.label.helptip"`
						FeishuLabelFlowprocessinvalid         string `json:"feishu.label.flowprocessinvalid"`
						EmailInteractiveLabelAbstract         string `json:"email.interactive.label.abstract"`
						EmailInteractiveLabelAsktitle         string `json:"email.interactive.label.asktitle"`
						EmailInteractiveLabelFlowname         string `json:"email.interactive.label.flowname"`
						FeishuInteractiveLabelCreator         string `json:"feishu.interactive.label.creator"`
						FeishuLabelFeishutaskTitletip         string `json:"feishu.label.feishutask.titletip"`
						EmailInteractiveLabelLinkblank        string `json:"email.interactive.label.linkblank"`
						FeishuInteractiveButtonSeemore        string `json:"feishu.interactive.button.seemore"`
						FeishuInteractiveLabelAbstract        string `json:"feishu.interactive.label.abstract"`
						FeishuInteractiveLabelFlowname        string `json:"feishu.interactive.label.flowname"`
						EmailInteractiveLabelCreatetime       string `json:"email.interactive.label.createtime"`
						EmailInteractiveLabelInstanceid       string `json:"email.interactive.label.instanceid"`
						EmailInteractiveLabelAddsigntask      string `json:"email.interactive.label.addsigntask"`
						FeishuInteractiveLabelCreatetime      string `json:"feishu.interactive.label.createtime"`
						FeishuInteractiveLabelInstanceid      string `json:"feishu.interactive.label.instanceid"`
						EmailInteractiveLabelDefaulttitle     string `json:"email.interactive.label.defaulttitle"`
						EmailInteractiveLabelRedmindtitle     string `json:"email.interactive.label.redmindtitle"`
						EmailInteractiveLabelTransfertask     string `json:"email.interactive.label.transfertask"`
						EmailInteractiveButtonStartprocess    string `json:"email.interactive.button.startprocess"`
						FeishuInteractiveButtonStartprocess   string `json:"feishu.interactive.button.startprocess"`
						EmailInteractiveLabelQaaskremindlabel string `json:"email.interactive.label.qaaskremindlabel"`
					} `json:"notification"`
				} `json:"zh-_c_n"`
				Default string `json:"default"`
			} `json:"language"`
			FlowDescription string `json:"flow_description"`
			ViewTemplate    struct {
				Form []interface{} `json:"form"`
				List []struct {
					Name     string `json:"name"`
					Type     string `json:"type"`
					Field    string `json:"field"`
					Children []struct {
						Name     string `json:"name"`
						Type     string `json:"type"`
						Field    string `json:"field"`
						TypeCode string `json:"type_code"`
					} `json:"children"`
				} `json:"list"`
				Detail []struct {
					Type       string `json:"type"`
					Properties struct {
						Project struct {
							Name       string `json:"name"`
							Type       string `json:"type"`
							Title      string `json:"title"`
							Properties struct {
							} `json:"properties"`
							XComponent string `json:"x-component"`
							XDecorator string `json:"x-decorator"`
						} `json:"project"`
						Duration struct {
							Name       string `json:"name"`
							Type       string `json:"type"`
							Title      string `json:"title"`
							Properties struct {
							} `json:"properties"`
							XComponent string `json:"x-component"`
							XDecorator string `json:"x-decorator"`
						} `json:"duration"`
						DeviceId struct {
							Name       string `json:"name"`
							Type       string `json:"type"`
							Title      string `json:"title"`
							Properties struct {
							} `json:"properties"`
							XComponent string `json:"x-component"`
							XDecorator string `json:"x-decorator"`
						} `json:"device_id"`
						DeviceOwner struct {
							Name       string `json:"name"`
							Type       string `json:"type"`
							Title      string `json:"title"`
							Properties struct {
							} `json:"properties"`
							XComponent string `json:"x-component"`
							XDecorator string `json:"x-decorator"`
						} `json:"device_owner"`
						DeviceManager struct {
							Name       string `json:"name"`
							Type       string `json:"type"`
							Title      string `json:"title"`
							Properties struct {
							} `json:"properties"`
							XComponent string `json:"x-component"`
							XDecorator string `json:"x-decorator"`
						} `json:"device_manager"`
						OnlineOperator struct {
							Name       string `json:"name"`
							Type       string `json:"type"`
							Title      string `json:"title"`
							Properties struct {
							} `json:"properties"`
							XComponent string `json:"x-component"`
							XDecorator string `json:"x-decorator"`
						} `json:"online_operator"`
					} `json:"properties"`
				} `json:"detail"`
				Notice []struct {
					Name     string `json:"name"`
					Type     string `json:"type"`
					Field    string `json:"field"`
					TypeCode string `json:"type_code"`
				} `json:"notice"`
			} `json:"view_template"`
			FlowTemplate struct {
				Flow []struct {
					Pa     string   `json:"_pa"`
					Name   string   `json:"name"`
					Next   []string `json:"next"`
					Type   string   `json:"type,omitempty"`
					Kind   string   `json:"_kind,omitempty"`
					Config struct {
						Error  []interface{} `json:"error,omitempty"`
						Finish []struct {
							Action string `json:"action"`
							Config struct {
								Path    string        `json:"path"`
								Query   []interface{} `json:"query"`
								Method  string        `json:"method"`
								Headers []interface{} `json:"headers"`
								Timeout int           `json:"timeout"`
							} `json:"config"`
						} `json:"finish,omitempty"`
					} `json:"config,omitempty"`
					Operator []struct {
						Type     int    `json:"type"`
						Field    string `json:"field"`
						GroupId  int    `json:"group_id"`
						TypeName string `json:"type_name"`
					} `json:"operator,omitempty"`
					OtherConfig struct {
						Context struct {
							Project struct {
								Visible  bool `json:"visible"`
								Editable bool `json:"editable"`
							} `json:"project"`
							Duration struct {
								Visible  bool `json:"visible"`
								Editable bool `json:"editable"`
							} `json:"duration"`
							DeviceId struct {
								Visible  bool `json:"visible"`
								Editable bool `json:"editable"`
							} `json:"device_id"`
							DeviceOwner struct {
								Visible  bool `json:"visible"`
								Editable bool `json:"editable"`
							} `json:"device_owner"`
							DeviceManager struct {
								Visible  bool `json:"visible"`
								Editable bool `json:"editable"`
							} `json:"device_manager"`
							OnlineOperator struct {
								Visible  bool `json:"visible"`
								Editable bool `json:"editable"`
							} `json:"online_operator"`
						} `json:"context,omitempty"`
						Nopeople struct {
							Enable bool `json:"enable"`
						} `json:"nopeople,omitempty"`
						MultiDeny   string `json:"multi_deny,omitempty"`
						ApprovalBtn struct {
						} `json:"approval_btn,omitempty"`
						MultiAccept  string `json:"multi_accept,omitempty"`
						Notification struct {
							EmailEnable  bool `json:"email_enable"`
							FeishuEnable bool `json:"feishu_enable"`
						} `json:"notification,omitempty"`
						DisplayInH5            string `json:"display_in_h5,omitempty"`
						MultiApproval          string `json:"multi_approval,omitempty"`
						AssignRuleWhenRuleInOr string `json:"assign_rule_when_rule_in_or,omitempty"`
					} `json:"other_config,omitempty"`
					FlowInstanceNodeRule  string `json:"flow_instance_node_rule,omitempty"`
					FlowInstanceNodeTitle string `json:"flow_instance_node_title,omitempty"`
				} `json:"flow"`
			} `json:"flow_template"`
			Assistants       []interface{} `json:"assistants"`
			FlowVersionId    string        `json:"flow_version_id"`
			IsTestForRelease bool          `json:"is_test_for_release"`
			OtherConfig      struct {
				Icon        string        `json:"icon"`
				Title       string        `json:"title"`
				KeyWords    []interface{} `json:"key_words"`
				MultiDeny   string        `json:"multi_deny"`
				Description string        `json:"description"`
				JobTransfer struct {
					InstanceTransfer          bool `json:"instance_transfer"`
					ProcessingTransfer        bool `json:"processing_transfer"`
					ProcessingForcastTransfer bool `json:"processing_forcast_transfer"`
				} `json:"job_transfer"`
				MultiAccept  string `json:"multi_accept"`
				Notification struct {
					EmailEnable         bool `json:"email_enable"`
					FeishuEnable        bool `json:"feishu_enable"`
					EndNodeEmailEnable  bool `json:"end_node_email_enable"`
					EndNodeFeishuEnable bool `json:"end_node_feishu_enable"`
				} `json:"notification"`
				ViewersType  string `json:"viewers_type"`
				CreatorLimit struct {
					AllowType      string        `json:"allow_type"`
					IgnoreType     string        `json:"ignore_type"`
					CreatorsAllow  []interface{} `json:"creators_allow"`
					CreatorsIgnore []interface{} `json:"creators_ignore"`
				} `json:"creator_limit"`
				DisplayInH5     string `json:"display_in_h5"`
				ProcessLevel    string `json:"process_level"`
				BusinessOwner   string `json:"business_owner"`
				MultiApproval   string `json:"multi_approval"`
				AllowCopyFlow   string `json:"allow_copy_flow"`
				AdvanceSettings struct {
					AutoDuplicate []interface{} `json:"auto_duplicate"`
				} `json:"advance_settings"`
				CustomPageLink struct {
					Pc       string `json:"pc"`
					Mobile   string `json:"mobile"`
					Disabled bool   `json:"disabled"`
				} `json:"custom_page_link"`
				DetailSyncType    string `json:"detail_sync_type"`
				BusinessCategory  string `json:"business_category"`
				RerunWhenRevoke   string `json:"rerun_when_revoke"`
				CustomContentLink struct {
					Pc       string `json:"pc"`
					Mobile   string `json:"mobile"`
					Disabled bool   `json:"disabled"`
				} `json:"custom_content_link"`
				IsAllowRevocation  bool          `json:"is_allow_revocation"`
				MobileViewEnabled  bool          `json:"mobile_view_enabled"`
				NotificationConfig []interface{} `json:"notification_config"`
				PermissionSettings struct {
					PermissionLevel string `json:"permission_level"`
				} `json:"permission_settings"`
				UploadSpecificDir struct {
				} `json:"upload_specific_dir"`
				BatchOperateDetail  []string `json:"batch_operate_detail"`
				BatchOperateEnabled bool     `json:"batch_operate_enabled"`
				TimeoutLimitSetting struct {
					Enabled    bool `json:"enabled"`
					Autopass   bool `json:"autopass"`
					ExpireDays int  `json:"expire_days"`
				} `json:"timeout_limit_setting"`
				IsAllowBatchApprove bool `json:"is_allow_batch_approve"`
			} `json:"other_config"`
			FlowCreatedTime int64         `json:"flow_created_time"`
			ProjectId       string        `json:"project_id"`
			MetaDataType    int           `json:"meta_data_type"`
			Notes           []interface{} `json:"notes"`
			DelegatesInfo   []interface{} `json:"delegates_info"`
			OperateLogs     []struct {
				FlowInstanceId     string `json:"flow_instance_id"`
				FlowInstanceNodeId string `json:"flow_instance_node_id"`
				TaskId             string `json:"task_id"`
				OperatorId         string `json:"operator_id"`
				LogContent         string `json:"log_content"`
				CreatedTime        int64  `json:"created_time"`
				UpdatedTime        int64  `json:"updated_time"`
			} `json:"operate_logs"`
			MetaData []struct {
				Name     string `json:"name"`
				Type     string `json:"type"`
				Field    string `json:"field"`
				TypeCode string `json:"type_code"`
			} `json:"meta_data"`
		} `json:"extra_info"`
		IsCanceled            bool   `json:"is_canceled"`
		CreatorUserName       string `json:"creator_user_name"`
		OriginCreator         string `json:"origin_creator"`
		OriginCreatorUserName string `json:"origin_creator_user_name"`
		Dictionary            struct {
		} `json:"dictionary"`
		Attachments []interface{} `json:"attachments"`
		Language    struct {
			ZhCN struct {
				Flow struct {
					EndTitle                 string `json:"end.title"`
					FlowName                 string `json:"flow_name"`
					FlowDescription          string `json:"flow_description"`
					Fqop60PZLJfNDzqJi2PTitle string `json:"Fqop60PZLJfNDzqJi2P.title"`
				} `json:"flow"`
				Public struct {
					LabelAsk                         string `json:"label.ask"`
					ButtonAsk                        string `json:"button.ask"`
					LabelTips                        string `json:"label.tips"`
					ButtonDeny                       string `json:"button.deny"`
					ButtonMore                       string `json:"button.more"`
					ButtonReply                      string `json:"button.reply"`
					LabelLaunch                      string `json:"label.launch"`
					ButtonAccept                     string `json:"button.accept"`
					ButtonCancel                     string `json:"button.cancel"`
					ButtonRemind                     string `json:"button.remind"`
					ButtonRevoke                     string `json:"button.revoke"`
					ButtonSubmit                     string `json:"button.submit"`
					ButtonUpload                     string `json:"button.upload"`
					LabelCompany                     string `json:"label.company"`
					LabelSponsor                     string `json:"label.sponsor"`
					LabelSummary                     string `json:"label.summary"`
					ButtonConfirm                    string `json:"button.confirm"`
					ButtonAddSign                    string `json:"button.add_sign"`
					ButtonTransfer                   string `json:"button.transfer"`
					LabelFlowDeny                    string `json:"label.flow_deny"`
					MessageSuccess                   string `json:"message.success"`
					LabelApplyTime                   string `json:"label.apply_time"`
					LabelAttachment                  string `json:"label.attachment"`
					LabelDepartment                  string `json:"label.department"`
					LabelFlowError                   string `json:"label.flow_error"`
					LabelNodeAsked                   string `json:"label.node_asked"`
					LabelAddSignTo                   string `json:"label.add_sign_to"`
					LabelApproveEnd                  string `json:"label.approve_end"`
					LabelCostCenter                  string `json:"label.cost_center"`
					LabelFlowRevoke                  string `json:"label.flow_revoke"`
					LabelInstanceId                  string `json:"label.instance_id"`
					LabelNodeDenied                  string `json:"label.node_denied"`
					LabelOrNodeTip                   string `json:"label.or_node_tip"`
					LabelTransferTo                  string `json:"label.transfer_to"`
					MessagePlsInput                  string `json:"message.pls_input"`
					LabelAndNodeTip                  string `json:"label.and_node_tip"`
					LabelFlowContent                 string `json:"label.flow_content"`
					LabelFlowInvalid                 string `json:"label.flow_invalid"`
					LabelFlowSuccess                 string `json:"label.flow_success"`
					LabelFlowTimeout                 string `json:"label.flow_timeout"`
					LabelLaunchAgain                 string `json:"label.launch_again"`
					LabelNodePending                 string `json:"label.node_pending"`
					LabelNodeReplyed                 string `json:"label.node_replyed"`
					LabelNodeTimeout                 string `json:"label.node_timeout"`
					LabelReasonTempl                 string `json:"label.reason_templ"`
					MessagePlsChoose                 string `json:"message.pls_choose"`
					LabelAuthorizedBy                string `json:"label.authorized_by"`
					LabelFlowProgress                string `json:"label.flow_progress"`
					LabelNodeAccepted                string `json:"label.node_accepted"`
					LabelNodeInserted                string `json:"label.node_inserted"`
					LabelNodeLaunched                string `json:"label.node_launched"`
					MessageRemindSent                string `json:"message.remind_sent"`
					LabelApproveDetail               string `json:"label.approve_detail"`
					MessageNextApprove               string `json:"message.next_approve"`
					LabelFlowProcessing              string `json:"label.flow_processing"`
					MessageSearchPeople              string `json:"message.search_people"`
					LabelConfirmToTempl              string `json:"label.confirm_to_templ"`
					LabelApprovalComments            string `json:"label.approval_comments"`
					MessageSearchNoresult            string `json:"message.search_noresult"`
					MessageGetNextApprove            string `json:"message.get_next_approve"`
					MessageRemindTooOften            string `json:"message.remind_too_often"`
					LabelNodeLaunchedAgain           string `json:"label.node_launched_again"`
					MessageFileUploadError           string `json:"message.file_upload_error"`
					LabelAuthorizedBySibmit          string `json:"label.authorized_by_sibmit"`
					MessageChooseCostCenter          string `json:"message.choose_cost_center"`
					MessageStillSomeApprove          string `json:"message.still_some_approve"`
					LabelAuthorizedByApproval        string `json:"label.authorized_by_approval"`
					MessageAllApproveComplete        string `json:"message.all_approve_complete"`
					MessageAttachmentOpenOnPc        string `json:"message.attachment_open_on_pc"`
					MessageAttachmentSizeLimit       string `json:"message.attachment_size_limit"`
					MessageAttachmentUploadLimit     string `json:"message.attachment_upload_limit"`
					MessageAttachmentNameLengthLimit string `json:"message.attachment_name_length_limit"`
					MessageConfirmToRemoveAttachment string `json:"message.confirm_to_remove_attachment"`
				} `json:"public"`
				Disabled  bool `json:"disabled"`
				Completed bool `json:"completed"`
				MetaData  struct {
					Project        string `json:"project"`
					Duration       string `json:"duration"`
					DeviceId       string `json:"device_id"`
					DeviceOwner    string `json:"device_owner"`
					DeviceManager  string `json:"device_manager"`
					OnlineOperator string `json:"online_operator"`
				} `json:"meta_data"`
				Notification struct {
					EmailLabelAgent                       string `json:"email.label.agent"`
					FeishuLabelAgent                      string `json:"feishu.label.agent"`
					FeishuLabelFlowdeny                   string `json:"feishu.label.flowdeny"`
					FeishuLabelAskremind                  string `json:"feishu.label.askremind"`
					FeishuLabelFlowerror                  string `json:"feishu.label.flowerror"`
					FeishuLabelFlowaccept                 string `json:"feishu.label.flowaccept"`
					FeishuLabelFlowremind                 string `json:"feishu.label.flowremind"`
					FeishuLabelAddsigntask                string `json:"feishu.label.addsigntask"`
					FeishuLabelFlowtimeout                string `json:"feishu.label.flowtimeout"`
					FeishuLabelQaaskremind                string `json:"feishu.label.qaaskremind"`
					FeishuLabelTransfertask               string `json:"feishu.label.transfertask"`
					EmailInteractiveLabelKey              string `json:"email.interactive.label.key"`
					EmailInteractiveLabelOther            string `json:"email.interactive.label.other"`
					FeishuLabelFlowawaitprocess           string `json:"feishu.label.flowawaitprocess"`
					EmailInteractiveLabelCompany          string `json:"email.interactive.label.company"`
					EmailInteractiveLabelCreator          string `json:"email.interactive.label.creator"`
					EmailInteractiveLabelHelptip          string `json:"email.interactive.label.helptip"`
					FeishuLabelFlowprocessinvalid         string `json:"feishu.label.flowprocessinvalid"`
					EmailInteractiveLabelAbstract         string `json:"email.interactive.label.abstract"`
					EmailInteractiveLabelAsktitle         string `json:"email.interactive.label.asktitle"`
					EmailInteractiveLabelFlowname         string `json:"email.interactive.label.flowname"`
					FeishuInteractiveLabelCreator         string `json:"feishu.interactive.label.creator"`
					FeishuLabelFeishutaskTitletip         string `json:"feishu.label.feishutask.titletip"`
					EmailInteractiveLabelLinkblank        string `json:"email.interactive.label.linkblank"`
					FeishuInteractiveButtonSeemore        string `json:"feishu.interactive.button.seemore"`
					FeishuInteractiveLabelAbstract        string `json:"feishu.interactive.label.abstract"`
					FeishuInteractiveLabelFlowname        string `json:"feishu.interactive.label.flowname"`
					EmailInteractiveLabelCreatetime       string `json:"email.interactive.label.createtime"`
					EmailInteractiveLabelInstanceid       string `json:"email.interactive.label.instanceid"`
					EmailInteractiveLabelAddsigntask      string `json:"email.interactive.label.addsigntask"`
					FeishuInteractiveLabelCreatetime      string `json:"feishu.interactive.label.createtime"`
					FeishuInteractiveLabelInstanceid      string `json:"feishu.interactive.label.instanceid"`
					EmailInteractiveLabelDefaulttitle     string `json:"email.interactive.label.defaulttitle"`
					EmailInteractiveLabelRedmindtitle     string `json:"email.interactive.label.redmindtitle"`
					EmailInteractiveLabelTransfertask     string `json:"email.interactive.label.transfertask"`
					EmailInteractiveButtonStartprocess    string `json:"email.interactive.button.startprocess"`
					FeishuInteractiveButtonStartprocess   string `json:"feishu.interactive.button.startprocess"`
					EmailInteractiveLabelQaaskremindlabel string `json:"email.interactive.label.qaaskremindlabel"`
				} `json:"notification"`
			} `json:"zh-CN"`
			Default string `json:"default"`
		} `json:"language"`
		CreatorProfile []struct {
			WorkerUserId   string `json:"worker_user_id"`
			UserName       string `json:"user_name"`
			EnglishName    string `json:"english_name"`
			Company        string `json:"company"`
			CompanyCode    string `json:"company_code"`
			CompanyEn      string `json:"companyEn"`
			CompanyCn      string `json:"companyCn"`
			StaffType      string `json:"staff_type"`
			Department     string `json:"department"`
			DepartmentCn   string `json:"departmentCn"`
			DepartmentEn   string `json:"departmentEn"`
			CostcenterCode string `json:"costcenter_code"`
			Costcenter     string `json:"costcenter"`
			Region         string `json:"region"`
			RegionNoHr     string `json:"region_no_hr"`
			Address        string `json:"address"`
		} `json:"creator_profile"`
		Notes            []interface{} `json:"notes"`
		OperatorRelative bool          `json:"operator_relative"`
		AdminList        []struct {
			WorkerUserId string `json:"worker_user_id"`
			Role         string `json:"role"`
		} `json:"admin_list"`
	} `json:"data"`
}
