package service

import (
	"net/http"
	"os"
	"testing"

	"github.com/gin-gonic/gin"
	gocahce "github.com/patrickmn/go-cache"

	ucfg "git.nevint.com/golang-libs/common-utils/config"
	ulog "git.nevint.com/golang-libs/common-utils/logger"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	"git.nevint.com/welkin2/welkin-backend/cache"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/config"
	log "git.nevint.com/welkin2/welkin-backend/logger"
)

var w client.Watcher
var ctx *gin.Context

func init() {
	os.Setenv("APOLLO_META", "https://apollo-config-cn-stg.nioint.com")
	os.Setenv("ENV", "stg")
	cfg := &ucfg.Config{}
	ucfg.GetConfigApollo("config.json", true, cfg, ucfg.NeedValidate(true), ucfg.ServiceName("ppd-welkin-backend"), ucfg.Cluster(ucfg.GetWelkinApolloCluster()))
	defer ulog.Final()
	cfg.Log.DirPath = "/tmp/logs"
	config.Cfg = cfg
	log.Init(cfg.Log, true)
	logger := log.Logger.Named("activity")
	w = client.NewWatcherByParam(config.Cfg, logger)
	var err error
	if err != nil {
		panic(err)
	}
	cache.CacheRealtimeDataID(w.Mongodb().Client)
	cache.PowerSwapCache = &cache.DeviceInfoCache{
		Cache:         gocahce.New(gocahce.NoExpiration, gocahce.NoExpiration),
		ResourceCache: gocahce.New(gocahce.NoExpiration, gocahce.NoExpiration),
	}
	cache.PowerSwapCache.RefreshDeviceInfoCache(w.Mongodb().Client)
	ctx = &gin.Context{Request: &http.Request{Header: make(http.Header)}}
	ctx.Request.Header.Set("X-Request-ID", "test")
}

func TestImageBuffer_CalculateCCImageV2(t *testing.T) {
	project := umw.PUS3
	imgData := umw.MongoImageInfo{
		DeviceId:   "PS-NIO-3285ff15-7f564f27",
		ImageURL:   "https://cdn-welkin-public-stg.nio.com/welkin/image/internal/PUS3/CC/PS-NIO-3285ff15-7f564f27/2024-08-14/14/CC-RFS2T47-20240814142653427.jpg",
		CameraType: "RFS2T47",
	}
	ib := &ImageBuffer{
		DeviceId:  imgData.DeviceId,
		ImageData: imgData,
		Ctx:       ctx,
	}
	ib.CalculateCCImageV2(project)
}
