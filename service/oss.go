package service

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"git.nevint.com/welkin2/welkin-backend/config"
	log "git.nevint.com/welkin2/welkin-backend/logger"
	"io"
	"io/ioutil"
	"net/http"
	"strings"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/rs/xid"
	"go.uber.org/zap"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	"git.nevint.com/welkin2/welkin-backend/model"
	"git.nevint.com/welkin2/welkin-backend/util"
)

type OSS struct {
	URL            string
	BrokerURL      string
	NMP            string
	AiURL          string
	AppId          string
	AppSecret      string
	Header         map[string]string
	Logger         *zap.SugaredLogger
	PromCollectors map[string]prometheus.Collector
}

func (o *OSS) IssueCommand(targetAppId int, deviceId, requestId, key string, bodyData interface{}) error {
	ph := &ProtoBuffer{o.Logger.Named("PB")}
	payload, err := ph.EncodeRemoteOperationMessage(requestId, key, bodyData)
	if err != nil {
		return err
	}
	header := map[string]string{
		"Content-Type": "application/x-www-form-urlencoded",
	}
	path := "/api/1/in/message/command"
	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:    fmt.Sprintf("%s%s?lang=zh_cn&region=cn&app_id=%s", o.NMP, path, o.AppId),
		Method: "POST",
		Header: header,
		RequestBody: map[string]interface{}{
			"scenario":      "welkin_scenario_1",
			"payload":       base64.URLEncoding.EncodeToString(payload),
			"target_app_id": targetAppId,
			"base64":        true,
			"device_ids":    deviceId,
			"ttl":           60,
			"nonce":         xid.New().String(),
			"ts":            time.Now().UnixMilli(),
		},
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		return err
	}
	defer body.Close()
	data, _ := ioutil.ReadAll(body)
	var responseData model.NMPCommandResponse
	if err = json.Unmarshal(data, &responseData); err != nil {
		return fmt.Errorf("unmarshal response err: %v, response: %s", err, string(data))
	}
	if statusCode/100 != 2 {
		return fmt.Errorf("status code: %d, err: %s", statusCode, string(data))
	}
	if responseData.Success == 0 {
		var errs []string
		for _, detail := range responseData.Data.Details {
			errs = append(errs, detail.Result)
		}
		return fmt.Errorf("oss command response err: %v", strings.Join(errs, ", "))
	}
	return nil
}

func (o *OSS) SendCommandForLogFile(ctx context.Context, userId, deviceId string, paramCode, paramValue interface{}) (model.OSSCtlCmdResponse, bool) {
	var response model.OSSCtlCmdResponse
	requestBody := map[string]interface{}{
		"device_model": "",
		"ability_type": "remote_config",
		"ability_operates": []map[string]interface{}{
			{"ability_code": paramCode,
				"ability_params": []map[string]interface{}{
					{"param_type": "input", "param_code": paramCode, "param_value": paramValue},
				}},
		},
	}
	ts := time.Now().Unix()
	path := fmt.Sprintf("/pe/prime/platform/v1/device_ability/operate/%s", deviceId)
	var vendor int64 = 8
	sign := ucmd.Sign{
		Timestamp:   ts,
		AppId:       o.AppId,
		AppSecret:   o.AppSecret,
		ContentType: o.Header["Content-Type"],
		Method:      "POST",
		Path:        path,
		URLParams:   map[string]interface{}{"vendor": vendor},
		BodyParams:  requestBody,
	}
	sn := sign.Generate()
	if sn == "" {
		o.Logger.Errorf("SendCommandForLogFile, send command to upload file: device_id: %s, `sign` is empty", deviceId)
		return response, false
	}
	header := map[string]string{
		"Content-Type": o.Header["Content-Type"],
		"X-User-ID":    userId,
		"X-User-Type":  "Welkin",
		"X-Request-ID": xid.New().String(),
	}
	log.CtxLog(ctx).Infof("SendCommandForLogFile, send oss command. device_id:%v body:%v header:%v", deviceId, ucmd.ToJsonStrIgnoreErr(requestBody), ucmd.ToJsonStrIgnoreErr(header))
	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:         fmt.Sprintf("%s%s?app_id=%s&vendor=%d&sign=%s&timestamp=%d", o.URL, path, o.AppId, vendor, sn, ts),
		Method:      "POST",
		Header:      header,
		RequestBody: requestBody,
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		o.Logger.Errorf("SendCommandForLogFile, send command %s to upload file: device_id: %s, err: %v", paramCode, deviceId, err)
		return response, false
	}
	defer body.Close()
	data, dErr := ioutil.ReadAll(body)
	if dErr != nil {
		o.Logger.Errorf("get file upload token, err: %s", dErr)
		return response, false
	}
	log.CtxLog(ctx).Infof("SendCommandForLogFile, send oss command. device_id:%v body:%v header:%v resp body:%v", deviceId, ucmd.ToJsonStrIgnoreErr(requestBody), ucmd.ToJsonStrIgnoreErr(header), string(data))
	if statusCode != http.StatusOK {
		o.Logger.Errorf("SendCommandForLogFile, send command %s to upload file: device_id: %s, err: %s", paramCode, deviceId, string(data))
		return response, false
	}
	if err = json.Unmarshal(data, &response); err != nil {
		o.Logger.Errorf("SendCommandForLogFile, send command %s to upload file: device_id: %s, err: %v", paramCode, deviceId, err)
		return response, false
	}
	if response.ResultCode != "success" {
		o.Logger.Errorf("SendCommandForLogFile, send command %s to upload file: device_id: %s, err: %v", paramCode, deviceId, string(data))
		return response, false
	}
	o.Logger.Infof("SendCommandForLogFile, send command %s to upload file: device_id: %s, succeeded to request oss", paramCode, deviceId)
	return response, true
}

func (o *OSS) SendImage(project, deviceId, serviceId, imgUrl, batteryId string, imgType, errCode int) {
	requestBody := map[string]interface{}{
		"project":    project,
		"device_id":  deviceId,
		"image_url":  imgUrl,
		"service_id": serviceId,
		"battery_id": batteryId,
		"image_type": imgType,
		"err_code":   errCode,
	}

	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:    strings.TrimRight(o.BrokerURL, "/") + "/v1/oss/image",
		Method: "POST",
		Header: map[string]string{
			"Content-Type": o.Header["Content-Type"],
		},
		RequestBody: requestBody,
	})
	body, statusCode, err := ct.Do()
	failCnt := o.PromCollectors[util.FailSendImage.ID].(*prometheus.CounterVec)
	if err != nil {
		failCnt.WithLabelValues("oss", fmt.Sprintf("%d", imgType)).Inc()
		o.Logger.Errorf("upload image to oss, err: %v", err)
		return
	}
	defer body.Close()
	data, dErr := ioutil.ReadAll(body)
	if dErr != nil {
		failCnt.WithLabelValues("oss", fmt.Sprintf("%d", imgType)).Inc()
		o.Logger.Errorf("upload image to oss, err: %s", dErr)
		return
	}
	if statusCode != http.StatusOK {
		failCnt.WithLabelValues("oss", fmt.Sprintf("%d", imgType)).Inc()
		o.Logger.Errorf("failed to upload image to oss, err: %s", string(data))
		return
	}
	o.Logger.Infof("succeeded to upload image to oss, request body: %v", requestBody)
	return
}

func (o *OSS) SendV2GControlCommand(userId, deviceId, abilityCode string, paramValues map[string]interface{}) (model.OSSCtlCmdResponse, bool) {
	var response model.OSSCtlCmdResponse
	abilityParams := make([]map[string]interface{}, 0)
	for k, v := range paramValues {
		abilityParams = append(abilityParams, map[string]interface{}{"param_code": k, "param_value": v})
	}
	requestBody := map[string]interface{}{
		"ability_operates": []map[string]interface{}{
			{"ability_code": abilityCode,
				"ability_params": abilityParams,
			},
		},
	}

	ts := time.Now().Unix()
	path := fmt.Sprintf("/pe/prime/platform/v1/device-ability/remote-control/%s", deviceId)
	var vendor int64 = 8
	sign := ucmd.Sign{
		Timestamp:   ts,
		AppId:       o.AppId,
		AppSecret:   o.AppSecret,
		ContentType: o.Header["Content-Type"],
		Method:      "POST",
		Path:        path,
		URLParams:   map[string]interface{}{"vendor": vendor},
		BodyParams:  requestBody,
	}
	sn := sign.Generate()
	if sn == "" {
		o.Logger.Errorf("send v2g command, `sign` is empty, device_id: %s, request body: %+v", deviceId, requestBody)
		return response, false
	}

	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:    fmt.Sprintf("%s%s?app_id=%s&vendor=%d&sign=%s&timestamp=%d", o.URL, path, o.AppId, vendor, sn, ts),
		Method: "POST",
		Header: map[string]string{
			"Content-Type": o.Header["Content-Type"],
			"X-User-ID":    userId,
			"X-User-Type":  "Welkin",
			"X-Request-ID": xid.New().String(),
		},
		RequestBody: requestBody,
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		o.Logger.Errorf("send v2g command, fail to send request, err: %v, device_id: %s, request body: %+v", err, deviceId, requestBody)
		return response, false
	}
	defer body.Close()
	data, dErr := io.ReadAll(body)
	if dErr != nil {
		o.Logger.Errorf("send v2g command, fail to read body, err: %v, device_id: %s, request body: %+v", dErr, deviceId, requestBody)
		return response, false
	}
	if statusCode != http.StatusOK {
		o.Logger.Errorf("send v2g command, status code: %v: device_id: %s, request body: %+v, response data: %s", statusCode, deviceId, requestBody, string(data))
		return response, false
	}
	if err = json.Unmarshal(data, &response); err != nil {
		o.Logger.Errorf("send v2g command, fail to unmarshal response, err: %v, device_id: %s, request body: %+v, response data: %s", err, deviceId, requestBody, string(data))
		return response, false
	}
	if response.ResultCode != "success" {
		o.Logger.Errorf("send v2g command, result code: %s, device_id: %s, request body: %+v, response data: %+v", response.ResultCode, deviceId, requestBody, response)
		return response, false
	}
	o.Logger.Infof("send v2g command, device_id: %s, succeeded to request oss, request body: %v", deviceId, requestBody)
	return response, true
}

func SendControlCommand2Device(ctx context.Context, deviceId string, header map[string]string, body map[string]interface{}) (model.OSSCtlCmdResponse, error) {
	var response model.OSSCtlCmdResponse
	ts := time.Now().Unix()
	path := fmt.Sprintf("/pe/prime/platform/v1/device-ability/remote-control/%v", deviceId)
	sign := ucmd.Sign{
		Timestamp:   ts,
		AppId:       config.Cfg.Sentry.AppId,
		AppSecret:   config.Cfg.Sentry.AppSecret,
		ContentType: header["Content-Type"],
		Method:      "POST",
		Path:        path,
		BodyParams:  body,
	}
	sn := sign.Generate()
	if sn == "" {
		log.CtxLog(ctx).Errorf("sign is empty. header: %v, body: %v", header, body)
		return response, errors.New("sign is empty")
	}
	log.CtxLog(ctx).Infof("oss send command. header:%v body:%v resourceId:%v", header, body, deviceId)
	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:         fmt.Sprintf("%s%s?app_id=%s&sign=%s&timestamp=%d", config.Cfg.OSS.PowUrl, path, config.Cfg.Sentry.AppId, sn, ts),
		Method:      "POST",
		Header:      header,
		RequestBody: body,
	})
	bodyReader, statusCode, err := ct.Do()
	if err != nil {
		log.CtxLog(ctx).Errorf("oss command send fail. device_id: %v, header: %v, body: %v, err: %v", deviceId, header, body, err)
		return response, err
	}
	defer bodyReader.Close()
	data, dErr := ioutil.ReadAll(bodyReader)
	if dErr != nil {
		log.CtxLog(ctx).Errorf("oss command response body read fail. device_id: %v, header: %v, body: %v, err: %v", deviceId, header, body, dErr)
		return response, dErr
	}
	if statusCode != http.StatusOK {
		log.CtxLog(ctx).Errorf("oss command response not 200. device_id: %v, header: %v, body: %v statusCode: %v", deviceId, header, body, statusCode)
		return response, errors.New(fmt.Sprintf("http status not ok. code:%v", statusCode))
	}

	if err = json.Unmarshal(data, &response); err != nil {
		log.CtxLog(ctx).Errorf("oss command response json.Unmarshal fail. data: %v, err: %v", string(data), err)
		return response, err
	}
	if response.ResultCode != "success" {
		log.CtxLog(ctx).Errorf("oss command response ResultCode not success. device_id: %v, resonse: %v", deviceId, response)
		return response, errors.New("oss command response ResultCode not success")
	}
	return response, nil
}

// 获取一个远程运维人员
func GetOneOccRemoteUser(ctx context.Context) (string, error) {
	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL: fmt.Sprintf("%v/pe/occ/event/v1/seats/simple-list?app_id=%v", config.Cfg.OSS.PowUrl, config.Cfg.Sentry.AppId),
		Header: map[string]string{
			"Content-Type": "application/json",
		},
		Method: "POST",
		RequestBody: map[string]interface{}{
			"attribute": "REMOTE_OPS_ENGINEER",
		},
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		log.CtxLog(ctx).Errorf("failed to http occ remote users, err: %v", err)
		return "", err
	}
	if statusCode != http.StatusOK {
		log.CtxLog(ctx).Errorf("failed to http occ remote users statusCode not 200, statusCode: %v", statusCode)
		return "", errors.New(fmt.Sprintf("failed to http occ remote users statusCode not 200, statusCode: %v", statusCode))
	}
	var occRemoteUserResp struct {
		RequestId   string `json:"request_id"`
		ServerTime  int64  `json:"server_time"`
		ResultCode  string `json:"result_code"`
		EncryptType int    `json:"encrypt_type"`
		Data        struct {
			TotalResults int `json:"total_results"`
			ResultList   []struct {
				Id            int    `json:"id"`
				DomainAccount string `json:"domain_account"`
				Ready         int    `json:"ready"` // 0忙碌 1已就绪 2 离开
			} `json:"result_list"`
		} `json:"data"`
	}
	defer body.Close()
	byteData, err := ioutil.ReadAll(body)
	if err != nil {
		log.CtxLog(ctx).Errorf("ioutil.ReadAll err, err: %v", err)
		return "", err
	}
	if err = json.Unmarshal(byteData, &occRemoteUserResp); err != nil {
		log.CtxLog(ctx).Errorf("json.Unmarshal err, err: %v", err)
		return "", err
	}
	occRemoteUser := ""
	for _, v := range occRemoteUserResp.Data.ResultList {
		if v.Ready == 1 {
			occRemoteUser = v.DomainAccount
			break
		}
		if v.Ready == 2 {
			continue
		}
		occRemoteUser = v.DomainAccount
	}
	return occRemoteUser, nil
}

// 获取换电站的站长和负责人
func GetDeviceAgentAndSupervisor(ctx context.Context, deviceId string) (string, string, error) {
	requestBody := map[string]interface{}{
		"category":   "PowerSwap",
		"fields":     "station_agent,device_supervisor",
		"device_ids": []string{deviceId},
	}
	startTime := time.Now()
	// https://apidoc.nioint.com/project/7501/interface/api/520099
	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:         strings.TrimRight(config.Cfg.OSS.PrimeUrl, "/") + "/v1/b/devices/detail",
		Method:      "POST",
		Header:      map[string]string{"Content-Type": "application/json"},
		RequestBody: requestBody,
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		log.CtxLog(ctx).Errorf("get prime resource details, device_ids: %s, err: %v", deviceId, err)
		return "", "", err
	}
	defer body.Close()
	data, dErr := ioutil.ReadAll(body)
	if dErr != nil {
		log.CtxLog(ctx).Errorf("get prime resource details, err: %s", dErr)
		return "", "", err
	}
	if statusCode != http.StatusOK {
		log.CtxLog(ctx).Errorf("get prime resource details, device_ids: %s, err: %v", deviceId, string(data))
		return "", "", errors.New("get prime resource details, error occurred")
	}
	var response PrimeResourceDetails
	if err = json.Unmarshal(data, &response); err != nil {
		log.CtxLog(ctx).Errorf("get prime resource details, device_ids: %s, err: %v", deviceId, err)
		return "", "", err
	}
	if response.ResultCode != "success" {
		log.CtxLog(ctx).Errorf("get prime resource details, device_ids: %s, err: %v", deviceId, string(data))
		return "", "", errors.New("get prime resource details, error occurred")
	}
	if len(response.Data.Results) == 0 {
		log.CtxLog(ctx).Warnf("prime device_ids: %s, detail not found", deviceId)
		return "", "", errors.New("prime resource details not found")
	}
	log.CtxLog(ctx).Infof("GetDeviceAgentAndSupervisor. resp:%v cost:%v", ucmd.ToJsonStrIgnoreErr(response), time.Since(startTime))
	return response.Data.Results[0].StationAgent, response.Data.Results[0].DeviceSupervisor, nil
}

// 获取设备激活时间
func GetDeviceActiveTime(ctx context.Context, resourceIds []string) (map[string]int64, error) {
	subArrayLength := 99
	res := map[string]int64{}
	acticeNotzero := 0
	for i := 0; i < len(resourceIds); i += subArrayLength {
		end := i + subArrayLength
		if end > len(resourceIds) {
			end = len(resourceIds)
		}

		partRes, err := getPartDeviceActiveTime(ctx, resourceIds[i:end])
		if err != nil {
			return nil, err
		}
		for resourceId, activeTime := range partRes {
			res[resourceId] = activeTime
			if activeTime != 0 {
				acticeNotzero++
			}
		}

	}
	log.CtxLog(ctx).Infof("调试 res:%v len:%v acticeNotzero:%v", res, len(res), acticeNotzero)
	return res, nil
}

func getPartDeviceActiveTime(ctx context.Context, resourceIds []string) (map[string]int64, error) {
	requestBody := map[string]interface{}{
		"category":   "PowerSwap",
		"fields":     "activate_time",
		"device_ids": resourceIds,
	}
	startTime := time.Now()
	// https://apidoc.nioint.com/project/7501/interface/api/520099
	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:         strings.TrimRight(config.Cfg.OSS.PrimeUrl, "/") + "/v1/b/devices/detail",
		Method:      "POST",
		Header:      map[string]string{"Content-Type": "application/json"},
		RequestBody: requestBody,
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		log.CtxLog(ctx).Errorf("get prime resource details, device_ids: %s, err: %v", resourceIds, err)
		return nil, err
	}
	defer body.Close()
	data, dErr := ioutil.ReadAll(body)
	if dErr != nil {
		log.CtxLog(ctx).Errorf("get prime resource details, err: %s", dErr)
		return nil, err
	}
	if statusCode != http.StatusOK {
		log.CtxLog(ctx).Errorf("get prime resource details, device_ids: %s, err: %v", resourceIds, string(data))
		return nil, errors.New("get prime resource details, error occurred")
	}
	var response PrimeResourceDetails
	if err = json.Unmarshal(data, &response); err != nil {
		log.CtxLog(ctx).Errorf("get prime resource details, device_ids: %s, err: %v", resourceIds, err)
		return nil, err
	}
	if response.ResultCode != "success" {
		log.CtxLog(ctx).Errorf("get prime resource details, device_ids: %s, err: %v", resourceIds, string(data))
		return nil, errors.New("get prime resource details, error occurred")
	}
	if len(response.Data.Results) == 0 {
		log.CtxLog(ctx).Warnf("prime device_ids: %s, detail not found", resourceIds)
		return nil, errors.New("prime resource details not found")
	}
	res := map[string]int64{}
	for _, result := range response.Data.Results {
		res[result.DeviceId] = result.ActivateTime
	}
	log.CtxLog(ctx).Infof("GetDeviceActiveTime. resp:%v cost:%v", ucmd.ToJsonStrIgnoreErr(response), time.Since(startTime))
	return res, nil
}

type DeviceDetails struct {
	Total   int            `json:"total_results"`
	Results []DeviceDetail `json:"result_list"`
}

type PrimeResourceDetails struct {
	OSSPrimeResponse
	Data DeviceDetails `json:"data"`
}
type OSSPrimeResponse struct {
	RequestId   string `json:"request_id"`
	ResultCode  string `json:"result_code"`
	ServerTime  int    `json:"server_time"`
	EncryptType int    `json:"encrypt_type"`
}
type DeviceDetail struct {
	DeviceId         string `json:"device_id"`
	DeviceSupervisor string `json:"device_supervisor"`
	StationAgent     string `json:"station_agent"`
	ActivateTime     int64  `json:"activate_time"`
}

func GetAllPowerChargeDevicesFromOSS(ctx context.Context, resourceType, resourceModel string) ([]OssPowerCharge, error) {
	allOssPowerChargeDevices := []OssPowerCharge{}
	pageNo := 1
	pageSize := 1000
	for {
		requestBody := map[string]interface{}{
			"resource_type":  resourceType,
			"resource_model": resourceModel,
			"page_no":        pageNo,
			"page_size":      pageSize,
		}

		ct := ucmd.NewHttpClient(ucmd.HttpClient{
			URL:    fmt.Sprintf("%v/v1/resources/powerChargers", config.Cfg.OSS.PowOssUrl),
			Method: "POST",
			Header: map[string]string{
				"Content-Type": "application/json;charset=UTF-8",
			},
			RequestBody: requestBody,
		})
		body, statusCode, err := ct.Do()
		if err != nil {
			return nil, err
		}
		defer body.Close()
		data, err := io.ReadAll(body)
		if err != nil {
			return nil, err
		}
		if statusCode != http.StatusOK {
			return nil, errors.New("http resp status not 200")
		}
		var resp OssPowerChargeResponse
		err = json.Unmarshal(data, &resp)
		if err != nil {
			return nil, err
		}
		if resp.ResultCode != "success" {
			return nil, errors.New("resp ResultCode is not success")
		}
		log.CtxLog(ctx).Infof("success get /v1/resources/powerChargers form oss.req:%v len:%v", requestBody, len(resp.Data.ResourceList))
		if len(resp.Data.ResourceList) == 0 {
			break
		}
		allOssPowerChargeDevices = append(allOssPowerChargeDevices, resp.Data.ResourceList...)
		pageNo++
	}
	return allOssPowerChargeDevices, nil
}

type OssPowerChargeResponse struct {
	ResultCode string `json:"result_code"`
	Message    string `json:"message"`
	RequestId  string `json:"request_id"`
	Data       struct {
		TotalResults int              `json:"total_results"`
		HasNext      bool             `json:"has_next"`
		ResourceList []OssPowerCharge `json:"resource_list"`
	} `json:"data"`
}

type OssPowerCharge struct {
	ResourceId            string `json:"resource_id"`
	ResourceType          string `json:"resource_type"`
	ResourceModel         string `json:"resource_model"`
	CreateTime            int64  `json:"create_time"`
	ServiceType           string `json:"service_type"`
	RegistrationCode      string `json:"registration_code"`
	OperatorId            string `json:"operator_id"`
	Region                string `json:"region"`
	EquipmentOwner        string `json:"equipment_owner"`
	FaultState            string `json:"fault_state"`
	ServiceState          string `json:"service_state"`
	IsConnected           bool   `json:"is_connected"`
	SoftwareVersion       string `json:"software_version,omitempty"`
	HardwareVersion       string `json:"hardware_version,omitempty"`
	FirmwareVersion       string `json:"firmware_version,omitempty"`
	ChargingConnectorList []struct {
		ConnectorId        string  `json:"connector_id"`
		RatedOutputPower   float64 `json:"rated_output_power"`
		RatedOutputCurrent float64 `json:"rated_output_current"`
		OriginId           string  `json:"origin_id"`
		TerminalNumber     string  `json:"terminal_number"`
		MinOutputPower     float64 `json:"min_output_power"`
		WorkState          int     `json:"work_state"`
		ConnectState       int     `json:"connect_state"`
		ConnectorName      string  `json:"connector_name,omitempty"`
		ParkNo             string  `json:"park_no,omitempty"`
	} `json:"charging_connector_list"`
	ExtData struct {
		PlmVersion string `json:"plm_version,omitempty"`
	} `json:"ext_data"`
	Connected        bool    `json:"connected"`
	GroupId          string  `json:"group_id,omitempty"`
	RegionId         string  `json:"region_id,omitempty"`
	Longitude        float64 `json:"longitude,omitempty"`
	Latitude         float64 `json:"latitude,omitempty"`
	RatedOutputPower float64 `json:"rated_output_power,omitempty"`
	Description      string  `json:"description,omitempty"`
	ActivateTime     int64   `json:"activate_time,omitempty"`
	GroupName        string  `json:"group_name,omitempty"`
	OriginId         string  `json:"origin_id,omitempty"`
	Address          string  `json:"address,omitempty"`
	Guide            string  `json:"guide,omitempty"`
	Followers        []struct {
		UserId int `json:"user_id"`
	} `json:"followers,omitempty"`
}

type PrimeResourceInfoMap struct {
	OSSPrimeResponse
	Data ResourceInfoMap `json:"data"`
}

type ResourceInfoMap struct {
	ResourceId     string `json:"resource_id"`
	RealResourceId string `json:"real_resource_id"`
}

func GetRealDeviceId(ctx context.Context, resourceId string) (string, error) {
	var (
		realResourceId string
		err            error
		response       PrimeResourceInfoMap
	)

	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:    fmt.Sprintf("%s/v1/devices/%s/realResourceInfo", strings.TrimRight(config.Cfg.OSS.PrimeUrl, "/"), resourceId),
		Method: "GET",
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		log.CtxLog(ctx).Errorf("get real resource info, resource_id: %s, err: %v", resourceId, err)
		return realResourceId, err
	}
	defer body.Close()
	data, dErr := ioutil.ReadAll(body)
	if dErr != nil {
		log.CtxLog(ctx).Errorf("get real resource info, err: %s", dErr)
		return realResourceId, dErr
	}
	if statusCode != http.StatusOK {
		log.CtxLog(ctx).Errorf("get real resource info, resource_id: %s, err: %v", resourceId, string(data))
		return realResourceId, errors.New("get device details, error occurred")
	}
	if err = json.Unmarshal(data, &response); err != nil {
		log.CtxLog(ctx).Errorf("get real resource info, resource_id: %s, err: %v", resourceId, err)
		return realResourceId, err
	}
	if response.ResultCode != "success" {
		log.CtxLog(ctx).Errorf("get real resource info, resource_id: %s, err: %v", resourceId, string(data))
		return realResourceId, errors.New("get real resource info, error occurred")
	}
	if resourceId != response.Data.ResourceId {
		log.CtxLog(ctx).Warnf("invalid resource id: %s, response resoure id: %s", resourceId, response.Data.ResourceId)
		return "", errors.New("invalid resource id")
	}
	realResourceId = response.Data.RealResourceId
	log.CtxLog(ctx).Infof("succeeded to get real resource info, resource_id: %s, real_resoure_id: %s", resourceId, realResourceId)
	return realResourceId, nil
}
