package service

import (
	"strconv"
	"strings"
	"sync"

	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"

	ucfg "git.nevint.com/golang-libs/common-utils/config"
	um "git.nevint.com/golang-libs/common-utils/model"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	"git.nevint.com/welkin2/welkin-backend/client"
	"git.nevint.com/welkin2/welkin-backend/model"
)

type MongoWatcher struct {
	Client      *client.MongoClient
	Logger      *zap.SugaredLogger
	RequestType string
	UserId      string
	DeviceId    string
}

func (m *MongoWatcher) InsertNewLoginHistory(username string, role, factory int, project ...string) (err error) {
	mu := sync.Mutex{}
	mu.Lock()
	defer mu.Unlock()
	switch m.RequestType {
	case um.BrownDragon:
		ch := &client.BrownDragonLoginHistory{
			Client:    m.Client.Client,
			Username:  username,
			UserId:    m.UserId,
			DeviceIdr: m.Device<PERSON>d,
			Role:      role,
			Factory:   factory,
		}
		err = ch.InsertLogin(umw.BrowndragonLoginHistory)
	default:
		ch := &client.CommonLoginHistory{
			Client:    m.Client.Client,
			Method:    "local",
			Username:  username,
			UserId:    m.UserId,
			DeviceIdr: m.DeviceId,
			Role:      role,
		}
		if len(project) != 0 {
			ch.Project = project[0]
		}
		err = ch.InsertLogin(umw.LoginHistory)
	}
	return
}

func (m *MongoWatcher) MakeUserLogout() (err error) {
	mu := sync.Mutex{}
	mu.Lock()
	defer mu.Unlock()
	switch m.RequestType {
	case um.BrownDragon:
		ch := client.BrownDragonLoginHistory{Client: m.Client.Client, DeviceIdr: m.DeviceId}
		err = ch.UpdateLogout(umw.BrowndragonLoginHistory)
	default:
		ch := client.CommonLoginHistory{Client: m.Client.Client, DeviceIdr: m.DeviceId}
		err = ch.UpdateLogout(umw.LoginHistory)
	}
	return
}

func (m *MongoWatcher) InsertCmsOperationRecord(requestId string, record interface{}) error {
	indexOptions := []client.IndexOption{
		{Name: "expire_date", Fields: bson.D{{"date", -1}}, ExpiredTime: 180 * 24 * 3600},
		{Name: "request_id", Fields: bson.D{{"request_id", 1}}, Unique: true},
		{Name: "device_id", Fields: bson.D{{"device_id", 1}}},
	}
	return m.Client.NewMongoEntry(bson.D{bson.E{Key: "request_id", Value: requestId}}).InsertOne(
		umw.Algorithm, umw.CmsOperationRecordPS2, record, indexOptions...)
}

type BaseIdentifier struct {
	Code int
	Msg  string
	Err  error
	User umw.MongoUserInfo
}

func (m *MongoWatcher) CheckUserIdentifier(cfg *ucfg.Config, password string) BaseIdentifier {
	var (
		response model.OSSAuthResponse
		result   BaseIdentifier
	)

	response, result.Err = SSOAuth(cfg, m.UserId, password)
	if result.Err != nil {
		result.Code, result.Msg = model.SSOERROR, "failed to sso auth"
	} else if response.ResultCode != "success" {
		result.Code, result.Msg = model.SSOERROR, "sso unauthorized: "+response.ResultCode
	}

	if result.Code == 0 {
		rawData, err := m.Client.NewMongoEntry(bson.D{bson.E{Key: "user_id", Value: m.UserId}}).GetOne(
			umw.OAuthDB, umw.UserBaseInfo)
		if err != nil {
			result.Code, result.Msg, result.Err = model.NOPERMISSION, "failed to authorize", err
		} else if rawData == nil {
			result.Code, result.Msg = model.NOPERMISSION, "welkin unauthorized: the user has no permission"
		} else {
			var record umw.MongoUserInfo
			if err = bson.Unmarshal(rawData, &record); err != nil {
				result.Code, result.Msg, result.Err = model.NOPERMISSION, "failed to authorize", err
			} else if len(record.Role) == 0 {
				result.Code, result.Msg = model.NOPERMISSION, "welkin unauthorized: the user has no permission"
			} else {
				result.Code = record.Role[0]
				result.User = record
			}
		}
	}
	return result
}

func (m *MongoWatcher) FindManyPLCRecords(dbName, colName string, uriParam model.PLCRecordParam) ([]byte, error) {
	filter := bson.D{bson.E{Key: "service_id", Value: uriParam.ServiceId}}
	if uriParam.BCStepNum != "" {
		bcList := make([]int32, 0)
		for _, s := range strings.Split(uriParam.BCStepNum, ",") {
			v, e := strconv.ParseInt(s, 10, 32)
			if e != nil {
				m.Logger.Errorf("failed to parse int, err: %v", e)
				continue
			}
			bcList = append(bcList, int32(v))
		}
		filter = append(filter, bson.E{Key: "bc_step_num", Value: bson.M{"$in": bcList}})
	}
	if uriParam.PLStepNum != "" {
		plList := make([]int32, 0)
		for _, s := range strings.Split(uriParam.PLStepNum, ",") {
			v, e := strconv.ParseInt(s, 10, 32)
			if e != nil {
				m.Logger.Errorf("failed to parse int, err: %v", e)
				continue
			}
			plList = append(plList, int32(v))
		}
		filter = append(filter, bson.E{Key: "pl_step_num", Value: bson.M{"$in": plList}})
	}
	byteData, err := m.Client.NewMongoEntry(filter).ListAllPLC(
		dbName, colName, client.Ordered{Key: "timestamp", Descending: uriParam.Descending})
	if err != nil {
		m.Logger.Errorf("failed to get all plc records, filter: %v", filter)
	} else {
		m.Logger.Infof("succeeded to get all plc records, filter: %v", filter)
	}
	return byteData, err
}
