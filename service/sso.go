package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/url"
	"time"

	ucmd "git.nevint.com/golang-libs/common-utils/cmd"
	ucfg "git.nevint.com/golang-libs/common-utils/config"
	"git.nevint.com/welkin2/welkin-backend/model"
)

// SSOAuth is the API userd to authenticate users who are swap station operators
func SSOAuth(conf *ucfg.Config, userId, password string) (model.OSSAuthResponse, error) {
	var response model.OSSAuthResponse

	cryted, err := ucmd.RsaEncrypt(password, conf.SSO.PublicKey)
	if err != nil {
		return response, err
	}
	header := map[string]string{
		"Content-Type": "application/x-www-form-urlencoded",
	}
	ts := time.Now().Unix()
	path := "/api/1/in/opensso/authenticate"
	sign := ucmd.Sign{
		Timestamp:   ts,
		AppId:       conf.Sentry.AppId,
		AppSecret:   conf.Sentry.AppSecret,
		ContentType: header["Content-Type"],
		Method:      "POST",
		Path:        path,
		URLParams:   map[string]interface{}{"hash_type": "sha256"},
		BodyParams: map[string]interface{}{
			"account_id": userId,
			"password":   cryted,
		},
	}
	sn := sign.Generate()
	if sn == "" {
		return response, errors.New("`sign` is empty")
	}
	ct := ucmd.NewHttpClient(ucmd.HttpClient{
		URL:    fmt.Sprintf("%s%s?app_id=%s&sign=%s&timestamp=%d&hash_type=sha256", conf.SSO.SignUrl, path, conf.Sentry.AppId, sn, ts),
		Method: "POST",
		Header: header,
		RequestBody: map[string]interface{}{
			"account_id": url.QueryEscape(userId),
			"password":   url.QueryEscape(cryted),
		},
	})
	body, statusCode, err := ct.Do()
	if err != nil {
		return response, err
	}
	defer body.Close()
	data, dErr := ioutil.ReadAll(body)
	if dErr != nil {
		return response, dErr
	}
	if statusCode/100 != 2 {
		return response, fmt.Errorf("status code: %d, err: %s", statusCode, string(data))
	}
	if err = json.Unmarshal(data, &response); err != nil {
		return response, err
	}
	return response, nil
}
