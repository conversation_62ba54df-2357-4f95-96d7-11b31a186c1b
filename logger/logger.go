package logger

import (
	"context"
	"github.com/gin-gonic/gin"
	"path/filepath"

	"go.uber.org/zap"

	ucfg "git.nevint.com/golang-libs/common-utils/config"
	ulog "git.nevint.com/golang-libs/common-utils/logger"
	um "git.nevint.com/golang-libs/common-utils/model"
)

var Logger *zap.SugaredLogger

// Init will initialize this module
func Init(lc ucfg.LogConfig, verbose bool) {
	Logger = ulog.RuntimeSugar
	if verbose {
		Logger = ulog.DevModeRuntimeSugar
	}
	runtimeLog := filepath.Join(lc.DirPath, "welkin-backend-runtime.log")
	accessLog := filepath.Join(lc.DirPath, "welkin-backend-access.log")
	//runtimeLog := filepath.Join("log", "welkin-backend-runtime.json")
	//accessLog := filepath.Join("log", "welkin-backend-access.json")
	if runtimeLog == "" && accessLog == "" {
		Logger.Panicf("at least one of runtimeLog and accessLog must be specified")
	}

	logOptions := []ulog.Options{
		{
			um.Runtime,
			runtimeLog,
			lc.MaxSize,
			lc.RemainDays,
			lc.MaxBackups,
		},
		{
			um.Access,
			accessLog,
			lc.MaxSize,
			lc.RemainDays,
			lc.MaxBackups,
		},
	}
	if err := ulog.Init(logOptions...); err != nil {
		Logger.Panicf("logger init failed: %v", err)
	}
}

//func CtxLog(c *gin.Context) *zap.SugaredLogger {
//	if c == nil {
//		return Logger
//	}
//	value, found := c.Get("logger")
//	if !found {
//		return Logger
//	}
//	v, ok := value.(*zap.SugaredLogger)
//	if !ok {
//		return Logger
//	}
//	return v
//}

func CtxLog(ctx context.Context) *zap.SugaredLogger {
	ginContext, ok := ctx.(*gin.Context)
	if !ok {
		return Logger
	}
	value, found := ginContext.Get("logger")
	if !found {
		return Logger
	}
	v, ok := value.(*zap.SugaredLogger)
	if !ok {
		return Logger
	}
	return v
}
