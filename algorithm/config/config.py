import os
import threading

from apollo.apollo_client import ApolloClient

config_dict = {}
event = threading.Event()


def listener(change_type, namespace, key, value):
    if change_type == "update":
        config_dict[key] = parse_value(value)


def init_config():
    long_poll_thread = threading.Thread(target=long_poll_config)
    long_poll_thread.setDaemon(True)
    long_poll_thread.start()


def long_poll_config():
    global config_dict
    app_id = os.getenv('SERVICE_NAME')
    cluster = os.getenv('INST_NAME')
    config_url = os.getenv('APOLLO_META')
    secret = os.getenv('APOLLO_ACCESSKEY_SECRET')
    client = ApolloClient(app_id=app_id, cluster=cluster, config_url=config_url, change_listener=listener, secret=secret)
    config_dict = client.get_config_map("settings")
    for key, val in config_dict.items():
        config_dict[key] = parse_value(val)
    event.wait()


def get_config():
    return config_dict


def parse_value(value):
    if value == "false":
        return False
    elif value == "true":
        return True

    try:
        int(value)
        return int(value)
    except:
        try:
            float(value)
            return float(value)
        except:
            return value
