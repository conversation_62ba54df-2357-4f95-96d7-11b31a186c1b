import base64
import gc
import time
import tracemalloc
import grpc
from concurrent import futures

relative_path = "./BSA"
if __name__ == '__main__':
    import os
    from sys import path

    print(os.path.dirname(os.path.abspath(__file__)))
    path.append(f"{os.path.dirname(os.path.abspath(__file__))}/../")

    from config.config import init_config

    init_config()
    relative_path = "."

from BSA.detect_v5_bk import yolo_v5
from BSA.utils.tool import *
from proto import algorithm_service_pb2, algorithm_service_pb2_grpc
from logger.logger import init_logger
from config.config import get_config


config = {}
while len(config) == 0:
    config = get_config()
    time.sleep(0.5)
logger = init_logger(config, "PowerSwap-image-detector", 'INFO')
detector = yolo_v5(weights=[f'{relative_path}/runs/yolov5m/exp1/weights/best.pt'],
                 source=f'{relative_path}/inference/powerswap2', output=f'{relative_path}/download_images/output',
                 txtpath=f'{relative_path}/inference/output_label')
tracemalloc.start()
snapshot_base = tracemalloc.take_snapshot()


class DetectImageServicer(algorithm_service_pb2_grpc.DetectImageServicer):
    def DetectAbnormalImage(self, request, context):
        if request.sha512 is None:
            logger.error("`sha512` is required")
            return algorithm_service_pb2.DetectResponse(err_code=1, message="`sha512` is required")
        if request.file is None:
            logger.error("`file` is required")
            return algorithm_service_pb2.DetectResponse(err_code=1, message="`file` is required")
        img_dec = base64.decodebytes(request.file.encode('utf-8'))
        if request.sha512 != hashlib.sha512(img_dec).hexdigest():
            logger.error("`file` not match with `sha512`")
            return algorithm_service_pb2.DetectResponse(err_code=1, message="`file` not match with `sha512`")

        # file_name = "tracemalloc.txt"
        # try:
        #     os.remove(file_name)
        # except Exception as e:
        #     logger.error(f"fail to remove file: {file_name}, err:{e}")
        # txt_file = open(file_name, "a")
        # txt_file.write(str(i))
        # txt_file.write("\n")
        abnormal, _, failure_image = detector.detect_one(img_dec)
        sha512, abnormal_img = '', ''
        if abnormal:
            failure_image_bytes = ndarray_to_bytes(failure_image)
            b64 = base64.b64encode(failure_image_bytes)
            sha512 = hashlib.sha512(failure_image_bytes).hexdigest()
            abnormal_img = b64.decode('utf-8')
        gc.collect()
        snapshot = tracemalloc.take_snapshot()
        top_stats = snapshot.compare_to(snapshot_base, 'lineno')
        # for stat in top_stats[:10]:
        #     txt_file.write(str(stat))
        #     txt_file.write("\n")

        logger.info(f"succeed to detect image, abnormal is {abnormal}, top memory diff: {top_stats[0]}")
        return algorithm_service_pb2.DetectResponse(sha512=sha512, abnormal_img=abnormal_img, abnormal=abnormal)


if __name__ == '__main__':
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))
    algorithm_service_pb2_grpc.add_DetectImageServicer_to_server(DetectImageServicer(), server)
    server.add_insecure_port('[::]:5000')
    logger.info("start server on [::]:5000")
    server.start()
    server.wait_for_termination()
