import cv2
import hashlib
import numpy as np

from .datasets_backup import SplitImages, letterbox


def cal_sha512(file):
    h = hashlib.sha512()
    while True:
        chunk = file.read(h.block_size)
        if not chunk:
            break
        h.update(chunk)
    return h.hexdigest()


def ndarray_to_bytes(image_np):
    data = cv2.imencode('.jpeg', image_np)[1]
    image_bytes = data.tobytes()
    return image_bytes


def one_img_dataset(image, img_size):
    image_bytes = np.fromstring(image, np.uint8)
    img0 = cv2.imdecode(image_bytes, cv2.IMREAD_UNCHANGED)
    img0_split, overlap_h, overlap_w = SplitImages(img0)
    # Padded resize
    img = letterbox(img0, new_shape=img_size)[0]
    # Convert
    img = img[:, :, ::-1].transpose(2, 0, 1)  # BGR to RGB, to 3x416x416
    img = np.ascontiguousarray(img)
    img0_split = img0_split[:, :, :, ::-1].transpose(0, 3, 1, 2)
    img0_split = np.ascontiguousarray(img0_split)
    return img, img0_split, overlap_h, overlap_w, img0
