import os
import sys
import platform
import shutil
import time
import datetime
from pathlib import Path
import re

import cv2
import numpy as np
import pandas as pd
import torch
import torch.backends.cudnn as cudnn
from numpy import random
import torchvision

from models.experimental import attempt_load
from BSA.utils.datasets_backup import LoadStreams, LoadImages_split, ConcatSplitPred, SplitImages
from BSA.utils.general import (
    check_img_size, non_max_suppression, apply_classifier, scale_coords, xyxy2xywh, plot_one_box, strip_optimizer)
from BSA.utils.torch_utils import select_device, load_classifier, time_synchronized
from BSA.utils.tool import one_img_dataset

os.environ['CUDA_VISIBLE_DEVICES'] = '0'


class yolo_v5(object):
    def __init__(self, weights=['./BSA/runs/yolov5m/exp1/weights/best.pt'],
                 source='./BSA/inference/powerswap2', output='./BSA/download_images/output',
                 txtpath='./BSA/inference/output_label', img_size=960, split=True, filter_=True,
                 conf_thres=0.7, iou_thres=0.4, device='', agnostic_nms=False, augment=False, update=False,
                 classes=None):
        self.weights = weights
        self.source = source
        self.output = output
        self.txtpath = txtpath
        self.img_size = img_size
        self.conf_thres = conf_thres
        self.iou_thres = iou_thres
        self.agnostic_nms = agnostic_nms
        self.augment = augment
        self.update = update
        self.classes = classes
        self.split = split
        self.filter = filter_
        self.path = './BSA/wrong_detection_confidence.xls'

        device = select_device(device)
        self.device = device

        half = device.type != 'cpu'  # half precision only supported on CUDA
        self.half = half

        model = attempt_load(weights, map_location=device)  # load FP32 model
        img_size = check_img_size(img_size, s=model.stride.max())  # check img_size
        if half:
            model.half()  # to FP16
        model.eval()
        self.model = model

        mask = cv2.imread('./BSA/mask/label_small.png', 0)
        ret, self.mask_binary = cv2.threshold(mask, 10, 255, cv2.THRESH_BINARY)

    @torch.no_grad()
    def split_images(self, img0_split, overlap_h, overlap_w):
        img_split = torch.zeros((9, 3, self.img_size, self.img_size), device=self.device)  # init img_split
        # Split images to get results, and concat predictions
        img_split = torch.from_numpy(img0_split).to(self.device)
        img_split = img_split.half() if self.half else img_split.float()  # uint8 to fp16/32
        img_split /= 255.0  # 0 - 255 to 0.0 - 1.0
        if img_split.ndimension() == 3:
            img_split = img_split.unsqueeze(0)
        pred_split = self.model(img_split, augment=self.augment)[0]
        pred_split = non_max_suppression(pred_split, self.conf_thres, self.iou_thres, classes=self.classes,
                                         agnostic=self.agnostic_nms)
        pred_split = ConcatSplitPred(pred_split, overlap_h, overlap_w)

        return pred_split

    @torch.no_grad()
    def split_images_not_parallel(self, img0_split, overlap_h, overlap_w):
        img = torch.zeros((1, 3, self.img_size, self.img_size), device=self.device)  # init img_split
        pred_split_all = []
        # Split images to get results, and concat predictions
        for i in range(img0_split.shape[0]):
            img = torch.from_numpy(img0_split[i]).to(self.device)
            img = img.half() if self.half else img.float()  # uint8 to fp16/32
            img /= 255.0  # 0 - 255 to 0.0 - 1.0
            if img.ndimension() == 3:
                img = img.unsqueeze(0)
            pred_split = self.model(img, augment=self.augment)[0]
            pred_split = non_max_suppression(pred_split, self.conf_thres, self.iou_thres, classes=self.classes,
                                             agnostic=self.agnostic_nms)
            pred_split_all.append(pred_split[0])

        pred_split_all = ConcatSplitPred(pred_split_all, overlap_h, overlap_w)

        return pred_split_all

    @torch.no_grad()
    def filter_wrong_detect(self, pred, img_shape):
        if len(pred) == 1 and pred[0] == None:
            return pred
        else:
            pred = pred[0]
            pred = pred.cpu()
            to_be_deleted = np.array([])
            plug_index = torch.nonzero(pred[:, -1] == 11, as_tuple=False).reshape(-1)
            key_index = torch.nonzero(pred[:, -1] == 13, as_tuple=False).reshape(-1)
            to_be_deleted = np.append(to_be_deleted, key_index)

            if len(plug_index) == 0:
                pass
            # plug检测准确
            elif len(plug_index) == 1:
                plug = pred[plug_index][0]
                # x1, y1, x2, y2
                plug_x1, plug_y1, plug_x2, plug_y2 = plug[0], plug[1], plug[2], plug[3]
                sleeve_index = torch.nonzero(pred[:, -1] == 5, as_tuple=False).reshape(-1)
                for i in sleeve_index:
                    x_cen = (pred[i][0] + pred[i][2]) / 2
                    y_cen = (pred[i][1] + pred[i][3]) / 2
                    if x_cen > plug_x1 and x_cen < plug_x2 and y_cen > plug_y1 and y_cen < plug_y2:
                        to_be_deleted = np.append(to_be_deleted, i)
                to_be_deleted = np.append(to_be_deleted, plug_index)

            elif len(plug_index) > 1:
                to_be_deleted = np.append(to_be_deleted, plug_index)

            large_x_index = torch.nonzero((pred[:, 2] - pred[:, 0]) > 350, as_tuple=False).reshape(-1)
            large_y_index = torch.nonzero((pred[:, 3] - pred[:, 1]) > 350, as_tuple=False).reshape(-1)
            to_be_deleted = np.append(to_be_deleted, large_x_index)
            to_be_deleted = np.append(to_be_deleted, large_y_index)

            # using mask
            for i in range(len(pred)):
                if self.mask_binary[int((pred[i][1] + pred[i][3]) // 2), int((pred[i][0] + pred[i][2]) // 2)] == 0:
                    to_be_deleted = np.append(to_be_deleted, i)

            pred = pred[np.setdiff1d(np.arange(len(pred)), to_be_deleted)]
            if pred.shape[0] == 0:
                pred = None
            else:
                pred = pred.cpu()

            return [pred]

    @torch.no_grad()
    def detect(self, img_path, image_url):
        save_txt = False
        t0 = time.time()
        save_img = True
        dataset = LoadImages_split(img_path, img_size=self.img_size)

        # Get names and colors
        names = self.model.module.names if hasattr(self.model, 'module') else self.model.names
        colors = [[random.randint(0, 255) for _ in range(3)] for _ in range(len(names))]

        # Run inference
        img = torch.zeros((1, 3, self.img_size, self.img_size), device=self.device)  # init img

        _ = self.model(img.half() if self.half else img) if self.device.type != 'cpu' else None  # run once
        for path, img, img0_split, overlap_h, overlap_w, im0s, vid_cap in dataset:
            img = torch.from_numpy(img).to(self.device)
            img = img.half() if self.half else img.float()  # uint8 to fp16/32
            img /= 255.0  # 0 - 255 to 0.0 - 1.0
            if img.ndimension() == 3:
                img = img.unsqueeze(0)

            # Inference
            t1 = time_synchronized()
            pred = self.model(img, augment=self.augment)[0]

            # Apply NMS
            pred = non_max_suppression(pred, self.conf_thres, self.iou_thres, classes=self.classes,
                                       agnostic=self.agnostic_nms)
            if not (len(pred) == 1 and pred[0] == None):
                pred[0][:, :4] = scale_coords(img.shape[2:], pred[0][:, :4], im0s.shape).round()

            # Split images
            if im0s.shape[0] == 1440 and im0s.shape[1] == 2560:
                self.split = False
            else:
                self.split = False

            if self.split:
                pred_split = self.split_images(img0_split, overlap_h, overlap_w)
                if not (len(pred_split) == 1 and pred_split[0] == None):
                    fused_pred = torch.cat((pred[0], pred_split[0]), 0) if pred[0] is not None else pred_split[0]
                    boxes, scores = fused_pred[:, :4], fused_pred[:, 4]
                    i = torchvision.ops.boxes.nms(boxes, scores, self.iou_thres)
                    fused_pred_after_nms = fused_pred[i]
                    pred = fused_pred_after_nms
                    pred = [pred]

            if self.filter:
                pred = self.filter_wrong_detect(pred, im0s.shape)

            if len(pred) == 1 and pred[0] == None:
                has_anomaly = False
            else:
                has_anomaly = True

            for i, det in enumerate(pred):  # detections per image
                p, s, im0 = path, '', im0s
                img_name = p.split('/')[-1]
                save_path = str(Path(self.output) / Path(p).name)
                txt_path = str(Path(self.txtpath) / Path(p).stem) + (
                    '_%g' % dataset.frame if dataset.mode == 'video' else '')

                gn = torch.tensor(im0.shape)[[1, 0, 1, 0]]  # normalization gain whwh
                if det is not None and len(det):
                    for c in det[:, -1]:
                        n = (det[:, -1] == c).sum()  # detections per class
                        if names[int(c)] not in s:
                            s += '%g%s ' % (n, names[int(c)])  # add to string

                    # Write results
                    for *xyxy, conf, cls in det:
                        # # Save confidence
                        # # starts = [each.start() for each in re.finditer('/', image_url)]  # [0, 8]
                        # # failure_image_url = image_url[:starts[-1] + 1] + 'failure/' + image_url[starts[-1] + 1:]
                        # failure_image_url = image_url
                        # df = pd.DataFrame([[conf.item(), cls.item(), img_name, image_url, failure_image_url]],
                        #                   columns=['confidence', 'class', 'station_id', 'image_url',
                        #                            'abnormal_image_url'])
                        # if not os.path.exists(self.path):
                        #     df.to_excel(self.path, index=None)
                        # else:
                        #     df2 = pd.read_excel(self.path)
                        #     df2 = df2.append(df)
                        #     df2.to_excel(self.path, index=None)

                        if save_img:  # Add bbox to image
                            label = '%s %.2f' % (names[int(cls)], conf)
                            # print('概率为:%f' % conf)
                            plot_one_box(xyxy, im0, label=label, color=colors[int(cls)], line_thickness=3)

                # Save results (image with detections)
                if save_img:
                    if dataset.mode == 'images':
                        cv2.imwrite(save_path, im0)
            t2 = time_synchronized()
            # print('Inference finished in (%.3fs).' % (t2 - t1))
            torch.cuda.empty_cache()

        return (has_anomaly, s, im0)

    @torch.no_grad()
    def detect_one(self, image):
        save_txt = False
        t0 = time.time()
        save_img = True

        # Get names and colors
        names = self.model.module.names if hasattr(self.model, 'module') else self.model.names
        colors = [[random.randint(0, 255) for _ in range(3)] for _ in range(len(names))]

        # Run inference
        img = torch.zeros((1, 3, self.img_size, self.img_size), device=self.device)  # init img

        _ = self.model(img.half() if self.half else img) if self.device.type != 'cpu' else None  # run once
        img, img0_split, overlap_h, overlap_w, im0s = one_img_dataset(image, self.img_size)
        img = torch.from_numpy(img).to(self.device)
        img = img.half() if self.half else img.float()  # uint8 to fp16/32
        img /= 255.0  # 0 - 255 to 0.0 - 1.0
        if img.ndimension() == 3:
            img = img.unsqueeze(0)

        # Inference
        t1 = time_synchronized()
        pred = self.model(img, augment=self.augment)[0]

        # Apply NMS
        pred = non_max_suppression(pred, self.conf_thres, self.iou_thres, classes=self.classes,
                                   agnostic=self.agnostic_nms)
        if not (len(pred) == 1 and pred[0] is None):
            pred[0][:, :4] = scale_coords(img.shape[2:], pred[0][:, :4], im0s.shape).round()

        # Split images
        if im0s.shape[0] == 1440 and im0s.shape[1] == 2560:
            self.split = False
        else:
            self.split = False

        if self.split:
            pred_split = self.split_images(img0_split, overlap_h, overlap_w)
            if not (len(pred_split) == 1 and pred_split[0] is None):
                fused_pred = torch.cat((pred[0], pred_split[0]), 0) if pred[0] is not None else pred_split[0]
                boxes, scores = fused_pred[:, :4], fused_pred[:, 4]
                i = torchvision.ops.boxes.nms(boxes, scores, self.iou_thres)
                fused_pred_after_nms = fused_pred[i]
                pred = fused_pred_after_nms
                pred = [pred]

        if self.filter:
            pred = self.filter_wrong_detect(pred, im0s.shape)

        if len(pred) == 1 and pred[0] is None:
            has_anomaly = False
        else:
            has_anomaly = True

        for i, det in enumerate(pred):  # detections per image
            s, im0 = '', im0s
            gn = torch.tensor(im0.shape)[[1, 0, 1, 0]]  # normalization gain whwh
            if det is not None and len(det):
                for c in det[:, -1]:
                    n = (det[:, -1] == c).sum()  # detections per class
                    if names[int(c)] not in s:
                        s += '%g%s ' % (n, names[int(c)])  # add to string
                # Write results
                for *xyxy, conf, cls in det:
                    if save_img:  # Add bbox to image
                        label = '%s %.2f' % (names[int(cls)], conf)
                        # print('概率为:%f' % conf)
                        plot_one_box(xyxy, im0, label=label, color=colors[int(cls)], line_thickness=3)
        t2 = time_synchronized()
        torch.cuda.empty_cache()
        return has_anomaly, s, im0
