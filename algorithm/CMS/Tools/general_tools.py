from datetime import datetime
from pymongo import MongoClient
from logging import Logger

from config.config import get_config

config = get_config()


MAX_ALLOWED_MODULE_POWER_1 = config['max_allowed_module_power_1']
MAX_ALLOWED_MODULE_POWER_2 = config['max_allowed_module_power_2']
POWER_DISTRIBUTION_CAPACITY = config['power_distribution_capacity']
CIRCUIT_01_DISTRIBUTION_CAPACITY = config['circuit_01_distribution_capacity']
CIRCUIT_02_DISTRIBUTION_CAPACITY = config['circuit_02_distribution_capacity']

MONGO_CONNECTION_URI = config['mongodb.welkin.uri']
MONGO_REDRABBIT_CONNECTION_URI = config['mongodb.redrabbit.uri']

PREVIOUS_MODEL_TRIGGER_TIME_DIFF = config['previous_model_trigger_time_diff']


def get_history_data_from_MongoDB(data_struct: dict, device_id: str, request_id: str, logger: Logger):

    # 连接到 MongoDB
    client = MongoClient(MONGO_CONNECTION_URI)
    database = client['algorithm']

    # 读取换电站历史运营记录
    history_operation_collection = database['cms-hive-history-data']

    history_operation_data = history_operation_collection.find_one({"device_id": device_id}, {"_id": 0, "date": 0})

    data_struct['device_history_info'] = history_operation_data

    # 读取换电站错峰充电电池需求数
    data_struct['battery_demand'] = history_operation_data.get('battery_demand', [])

    # 读取cms上一次模型执行参数
    ps2_cms_op_collection = database['cms-operation-record-powerswap2']

    cms_previous_op_data = list(ps2_cms_op_collection.find({"device_id": device_id}, {"_id": 0}).sort([("model_trigger_time", -1)]).limit(1))

    if len(cms_previous_op_data) > 0:
        cms_previous_op_data = cms_previous_op_data[0]

        # 判断距离现在时间是否超过N分钟
        if (data_struct['model_trigger_time'] - cms_previous_op_data['model_trigger_time'])/1000 < PREVIOUS_MODEL_TRIGGER_TIME_DIFF:
            # 上次参数和本次属于同一个充电过程
            for slot_info in data_struct['battery_info']:
                for slot_info_pre in cms_previous_op_data['battery_info']:
                    if slot_info['slot_id'] == slot_info_pre['slot_id']:
                        slot_info["pre_before_switch_current"] = slot_info_pre['before_switch_current']
                        slot_info["pre_switch_soc"] = slot_info_pre['switch_soc']
                        slot_info["pre_after_switch_current"] = slot_info_pre['after_switch_current']

                        if (slot_info["pre_before_switch_current"] != 0) or (slot_info["pre_switch_soc"] != 0) or \
                        (slot_info["pre_after_switch_current"] != 0):
                            slot_info["optimization_label"] = 1
                        else:
                            slot_info["optimization_label"] = 0

                        break
        else:
            # 上次参数和本次不属于同一个充电过程
            for slot_info in data_struct['battery_info']:
                slot_info["optimization_label"] = 0
                slot_info["pre_before_switch_current"] = 0
                slot_info["pre_switch_soc"] = 0
                slot_info["pre_after_switch_current"] = 0

    else:
        for slot_info in data_struct['battery_info']:
            slot_info["optimization_label"] = 0
            slot_info["pre_before_switch_current"] = 0
            slot_info["pre_switch_soc"] = 0
            slot_info["pre_after_switch_current"] = 0

    client.close()

    # 读取赤兔数据库参数配方相关参数

    client = MongoClient(MONGO_REDRABBIT_CONNECTION_URI)
    database = client['PowerSwap2']
    collection = database['devices']

    # 这是一个list
    device_parameter_data = collection.find_one({"_id": device_id})['params']

    for slot_info in data_struct['battery_info']:
        # 对每个仓位去遍历配方参数点
        # 遍历计数
        cnt = 0
        for key_data in device_parameter_data:
            if cnt == 4:
                break

            if key_data['key'] == 870007:
                slot_info['power_distribution_capacity'] = key_data['value']
                cnt += 1
                continue

            if key_data['key'] == 870060:
                slot_info['circuit_01_distribution_capacity'] = key_data['value']
                cnt += 1
                continue

            if key_data['key'] == 870061:
                slot_info['circuit_02_distribution_capacity'] = key_data['value']
                cnt += 1
                continue

            if (1 <= key_data['key'] - 850000 + 1 <= 13) and (slot_info['slot_id'] == key_data['key'] - 850000 + 1):
                slot_info['branch_circuit_current_limit'] = -key_data['value']
                cnt += 1
                continue

        if slot_info.get('power_distribution_capacity') is None:
            slot_info['power_distribution_capacity'] = POWER_DISTRIBUTION_CAPACITY

        if slot_info.get("circuit_01_distribution_capacity") is None:
            slot_info['circuit_01_distribution_capacity'] = CIRCUIT_01_DISTRIBUTION_CAPACITY

        if slot_info.get("circuit_02_distribution_capacity") is None:
            slot_info['circuit_02_distribution_capacity'] = CIRCUIT_02_DISTRIBUTION_CAPACITY

    client.close()

    logger.info("[%s] [%s] - " % (device_id, request_id) + "data struct is %s" % data_struct)

    # 当前模块功率赋值
    for slot_info in data_struct['battery_info']:
        slot_info["max_allowed_module_power_1"] = MAX_ALLOWED_MODULE_POWER_1
        slot_info["max_allowed_module_power_2"] = MAX_ALLOWED_MODULE_POWER_2

        # 检查是否获取到 power_distribution_capacity 等参数
        if 'power_distribution_capacity' not in slot_info.keys():
            return 2001

        if 'circuit_01_distribution_capacity' not in slot_info.keys():
            return 2002

        if 'circuit_02_distribution_capacity' not in slot_info.keys():
            return 2003

        if 'branch_circuit_current_limit' not in slot_info.keys():
            return 2004

    current_hour = datetime.fromtimestamp(data_struct['model_trigger_time'] / 1000).hour
    if len(data_struct['battery_demand']) > 0:
        # current_hour = 14

        for item in data_struct['battery_demand']:
            if item['hour'] == current_hour:
                data_struct['battery_demand'] = [item]
                break

        # 相当于没有对应小时段的数据
        if len(data_struct['battery_demand']) > 1:
            # return 2005
            # 用历史数据替代
            data_struct['battery_demand'] = []
            history_battery_demand = {}
            # 如果没有历史订单数据
            if 'map_device_battery_type_hourly_service' not in data_struct['device_history_info'].keys():
                return 2005
            else:
                # 如果70 或 100都没有key
                if '70kWh' not in data_struct['device_history_info']['map_device_battery_type_hourly_service'].keys() or \
                        '100kWh' not in data_struct['device_history_info'][
                    'map_device_battery_type_hourly_service'].keys():
                    return 2005

                else:
                    # 各电池种类都有key
                    history_battery_demand['70'] = \
                    data_struct['device_history_info']['map_device_battery_type_hourly_service']['70kWh'].get(str(current_hour), 0)
                    history_battery_demand['100'] = \
                    data_struct['device_history_info']['map_device_battery_type_hourly_service']['100kWh'].get(str(current_hour), 0)
                    history_battery_demand['hour'] = current_hour
                    data_struct['battery_demand'].append(history_battery_demand)


    else:
        # return 2005
        history_battery_demand = {}
        # 如果没有历史订单数据
        if 'map_device_battery_type_hourly_service' not in data_struct['device_history_info'].keys():
            return 2005
        else:
            # 如果70 或 100都没有key
            if '70kWh' not in data_struct['device_history_info']['map_device_battery_type_hourly_service'].keys() or \
                    '100kWh' not in data_struct['device_history_info']['map_device_battery_type_hourly_service'].keys():
                return 2005

            else:
                # 各电池种类都有key
                history_battery_demand['70'] = data_struct['device_history_info']['map_device_battery_type_hourly_service']['70kWh'].get(str(current_hour), 0)
                history_battery_demand['100'] = data_struct['device_history_info']['map_device_battery_type_hourly_service']['100kWh'].get(str(current_hour), 0)
                history_battery_demand['hour'] = current_hour
                data_struct['battery_demand'].append(history_battery_demand)

    return data_struct


def initial_battery_info_reconstruction(battery_info):
    """ battery info @ beginning call
    arguments:
        bin: [1, 4, 6, 12] charging bin
    return:
        init_temp, 
        init_current, 
        capacity, 
        flex_charging, 
        init_soc

    """
    # 对传入电池初始信息进行转换
    init_temp = {}
    init_current = {}
    capacity = {}  # modified
    flex_charging = {}
    init_soc = {}
    # {'01': {'init_temp': 28, 'init_current': -220.8}}
    for k, v in battery_info.items():
        if v['init_current'] == 0:
            continue
        init_temp[k] = v['init_temp']
        init_current[k] = v['init_current']
        capacity[k] = v['capacity']
        if v['flex_charging'] == 1:
            flex_charging[k] = True
        elif v['flex_charging'] == 0:
            flex_charging[k] = False
        elif v['flex_charging'] == 2:
            flex_charging[k] = False
        else:
            flex_charging[k] = 3

        init_soc[k] = v['init_soc']

    return init_temp, init_current, capacity, flex_charging, init_soc

