import time

import grpc
from concurrent import futures
from google.protobuf.json_format import MessageToDict

relative_path = './CMS'

if __name__ == '__main__':
    import os
    from sys import path

    print(os.path.dirname(os.path.abspath(__file__)))
    path.append(f"{os.path.dirname(os.path.abspath(__file__))}/../")

    from config.config import init_config

    init_config()
    relative_path = '.'


from proto import cms_pb2, cms_pb2_grpc
from logger.logger import init_logger
from config.config import get_config
from CMS.Model.model_operation import main
from CMS.Tools.general_tools import MAX_ALLOWED_MODULE_POWER_1, MAX_ALLOWED_MODULE_POWER_2


config = {}
while len(config) == 0:
    config = get_config()
    time.sleep(0.5)
logger = init_logger(config, "CMS-calculation", 'INFO')


class CMSServicer(cms_pb2_grpc.CMSServicer):
    def CalculateCMS(self, request, context):
        start = time.time()
        try:
            data_struct = MessageToDict(request, preserving_proto_field_name=True, including_default_value_fields=True)
            data_struct['model_trigger_time'] = int(data_struct['model_trigger_time'])
        except Exception as e:
            logger.error(f'fail to convert to dict, err: {e}')
            return cms_pb2.CMSResponse(err_code=2006, message=str(e))

        try:
            res = main(data_struct=data_struct, logger=logger, relative_path=relative_path)
        except Exception as e:
            logger.error(f'fail to calculate cms, err: {e}, request id: {data_struct["request_id"]}')
            return cms_pb2.CMSResponse(err_code=2006, message=str(e))
        if res['err_code'] == 0:
            logger.info(f'succeed to calculate cms, request id: {data_struct["request_id"]}, time use: {time.time()-start}')

        try:
            battery_info = {}
            for slot_id, item in res['results'].items():
                battery_info[slot_id] = cms_pb2.CMSResult(
                    power_distribution_capacity=int(item['power_distribution_capacity']),
                    branch_circuit_current_limit=int(item['branch_circuit_current_limit']),
                    circuit_01_distribution_capacity=int(item['circuit_01_distribution_capacity']),
                    circuit_02_distribution_capacity=int(item['circuit_02_distribution_capacity']),
                    max_allowed_module_power_1=MAX_ALLOWED_MODULE_POWER_1,
                    max_allowed_module_power_2=MAX_ALLOWED_MODULE_POWER_2,
                    optimization_label=item.get('optimization_label', 0),
                    before_switch_current=item.get('before_switch_current', 0),
                    switch_soc=item.get('switch_soc', 0),
                    after_switch_current=item.get('after_switch_current', 0),
                    charging_mode=item.get('charging_mode', ''),
                    switch_moment=item.get('switch_moment', 0),
                    current_current=item.get('current_current', 0),
                    need_executed=item.get('need_executed', False),
                )
        except Exception as e:
            logger.error(f'fail to write battery_info, err: missing key {e}, cms result: {res}, request id: {data_struct["request_id"]}')
            return cms_pb2.CMSResponse(err_code=2006, message=f'missing key {str(e)}')

        try:
            battery_demand = []
            for _, bd in enumerate(res['battery_demand']):
                battery_demand.append(cms_pb2.BatteryDemand(
                    hour=bd['hour'],
                    battery70=int(bd['70']),
                    battery100=int(bd['100']),
                ))
        except Exception as e:
            logger.error(f'fail to write battery_demand, err: missing key {e}, cms result: {res}, request id: {data_struct["request_id"]}')
            return cms_pb2.CMSResponse(err_code=2006, message=f'missing key {str(e)}')

        try:
            response = cms_pb2.CMSResponse(err_code=res['err_code'], message=res['message'], version=res['version'],
                                           battery_info=battery_info, battery_demand=battery_demand)
        except Exception as e:
            logger.error(f'fail to write response, err: {e}, cms result: {res}, request id: {data_struct["request_id"]}')
            return cms_pb2.CMSResponse(err_code=2006, message=str(e))
        return response


if __name__ == '__main__':
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=1))
    cms_pb2_grpc.add_CMSServicer_to_server(CMSServicer(), server)
    server.add_insecure_port('[::]:5002')
    logger.info("start rpc server on [::]:5002")
    server.start()
    server.wait_for_termination()
