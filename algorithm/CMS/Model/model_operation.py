from logging import Logger

from CMS.Model.decision_model_v2 import challenge_competition, charging_simulation, \
    conditional_optimization_battery_selection
from CMS.Model.decision_model_v2 import decision_model
from CMS.Tools.general_tools import get_history_data_from_MongoDB


def result_change_format(data_struct: dict, result: list):
    '''
    result: 计算结果list
    '''

    result_dict = {}

    # 计算出优化结果list中所有的slot_id
    op_slot_id_list = []

    for battery_info in data_struct['battery_info']:

        for op_result in result:

            if battery_info['slot_id'] == int(op_result['slot_id']):
                op_slot_id_list.append(int(op_result['slot_id']))

                op_result['need_executed'] = True
                op_result['power_distribution_capacity'] = battery_info.get('power_distribution_capacity', 0)
                op_result['branch_circuit_current_limit'] = battery_info.get('branch_circuit_current_limit', 0)
                op_result['circuit_01_distribution_capacity'] = battery_info.get('circuit_01_distribution_capacity', 0)
                op_result['circuit_02_distribution_capacity'] = battery_info.get('circuit_02_distribution_capacity', 0)
                op_result['optimization_label'] = 1

                result_dict[battery_info['slot_id']] = op_result

                break

    # 确定是否是没有参与计算的站点，也要存在输出中
    for battery_info in data_struct['battery_info']:
        if battery_info['slot_id'] not in op_slot_id_list:
            slot_info = {}
            slot_info['slot_id'] = str(battery_info['slot_id']).rjust(2, '0')
            slot_info['battery_id'] = battery_info['battery_id']

            slot_info['need_executed'] = False
            slot_info['power_distribution_capacity'] = battery_info.get('power_distribution_capacity', 0)
            slot_info['branch_circuit_current_limit'] = battery_info.get('branch_circuit_current_limit', 0)
            slot_info['circuit_01_distribution_capacity'] = battery_info.get('circuit_01_distribution_capacity', 0)
            slot_info['circuit_02_distribution_capacity'] = battery_info.get('circuit_02_distribution_capacity', 0)
            slot_info['optimization_label'] = 0

            result_dict[battery_info['slot_id']] = slot_info

    return result_dict


def main(data_struct: dict, logger: Logger, relative_path: str):
    """
    主函数
    :return:
    """

    device_id = data_struct['device_id']
    request_id = data_struct['request_id']
    output_data = {'err_code': 0, 'message': 'success', 'version': '1.0.0', 'results': {}, 'battery_demand': []}

    # logger.info("[%s] [%s] - " % (device_id, request_id) + "model operation started")
    # 从数据库获取剩余部分模型所需参数
    try:
        data_struct = get_history_data_from_MongoDB(data_struct=data_struct, device_id=device_id, request_id=request_id, logger=logger)

        if not isinstance(data_struct, dict):
            err_code = 1004
            message = 'dependency data retrieve failed'

            if data_struct == 2001:
                err_code = data_struct
                message = 'feature [power_distribution_capacity] missing'

            elif data_struct == 2002:
                err_code = data_struct
                message = 'feature [circuit_01_distribution_capacity] missing'

            elif data_struct == 2003:
                err_code = data_struct
                message = 'feature [circuit_02_distribution_capacity] missing'

            elif data_struct == 2004:
                err_code = data_struct
                message = 'feature [branch_circuit_current_limit] missing'

            elif data_struct == 2005:
                err_code = data_struct
                message = 'feature [battery_demand] missing'

            output_data['err_code'] = err_code
            output_data['message'] = message
            logger.error("[%s] [%s] - " % (device_id, request_id) + message)

            return output_data

    except Exception as err:
        err_code = 2000
        message = 'dependency data retrieve failed'
        output_data['err_code'] = err_code
        output_data['message'] = message
        logger.error("[%s] [%s] - " % (device_id, request_id) + message + ", err: %s" % err)

        return output_data

    # logger.info("[%s] [%s] - " % (device_id, request_id) + "dependency data retrieve succeeded, simulation model called")

    # 计算仿真结果
    try:
        charging_simulation_list = charging_simulation(logger=logger, data_struct=data_struct, relative_path=relative_path)
    except Exception as err:
        err_code = 1000
        message = 'simulation calculation failed'
        output_data['err_code'] = err_code
        output_data['message'] = message

        logger.error("[%s] [%s] - " % (device_id, request_id) + message + ", err: %s" % err)

        return output_data

    logger.info("[%s] [%s] - " % (device_id, request_id) + "simulation calculation finished, the number of simulation cases are: %d" % (len(charging_simulation_list)))

    try:
        # 将电池分为优先级的和没有优化级的
        # prior充电电池一定满足电池数量需求
        prior_charging_slot_id, non_prior_charging_slot_id, \
            fully_charged_slot_id, \
            battery_ready_num_70, battery_ready_num_100 = conditional_optimization_battery_selection(data_struct=data_struct)
    except Exception as err:
        err_code = 1001
        message = 'pre-optimization battery classification failed'
        output_data['err_code'] = err_code
        output_data['message'] = message

        logger.error("[%s] [%s] - " % (device_id, request_id) + message + ", err: %s" % err)
        return output_data

    # logger.info("[%s] [%s] - " % (device_id, request_id) + "decision optimization model called")

    try:
        # 如果charging_df为空，说明没有正在充电的电池
        current_result_list = decision_model(charging_df=charging_simulation_list, data_struct=data_struct,
                                             battery_ready_num=(battery_ready_num_70 + battery_ready_num_100),
                                             prior_charging_slot_id=prior_charging_slot_id,
                                             non_prior_charging_slot_id=non_prior_charging_slot_id,
                                             fully_charged_slot_id=fully_charged_slot_id)
    except Exception as err:
        err_code = 1002
        message = 'optimization calculation failed'
        output_data['err_code'] = err_code
        output_data['message'] = message
        logger.error("[%s] [%s] - " % (device_id, request_id) + message + ", err: %s" % err)
        return output_data

    # logger.info("[%s] [%s] - " % (device_id, request_id) + "competition model called")

    try:
        # 进行擂台赛制比较各仓位的优化结果
        final_result = challenge_competition(pre_data_struct=data_struct, current_result=current_result_list,
                                             battery_ready_num=(battery_ready_num_70 + battery_ready_num_100),
                                             prior_charging_slot_id=prior_charging_slot_id, logger=logger,
                                             relative_path=relative_path)
    except Exception as err:
        err_code = 1003
        message = 'challenge competition calculation failed'
        output_data['err_code'] = err_code
        output_data['message'] = message
        logger.error("[%s] [%s] - " % (device_id, request_id) + message + ", err: %s" % err)
        return output_data

    # logger.info("[%s] [%s] - " % (device_id, request_id) + "competition calculation finished, cms model results calculation finished, result data format transformation started")

    try:
        result_dict = result_change_format(data_struct=data_struct, result=final_result)
    except Exception as err:
        err_code = 1004
        message = 'result data format transformation failed'
        output_data['err_code'] = err_code
        output_data['message'] = message
        logger.error("[%s] [%s] - " % (device_id, request_id) + message + ", err: %s" % err)
        return output_data

    # logger.info("[%s] [%s] - " % (device_id, request_id) + "result data format transformation finished")

    output_data['results'] = result_dict
    output_data['battery_demand'] = data_struct['battery_demand']

    return output_data
