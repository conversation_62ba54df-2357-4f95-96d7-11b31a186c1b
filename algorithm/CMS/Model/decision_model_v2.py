import copy
from datetime import datetime
from logging import Logger
import json
import numpy as np
import time
import pandas as pd
from pandas.core.common import SettingWithCopyWarning
import random
import warnings
import os

from CMS.Model.cms_simulation import advance_battery_selection, battery_initial_info, charging_mode_simulation_feedback_system, swap_initial_info
from config.config import get_config

config = get_config()


warnings.filterwarnings("ignore", category=FutureWarning)
warnings.simplefilter("ignore", category=SettingWithCopyWarning)


MAX_CHARGING_TIME = config['simu_time']  # TODO 需确定限值
CHARGING_FINISH_SOC = config['charging_finish_soc']              # 充电完成用户SOC

BATTERY_MAINTENANCE_CHARGING_CURRENT = config['battery_maintenance_charging_current']
BATTERY_MAINTENANCE_START_REAL_SOC = config['battery_maintenance_start_real_soc']

BATTERY_MAINTENANCE_START_HOUR = config['battery_maintenance_start_hour']
BATTERY_MAINTENANCE_END_HOUR = config['battery_maintenance_end_hour']

CANDIDATE_VOLUME = config['candidate_volume']                    # 控制候选池中单仓方案数量
HEAD_TAIL_SAMPLE_RATIO = config['head_tail_sample_ratio']
SAMPLE_RATIO = config['sample_ratio']
BEST_CASE_RATIO = config['best_case_ratio']
WORST_CASE_RATIO = config['worst_case_ratio']
QUEUE_TIME_LIMIT = config['queue_time_limit']                    # 排队时间阈值（分钟）
DEFAULT_SERVICE_NUM = config['default_service_num']
PRIOR_BATTERY_NUM = 4                                         # 优先充电电池数
PRIORITY_70_CHARGING_NUM = config['priority_70_charging_num']    # 70度优先充电电池数
PRIORITY_100_CHARGING_NUM = config['priority_100_charging_num']  # 100度优先充电电池数
GRANULARITY = config['granularity']                              # 单个决策变量选择数量
FIRST_OPT_CURRENT_INCREMENT_SIZE = config['first_opt_current_increment_size']
CURRENT_INCREMENT_SIZE = config['current_increment_size']
REFINEMENT_RANGE = config['refinement_range']
QUEUE_LIMIT = config['queue_limit']                              # 单个站点的排队上限，根据高低单量站进行调整，5适用于低单量站点
PENALTY = config['penalty']                                      # 惩罚系数
SERVICE_DIFF_MUTIPLE = config['service_diff_mutiple']


# total_charging_simulation_resu_list = []
# def multiprocess_calculation_result(simulated_result):
#     '''
#     回调函数，在多进程计算完成，调用存入结果
#     '''

#     total_charging_simulation_resu_list.append(simulated_result)


def charging_simulation(logger: Logger, data_struct: dict, operation_type='optimization', relative_path='.'):
    """
    通过仿真模块获得
    :return: 仿真结果
    """
    # charging_simu = np.load('test.npy').tolist()
    # with open("test1.pkl", "rb") as fp:
    #     charging_simu = pickle.load(fp)
    # 根据data_struct中每个电池的优化标签，判断该电池属于微调还是粗调
    # 判断每块电池优化状态
    device_id = data_struct['device_id']
    model_trigger_time = data_struct['model_trigger_time']
    battery_info = data_struct['battery_info']
    battery_demand = data_struct['battery_demand']
    # service_info = data_struct['service_info']
    module_type = data_struct['module_type']

    # map_hourly_electricity_price更新为使用入参参数
    map_electricity_price = data_struct['device_history_info']['map_hourly_electricity_price']
    
    # try:
    #     with open('./Mapping/map_hourly_electricity_price.json', 'r', encoding='utf-8') as fp:
    #         map_electricity_price = json.loads(fp.read())
    # except Exception as err:
    #     print(err)

    try:
        with open(f'{relative_path}/Mapping/map_soc_temp_ocv.json', 'r', encoding='utf-8') as fp:
            map_soc_temp_ocv = json.loads(fp.read())
    except Exception as err:
        logger.error(str(err))

    try:
        with open(f'{relative_path}/Mapping/map_soc_temp_current.json', 'r', encoding='utf-8') as fp:
            map_soc_cur = json.loads(fp.read())
    except Exception as err:
        logger.error(str(err))

    try:
        with open(f'{relative_path}/Mapping/map_soc_temp_voltage.json', 'r', encoding='utf-8') as fp:
            map_soc_vol = json.loads(fp.read())
    except Exception as err:
        logger.error(str(err))
    
    # 选择决策变量
    if operation_type == 'optimization':
        optimization_range = {}

        for single_battery_info in battery_info:
            # 先判断电池是否充电，每有一块电池充电，就增加granularity^3个仿真case
            # 减少仿真次数
            # 和soc比较原因是防止达到充电结束soc时继续充电（满充等情况）
            if single_battery_info['charging_status'] == 2 or single_battery_info['battery_user_soc'] >= CHARGING_FINISH_SOC:
                continue

            if (single_battery_info['battery_id'] is None) or len(single_battery_info['battery_id']) == 0:
                continue
            else:
                optimization_range[str(single_battery_info['slot_id']).rjust(2, '0')] = {}

                increment_size = FIRST_OPT_CURRENT_INCREMENT_SIZE

                # 如果是未粗调电池
                if single_battery_info['optimization_label'] == 0:
                    # 需要把前和后的电流范围分开，因为前后的电流范围不一样
                    before_left, before_right = -220, -30
                    after_left, after_right = -220, -30
                    left_soc, right_soc = 20, 75

                # 否则认为是已粗调电池
                else:
                    before_left, before_right = max(single_battery_info['pre_before_switch_current']-REFINEMENT_RANGE, -220), \
                                                min(single_battery_info['pre_before_switch_current']+REFINEMENT_RANGE, -30)
                    if before_left > before_right:
                        before_left, before_right = before_right, before_left

                    after_left, after_right = max(single_battery_info['pre_after_switch_current']-REFINEMENT_RANGE, -220), \
                                            min(single_battery_info['pre_after_switch_current']+REFINEMENT_RANGE, -30)
                    if after_left > after_right:
                        after_left, after_right = after_right, after_left
                    
                    left_soc, right_soc = max(20, single_battery_info['pre_switch_soc']-REFINEMENT_RANGE), \
                                        min(75, single_battery_info['pre_switch_soc']+REFINEMENT_RANGE)
                    if left_soc > right_soc:
                        left_soc, right_soc = right_soc, left_soc

                    increment_size = CURRENT_INCREMENT_SIZE

                switch_soc_array = []
                before_switch_current_array = []
                after_switch_current_array = []

                for i in np.arange(before_left, before_right, increment_size):
                    # before_left_i = int((before_right - before_left) / GRANULARITY * i + before_left)
                    # before_right_i = int((before_right - before_left) / GRANULARITY * (i + 1) + before_left)

                    # before_switch_current_array.append(random.randrange(before_left_i, before_right_i))
                    # after_switch_current_array.append(random.randrange(after_left_i, after_right_i))
                    before_switch_current_array.append(i)

                for i in np.arange(after_left, after_right, increment_size):
                    after_switch_current_array.append(i)

                for i in np.arange(left_soc, right_soc, increment_size):
                    switch_soc_array.append(i)
                    
                # 放入字典中，后面传入仿真模块
                '''
                {
                    '03': {
                        'switch_soc_array': [27, 32, 52, 57, 64], 
                        'before_switch_current_array': [-207, -145, -143, -100, -67], 
                        'after_switch_current_array': [-193, -162, -122, -86, -57]
                    }, 
                }
                '''
                optimization_range[str(single_battery_info['slot_id']).rjust(2, '0')]['switch_soc_array'] = list(set(switch_soc_array))
                optimization_range[str(single_battery_info['slot_id']).rjust(2, '0')][
                    'before_switch_current_array'] = list(set(np.append(before_switch_current_array, -55)))
                optimization_range[str(single_battery_info['slot_id']).rjust(2, '0')][
                    'after_switch_current_array'] = list(set(np.append(after_switch_current_array, -55)))

        # 仿真模块，循环迭代optimization_range里面的电流范围
        total_charging_simulation_resu_list = []

    elif operation_type == 'challenge':
        pass

    # 提前选择仿真电池
    # 先预选当前正在充电的电池以外的电池加入充电队列（off状态就是没有预选）
    # 如果电池id为空，则认为该仓没有电池
    simulated_selected_battery, simulated_battery_demand = advance_battery_selection(battery_info=battery_info, battery_demand=battery_demand,
                                                                                     simulated_battery_selection='off')

    # 该模块会模拟电池充电初始柔性状态
    # 如果电池id为空，则认为该仓没有电池，最终的输出中不包含没有电池的仓位
    initial_battery_temp, initial_charging_current, battery_type, battery_capacity, \
    initial_status_flex_charging, initial_battery_soc, \
    max_allowed_module_power_1, max_allowed_module_power_2, initial_battery_id, \
    simulated_battery_label, \
    simulated_switch_soc, simulated_before_switch_current, simulated_after_switch_current = battery_initial_info(battery_info=simulated_selected_battery)

    branch_circuit_current_limit = swap_initial_info(battery_info=battery_info)

    if operation_type == 'optimization':

        # queue_pre = Manager().Queue()
        # queue_post = Manager().Queue()
        # 对于所有case开启进程池，准备多进程异步计算
        # pool = Pool()
        # 所有仓的input case 的list
        # all_slot_input_data_list = []

        for slot_id, single_battery_info in optimization_range.items():
            # 新版本在送入仿真模块前，需要把switch_soc等信息转化为对应仓的dict
            switch_soc_array = single_battery_info['switch_soc_array']
            before_switch_current_array = single_battery_info['before_switch_current_array']
            after_switch_current_array = single_battery_info['after_switch_current_array']
            
            # 缓存迭代的case
            simulated_switch_soc_list = []
            simulated_before_switch_current_list = []
            simulated_after_switch_current_list = []
            # 当前简易版本，所有仓位case都是一样的
            for switch_soc in switch_soc_array:
                for before_switch_current in before_switch_current_array:
                    for after_switch_current in after_switch_current_array:
                        # 每个仓位都是一样的参数当前
                        # for k in simulated_switch_soc.keys():
                        simulated_switch_soc[slot_id] = switch_soc
                        simulated_before_switch_current[slot_id] = before_switch_current
                        simulated_after_switch_current[slot_id] = after_switch_current

                        valid_flag = is_vaild_charging_case(initial_status_flex_charging=initial_status_flex_charging, 
                                                            slot_id=slot_id,
                                                            simulated_before_switch_current=simulated_before_switch_current,
                                                            simulated_after_switch_current=simulated_after_switch_current)
                        # 如果不合法，不放入计算case
                        if valid_flag == 0:
                            continue
                        # 放入计算，同时对应的case只计算对应仓的仿真
                        else:
                            # 在这里判断不了是否临仓有电池，但不影响：如果临仓没有电池，进行仿真
                            single_slot_simulated_switch_soc, single_slot_simulated_before_switch_current, \
                                single_slot_simulated_after_switch_current = single_slot_strategy_preparation(
                                    simulated_switch_soc=simulated_switch_soc,
                                    simulated_before_switch_current=simulated_before_switch_current, 
                                    simulated_after_switch_current=simulated_after_switch_current, slot_id=slot_id)

                        simulated_switch_soc_list.append(copy.deepcopy(single_slot_simulated_switch_soc))
                        simulated_before_switch_current_list.append(copy.deepcopy(single_slot_simulated_before_switch_current))
                        simulated_after_switch_current_list.append(copy.deepcopy(single_slot_simulated_after_switch_current))

            single_slot_initial_battery_temp, single_slot_initial_charging_current, single_slot_battery_type, \
                                single_slot_battery_capacity, single_slot_initial_status_flex_charging, single_slot_initial_battery_soc, \
                                single_slot_initial_battery_id, single_slot_simulated_battery_label = single_slot_simulation_preparation(
                                
                                    initial_battery_temp=initial_battery_temp, initial_charging_current=initial_charging_current, \
                                    battery_type=battery_type, battery_capacity=battery_capacity, \
                                    initial_status_flex_charging=initial_status_flex_charging, \
                                    initial_battery_soc=initial_battery_soc, \
                                    initial_battery_id=initial_battery_id, \
                                    simulated_battery_label=simulated_battery_label, \
                                    slot_id=slot_id
                                )
            
            # # 多进程计算前整合所有运行case
            # single_slot_input_data_list = multiprocess_simulation_data_preparation(
            #     switch_soc_array=simulated_switch_soc_list, 
            #     before_switch_current_array=simulated_before_switch_current_list, after_switch_current_array=simulated_after_switch_current_list,

            #     current_pre=single_slot_initial_charging_current, battery_capacity=single_slot_battery_capacity, 
            #     status_flex_charging_pre=single_slot_initial_status_flex_charging, init_soc=single_slot_initial_battery_soc,

            #     branch_circuit_current_limit=branch_circuit_current_limit,
            #     map_soc_cur=map_soc_cur, map_soc_vol=map_soc_vol,
            #     map_soc_temp_ocv=map_soc_temp_ocv, module_type=module_type,
            #     device_id=device_id, model_trigger_time=model_trigger_time, map_electricity_price=map_electricity_price,
            #     initial_battery_id=initial_battery_id, simulated_battery_label=simulated_battery_label
            # )
            # all_slot_input_data_list.extend(single_slot_input_data_list)

        # for single_case in all_slot_input_data_list:
        #     pool.apply_async(func=multiprocess_charging_mode_simulation_feedback_system, args=(single_case,),
        #                         callback=multiprocess_calculation_result)
        # pool.map_async(func=multiprocess_charging_mode_simulation_feedback_system, iterable=all_slot_input_data_list,
        #                     callback=multiprocess_calculation_result)

        # 等待各仓case全部加入异步计算后，等待进程结束
        # pool.close()
        # pool.join()

            charging_simulation_resu_list = charging_mode_simulation_feedback_system(
                switch_soc_array=simulated_switch_soc_list, 
                before_switch_current_array=simulated_before_switch_current_list, after_switch_current_array=simulated_after_switch_current_list,

                current_pre=single_slot_initial_charging_current, battery_capacity=single_slot_battery_capacity, 
                status_flex_charging_pre=single_slot_initial_status_flex_charging, init_soc=single_slot_initial_battery_soc,

                branch_circuit_current_limit=branch_circuit_current_limit,
                map_soc_cur=map_soc_cur, map_soc_vol=map_soc_vol,
                map_soc_temp_ocv=map_soc_temp_ocv, module_type=module_type,
                device_id=device_id, model_trigger_time=model_trigger_time, map_electricity_price=map_electricity_price,
                initial_battery_id=initial_battery_id, simulated_battery_label=simulated_battery_label,
                system_mode='running'
                )
            
            # 将每个仓的仿真结果放入list
            total_charging_simulation_resu_list.append(pd.DataFrame(charging_simulation_resu_list)[[slot_id]])

    elif operation_type == 'challenge':
        # 如果某块电池为第一次优化，在进行挑战时，上一次对应电池优化结果可能为空
        for k in simulated_switch_soc.keys():
            if simulated_switch_soc[k] == None or simulated_switch_soc[k] == 0:
                simulated_switch_soc[k] = 0
            if simulated_before_switch_current[k] == None or simulated_before_switch_current[k] == 0:
                simulated_before_switch_current[k] = -220
            if simulated_after_switch_current[k] == None or simulated_after_switch_current[k] == 0:
                simulated_after_switch_current[k] = -220

        simulated_switch_soc_list = []
        simulated_before_switch_current_list = []
        simulated_after_switch_current_list = []

        simulated_switch_soc_list.append(simulated_switch_soc)
        simulated_before_switch_current_list.append(simulated_before_switch_current)
        simulated_after_switch_current_list.append(simulated_after_switch_current)

        charging_simulation_resu_list = charging_mode_simulation_feedback_system(
            switch_soc_array=simulated_switch_soc_list, 
            before_switch_current_array=simulated_before_switch_current_list, after_switch_current_array=simulated_after_switch_current_list,
            current_pre=initial_charging_current, battery_capacity=battery_capacity, 
            status_flex_charging_pre=initial_status_flex_charging, init_soc=initial_battery_soc,
            branch_circuit_current_limit=branch_circuit_current_limit,
            map_soc_cur=map_soc_cur, map_soc_vol=map_soc_vol,
            map_soc_temp_ocv=map_soc_temp_ocv, module_type=module_type,
            device_id=device_id, model_trigger_time=model_trigger_time, map_electricity_price=map_electricity_price,
            initial_battery_id=initial_battery_id, simulated_battery_label=simulated_battery_label,
            system_mode='running'
            )
        
        return charging_simulation_resu_list


    return total_charging_simulation_resu_list


def is_vaild_charging_case(initial_status_flex_charging: dict, slot_id: str,
                           simulated_before_switch_current: dict,
                           simulated_after_switch_current: dict):
    '''
    返回是否是合理case，比如3仓策略220A，但4仓也有电池在充电，不可能达到这个值
    任何一组的其中一个策略值不合法，整个case都不合法
    0 | 1
    '''
    valid_flag = 1
    slot_min_current = -110
    # 偶数
    if int(slot_id) % 2 == 0:
        # 如果临仓也在dict中
        if int(initial_status_flex_charging.get(str(int(slot_id)-1).rjust(2, '0'), 2)) != 2:
            # 如果case的电流值 <  非柔性限制 (电流为负，取大于)
            if simulated_before_switch_current[slot_id] < slot_min_current or \
                simulated_after_switch_current[slot_id] < slot_min_current:
                valid_flag = 0
                
        # 如果不在dict中，说明没有电池 / 不充 / 充满
        else:
            slot_min_current = -220
            # 如果case的电流值 <  非柔性限制 (电流为负，取大于)
            if simulated_before_switch_current[slot_id] < slot_min_current or \
                simulated_after_switch_current[slot_id] < slot_min_current:
                valid_flag = 0

    # 奇数
    elif int(slot_id) % 2 != 0 and slot_id != '13':
        if int(initial_status_flex_charging.get(str(int(slot_id)+1).rjust(2, '0'), 2)) != 2:
            # 如果case的电流值 <  非柔性限制 (电流为负，取大于)
            if simulated_before_switch_current[slot_id] < slot_min_current or \
                simulated_after_switch_current[slot_id] < slot_min_current:
                valid_flag = 0

        # 如果不在dict中，说明没有电池 / 不充 / 充满
        else:
            slot_min_current = -220
            # 如果case的电流值 <  非柔性限制 (电流为负，取大于)
            if simulated_before_switch_current[slot_id] < slot_min_current or \
                simulated_after_switch_current[slot_id] < slot_min_current:
                valid_flag = 0
    # 13仓
    else:
        if simulated_before_switch_current[slot_id] < slot_min_current or \
                simulated_after_switch_current[slot_id] < slot_min_current:
                valid_flag = 0


    return valid_flag


def single_slot_strategy_preparation(simulated_switch_soc: dict,
    simulated_before_switch_current: dict, simulated_after_switch_current: dict, slot_id: str):
    '''
    加入相邻仓位电池进入仿真
    '''

    if int(slot_id) % 2 == 0:
        neighbour_slot_id = str(int(slot_id)-1).rjust(2, '0')

    elif int(slot_id) % 2 != 0 and slot_id != '13':
        neighbour_slot_id = str(int(slot_id)+1).rjust(2, '0')

    else:
        neighbour_slot_id = -1
    
    single_slot_simulated_switch_soc = {}
    single_slot_simulated_before_switch_current = {}
    single_slot_simulated_after_switch_current = {}

    single_slot_simulated_switch_soc[slot_id] = copy.deepcopy(simulated_switch_soc)[slot_id]
    single_slot_simulated_before_switch_current[slot_id] = copy.deepcopy(simulated_before_switch_current)[slot_id]
    single_slot_simulated_after_switch_current[slot_id] = copy.deepcopy(simulated_after_switch_current)[slot_id]

    # 相邻仓位 加入
    if neighbour_slot_id != -1:
        single_slot_simulated_switch_soc[neighbour_slot_id] = single_slot_simulated_switch_soc[slot_id]
        single_slot_simulated_before_switch_current[neighbour_slot_id] = single_slot_simulated_before_switch_current[slot_id]
        single_slot_simulated_after_switch_current[neighbour_slot_id] = single_slot_simulated_after_switch_current[slot_id]


    return single_slot_simulated_switch_soc, single_slot_simulated_before_switch_current, \
        single_slot_simulated_after_switch_current


def single_slot_simulation_preparation(
        initial_battery_temp: dict, initial_charging_current: dict, battery_type: dict, battery_capacity: dict, \
        initial_status_flex_charging: dict, initial_battery_soc: dict, \
        initial_battery_id: dict, \
        simulated_battery_label: dict, \
        slot_id: str):
    '''
    单独仿真某一仓的数据处理
    '''

    if int(slot_id) % 2 == 0:
        neighbour_slot_id = str(int(slot_id)-1).rjust(2, '0')

    elif int(slot_id) % 2 != 0 and slot_id != '13':
        neighbour_slot_id = str(int(slot_id)+1).rjust(2, '0')

    else:
        neighbour_slot_id = -1

    single_slot_initial_battery_temp = {}
    single_slot_initial_charging_current = {}
    single_slot_battery_type = {}
    single_slot_battery_capacity = {}
    single_slot_initial_status_flex_charging = {}
    single_slot_initial_battery_soc = {}
    single_slot_initial_battery_id = {}
    single_slot_simulated_battery_label = {}

    single_slot_initial_battery_temp[slot_id] = copy.deepcopy(initial_battery_temp)[slot_id]
    single_slot_initial_charging_current[slot_id] = copy.deepcopy(initial_charging_current)[slot_id]
    single_slot_battery_type[slot_id] = copy.deepcopy(battery_type)[slot_id]
    single_slot_battery_capacity[slot_id] = copy.deepcopy(battery_capacity)[slot_id]
    single_slot_initial_status_flex_charging[slot_id] = copy.deepcopy(initial_status_flex_charging)[slot_id]
    single_slot_initial_battery_soc[slot_id] = copy.deepcopy(initial_battery_soc)[slot_id]
    single_slot_initial_battery_id[slot_id] = copy.deepcopy(initial_battery_id)[slot_id]
    single_slot_simulated_battery_label[slot_id] = copy.deepcopy(simulated_battery_label)[slot_id]

    if neighbour_slot_id != -1:
        if initial_charging_current.get(neighbour_slot_id):

            single_slot_initial_battery_temp[neighbour_slot_id] = copy.deepcopy(initial_battery_temp)[neighbour_slot_id]
            single_slot_initial_charging_current[neighbour_slot_id] = copy.deepcopy(initial_charging_current)[neighbour_slot_id]
            single_slot_battery_type[neighbour_slot_id] = copy.deepcopy(battery_type)[neighbour_slot_id]
            single_slot_battery_capacity[neighbour_slot_id] = copy.deepcopy(battery_capacity)[neighbour_slot_id]
            single_slot_initial_status_flex_charging[neighbour_slot_id] = copy.deepcopy(initial_status_flex_charging)[neighbour_slot_id]
            single_slot_initial_battery_soc[neighbour_slot_id] = copy.deepcopy(initial_battery_soc)[neighbour_slot_id]
            single_slot_initial_battery_id[neighbour_slot_id] = copy.deepcopy(initial_battery_id)[neighbour_slot_id]
            single_slot_simulated_battery_label[neighbour_slot_id] = copy.deepcopy(simulated_battery_label)[neighbour_slot_id]



    return single_slot_initial_battery_temp, single_slot_initial_charging_current, single_slot_battery_type, \
        single_slot_battery_capacity, single_slot_initial_status_flex_charging, single_slot_initial_battery_soc, \
        single_slot_initial_battery_id, single_slot_simulated_battery_label


def price_coefficient(map_price: dict, electricity_price_model: str):
    """
    电价系数
    :param device_id: 设备ID
    :return: 峰谷平尖电价
    """
    # price_list = pd.read_csv('./Data/optimization/electricity_price.csv')
    # try:
    #     with open('./Mapping/map_device_electricity_price.json', 'r', encoding='utf-8') as fp:
    #         map_device_electricity_price = json.loads(fp.read())
    # except Exception as err:
    #     print(err)
    if electricity_price_model == 'one_price':
        price_v, price_f, price_p, price_t = 0.843493, 0.843493, 0.843493, 0.843493

    else:
        if map_price.get('valley'):
            price_v = map_price['valley']
        else:
            price_v = 0.309759

        if map_price.get('flat'):
            price_f = map_price['flat']
        else:
            price_f = 0.576626

        if map_price.get('peak'):
            price_p = map_price['peak']
        else:
            price_p = 0.843493

        if map_price.get('tip'):
            price_t = map_price['tip']
        else:
            price_t = 1.043643


    return price_v, price_f, price_p, price_t


def queue_model(q_lambda, miu):
    """
    M/M/S/k排队模型
    :param q_lambda: 平均到达率 辆/小时
    :param miu: 服务能力 辆/小时
    :return: 逗留时间的期望， 排队时间期望
    """
    # def queue_model(df):
    # q_lambda = df['q_lambda']  # 平均到达率 辆/小时
    # miu = df['miu']  # 服务能力 辆/小时
    q_lambda = max(0.1, q_lambda)
    rho = q_lambda / miu  # 服务强度
    queue_limit = QUEUE_LIMIT
    if rho == 1: 
        # P0 = 1 / (k + 1)  # 在统计平衡时，系统中有0个顾客的概率。
        probability_k = 1 / (queue_limit + 1)  # 在统计平衡时，系统中有j个顾客的概率。
        expect_length = queue_limit / 2  # 队长期望
        expect_queue_length = queue_limit * (queue_limit - 1) / (2 * (queue_limit + 1))  # 等待队长的期望
    else:
        probability_k = rho ** queue_limit * (1 - rho) / (1 - rho ** (queue_limit + 1))
        # expect_length = (rho * (1 - (queue_limit + 1) * rho ** queue_limit) + queue_limit * rho ** (queue_limit + 1)) \
        #                 / (1 - rho) / (1 - rho ** (queue_limit + 1))
        expect_length = rho / (1 - rho) - \
                        (queue_limit + 1) * rho ** (queue_limit + 1) / (1 - rho ** (queue_limit + 1))
        expect_queue_length = rho ** 2 / (1 - rho) - \
                              (queue_limit + rho) * rho ** (queue_limit + 1) / (1 - rho ** (queue_limit + 1))

    expect_wait_time = expect_length / (q_lambda * (1 - probability_k)) * 60  # 逗留时间的期望
    expect_queue_time = expect_queue_length / (q_lambda * (1 - probability_k)) * 60  # 排队时间的期望


    return expect_wait_time, expect_queue_time


def calculate_lambda(map: dict, hour):
    """
    计算排队平均到达率 辆/小时
    :param hour: 小时
    :return: 某设备的某小时到达数量
    """
    # 如何感知当前小时段已经完成了多少单量 # todo
    # queue_num = map[(map['device_id'] == device_id) & (map['hour'] == hour)]
    
    if map.get(str(hour)):
        q_lambda = map[str(hour)]
    else:
        q_lambda = DEFAULT_SERVICE_NUM


    return q_lambda


def calculate_miu(df: pd.DataFrame, name_list: list, battery_ready_num: int):
    """
    计算服务能力 辆/小时
    :param df:
    :param name_list: 有充电的仓号
    :param num_battery_ready: 满电电池数量
    :return: 服务能力
    """
    # 充满所需时长
    col_list = ['simulated_total_time' + x for x in name_list]
    # 平均单位时间内
    miu = 3600 / (df[col_list].sum(axis=1) / len(col_list)) * len(col_list) + battery_ready_num


    return miu


def health_model():
    """
    电池健康度模型
    :return: 健康度情况
    """
    # TODO 待电池组给出
    return random.randrange(80, 100) / 100


def charging_simu_single_slot(charging_df: pd.DataFrame, slot: str, map_device_electricity_price: dict,
                              electricity_price_model: str):
    """
    单仓情况解析
    :param charging_df: 仿真所有仓情况
    :param slot: 仓号
    :return: 解析后的单仓情况
    """
    # slot = '02'
    # charging_uni是单仓的情况，所有仿真case
    price_v, price_f, price_p, price_t = price_coefficient(map_device_electricity_price,
                                                           electricity_price_model=electricity_price_model)

    df_charging_single_slot = charging_df[[slot]][charging_df[[slot]][slot].notna()]

    df_charging_single_slot['charging_mode'] = df_charging_single_slot.apply(
        lambda x: x[slot][0]['simulated_charging_mode'], axis=1)
    df_charging_single_slot['current_battery_real_soc'] = df_charging_single_slot.apply(
        lambda x: x[slot][0]['current_battery_real_soc'], axis=1)
    df_charging_single_slot['simulated_switch_soc'] = df_charging_single_slot.apply(
        lambda x: x[slot][0]['simulated_switch_soc'], axis=1)
    df_charging_single_slot['simulated_switch_moment'] = df_charging_single_slot.apply(
        lambda x: x[slot][0]['simulated_switch_moment'], axis=1)
    df_charging_single_slot['before_switch_current'] = df_charging_single_slot.apply(
        lambda x: x[slot][0]['simulated_before_switch_current'], axis=1)
    df_charging_single_slot['after_switch_current'] = df_charging_single_slot.apply(
        lambda x: x[slot][0]['simulated_after_switch_current'], axis=1)
    df_charging_single_slot['battery_id'] = df_charging_single_slot.apply(lambda x: x[slot][0]['battery_id'], axis=1)
    # df_charging_single_slot['simulated_eta_battery'] = df_charging_single_slot.apply(lambda x: x[slot][0]['simulated_eta_battery'], axis=1)
    # df_charging_single_slot['simulated_eta_module'] = df_charging_single_slot.apply(lambda x: x[slot][0]['simulated_eta_module'], axis=1)
    df_charging_single_slot['simulated_module_output_power'] = df_charging_single_slot.apply(
        lambda x: x[slot][0]['simulated_module_output_power'], axis=1)
    df_charging_single_slot['simulated_interval_charging_time'] = df_charging_single_slot.apply(
        lambda x: x[slot][0]['simulated_interval_charging_time'], axis=1)
    df_charging_single_slot['simulated_interval_charging_energy'] = df_charging_single_slot.apply(
        lambda x: x[slot][0]['simulated_interval_charging_energy'], axis=1)
    df_charging_single_slot['simulated_valley_time'] = df_charging_single_slot.apply(
        lambda x: x['simulated_interval_charging_time'][0], axis=1)
    df_charging_single_slot['simulated_flat_time'] = df_charging_single_slot.apply(
        lambda x: x['simulated_interval_charging_time'][1], axis=1)
    df_charging_single_slot['simulated_peak_time'] = df_charging_single_slot.apply(
        lambda x: x['simulated_interval_charging_time'][2], axis=1)
    df_charging_single_slot['simulated_tip_time'] = df_charging_single_slot.apply(
        lambda x: x['simulated_interval_charging_time'][3], axis=1)
    df_charging_single_slot['simulated_total_time'] = df_charging_single_slot['simulated_tip_time'] + \
                                                      df_charging_single_slot['simulated_peak_time'] + \
                                                      df_charging_single_slot['simulated_flat_time'] + \
                                                      df_charging_single_slot['simulated_valley_time']

    # 至少3个方案，且最大充电时间不超过6h
    if (df_charging_single_slot['simulated_total_time'] < MAX_CHARGING_TIME).sum() > 3:
        df_charging_single_slot = df_charging_single_slot[
            df_charging_single_slot['simulated_total_time'] < MAX_CHARGING_TIME]  # 删除超过6h的case

    df_charging_single_slot['simulated_valley_energy'] = df_charging_single_slot.apply(
        lambda x: x['simulated_interval_charging_energy'][0],
        axis=1)
    df_charging_single_slot['simulated_flat_energy'] = df_charging_single_slot.apply(
        lambda x: x['simulated_interval_charging_energy'][1],
        axis=1)
    df_charging_single_slot['simulated_peak_energy'] = df_charging_single_slot.apply(
        lambda x: x['simulated_interval_charging_energy'][2],
        axis=1)
    df_charging_single_slot['simulated_tip_energy'] = df_charging_single_slot.apply(
        lambda x: x['simulated_interval_charging_energy'][3],
        axis=1)
    df_charging_single_slot['cost'] = df_charging_single_slot.apply(lambda x:
                                                                    x['simulated_valley_energy'] * price_v +
                                                                    x['simulated_flat_energy'] * price_f +
                                                                    x['simulated_peak_energy'] * price_p +
                                                                    x['simulated_tip_energy'] * price_t, axis=1)

    df_charging_single_slot['current_current'] = df_charging_single_slot.apply(lambda x: x['after_switch_current'] \
        if x['simulated_switch_soc'] < x['current_battery_real_soc'] \
        else x['before_switch_current'], axis=1)

    df_charging_single_slot.sort_values(by='cost', inplace=True)
    df_charging_single_slot.reset_index(drop=True, inplace=True)
    # df_charging_single_slot.drop([slot, 'simulated_interval_charging_time'], axis=1, inplace=True)

    return df_charging_single_slot


def eval_model(cost: float, queue_time: float, health: float):
    """
    评价模型
    :param cost: 充电花费
    :param queue_time: 排队时长
    :param health: 健康度
    :return: 加权后的得分
    """
    # 排队时长越长，惩罚分越高
    # 多目标评价对应的决策变量已经转换为归一化后数值
    # weight_c, weight_q, weight_h = 1, 2, -3  # TODO 各部分权重
    weight_c, weight_q = 0.35, 0.65
    # penalty = cost * weight_c + queue_time * weight_q + health * weight_h
    penalty = cost * weight_c + queue_time * weight_q


    return penalty


def collect_pool(df_charging_single_slot: pd.DataFrame):
    """
    候选池抽样
    :param df_charging_single_slot: 单仓充电情况
    :return: 抽样后的单仓充电情况
    """

    # 基于正序的方案组合，头部和尾部对应每个仓都是最优和最优，最差和最差
    # 基于大样本随机的组合，头部尾部逻辑不变，中间80%数据增大并随机打乱
    df_filtered_candidate_pool = copy.deepcopy(df_charging_single_slot)
    if len(df_charging_single_slot) > CANDIDATE_VOLUME:
        df_filtered_candidate_pool = df_filtered_candidate_pool.sample(n=int(CANDIDATE_VOLUME))
    else:
        df_filtered_candidate_pool = df_filtered_candidate_pool.sample(n=int(CANDIDATE_VOLUME), replace=True)

    best_candidate = df_filtered_candidate_pool.iloc[:int(CANDIDATE_VOLUME*HEAD_TAIL_SAMPLE_RATIO*BEST_CASE_RATIO)].\
        sort_values(by='cost').reset_index(drop=True)
    worst_candidate = df_filtered_candidate_pool.iloc[-int(CANDIDATE_VOLUME*HEAD_TAIL_SAMPLE_RATIO*(WORST_CASE_RATIO)):].\
        sort_values(by='cost').reset_index(drop=True)

    df_filtered_candidate_pool = pd.concat(
        [
            best_candidate, 
            df_filtered_candidate_pool.sample(n=int(CANDIDATE_VOLUME*SAMPLE_RATIO)), 
            worst_candidate
            ]
        ).reset_index(drop=True)
        # sort_values(by='cost').reset_index(drop=True)


    return df_filtered_candidate_pool


def adaptive_opt(step: int, left: int, right: int, df_case_pool_merge: pd.DataFrame,
                 #  q_map, device_id, col_name_list, num_battery_ready
                 ):
    """
    自适应步长迭代
    :param step: 步长
    :param left: 左侧开始
    :param right: 右侧结束
    :param df_case_pool_merge: 候选池
    :param q_map: 排队历史表
    :param device_id: 设备id
    :param col_name_list: 需要的column
    :param num_battery_ready: 充满的电池数量
    :return: 最优结果
    """
    # 计算当前样本下最大值、最小值、平均值
    if right == len(df_case_pool_merge):
        iter = range(left, right, step)
    else:
        iter = range(left, right + step, step)

    i = 0
    if step == 1:
        for ind, line in df_case_pool_merge.loc[iter].iterrows():
            # # 单位时间内到达的排队人数（换电订单）
            # q_lambda = calculate_lambda(q_map, device_id, time.localtime().tm_hour)
            # # 单位时间内服务的人数（换电订单）
            # miu = calculate_miu(line, col_name_list, num_battery_ready)
            # queue_time = queue_model(q_lambda, miu)
            i += 1
            penalty = eval_model(line['cost_norm'], line['expect_queue_time_norm'], line['health'])
            # mark = eval_model(line['cost'], queue_time[1], line['health'])
            # print(ind, line['queueing_model_results'][1], penalty)
            if line['queueing_model_results'][1] < QUEUE_TIME_LIMIT and penalty < PENALTY:
                # print(iter, 'total_penalty', penalty, 'cost score: ', line['cost_norm'], 'queue time score: ', line['expect_queue_time_norm'])
                # print(iter, 'cost score: ', line['cost'], 'queue time score: ', line['queueing_model_results'][1])

                return line

            # if i == len(df_case_pool_merge.loc[iter]):

            #     return df_case_pool_merge.iloc[-1, :]

    else:
        # 每次迭代，选择iter中step的每一个
        for ind, line in df_case_pool_merge.loc[iter].iterrows():
            # 多目标评价
            penalty = eval_model(line['cost_norm'], line['expect_queue_time_norm'], line['health'])
            # 记录区间
            i += 1
            # print(i, 'cost score: ', line['cost_norm'], 'queue time score: ', line['expect_queue_time_norm'])
            # print(ind, line['queueing_model_results'][1], penalty)
            if line['queueing_model_results'][1] < QUEUE_TIME_LIMIT and penalty < PENALTY:
                # decision_resu = line
                if i < 2:
                    left = iter[0]
                    right = iter[1]
                else:
                    left = iter[i - 2]
                    right = iter[i - 1]
                # return adaptive_opt(int(step / 10), left, right, df_case_pool_merge, q_map, device_id, col_name_list,
                #                     num_battery_ready)
                return adaptive_opt(int(step / 10), left, right, df_case_pool_merge)

            if i == len(df_case_pool_merge.loc[iter]):
                # print('can not find the best strategy regarding to the queue time requirement')
                # i-1 到 i之前的case，cost升序，但penalty不一定按照升序排序，刻度上对应不满足的case，在2个刻度之间会有满足的case

                # return adaptive_opt(int(step / 10), left, right, df_case_pool_merge)
                return df_case_pool_merge.iloc[-1, :]


def conditional_optimization_battery_selection(data_struct: dict):
    '''
    分为3个部分：优先电池，次优先电池，满电电池

    '''

    # try:
    #     with open('./Mapping/map_device_battery_type_hourly_service.json', 'r', encoding='utf-8') as fp:
    #         map_battery_type_history_lambda = json.loads(fp.read())
    # except Exception as err:
    #     print(err)
    map_battery_type_history_lambda = data_struct['device_history_info']['map_device_battery_type_hourly_service']

    device_id = data_struct['device_id']
    model_trigger_time = data_struct['model_trigger_time']

    df = pd.DataFrame(data_struct['battery_info']).sort_values('battery_real_soc', ascending=False)

    # battery_info_list = data_struct['battery_info'].sort(key=lambda x: x['battery_real_soc'], reverse=True)
    df_basic_info_batt_charged = df[(df['battery_user_soc'] >= CHARGING_FINISH_SOC) & (df['charging_status'] == 2)\
            ].sort_values('battery_real_soc', ascending=False)
    # 这个过程中算上了非满电电池未开始充电
    df_basic_info_batt_charging = df[~((df['battery_user_soc'] >= CHARGING_FINISH_SOC) & (df['charging_status'] == 2))\
            ].sort_values('battery_real_soc', ascending=False)
    
    # 这个参数更新为错峰小时段电池需求数量
    # priority_70_charging_num, priority_100_charging_num = PRIORITY_70_CHARGING_NUM, PRIORITY_100_CHARGING_NUM
    priority_70_charging_num, priority_100_charging_num = data_struct["battery_demand"][0]['70'], data_struct["battery_demand"][0]['100']

    # 当前小时段
    charging_start_hour = datetime.fromtimestamp(model_trigger_time/1000).hour
    # 如果小时段对应需求量 大于 N倍 历史订单量，说明是夜间准备电池阶段
    history_70_service_num = map_battery_type_history_lambda['70kWh'].get(str(charging_start_hour), 0)
    history_100_service_num = map_battery_type_history_lambda['100kWh'].get(str(charging_start_hour), 0)

    if priority_70_charging_num > SERVICE_DIFF_MUTIPLE * history_70_service_num:
        priority_70_charging_num = history_70_service_num

    if priority_100_charging_num > SERVICE_DIFF_MUTIPLE * history_100_service_num:
        priority_100_charging_num = history_100_service_num

    prior_charging_slot_id = []
    non_prior_charging_slot_id = []
    fully_charged_slot_id = []
    prior_charging_70_slot_id = []
    non_prior_charging_70_slot_id = []
    fully_charged_70_slot_id = []
    prior_charging_100_slot_id = []
    non_prior_charging_100_slot_id = []
    fully_charged_100_slot_id = []

    # 先检查满电电池
    for ind, row in df_basic_info_batt_charged.iterrows():
        if row['battery_real_soc'] is None:
            continue
        
        # 后续改为用用户soc 93
        # soc降序排序，无论是正在充电还是满电，都考虑到优先电池中
        if row['battery_type'] in [1, 3, 5, 7, 8, 11, 14]:
            fully_charged_slot_id.append(str(row['slot_id']).rjust(2, '0'))
            fully_charged_70_slot_id.append(str(row['slot_id']).rjust(2, '0'))
            priority_70_charging_num -= 1

        elif row['battery_type'] in [6, 13]:
            fully_charged_slot_id.append(str(row['slot_id']).rjust(2, '0'))
            fully_charged_100_slot_id.append(str(row['slot_id']).rjust(2, '0'))
            priority_100_charging_num -= 1

        else:
            fully_charged_slot_id.append(str(row['slot_id']).rjust(2, '0'))
            fully_charged_70_slot_id.append(str(row['slot_id']).rjust(2, '0'))
            priority_70_charging_num -= 1

    # 如果满电电池还不满足优先需要，则再继续挑选充电电池，直到满足为止
    for ind, row in df_basic_info_batt_charging.iterrows():
        if row['battery_real_soc'] is None:
            continue
        
        if row['battery_user_soc'] < CHARGING_FINISH_SOC and row['charging_status'] != 2:
            if row['battery_type'] in [1, 3, 5, 7, 8, 11, 14]:
                if priority_70_charging_num > 0:
                    prior_charging_slot_id.append(str(row['slot_id']).rjust(2, '0'))
                    prior_charging_70_slot_id.append(str(row['slot_id']).rjust(2, '0'))
                    priority_70_charging_num -= 1
                else:
                    non_prior_charging_slot_id.append(str(row['slot_id']).rjust(2, '0'))
                    non_prior_charging_70_slot_id.append(str(row['slot_id']).rjust(2, '0'))

            elif row['battery_type'] in [6, 13]:
                if priority_100_charging_num > 0:
                    prior_charging_slot_id.append(str(row['slot_id']).rjust(2, '0'))
                    prior_charging_100_slot_id.append(str(row['slot_id']).rjust(2, '0'))
                    priority_100_charging_num -= 1
                else:
                    non_prior_charging_slot_id.append(str(row['slot_id']).rjust(2, '0'))
                    non_prior_charging_100_slot_id.append(str(row['slot_id']).rjust(2, '0'))

            else:
                if priority_70_charging_num > 0:
                    prior_charging_slot_id.append(str(row['slot_id']).rjust(2, '0'))
                    prior_charging_70_slot_id.append(str(row['slot_id']).rjust(2, '0'))
                    priority_70_charging_num -= 1
                else:
                    non_prior_charging_slot_id.append(str(row['slot_id']).rjust(2, '0'))
                    non_prior_charging_70_slot_id.append(str(row['slot_id']).rjust(2, '0'))

        # 未满电但同时没有充电的电池
        else:
            if row['battery_type'] in [1, 3, 5, 7, 8, 11, 14]:
                non_prior_charging_slot_id.append(str(row['slot_id']).rjust(2, '0'))
                non_prior_charging_70_slot_id.append(str(row['slot_id']).rjust(2, '0'))

            elif row['battery_type'] in [6, 13]:
                non_prior_charging_slot_id.append(str(row['slot_id']).rjust(2, '0'))
                non_prior_charging_100_slot_id.append(str(row['slot_id']).rjust(2, '0'))

            else:
                non_prior_charging_slot_id.append(str(row['slot_id']).rjust(2, '0'))
                non_prior_charging_70_slot_id.append(str(row['slot_id']).rjust(2, '0'))
    
    
    return prior_charging_slot_id, non_prior_charging_slot_id, fully_charged_slot_id, \
        len(fully_charged_70_slot_id), len(fully_charged_100_slot_id)


def decision_model(charging_df: list, data_struct: dict,
                   battery_ready_num: int, prior_charging_slot_id: list, non_prior_charging_slot_id: list,
                   fully_charged_slot_id: list,
                   operation_type='optimization'):
    """
    决策模型
    :param charging_df: 仿真充电数据
    :return: 决策结果
    """

    # 读取历史小时单量
    # 顾客平均到达率（单/时）
    map_history_lambda = data_struct['device_history_info']['map_device_hourly_service']
    # 读取历史站点电价
    map_device_electricity_price = data_struct['device_history_info']['map_device_electricity_price']

    electricity_price_model = data_struct['device_history_info']['electricity_price_model']

    device_id = data_struct['device_id']
    model_trigger_time = data_struct['model_trigger_time']
    # charging_df = pd.DataFrame(charging_df)

    df_case_pool_merge = pd.DataFrame()
    # dataframe的column名称改名
    col_name_old, col_name_new = [], []

    if len(prior_charging_slot_id) < 1:
        pass
    else:
        i = 0
        # 需要条件约束下的优化来自优先充电队列中的电池
        for col in prior_charging_slot_id:  # 根据起始SOC、
            # if col not in charging_df.columns:
            #     continue
            for charging_simulation in charging_df:
                if col == charging_simulation.columns.values[0]:
                    # 单仓充电数据，dataframe
                    df_charging_single_slot = charging_simu_single_slot(charging_df=charging_simulation, slot=col,
                                                                        map_device_electricity_price=map_device_electricity_price,
                                                                        electricity_price_model=electricity_price_model)
                    # 单仓候选池抽样
                    df_filtered_candidate_pool = collect_pool(df_charging_single_slot)

                    # 用于每个仓merge时的cross join
                    # df_filtered_candidate_pool['value'] = 1
                    if i == 0:
                        df_case_pool_merge = df_filtered_candidate_pool
                        col_name_old = df_filtered_candidate_pool.columns
                        col_name_new = [x + col for x in df_filtered_candidate_pool.columns]
                        df_case_pool_merge.rename(dict(zip(col_name_old, col_name_new)), axis=1, inplace=True)
                    else:
                        # df_case_pool_merge = df_case_pool_merge.merge(df_filtered_candidate_pool, how='left', on='value', suffixes=('', col))
                        col_name_old = df_filtered_candidate_pool.columns
                        col_name_new = [x + col for x in df_filtered_candidate_pool.columns]
                        df_filtered_candidate_pool.rename(dict(zip(col_name_old, col_name_new)), axis=1, inplace=True)
                        df_case_pool_merge = pd.concat([df_case_pool_merge, df_filtered_candidate_pool], axis=1)
                    # col_name_list.append(col)

                    i += 1
                    break

        # 求所有仓位cost的平均值，虚拟电费
        df_case_pool_merge['cost'] = df_case_pool_merge[['cost' + x for x in prior_charging_slot_id]].sum(axis=1)
        # 暂时没有health model的引入
        df_case_pool_merge['health'] = 1

        # 进入自适应优化模块之前，计算df_case_pool_merge对应cost queue_time health的最大最小和平均值
        # 排队模型M/M/1/k
        # todo: 如果在某类电池待服务电池数量为0的时候，切换到单类电池的排队
        # 单位时间内到达的排队人数（换电订单）
        df_case_pool_merge['lambda'] = calculate_lambda(map=map_history_lambda,
                                                        hour=datetime.fromtimestamp(model_trigger_time / 1000).hour)
        # 单位时间内服务的人数（换电订单）
        df_case_pool_merge['miu'] = calculate_miu(df=df_case_pool_merge, name_list=prior_charging_slot_id,
                                                  battery_ready_num=battery_ready_num)

        # 计算每一个case的排队模型结果，expect_wait_time, expect_queue_time
        df_case_pool_merge['queueing_model_results'] = df_case_pool_merge.apply(
            lambda x: queue_model(x['lambda'], x['miu']), axis=1)

        # 计算所有case的max，min
        alpha_cost = df_case_pool_merge['cost'].max() - df_case_pool_merge['cost'].min() + 0.1
        beta_cost = df_case_pool_merge['cost'].min()

        # alpha_health = df_case_pool_merge['health'].max() - df_case_pool_merge['health'].min()
        # beta_health = df_case_pool_merge['health'].min()

        alpha_expect_wait_time = df_case_pool_merge['queueing_model_results'].apply(lambda x: x[0]).max() - \
                                 df_case_pool_merge['queueing_model_results'].apply(lambda x: x[0]).min() + 0.1
        beta_expect_wait_time = df_case_pool_merge['queueing_model_results'].apply(lambda x: x[0]).min()

        alpha_expect_queue_time = df_case_pool_merge['queueing_model_results'].apply(lambda x: x[1]).max() - \
                                  df_case_pool_merge['queueing_model_results'].apply(lambda x: x[1]).min() + 0.1
        beta_expect_queue_time = df_case_pool_merge['queueing_model_results'].apply(lambda x: x[1]).min()

        # with open("./Data/optimization/tmp_decision_variables.pkl", "wb") as fp:
        #     pickle.dump([alpha_cost, beta_cost, alpha_expect_queue_time, beta_expect_queue_time], fp)

        # 计算所有case的归一化值
        df_case_pool_merge['cost_norm'] = df_case_pool_merge['cost'].apply(lambda x: (x - beta_cost) / (alpha_cost))
        # df_case_pool_merge['health_norm'] = df_case_pool_merge['health'].apply(lambda x: (x - beta_health) / alpha_health)
        df_case_pool_merge['expect_wait_time_norm'] = df_case_pool_merge['queueing_model_results'].apply(
            lambda x: (x[0] - beta_expect_wait_time) / alpha_expect_wait_time)
        df_case_pool_merge['expect_queue_time_norm'] = df_case_pool_merge['queueing_model_results'].apply(
            lambda x: (x[1] - beta_expect_queue_time) / alpha_expect_queue_time)

        df_case_pool_merge.sort_values(by=['cost_norm', 'health'], ascending=[True, False])

        left = 0
        right = len(df_case_pool_merge)
        # 根据数量，确定最开始的步长
        step = int(''.join(['1' if i == 0 else '0' for i in range(max(len(str(right - left)) - 1, 1))]))

        # 递归调用自适应优化模块，每次调用缩小搜索范围
        decision_resu = adaptive_opt(step=step, left=left, right=right, df_case_pool_merge=df_case_pool_merge)

    resu_all = []
    # 获取对应仓位的优化充电时长，目的用于比较是否需要切换
    # 更新为使用switch_soc
    # 组装候选池中的电池
    if len(prior_charging_slot_id) < 1:
        pass
    else:
        for slot in prior_charging_slot_id:
            resu = {
                # 'device_id': device_id,
                # 'model_trigger_time': data_struct['model_trigger_time'],
                'slot_id': slot,
                'battery_id': decision_resu[f'battery_id{slot}'],
                'charging_mode': decision_resu[f'charging_mode{slot}'],
                # 'battery_real_soc': decision_resu[f'current_battery_real_soc{slot}'],
                'switch_soc': decision_resu[f'simulated_switch_soc{slot}'],
                'switch_moment': decision_resu[f'simulated_switch_moment{slot}'],
                'before_switch_current': decision_resu[f'before_switch_current{slot}'],
                'after_switch_current': decision_resu[f'after_switch_current{slot}'],
                'current_current': decision_resu[f'current_current{slot}']}
            resu_all.append(resu)

    # 组装候选池外的电池
    # 次优先电池
    if len(non_prior_charging_slot_id) < 1:
        pass
    else:
        # for slot in non_prior_charging_slot_id:
        for charging_simulation in charging_df:
            # 若为非优先充电电池

            if charging_simulation.columns.values[0] in non_prior_charging_slot_id:
                # 单仓的充电模拟按照最低cost排序
                df_charging_single_slot = charging_simu_single_slot(charging_simulation,
                                                                    charging_simulation.columns.values[0],
                                                                    map_device_electricity_price=map_device_electricity_price,
                                                                    electricity_price_model=electricity_price_model)
                # df_charging_single_slot = charging_simu_single_slot(charging_df, slot)

                # 若时间是在夜间24点至4点
                # 电池保养优先级 高于 节能
                if (datetime.fromtimestamp(
                        data_struct['model_trigger_time'] / 1000).hour >= BATTERY_MAINTENANCE_START_HOUR and \
                        datetime.fromtimestamp(
                            data_struct['model_trigger_time'] / 1000).hour <= BATTERY_MAINTENANCE_END_HOUR
                ):
                    # and df_charging_single_slot.loc[0, 'current_battery_real_soc'] > BATTERY_MAINTENANCE_START_REAL_SOC:

                    df_charging_single_slot = df_charging_single_slot[
                        df_charging_single_slot['current_current'] == BATTERY_MAINTENANCE_CHARGING_CURRENT].reset_index(
                        drop=True)

                resu = {
                    # 'device_id': device_id,
                    # 'model_trigger_time': data_struct['model_trigger_time'],
                    'slot_id': charging_simulation.columns.values[0],
                    'battery_id': df_charging_single_slot.loc[0, 'battery_id'],
                    'charging_mode': df_charging_single_slot.loc[0, 'charging_mode'],
                    # 'battery_real_soc': df_charging_single_slot.loc[0, 'current_battery_real_soc'],
                    'switch_soc': df_charging_single_slot.loc[0, 'simulated_switch_soc'],
                    'switch_moment': df_charging_single_slot.loc[0, 'simulated_switch_moment'],
                    'before_switch_current': df_charging_single_slot.loc[0, 'before_switch_current'],
                    'after_switch_current': df_charging_single_slot.loc[0, 'after_switch_current'],
                    'current_current': df_charging_single_slot.loc[0, 'current_current']}
                resu_all.append(resu)

    return resu_all


def first_time_optimization_check(df: pd.DataFrame, data_struct: dict):
    
    for i in range(len(data_struct['battery_info'])):
        # 如果battery_id 不在数据库中，则属于第一次优化
        if data_struct['battery_info'][i]['battery_id'] not in df['battery_id'].tolist():
            data_struct['battery_info'][i]['optimization_label'] = 0
            data_struct['battery_info'][i]['pre_before_switch_current'] = None
            data_struct['battery_info'][i]['pre_switch_soc'] = None
            data_struct['battery_info'][i]['pre_after_switch_current'] = None
        else:
            data_struct['battery_info'][i]['optimization_label'] = 1
            # 同时将第一次优化结果放入data_struct中
            data_struct['battery_info'][i]['pre_before_switch_current'] = df[df['battery_id'] == 
                                                                                       data_struct['battery_info'][i]['battery_id']
                                                                                       ]['pre_before_switch_current'].values[0]
            data_struct['battery_info'][i]['pre_switch_soc'] = df[df['battery_id'] == 
                                                                                       data_struct['battery_info'][i]['battery_id']
                                                                                       ]['pre_switch_soc'].values[0]
            data_struct['battery_info'][i]['pre_after_switch_current'] = df[df['battery_id'] == 
                                                                                       data_struct['battery_info'][i]['battery_id']
                                                                                       ]['pre_after_switch_current'].values[0]


    return data_struct


def challenge_competition(pre_data_struct: dict, current_result: list, battery_ready_num: int,
                          prior_charging_slot_id: list, logger: Logger, relative_path: str):
    '''
    :param pre_data_struct: 上次优化的数据结构
    :param current_result: 当前优化的结果
    :param num_battery_ready: 当前待服务的电池数量
    :param prior_charging_slot_id: 优先充电的仓位

    单仓进行擂台赛制优化，若当前策略优于上次策略，则更新至当前对应仓位的策略
    若属于优先充电的电池，需要同时比较排队时长
    '''
    current_data_struct = copy.deepcopy(pre_data_struct)
    # device_id = current_data_struct['device_id']
    model_trigger_time = pre_data_struct['model_trigger_time']
    
    # 此处临时存储数据为归一化对应参数
    # with open("./Data/optimization/tmp_decision_variables.pkl", "rb") as fp:
    #     tmp_decision_variables = pickle.load(fp)

    map_history_lambda = pre_data_struct['device_history_info']['map_device_hourly_service']
    # 读取历史站点电价
    map_device_electricity_price = pre_data_struct['device_history_info']['map_device_electricity_price']
    electricity_price_model = pre_data_struct['device_history_info']['electricity_price_model']

    # 计算上次优化策略在当前调用下的结果
    pre_charging_simulation_resu = charging_simulation(logger=logger, data_struct=pre_data_struct, operation_type='challenge', relative_path=relative_path)

    # 计算当前优化策略在当前调用下的结果
    # 将current_data_struct中每个电池的优化策略结果进行更新
    for item in current_result:
        for i in range(len(current_data_struct['battery_info'])):
            if item['battery_id'] == current_data_struct['battery_info'][i]['battery_id']:
                current_data_struct['battery_info'][i]['pre_before_switch_current'] = item['before_switch_current']
                current_data_struct['battery_info'][i]['pre_switch_soc'] = item['switch_soc']
                current_data_struct['battery_info'][i]['pre_after_switch_current'] = item['after_switch_current']
                break

    current_charging_simulation_resu = charging_simulation(logger=logger, data_struct=current_data_struct, operation_type='challenge', relative_path=relative_path)

    # 只有在prior_charging_slot_id中的电池才会同时比较cost和queue_time
    # 其他电池仅比较cost
    # battery_info = current_data_struct['battery_info']
    
    for i in range(len(current_data_struct['battery_info'])):
        # 如果电池是第一次优化，直接应用对应电池优化结果
        if current_data_struct['battery_info'][i]['optimization_label'] == 0:
            # current_result中已有对应电池的优化结果
            # print(f"slot id {str(current_data_struct['battery_info'][i]['slot_id']).rjust(2, '0')}: current optimized result is the 1st time optimization")
            continue

        # 如果电池不是第一次优化，需要进行比较
        else:
            for j in range(len(current_result)):
                # 如果current_data_struct中的电池id和current_result中的电池id相同
                if current_result[j]['battery_id'] == current_data_struct['battery_info'][i]['battery_id']:
                    # 如果电池在prior_charging_slot_id中（代表优先充电）
                    if current_result[j]['slot_id'] in prior_charging_slot_id:
                        slot_id = current_result[j]['slot_id']
                        # 上一次结果
                        df_slot_pre_result = charging_simu_single_slot(charging_df=pd.DataFrame(pre_charging_simulation_resu), slot=slot_id,
                                                                       map_device_electricity_price=map_device_electricity_price,
                                                                       electricity_price_model=electricity_price_model)

                        df_slot_pre_result.rename(dict(zip(df_slot_pre_result.columns,
                                                           [x + slot_id for x in df_slot_pre_result.columns])), axis=1, inplace=True)
                        # 单位时间内到达的排队人数（换电订单）
                        df_slot_pre_result['lambda'] = calculate_lambda(map=map_history_lambda, hour=datetime.fromtimestamp(model_trigger_time/1000).hour)
                        # 单位时间内服务的人数（换电订单）
                        df_slot_pre_result['miu'] = calculate_miu(df=df_slot_pre_result, name_list=[slot_id], battery_ready_num=battery_ready_num)
                        # 计算每一个case的排队模型结果，expect_wait_time, expect_queue_time
                        df_slot_pre_result['queueing_model_results'] = df_slot_pre_result.apply(lambda x: queue_model(x['lambda'], x['miu']), axis=1)
                        # 计算所有case的归一化值
                        # df_slot_pre_result['cost_norm'] = df_slot_pre_result[f'cost{slot_id}'].apply(
                        #     lambda x: (x - tmp_decision_variables[1]) / tmp_decision_variables[0])
                        # df_slot_pre_result['expect_queue_time_norm'] = df_slot_pre_result['queueing_model_results'].apply(
                        #     lambda x: (x[1] - tmp_decision_variables[3]) / tmp_decision_variables[2])
                        # df_slot_pre_result['penalty'] = df_slot_pre_result.apply(lambda x: eval_model(x['cost_norm'], x['expect_queue_time_norm'], 1), axis=1)

                        # 当前结果
                        df_slot_current_result = charging_simu_single_slot(charging_df=pd.DataFrame(current_charging_simulation_resu), slot=slot_id,
                                                                           map_device_electricity_price=map_device_electricity_price,
                                                                           electricity_price_model=electricity_price_model)

                        df_slot_current_result.rename(dict(zip(df_slot_current_result.columns, 
                                                           [x + slot_id for x in df_slot_current_result.columns])), axis=1, inplace=True)
                        # 单位时间内到达的排队人数（换电订单）
                        df_slot_current_result['lambda'] = calculate_lambda(map=map_history_lambda, hour=datetime.fromtimestamp(model_trigger_time/1000).hour)
                        # 单位时间内服务的人数（换电订单）
                        df_slot_current_result['miu'] = calculate_miu(df=df_slot_current_result, name_list=[slot_id], battery_ready_num=battery_ready_num)
                        # 计算每一个case的排队模型结果，expect_wait_time, expect_queue_time
                        df_slot_current_result['queueing_model_results'] = df_slot_current_result.apply(lambda x: queue_model(x['lambda'], x['miu']), axis=1)
                        # 计算所有case的归一化值
                        # df_slot_current_result['cost_norm'] = df_slot_current_result[f'cost{slot_id}'].apply(
                        #     lambda x: (x - tmp_decision_variables[1]) / tmp_decision_variables[0])
                        # df_slot_current_result['expect_queue_time_norm'] = df_slot_current_result['queueing_model_results'].apply(
                        #     lambda x: (x[1] - tmp_decision_variables[3]) / tmp_decision_variables[2])
                        # df_slot_current_result['penalty'] = df_slot_current_result.apply(lambda x: eval_model(x['cost_norm'], x['expect_queue_time_norm'], 1), axis=1)

                        # 比较结果
                        if df_slot_current_result['queueing_model_results'].values[0][1] < df_slot_pre_result['queueing_model_results'].values[0][1]:
                            # print(f"slot id {slot_id}: current optimized result is {df_slot_current_result['penalty'].values[0]}")
                            # print(f"slot id {slot_id}: previous optimized result is {df_slot_pre_result['penalty'].values[0]}")
                            # print(f"slot id {slot_id}: current optimized result is better than previous optimized result")
                            pass

                        else:
                            # print(f"slot id {slot_id}: current optimized result is {df_slot_current_result['penalty'].values[0]}")
                            # print(f"slot id {slot_id}: previous optimized result is {df_slot_pre_result['penalty'].values[0]}")
                            # print(f"slot id {slot_id}: previous optimized result is better than current optimized result")

                            current_result[j]['before_switch_current'] = df_slot_pre_result[f'before_switch_current{slot_id}'].values[0]
                            current_result[j]['switch_soc'] = df_slot_pre_result[f'simulated_switch_soc{slot_id}'].values[0]
                            current_result[j]['after_switch_current'] = df_slot_pre_result[f'after_switch_current{slot_id}'].values[0]

                            # 判断当前soc和转换soc的大小
                            # 如果当前soc小于转换soc，说明没有到达转换点，使用before_switch_current
                            current_result[j]['current_current'] = df_slot_pre_result[f'current_current{slot_id}'].values[0]

                    # 如果电池不在prior_charging_slot_id中
                    else:
                        slot_id = current_result[j]['slot_id']
                        # 上一次结果
                        df_slot_pre_result = charging_simu_single_slot(charging_df=pd.DataFrame(pre_charging_simulation_resu), slot=slot_id,
                                                                       map_device_electricity_price=map_device_electricity_price,
                                                                       electricity_price_model=electricity_price_model)

                        # 当前结果
                        df_slot_current_result = charging_simu_single_slot(charging_df=pd.DataFrame(current_charging_simulation_resu), slot=slot_id,
                                                                           map_device_electricity_price=map_device_electricity_price,
                                                                           electricity_price_model=electricity_price_model)

                        # 比较结果
                        if df_slot_current_result['cost'].values[0] < df_slot_pre_result['cost'].values[0]:
                            # print(f"slot id {slot_id}: current optimized result is {df_slot_current_result['cost'].values[0]}")
                            # print(f"slot id {slot_id}: previous optimized result is {df_slot_pre_result['cost'].values[0]}")
                            # print(f"slot id {slot_id}: current optimized result is better than previous optimized result")
                            pass

                        else:
                            # print(f"slot id {slot_id}: current optimized result is {df_slot_current_result['cost'].values[0]}")
                            # print(f"slot id {slot_id}: previous optimized result is {df_slot_pre_result['cost'].values[0]}")
                            # print(f"slot id {slot_id}: previous optimized result is better than current optimized result")

                            current_result[j]['before_switch_current'] = df_slot_pre_result['before_switch_current'].values[0]
                            current_result[j]['switch_soc'] = df_slot_pre_result['simulated_switch_soc'].values[0]
                            current_result[j]['after_switch_current'] = df_slot_pre_result['after_switch_current'].values[0]

                            # 判断当前soc和转换soc的大小
                            # 如果当前soc小于转换soc，说明没有到达转换点，使用before_switch_current
                            current_result[j]['current_current'] = df_slot_pre_result['current_current'].values[0]
                            
                    break
    

    return current_result


if __name__ == '__main__':
    # set_start_method('spawn')
    pass
    

