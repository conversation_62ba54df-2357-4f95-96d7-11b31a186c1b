import copy
from datetime import datetime, timedelta
import json
import numpy as np

from config.config import get_config

config = get_config()

# 固定参数
TS = config['ts']
SIMU_TIME = config['simu_time']
CHARGING_FINISH_SOC = config['charging_finish_soc']
CHARGING_STOP_SOC = config['charging_stop_soc']
AVERAGE_SWAP_TIME = config['average_swap_time']

# 电池参数
BATTERY_70_CAPACITY = config['battery_70_capacity']
BATTERY_75_CAPACITY = config['battery_75_capacity']
BATTERY_100_CAPACITY = config['battery_100_capacity']


def module_power_limit_control(
    battery_capacity: int, current_pre: float, soc_pre: float, 
    map_soc_temp_ocv: dict, module_type: str, flex_charging_pre: bool
    ):
    '''
    arguments:
        current_pre: charging_current @ t-1
        soc_pre: battery real soc @ t-1, 
        map_soc_temp_ocv: hasing mapping for [soc, temp] --> ocv

    returns:
        eta_battery_pre, 
        eta_module_pre, 
        single_slot_module_output_power_pre: unit: kW

    
    '''
    if battery_capacity == BATTERY_75_CAPACITY:
        battery_type = '70'
    elif battery_capacity == BATTERY_75_CAPACITY:
        battery_type = '75'
    elif battery_capacity == BATTERY_100_CAPACITY:
        battery_type = '100'
    else:
        battery_type = '70'

    if soc_pre > 100:
        soc_pre = 100
    elif soc_pre < 0:
        soc_pre = 0
    
    # if battery_temp_pre > 35:
    #     battery_temp_pre = 35
    # elif battery_temp_pre < -5:
    #     battery_temp_pre = -5
    
    # 现阶段不考虑温度影响，统一设为25度
    open_circuit_voltage_pre = map_soc_temp_ocv[battery_type][str(int(soc_pre))]['25']

    charge_ratio = abs(current_pre)/battery_capacity
    # calculate eta_battery_pre
    if battery_type == '70':
        eta_battery_pre = 0.0189*charge_ratio**2 - 0.0477*charge_ratio + 0.9934

    elif battery_type  == '75':
        eta_battery_pre = 0.0241*charge_ratio**2 - 0.0557*charge_ratio + 0.9903

    elif battery_type == '100':
        eta_battery_pre = 0.0362*charge_ratio**2 - 0.0654*charge_ratio + 0.9987

    else:
        eta_battery_pre = 0.0189*charge_ratio**2 - 0.0477*charge_ratio + 0.9934

    
    module_output_power_pre = abs(round(open_circuit_voltage_pre * current_pre / eta_battery_pre / 1000, 2))

    if flex_charging_pre:
        single_slot_module_output_power_pre = module_output_power_pre / 2
    else:
        single_slot_module_output_power_pre = module_output_power_pre

    # 模块计算定义域是[0, 40], 包含两个模块
    # definition domain [0, 40], including 2 modules in one slot 
    if module_type == 'UU':
        # 分段函数
        if single_slot_module_output_power_pre >= 0 and single_slot_module_output_power_pre < 20:
            # eta_module_pre = round(
            #     -6.98875595e-07*single_slot_module_output_power_pre**4 + 7.15653336e-05*single_slot_module_output_power_pre**3 \
            #     - 2.62292677e-03*single_slot_module_output_power_pre**2 + 4.03951229e-02*single_slot_module_output_power_pre + 0.734587585
            # , 4)
            eta_module_pre = \
                -6.989e-7 * single_slot_module_output_power_pre ** 4 + 7.157e-5 * single_slot_module_output_power_pre ** 3 + \
                -2.623e-3 * single_slot_module_output_power_pre ** 2 + 0.04040 * single_slot_module_output_power_pre + 0.7346
        else:
            eta_module_pre = -5.612e-4 * single_slot_module_output_power_pre + 0.9687 - 0.0033399999999998986

    elif module_type == 'YFY':
        if single_slot_module_output_power_pre >= 0 and single_slot_module_output_power_pre < 20:
            # eta_module_pre = round(
            #     -0.000000902872779*single_slot_module_output_power_pre**4 + 0.0000930986138*single_slot_module_output_power_pre**3 \
            #     -0.0034174051*single_slot_module_output_power_pre**2 + 0.0521601548*single_slot_module_output_power_pre + 0.659863142
            # , 4)
            eta_module_pre = \
                -9.029e-7 * single_slot_module_output_power_pre ** 4 + 9.310e-5 * single_slot_module_output_power_pre ** 3 + \
                -3.417e-3 * single_slot_module_output_power_pre ** 2 + 5.216e-2 * single_slot_module_output_power_pre + 6.59863142e-01
        else:
            eta_module_pre = -6.187e-4 * single_slot_module_output_power_pre + 0.9531 - 0.004126858000000122

    
    return eta_battery_pre, eta_module_pre, single_slot_module_output_power_pre, module_output_power_pre


def battery_charging_current_control(
    current_limit: dict,
    battery_capacity: dict, current_pre: dict, soc_pre: dict, flex_charging_pre: dict, map_soc_cur: dict, map_soc_vol: dict, 
    branch_circuit_current_limit: dict,
    eta_battery_pre: dict
    ):
    """
    arguments:
        battery_capacity
        current_pre
        soc_pre
        flex_charging_pre: flex==True, non_flex
        map_soc_cur
        map_soc_vol
        battery_temp_next
        battery_temp_pre

    return:
        current_next: current @ t
        soc_next: soc @ t
        flex_charging_next: flex_charging @ t

    """

    current_next = {}
    soc_next = {}
    power_limit = {}
    current_pcu = {}
    flex_charging_next = {}

    for bin in battery_capacity.keys():
        flex_charging_next[bin] = flex_charging_pre[bin]

    for bin in soc_pre.keys():
        # keys 获取每块电池，进行电流请求值的计算
        # 安时法
        if battery_capacity[bin] == 0:
            soc_next[bin] = soc_pre[bin]
        else:
            soc_next[bin] = soc_pre[bin] + TS * abs(current_pre[bin]) / 3600 / battery_capacity[bin] * 100 * eta_battery_pre[bin]

        # 计算到SOC为90，停止计算
        if soc_next[bin] >= CHARGING_STOP_SOC:
            # current_next[bin] = 0
            flex_charging_next[bin] = False

    ######
    # flex_charging_next再次进行判断临仓是否需要柔性充电
    for bin in soc_pre.keys():
        # 如果当前仓位为非柔性充电，判断临仓是否正在充电，切换至柔性充电
        if bin == '01':
            if flex_charging_next[bin] == True:
                continue
            # 在仿真时，如果临仓没有充电
            if soc_pre.get('02') is not None:
                
                # 如果临仓没有充电，切换至柔性充电，温度暂时没有可以参考ttc
                if soc_next['02'] >= CHARGING_STOP_SOC:
                    flex_charging_next[bin] = True
                else:
                    # 临仓正在充电
                    flex_charging_next[bin] = False
            else:
                flex_charging_next[bin] = True

        elif bin == '02':
            if flex_charging_next[bin] == True:
                continue
            # 在仿真时，如果临仓没有充电
            if soc_pre.get('01') is not None:
                
                # 如果临仓没有充电，切换至柔性充电
                if soc_next['01'] >= CHARGING_STOP_SOC:
                    flex_charging_next[bin] = True
                else:
                    # 临仓正在充电
                    flex_charging_next[bin] = False
            else:
                flex_charging_next[bin] = True

        elif bin == '03':
            if flex_charging_next[bin] == True:
                continue
            # 在仿真时，如果临仓没有充电
            if soc_pre.get('04') is not None:
                
                # 如果临仓没有充电，切换至柔性充电
                if soc_next['04'] >= CHARGING_STOP_SOC:
                    flex_charging_next[bin] = True
                else:
                    # 临仓正在充电
                    flex_charging_next[bin] = False
            else:
                flex_charging_next[bin] = True

        elif bin == '04':
            if flex_charging_next[bin] == True:
                continue
            # 在仿真时，如果临仓没有充电
            if soc_pre.get('03') is not None:
                
                # 如果临仓没有充电，切换至柔性充电
                if soc_next['03'] >= CHARGING_STOP_SOC:
                    flex_charging_next[bin] = True
                else:
                    # 临仓正在充电
                    flex_charging_next[bin] = False
            else:
                flex_charging_next[bin] = True

        elif bin == '05':
            if flex_charging_next[bin] == True:
                continue
            # 在仿真时，如果临仓没有充电
            if soc_pre.get('06') is not None:
                
                # 如果临仓没有充电，切换至柔性充电
                if soc_next['06'] >= CHARGING_STOP_SOC:
                    flex_charging_next[bin] = True
                else:
                    # 临仓正在充电
                    flex_charging_next[bin] = False
            else:
                flex_charging_next[bin] = True

        elif bin == '06':
            if flex_charging_next[bin] == True:
                continue
            # 在仿真时，如果临仓没有充电
            if soc_pre.get('05') is not None:
                
                # 如果临仓没有充电，切换至柔性充电
                if soc_next['05'] >= CHARGING_STOP_SOC:
                    flex_charging_next[bin] = True
                else:
                    # 临仓正在充电
                    flex_charging_next[bin] = False
            else:
                flex_charging_next[bin] = True

        elif bin == '07':
            if flex_charging_next[bin] == True:
                continue
            # 在仿真时，如果临仓没有充电
            if soc_pre.get('08') is not None:
                
                # 如果临仓没有充电，切换至柔性充电
                if soc_next['08'] >= CHARGING_STOP_SOC:
                    flex_charging_next[bin] = True
                else:
                    # 临仓正在充电
                    flex_charging_next[bin] = False
            else:
                flex_charging_next[bin] = True

        elif bin == '08':
            if flex_charging_next[bin] == True:
                continue
            # 在仿真时，如果临仓没有充电
            if soc_pre.get('07') is not None:
                
                # 如果临仓没有充电，切换至柔性充电
                if soc_next['07'] >= CHARGING_STOP_SOC:
                    flex_charging_next[bin] = True
                else:
                    # 临仓正在充电
                    flex_charging_next[bin] = False
            else:
                flex_charging_next[bin] = True

        elif bin == '09':
            if flex_charging_next[bin] == True:
                continue
            # 在仿真时，如果临仓没有充电
            if soc_pre.get('10') is not None:
                
                # 如果临仓没有充电，切换至柔性充电
                if soc_next['10'] >= CHARGING_STOP_SOC:
                    flex_charging_next[bin] = True
                else:
                    # 临仓正在充电
                    flex_charging_next[bin] = False
            else:
                flex_charging_next[bin] = True

        elif bin == '10':
            if flex_charging_next[bin] == True:
                continue
            # 在仿真时，如果临仓没有充电
            if soc_pre.get('09') is not None:
                
                # 如果临仓没有充电，切换至柔性充电
                if soc_next['09'] >= CHARGING_STOP_SOC:
                    flex_charging_next[bin] = True
                else:
                    # 临仓正在充电
                    flex_charging_next[bin] = False
            else:
                flex_charging_next[bin] = True

        elif bin == '11':
            if flex_charging_next[bin] == True:
                continue
            # 在仿真时，如果临仓没有充电
            if soc_pre.get('12') is not None:
                
                # 如果临仓没有充电，切换至柔性充电
                if soc_next['12'] >= CHARGING_STOP_SOC:
                    flex_charging_next[bin] = True
                else:
                    # 临仓正在充电
                    flex_charging_next[bin] = False
            else:
                flex_charging_next[bin] = True

        elif bin == '12':
            if flex_charging_next[bin] == True:
                continue
            # 在仿真时，如果临仓没有充电
            if soc_pre.get('11') is not None:
                
                # 如果临仓没有充电，切换至柔性充电
                if soc_next['11'] >= CHARGING_STOP_SOC:
                    flex_charging_next[bin] = True
                else:
                    # 临仓正在充电
                    flex_charging_next[bin] = False
            else:
                flex_charging_next[bin] = True

    ######

    for bin in soc_next.keys():

        if battery_capacity[bin] == BATTERY_75_CAPACITY:
            capa = '75'
        elif battery_capacity[bin] == BATTERY_70_CAPACITY:
            capa = '70'
        elif battery_capacity[bin] == BATTERY_100_CAPACITY:
            capa = '100'
        else:
            capa = '70'

        # 通过soc—temp与current、voltage的map来匹配，提高计算效率
        current_bms = map_soc_cur[capa][str(int(soc_next[bin]))]['25']
        voltage = map_soc_vol[capa][str(int(soc_next[bin]))]['25']
        
        # 需要加入柔性判断逻辑
        if flex_charging_next[bin]:
            power_limit[bin] = 80000
            # 偶数
            if int(bin) % 2 == 0:
                current_pcu[bin] = branch_circuit_current_limit[bin] + branch_circuit_current_limit[str(int(bin)-1).rjust(2, '0')]
            # 奇数
            elif int(bin) % 2 != 0 and bin != '13':
                current_pcu[bin] = branch_circuit_current_limit[bin] + branch_circuit_current_limit[str(int(bin)+1).rjust(2, '0')]
            else:
                current_pcu[bin] = branch_circuit_current_limit[bin]
        else:
            power_limit[bin] = 40000
            current_pcu[bin] = branch_circuit_current_limit[bin]
        # 当前soc，电池温度下，电池电压
        if voltage == 0:
            # 主控可以提供的功率大小
            current_module = 0
        else:
            current_module = - power_limit[bin] / voltage

        current_next[bin] = max(current_bms, current_pcu[bin], current_limit[bin], current_module)
        # print(current_bms, current_pcu)
        # 计算到SOC为90，停止计算
        if soc_next[bin] >= CHARGING_STOP_SOC:
            current_next[bin] = 0


    return current_next, soc_next, flex_charging_next


def ampere_hour_charging_method(
    switch_soc: dict, before_switch_current: dict, after_switch_current: dict,
    current_pre: dict, battery_capacity: dict, status_flex_charging_pre: dict, init_soc: dict,
    branch_circuit_current_limit: dict,
    map_soc_cur: dict, map_soc_vol: dict,
    map_soc_temp_ocv: dict, module_type: dict
    ):
    '''
    
    '''
    time_step = int(SIMU_TIME / TS)

    # 以下字典，key 为电池仓位'03'，value为一个列表，分别装入电池温度，电流，soc等信息
    # 并存入第一个初始值
    simulated_current = {}
    simulated_soc = {}
    simulated_charging_time = {}
    simulated_eta_battery = {}
    simulated_eta_module = {}
    simulated_single_slot_module_output_power = {}
    simulated_module_output_power = {}
    simulated_charging_energy = {}
    simulated_module_energy = {}
    ############
    current_limit = {}
    simulated_switch_flag = {}
    # 记录跳转switch的时间点
    simulated_switch_moment = {}
    ############
    # 初始值赋值
    for bin in init_soc.keys():
        ############
        current_limit[bin] = before_switch_current[bin]
        # 跳转标志位
        if init_soc[bin] < switch_soc[bin]:
            simulated_switch_flag[bin] = 0
        else:
            simulated_switch_moment[bin] = 0
            simulated_switch_flag[bin] = 1
        ############
        simulated_current[bin] = list()
        simulated_current[bin].append(current_pre[bin])

        simulated_soc[bin] = list()
        simulated_soc[bin].append(init_soc[bin])

        single_eta_battery_pre, single_eta_module_pre, \
            single_slot_module_output_power_pre, module_output_power_pre = module_power_limit_control(
                                            battery_capacity=battery_capacity[bin], current_pre=current_pre[bin], soc_pre=init_soc[bin], 
                                            map_soc_temp_ocv=map_soc_temp_ocv, module_type=module_type, 
                                            flex_charging_pre=status_flex_charging_pre[bin]
                                        )
        simulated_eta_battery[bin] = list()
        simulated_eta_battery[bin].append(single_eta_battery_pre)

        simulated_eta_module[bin] = list()
        simulated_eta_module[bin].append(single_eta_module_pre)

        simulated_single_slot_module_output_power[bin] = list()
        simulated_single_slot_module_output_power[bin].append(single_slot_module_output_power_pre)

        simulated_module_output_power[bin] = list()
        simulated_module_output_power[bin].append(module_output_power_pre)

        # 交流充电电能
        simulated_charging_energy[bin] = list()
        simulated_charging_energy[bin].append(round(module_output_power_pre * TS / 3600 / single_eta_module_pre, 3))

        simulated_module_energy[bin] = list()
        simulated_module_energy[bin].append(round(module_output_power_pre * TS / 3600, 3))

    for iter in range(0, time_step, 1):
        iter_current = {}
        iter_soc = {}
        iter_eta_battery = {}
        iter_eta_module = {}
        for k, item in simulated_current.items():
            # 电池电量显示充满则不会循环迭代
            if simulated_soc[k][-1] >= CHARGING_STOP_SOC:
                continue
            else:
                iter_current[k] = simulated_current[k][-1]
                iter_soc[k] = simulated_soc[k][-1]
                iter_eta_battery[k] = simulated_eta_battery[k][-1]
                iter_eta_module[k] = simulated_eta_module[k][-1]

        # 判断电池是否都充满了
        if len(iter_soc) < 1:
            break
        
        ############
        # 电池充电电流切换控制
        for bin in iter_current.keys():
            # 若该仓电池soc大于switch 设定soc
            if iter_soc[bin] > switch_soc[bin]:
                if simulated_switch_flag[bin] == 0:
                    current_limit[bin] = after_switch_current[bin]
                    # 如果某仓的计算的soc没有增加，一直达不到switch_soc，就会出现最终输出没有该仓的swith点
                    simulated_switch_moment[bin] = int(iter*10)
                    # 代表已经切换
                    simulated_switch_flag[bin] = 1
                else:
                    continue
            else:
                # 如果达到最后一帧仿真
                if iter == len(range(0, time_step, 1)) - 1:
                    simulated_switch_moment[bin] = 0
        ############
        
        # 暂时cms仿真没有加入电池温度的计算
        current_next, soc_next, status_flex_charging_next = battery_charging_current_control(
            current_limit=current_limit,
            battery_capacity=battery_capacity, current_pre=iter_current, soc_pre=iter_soc, flex_charging_pre=status_flex_charging_pre,
            branch_circuit_current_limit=branch_circuit_current_limit,
            map_soc_cur=map_soc_cur, map_soc_vol=map_soc_vol, 
            eta_battery_pre=iter_eta_battery
        )

        for bin in iter_current.keys():
            simulated_current[bin].append(current_next[bin])
            simulated_soc[bin].append(soc_next[bin])
            # calculate eta @ t
            single_eta_battery_next, single_eta_module_next, \
                single_slot_module_output_power_next, module_output_power_next = module_power_limit_control(
                                                    battery_capacity=battery_capacity[bin], current_pre=current_next[bin], soc_pre=soc_next[bin], 
                                                    map_soc_temp_ocv=map_soc_temp_ocv, module_type=module_type, 
                                                    flex_charging_pre=status_flex_charging_next[bin]
                                                )

            simulated_eta_battery[bin].append(single_eta_battery_next)
            simulated_eta_module[bin].append(single_eta_module_next)
            simulated_single_slot_module_output_power[bin].append(single_slot_module_output_power_next)
            simulated_module_output_power[bin].append(module_output_power_next)
            simulated_charging_energy[bin].append(round(module_output_power_next * TS / 3600 / single_eta_module_next, 3))
            simulated_module_energy[bin].append(round(module_output_power_next * TS / 3600, 3))

        # 柔性充电状态开关
        status_flex_charging_pre = status_flex_charging_next

    # 计算各仓位充电时长
    for bin in simulated_soc.keys():
        simulated_charging_time[bin] = len(simulated_soc[bin]) * TS


    return simulated_eta_battery, simulated_eta_module, simulated_single_slot_module_output_power, simulated_module_output_power, \
        simulated_charging_time, simulated_charging_energy, simulated_soc, simulated_current, simulated_switch_moment, simulated_module_energy


def operation_hour_category(device_id: str, current_hour: int, map_electricity_price: dict):
    '''
    arguments:
        device_id,
        model_trigger_time: model call and start time,
        map_electricity_price

    returns:
        

    '''
    # model_trigger_hour = datetime.fromtimestamp(model_trigger_time/1000).hour
    if current_hour >= 24:
        current_hour = current_hour % 24

    if len(map_electricity_price) > 0:
        for price_info in map_electricity_price:
            if len(price_info) > 0:
                if float(price_info['start_hour']) <= current_hour <= float(price_info['end_hour']):
                    operation_hour_tag = price_info.get('price_tag', 'flat')
                    peration_hour_price = float(price_info.get('price', 0.8306))
                    operation_hour_info = [operation_hour_tag, peration_hour_price]
                    break

                else:
                    operation_hour_info = ['flat', 0.8306]
            else:
                operation_hour_info = ['flat', 0.8306]

    else:
        operation_hour_info = ['flat', 0.8306]
    

    return operation_hour_info


def valley_flat_peak_tip_charging_time_v2(
    device_id: str, model_trigger_time: int, map_electricity_price: dict, simulated_charging_time: dict,
    simulated_charging_energy: dict
    ):
    '''
    这个版本将时间段划分为30分钟，原因是有些地区尖峰平谷的时间段会在30分的情况下切换
    '''
    
    # 电池调整后充电时长信息
    simulated_valley_flat_peak_tip_charging_time = {}
    simulated_valley_flat_peak_tip_charging_energy = {}

    # 判断触发所在的小时段，同时确定是否在前半小时 / 后半小时
    for bin in simulated_charging_time.keys():
        simulated_valley_flat_peak_tip_charging_time[bin] = list()

        # 各个仓位电池预计充满的结束时间戳
        simulated_charging_end_timestamp = model_trigger_time + simulated_charging_time[bin]*1000

        charging_start_hour = datetime.fromtimestamp(model_trigger_time/1000).hour
        simulated_charging_end_hour = datetime.fromtimestamp(simulated_charging_end_timestamp/1000).hour

        # 触发时间起到充电结束，所经历的小时段
        # 如果时间end 小于 start，说明到第二天了
        if charging_start_hour <= simulated_charging_end_hour:
            charging_hour_num = np.arange(int(charging_start_hour), int(simulated_charging_end_hour)+1, 1)
        else:
            charging_hour_num = np.arange(int(charging_start_hour), int(simulated_charging_end_hour)+24+1, 1)

        # 各时段初始充电时长
        valley_charging_time = 0
        flat_charging_time = 0
        peak_charging_time = 0
        tip_charging_time = 0
        # 各时段初始充电电能
        valley_charging_energy = 0
        flat_charging_energy = 0
        peak_charging_energy = 0
        tip_charging_energy = 0
        start_segment = 0

        for i in range(len(charging_hour_num)):
            # i 代表距离触发时间i个小时
            for j in np.arange(0, 1, 0.5):
                # 获取的每次电价所属时段，分别计算在各区间的累记充电时长
                # 计算每个电池当前时段充电时长
                current_hour_start_timestamp = datetime.timestamp(
                    datetime.strptime(
                        datetime.fromtimestamp(model_trigger_time/1000).strftime('%Y-%m-%d %H:%M:%S'), 
                        '%Y-%m-%d %H:%M:%S'
                        ).replace(minute=0, second=0,microsecond=0) + timedelta(hours=i+j)
                )
                current_hour_end_timestamp = datetime.timestamp(
                    datetime.strptime(
                        datetime.fromtimestamp(model_trigger_time/1000).strftime('%Y-%m-%d %H:%M:%S'), 
                        '%Y-%m-%d %H:%M:%S'
                        ).replace(minute=0, second=0,microsecond=0) + timedelta(hours=i+j+0.5)
                )
                # 如果触发时间 大于 半小时末尾，在后半小时
                if model_trigger_time/1000 > current_hour_end_timestamp:
                    continue
                # 如果充电结束时间 小于 半小时末尾，在后半小时
                if simulated_charging_end_timestamp < current_hour_start_timestamp:
                    continue

                current_hour_charging_start_timestamp = max(model_trigger_time/1000, current_hour_start_timestamp)
    
                current_hour_charging_end_timestamp = min(current_hour_end_timestamp, simulated_charging_end_timestamp/1000)
                # 计算该小时段涉及的数据点数量，向下取整
                current_hour_length = int((current_hour_charging_end_timestamp - current_hour_charging_start_timestamp)/TS)

                current_hour = charging_hour_num[i] + j
                # 如果小时数 >= 24 --> 0
                charging_hour_info = operation_hour_category(
                                        device_id=device_id, current_hour=current_hour, 
                                        map_electricity_price=map_electricity_price)

                # if charging_hour_info[0] in [None, 'nan', 'NaN', np.nan, np.NaN, '', 'None']:
                #     charging_hour_info[0] = 'flat'

                if charging_hour_info[0] == 'valley':
                    valley_charging_time += (current_hour_charging_end_timestamp - current_hour_charging_start_timestamp)
                    # 累加当前仓对应时段充电电能
                    valley_charging_energy += sum(simulated_charging_energy[bin][start_segment: start_segment+current_hour_length])
                    start_segment = start_segment + current_hour_length

                elif charging_hour_info[0] == 'flat':
                    flat_charging_time += (current_hour_charging_end_timestamp - current_hour_charging_start_timestamp)
                    # 累加当前仓对应时段充电电能
                    flat_charging_energy += sum(simulated_charging_energy[bin][start_segment: start_segment+current_hour_length])
                    start_segment = start_segment + current_hour_length

                elif charging_hour_info[0] == 'peak':
                    peak_charging_time += (current_hour_charging_end_timestamp - current_hour_charging_start_timestamp)
                    # 累加当前仓对应时段充电电能
                    peak_charging_energy += sum(simulated_charging_energy[bin][start_segment: start_segment+current_hour_length])
                    start_segment = start_segment + current_hour_length

                elif charging_hour_info[0] == 'tip':
                    tip_charging_time += (current_hour_charging_end_timestamp - current_hour_charging_start_timestamp)
                    # 累加当前仓对应时段充电电能
                    tip_charging_energy += sum(simulated_charging_energy[bin][start_segment: start_segment+current_hour_length])
                    start_segment = start_segment + current_hour_length

            # 存入dict
            simulated_valley_flat_peak_tip_charging_time[bin] = [valley_charging_time, flat_charging_time, peak_charging_time, tip_charging_time]

            simulated_valley_flat_peak_tip_charging_energy[bin] = [valley_charging_energy, flat_charging_energy, \
                                                                peak_charging_energy, tip_charging_energy]


    return simulated_valley_flat_peak_tip_charging_time, simulated_valley_flat_peak_tip_charging_energy


def valley_flat_peak_tip_charging_time(
    device_id: str, model_trigger_time: int, map_electricity_price: dict, simulated_charging_time: dict,
    simulated_charging_energy: dict
    ):
    '''
    arguments:
        device_id,
        model_trigger_time,
        map_electricity_price,
        simulated_charging_time,

    return:
        simulated_valley_flat_peak_tip_charging_time = {
            "01": [
                500, 1500, 300, 300
            ]
        }

    '''

    # 电池调整后充电时长信息
    simulated_valley_flat_peak_tip_charging_time = {}
    simulated_valley_flat_peak_tip_charging_energy = {}

    # 按照各仓位进行计算
    for bin in simulated_charging_time.keys():
        simulated_valley_flat_peak_tip_charging_time[bin] = list()

        # 各个仓位电池预计充满的结束时间戳
        simulated_charging_end_timestamp = model_trigger_time + simulated_charging_time[bin]*1000

        charging_start_hour = datetime.fromtimestamp(model_trigger_time/1000).hour
        simulated_charging_end_hour = datetime.fromtimestamp(simulated_charging_end_timestamp/1000).hour

        # 触发时间起到充电结束，所经历的小时段
        charging_hour_num = np.arange(int(charging_start_hour), int(simulated_charging_end_hour)+1, 1)

        # 各时段初始充电时长
        valley_charging_time = 0
        flat_charging_time = 0
        peak_charging_time = 0
        tip_charging_time = 0
        # 各时段初始充电电能
        valley_charging_energy = 0
        flat_charging_energy = 0
        peak_charging_energy = 0
        tip_charging_energy = 0
        start_segment = 0
        # 存储各仓位充电至结束的时段电价情况和 最短充电至某小时段index
        # dict_battery_charging_hour_info[bin]['info'] = list()
        # dict_battery_charging_hour_info[bin]['max_hour_index'] = 0

        for h in range(len(charging_hour_num)):
            current_hour = charging_hour_num[h]
            charging_hour_info = operation_hour_category(
                                    device_id=device_id, current_hour=current_hour, 
                                    map_electricity_price=map_electricity_price)

            if charging_hour_info[0] in [None, 'nan', 'NaN', np.nan, np.NaN, '', 'None']:
                charging_hour_info[0] = 'flat'

            # 获取的每次电价所属时段，分别计算在各区间的累记充电时长
            # calculate max allowed charging time for each battery - cancelled for now
            # 计算每个电池当前时段充电时长
            current_hour_start_timestamp = datetime.timestamp(
                datetime.strptime(
                    datetime.fromtimestamp(model_trigger_time/1000).strftime('%Y-%m-%d %H:%M:%S'), 
                    '%Y-%m-%d %H:%M:%S'
                    ).replace(minute=0, second=0,microsecond=0) + timedelta(hours=h)
            )
            current_hour_end_timestamp = datetime.timestamp(
                datetime.strptime(
                    datetime.fromtimestamp(model_trigger_time/1000).strftime('%Y-%m-%d %H:%M:%S'), 
                    '%Y-%m-%d %H:%M:%S'
                    ).replace(minute=0, second=0,microsecond=0) + timedelta(hours=h+1)
            )

            current_hour_charging_start_timestamp = max(model_trigger_time/1000, current_hour_start_timestamp)
 
            current_hour_charging_end_timestamp = min(current_hour_end_timestamp, simulated_charging_end_timestamp/1000)
            # 计算该小时段涉及的数据点数量
            current_hour_length = int((current_hour_charging_end_timestamp - current_hour_charging_start_timestamp)/10)

            if charging_hour_info[0] == 'valley':
                valley_charging_time += (current_hour_charging_end_timestamp - current_hour_charging_start_timestamp)
                # 累加当前仓对应时段充电电能
                valley_charging_energy += sum(simulated_charging_energy[bin][start_segment: start_segment+current_hour_length])
                start_segment = start_segment + current_hour_length

            elif charging_hour_info[0] == 'flat':
                flat_charging_time += (current_hour_charging_end_timestamp - current_hour_charging_start_timestamp)
                # 累加当前仓对应时段充电电能
                flat_charging_energy += sum(simulated_charging_energy[bin][start_segment: start_segment+current_hour_length])
                start_segment = start_segment + current_hour_length

            elif charging_hour_info[0] == 'peak':
                peak_charging_time += (current_hour_charging_end_timestamp - current_hour_charging_start_timestamp)
                # 累加当前仓对应时段充电电能
                peak_charging_energy += sum(simulated_charging_energy[bin][start_segment: start_segment+current_hour_length])
                start_segment = start_segment + current_hour_length

            elif charging_hour_info[0] == 'tip':
                tip_charging_time += (current_hour_charging_end_timestamp - current_hour_charging_start_timestamp)
                # 累加当前仓对应时段充电电能
                tip_charging_energy += sum(simulated_charging_energy[bin][start_segment: start_segment+current_hour_length])
                start_segment = start_segment + current_hour_length

        # 存入dict
        simulated_valley_flat_peak_tip_charging_time[bin] = [valley_charging_time, flat_charging_time, peak_charging_time, tip_charging_time]

        simulated_valley_flat_peak_tip_charging_energy[bin] = [valley_charging_energy, flat_charging_energy, \
                                                               peak_charging_energy, tip_charging_energy]
        

    return simulated_valley_flat_peak_tip_charging_time, simulated_valley_flat_peak_tip_charging_energy


def advance_battery_selection(battery_info: list, battery_demand: list, simulated_battery_selection='on'):
    '''
    argument:
        battery_info: current battery info for every slot including no-battery slot
        battery_demand: battery need for current hour
    return

    '''
    simulated_battery_info = battery_info
    simulated_battery_demand = copy.deepcopy(battery_demand[0])
    simulated_selected_battery = []
    simulated_70_optional_battery = []
    simulated_100_optional_battery = []
    total_70_battery_num = 0
    total_100_battery_num = 0

    for i in range(len(simulated_battery_info)):
        # if simulated_battery_info[i]['battery_id'] is not None and simulated_battery_info[i]['battery_type'] in [1, 5, 7, 8, 11, 14]:
        #     total_70_battery_num += 1
        # elif simulated_battery_info[i]['battery_id'] is not None and simulated_battery_info[i]['battery_type'] in [6, 13]:
        #     total_100_battery_num += 1
        # else:
        #     total_70_battery_num += 1
        if (simulated_battery_info[i]['battery_id'] is None) or len(simulated_battery_info[i]['battery_id']) == 0:
            # 若没有电池，则跳过
            continue
        else:
            # 判断电池种类，若为70度电池
            if simulated_battery_info[i]['battery_type'] in [1, 3, 5, 7, 8, 11, 14]:
                # 该电池如果是满电电池
                if simulated_battery_info[i]['charging_status'] == 2 and simulated_battery_info[i]['battery_user_soc'] >= CHARGING_FINISH_SOC:
                    simulated_battery_demand['70'] -= 1
                    simulated_battery_info[i]['simulated_battery_label'] = 'actual_ready_battery'
                    # simulated_selected_battery.append(simulated_battery_info[i])
                # 若为正在充电电池
                elif simulated_battery_info[i]['charging_status'] != 2 and simulated_battery_info[i]['battery_user_soc'] < CHARGING_FINISH_SOC:
                    simulated_battery_demand['70'] -= 1
                    simulated_battery_info[i]['simulated_battery_label'] = 'actual_charging_battery'
                    simulated_selected_battery.append(simulated_battery_info[i])
                # 电池为非满电同时没有充电
                else:
                    simulated_70_optional_battery.append(simulated_battery_info[i])
            # 属于100度电池
            elif simulated_battery_info[i]['battery_type'] in [6, 13]:
                # 该电池如果是满电电池
                if simulated_battery_info[i]['charging_status'] == 2 and simulated_battery_info[i]['battery_user_soc'] >= CHARGING_FINISH_SOC:
                    simulated_battery_demand['100'] -= 1
                    simulated_battery_info[i]['simulated_battery_label'] = 'actual_ready_battery'
                    # simulated_selected_battery.append(simulated_battery_info[i])
                # 若为正在充电电池
                elif simulated_battery_info[i]['charging_status'] != 2 and simulated_battery_info[i]['battery_user_soc'] < CHARGING_FINISH_SOC:
                    simulated_battery_demand['100'] -= 1
                    simulated_battery_info[i]['simulated_battery_label'] = 'actual_charging_battery'
                    simulated_selected_battery.append(simulated_battery_info[i])
                # 电池为非满电同时没有充电
                else:
                    simulated_100_optional_battery.append(simulated_battery_info[i])
            # 剩余部分归为70度电池
            else:
                # 该电池如果是满电电池
                if simulated_battery_info[i]['charging_status'] == 2 and simulated_battery_info[i]['battery_user_soc'] >= CHARGING_FINISH_SOC:
                    simulated_battery_demand['70'] -= 1
                    simulated_battery_info[i]['simulated_battery_label'] = 'actual_ready_battery'
                    # simulated_selected_battery.append(simulated_battery_info[i])
                # 若为正在充电电池
                elif simulated_battery_info[i]['charging_status'] != 2 and simulated_battery_info[i]['battery_user_soc'] < CHARGING_FINISH_SOC:
                    simulated_battery_demand['70'] -= 1
                    simulated_battery_info[i]['simulated_battery_label'] = 'actual_charging_battery'
                    simulated_selected_battery.append(simulated_battery_info[i])
                # 电池为非满电同时没有充电
                else:
                    simulated_70_optional_battery.append(simulated_battery_info[i])

    if simulated_battery_selection == 'on':
        # 判断是否需要额外选电池
        while simulated_battery_demand['70'] > 0 and len(simulated_70_optional_battery) > 0:
            # 选择一块电池，需求 -1
            simulated_battery_demand['70'] -= 1
            simulated_70_optional_battery.sort(key=lambda x: x['battery_real_soc'], reverse=True)
            # 放入仿真所选电池队列
            simulated_70_optional_battery[0]['simulated_battery_label'] = 'simulated_charging_battery'
            simulated_selected_battery.append(simulated_70_optional_battery[0])
            # 将其移出
            simulated_70_optional_battery.remove(simulated_70_optional_battery[0])

        while simulated_battery_demand['100'] > 0 and len(simulated_100_optional_battery) > 0:
            # 选择一块电池，需求 -1
            simulated_battery_demand['100'] -= 1
            simulated_100_optional_battery.sort(key=lambda x: x['battery_real_soc'], reverse=True)
            # 放入仿真所选电池队列
            simulated_100_optional_battery[0]['simulated_battery_label'] = 'simulated_charging_battery'
            simulated_selected_battery.append(simulated_100_optional_battery[0])
            # 将其移出
            simulated_100_optional_battery.remove(simulated_100_optional_battery[0])
    else:
        pass

    # 输出选完电池后的需求和选电池后需要进行充电的电池

    return simulated_selected_battery, simulated_battery_demand


def multiprocess_simulation_data_preparation(
        switch_soc_array: list, before_switch_current_array: list, after_switch_current_array: list, 
        current_pre: dict, battery_capacity: dict, status_flex_charging_pre: dict, init_soc: dict,
        branch_circuit_current_limit: dict,
        map_soc_cur: dict, map_soc_vol: dict,
        map_soc_temp_ocv: dict, module_type: dict,
        device_id: str, model_trigger_time: int, map_electricity_price: dict,
        initial_battery_id: dict, simulated_battery_label: dict,
        ):
    '''
    多进程仿真case计算前，单个case的准备
    '''

    single_slot_input_data_list = []
    for i in range(len(switch_soc_array)):
        single_case_data_list = []

        switch_soc = switch_soc_array[i]
        before_switch_current = before_switch_current_array[i]
        after_switch_current = after_switch_current_array[i]

        single_case_data_list.extend([switch_soc, before_switch_current, after_switch_current])

        single_case_data_list.extend(
            [
                current_pre, battery_capacity, status_flex_charging_pre, init_soc,
                branch_circuit_current_limit,
                map_soc_cur, map_soc_vol, map_soc_temp_ocv, module_type,
                device_id, model_trigger_time, map_electricity_price,
                initial_battery_id, simulated_battery_label,
            ]
        )
        single_slot_input_data_list.append(single_case_data_list)
        # queue.put(single_case_data_list)


    return single_slot_input_data_list


def multiprocess_charging_mode_simulation_feedback_system(input_data: list):
    '''
    
    '''

    switch_soc, before_switch_current, after_switch_current = input_data[0], input_data[1], input_data[2]
    current_pre, battery_capacity, status_flex_charging_pre, init_soc = input_data[3], input_data[4], input_data[5], input_data[6]
    branch_circuit_current_limit, map_soc_cur, map_soc_vol, map_soc_temp_ocv = input_data[7], input_data[8], input_data[9], input_data[10]
    module_type, device_id, model_trigger_time, map_electricity_price = input_data[11], input_data[12], input_data[13], input_data[14]
    initial_battery_id, simulated_battery_label = input_data[15], input_data[16]

    dict_single_simulated_charging_result = {}
    # 在此条件下，单块电池自身充电效率的计算
    simulated_eta_battery, simulated_eta_module, \
        simulated_single_slot_module_output_power, simulated_module_output_power, \
        simulated_charging_time, simulated_charging_energy, simulated_soc, simulated_current, \
            simulated_switch_moment, simulated_module_energy = ampere_hour_charging_method(
            switch_soc=switch_soc, before_switch_current=before_switch_current, 
            after_switch_current=after_switch_current, 
            current_pre=current_pre, battery_capacity=battery_capacity, 
            status_flex_charging_pre=status_flex_charging_pre, init_soc=init_soc,
            branch_circuit_current_limit=branch_circuit_current_limit,
            map_soc_cur=map_soc_cur, map_soc_vol=map_soc_vol,
            map_soc_temp_ocv=map_soc_temp_ocv, module_type=module_type
            )

    # 计算峰谷平尖各段充电时长
    simulated_interval_charging_time, simulated_interval_charging_energy = valley_flat_peak_tip_charging_time_v2(
                                            device_id=device_id, model_trigger_time=model_trigger_time, 
                                            map_electricity_price=map_electricity_price, 
                                            simulated_charging_time=simulated_charging_time,
                                            simulated_charging_energy=simulated_charging_energy
                                            )

    # 保存此次仿真电池数据，同时存储仿真条件
    for k in simulated_eta_battery.keys():
        dict_single_simulated_charging_result[k] = list()
        simulated_battery_result = {}
        simulated_battery_result['battery_id'] = initial_battery_id[k]
        simulated_battery_result['slot_id'] = k
        simulated_battery_result['simulated_eta_battery'] = np.mean(simulated_eta_battery[k])
        simulated_battery_result['simulated_eta_module'] = np.mean(simulated_eta_module[k])
        simulated_battery_result['simulated_single_slot_module_output_power'] = np.mean(simulated_single_slot_module_output_power[k])
        simulated_battery_result['simulated_module_output_power'] = np.mean(simulated_module_output_power[k])
        simulated_battery_result['simulated_interval_charging_time'] = simulated_interval_charging_time[k]
        simulated_battery_result['simulated_interval_charging_energy'] = simulated_interval_charging_energy[k]
        simulated_battery_result['simulated_battery_label'] = simulated_battery_label[k]
        simulated_battery_result['simulated_switch_moment'] = simulated_switch_moment[k]
        simulated_battery_result['simulated_switch_soc'] = switch_soc[k]
        simulated_battery_result['current_battery_real_soc'] = init_soc[k]

        simulated_battery_result['simulated_before_switch_current'] = before_switch_current[k]
        simulated_battery_result['simulated_after_switch_current'] = after_switch_current[k]

        if before_switch_current[k] < after_switch_current[k]:
            charging_mode = 'downstairs'
        elif before_switch_current[k] > after_switch_current[k]:
            charging_mode = 'upstairs'
        else:
            charging_mode = 'flat'
        simulated_battery_result['simulated_charging_mode'] = charging_mode

        dict_single_simulated_charging_result[k].append(simulated_battery_result)


    return dict_single_simulated_charging_result


def charging_mode_simulation_feedback_system(
    # simulated_max_charging_time: dict,
    switch_soc_array: list, before_switch_current_array: list, after_switch_current_array: list,
    current_pre: dict, battery_capacity: dict, status_flex_charging_pre: dict, init_soc: dict,
    branch_circuit_current_limit: dict,
    map_soc_cur: dict, map_soc_vol: dict,
    map_soc_temp_ocv: dict, module_type: dict,
    device_id: str, model_trigger_time: int, map_electricity_price: dict,
    initial_battery_id: dict, simulated_battery_label: dict,
    system_mode='running'
    ):
    '''
    
    '''
    simulated_result = []
    
    # fig_soc = go.Figure()
    # fig_current = go.Figure()
    # fig_eta_battery = go.Figure()

    # is_plot = 1

    # for switch_soc in switch_soc_array:
    #     for before_switch_current in before_switch_current_array:
    #         for after_switch_current in after_switch_current_array:
    for i in range(len(switch_soc_array)):
        switch_soc = switch_soc_array[i]
        before_switch_current = before_switch_current_array[i]
        after_switch_current = after_switch_current_array[i]

        dict_single_simulated_charging_result = {}
        # 在此条件下，单块电池自身充电效率的计算
        simulated_eta_battery, simulated_eta_module, \
            simulated_single_slot_module_output_power, simulated_module_output_power, \
            simulated_charging_time, simulated_charging_energy, simulated_soc, simulated_current, \
                simulated_switch_moment, simulated_module_energy = ampere_hour_charging_method(
                switch_soc=switch_soc, before_switch_current=before_switch_current, 
                after_switch_current=after_switch_current, 
                current_pre=current_pre, battery_capacity=battery_capacity, 
                status_flex_charging_pre=status_flex_charging_pre, init_soc=init_soc,
                branch_circuit_current_limit=branch_circuit_current_limit,
                map_soc_cur=map_soc_cur, map_soc_vol=map_soc_vol,
                map_soc_temp_ocv=map_soc_temp_ocv, module_type=module_type
                )
        # if system_mode == 'validation':
        #     with open("./Data/simulated_soc_validation.pkl", "wb") as fp:
        #         pickle.dump(simulated_soc, fp)
        #     with open("./Data/simulated_current_validation.pkl", "wb") as fp:
        #         pickle.dump(simulated_current, fp)
        #     with open("./Data/simulated_charging_energy_validation.pkl", "wb") as fp:
        #         pickle.dump(simulated_charging_energy, fp)
        #     with open("./Data/simulated_module_energy_validation.pkl", "wb") as fp:
        #         pickle.dump(simulated_module_energy, fp)
        # else:
        #     pass

        # fig_soc = plot_TTO2('original', simulated_soc, 'soc', is_plot=is_plot, fig=fig_soc)
        # fig_current = plot_TTO2('original', simulated_current, 'current', is_plot=is_plot, fig=fig_current)
        # fig_eta_battery = plot_TTO2('original', simulated_eta_battery, 'eta_battery', is_plot=is_plot, fig=fig_eta_battery)

        # 计算峰谷平尖各段充电时长
        simulated_interval_charging_time, simulated_interval_charging_energy = valley_flat_peak_tip_charging_time_v2(
                                                device_id=device_id, model_trigger_time=model_trigger_time, 
                                                map_electricity_price=map_electricity_price, 
                                                simulated_charging_time=simulated_charging_time,
                                                simulated_charging_energy=simulated_charging_energy
                                                )

        # # 对应单块电池充电时长和最大允许时长的比较
        # if simulated_charging_time[bin] > simulated_max_charging_time[bin]['max_charging_time']:
        #     # 对此次电池仿真数据打上标签说明是否超时
        #     simulated_charging_overtime_status = 'exceeded'
        # else:
        #     simulated_charging_overtime_status = 'satisfied'

        # 保存此次仿真电池数据，同时存储仿真条件
        for k in simulated_eta_battery.keys():
            dict_single_simulated_charging_result[k] = list()
            simulated_battery_result = {}
            simulated_battery_result['battery_id'] = initial_battery_id[k]
            simulated_battery_result['slot_id'] = k
            simulated_battery_result['simulated_eta_battery'] = np.mean(simulated_eta_battery[k])
            simulated_battery_result['simulated_eta_module'] = np.mean(simulated_eta_module[k])
            simulated_battery_result['simulated_single_slot_module_output_power'] = np.mean(simulated_single_slot_module_output_power[k])
            simulated_battery_result['simulated_module_output_power'] = np.mean(simulated_module_output_power[k])
            simulated_battery_result['simulated_interval_charging_time'] = simulated_interval_charging_time[k]
            simulated_battery_result['simulated_interval_charging_energy'] = simulated_interval_charging_energy[k]
            simulated_battery_result['simulated_battery_label'] = simulated_battery_label[k]
            simulated_battery_result['simulated_switch_moment'] = simulated_switch_moment[k]
            simulated_battery_result['simulated_switch_soc'] = switch_soc[k]
            simulated_battery_result['current_battery_real_soc'] = init_soc[k]

            simulated_battery_result['simulated_before_switch_current'] = before_switch_current[k]
            simulated_battery_result['simulated_after_switch_current'] = after_switch_current[k]

            if before_switch_current[k] < after_switch_current[k]:
                charging_mode = 'downstairs'
            elif before_switch_current[k] > after_switch_current[k]:
                charging_mode = 'upstairs'
            else:
                charging_mode = 'flat'
            simulated_battery_result['simulated_charging_mode'] = charging_mode

            dict_single_simulated_charging_result[k].append(simulated_battery_result)

        simulated_result.append(dict_single_simulated_charging_result)

    # plot_presentation(None, None, fig_eta_battery, fig_soc, fig_current, is_show=is_plot)

    return simulated_result


def battery_initial_info(battery_info: dict):
    """ battery info @ beginning call
    arguments:
        bin: [1, 4, 6, 12] charging bin
    return:
        initial_battery_temp,
        initial_charging_current,
        battery_type,
        battery_capacity,
        initial_status_flex_charging,
        initial_battery_soc,
        branch_circuit_current_limit,
        max_allowed_module_power_1,
        max_allowed_module_power_2

    """
    # 对传入电池初始信息进行转换
    initial_battery_temp = {}
    initial_charging_current = {}
    battery_type = {}
    battery_capacity = {}  # modified
    initial_status_flex_charging = {}
    initial_battery_user_soc = {}
    initial_battery_soc = {}
    max_allowed_module_power_1 = {}
    max_allowed_module_power_2 = {}
    initial_battery_id = {}
    simulated_battery_label = {}
    simulated_switch_soc = {}
    simulated_before_switch_current = {}
    simulated_after_switch_current = {}

    # {'01': {'init_temp': 28, 'init_current': -220.8}}
    for item in battery_info:
        # 如果没有电池
        if (item['battery_id'] is None) or len(item['battery_id']) == 0:
            continue
        k = str(item['slot_id']).rjust(2, '0')
        # k = item['battery_id']
        initial_battery_temp[k] = item['initial_battery_temp']
        initial_charging_current[k] = item['charging_current']
        battery_type[k] = item['battery_type']
        battery_capacity[k] = item['battery_capacity']
        initial_status_flex_charging[k] = item['charging_status']
        initial_battery_user_soc[k] = item['battery_user_soc']
        initial_battery_soc[k] = item['battery_real_soc']

        max_allowed_module_power_1[k] = item['max_allowed_module_power_1']
        max_allowed_module_power_2[k] = item['max_allowed_module_power_2']

        initial_battery_id[k] = item['battery_id']
        simulated_battery_label[k] = item['simulated_battery_label']
        simulated_switch_soc[k] = item['pre_switch_soc']
        simulated_before_switch_current[k] = item['pre_before_switch_current']
        simulated_after_switch_current[k] = item['pre_after_switch_current']

    for k in initial_charging_current.keys():
        # 判断当前电池若为2（未充电状态），临仓是否在充电，若没有充电则可以进入柔性，此处需要整站最大电容量
        if k == '01':
            if initial_battery_user_soc[k] >= CHARGING_FINISH_SOC and initial_status_flex_charging[k] == 2:
                continue
            if initial_battery_soc.get('02') is not None:
                # 充满/没有充电
                # 正式时要用and initial_status_flex_charging['02'] == 2，validation阶段用 in [0, 2]
                if initial_status_flex_charging['02'] == 2:
                    initial_status_flex_charging[k] = 1
                else:
                    # 临仓正在充电
                    initial_status_flex_charging[k] = 0
            else:
                initial_status_flex_charging[k] = 1

        elif k == '02':
            if initial_battery_user_soc[k] >= CHARGING_FINISH_SOC and initial_status_flex_charging[k] == 2:
                continue
            if initial_battery_soc.get('01') is not None:
                # 充满
                if initial_status_flex_charging['01'] == 2:
                    initial_status_flex_charging[k] = 1
                else:
                    # 临仓正在充电
                    initial_status_flex_charging[k] = 0
            else:
                initial_status_flex_charging[k] = 1
        elif k == '03':
            if initial_battery_user_soc[k] >= CHARGING_FINISH_SOC and initial_status_flex_charging[k] == 2:
                continue
            if initial_battery_soc.get('04') is not None:
                # 充满
                if initial_status_flex_charging['04'] == 2:
                    initial_status_flex_charging[k] = 1
                else:
                    # 临仓正在充电
                    initial_status_flex_charging[k] = 0
            else:
                initial_status_flex_charging[k] = 1
        elif k == '04':
            if initial_battery_user_soc[k] >= CHARGING_FINISH_SOC and initial_status_flex_charging[k] == 2:
                continue
            if initial_battery_soc.get('03') is not None:
                # 充满
                if initial_status_flex_charging['03'] == 2:
                    initial_status_flex_charging[k] = 1
                else:
                    # 临仓正在充电
                    initial_status_flex_charging[k] = 0
            else:
                initial_status_flex_charging[k] = 1
        elif k == '05':
            if initial_battery_user_soc[k] >= CHARGING_FINISH_SOC and initial_status_flex_charging[k] == 2:
                continue
            if initial_battery_soc.get('06') is not None:
                # 充满
                if initial_status_flex_charging['06'] == 2:
                    initial_status_flex_charging[k] = 1
                else:
                    # 临仓正在充电
                    initial_status_flex_charging[k] = 0
            else:
                initial_status_flex_charging[k] = 1
        elif k == '06':
            if initial_battery_user_soc[k] >= CHARGING_FINISH_SOC and initial_status_flex_charging[k] == 2:
                continue
            if initial_battery_soc.get('05') is not None:
                # 充满
                if initial_status_flex_charging['05'] == 2:
                    initial_status_flex_charging[k] = 1
                else:
                    # 临仓正在充电
                    initial_status_flex_charging[k] = 0
            else:
                initial_status_flex_charging[k] = 1
        elif k == '07':
            if initial_battery_user_soc[k] >= CHARGING_FINISH_SOC and initial_status_flex_charging[k] == 2:
                continue
            if initial_battery_soc.get('08') is not None:
                # 充满
                if initial_status_flex_charging['08'] == 2:
                    initial_status_flex_charging[k] = 1
                else:
                    # 临仓正在充电
                    initial_status_flex_charging[k] = 0
            else:
                initial_status_flex_charging[k] = 1
        elif k == '08':
            if initial_battery_user_soc[k] >= CHARGING_FINISH_SOC and initial_status_flex_charging[k] == 2:
                continue
            if initial_battery_soc.get('07') is not None:
                # 充满
                if initial_status_flex_charging['07'] == 2:
                    initial_status_flex_charging[k] = 1
                else:
                    # 临仓正在充电
                    initial_status_flex_charging[k] = 0
            else:
                initial_status_flex_charging[k] = 1
        elif k == '09':
            # 本身是否充满
            if initial_battery_user_soc[k] >= CHARGING_FINISH_SOC and initial_status_flex_charging[k] == 2:
                continue
            if initial_battery_soc.get('10') is not None:
                # 充满
                if initial_status_flex_charging['10'] == 2:
                    initial_status_flex_charging[k] = 1
                else:
                    # 临仓正在充电
                    initial_status_flex_charging[k] = 0
            else:
                initial_status_flex_charging[k] = 1
        elif k == '10':
            if initial_battery_user_soc[k] >= CHARGING_FINISH_SOC and initial_status_flex_charging[k] == 2:
                continue
            if initial_battery_soc.get('09') is not None:
                # 充满
                if initial_status_flex_charging['09'] == 2:
                    initial_status_flex_charging[k] = 1
                else:
                    # 临仓正在充电
                    initial_status_flex_charging[k] = 0
            else:
                initial_status_flex_charging[k] = 1
        elif k == '11':
            if initial_battery_user_soc[k] >= CHARGING_FINISH_SOC and initial_status_flex_charging[k] == 2:
                continue
            if initial_battery_soc.get('12') is not None:
                # 充满
                if initial_status_flex_charging['12'] == 2:
                    initial_status_flex_charging[k] = 1
                else:
                    # 临仓正在充电
                    initial_status_flex_charging[k] = 0
            else:
                initial_status_flex_charging[k] = 1
        elif k == '12':
            if initial_battery_user_soc[k] >= CHARGING_FINISH_SOC and initial_status_flex_charging[k] == 2:
                continue
            if initial_battery_soc.get('11') is not None:
                # 充满
                if initial_status_flex_charging['11'] == 2:
                    initial_status_flex_charging[k] = 1
                else:
                    # 临仓正在充电
                    initial_status_flex_charging[k] = 0
            else:
                initial_status_flex_charging[k] = 1
        elif k == '13':
            if initial_battery_user_soc[k] >= CHARGING_FINISH_SOC and initial_status_flex_charging[k] == 2:
                continue
            else:
                # 正在充电
                initial_status_flex_charging[k] = 0
               
    for k in initial_charging_current.keys():
        if initial_status_flex_charging[k] == 1:
            initial_status_flex_charging[k] = True
        else:
            initial_status_flex_charging[k] = False


    return initial_battery_temp, initial_charging_current, battery_type, battery_capacity, \
        initial_status_flex_charging, initial_battery_soc, \
        max_allowed_module_power_1, max_allowed_module_power_2, initial_battery_id, \
        simulated_battery_label, simulated_switch_soc, simulated_before_switch_current, simulated_after_switch_current


def swap_initial_info(battery_info: dict):
    '''
    换电站充电设置参数
    '''
    branch_circuit_current_limit = {}

    for item in battery_info:
        k = str(item['slot_id']).rjust(2, '0')

        branch_circuit_current_limit[k] = item['branch_circuit_current_limit']
        # max_allowed_module_power_1[k] = item['max_allowed_module_power_1']
        # max_allowed_module_power_2[k] = item['max_allowed_module_power_2']

    return branch_circuit_current_limit


def cms_model(switch_soc_array: list, before_switch_current_array: list, after_switch_current_array: list,
              initial_charging_current: dict, battery_capacity: dict, 
              initial_status_flex_charging: dict, initial_battery_soc: dict,
              branch_circuit_current_limit: dict,
              device_id: str, module_type: str, model_trigger_time: int,
              initial_battery_id: dict, simulated_battery_label: dict,
              system_mode: str):
    '''
    arguments:
        data_struct: dict
            basic_info: dict, battery and device infos
            service_info: dict, swap service order info

    returns:
        simulation_results

    '''

    try:
        with open('./Mapping/map_electricity_price.json', 'r', encoding='utf-8') as fp:
            map_electricity_price = json.loads(fp.read())
    except Exception as e:
        print(e)

    try:
        with open('./Mapping/map_soc_temp_ocv.json', 'r', encoding='utf-8') as fp:
            map_soc_temp_ocv = json.loads(fp.read())
    except Exception as e:
        print(e)

    try:
        with open('./Mapping/map_soc_temp_current.json', 'r', encoding='utf-8') as fp:
            map_soc_cur = json.loads(fp.read())
    except Exception as e:
        print(e)

    try:
        with open('./Mapping/map_soc_temp_voltage.json', 'r', encoding='utf-8') as fp:
            map_soc_vol = json.loads(fp.read())
    except Exception as e:
        print(e)

    simulated_result = charging_mode_simulation_feedback_system(
                            switch_soc_array=switch_soc_array, 
                            before_switch_current_array=before_switch_current_array, after_switch_current_array=after_switch_current_array,
                            current_pre=initial_charging_current, battery_capacity=battery_capacity, 
                            status_flex_charging_pre=initial_status_flex_charging, init_soc=initial_battery_soc,
                            map_soc_cur=map_soc_cur, map_soc_vol=map_soc_vol,
                            branch_circuit_current_limit=branch_circuit_current_limit,
                            map_soc_temp_ocv=map_soc_temp_ocv, module_type=module_type,
                            device_id=device_id, model_trigger_time=model_trigger_time, map_electricity_price=map_electricity_price,
                            initial_battery_id=initial_battery_id, simulated_battery_label=simulated_battery_label,
                            system_mode=system_mode
                            )
        
    return simulated_result
