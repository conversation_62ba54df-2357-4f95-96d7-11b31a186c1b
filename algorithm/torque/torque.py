import json
import time
import grpc
import numpy as np
import pandas as pd
import requests
from concurrent import futures
from pymongo import MongoClient

if __name__ == '__main__':
    import os
    from sys import path

    print(os.path.dirname(os.path.abspath(__file__)))
    path.append(f"{os.path.dirname(os.path.abspath(__file__))}/../")

    from config.config import init_config

    init_config()

from proto import torque_pb2, torque_pb2_grpc
from logger.logger import init_logger
from config.config import get_config

config = {}
while len(config) == 0:
    config = get_config()
    time.sleep(0.5)
logger = init_logger(config, "Torque-feature-calculation", 'INFO')


class TorqueServicer(torque_pb2_grpc.TorqueServicer):
    def CalculateTorqueFeature(self, request, context):
        pool = futures.ThreadPoolExecutor(1)
        request_id = request.requestId
        service_list = [(serve.env, serve.service_id) for serve in request.serviceLst]
        start_time = [str(serve.start_time) for serve in request.serviceLst]
        end_time = [str(serve.end_time) for serve in request.serviceLst]
        device_type = request.deviceType
        logger.info("get torque parameters, request_id: {0}, service_list: {1}".format(request_id, service_list))
        for i in range(len(service_list)):
            try:
                feature_list, torque_report = pd.DataFrame(), pd.DataFrame()
                pool.submit(run, request_id, feature_list, start_time[i], end_time[i], service_list[i], device_type)
            except Exception as e:
                return torque_pb2.TorqueFeatureResponse(err_code=1, message=f"fail to CalculateTorqueFeature: {e}")
        return torque_pb2.TorqueFeatureResponse(err_code=0, message="success")


def login_mongodb():
    client = MongoClient(config['mongodb.welkin.uri'])
    return client


def get_collection(client):
    # 指定mongodb的数据集
    db_name, collection_name = 'factoryData', 'torque-feature-calculation'
    db = client[db_name]
    collection = db[collection_name]
    return collection


def axis_feature(axis, result):
    # 计算特征值：有数据的正常计算，无数据的置零
    if axis.shape[0] > 0:
        # 四轮推杆匀速缩回过程扭矩均值绝对值(左前）
        result['left_front_push_rod_mean_abs'] = [abs(axis[(axis["pl_step_num"] == 18) & (axis["axis_num"] == '29') & (
                axis["speed"] <= -22)]["torque"].mean())]
        # 四轮推杆匀速缩回过程扭矩均值绝对值(右前）
        result['right_front_push_rod_mean_abs'] = [abs(axis[(axis["pl_step_num"] == 18) & (axis["axis_num"] == '30') & (
                axis["speed"] <= -22)]["torque"].mean())]
        # 四轮推杆匀速缩回过程扭矩均值绝对值(左后）
        result['left_back_push_rod_mean_abs'] = [abs(axis[(axis["pl_step_num"] == 18) & (axis["axis_num"] == '33') & (
                axis["speed"] <= -22)]["torque"].mean())]
        # 四轮推杆匀速缩回过程扭矩均值绝对值(右后）
        result['right_back_push_rod_mean_abs'] = [abs(axis[(axis["pl_step_num"] == 18) & (axis["axis_num"] == '34') & (
                axis["speed"] <= -22)]["torque"].mean())]
        # 四轮推杆匀速缩回过程扭矩极值的绝对值(左前）
        result['left_front_push_rod_extremum_abs'] = [
            abs(axis[(axis["pl_step_num"] == 18) & (axis["axis_num"] == '29') & (
                    axis["speed"] <= -22)]["torque"].min())]
        # 四轮推杆匀速缩回过程扭矩极值的绝对值(右前）
        result['right_front_push_rod_extremum_abs'] = [
            abs(axis[(axis["pl_step_num"] == 18) & (axis["axis_num"] == '30') & (
                    axis["speed"] <= -22)]["torque"].min())]
        # 四轮推杆匀速缩回过程扭矩极值的绝对值(左后）
        result['left_back_push_rod_extremum_abs'] = [
            abs(axis[(axis["pl_step_num"] == 18) & (axis["axis_num"] == '33') & (
                    axis["speed"] <= -22)]["torque"].min())]
        # 四轮推杆匀速缩回过程扭矩极值的绝对值(右后）
        result['right_back_push_rod_extremum_abs'] = [
            abs(axis[(axis["pl_step_num"] == 18) & (axis["axis_num"] == '34') & (
                    axis["speed"] <= -22)]["torque"].min())]

        # 开门过程扭矩均值绝对值(左）
        result['left_hatch_door_mean_abs'] = [abs(axis[(axis["pl_step_num"] == 2) & (axis["axis_num"] == '16') & (
                axis["position"] >= 50) & (axis["position"] <= 920)]["torque"].mean())]
        # 开门过程扭矩均值绝对值(右）
        result['right_hatch_door_mean_abs'] = [abs(axis[(axis["pl_step_num"] == 2) & (axis["axis_num"] == '17') & (
                axis["position"] >= 50) & (axis["position"] <= 920)]["torque"].mean())]
        # 开门过程扭矩极值绝对值(左）
        result['left_hatch_door_extremum_abs'] = [abs(axis[(axis["pl_step_num"] == 2) & (axis["axis_num"] == '16') & (
                axis["position"] >= 50) & (axis["position"] <= 920)]["torque"].min())]
        # 开门过程扭矩极值绝对值(右）
        result['right_hatch_door_extremum_abs'] = [abs(axis[(axis["pl_step_num"] == 2) & (axis["axis_num"] == '17') & (
                axis["position"] >= 50) & (axis["position"] <= 920)]["torque"].min())]

        # RGV平移至加解锁位均值
        result['rgv_move_mean_abs'] = [
            abs(axis[(axis["pl_step_num"] == 12) & (axis["axis_num"] == '36') & (axis["speed"] > 0)]["torque"].mean())]
        # RGV带载举升至销子位均值
        result['rgv_lift_mean_abs'] = [
            abs(axis[(axis["pl_step_num"] == 14) & (axis["axis_num"] == '37') & (axis["speed"] > 60)]["torque"].mean())]

        # 定位销伸出过程均值（左前）
        result['left_front_locate_pin_mean_abs'] = [
            abs(axis[(axis["pl_step_num"] == 14) & (axis["axis_num"] == '26') & (axis["speed"] > 50)]["torque"].mean())]
        # 定位销伸出过程均值（右后）
        result['right_back_locate_pin_mean_abs'] = [
            abs(axis[(axis["pl_step_num"] == 14) & (axis["axis_num"] == '28') & (axis["speed"] > 50)]["torque"].mean())]

        # 导向条伸出均值（前）
        result['front_guide_chain_mean_abs'] = \
            [abs(axis[(axis["pl_step_num"] == 7) & (axis["axis_num"] == '31') & (axis["speed"] > 0)]["torque"].mean())]
        # 导向条伸出均值（后）
        result['back_guide_chain_mean_abs'] = \
            [abs(axis[(axis["pl_step_num"] == 7) & (axis["axis_num"] == '35') & (axis["speed"] > 0)]["torque"].mean())]

        # 1#加解锁枪——解锁快旋阶段扭矩均值
        result['lock_unlock_gun_mean_abs_1'] = \
            [abs(axis[(axis["pl_step_num"] == 4) & (axis["axis_num"] == '4') & (axis["speed"] >= 4920)][
                     "torque"].mean())]
        # 2#加解锁枪——解锁快旋阶段扭矩均值
        result['lock_unlock_gun_mean_abs_2'] = \
            [abs(axis[(axis["pl_step_num"] == 4) & (axis["axis_num"] == '5') & (axis["speed"] >= 4920)][
                     "torque"].mean())]
        # 3#加解锁枪——解锁快旋阶段扭矩均值
        result['lock_unlock_gun_mean_abs_3'] = \
            [abs(axis[(axis["pl_step_num"] == 4) & (axis["axis_num"] == '6') & (axis["speed"] >= 4920)][
                     "torque"].mean())]
        # 4#加解锁枪——解锁快旋阶段扭矩均值
        result['lock_unlock_gun_mean_abs_4'] = \
            [abs(axis[(axis["pl_step_num"] == 4) & (axis["axis_num"] == '7') & (axis["speed"] >= 4920)][
                     "torque"].mean())]
        # 5#加解锁枪——解锁快旋阶段扭矩均值
        result['lock_unlock_gun_mean_abs_5'] = \
            [abs(axis[(axis["pl_step_num"] == 4) & (axis["axis_num"] == '8') & (axis["speed"] >= 4920)][
                     "torque"].mean())]
        # 6#加解锁枪——解锁快旋阶段扭矩均值
        result['lock_unlock_gun_mean_abs_6'] = \
            [abs(axis[(axis["pl_step_num"] == 4) & (axis["axis_num"] == '9') & (axis["speed"] >= 4920)][
                     "torque"].mean())]
        # 7#加解锁枪——解锁快旋阶段扭矩均值
        result['lock_unlock_gun_mean_abs_7'] = \
            [abs(axis[(axis["pl_step_num"] == 4) & (axis["axis_num"] == '10') & (axis["speed"] >= 4920)][
                     "torque"].mean())]
        # 8#加解锁枪——解锁快旋阶段扭矩均值
        result['lock_unlock_gun_mean_abs_8'] = \
            [abs(axis[(axis["pl_step_num"] == 4) & (axis["axis_num"] == '11') & (axis["speed"] >= 4920)][
                     "torque"].mean())]
        # 9#加解锁枪——解锁快旋阶段扭矩均值
        result['lock_unlock_gun_mean_abs_9'] = \
            [abs(axis[(axis["pl_step_num"] == 4) & (axis["axis_num"] == '12') & (axis["speed"] >= 4920)][
                     "torque"].mean())]
        # 10#加解锁枪——解锁快旋阶段扭矩均值
        result['lock_unlock_gun_mean_abs_10'] = \
            [abs(axis[(axis["pl_step_num"] == 4) & (axis["axis_num"] == '13') & (axis["speed"] >= 4920)][
                     "torque"].mean())]

        # 接驳机空载匀速运动过程扭矩均值
        result['shuttle_mean_abs'] = \
            [abs(
                axis[(axis["pl_step_num"] == 8) & (axis["axis_num"] == '40') & (axis["speed"] > 155)]["torque"].mean())]

        # 货叉在目标仓带载缩回均值
        result['pallet_fork_mean_abs'] = \
            [axis[(axis["bc_step_num"] == 2) & (axis["axis_num"] == '1') & (axis["speed"].abs() >= 370)][
                 "torque"].abs().mean()]

        # 堆垛机空载行走至目标仓均值
        result['stacker_shift_noload_mean_abs'] = \
            [axis[(axis["bc_step_num"] == 1) & (axis["axis_num"] == '2') & (axis["position"].abs() >= 1000)][
                 "torque"].abs().mean()]
        # 堆垛机带载行走至升降仓对接位均值
        result['stacker_shift_load_mean_abs'] = \
            [axis[(axis["bc_step_num"] == 3) & (axis["axis_num"] == '2') & (axis["position"].abs() >= 1000)][
                 "torque"].abs().mean()]

        # 堆垛机带载升至目标仓均值
        result['stacker_lift_load_mean_abs'] = \
            [axis[(axis["bc_step_num"] == 9) & (axis["axis_num"] == '3') & (axis["position"].abs() >= 500)][
                 "torque"].abs().mean()]
        # 堆垛机空载降至初始位均值
        result['stacker_lift_noload_mean_abs'] = \
            [axis[(axis["bc_step_num"] == 11) & (axis["axis_num"] == '3') & (axis["position"].abs() >= 400)][
                 "torque"].abs().mean()]
        # result = result.fillna(0)
        return result
    else:
        result['left_front_push_rod_mean_abs'] = [0.0]
        # 匀速缩回过程扭矩均值绝对值(右前）
        result['right_front_push_rod_mean_abs'] = [0.0]
        # 匀速缩回过程扭矩均值绝对值(左后）
        result['left_back_push_rod_mean_abs'] = [0.0]
        # 匀速缩回过程扭矩均值绝对值(右后）
        result['right_back_push_rod_mean_abs'] = [0.0]
        # 匀速缩回过程扭矩极值的绝对值(左前）
        result['left_front_push_rod_extremum_abs'] = [0.0]
        # 匀速缩回过程扭矩极值的绝对值(右前）
        result['right_front_push_rod_extremum_abs'] = [0.0]
        # 匀速缩回过程扭矩极值的绝对值(左后）
        result['left_back_push_rod_extremum_abs'] = [0.0]
        # 匀速缩回过程扭矩极值的绝对值(右后）
        result['right_back_push_rod_extremum_abs'] = [0.0]

        # 开门过程扭矩均值绝对值(左）
        result['left_hatch_door_mean_abs'] = [0.0]
        # 开门过程扭矩均值绝对值(右）
        result['right_hatch_door_mean_abs'] = [0.0]
        # 开门过程扭矩均值绝对值(左）
        result['left_hatch_door_extremum_abs'] = [0.0]
        # 开门过程扭矩均值绝对值(右）
        result['right_hatch_door_extremum_abs'] = [0.0]

        # RGV平移至加解锁位均值
        result['rgv_move_mean_abs'] = [0.0]
        # RGV带载举升至销子位均值
        result['rgv_lift_mean_abs'] = [0.0]

        # 定位销伸出过程均值（左前）
        result['left_front_locate_pin_mean_abs'] = [0.0]
        # 定位销伸出过程均值（右后）
        result['right_back_locate_pin_mean_abs'] = [0.0]

        # 导向条伸出均值（前）
        result['front_guide_chain_mean_abs'] = [0.0]
        # 导向条伸出均值（后）
        result['back_guide_chain_mean_abs'] = [0.0]

        # 1#加解锁枪——解锁快旋阶段扭矩均值
        result['lock_unlock_gun_mean_abs_1'] = [0.0]
        # 2#加解锁枪——解锁快旋阶段扭矩均值
        result['lock_unlock_gun_mean_abs_2'] = [0.0]
        # 3#加解锁枪——解锁快旋阶段扭矩均值
        result['lock_unlock_gun_mean_abs_3'] = [0.0]
        # 4#加解锁枪——解锁快旋阶段扭矩均值
        result['lock_unlock_gun_mean_abs_4'] = [0.0]
        # 5#加解锁枪——解锁快旋阶段扭矩均值
        result['lock_unlock_gun_mean_abs_5'] = [0.0]
        # 6#加解锁枪——解锁快旋阶段扭矩均值
        result['lock_unlock_gun_mean_abs_6'] = [0.0]
        # 7#加解锁枪——解锁快旋阶段扭矩均值
        result['lock_unlock_gun_mean_abs_7'] = [0.0]
        # 8#加解锁枪——解锁快旋阶段扭矩均值
        result['lock_unlock_gun_mean_abs_8'] = [0.0]
        # 9#加解锁枪——解锁快旋阶段扭矩均值
        result['lock_unlock_gun_mean_abs_9'] = [0.0]
        # 10#加解锁枪——解锁快旋阶段扭矩均值
        result['lock_unlock_gun_mean_abs_10'] = [0.0]

        # 接驳机空载匀速运动过程扭矩均值
        result['shuttle_mean_abs'] = [0.0]

        # 货叉在目标仓带载缩回均值
        result['pallet_fork_mean_abs'] = [0.0]

        # 堆垛机空载行走至目标仓均值
        result['stacker_shift_noload_mean_abs'] = [0.0]
        # 堆垛机带载行走至升降仓对接位均值
        result['stacker_shift_load_mean_abs'] = [0.0]

        # 堆垛机带载升至目标仓均值
        result['stacker_lift_load_mean_abs'] = [0.0]
        # 堆垛机空载降至初始位均值
        result['stacker_lift_noload_mean_abs'] = [0.0]
    return result


def get_html_data(url):
    # 通过爬虫获取天宫api数据
    headers = {'User-Agent': 'Chrome/58.0.3029.110'}
    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        return json.loads(response.text)
    return None


def get_url(env, device_id, start_time, end_time, service_id, device_type):
    # 基于查询指令生成天宫查询的url
    url = "{0}/quality/factory/plc-record/{1}/{2}?start_time={3}&end_time={4}&service_id={5}".format(
        config[f'welkin.{env}'], device_type, device_id, start_time, end_time, service_id)
    return url


def service_process(service_Id, service_data):
    # 指定数据集并开始计算特征
    if service_data.shape[0] > 0:
        plc_data = service_data[service_data['service_id'] == service_Id]
        not_found_plc_record = False
    else:
        plc_data = pd.DataFrame()
        not_found_plc_record = True
    result = pd.DataFrame()
    result_process = axis_feature(plc_data, result)
    incomplete_plc_record = result_process.iloc[:, :-2].isnull().any().any()
    result_process = result_process.fillna(0)
    return result_process, not_found_plc_record, incomplete_plc_record


def data_parse(test_data):
    # 对数据进行透视
    if len(test_data['data']) > 0:
        start = pd.DataFrame(test_data)[['device_id', 'service_id',
                                         'start_time', 'end_time', 'total', 'data']]  # 取有信息的数据
        parsed_result = pd.DataFrame()
        for i in range(len(start)):
            data = pd.DataFrame(start['data'][i]).reset_index()
            id_vars = ['timestamp', 'pl_step_num', 'bc_step_num', 'index']
            value_vars = data.columns.to_list()
            for exp_var in id_vars:  # 去除用于索引的字段
                try:
                    value_vars.remove(exp_var)
                except Exception as e:
                    logger.error(
                        f'fail to find idx var, 未从列表[{value_vars}]中找到元素"{exp_var}"，可能跟高速录播数据结构变更有关，请及时修复！错误原因：{e}')
            try:
                test = pd.melt(data, id_vars=id_vars, value_vars=value_vars, var_name=['axis_num'])
                result = test.pivot(index=['timestamp', 'pl_step_num', 'bc_step_num', 'axis_num'],
                                    columns='index', values='value').reset_index()  # 数据透视
            except Exception as e:
                logger.error(f'fail to operate on DataFrame, 对pandas.DataFrame进行操作时报错，错误原因：{e}')
                raise
            parsed_result = pd.concat([parsed_result, result])
        res = parsed_result.sort_values(by=['timestamp'])
        common_cols = start.columns.to_list()[:-1]
        for col in common_cols:  # 补充订单信息
            res[col] = start.loc[0, col]
        return res
    else:
        start = pd.DataFrame(columns=['device_id', 'service_id', 'start_time', 'end_time', 'total', 'data'])
        return start


def run(request_id, feature_list, start_time, end_time, service, device_type):
    # 登录mongodb
    client = login_mongodb()
    collection = get_collection(client)

    try:  # 执行计算主程序，按照订单清单进行遍历
        process_start_time = time.time()
        env = service[0]
        service_id = service[1]
        # logger.info(f'{i + 1}-正在处理订单【{service_id}】')
        url = get_url(env, service_id[:24], start_time, end_time, service_id, device_type)
        # logger.info(f'正在请求高速录播数据：{url}')
        test_data = get_html_data(url)
        get_service_time = time.time()
        service_time = get_service_time - process_start_time
        logger.info(f"success to get plc data, 已成功获取【{service_id}】的高速录播时序，所用时间为：【{service_time}】")

        try:  # 解析高速录播数据
            input_data = data_parse(test_data)
            # logger.info(f'已成功解析订单【{service_id}】的高速录播时序')
        except Exception as e:
            input_data = data_parse(test_data)
            logger.error(f'fail to parse plc data, 解析【{service_id}】高速录播时序失败！错误原因：{e}')
        get_process_time = time.time()
        process_time = get_process_time - get_service_time
        logger.info(f"处理第订单数据所用时间为：【{process_time}】")
        try:  # 计算出厂报告扭矩特征值
            result_process, not_found_plc_record, incomplete_plc_record = service_process(service_id, input_data)
            # logger.info(f'已计算得到扭矩特征结果：{result_process}')
        except Exception as e:
            logger.error(f'fail to calculate torque feature, 提取【{service_id}】出厂报告扭矩特征失败！错误原因：{e}')
        get_feature_time = time.time()
        feature_time = get_feature_time - get_process_time
        logger.info(f"处理订单特征所用时间为：【{feature_time}】")
        try:  # 生产mongodb数据
            feature_list = pd.concat([feature_list, result_process])
            part_report = pd.DataFrame()
            part_report['request_id'] = [request_id]
            part_report['env'] = [env]
            part_report['device_id'] = service_id[:24]
            part_report['service_id'] = [service_id]
            part_report['not_found_plc_record'] = [not_found_plc_record]
            part_report['incomplete_plc_record'] = [incomplete_plc_record]
            timestamp = time.time()
            part_report['created_time'] = int(timestamp * 1000)
            part_report['updated_time'] = int(timestamp * 1000)
            if len(feature_list) > 0:
                for ii in range(len(feature_list)):
                    feature_result = []
                    for jj in range(len(feature_list.columns)):
                        tmp_dic = {
                            'name': feature_list.columns[jj],
                            'value': float(feature_list.iloc[ii, jj])
                        }
                        feature_result.append(tmp_dic)
            else:
                feature_result = []
                for jj in range(len(feature_list.columns)):
                    tmp_dic = {
                        'name': feature_list.columns[jj],
                        'value': 0.0
                    }
                    feature_result.append(tmp_dic)
            part_report['feature_results'] = [feature_result]
            json_str = part_report.to_dict('records')[0]
        except Exception as e:
            logger.error(f'fail to generate mongo data, 生成mongodb待存储数据失败！错误原因：{e}')
            return False

        try:  # 存储数据至mongodb
            collection.update_one({"service_id": service_id}, {'$set': json_str}, upsert=True)
            # logger.info(f"订单【{service_id}】扭矩数据已写入mongodb")
        except Exception as e:
            logger.error(f'fail to write mongo, 未成功存储数据【{service_id}】扭矩数据至mongodb, 错误原因：{e}')
            return False
        get_write_time = time.time()
        write_time = get_write_time - get_feature_time
        logger.info(f"写入订单特征所用时间为：【{write_time}】")

    except Exception as e:
        logger.error(f'fail in calculation service, 计算服务意外终止，发生错误：{e}')
        return False

    client.close()
    logger.info(f"succeed to process service, request_id: {request_id}, service_id: {service_id}")
    return True


if __name__ == '__main__':
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))
    torque_pb2_grpc.add_TorqueServicer_to_server(TorqueServicer(), server)
    server.add_insecure_port('[::]:5001')
    logger.info("start rpc server on [::]:5001")
    server.start()
    server.wait_for_termination()
