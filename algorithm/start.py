import time
import grpc
import multiprocessing as mp
from concurrent import futures

from proto import torque_pb2, torque_pb2_grpc, algorithm_service_pb2, algorithm_service_pb2_grpc, cms_pb2, cms_pb2_grpc
from logger.logger import init_logger
from config.config import init_config, get_config

init_config()

from BSA.bsa import DetectImageServicer
from torque.torque import TorqueServicer
from CMS.cms import CMSServicer


config = {}
while len(config) == 0:
    config = get_config()
    time.sleep(0.5)
logger = init_logger(config, "RPC-Service", 'INFO')


def start_grpc_server(port, max_workers, add_servicer, servicer):
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=max_workers))
    add_servicer(servicer(), server)
    server.add_insecure_port(f'[::]:{port}')
    logger.info(f"start rpc server on [::]:{port}")
    server.start()
    server.wait_for_termination()


if __name__ == '__main__':
    args_list = [
        (5000, 10, algorithm_service_pb2_grpc.add_DetectImageServicer_to_server, DetectImageServicer),
        (5001, 10, torque_pb2_grpc.add_TorqueServicer_to_server, TorqueServicer),
        (5002, 1, cms_pb2_grpc.add_CMSServicer_to_server, CMSServicer)
    ]
    pool = mp.Pool()
    for args in args_list:
        pool.apply_async(start_grpc_server, args)
    pool.close()
    pool.join()
