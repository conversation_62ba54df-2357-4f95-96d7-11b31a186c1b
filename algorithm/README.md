### Run algorithm service

```shell
python start.py --config <path-to-config>
```

### Update algorithm service

1. Define proto file in `./proto`

2. Generate corresponding go/py file

    ```shell
    go get google.golang.org/protobuf
    
    protoc --go_out=. --go-grpc_out=. proto/<filename>.proto
    ```
    
    ```shell
    python -m pip install grpcio
    python -m pip install grpcio-tools
    
    python -m grpc_tools.protoc --python_out=. --grpc_python_out=. -I. proto/<filename>.proto
    ```

3. Write server-side code in python, example: 

    `./torque/torque.py`
    ```python
    from proto import torque_pb2, torque_pb2_grpc
    from logger.logger import init_logger
    from config.config import get_config
    
    config = get_config()
    logger = init_logger(config, "Torque-feature-calculation", 'INFO')
    
    class TorqueServicer(torque_pb2_grpc.TorqueServicer):
        def CalculateTorqueFeature(self, request, context):
            # write your code here
            return torque_pb2.TorqueFeatureResponse(err_code=0, message="success")
    ```
    
    add following code to `./start.py`
    ```python
    from proto import torque_pb2, torque_pb2_grpc
    
    from torque.torque import TorqueServicer
    
    if __name__ == '__main__':
        # ...
        torque_pb2_grpc.add_TorqueServicer_to_server(TorqueServicer(), server)
        # ...
    ```

4. Write client-side code in go, example: 

   add following code to `../client/grpc.go`
   
   ```go
   import "git.nevint.com/welkin2/welkin-backend/rpc-service/proto"
   
   func (r *GRPCClient) CalculateTorqueFeature(ctx *gin.Context, ...) {
       // ... 
       client := proto.NewTorqueClient(conn)
       serviceList := make([]*proto.ServiceInfo, 0)
       
       req := proto.TorqueFeatureRequest{
           RequestId:  requestId,
           DeviceId:   deviceId,
           DeviceType: project,
           ServiceLst: serviceList,
       }
       resp, err := client.CalculateTorqueFeature(ctx, &req)
       // ...
   }
   ```
