#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time:2020.09.12
# @author:xhrg
# @email:<EMAIL>
import os

import urllib.request
from urllib.error import HTTPError
from urllib import parse


def http_request(url, timeout, headers={}):
    try:
        request = urllib.request.Request(url, headers=headers)
        res = urllib.request.urlopen(request, timeout=timeout)
        body = res.read().decode("utf-8")
        return res.code, body
    except HTTPError as e:
        if e.code == 304:
            # print("http_request error,code is 304, maybe you should check secret")
            return 304, None
        # print(f"http_request error,code is {e.code}, msg is {e}, url: {url}")
        raise e


def url_encode(params):
    return parse.urlencode(params)


def makedirs_wrapper(path):
    os.makedirs(path, exist_ok=True)
