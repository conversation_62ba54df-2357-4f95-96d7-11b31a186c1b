import logging
import os
from datetime import datetime
from logging import handlers
from pythonjsonlogger import jsonlogger


class CustomJsonFormatter(jsonlogger.JsonFormatter):
    def add_fields(self, log_record, record, message_dict):
        super(CustomJsonFormatter, self).add_fields(log_record, record, message_dict)
        if not log_record.get('logtime'):
            now = time_format(datetime.now().astimezone())
            log_record['logtime'] = now
        if log_record.get('level'):
            log_record['level'] = log_record['level'].upper()
        else:
            log_record['level'] = record.levelname
        if not log_record.get('logger'):
            log_record['logger'] = record.name
        if not log_record.get('caller'):
            log_record['caller'] = f"{record.pathname}:{record.lineno}"
        if not log_record.get('message'):
            log_record['message'] = record.message
        if not log_record.get('env'):
            log_record['env'] = get_env()
        if not log_record.get('system'):
            log_record['system'] = get_service_name()
        if not log_record.get('module'):
            log_record['module'] = get_inst_name()
        if not log_record.get('customindex'):
            log_record['customindex'] = get_inst_name()


def init_logger(config, name, loglevel):
    log_path = config['log.path']
    local_log = os.getenv('LOCAL_LOG')
    if local_log is not None:
        log_path = local_log
    if not os.path.exists(log_path):
        os.makedirs(log_path)
    runtime_log = f"{log_path}/rpc-service-runtime.json"

    logger = logging.getLogger(name)
    logger.setLevel(loglevel)
    formatter = CustomJsonFormatter(
        '%(level)s%(logtime)s%(logger)s%(caller)s%(message)s%(env)s%(system)s%(module)s%(customindex)s')

    time_rotating_file_handler = handlers.TimedRotatingFileHandler(filename=runtime_log, when='D',
                                                                   backupCount=config['log.backup_count'])
    time_rotating_file_handler.setLevel(loglevel)
    time_rotating_file_handler.setFormatter(formatter)
    logger.addHandler(time_rotating_file_handler)
    return logger


def time_format(dt):
    return "{0}.{1}{2}".format(
        dt.strftime('%Y-%m-%dT%H:%M:%S'),
        "{:03}".format(int(dt.microsecond // 1e3)),
        dt.strftime('%z')
    )


def get_env():
    env = os.getenv('ENV')
    if env is None:
        env = os.getenv("K8S_ENV")
        if env is None:
            return ""
    return env


def get_service_name():
    service_name = os.getenv("SERVICE_NAME")
    if service_name is None:
        return ""
    return service_name


def get_inst_name():
    inst_name = os.getenv("INST_NAME")
    if inst_name is None:
        return ""
    return inst_name
