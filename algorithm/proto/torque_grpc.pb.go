// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.22.3
// source: proto/torque.proto

package proto

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Torque_CalculateTorqueFeature_FullMethodName = "/proto.Torque/CalculateTorqueFeature"
)

// TorqueClient is the client API for Torque service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TorqueClient interface {
	CalculateTorqueFeature(ctx context.Context, in *TorqueFeatureRequest, opts ...grpc.CallOption) (*TorqueFeatureResponse, error)
}

type torqueClient struct {
	cc grpc.ClientConnInterface
}

func NewTorqueClient(cc grpc.ClientConnInterface) TorqueClient {
	return &torqueClient{cc}
}

func (c *torqueClient) CalculateTorqueFeature(ctx context.Context, in *TorqueFeatureRequest, opts ...grpc.CallOption) (*TorqueFeatureResponse, error) {
	out := new(TorqueFeatureResponse)
	err := c.cc.Invoke(ctx, Torque_CalculateTorqueFeature_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TorqueServer is the server API for Torque service.
// All implementations must embed UnimplementedTorqueServer
// for forward compatibility
type TorqueServer interface {
	CalculateTorqueFeature(context.Context, *TorqueFeatureRequest) (*TorqueFeatureResponse, error)
	mustEmbedUnimplementedTorqueServer()
}

// UnimplementedTorqueServer must be embedded to have forward compatible implementations.
type UnimplementedTorqueServer struct {
}

func (UnimplementedTorqueServer) CalculateTorqueFeature(context.Context, *TorqueFeatureRequest) (*TorqueFeatureResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CalculateTorqueFeature not implemented")
}
func (UnimplementedTorqueServer) mustEmbedUnimplementedTorqueServer() {}

// UnsafeTorqueServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TorqueServer will
// result in compilation errors.
type UnsafeTorqueServer interface {
	mustEmbedUnimplementedTorqueServer()
}

func RegisterTorqueServer(s grpc.ServiceRegistrar, srv TorqueServer) {
	s.RegisterService(&Torque_ServiceDesc, srv)
}

func _Torque_CalculateTorqueFeature_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TorqueFeatureRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TorqueServer).CalculateTorqueFeature(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Torque_CalculateTorqueFeature_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TorqueServer).CalculateTorqueFeature(ctx, req.(*TorqueFeatureRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Torque_ServiceDesc is the grpc.ServiceDesc for Torque service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Torque_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "proto.Torque",
	HandlerType: (*TorqueServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CalculateTorqueFeature",
			Handler:    _Torque_CalculateTorqueFeature_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/torque.proto",
}
