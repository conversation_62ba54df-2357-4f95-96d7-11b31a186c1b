# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: algorithm_service.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x17\x61lgorithm_service.proto\x12\x05proto\"-\n\rDetectRequest\x12\x0c\n\x04\x66ile\x18\x01 \x01(\t\x12\x0e\n\x06sha512\x18\x02 \x01(\t\"k\n\x0e\x44\x65tectResponse\x12\x10\n\x08\x65rr_code\x18\x01 \x01(\x05\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x10\n\x08\x61\x62normal\x18\x03 \x01(\x08\x12\x14\n\x0c\x61\x62normal_img\x18\x04 \x01(\t\x12\x0e\n\x06sha512\x18\x05 \x01(\t2S\n\x0b\x44\x65tectImage\x12\x44\n\x13\x44\x65tectAbnormalImage\x12\x14.proto.DetectRequest\x1a\x15.proto.DetectResponse\"\x00\x62\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'algorithm_service_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _DETECTREQUEST._serialized_start=34
  _DETECTREQUEST._serialized_end=79
  _DETECTRESPONSE._serialized_start=81
  _DETECTRESPONSE._serialized_end=188
  _DETECTIMAGE._serialized_start=190
  _DETECTIMAGE._serialized_end=273
# @@protoc_insertion_point(module_scope)
