# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import proto.torque_pb2 as torque__pb2


class TorqueStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CalculateTorqueFeature = channel.unary_unary(
                '/proto.Torque/CalculateTorqueFeature',
                request_serializer=torque__pb2.TorqueFeatureRequest.SerializeToString,
                response_deserializer=torque__pb2.TorqueFeatureResponse.FromString,
                )


class TorqueServicer(object):
    """Missing associated documentation comment in .proto file."""

    def CalculateTorqueFeature(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_TorqueServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CalculateTorqueFeature': grpc.unary_unary_rpc_method_handler(
                    servicer.CalculateTorqueFeature,
                    request_deserializer=torque__pb2.TorqueFeatureRequest.FromString,
                    response_serializer=torque__pb2.TorqueFeatureResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'proto.Torque', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class Torque(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def CalculateTorqueFeature(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/proto.Torque/CalculateTorqueFeature',
            torque__pb2.TorqueFeatureRequest.SerializeToString,
            torque__pb2.TorqueFeatureResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
