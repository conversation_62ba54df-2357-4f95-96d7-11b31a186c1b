# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from proto import cms_pb2 as proto_dot_cms__pb2


class CMSStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CalculateCMS = channel.unary_unary(
                '/proto.CMS/CalculateCMS',
                request_serializer=proto_dot_cms__pb2.CMSRequest.SerializeToString,
                response_deserializer=proto_dot_cms__pb2.CMSResponse.FromString,
                )


class CMSServicer(object):
    """Missing associated documentation comment in .proto file."""

    def CalculateCMS(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_CMSServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CalculateCMS': grpc.unary_unary_rpc_method_handler(
                    servicer.CalculateCMS,
                    request_deserializer=proto_dot_cms__pb2.CMSRequest.FromString,
                    response_serializer=proto_dot_cms__pb2.CMSResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'proto.CMS', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class CMS(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def CalculateCMS(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/proto.CMS/CalculateCMS',
            proto_dot_cms__pb2.CMSRequest.SerializeToString,
            proto_dot_cms__pb2.CMSResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
