# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import proto.algorithm_service_pb2 as algorithm__service__pb2


class DetectImageStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.DetectAbnormalImage = channel.unary_unary(
                '/proto.DetectImage/DetectAbnormalImage',
                request_serializer=algorithm__service__pb2.DetectRequest.SerializeToString,
                response_deserializer=algorithm__service__pb2.DetectResponse.FromString,
                )


class DetectImageServicer(object):
    """Missing associated documentation comment in .proto file."""

    def DetectAbnormalImage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_DetectImageServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'DetectAbnormalImage': grpc.unary_unary_rpc_method_handler(
                    servicer.DetectAbnormalImage,
                    request_deserializer=algorithm__service__pb2.DetectRequest.FromString,
                    response_serializer=algorithm__service__pb2.DetectResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'proto.DetectImage', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class DetectImage(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def DetectAbnormalImage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/proto.DetectImage/DetectAbnormalImage',
            algorithm__service__pb2.DetectRequest.SerializeToString,
            algorithm__service__pb2.DetectResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
