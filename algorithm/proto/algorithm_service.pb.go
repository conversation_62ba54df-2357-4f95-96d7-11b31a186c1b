// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.22.3
// source: proto/algorithm_service.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DetectRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	File   string `protobuf:"bytes,1,opt,name=file,proto3" json:"file,omitempty"`
	Sha512 string `protobuf:"bytes,2,opt,name=sha512,proto3" json:"sha512,omitempty"`
}

func (x *DetectRequest) Reset() {
	*x = DetectRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_algorithm_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetectRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectRequest) ProtoMessage() {}

func (x *DetectRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_algorithm_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectRequest.ProtoReflect.Descriptor instead.
func (*DetectRequest) Descriptor() ([]byte, []int) {
	return file_proto_algorithm_service_proto_rawDescGZIP(), []int{0}
}

func (x *DetectRequest) GetFile() string {
	if x != nil {
		return x.File
	}
	return ""
}

func (x *DetectRequest) GetSha512() string {
	if x != nil {
		return x.Sha512
	}
	return ""
}

type DetectResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrCode     int32  `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	Message     string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Abnormal    bool   `protobuf:"varint,3,opt,name=abnormal,proto3" json:"abnormal,omitempty"`
	AbnormalImg string `protobuf:"bytes,4,opt,name=abnormal_img,json=abnormalImg,proto3" json:"abnormal_img,omitempty"`
	Sha512      string `protobuf:"bytes,5,opt,name=sha512,proto3" json:"sha512,omitempty"`
}

func (x *DetectResponse) Reset() {
	*x = DetectResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_algorithm_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetectResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectResponse) ProtoMessage() {}

func (x *DetectResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_algorithm_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectResponse.ProtoReflect.Descriptor instead.
func (*DetectResponse) Descriptor() ([]byte, []int) {
	return file_proto_algorithm_service_proto_rawDescGZIP(), []int{1}
}

func (x *DetectResponse) GetErrCode() int32 {
	if x != nil {
		return x.ErrCode
	}
	return 0
}

func (x *DetectResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *DetectResponse) GetAbnormal() bool {
	if x != nil {
		return x.Abnormal
	}
	return false
}

func (x *DetectResponse) GetAbnormalImg() string {
	if x != nil {
		return x.AbnormalImg
	}
	return ""
}

func (x *DetectResponse) GetSha512() string {
	if x != nil {
		return x.Sha512
	}
	return ""
}

var File_proto_algorithm_service_proto protoreflect.FileDescriptor

var file_proto_algorithm_service_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68,
	0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x3b, 0x0a, 0x0d, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x68, 0x61, 0x35, 0x31, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61,
	0x35, 0x31, 0x32, 0x22, 0x9c, 0x01, 0x0a, 0x0e, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x72, 0x72, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x61,
	0x62, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x61,
	0x62, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x62, 0x6e, 0x6f, 0x72,
	0x6d, 0x61, 0x6c, 0x5f, 0x69, 0x6d, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61,
	0x62, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x49, 0x6d, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68,
	0x61, 0x35, 0x31, 0x32, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61, 0x35,
	0x31, 0x32, 0x32, 0x53, 0x0a, 0x0b, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x12, 0x44, 0x0a, 0x13, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x41, 0x62, 0x6e, 0x6f, 0x72,
	0x6d, 0x61, 0x6c, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x09, 0x5a, 0x07, 0x2e, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_algorithm_service_proto_rawDescOnce sync.Once
	file_proto_algorithm_service_proto_rawDescData = file_proto_algorithm_service_proto_rawDesc
)

func file_proto_algorithm_service_proto_rawDescGZIP() []byte {
	file_proto_algorithm_service_proto_rawDescOnce.Do(func() {
		file_proto_algorithm_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_algorithm_service_proto_rawDescData)
	})
	return file_proto_algorithm_service_proto_rawDescData
}

var file_proto_algorithm_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_proto_algorithm_service_proto_goTypes = []interface{}{
	(*DetectRequest)(nil),  // 0: proto.DetectRequest
	(*DetectResponse)(nil), // 1: proto.DetectResponse
}
var file_proto_algorithm_service_proto_depIdxs = []int32{
	0, // 0: proto.DetectImage.DetectAbnormalImage:input_type -> proto.DetectRequest
	1, // 1: proto.DetectImage.DetectAbnormalImage:output_type -> proto.DetectResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_proto_algorithm_service_proto_init() }
func file_proto_algorithm_service_proto_init() {
	if File_proto_algorithm_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_algorithm_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetectRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_algorithm_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetectResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_algorithm_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_algorithm_service_proto_goTypes,
		DependencyIndexes: file_proto_algorithm_service_proto_depIdxs,
		MessageInfos:      file_proto_algorithm_service_proto_msgTypes,
	}.Build()
	File_proto_algorithm_service_proto = out.File
	file_proto_algorithm_service_proto_rawDesc = nil
	file_proto_algorithm_service_proto_goTypes = nil
	file_proto_algorithm_service_proto_depIdxs = nil
}
