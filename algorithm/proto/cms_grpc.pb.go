// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.22.3
// source: proto/cms.proto

package proto

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	CMS_CalculateCMS_FullMethodName = "/proto.CMS/CalculateCMS"
)

// CMSClient is the client API for CMS service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CMSClient interface {
	CalculateCMS(ctx context.Context, in *CMSRequest, opts ...grpc.CallOption) (*CMSResponse, error)
}

type cMSClient struct {
	cc grpc.ClientConnInterface
}

func NewCMSClient(cc grpc.ClientConnInterface) CMSClient {
	return &cMSClient{cc}
}

func (c *cMSClient) CalculateCMS(ctx context.Context, in *CMSRequest, opts ...grpc.CallOption) (*CMSResponse, error) {
	out := new(CMSResponse)
	err := c.cc.Invoke(ctx, CMS_CalculateCMS_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CMSServer is the server API for CMS service.
// All implementations must embed UnimplementedCMSServer
// for forward compatibility
type CMSServer interface {
	CalculateCMS(context.Context, *CMSRequest) (*CMSResponse, error)
	mustEmbedUnimplementedCMSServer()
}

// UnimplementedCMSServer must be embedded to have forward compatible implementations.
type UnimplementedCMSServer struct {
}

func (UnimplementedCMSServer) CalculateCMS(context.Context, *CMSRequest) (*CMSResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CalculateCMS not implemented")
}
func (UnimplementedCMSServer) mustEmbedUnimplementedCMSServer() {}

// UnsafeCMSServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CMSServer will
// result in compilation errors.
type UnsafeCMSServer interface {
	mustEmbedUnimplementedCMSServer()
}

func RegisterCMSServer(s grpc.ServiceRegistrar, srv CMSServer) {
	s.RegisterService(&CMS_ServiceDesc, srv)
}

func _CMS_CalculateCMS_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CMSRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CMSServer).CalculateCMS(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CMS_CalculateCMS_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CMSServer).CalculateCMS(ctx, req.(*CMSRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CMS_ServiceDesc is the grpc.ServiceDesc for CMS service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CMS_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "proto.CMS",
	HandlerType: (*CMSServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CalculateCMS",
			Handler:    _CMS_CalculateCMS_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/cms.proto",
}
