syntax = "proto3";

package proto;
option go_package = "./proto";

message ServiceInfo {
  string service_id = 1;
  string env = 2;
  int64 start_time = 3;
  int64 end_time = 4;
}

message TorqueFeatureRequest {
  string requestId = 1;
  string deviceId = 2;
  string deviceType = 3;
  repeated ServiceInfo serviceLst = 4;
}

message TorqueFeatureResponse {
  int32 err_code = 1;
  string message = 2;
}

service Torque {
  rpc CalculateTorqueFeature (TorqueFeatureRequest) returns (TorqueFeatureResponse) {}
}