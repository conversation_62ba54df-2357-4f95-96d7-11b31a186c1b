// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.22.3
// source: proto/cms.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BatteryInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SlotId             int32   `protobuf:"varint,1,opt,name=slot_id,json=slotId,proto3" json:"slot_id,omitempty"`
	BatteryId          string  `protobuf:"bytes,2,opt,name=battery_id,json=batteryId,proto3" json:"battery_id,omitempty"`
	BatteryType        int32   `protobuf:"varint,3,opt,name=battery_type,json=batteryType,proto3" json:"battery_type,omitempty"`
	BatteryCapacity    int32   `protobuf:"varint,4,opt,name=battery_capacity,json=batteryCapacity,proto3" json:"battery_capacity,omitempty"`
	ChargingStatus     int32   `protobuf:"varint,5,opt,name=charging_status,json=chargingStatus,proto3" json:"charging_status,omitempty"`
	BatteryUserSoc     float64 `protobuf:"fixed64,6,opt,name=battery_user_soc,json=batteryUserSoc,proto3" json:"battery_user_soc,omitempty"`
	BatteryRealSoc     float64 `protobuf:"fixed64,7,opt,name=battery_real_soc,json=batteryRealSoc,proto3" json:"battery_real_soc,omitempty"`
	ChargingCurrent    float64 `protobuf:"fixed64,8,opt,name=charging_current,json=chargingCurrent,proto3" json:"charging_current,omitempty"`
	InitialBatteryTemp float64 `protobuf:"fixed64,9,opt,name=initial_battery_temp,json=initialBatteryTemp,proto3" json:"initial_battery_temp,omitempty"`
}

func (x *BatteryInfo) Reset() {
	*x = BatteryInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_cms_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatteryInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatteryInfo) ProtoMessage() {}

func (x *BatteryInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_cms_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatteryInfo.ProtoReflect.Descriptor instead.
func (*BatteryInfo) Descriptor() ([]byte, []int) {
	return file_proto_cms_proto_rawDescGZIP(), []int{0}
}

func (x *BatteryInfo) GetSlotId() int32 {
	if x != nil {
		return x.SlotId
	}
	return 0
}

func (x *BatteryInfo) GetBatteryId() string {
	if x != nil {
		return x.BatteryId
	}
	return ""
}

func (x *BatteryInfo) GetBatteryType() int32 {
	if x != nil {
		return x.BatteryType
	}
	return 0
}

func (x *BatteryInfo) GetBatteryCapacity() int32 {
	if x != nil {
		return x.BatteryCapacity
	}
	return 0
}

func (x *BatteryInfo) GetChargingStatus() int32 {
	if x != nil {
		return x.ChargingStatus
	}
	return 0
}

func (x *BatteryInfo) GetBatteryUserSoc() float64 {
	if x != nil {
		return x.BatteryUserSoc
	}
	return 0
}

func (x *BatteryInfo) GetBatteryRealSoc() float64 {
	if x != nil {
		return x.BatteryRealSoc
	}
	return 0
}

func (x *BatteryInfo) GetChargingCurrent() float64 {
	if x != nil {
		return x.ChargingCurrent
	}
	return 0
}

func (x *BatteryInfo) GetInitialBatteryTemp() float64 {
	if x != nil {
		return x.InitialBatteryTemp
	}
	return 0
}

type CMSRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId        string         `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	ModelTriggerTime int64          `protobuf:"varint,2,opt,name=model_trigger_time,json=modelTriggerTime,proto3" json:"model_trigger_time,omitempty"`
	DeviceId         string         `protobuf:"bytes,3,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	ModuleType       string         `protobuf:"bytes,4,opt,name=module_type,json=moduleType,proto3" json:"module_type,omitempty"`
	BatteryInfo      []*BatteryInfo `protobuf:"bytes,5,rep,name=battery_info,json=batteryInfo,proto3" json:"battery_info,omitempty"`
}

func (x *CMSRequest) Reset() {
	*x = CMSRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_cms_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CMSRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CMSRequest) ProtoMessage() {}

func (x *CMSRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_cms_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CMSRequest.ProtoReflect.Descriptor instead.
func (*CMSRequest) Descriptor() ([]byte, []int) {
	return file_proto_cms_proto_rawDescGZIP(), []int{1}
}

func (x *CMSRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *CMSRequest) GetModelTriggerTime() int64 {
	if x != nil {
		return x.ModelTriggerTime
	}
	return 0
}

func (x *CMSRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *CMSRequest) GetModuleType() string {
	if x != nil {
		return x.ModuleType
	}
	return ""
}

func (x *CMSRequest) GetBatteryInfo() []*BatteryInfo {
	if x != nil {
		return x.BatteryInfo
	}
	return nil
}

type CMSResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PowerDistributionCapacity      int32   `protobuf:"varint,1,opt,name=power_distribution_capacity,json=powerDistributionCapacity,proto3" json:"power_distribution_capacity,omitempty"`
	BranchCircuitCurrentLimit      int32   `protobuf:"varint,2,opt,name=branch_circuit_current_limit,json=branchCircuitCurrentLimit,proto3" json:"branch_circuit_current_limit,omitempty"`
	Circuit_01DistributionCapacity int32   `protobuf:"varint,3,opt,name=circuit_01_distribution_capacity,json=circuit01DistributionCapacity,proto3" json:"circuit_01_distribution_capacity,omitempty"`
	Circuit_02DistributionCapacity int32   `protobuf:"varint,4,opt,name=circuit_02_distribution_capacity,json=circuit02DistributionCapacity,proto3" json:"circuit_02_distribution_capacity,omitempty"`
	MaxAllowedModulePower_1        int32   `protobuf:"varint,5,opt,name=max_allowed_module_power_1,json=maxAllowedModulePower1,proto3" json:"max_allowed_module_power_1,omitempty"`
	MaxAllowedModulePower_2        int32   `protobuf:"varint,6,opt,name=max_allowed_module_power_2,json=maxAllowedModulePower2,proto3" json:"max_allowed_module_power_2,omitempty"`
	OptimizationLabel              int32   `protobuf:"varint,7,opt,name=optimization_label,json=optimizationLabel,proto3" json:"optimization_label,omitempty"`
	BeforeSwitchCurrent            float64 `protobuf:"fixed64,8,opt,name=before_switch_current,json=beforeSwitchCurrent,proto3" json:"before_switch_current,omitempty"`
	SwitchSoc                      float64 `protobuf:"fixed64,9,opt,name=switch_soc,json=switchSoc,proto3" json:"switch_soc,omitempty"`
	AfterSwitchCurrent             float64 `protobuf:"fixed64,10,opt,name=after_switch_current,json=afterSwitchCurrent,proto3" json:"after_switch_current,omitempty"`
	ChargingMode                   string  `protobuf:"bytes,11,opt,name=charging_mode,json=chargingMode,proto3" json:"charging_mode,omitempty"`
	SwitchMoment                   float64 `protobuf:"fixed64,12,opt,name=switch_moment,json=switchMoment,proto3" json:"switch_moment,omitempty"`
	CurrentCurrent                 float64 `protobuf:"fixed64,13,opt,name=current_current,json=currentCurrent,proto3" json:"current_current,omitempty"`
	NeedExecuted                   bool    `protobuf:"varint,14,opt,name=need_executed,json=needExecuted,proto3" json:"need_executed,omitempty"`
}

func (x *CMSResult) Reset() {
	*x = CMSResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_cms_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CMSResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CMSResult) ProtoMessage() {}

func (x *CMSResult) ProtoReflect() protoreflect.Message {
	mi := &file_proto_cms_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CMSResult.ProtoReflect.Descriptor instead.
func (*CMSResult) Descriptor() ([]byte, []int) {
	return file_proto_cms_proto_rawDescGZIP(), []int{2}
}

func (x *CMSResult) GetPowerDistributionCapacity() int32 {
	if x != nil {
		return x.PowerDistributionCapacity
	}
	return 0
}

func (x *CMSResult) GetBranchCircuitCurrentLimit() int32 {
	if x != nil {
		return x.BranchCircuitCurrentLimit
	}
	return 0
}

func (x *CMSResult) GetCircuit_01DistributionCapacity() int32 {
	if x != nil {
		return x.Circuit_01DistributionCapacity
	}
	return 0
}

func (x *CMSResult) GetCircuit_02DistributionCapacity() int32 {
	if x != nil {
		return x.Circuit_02DistributionCapacity
	}
	return 0
}

func (x *CMSResult) GetMaxAllowedModulePower_1() int32 {
	if x != nil {
		return x.MaxAllowedModulePower_1
	}
	return 0
}

func (x *CMSResult) GetMaxAllowedModulePower_2() int32 {
	if x != nil {
		return x.MaxAllowedModulePower_2
	}
	return 0
}

func (x *CMSResult) GetOptimizationLabel() int32 {
	if x != nil {
		return x.OptimizationLabel
	}
	return 0
}

func (x *CMSResult) GetBeforeSwitchCurrent() float64 {
	if x != nil {
		return x.BeforeSwitchCurrent
	}
	return 0
}

func (x *CMSResult) GetSwitchSoc() float64 {
	if x != nil {
		return x.SwitchSoc
	}
	return 0
}

func (x *CMSResult) GetAfterSwitchCurrent() float64 {
	if x != nil {
		return x.AfterSwitchCurrent
	}
	return 0
}

func (x *CMSResult) GetChargingMode() string {
	if x != nil {
		return x.ChargingMode
	}
	return ""
}

func (x *CMSResult) GetSwitchMoment() float64 {
	if x != nil {
		return x.SwitchMoment
	}
	return 0
}

func (x *CMSResult) GetCurrentCurrent() float64 {
	if x != nil {
		return x.CurrentCurrent
	}
	return 0
}

func (x *CMSResult) GetNeedExecuted() bool {
	if x != nil {
		return x.NeedExecuted
	}
	return false
}

type BatteryDemand struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hour       int32 `protobuf:"varint,1,opt,name=hour,proto3" json:"hour,omitempty"`
	Battery70  int32 `protobuf:"varint,2,opt,name=battery70,proto3" json:"battery70,omitempty"`
	Battery100 int32 `protobuf:"varint,3,opt,name=battery100,proto3" json:"battery100,omitempty"`
}

func (x *BatteryDemand) Reset() {
	*x = BatteryDemand{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_cms_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatteryDemand) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatteryDemand) ProtoMessage() {}

func (x *BatteryDemand) ProtoReflect() protoreflect.Message {
	mi := &file_proto_cms_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatteryDemand.ProtoReflect.Descriptor instead.
func (*BatteryDemand) Descriptor() ([]byte, []int) {
	return file_proto_cms_proto_rawDescGZIP(), []int{3}
}

func (x *BatteryDemand) GetHour() int32 {
	if x != nil {
		return x.Hour
	}
	return 0
}

func (x *BatteryDemand) GetBattery70() int32 {
	if x != nil {
		return x.Battery70
	}
	return 0
}

func (x *BatteryDemand) GetBattery100() int32 {
	if x != nil {
		return x.Battery100
	}
	return 0
}

type CMSResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrCode       int32                `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	Message       string               `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Version       string               `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	BatteryInfo   map[int32]*CMSResult `protobuf:"bytes,4,rep,name=battery_info,json=batteryInfo,proto3" json:"battery_info,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	BatteryDemand []*BatteryDemand     `protobuf:"bytes,5,rep,name=battery_demand,json=batteryDemand,proto3" json:"battery_demand,omitempty"`
}

func (x *CMSResponse) Reset() {
	*x = CMSResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_cms_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CMSResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CMSResponse) ProtoMessage() {}

func (x *CMSResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_cms_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CMSResponse.ProtoReflect.Descriptor instead.
func (*CMSResponse) Descriptor() ([]byte, []int) {
	return file_proto_cms_proto_rawDescGZIP(), []int{4}
}

func (x *CMSResponse) GetErrCode() int32 {
	if x != nil {
		return x.ErrCode
	}
	return 0
}

func (x *CMSResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CMSResponse) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *CMSResponse) GetBatteryInfo() map[int32]*CMSResult {
	if x != nil {
		return x.BatteryInfo
	}
	return nil
}

func (x *CMSResponse) GetBatteryDemand() []*BatteryDemand {
	if x != nil {
		return x.BatteryDemand
	}
	return nil
}

var File_proto_cms_proto protoreflect.FileDescriptor

var file_proto_cms_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xed, 0x02, 0x0a, 0x0b, 0x42, 0x61, 0x74,
	0x74, 0x65, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x6c, 0x6f, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x6c, 0x6f, 0x74, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64,
	0x12, 0x21, 0x0a, 0x0c, 0x62, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x62, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x62, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x5f, 0x63,
	0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x62,
	0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x27,
	0x0a, 0x0f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x63, 0x68, 0x61, 0x72, 0x67, 0x69, 0x6e,
	0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x61, 0x74, 0x74, 0x65,
	0x72, 0x79, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x6f, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x0e, 0x62, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x55, 0x73, 0x65, 0x72, 0x53, 0x6f,
	0x63, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x5f, 0x72, 0x65, 0x61,
	0x6c, 0x5f, 0x73, 0x6f, 0x63, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x62, 0x61, 0x74,
	0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x61, 0x6c, 0x53, 0x6f, 0x63, 0x12, 0x29, 0x0a, 0x10, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x69, 0x6e, 0x67, 0x43,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61,
	0x6c, 0x5f, 0x62, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x12, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x42, 0x61, 0x74,
	0x74, 0x65, 0x72, 0x79, 0x54, 0x65, 0x6d, 0x70, 0x22, 0xce, 0x01, 0x0a, 0x0a, 0x43, 0x4d, 0x53,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f,
	0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x10, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x35, 0x0a, 0x0c, 0x62, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x42, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x62, 0x61,
	0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xe2, 0x05, 0x0a, 0x09, 0x43, 0x4d,
	0x53, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3e, 0x0a, 0x1b, 0x70, 0x6f, 0x77, 0x65, 0x72,
	0x5f, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61,
	0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x19, 0x70, 0x6f,
	0x77, 0x65, 0x72, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x3f, 0x0a, 0x1c, 0x62, 0x72, 0x61, 0x6e, 0x63,
	0x68, 0x5f, 0x63, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x19, 0x62,
	0x72, 0x61, 0x6e, 0x63, 0x68, 0x43, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x43, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x47, 0x0a, 0x20, 0x63, 0x69, 0x72, 0x63,
	0x75, 0x69, 0x74, 0x5f, 0x30, 0x31, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x1d, 0x63, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x30, 0x31, 0x44, 0x69, 0x73,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74,
	0x79, 0x12, 0x47, 0x0a, 0x20, 0x63, 0x69, 0x72, 0x63, 0x75, 0x69, 0x74, 0x5f, 0x30, 0x32, 0x5f,
	0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x70,
	0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x1d, 0x63, 0x69, 0x72,
	0x63, 0x75, 0x69, 0x74, 0x30, 0x32, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x3a, 0x0a, 0x1a, 0x6d, 0x61,
	0x78, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x31, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x16,
	0x6d, 0x61, 0x78, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x50, 0x6f, 0x77, 0x65, 0x72, 0x31, 0x12, 0x3a, 0x0a, 0x1a, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x6c,
	0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x70, 0x6f, 0x77,
	0x65, 0x72, 0x5f, 0x32, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x16, 0x6d, 0x61, 0x78, 0x41,
	0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x6f, 0x77, 0x65,
	0x72, 0x32, 0x12, 0x2d, 0x0a, 0x12, 0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11,
	0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x12, 0x32, 0x0a, 0x15, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x73, 0x77, 0x69, 0x74,
	0x63, 0x68, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x13, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x43, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x5f,
	0x73, 0x6f, 0x63, 0x18, 0x09, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x73, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x53, 0x6f, 0x63, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x73, 0x77,
	0x69, 0x74, 0x63, 0x68, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x12, 0x61, 0x66, 0x74, 0x65, 0x72, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x43,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x68, 0x61, 0x72, 0x67, 0x69,
	0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0c, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x4d, 0x6f, 0x6d, 0x65, 0x6e, 0x74,
	0x12, 0x27, 0x0a, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x6e, 0x65, 0x65,
	0x64, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0c, 0x6e, 0x65, 0x65, 0x64, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x22, 0x61,
	0x0a, 0x0d, 0x42, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x44, 0x65, 0x6d, 0x61, 0x6e, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x68, 0x6f, 0x75, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x68,
	0x6f, 0x75, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x37, 0x30,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x62, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x37,
	0x30, 0x12, 0x1e, 0x0a, 0x0a, 0x62, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x31, 0x30, 0x30, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x62, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x31, 0x30,
	0x30, 0x22, 0xb3, 0x02, 0x0a, 0x0b, 0x43, 0x4d, 0x53, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x72, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x46, 0x0a, 0x0c, 0x62, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43,
	0x4d, 0x53, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x65,
	0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x62, 0x61, 0x74,
	0x74, 0x65, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3b, 0x0a, 0x0e, 0x62, 0x61, 0x74, 0x74,
	0x65, 0x72, 0x79, 0x5f, 0x64, 0x65, 0x6d, 0x61, 0x6e, 0x64, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79,
	0x44, 0x65, 0x6d, 0x61, 0x6e, 0x64, 0x52, 0x0d, 0x62, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x44,
	0x65, 0x6d, 0x61, 0x6e, 0x64, 0x1a, 0x50, 0x0a, 0x10, 0x42, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79,
	0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x26, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x43, 0x4d, 0x53, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x32, 0x3e, 0x0a, 0x03, 0x43, 0x4d, 0x53, 0x12, 0x37,
	0x0a, 0x0c, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x4d, 0x53, 0x12, 0x11,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x4d, 0x53, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x4d, 0x53, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x09, 0x5a, 0x07, 0x2e, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_cms_proto_rawDescOnce sync.Once
	file_proto_cms_proto_rawDescData = file_proto_cms_proto_rawDesc
)

func file_proto_cms_proto_rawDescGZIP() []byte {
	file_proto_cms_proto_rawDescOnce.Do(func() {
		file_proto_cms_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_cms_proto_rawDescData)
	})
	return file_proto_cms_proto_rawDescData
}

var file_proto_cms_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_proto_cms_proto_goTypes = []interface{}{
	(*BatteryInfo)(nil),   // 0: proto.BatteryInfo
	(*CMSRequest)(nil),    // 1: proto.CMSRequest
	(*CMSResult)(nil),     // 2: proto.CMSResult
	(*BatteryDemand)(nil), // 3: proto.BatteryDemand
	(*CMSResponse)(nil),   // 4: proto.CMSResponse
	nil,                   // 5: proto.CMSResponse.BatteryInfoEntry
}
var file_proto_cms_proto_depIdxs = []int32{
	0, // 0: proto.CMSRequest.battery_info:type_name -> proto.BatteryInfo
	5, // 1: proto.CMSResponse.battery_info:type_name -> proto.CMSResponse.BatteryInfoEntry
	3, // 2: proto.CMSResponse.battery_demand:type_name -> proto.BatteryDemand
	2, // 3: proto.CMSResponse.BatteryInfoEntry.value:type_name -> proto.CMSResult
	1, // 4: proto.CMS.CalculateCMS:input_type -> proto.CMSRequest
	4, // 5: proto.CMS.CalculateCMS:output_type -> proto.CMSResponse
	5, // [5:6] is the sub-list for method output_type
	4, // [4:5] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_proto_cms_proto_init() }
func file_proto_cms_proto_init() {
	if File_proto_cms_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_cms_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatteryInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_cms_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CMSRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_cms_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CMSResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_cms_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatteryDemand); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_cms_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CMSResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_cms_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_cms_proto_goTypes,
		DependencyIndexes: file_proto_cms_proto_depIdxs,
		MessageInfos:      file_proto_cms_proto_msgTypes,
	}.Build()
	File_proto_cms_proto = out.File
	file_proto_cms_proto_rawDesc = nil
	file_proto_cms_proto_goTypes = nil
	file_proto_cms_proto_depIdxs = nil
}
