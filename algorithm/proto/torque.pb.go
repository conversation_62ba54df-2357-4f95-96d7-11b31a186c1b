// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.22.3
// source: proto/torque.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ServiceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceId string `protobuf:"bytes,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	Env       string `protobuf:"bytes,2,opt,name=env,proto3" json:"env,omitempty"`
	StartTime int64  `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime   int64  `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
}

func (x *ServiceInfo) Reset() {
	*x = ServiceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_torque_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceInfo) ProtoMessage() {}

func (x *ServiceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_torque_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceInfo.ProtoReflect.Descriptor instead.
func (*ServiceInfo) Descriptor() ([]byte, []int) {
	return file_proto_torque_proto_rawDescGZIP(), []int{0}
}

func (x *ServiceInfo) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *ServiceInfo) GetEnv() string {
	if x != nil {
		return x.Env
	}
	return ""
}

func (x *ServiceInfo) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ServiceInfo) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

type TorqueFeatureRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId  string         `protobuf:"bytes,1,opt,name=requestId,proto3" json:"requestId,omitempty"`
	DeviceId   string         `protobuf:"bytes,2,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	DeviceType string         `protobuf:"bytes,3,opt,name=deviceType,proto3" json:"deviceType,omitempty"`
	ServiceLst []*ServiceInfo `protobuf:"bytes,4,rep,name=serviceLst,proto3" json:"serviceLst,omitempty"`
}

func (x *TorqueFeatureRequest) Reset() {
	*x = TorqueFeatureRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_torque_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TorqueFeatureRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TorqueFeatureRequest) ProtoMessage() {}

func (x *TorqueFeatureRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_torque_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TorqueFeatureRequest.ProtoReflect.Descriptor instead.
func (*TorqueFeatureRequest) Descriptor() ([]byte, []int) {
	return file_proto_torque_proto_rawDescGZIP(), []int{1}
}

func (x *TorqueFeatureRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *TorqueFeatureRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *TorqueFeatureRequest) GetDeviceType() string {
	if x != nil {
		return x.DeviceType
	}
	return ""
}

func (x *TorqueFeatureRequest) GetServiceLst() []*ServiceInfo {
	if x != nil {
		return x.ServiceLst
	}
	return nil
}

type TorqueFeatureResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrCode int32  `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *TorqueFeatureResponse) Reset() {
	*x = TorqueFeatureResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_torque_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TorqueFeatureResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TorqueFeatureResponse) ProtoMessage() {}

func (x *TorqueFeatureResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_torque_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TorqueFeatureResponse.ProtoReflect.Descriptor instead.
func (*TorqueFeatureResponse) Descriptor() ([]byte, []int) {
	return file_proto_torque_proto_rawDescGZIP(), []int{2}
}

func (x *TorqueFeatureResponse) GetErrCode() int32 {
	if x != nil {
		return x.ErrCode
	}
	return 0
}

func (x *TorqueFeatureResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_proto_torque_proto protoreflect.FileDescriptor

var file_proto_torque_proto_rawDesc = []byte{
	0x0a, 0x12, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x6f, 0x72, 0x71, 0x75, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x78, 0x0a, 0x0b, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x76,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x6e, 0x76, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xa4, 0x01, 0x0a, 0x14, 0x54, 0x6f, 0x72, 0x71, 0x75, 0x65,
	0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1c,
	0x0a, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x32, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x4c, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x73, 0x74, 0x22, 0x4c, 0x0a, 0x15,
	0x54, 0x6f, 0x72, 0x71, 0x75, 0x65, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x72, 0x72, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x32, 0x5f, 0x0a, 0x06, 0x54, 0x6f,
	0x72, 0x71, 0x75, 0x65, 0x12, 0x55, 0x0a, 0x16, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74,
	0x65, 0x54, 0x6f, 0x72, 0x71, 0x75, 0x65, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x1b,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x6f, 0x72, 0x71, 0x75, 0x65, 0x46, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x6f, 0x72, 0x71, 0x75, 0x65, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x09, 0x5a, 0x07, 0x2e,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_torque_proto_rawDescOnce sync.Once
	file_proto_torque_proto_rawDescData = file_proto_torque_proto_rawDesc
)

func file_proto_torque_proto_rawDescGZIP() []byte {
	file_proto_torque_proto_rawDescOnce.Do(func() {
		file_proto_torque_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_torque_proto_rawDescData)
	})
	return file_proto_torque_proto_rawDescData
}

var file_proto_torque_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_proto_torque_proto_goTypes = []interface{}{
	(*ServiceInfo)(nil),           // 0: proto.ServiceInfo
	(*TorqueFeatureRequest)(nil),  // 1: proto.TorqueFeatureRequest
	(*TorqueFeatureResponse)(nil), // 2: proto.TorqueFeatureResponse
}
var file_proto_torque_proto_depIdxs = []int32{
	0, // 0: proto.TorqueFeatureRequest.serviceLst:type_name -> proto.ServiceInfo
	1, // 1: proto.Torque.CalculateTorqueFeature:input_type -> proto.TorqueFeatureRequest
	2, // 2: proto.Torque.CalculateTorqueFeature:output_type -> proto.TorqueFeatureResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_proto_torque_proto_init() }
func file_proto_torque_proto_init() {
	if File_proto_torque_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_torque_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_torque_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TorqueFeatureRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_torque_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TorqueFeatureResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_torque_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_torque_proto_goTypes,
		DependencyIndexes: file_proto_torque_proto_depIdxs,
		MessageInfos:      file_proto_torque_proto_msgTypes,
	}.Build()
	File_proto_torque_proto = out.File
	file_proto_torque_proto_rawDesc = nil
	file_proto_torque_proto_goTypes = nil
	file_proto_torque_proto_depIdxs = nil
}
