// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.22.3
// source: proto/algorithm_service.proto

package proto

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	DetectImage_DetectAbnormalImage_FullMethodName = "/proto.DetectImage/DetectAbnormalImage"
)

// DetectImageClient is the client API for DetectImage service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DetectImageClient interface {
	DetectAbnormalImage(ctx context.Context, in *DetectRequest, opts ...grpc.CallOption) (*DetectResponse, error)
}

type detectImageClient struct {
	cc grpc.ClientConnInterface
}

func NewDetectImageClient(cc grpc.ClientConnInterface) DetectImageClient {
	return &detectImageClient{cc}
}

func (c *detectImageClient) DetectAbnormalImage(ctx context.Context, in *DetectRequest, opts ...grpc.CallOption) (*DetectResponse, error) {
	out := new(DetectResponse)
	err := c.cc.Invoke(ctx, DetectImage_DetectAbnormalImage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DetectImageServer is the server API for DetectImage service.
// All implementations must embed UnimplementedDetectImageServer
// for forward compatibility
type DetectImageServer interface {
	DetectAbnormalImage(context.Context, *DetectRequest) (*DetectResponse, error)
	mustEmbedUnimplementedDetectImageServer()
}

// UnimplementedDetectImageServer must be embedded to have forward compatible implementations.
type UnimplementedDetectImageServer struct {
}

func (UnimplementedDetectImageServer) DetectAbnormalImage(context.Context, *DetectRequest) (*DetectResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DetectAbnormalImage not implemented")
}
func (UnimplementedDetectImageServer) mustEmbedUnimplementedDetectImageServer() {}

// UnsafeDetectImageServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DetectImageServer will
// result in compilation errors.
type UnsafeDetectImageServer interface {
	mustEmbedUnimplementedDetectImageServer()
}

func RegisterDetectImageServer(s grpc.ServiceRegistrar, srv DetectImageServer) {
	s.RegisterService(&DetectImage_ServiceDesc, srv)
}

func _DetectImage_DetectAbnormalImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DetectRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DetectImageServer).DetectAbnormalImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DetectImage_DetectAbnormalImage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DetectImageServer).DetectAbnormalImage(ctx, req.(*DetectRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DetectImage_ServiceDesc is the grpc.ServiceDesc for DetectImage service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DetectImage_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "proto.DetectImage",
	HandlerType: (*DetectImageServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DetectAbnormalImage",
			Handler:    _DetectImage_DetectAbnormalImage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/algorithm_service.proto",
}
