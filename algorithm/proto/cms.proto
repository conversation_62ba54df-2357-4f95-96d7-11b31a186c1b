syntax = "proto3";

package proto;
option go_package = "./proto";

message BatteryInfo {
  int32 slot_id = 1;
  string battery_id = 2;
  int32 battery_type = 3;
  int32 battery_capacity = 4;
  int32 charging_status = 5;
  double battery_user_soc = 6;
  double battery_real_soc = 7;
  double charging_current = 8;
  double initial_battery_temp = 9;
}

message CMSRequest {
  string request_id = 1;
  int64 model_trigger_time = 2;
  string device_id = 3;
  string module_type = 4;
  repeated BatteryInfo battery_info = 5;
}

message CMSResult {
  int32 power_distribution_capacity = 1;
  int32 branch_circuit_current_limit = 2;
  int32 circuit_01_distribution_capacity = 3;
  int32 circuit_02_distribution_capacity = 4;
  int32 max_allowed_module_power_1 = 5;
  int32 max_allowed_module_power_2 = 6;
  int32 optimization_label = 7;
  double before_switch_current = 8;
  double switch_soc = 9;
  double after_switch_current = 10;
  string charging_mode = 11;
  double switch_moment = 12;
  double current_current = 13;
  bool need_executed = 14;
}

message BatteryDemand {
  int32 hour = 1;
  int32 battery70 = 2;
  int32 battery100 = 3;
}

message CMSResponse {
  int32 err_code = 1;
  string message = 2;
  string version = 3;
  map<int32, CMSResult> battery_info = 4;
  repeated BatteryDemand battery_demand = 5;
}

service CMS {
  rpc CalculateCMS (CMSRequest) returns (CMSResponse) {}
}