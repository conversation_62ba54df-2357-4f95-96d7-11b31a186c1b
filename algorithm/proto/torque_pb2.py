# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: torque.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0ctorque.proto\x12\x05proto\"T\n\x0bServiceInfo\x12\x12\n\nservice_id\x18\x01 \x01(\t\x12\x0b\n\x03\x65nv\x18\x02 \x01(\t\x12\x12\n\nstart_time\x18\x03 \x01(\x03\x12\x10\n\x08\x65nd_time\x18\x04 \x01(\x03\"w\n\x14TorqueFeatureRequest\x12\x11\n\trequestId\x18\x01 \x01(\t\x12\x10\n\x08\x64\x65viceId\x18\x02 \x01(\t\x12\x12\n\ndeviceType\x18\x03 \x01(\t\x12&\n\nserviceLst\x18\x04 \x03(\x0b\x32\x12.proto.ServiceInfo\":\n\x15TorqueFeatureResponse\x12\x10\n\x08\x65rr_code\x18\x01 \x01(\x05\x12\x0f\n\x07message\x18\x02 \x01(\t2_\n\x06Torque\x12U\n\x16\x43\x61lculateTorqueFeature\x12\x1b.proto.TorqueFeatureRequest\x1a\x1c.proto.TorqueFeatureResponse\"\x00\x62\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'torque_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _SERVICEINFO._serialized_start=23
  _SERVICEINFO._serialized_end=107
  _TORQUEFEATUREREQUEST._serialized_start=109
  _TORQUEFEATUREREQUEST._serialized_end=228
  _TORQUEFEATURERESPONSE._serialized_start=230
  _TORQUEFEATURERESPONSE._serialized_end=288
  _TORQUE._serialized_start=290
  _TORQUE._serialized_end=385
# @@protoc_insertion_point(module_scope)
