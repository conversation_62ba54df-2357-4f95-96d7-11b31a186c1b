# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: proto/cms.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0fproto/cms.proto\x12\x05proto\"\xe7\x01\n\x0b\x42\x61tteryInfo\x12\x0f\n\x07slot_id\x18\x01 \x01(\x05\x12\x12\n\nbattery_id\x18\x02 \x01(\t\x12\x14\n\x0c\x62\x61ttery_type\x18\x03 \x01(\x05\x12\x18\n\x10\x62\x61ttery_capacity\x18\x04 \x01(\x05\x12\x17\n\x0f\x63harging_status\x18\x05 \x01(\x05\x12\x18\n\x10\x62\x61ttery_user_soc\x18\x06 \x01(\x01\x12\x18\n\x10\x62\x61ttery_real_soc\x18\x07 \x01(\x01\x12\x18\n\x10\x63harging_current\x18\x08 \x01(\x01\x12\x1c\n\x14initial_battery_temp\x18\t \x01(\x01\"\x8e\x01\n\nCMSRequest\x12\x12\n\nrequest_id\x18\x01 \x01(\t\x12\x1a\n\x12model_trigger_time\x18\x02 \x01(\x03\x12\x11\n\tdevice_id\x18\x03 \x01(\t\x12\x13\n\x0bmodule_type\x18\x04 \x01(\t\x12(\n\x0c\x62\x61ttery_info\x18\x05 \x03(\x0b\x32\x12.proto.BatteryInfo\"\xbd\x03\n\tCMSResult\x12#\n\x1bpower_distribution_capacity\x18\x01 \x01(\x05\x12$\n\x1c\x62ranch_circuit_current_limit\x18\x02 \x01(\x05\x12(\n circuit_01_distribution_capacity\x18\x03 \x01(\x05\x12(\n circuit_02_distribution_capacity\x18\x04 \x01(\x05\x12\"\n\x1amax_allowed_module_power_1\x18\x05 \x01(\x05\x12\"\n\x1amax_allowed_module_power_2\x18\x06 \x01(\x05\x12\x1a\n\x12optimization_label\x18\x07 \x01(\x05\x12\x1d\n\x15\x62\x65\x66ore_switch_current\x18\x08 \x01(\x01\x12\x12\n\nswitch_soc\x18\t \x01(\x01\x12\x1c\n\x14\x61\x66ter_switch_current\x18\n \x01(\x01\x12\x15\n\rcharging_mode\x18\x0b \x01(\t\x12\x15\n\rswitch_moment\x18\x0c \x01(\x01\x12\x17\n\x0f\x63urrent_current\x18\r \x01(\x01\x12\x15\n\rneed_executed\x18\x0e \x01(\x08\"D\n\rBatteryDemand\x12\x0c\n\x04hour\x18\x01 \x01(\x05\x12\x11\n\tbattery70\x18\x02 \x01(\x05\x12\x12\n\nbattery100\x18\x03 \x01(\x05\"\xf0\x01\n\x0b\x43MSResponse\x12\x10\n\x08\x65rr_code\x18\x01 \x01(\x05\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x0f\n\x07version\x18\x03 \x01(\t\x12\x39\n\x0c\x62\x61ttery_info\x18\x04 \x03(\x0b\x32#.proto.CMSResponse.BatteryInfoEntry\x12,\n\x0e\x62\x61ttery_demand\x18\x05 \x03(\x0b\x32\x14.proto.BatteryDemand\x1a\x44\n\x10\x42\x61tteryInfoEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\x1f\n\x05value\x18\x02 \x01(\x0b\x32\x10.proto.CMSResult:\x02\x38\x01\x32>\n\x03\x43MS\x12\x37\n\x0c\x43\x61lculateCMS\x12\x11.proto.CMSRequest\x1a\x12.proto.CMSResponse\"\x00\x42\tZ\x07./protob\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'proto.cms_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'Z\007./proto'
  _CMSRESPONSE_BATTERYINFOENTRY._options = None
  _CMSRESPONSE_BATTERYINFOENTRY._serialized_options = b'8\001'
  _BATTERYINFO._serialized_start=27
  _BATTERYINFO._serialized_end=258
  _CMSREQUEST._serialized_start=261
  _CMSREQUEST._serialized_end=403
  _CMSRESULT._serialized_start=406
  _CMSRESULT._serialized_end=851
  _BATTERYDEMAND._serialized_start=853
  _BATTERYDEMAND._serialized_end=921
  _CMSRESPONSE._serialized_start=924
  _CMSRESPONSE._serialized_end=1164
  _CMSRESPONSE_BATTERYINFOENTRY._serialized_start=1096
  _CMSRESPONSE_BATTERYINFOENTRY._serialized_end=1164
  _CMS._serialized_start=1166
  _CMS._serialized_end=1228
# @@protoc_insertion_point(module_scope)
