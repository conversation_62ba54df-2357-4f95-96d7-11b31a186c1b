package cache

import (
	"encoding/json"
	udao "git.nevint.com/golang-libs/common-utils/dao"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
	"github.com/patrickmn/go-cache"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
	"strings"
	"sync"
)

type EventCache struct {
	Cache  map[string]*cache.Cache
	Mu     sync.RWMutex
	Logger *zap.SugaredLogger
}

var SwapEventCache *EventCache

func (e *EventCache) RefreshSwapEventCache(mongoClient *mongo.Client) {
	byteData, err := udao.NewMongoEntry(mongoClient, bson.D{{"event_type", "swap_event"}}).ListAll(umw.Device2Cloud, "event")
	if err != nil {
		e.Logger.Errorf("refresh swap event, get mongo data, err: %v", err)
		return
	}
	var mongoSwapEventTag []MongoMechanicalSwapEventTag
	if err = json.Unmarshal(byteData, &mongoSwapEventTag); err != nil {
		e.Logger.Errorf("refresh swap event, fail to unmarshal: %v, data: %s", err, string(byteData))
		return
	}
	e.Mu.Lock()
	defer e.Mu.Unlock()
	for _, record := range mongoSwapEventTag {
		if record.EventId == "" || record.Project == "" {
			continue
		}
		if e.Cache[record.Project] == nil {
			e.Cache[record.Project] = cache.New(cache.NoExpiration, cache.NoExpiration)
		}
		contextMap := make(map[string]struct{})
		for _, contextId := range strings.Split(record.Context, ",") {
			contextMap[contextId] = struct{}{}
		}
		e.Cache[record.Project].Set(record.EventId, MechanicalSwapEventTag{
			Project:          record.Project,
			EventId:          record.EventId,
			EventDescription: record.EventDescription,
			ContextMap:       contextMap,
		}, cache.NoExpiration)
	}
	e.Logger.Infof("refresh swap event. refrsh %v items", len(mongoSwapEventTag))
}

func (e *EventCache) GetAllSwapEvents(project string) []MechanicalSwapEventTag {
	var res []MechanicalSwapEventTag
	e.Mu.RLock()
	defer e.Mu.RUnlock()
	if e.Cache[project] == nil {
		return res
	}
	for _, item := range e.Cache[project].Items() {
		swapEventTag := item.Object.(MechanicalSwapEventTag)
		res = append(res, swapEventTag)
	}
	return res
}

func (e *EventCache) GetSingleSwapEvent(project, eventId string) (MechanicalSwapEventTag, bool) {
	var res MechanicalSwapEventTag
	e.Mu.RLock()
	defer e.Mu.RUnlock()
	if e.Cache[project] == nil {
		return res, false
	}
	item, ok := e.Cache[project].Get(eventId)
	if !ok {
		return res, ok
	}
	res, ok = item.(MechanicalSwapEventTag)
	return res, ok
}

type MechanicalSwapEventTag struct {
	Project          string              `json:"project"`
	EventId          string              `json:"event_id"`
	EventDescription string              `json:"event_description"`
	ContextMap       map[string]struct{} `json:"context_list"`
}
type MongoMechanicalSwapEventTag struct {
	Project          string `json:"project" bson:"project"`
	EventId          string `json:"event_id" bson:"event_id"`
	EventDescription string `json:"event_description" bson:"event_description"`
	Context          string `json:"context" bson:"context"`
}
