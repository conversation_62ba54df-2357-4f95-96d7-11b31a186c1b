package cache

import (
	"encoding/json"

	"github.com/patrickmn/go-cache"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"

	udao "git.nevint.com/golang-libs/common-utils/dao"
)

type DateInfo struct {
	DayStr    string `json:"day_str"`
	IsWorkday int64  `json:"is_workday"`
	IsHoliday int64  `json:"is_holiday"`
}

type DateCache struct {
	Cache  *cache.Cache
	Logger *zap.SugaredLogger
}

var DateInfoCache *DateCache

func (d *DateCache) RefreshDateInfoCache(mongoClient *mongo.Client) error {
	byteData, err := udao.NewMongoEntry(mongoClient, bson.D{{"is_workday", bson.M{"$exists": true}}}).ListAll("common", "date_info")
	if err != nil {
		return err
	}
	var dateInfoList []DateInfo
	if err = json.Unmarshal(byteData, &dateInfoList); err != nil {
		return err
	}
	for _, record := range dateInfoList {
		if record.DayStr == "" {
			continue
		}
		d.Cache.Set(record.DayStr, record, cache.NoExpiration)
	}
	d.Logger.Infof("refresh date info. refresh %v items", len(dateInfoList))
	return nil
}

func (d *DateCache) GetDateInfo(dayStr string) (DateInfo, bool) {
	if d.Cache == nil {
		return DateInfo{}, false
	}
	if data, found := d.Cache.Get(dayStr); found {
		return data.(DateInfo), true
	}
	return DateInfo{}, false
}

// IsDateWorkday checks if the given date is a workday
func (d *DateCache) IsDateWorkday(dayStr string) bool {
	dateInfo, ok := d.GetDateInfo(dayStr)
	if !ok {
		return false
	}
	return dateInfo.IsWorkday == 1
}
