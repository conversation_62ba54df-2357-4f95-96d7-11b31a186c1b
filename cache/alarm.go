package cache

import (
	"encoding/json"
	"fmt"
	"strings"
	"sync"

	"github.com/patrickmn/go-cache"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"

	udao "git.nevint.com/golang-libs/common-utils/dao"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
)

type AlarmInfoCache struct {
	cache  map[string]*cache.Cache
	mu     sync.RWMutex
	Logger *zap.SugaredLogger
}

func InitAlarmInfoCache() {
	StuckAlarmInfoCache = &AlarmInfoCache{
		cache: make(map[string]*cache.Cache),
		mu:    sync.RWMutex{},
	}
	DeviceAlarmInfoCache = &AlarmInfoCache{
		cache: make(map[string]*cache.Cache),
		mu:    sync.RWMutex{},
	}
}

// StuckAlarmInfoCache 挂车告警列表本地缓存
var StuckAlarmInfoCache *AlarmInfoCache

func (a *AlarmInfoCache) RefreshStuckAlarmInfoCache(mongoClient *mongo.Client) {
	byteData, err := udao.NewMongoEntry(mongoClient).ListAll(umw.Device2Cloud, "stuck-alarm")
	if err != nil {
		a.Logger.Errorf("refresh stuck alarm info, get mongo data, err: %v", err)
		return
	}
	var mongoStuckAlarmInfo []umw.MongoStuckAlarmInfo
	if err = json.Unmarshal(byteData, &mongoStuckAlarmInfo); err != nil {
		a.Logger.Errorf("refresh stuck alarm info, fail to unmarshal alarm info, err: %v, data: %s", err, string(byteData))
		return
	}
	a.mu.Lock()
	defer a.mu.Unlock()
	for _, alarmInfo := range mongoStuckAlarmInfo {
		if alarmInfo.AlarmId == "" || alarmInfo.Project == "" {
			continue
		}
		if a.cache[alarmInfo.Project] == nil {
			a.cache[alarmInfo.Project] = cache.New(cache.NoExpiration, cache.NoExpiration)
		}
		a.cache[alarmInfo.Project].Set(alarmInfo.AlarmId, alarmInfo, cache.NoExpiration)
	}
	a.Logger.Infof("refresh stuck alarm info success. refrsh %v items", len(mongoStuckAlarmInfo))
}

func (a *AlarmInfoCache) GetAllStuckAlarms(project string) []*umw.MongoStuckAlarmInfo {
	var res []*umw.MongoStuckAlarmInfo
	a.mu.RLock()
	defer a.mu.RUnlock()
	if a.cache[project] == nil {
		return res
	}
	for _, item := range a.cache[project].Items() {
		mongoStuckAlarmInfo := item.Object.(umw.MongoStuckAlarmInfo)
		res = append(res, &mongoStuckAlarmInfo)
	}
	return res
}

func (a *AlarmInfoCache) GetSingleStuckAlarm(project, alarmId string) (*umw.MongoStuckAlarmInfo, bool) {
	var res umw.MongoStuckAlarmInfo
	a.mu.RLock()
	defer a.mu.RUnlock()
	if a.cache[project] == nil {
		return &res, false
	}
	item, ok := a.cache[project].Get(alarmId)
	if !ok {
		return &res, ok
	}
	res, ok = item.(umw.MongoStuckAlarmInfo)
	return &res, ok
}

// DeviceAlarmInfoCache 设备告警列表本地缓存
var DeviceAlarmInfoCache *AlarmInfoCache

func (a *AlarmInfoCache) RefreshDeviceAlarmInfoCache(mongoClient *mongo.Client) {
	syncAlarm := func(project string) {
		renameProject := project
		if project == umw.PSC4 {
			renameProject = umw.PUS4
		}
		byteData, err := udao.NewMongoEntry(mongoClient).ListAll(umw.Device2Cloud, fmt.Sprintf("alarm-%s", strings.ToLower(renameProject)))
		if err != nil {
			a.Logger.Errorf("refresh device alarm info, get mongo data, err: %v", err)
			return
		}
		var mongoDeviceAlarmInfo []umw.MongoDeviceAlarmInfo
		if err = json.Unmarshal(byteData, &mongoDeviceAlarmInfo); err != nil {
			a.Logger.Errorf("refresh device alarm info, fail to unmarshal alarm info, err: %v, data: %s", err, string(byteData))
			return
		}
		a.mu.Lock()
		defer a.mu.Unlock()
		for _, alarmInfo := range mongoDeviceAlarmInfo {
			if alarmInfo.DataId == "" {
				continue
			}
			if a.cache[project] == nil {
				a.cache[project] = cache.New(cache.NoExpiration, cache.NoExpiration)
			}
			a.cache[project].Set(alarmInfo.DataId, alarmInfo, cache.NoExpiration)
		}
		a.Logger.Infof("refresh %s device alarm info success. refrsh %v items", project, len(mongoDeviceAlarmInfo))
	}
	syncAlarm(umw.PowerSwap2)
	syncAlarm(umw.PUS3)
	syncAlarm(umw.PUS4)
	syncAlarm(umw.PSC4)
	syncAlarm(umw.FYPUS1)
	syncAlarm(umw.PowerCharger3_1)
}

func (a *AlarmInfoCache) GetAllDeviceAlarms(project string) []*umw.MongoDeviceAlarmInfo {
	var res []*umw.MongoDeviceAlarmInfo
	a.mu.RLock()
	defer a.mu.RUnlock()
	if a.cache[project] == nil {
		return res
	}
	for _, item := range a.cache[project].Items() {
		mongoDeviceAlarmInfo := item.Object.(umw.MongoDeviceAlarmInfo)
		res = append(res, &mongoDeviceAlarmInfo)
	}
	return res
}

func (a *AlarmInfoCache) GetSingleDeviceAlarm(project, alarmId string) (*umw.MongoDeviceAlarmInfo, bool) {
	var res umw.MongoDeviceAlarmInfo
	a.mu.RLock()
	defer a.mu.RUnlock()
	if a.cache[project] == nil {
		return &res, false
	}
	item, ok := a.cache[project].Get(alarmId)
	if !ok {
		return &res, ok
	}
	res, ok = item.(umw.MongoDeviceAlarmInfo)
	return &res, ok
}
