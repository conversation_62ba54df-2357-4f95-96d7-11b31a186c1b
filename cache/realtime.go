package cache

import (
	"encoding/json"
	"fmt"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"

	udao "git.nevint.com/golang-libs/common-utils/dao"
	umw "git.nevint.com/golang-libs/common-utils/model/welkin"
)

var (
	RealtimePS2Data       = make(map[string]DeviceRealtimeDataId)  // deprecated, use OSSRealtimeData
	OSSRealtimePS3Data    = make(map[string]DeviceRealtimeDataId)  // deprecated, use OSSRealtimeData
	OSSRealtimePS4Data    = make(map[string]DeviceRealtimeDataId)  // deprecated, use OSSRealtimeData
	OSSRealtimeFY1Data    = make(map[string]DeviceRealtimeDataId)  // deprecated, use OSSRealtimeData
	WelkinRealtimePS3Data = make(map[string]DeviceRealtimeDataId)  // deprecated, use WelkinRealtimeData
	WelkinRealtimePS4Data = make(map[string]DeviceRealtimeDataId)  // deprecated, use WelkinRealtimeData
	WelkinRealtimeFY1Data = make(map[string]DeviceRealtimeDataId)  // deprecated, use WelkinRealtimeData

	OSSRealtimeData    = make(map[string]map[string]DeviceRealtimeDataId)
	WelkinRealtimeData = make(map[string]map[string]DeviceRealtimeDataId)

	SensorFYPUS1VarNameMap = make(map[string]int)
	SensorPS4VarNameMap    = make(map[string]int)
	SensorPS3VarNameMap    = make(map[string]int)
	SensorPS2VarNameMap    = make(map[string]int)

	DIPS4NameMap    = make(map[string]int)
	DIFYPUS1NameMap = make(map[string]int)
)

type DeviceRealtimeDataId struct {
	DataId    string `json:"data_id" bson:"data_id"`
	VarType   string `json:"var_type" bson:"var_type"`
	Variable  string `json:"var_name" bson:"var_name"`
	VarCnName string `json:"var_cn_name" bson:"var_cn_name"`
}

type DeviceAlarmDataId struct {
	DataId     string `json:"data_id"`
	VarCnName  string `json:"var_cn_name"`
	VarEnName  string `json:"var_en_name"`
	AlarmLevel int32  `json:"alarm_level"`
}

func CacheRealtimeDataID(cli *mongo.Client) error {
	if OSSRealtimeData[umw.PowerSwap2] == nil {
		OSSRealtimeData[umw.PowerSwap2] = make(map[string]DeviceRealtimeDataId)
	}
	if OSSRealtimeData[umw.PUS3] == nil {
		OSSRealtimeData[umw.PUS3] = make(map[string]DeviceRealtimeDataId)
	}
	if OSSRealtimeData[umw.PUS4] == nil {
		OSSRealtimeData[umw.PUS4] = make(map[string]DeviceRealtimeDataId)
	}
	if OSSRealtimeData[umw.FYPUS1] == nil {
		OSSRealtimeData[umw.FYPUS1] = make(map[string]DeviceRealtimeDataId)
	}
	if WelkinRealtimeData[umw.PUS3] == nil {
		WelkinRealtimeData[umw.PUS3] = make(map[string]DeviceRealtimeDataId)
	}
	if WelkinRealtimeData[umw.PUS4] == nil {
		WelkinRealtimeData[umw.PUS4] = make(map[string]DeviceRealtimeDataId)
	}
	if WelkinRealtimeData[umw.FYPUS1] == nil {
		WelkinRealtimeData[umw.FYPUS1] = make(map[string]DeviceRealtimeDataId)
	}

	mgoEntry := udao.NewMongoEntry(cli)
	rawData, err := mgoEntry.ListAll(umw.Device2Cloud, "realtime-powerswap2", udao.MongoOptions{
		Projection: &bson.M{"data_id": 1, "var_type": 1, "_id": 0, "var_cn_name": 1},
	})
	if err != nil {
		return fmt.Errorf("get powerswap2 realtime from mongo: %v", err)
	}
	if rawData != nil {
		var results []DeviceRealtimeDataId
		if err = json.Unmarshal(rawData, &results); err != nil {
			return fmt.Errorf("unmarshal powerswap2 realtime: %v", err)
		}
		for _, item := range results {
			RealtimePS2Data[item.DataId] = item
			OSSRealtimeData[umw.PowerSwap2][item.DataId] = item
		}
	}
	rawData, err = mgoEntry.ListAll(umw.Device2Cloud, "welkin-realtime-pus3", udao.MongoOptions{
		Projection: &bson.M{"data_id": 1, "var_type": 1, "var_name": 1, "_id": 0, "var_cn_name": 1},
	})
	if err != nil {
		return fmt.Errorf("get pus3 realtime for welkin from mongo: %v", err)
	}
	if rawData != nil {
		var results []DeviceRealtimeDataId
		if err = json.Unmarshal(rawData, &results); err != nil {
			return fmt.Errorf("unmarshal pus3 realtime for welkin: %v", err)
		}
		for _, item := range results {
			WelkinRealtimePS3Data[item.DataId] = item
			WelkinRealtimeData[umw.PUS3][item.DataId] = item
		}
	}
	rawData, err = mgoEntry.ListAll(umw.Device2Cloud, "welkin-realtime-pus4", udao.MongoOptions{
		Projection: &bson.M{"data_id": 1, "var_type": 1, "var_name": 1, "_id": 0, "var_cn_name": 1},
	})
	if err != nil {
		return fmt.Errorf("get pus4 realtime for welkin from mongo: %v", err)
	}
	if rawData != nil {
		var results []DeviceRealtimeDataId
		if err = json.Unmarshal(rawData, &results); err != nil {
			return fmt.Errorf("unmarshal pus4 realtime for welkin: %v", err)
		}
		for _, item := range results {
			WelkinRealtimePS4Data[item.DataId] = item
			WelkinRealtimeData[umw.PUS4][item.DataId] = item
		}
	}
	rawData, err = mgoEntry.ListAll(umw.Device2Cloud, "welkin-realtime-fypus1", udao.MongoOptions{
		Projection: &bson.M{"data_id": 1, "var_type": 1, "var_name": 1, "_id": 0, "var_cn_name": 1},
	})
	if err != nil {
		return fmt.Errorf("get fypus1 realtime for welkin from mongo: %v", err)
	}
	if rawData != nil {
		var results []DeviceRealtimeDataId
		if err = json.Unmarshal(rawData, &results); err != nil {
			return fmt.Errorf("unmarshal fypus1 realtime for welkin: %v", err)
		}
		for _, item := range results {
			WelkinRealtimeFY1Data[item.DataId] = item
			WelkinRealtimeData[umw.FYPUS1][item.DataId] = item
		}
	}
	rawData, err = mgoEntry.ListAll(umw.Device2Cloud, "oss-realtime-pus3", udao.MongoOptions{
		Projection: &bson.M{"data_id": 1, "var_type": 1, "_id": 0, "var_cn_name": 1},
	})
	if err != nil {
		return fmt.Errorf("get pus3 realtime for oss from mongo: %v", err)
	}
	if rawData != nil {
		var results []DeviceRealtimeDataId
		if err = json.Unmarshal(rawData, &results); err != nil {
			return fmt.Errorf("unmarshal pus3 realtime for oss: %v", err)
		}
		for _, item := range results {
			OSSRealtimePS3Data[item.DataId] = item
			OSSRealtimeData[umw.PUS3][item.DataId] = item
		}
	}
	rawData, err = mgoEntry.ListAll(umw.Device2Cloud, "oss-realtime-pus4", udao.MongoOptions{
		Projection: &bson.M{"data_id": 1, "var_type": 1, "_id": 0, "var_cn_name": 1},
	})
	if err != nil {
		return fmt.Errorf("get pus4 realtime for oss from mongo: %v", err)
	}
	if rawData != nil {
		var results []DeviceRealtimeDataId
		if err = json.Unmarshal(rawData, &results); err != nil {
			return fmt.Errorf("unmarshal pus4 realtime for oss: %v", err)
		}
		for _, item := range results {
			OSSRealtimePS4Data[item.DataId] = item
			OSSRealtimeData[umw.PUS4][item.DataId] = item
		}
	}	
	rawData, err = mgoEntry.ListAll(umw.Device2Cloud, "oss-realtime-fypus1", udao.MongoOptions{
		Projection: &bson.M{"data_id": 1, "var_type": 1, "_id": 0, "var_cn_name": 1},
	})
	if err != nil {
		return fmt.Errorf("get fypus1 realtime for oss from mongo: %v", err)
	}
	if rawData != nil {
		var results []DeviceRealtimeDataId
		if err = json.Unmarshal(rawData, &results); err != nil {
			return fmt.Errorf("unmarshal fypus1 realtime for oss: %v", err)
		}
		for _, item := range results {
			OSSRealtimeFY1Data[item.DataId] = item
			OSSRealtimeData[umw.FYPUS1][item.DataId] = item
		}
	}
	return nil
}

func CachePLCDataID(cli *mongo.Client) error {
	mgoEntry := udao.NewMongoEntry(cli)
	rawData, err := mgoEntry.ListAll(umw.Device2Cloud, "sensor-powerswap2", udao.MongoOptions{
		Projection: &bson.M{"var_name": 1, "number": 1, "_id": 0},
	})
	if err != nil {
		return fmt.Errorf("get powerswap2 sensor from mongo: %v", err)
	}
	if rawData != nil {
		var results []umw.MongoSensorVarName
		if err = json.Unmarshal(rawData, &results); err != nil {
			return fmt.Errorf("unmarshal powerswap2 sensor: %v", err)
		}
		for _, item := range results {
			SensorPS2VarNameMap[item.VarName] = item.Number - 1
		}
	}

	rawData, err = mgoEntry.ListAll(umw.Device2Cloud, "sensor-pus3", udao.MongoOptions{
		Projection: &bson.M{"var_name": 1, "number": 1, "_id": 0},
	})
	if err != nil {
		return fmt.Errorf("get pus3 sensor from mongo: %v", err)
	}
	if rawData != nil {
		var results []umw.MongoSensorVarName
		if err = json.Unmarshal(rawData, &results); err != nil {
			return fmt.Errorf("unmarshal pus3 sensor: %v", err)
		}
		for _, item := range results {
			SensorPS3VarNameMap[item.VarName] = item.Number - 1
		}
	}
	return nil
}

func init() {
	// 直接硬写
	pus4Sensor := []string{"bc_slot13_lc_retract_sensor_1", "bc_slot13_lc_retract_sensor_2", "bc_slot13_ec_retract_sensor_1", "bc_slot13_liq_flow_switch_st", "bc_slot14_lc_retract_sensor_1", "bc_slot14_lc_retract_sensor_2", "bc_slot14_ec_retract_sensor_1", "bc_slot14_liq_flow_switch_st", "bc_slot15_lc_retract_sensor_1", "bc_slot15_lc_retract_sensor_2", "bc_slot15_ec_retract_sensor_1", "bc_slot15_liq_flow_switch_st", "bc_slot16_lc_retract_sensor_1", "bc_slot16_lc_retract_sensor_2", "bc_slot16_ec_retract_sensor_1", "bc_slot16_liq_flow_switch_st", "bc_slot17_lc_retract_sensor_1", "bc_slot17_lc_retract_sensor_2", "bc_slot17_ec_retract_sensor_1", "bc_slot17_liq_flow_switch_st", "bc_fire_push_extend_sensor_1", "bc_fire_push_retract_sensor_1", "fire_liq_check", "bc_slot24_reached_sensor", "bcslot13_17_pressure_switch_st", "bc_slot13_ec_retract_sensor_2", "bc_slot13_reached_sensor", "bc_slot14_ec_retract_sensor_2", "bc_slot14_reached_sensor", "bc_slot15_ec_retract_sensor_2", "bc_slot15_reached_sensor", "bc_slot16_ec_retract_sensor_2", "bc_slot16_reached_sensor", "bc_slot17_ec_retract_sensor_2", "bc_slot17_reached_sensor", "bc_slot13_smoke_sensor", "bc_slot14_smoke_sensor", "bc_slot15_smoke_sensor", "bc_slot16_smoke_sensor", "bc_slot17_smoke_sensor", "bc_slot18_lc_retract_sensor_1", "bc_slot18_lc_retract_sensor_2", "bc_slot18_ec_retract_sensor_1", "bc_slot18_liq_flow_switch_st", "bc_slot19_lc_retract_sensor_1", "bc_slot19_lc_retract_sensor_2", "bc_slot19_ec_retract_sensor_1", "bc_slot19_liq_flow_switch_st", "bc_slot20_lc_retract_sensor_1", "bc_slot20_lc_retract_sensor_2", "bc_slot20_ec_retract_sensor_1", "bc_slot20_liq_flow_switch_st", "bc_slot21_lc_retract_sensor_1", "bc_slot21_lc_retract_sensor_2", "bc_slot21_ec_retract_sensor_1", "bc_slot21_liq_flow_switch_st", "bc_slot22_lc_retract_sensor_1", "bc_slot22_lc_retract_sensor_2", "bc_slot22_ec_retract_sensor_1", "bc_slot22_liq_flow_switch_st", "bc_slot23_ec_retract_sensor_1", "bc_fire_push_extend_sensor_2", "bc_fire_push_retract_sensor_2", "bcslot18_22_pressure_switch_st", "bc_slot18_ec_retract_sensor_2", "bc_slot18_reached_sensor", "bc_slot19_ec_retract_sensor_2", "bc_slot19_reached_sensor", "bc_slot20_ec_retract_sensor_2", "bc_slot20_reached_sensor", "bc_slot18_smoke_sensor", "bc_slot19_smoke_sensor", "bc_slot20_smoke_sensor", "bc_slot21_ec_retract_sensor_2", "bc_slot21_reached_sensor", "bc_slot22_ec_retract_sensor_2", "bc_slot22_reached_sensor", "bc_slot23_ec_retract_sensor_2", "bc_slot23_reached_sensor", "bc_slot21_smoke_sensor", "bc_slot22_smoke_sensor", "bc_slot23_smoke_sensor", "bc_slot1_ec_retract_sensor_1", "bc_slot1_reached_sensor", "bc_slot2_ec_retract_sensor_1", "bc_slot2_reached_sensor", "bc_slot3_ec_retract_sensor_1", "bc_slot3_reached_sensor", "bc_slot4_ec_retract_sensor_1", "bc_slot4_reached_sensor", "bc_slot5_ec_retract_sensor_1", "bc_slot5_reached_sensor", "bc_slot6_ec_retract_sensor_1", "bc_slot6_reached_sensor", "bc_slot1_ec_retract_sensor_2", "bc_slot2_ec_retract_sensor_2", "bc_slot3_ec_retract_sensor_2", "bc_slot4_ec_retract_sensor_2", "bc_slot5_ec_retract_sensor_2", "bc_slot6_ec_retract_sensor_2", "bc_slot1_smoke_sensor", "bc_slot2_smoke_sensor", "bc_slot3_smoke_sensor", "bc_slot4_smoke_sensor", "bc_slot5_smoke_sensor", "bc_slot6_smoke_sensor", "bc_slot7_ec_retract_sensor_1", "bc_slot7_reached_sensor", "bc_slot8_ec_retract_sensor_1", "bc_slot8_reached_sensor", "bc_slot9_ec_retract_sensor_1", "bc_slot9_reached_sensor", "bc_slot10_ec_retract_sensor_1", "bc_slot10_reached_sensor", "bc_slot11_ec_retract_sensor_1", "bc_slot11_reached_sensor", "bc_slot12_ec_retract_sensor_1", "bc_slot12_reached_sensor", "bc_slot7_ec_retract_sensor_2", "bc_slot8_ec_retract_sensor_2", "bc_slot9_ec_retract_sensor_2", "bc_slot10_ec_retract_sensor_2", "bc_slot11_ec_retract_sensor_2", "bc_slot12_ec_retract_sensor_2", "bc_slot7_smoke_sensor", "bc_slot8_smoke_sensor", "bc_slot9_smoke_sensor", "bc_slot10_smoke_sensor", "bc_slot11_smoke_sensor", "bc_slot12_smoke_sensor", "RGV_bc_reach_sensor_01", "RGV_bc_reach_sensor_02", "RGV_bc_reach_sensor_04", "RGV_bc_reach_sensor_05", "RGV_bc_reach_sensor_07", "gun1_lift_home_sensor", "gun2_lift_home_sensor", "gun9_lift_home_sensor", "gun10_lift_home_sensor", "gun11_lift_home_sensor", "gun12_lift_home_sensor", "lr_pin_touch_sensor", "lf_pin_retract_sensor", "rr_pin_retract_sensor", "lr_pin_retract_sensor", "pl_stopper_01_work_sensor", "pl_stopper_01_home_sensor", "pl_stopper_02_work_sensor", "pl_stopper_02_home_sensor", "l_bat_pin_retract_sensor", "r_bat_pin_retract_sensor", "pl_stopper_01_reach_sensor", "pl_stopper_02_reach_sensor", "pl_move_work_sensor_1", "pl_stopper_01_dece_sensor", "fork_left_extend_sensor_1", "fork_left_extend_sensor_2", "fork_retract_sensor_2", "stacker_move_f_sensor", "stacker_move_r_sensor", "stacker_move_RGV_sensor", "stacker_left_safe_sensor_1", "stacker_right_safe_sensor_1", "stacker_high_sensor_1", "fork_right_extend_sensor_1", "stacker_bat_sensor_1", "stacker_bat_sensor_2", "stacker_bat_sensor_3", "pl_buffer_dece_sensor_2", "pl_buffer_sensor_f_2", "buffer_stopper_01_extend_sensor_02", "buffer_stopper_01_retract_sensor_02", "pl_buffer_sensor_r_2", "buffer_stopper_02_extend_sensor_02", "buffer_stopper_02_retract_sensor_02", "pl_f_guide_home_sensor", "right_buffer_safe_sensor", "liq_level_warning", "pl_door_01_close_sensor", "pl_door_02_close_sensor", "pl_door_01_open_sensor", "pl_door_02_open_sensor", "maintain_area_safety_01", "maintain_area_safety_02", "left_buffer_safe_sensor", "pl_r_guide_home_sensor", "pl_buffer_dece_sensor_1", "pl_buffer_sensor_f_1", "pl_buffer_sensor_r_1", "stacker_lift_up_limit_sensor", "stacker_lift_down_limit_sensor", "lr_lift_up_limit_sensor", "lr_lift_home_sensor", "stacker_move_f_limit_sensor", "stacker_move_r_limit_sensor"}
	fypus1Sensor := []string{"bc_slot1_reached_sensor", "bc_slot2_reached_sensor", "bc_slot3_reached_sensor", "bc_slot1_smoke_sensor", "bc_slot2_smoke_sensor", "bc_slot3_smoke_sensor", "stacker_home_brake", "bc_slot4_reached_sensor", "bc_slot5_reached_sensor", "bc_slot6_reached_sensor", "bc_slot4_smoke_sensor", "bc_slot5_smoke_sensor", "bc_slot6_smoke_sensor", "bc_slot10_lc_retract_sensor", "bc_slot11_lc_retract_sensor", "bc_slot12_lc_retract_sensor", "bc_slot10_reached_sensor", "bc_slot11_reached_sensor", "bc_slot12_reached_sensor", "bc_slot10_lc_cooling_swicth", "bc_slot11_lc_cooling_swicth", "bc_slot12_lc_cooling_swicth", "Fire_bunker_1_extend_sensor", "Fire_bunker_1_retract_sensor", "bc_slot11_smoke_sensor", "bc_slot12_smoke_sensor", "bc_slot7_lc_retract_sensor", "bc_slot8_lc_retract_sensor", "bc_slot9_lc_retract_sensor", "bc_slot7_reached_sensor", "bc_slot8_reached_sensor", "bc_slot9_reached_sensor", "bc_slot7_lc_cooling_swicth", "bc_slot8_lc_cooling_swicth", "bc_slot9_lc_cooling_swicth", "bc_slot7_smoke_sensor", "bc_slot8_smoke_sensor", "bc_slot9_smoke_sensor", "bc_slot10_smoke_sensor", "baffle_1_extend_sensor", "baffle_1_retract_sensor", "baffle_3_extend_sensor", "baffle_3_retract_sensor", "baffle_up_exist_sensor_f", "baffle_down_exist_sensor_f", "bc_lift_home_sensor_1", "bc_lift_work_sensor_1", "platfrom_water_check_1", "platfrom_water_check_2", "vehical_lift_lf_home_sensor", "RGV_S_home_sensor", "vehical_lift_rf_home_sensor", "S_l_pin_work_sensor", "vehical_lift_lr_home_sensor", "S_l_pin_home_sensor", "vehical_lift_lf_home_brake", "S_r_pin_work_sensor", "vehical_lift_rf_home_brake", "S_r_pin_home_sensor", "vehical_lift_lr_home_brake", "vehical_lift_rr_home_brake", "RGV_lift_home_sensor", "RGV_move_work_sensor", "RGV_move_home_sensor", "RGV_reach_sensor_1", "RGV_reach_sensor_2", "RGV_reach_sensor_3", "RGV_reach_sensor_4", "lf_pin_home_sensor", "lf_pin_touch_sensor", "rr_pin_home_sensor", "rr_pin_touch_sensor", "bc_slot1_ec_retract_sensor", "bc_slot2_ec_retract_sensor", "bc_slot3_ec_retract_sensor", "bc_slot4_ec_retract_sensor", "bc_slot5_ec_retract_sensor", "bc_slot6_ec_retract_sensor", "door_open_sensor", "door_close_sensor", "vehical_lift_rr_home_sensor", "baffle_2_extend_sensor", "baffle_2_retract_sensor", "baffle_4_extend_sensor", "baffle_4_retract_sensor", "baffle_up_exist_sensor_r", "baffle_down_exist_sensor_r", "bc_lift_home_sensor_2", "bc_lift_work_sensor_2", "stacker_lift_1_sensor", "stacker_lift_2_sensor", "stacker_lift_3_sensor", "stacker_lift_4_sensor", "stacker_lift_5_sensor", "stacker_lift_6_sensor", "stacker_lift_home_sensor", "stacker_pin_1_sensor", "stacker_pin_2_sensor", "fork_work_sensor_1", "fork_work_sensor_3", "fork_home_sensor_1", "fork_home_sensor_2", "fork_exist_sensor_1", "fork_exist_sensor_2", "bc_safe_sensor_1", "bc_safe_sensor_2", "bc_slot7_ec_retract_sensor", "bc_slot8_ec_retract_sensor", "bc_slot9_ec_retract_sensor", "bc_fire_water_check_1", "bc_slot10_ec_retract_sensor", "bc_slot11_ec_retract_sensor", "bc_slot12_ec_retract_sensor", "Fire_bunker_2_extend_sensor", "Fire_bunker_2_retract_sensor", "bc_slot13_reached_sensor", "bc_fire_water_check_2", "bc_fire_water_T_check", "pl_move_f_limit_sensor", "pl_move_r_limit_sensor", "lr_lift_up_limit_sensor", "lr_lift_down_limit_sensor", "fork_X_left_limit_sensor", "fork_X_right_limit_sensor", "stacker_lift_up_limit_sensor", "stacker_lift_down_limit_sensor", "maintain_area_safety_01", "maintain_area_safety_02", "fire_door_check_1", "fire_door_check_2", "reserved_5", "reserved_6", "reserved_7", "reserved_8", "reserved_9", "reserved_10", "reserved_11", "reserved_12", "reserved_13", "reserved_14", "reserved_15", "reserved_16", "reserved_17", "reserved_18", "reserved_19", "reserved_20", "reserved_21", "reserved_22", "reserved_23", "reserved_24", "reserved_25", "reserved_26", "reserved_27", "reserved_28", "reserved_29", "reserved_30", "reserved_31", "reserved_32", "reserved_33", "reserved_34", "reserved_35", "reserved_36", "reserved_37", "reserved_38", "reserved_39", "reserved_40", "reserved_41", "reserved_42", "reserved_43", "reserved_44", "reserved_45", "reserved_46", "reserved_47", "reserved_48", "reserved_49", "reserved_50", "reserved_51", "reserved_52", "reserved_53", "reserved_54", "reserved_55", "reserved_56", "reserved_57", "reserved_58", "reserved_59", "reserved_60", "reserved_61", "reserved_62", "reserved_63", "reserved_64", "reserved_65", "reserved_66", "reserved_67", "reserved_68", "reserved_69", "reserved_70", "reserved_71", "reserved_72", "reserved_73", "reserved_74"}
	pus4DI := []string{"emergency_stop_switch_01", "trans_fire_stopper_retract", "maintain_operate_swicth", "A01_A1_check", "I_power_st_1", "I_power_st_2", "I_power_st_3", "I_power_st_4", "I_power_st_5", "I_power_st_6", "I_power_st_7", "I_power_st_8", "I_power_st_9", "I_power_st_10", "I_power_st_11", "I_power_st_12", "I_power_st_13", "I_water_press_up", "I_power_st_14", "I_power_st_15", "I_water_press_down", "I_fire_pump_feedback", "I_fire_heater_feedback", "I_fire_D_door", "I_fire_E_door", "I_heater_temp_up", "I_heater_temp_down", "I_flow_switch_up", "I_flow_switch_down"}
	fypus1DI := []string{"I_power_st_1", "I_power_st_2", "I_power_st_3", "I_power_st_4", "I_power_st_5", "I_power_st_6", "I_power_st_7", "I_power_st_8", "I_power_st_9", "I_power_st_10", "I_power_st_11", "I_power_st_12", "I_power_st_13", "I_power_st_14", "I_power_st_15", "trans_fire_stopper_retract", "emergency_stop_switch_01", "battery_slot_area_check", "stacker_chain_check_1", "stacker_chain_check_2", "Maitaindoor_security_1", "Maitaindoor_security_2", "scanistor_di_err", "fire_door_check_1", "fire_door_check_2", "fire_spray_flow_nc_feedblack", "fire_spray_flow_no_feedblack", "fire_spray_T_limit_up_feedblack", "fire_spray_T_limit_down_feedblack", "fire_spray_P_limit_up_feedblack", "fire_spray_P_limit_down_feedblack", "fire_spray_power_F1_feedblack", "fire_spray_power_KM_feedblack", "scanistor_safe_feedblack", "carpet_safe_feedblack", "battery_slot_area_KM_feedblack", "platfrom_area_KM_feedblack", "maintain_operate_swicth", "platfrom_door_safe_check_feedblack", "hydraulic_cylinder_water_low_err", "hydraulic_cylinder_water_jam_err", "I_power_st_16", "reserved_DI_1", "reserved_DI_2", "reserved_DI_3", "reserved_DI_4", "reserved_DI_5", "reserved_DI_6", "reserved_DI_7", "reserved_DI_8"}
	for i, s := range pus4Sensor {
		SensorPS4VarNameMap[s] = i
	}
	for i, s := range fypus1Sensor {
		SensorFYPUS1VarNameMap[s] = i
	}
	for i, s := range pus4DI {
		DIPS4NameMap[s] = i
	}
	for i, s := range fypus1DI {
		DIFYPUS1NameMap[s] = i
	}

}
