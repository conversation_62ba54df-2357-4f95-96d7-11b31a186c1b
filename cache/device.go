package cache

import (
	"context"
	"time"

	"github.com/patrickmn/go-cache"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	umw "git.nevint.com/golang-libs/common-utils/model/welkin"

	log "git.nevint.com/welkin2/welkin-backend/logger"
)

type DeviceInfoCache struct {
	Cache         *cache.Cache //  设备Id为key
	ResourceCache *cache.Cache //  Resource idId为key
}

// device info cache from mongo db
var PowerSwapCache *DeviceInfoCache

var ChargerCache *DeviceInfoCache

func getAllDevices(mongoClient *mongo.Client) (devicesData []umw.MongoDeviceInfo, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
	defer cancel()

	allDevices := []umw.MongoDeviceInfo{}
	filter := bson.D{}
	Limit := int64(5000)
	Offset := int64(0)
	for {
		cur, err := mongoClient.Database("oauth").Collection("device_basic_info").Find(ctx, filter, options.Find().SetSort(bson.M{"_id": -1}).SetSkip(Offset).SetLimit(Limit))
		if err != nil {
			return nil, err
		}
		var devices []umw.MongoDeviceInfo
		if err = cur.All(ctx, &devices); err != nil {
			return nil, err
		}
		allDevices = append(allDevices, devices...)

		if len(devices) < int(Limit) {
			break
		}
		Offset = Offset + int64(len(devices))
	}

	return allDevices, nil
}

func getAllChargers(mongoClient *mongo.Client) (devicesData []umw.MongoDeviceInfo, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
	defer cancel()

	allDevices := []umw.MongoDeviceInfo{}
	filter := bson.D{}
	Limit := int64(5000)
	Offset := int64(0)
	for {
		cur, err := mongoClient.Database(umw.OAuthDB).Collection("charger_basic_info").Find(ctx, filter, options.Find().SetSort(bson.M{"_id": -1}).SetSkip(Offset).SetLimit(Limit))
		if err != nil {
			return nil, err
		}
		var devices []umw.MongoDeviceInfo
		if err = cur.All(ctx, &devices); err != nil {
			return nil, err
		}
		allDevices = append(allDevices, devices...)

		if len(devices) < int(Limit) {
			break
		}
		Offset = Offset + int64(len(devices))
	}

	return allDevices, nil
}

func (d *DeviceInfoCache) RefreshDeviceInfoCache(mongoClient *mongo.Client) {
	devices, err := getAllDevices(mongoClient)
	if err != nil {
		log.Logger.Errorf("RefreshDeviceInfoCache err:%v", err)
		return
	}
	for _, deviceInfo := range devices {
		if deviceInfo.DeviceId == "" {
			continue
		}
		d.Cache.Set(deviceInfo.DeviceId, deviceInfo, cache.NoExpiration)
		d.ResourceCache.Set(deviceInfo.ResourceId, deviceInfo, cache.NoExpiration)
	}
	log.Logger.Infof("RefreshDeviceInfoCache success. refrsh %v items", len(devices))
}

func (d *DeviceInfoCache) RefreshChargerInfoCache(mongoClient *mongo.Client) {
	chargers, err := getAllChargers(mongoClient)
	if err != nil {
		log.Logger.Errorf("RefreshDeviceInfoCache err:%v", err)
		return
	}
	for _, deviceInfo := range chargers {
		if deviceInfo.DeviceId == "" {
			continue
		}
		d.Cache.Set(deviceInfo.DeviceId, deviceInfo, cache.NoExpiration)
		d.ResourceCache.Set(deviceInfo.ResourceId, deviceInfo, cache.NoExpiration)
	}
	log.Logger.Infof("RefreshDeviceInfoCache success. refrsh %v items", len(chargers))
}

func (d *DeviceInfoCache) GetAllDevices() []umw.MongoDeviceInfo {
	res := []umw.MongoDeviceInfo{}
	for _, item := range d.Cache.Items() {
		res = append(res, item.Object.(umw.MongoDeviceInfo))
	}
	return res
}

func (d *DeviceInfoCache) GetSingleDevice(deviceId string) (*umw.MongoDeviceInfo, bool) {
	var res umw.MongoDeviceInfo
	if d.Cache == nil {
		return &res, false
	}
	item, ok := d.Cache.Get(deviceId)
	if !ok {
		return &res, ok
	}
	res, ok = item.(umw.MongoDeviceInfo)
	return &res, ok
}

func (d *DeviceInfoCache) GetDeviceByResourceId(resourceId string) (*umw.MongoDeviceInfo, bool) {
	var res umw.MongoDeviceInfo
	if d.Cache == nil {
		return &res, false
	}
	item, ok := d.ResourceCache.Get(resourceId)
	if !ok {
		return &res, ok
	}
	res, ok = item.(umw.MongoDeviceInfo)
	return &res, ok
}
